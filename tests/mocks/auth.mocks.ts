// auth.mocks.ts
import { vi } from 'vitest';

// Mock fetch implementation
export const mockFetch = vi.fn();

// Mock session storage
export function mockSessionStorage() {
  // Create a simple in-memory storage object
  const storage: Record<string, string> = {};

  // Mock sessionStorage
  vi.stubGlobal('sessionStorage', {
    getItem: vi.fn((key: string) => storage[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach(key => {
        delete storage[key];
      });
    }),
    key: vi.fn((index: number) => Object.keys(storage)[index] || null),
    length: Object.keys(storage).length,
  });

  // Mock localStorage
  vi.stubGlobal('localStorage', {
    getItem: vi.fn((key: string) => storage[key] || null),
    setItem: vi.fn((key: string, value: string) => {
      storage[key] = value;
    }),
    removeItem: vi.fn((key: string) => {
      delete storage[key];
    }),
    clear: vi.fn(() => {
      Object.keys(storage).forEach(key => {
        delete storage[key];
      });
    }),
    key: vi.fn((index: number) => Object.keys(storage)[index] || null),
    length: Object.keys(storage).length,
  });

  return storage;
}

// Mock cookie functions
export function mockCookies() {
  const cookies: Record<string, string> = {};

  // Mock document.cookie
  Object.defineProperty(document, 'cookie', {
    get: vi.fn(() => {
      return Object.entries(cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');
    }),
    set: vi.fn((cookieString: string) => {
      const [cookiePair] = cookieString.split(';');
      if (!cookiePair) return;

      const [name, value] = cookiePair.split('=');
      if (!name || !value) return;

      cookies[name.trim()] = value.trim();
    }),
  });

  // Helper to clear cookies
  const clearCookies = () => {
    Object.keys(cookies).forEach(key => {
      delete cookies[key];
    });
  };

  return { cookies, clearCookies };
}

// Mock FormData
export class MockFormData {
  private data: Record<string, string> = {};

  append(key: string, value: string) {
    this.data[key] = value;
  }

  get(key: string) {
    return this.data[key] || null;
  }

  has(key: string) {
    return key in this.data;
  }

  delete(key: string) {
    delete this.data[key];
  }

  entries() {
    return Object.entries(this.data);
  }

  keys() {
    return Object.keys(this.data);
  }

  values() {
    return Object.values(this.data);
  }

  forEach(callback: (value: string, key: string) => void) {
    Object.entries(this.data).forEach(([key, value]) => {
      callback(value, key);
    });
  }
}

// Mock window.location
export function mockWindowLocation(url = 'https://example.com') {
  const location = new URL(url);
  
  vi.stubGlobal('location', {
    ...location,
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  });

  return location;
}

// Mock clearCookie function
export const mockClearCookie = vi.fn();
