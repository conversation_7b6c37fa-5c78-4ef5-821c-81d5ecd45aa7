import { vi } from 'vitest';

export class AudioWorkletNodeMock {
  numberOfInputs: number;
  numberOfOutputs: number;
  channelCount: number;
  channelCountMode: string;
  channelInterpretation: string;

  constructor() {
    this.numberOfInputs = 1;
    this.numberOfOutputs = 1;
    this.channelCount = 2;
    this.channelCountMode = 'max';
    this.channelInterpretation = 'speakers';
  }

  connect() {
    return this;
  }

  disconnect() {
    return this;
  }

  addModule() {
    return vi.fn();
  }

  port() {
    return new MessagePort();
  }
}


export class AudioContextMock {
  currentTime: number;
  sampleRate: number;
  state: string;
  audioWorklet: AudioWorkletNodeMock;

  constructor() {
    this.currentTime = 0;
    this.sampleRate = 44100;
    this.audioWorklet = new AudioWorkletNodeMock();
    this.state = 'running';
  }

  createBuffer(numberOfChannels: any, length: any, sampleRate: any) {
    return new AudioBufferMock(numberOfChannels, length, sampleRate);
  }

  createBufferSource() {
    return new AudioBufferSourceNodeMock();
  }

  createGain() {
    return new GainNodeMock();
  }

  createOscillator() {
    return new OscillatorNodeMock();
  }

  createAnalyser() {
    return new AnalyserNodeMock();
  }

  createBiquadFilter() {
    return new BiquadFilterNodeMock();
  }

  createPanner() {
    return new PannerNodeMock();
  }

  createStereoPanner() {
    return new StereoPannerNodeMock();
  }

  createDynamicsCompressor() {
    return new DynamicsCompressorNodeMock();
  }

  createWaveShaper() {
    return new WaveShaperNodeMock();
  }

  decodeAudioData(audioData: { byteLength: number; }) {
    return Promise.resolve(new AudioBufferMock(2, audioData.byteLength / 2, this.sampleRate));
  }

  suspend() {
    this.state = 'suspended';
    return Promise.resolve();
  }

  resume() {
    this.state = 'running';
    return Promise.resolve();
  }

  close() {
    this.state = 'closed';
    return Promise.resolve();
  }
}

export class AudioContextSuspendedMock extends AudioContextMock {
  constructor() {
    super();
    this.state = 'suspended';
  }
}

class AudioNodeMock {
  context: AudioContextMock;
  numberOfInputs: number;
  numberOfOutputs: number;
  channelCount: number;
  channelCountMode: string;
  channelInterpretation: string;
  constructor() {
    this.context = new AudioContextMock();
    this.numberOfInputs = 1;
    this.numberOfOutputs = 1;
    this.channelCount = 2;
    this.channelCountMode = 'max';
    this.channelInterpretation = 'speakers';
  }

  connect() {
    return this;
  }

  disconnect() {
    return this;
  }
}

class AudioBufferMock {
  numberOfChannels: any;
  length: any;
  sampleRate: any;
  duration: number;

  constructor(numberOfChannels: number, length: number, sampleRate: number) {
    this.numberOfChannels = numberOfChannels;
    this.length = length;
    this.sampleRate = sampleRate;
    this.duration = length / sampleRate;
  }

  getChannelData(channel: any) {
    return new Float32Array(this.length);
  }
}

class AudioBufferSourceNodeMock extends AudioNodeMock {
  buffer: null;
  playbackRate: { value: number; };
  loop: boolean;
  loopStart: number;
  loopEnd: number;
  constructor() {
    super();
    this.buffer = null;
    this.playbackRate = { value: 1 };
    this.loop = false;
    this.loopStart = 0;
    this.loopEnd = 0;
  }

  start() {}

  stop() {}
}

class GainNodeMock extends AudioNodeMock {
  gain: { value: number; };
  constructor() {
    super();
    this.gain = { value: 1 };
  }
}

class OscillatorNodeMock extends AudioNodeMock {
  frequency: { value: number; };
  detune: { value: number; };
  type: string;
  constructor() {
    super();
    this.frequency = { value: 440 };
    this.detune = { value: 0 };
    this.type = 'sine';
  }

  start() {}

  stop() {}
}

class AnalyserNodeMock extends AudioNodeMock {
  fftSize: number;
  frequencyBinCount: number;
  minDecibels: number;
  maxDecibels: number;
  smoothingTimeConstant: number;
  constructor() {
    super();
    this.fftSize = 2048;
    this.frequencyBinCount = this.fftSize / 2;
    this.minDecibels = -100;
    this.maxDecibels = -30;
    this.smoothingTimeConstant = 0.8;
  }

  getByteFrequencyData(array: any) {}

  getByteTimeDomainData(array: any) {}

  getFloatFrequencyData(array: any) {}

  getFloatTimeDomainData(array: any) {}
}

class BiquadFilterNodeMock extends AudioNodeMock {
  frequency: { value: number; };
  detune: { value: number; };
  Q: { value: number; };
  gain: { value: number; };
  type: string;
  constructor() {
    super();
    this.frequency = { value: 350 };
    this.detune = { value: 0 };
    this.Q = { value: 1 };
    this.gain = { value: 0 };
    this.type = 'lowpass';
  }
}

class PannerNodeMock extends AudioNodeMock {
  positionX: { value: number; };
  positionY: { value: number; };
  positionZ: { value: number; };
  orientationX: { value: number; };
  orientationY: { value: number; };
  orientationZ: { value: number; };
  panningModel: string;
  distanceModel: string;
  refDistance: number;
  maxDistance: number;
  rolloffFactor: number;
  coneInnerAngle: number;
  coneOuterAngle: number;
  coneOuterGain: number;
  constructor() {
    super();
    this.positionX = { value: 0 };
    this.positionY = { value: 0 };
    this.positionZ = { value: 0 };
    this.orientationX = { value: 1 };
    this.orientationY = { value: 0 };
    this.orientationZ = { value: 0 };
    this.panningModel = 'HRTF';
    this.distanceModel = 'inverse';
    this.refDistance = 1;
    this.maxDistance = 10000;
    this.rolloffFactor = 1;
    this.coneInnerAngle = 360;
    this.coneOuterAngle = 360;
    this.coneOuterGain = 0;
  }
}

class StereoPannerNodeMock extends AudioNodeMock {
  pan: { value: number; };
  constructor() {
    super();
    this.pan = { value: 0 };
  }
}

class DynamicsCompressorNodeMock extends AudioNodeMock {
  threshold: { value: number; };
  knee: { value: number; };
  ratio: { value: number; };
  attack: { value: number; };
  release: { value: number; };
  constructor() {
    super();
    this.threshold = { value: -24 };
    this.knee = { value: 30 };
    this.ratio = { value: 12 };
    this.attack = { value: 0.003 };
    this.release = { value: 0.25 };
  }
}

class WaveShaperNodeMock extends AudioNodeMock {
  curve: null;
  oversample: string;
  constructor() {
    super();
    this.curve = null;
    this.oversample = 'none';
  }
}

class AudioListenerMock {
  positionX: { value: number; };
  positionY: { value: number; };
  positionZ: { value: number; };
  forwardX: { value: number; };
  forwardY: { value: number; };
  forwardZ: { value: number; };
  upX: { value: number; };
  upY: { value: number; };
  upZ: { value: number; };
  constructor() {
    this.positionX = { value: 0 };
    this.positionY = { value: 0 };
    this.positionZ = { value: 0 };
    this.forwardX = { value: 0 };
    this.forwardY = { value: 0 };
    this.forwardZ = { value: -1 };
    this.upX = { value: 0 };
    this.upY = { value: 1 };
    this.upZ = { value: 0 };
  }
}

// Ensure the prototype is set correctly
// AudioBufferSourceNodeMock.prototype = Object.create(AudioBufferSourceNode.prototype);
// AudioBufferSourceNodeMock.prototype.constructor = AudioBufferSourceNodeMock;

global.AudioContext = vi.fn().mockImplementation(() => new AudioContextMock());
// global.AudioBuffer = AudioBufferMock;
// global.AudioNode = AudioNodeMock;
// global.AudioBufferSourceNode = AudioBufferSourceNodeMock;
// global.GainNode = GainNodeMock;
// global.OscillatorNode = OscillatorNodeMock;
// global.AnalyserNode = AnalyserNodeMock;
// global.BiquadFilterNode = BiquadFilterNodeMock;
// global.PannerNode = PannerNodeMock;
// global.StereoPannerNode = StereoPannerNodeMock;
// global.DynamicsCompressorNode = DynamicsCompressorNodeMock;
// global.WaveShaperNode = WaveShaperNodeMock;
// global.AudioListener = AudioListenerMock;