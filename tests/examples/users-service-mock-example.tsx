import { render, screen } from '@solidjs/testing-library';
import { Component } from 'solid-js';
import { useService } from 'solid-services';
import { ClientSideUserDto, UserDto, Roles } from '~/proto/user-renditions';
import UsersService from '~/services/users-service';
import { MockUsersService } from '@test/mocks/service.mocks';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Mock the solid-services useService function
vi.mock('solid-services', () => ({
  useService: vi.fn()
}));

// Example component that uses UsersService
const UsersList: Component = () => {
  const usersService = useService(UsersService)();

  return (
    <div>
      <h1>Users List</h1>
      <ul data-testid="users-list">
        {usersService.users.map(user => (
          <li data-testid={`user-${user.socketID}`} key={user.socketID}>
            {user.userDto?.username || 'Unknown User'}
          </li>
        ))}
      </ul>
    </div>
  );
};

// Example test that demonstrates how to use the MockUsersService
describe('UsersList Component with MockUsersService', () => {
  let mockUsersService: ReturnType<typeof MockUsersService>;

  beforeEach(() => {
    // Create a new mock service for each test
    mockUsersService = MockUsersService();
    mockUsersService._clearMockUsers();

    // Configure useService to return our mock
    (useService as any).mockImplementation((service) => {
      if (service === UsersService) {
        return () => mockUsersService;
      }
      return vi.fn();
    });
  });

  it('should render an empty list when there are no users', () => {
    render(() => <UsersList />);

    const usersList = screen.getByTestId('users-list');
    expect(usersList.children.length).toBe(0);
  });

  it('should render users from the service', () => {
    // Create and add mock users
    const createMockUser = (username: string, socketID: string) => {
      const userDto = UserDto.create({
        username,
        usertag: `${username}#1234`,
        socketID,
        roles: [Roles.USER]
      });

      return ClientSideUserDto.create({
        userDto,
        socketID
      });
    };

    // Add two mock users
    mockUsersService._addMockUser(createMockUser('User1', 'socket1'));
    mockUsersService._addMockUser(createMockUser('User2', 'socket2'));

    render(() => <UsersList />);

    // Check that both users are rendered
    const usersList = screen.getByTestId('users-list');
    expect(usersList.children.length).toBe(2);

    // Check specific users
    expect(screen.getByTestId('user-socket1').textContent).toBe('User1');
    expect(screen.getByTestId('user-socket2').textContent).toBe('User2');
  });

  it('should update when users are added to the service', () => {
    // Initial render with no users
    const { rerender } = render(() => <UsersList />);

    // Check initial empty state
    const usersList = screen.getByTestId('users-list');
    expect(usersList.children.length).toBe(0);

    // Add a user to the mock service
    const userDto = UserDto.create({
      username: 'NewUser',
      usertag: 'NewUser#1234',
      socketID: 'socket3',
      roles: [Roles.USER]
    });

    mockUsersService._addMockUser(ClientSideUserDto.create({
      userDto,
      socketID: 'socket3'
    }));

    // Rerender the component
    rerender();

    // Check that the new user is rendered
    expect(usersList.children.length).toBe(1);
    expect(screen.getByTestId('user-socket3').textContent).toBe('NewUser');
  });
});
