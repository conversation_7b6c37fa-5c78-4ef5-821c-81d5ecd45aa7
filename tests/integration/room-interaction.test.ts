// room-interaction.test.ts
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';
import {createEventBus} from '@solid-primitives/event-bus';
import {AppStateEffects, AppStateEffects_Action} from '~/proto/pianorhythm-effects';
import {AppStateEvents} from '~/proto/pianorhythm-events';
import {CreateRoomParam} from '~/proto/server-message';
import {ClientSideUserDto, UserMetaDto, UserStatus, WorldData} from '~/proto/user-renditions';
import {
    MockAppService,
    MockCoreService,
    MockDatabaseServiceController,
    MockI18nService,
    MockWebSocketService
} from '@test/mocks/service.mocks';
import {useService} from 'solid-services';
import WebsocketService from '~/services/websocket.service';
import AppService from '~/services/app.service';
import RoomsService from '~/services/rooms.service';
import ChatService from '~/services/chat.service';
import I18nService from '~/services/i18n.service';
import {RoomStatus, RoomType} from '~/proto/room-renditions';
import UsersService from "~/services/users-service";
import DatabaseServiceController from "~/services/db.service";
import {undefined} from "zod";

// Mock dependencies
vi.mock('solid-services', () => ({
    useService: vi.fn()
}));

vi.mock('~/services/notification.service', () => ({
    default: {
        show: vi.fn(),
        hide: vi.fn()
    }
}));

describe('Room Interaction Flow Tests', () => {
    let appServiceMock: ReturnType<typeof MockAppService>;
    let websocketServiceMock: ReturnType<typeof MockWebSocketService>;
    let coreServiceMock: ReturnType<typeof MockCoreService>;
    let i18nServiceMock: ReturnType<typeof MockI18nService>;
    let databaseServiceControllerMock: ReturnType<typeof MockDatabaseServiceController>;
    let roomsService: ReturnType<typeof RoomsService>;
    let chatService: ReturnType<typeof ChatService>;
    let usersService: ReturnType<typeof UsersService>;
    let appStateEffectsBus: ReturnType<typeof createEventBus<AppStateEffects>>;
    let appStateEventsBus: ReturnType<typeof createEventBus<AppStateEvents>>;

    beforeEach(() => {
        // Setup mocks
        appServiceMock = MockAppService();
        websocketServiceMock = MockWebSocketService();
        coreServiceMock = MockCoreService();
        i18nServiceMock = MockI18nService();
        databaseServiceControllerMock = MockDatabaseServiceController();

        appStateEffectsBus = createEventBus<AppStateEffects>();
        appStateEventsBus = createEventBus<AppStateEvents>();

        // Setup i18n mock
        i18nServiceMock.t_server = vi.fn().mockReturnValue('translated message');

        // Setup app service mock
        appServiceMock.coreService = vi.fn().mockReturnValue(coreServiceMock);
        appServiceMock.appStateEffects = appStateEffectsBus;
        appServiceMock.appStateEvents = appStateEventsBus;
        appServiceMock.isClientRoomOwner = vi.fn().mockReturnValue(false);
        appServiceMock.isClientMod = vi.fn().mockReturnValue(false);
        appServiceMock.isClientMember = vi.fn().mockReturnValue(true);
        appServiceMock.roomID = vi.fn().mockReturnValue(null);

        // Mock useService to return our mocks
        (useService as any).mockImplementation((service: any) => {
            if (service === AppService) return () => appServiceMock;
            if (service === WebsocketService) return () => websocketServiceMock;
            if (service === I18nService) return () => i18nServiceMock;
            if (service === DatabaseServiceController) return () => databaseServiceControllerMock;
            return () => ({});
        });

        // Initialize services
        roomsService = RoomsService();
        roomsService.initialize(coreServiceMock, appStateEffectsBus, appStateEventsBus);

        // Create ChatService with mocked dependencies
        chatService = ChatService();
        chatService.initialize(appStateEffectsBus, createEventBus());

        usersService = UsersService();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('should handle chat messaging correctly', async () => {
        // 1. Join a room
        const roomName = 'TestRoom';
        const roomId = 'room-123';

        // Mock successful room join
        websocketServiceMock.joinRoomByName = vi.fn().mockImplementation((name: string) => {
            expect(name).toBe(roomName);

            // Simulate server response for successful join
            appStateEffectsBus.emit({
                action: AppStateEffects_Action.JoinedRoomSuccess,
                joinedRoomData: {
                    roomID: roomId,
                    roomName: roomName,
                    roomType: RoomType.Lobby,
                    roomStatus: RoomStatus.Public,
                    roomSettings: undefined,
                    roomOwner: 'socket1'
                }
            });
        });

        // Join the room
        websocketServiceMock.joinRoomByName(roomName);

        // Update app service mock to reflect joined state
        appServiceMock.roomID = vi.fn().mockReturnValue(roomId);

        // 2. Send chat messages
        const chatMessage = 'Hello, world!';
        websocketServiceMock.emitChatMessage = vi.fn().mockImplementation((message: string) => {
            expect(message).toBe(chatMessage);
        });

        websocketServiceMock.emitChatMessage(chatMessage);
        expect(websocketServiceMock.emitChatMessage).toHaveBeenCalledWith(chatMessage);

        // 3. Test message editing
        const editedMessageId = 'msg-123';
        const editedContent = 'Updated message';

        websocketServiceMock.emitChatMessage = vi.fn().mockImplementation((message: string, replyID: string, editID: string, options?: any) => {
            expect(message).toBe(editedContent);
            expect(editID).toBe(editedMessageId);
            return Promise.resolve();
        });

        websocketServiceMock.emitChatMessage(editedContent, undefined, editedMessageId);
        expect(websocketServiceMock.emitChatMessage).toHaveBeenCalledWith(editedContent, undefined, editedMessageId);
    });

    it.skip('should handle user presence and status updates', async () => {
        // 1. Track users joining/leaving
        const socketId = 'socket-123';
        const roomId = 'room-123';
        appServiceMock.roomID = vi.fn().mockReturnValue(roomId);

        // Create a test user
        const testUser: ClientSideUserDto = {
            localChatMuted: false, localNotesMuted: false, socketID: socketId,
            userDto: {
                badges: [],
                color: '',
                isProMember: false,
                nickname: 'Tester',
                roles: [],
                selfMuted: false,
                serverChatMuted: false,
                serverNotesMuted: false,
                socketID: 'socket-456',
                status: UserStatus.None,
                statusText: 'Online',
                username: 'TestUser',
                usertag: 'testuser#123',
                uuid: '',
                meta: {
                    bot: false,
                    discordBot: false,
                    enteredRoomDateTime: '2023-10-02T14:00:00Z',
                    modifiedDate: '2023-10-02T14:56:78Z',
                },
                worldData: WorldData.create({})
            },
        };

        // Simulate user joining
        appStateEffectsBus.emit({
            action: AppStateEffects_Action.AddUser,
            clientSideUserDto: testUser
        });

        // Expect user to be in users service
        expect(usersService.getUserBySocketID(testUser.socketID)).toEqual(testUser);

        // 2. Update and observe user status
        const updatedUser = {...testUser};
        updatedUser.userDto.statusText = 'Away';

        // Simulate user status update
        appStateEffectsBus.emit({
            action: AppStateEffects_Action.RoomEvent,
            roomEvent: ['UserUpdated', updatedUser]
        });

        // 3. Test typing indicators
        const typingUser = {...testUser};
        typingUser.isTyping = true;

        // Simulate typing indicator
        appStateEffectsBus.emit({
            action: AppStateEffects_Action.RoomEvent,
            roomEvent: ['UserUpdated', typingUser]
        });

        // Simulate users typing event
        appStateEffectsBus.emit({
            action: AppStateEffects_Action.RoomEvent,
            roomEvent: ['UsersTyping', [typingUser.userDto.socketID]]
        });

        // Test client emitting typing status
        websocketServiceMock.emitIsTyping = vi.fn().mockImplementation((isTyping) => {
            expect(isTyping).toBe(true);
        });

        websocketServiceMock.emitIsTyping(true);
        expect(websocketServiceMock.emitIsTyping).toHaveBeenCalledWith(true);

        // Simulate user leaving
        appStateEffectsBus.emit({
            action: AppStateEffects_Action.RoomEvent,
            roomEvent: ['UserLeft', testUser.userDto.socketID]
        });
    });

    it('should handle room ownership and permissions correctly', async () => {
        // 1. Create room as owner
        const roomName = 'OwnerTestRoom';
        const roomId = 'owner-room-123';

        // Setup room creation parameters
        const createRoomParams = CreateRoomParam.create({
            RoomName: roomName,
            RoomOwner: 'TestOwner',
            MaxPlayers: 10,
            OnlyOwnerCanChat: false,
            OnlyOwnerCanPlay: false,
            AllowGuests: true
        });

        websocketServiceMock.createOrUpdateRoom = vi.fn().mockImplementation((params) => {
            expect(params.RoomName).toBe(roomName);

            // Simulate successful room creation
            appStateEffectsBus.emit({
                action: AppStateEffects_Action.JoinedRoomSuccess,
                joinedRoomData: {
                    roomID: roomId,
                    roomName: roomName,
                    roomType: RoomType.Lobby,
                    roomStatus: RoomStatus.Public,
                    roomSettings: undefined,
                    roomOwner: ''
                }
            });

            return Promise.resolve();
        });

        await websocketServiceMock.createOrUpdateRoom(createRoomParams);
        expect(websocketServiceMock.createOrUpdateRoom).toHaveBeenCalledWith(createRoomParams);

        // Update app service to reflect owner status
        appServiceMock.roomID = vi.fn().mockReturnValue(roomId);
        appServiceMock.isClientRoomOwner = vi.fn().mockReturnValue(true);

        // 2. Test owner-specific commands
        const ownerCommand = '$kick TestUser';

        websocketServiceMock.emitRoomChatServerCommand = vi.fn().mockImplementation((command) => {
            expect(command).toBe(ownerCommand);
        });

        chatService.runRoomOwnerChatCommand({
            moduleID: "",
            command: 'kick',
            roomOwnerOnly: true,
            disabled: false
        }, ['TestUser']);
        expect(websocketServiceMock.emitRoomChatServerCommand).toHaveBeenCalledWith(ownerCommand);

        // 3. Test permission restrictions for non-owners
        appServiceMock.isClientRoomOwner = vi.fn().mockReturnValue(false);

        chatService.runRoomOwnerChatCommand({
            moduleID: "",
            command: 'kick',
            roomOwnerOnly: true,
            disabled: false
        }, ['TestUser']);
        // Should not call emitRoomChatServerCommand again since we're no longer owner
        expect(websocketServiceMock.emitRoomChatServerCommand).toHaveBeenCalledTimes(1);

        // 4. Test moderator actions
        appServiceMock.isClientMod = vi.fn().mockReturnValue(true);

        const modCommand = '//mute TestUser';

        // Reset the mock implementation
        websocketServiceMock.emitRoomChatServerCommand = vi.fn();
        websocketServiceMock.emitRoomChatServerCommand = vi.fn().mockImplementation((command) => {
            expect(command).toBe(modCommand);
        });

        chatService.runServerModChatCommand({
            moduleID: "",
            command: 'mute',
            modOnly: true,
            disabled: false
        }, ['TestUser']);
        expect(websocketServiceMock.emitRoomChatServerCommand).toHaveBeenCalledWith(modCommand);
    });
})
;