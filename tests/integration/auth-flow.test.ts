// auth-flow.test.ts
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';
import {getSession} from '~/lib/server';
import {mockSessionStorage} from '../mocks/auth.mocks';
// Import after mocking
import {forgotPassword, handleOAuth2Response, login, register} from '~/lib';

// Mock the required modules
vi.mock('~/lib/server', async () => {
  return {
    getSession: vi.fn(),
    logout: vi.fn(),
    UserSessionHelper: {
      getHeaders: vi.fn(() => ({}))
    }
  };
});

vi.mock('solid-js/web', () => ({
  getRequestEvent: vi.fn(() => ({
    request: {
      headers: {
        get: vi.fn(),
      },
    },
  })),
  isServer: true,
}));

vi.mock('@solidjs/router', () => ({
  redirect: vi.fn(),
  json: vi.fn((data) => data),
  action: vi.fn((fn) => fn),
  query: vi.fn((fn) => fn),
  useNavigate: vi.fn(() => vi.fn()),
  useSearchParams: vi.fn(() => [{ at: 'test-token', rt: 'refresh-token', sessionID: 'session-id' }]),
}));

// Mock the lib functions directly
vi.mock('~/lib', () => ({
  register: vi.fn().mockImplementation(async () => ({ output: true })),
  login: vi.fn().mockImplementation(async () => ({
    token: 'jwt-token-123',
    refreshToken: 'refresh-token-123',
    username: 'testuser',
    usertag: '1234',
    roles: ['user']
  })),
  forgotPassword: vi.fn().mockImplementation(async () => ({ success: true })),
  handleOAuth2Response: vi.fn().mockImplementation(async () => {
    // This function will be implemented in the test
  }),
  logout: vi.fn(),
}));

describe('Authentication Flow Integration Tests', () => {
  let mockFetch: any;
  let sessionData: any;
  let sessionUpdateFn: any;

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup fetch mock
    mockFetch = vi.fn();
    mockFetch.mockImplementation(async (url: string, _: any) => {
      if (url.includes('/api/register')) {
        return {
          ok: true,
          json: async () => ({ output: true })
        };
      } else if (url.includes('/api/verify-email')) {
        return {
          ok: true,
          json: async () => ({ verified: true })
        };
      } else if (url.includes('/login')) {
        return {
          ok: true,
          json: async () => ({
            token: 'jwt-token-123',
            refreshToken: 'refresh-token-123',
            username: 'testuser',
            usertag: '1234',
            roles: ['user']
          })
        };
      } else if (url.includes('/forgot-password')) {
        return {
          ok: true,
          json: async () => ({ success: true })
        };
      } else if (url.includes('/reset-password')) {
        return {
          ok: true,
          json: async () => ({ success: true })
        };
      } else if (url.includes('/validate-session')) {
        return {
          ok: true,
          json: async () => ({
            valid: true,
            username: 'testuser',
            usertag: '1234',
            roles: ['user']
          })
        };
      } else if (url.includes('/refresh-token')) {
        return {
          ok: true,
          json: async () => ({
            accessToken: 'new-access-token-123',
            refreshToken: 'new-refresh-token-123'
          })
        };
      } else if (url.includes('/api/user-info')) {
        return {
          ok: true,
          json: async () => ({
            username: 'oauth-user',
            usertag: '5678',
            roles: ['user']
          })
        };
      }

      return {
        ok: false,
        json: async () => ({ error: 'Not found' })
      };
    });
    vi.stubGlobal('fetch', mockFetch);

    // Mock session storage
    mockSessionStorage();

    // Mock environment variables
    process.env.PIANORHYTHM_SERVER_URL = 'https://api.test.com';

    // Setup session data and mock
    sessionData = {
      accessToken: 'access-token-123',
      refreshToken: 'refresh-token-123',
      sessionToken: 'session-token-123',
    };

    sessionUpdateFn = vi.fn(async (updater) => {
      const newData = updater(sessionData);
      Object.assign(sessionData, newData);
      return sessionData;
    });

    (getSession as any).mockResolvedValue({
      data: sessionData,
      update: sessionUpdateFn
    });

    // Mock handleOAuth2Response implementation
    (handleOAuth2Response as any).mockImplementation(async (data: any) => {
      const session = await getSession();
      await session.update((d: any) => ({
        ...d,
        accessToken: data.at,
        refreshToken: data.rt || d.refreshToken,
        sessionToken: data.sessionID ? `rst-id=${data.sessionID}` : d.sessionToken,
      }));
    });
  });

  afterEach(() => {
    vi.unstubAllGlobals();
  });

  it('should register a new user and verify email flow', async () => {
    // Setup test user data
    const testUser = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'Password123!',
      password2: 'Password123!',
      tos: true
    };

    // Create form data for registration
    const formData = new FormData();
    Object.entries(testUser).forEach(([key, value]) => {
      formData.append(key, value.toString());
    });

    // Test registration
    const registerResult = await register(formData);
    expect(registerResult).toEqual({ output: true });

    // Simulate verification API call
    const verificationResult = await fetch('https://api.test.com/api/verify-email', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ token: 'verification-token-123' })
    }).then(res => res.json());

    expect(verificationResult).toEqual({ verified: true });

    // Test login with new credentials
    const loginFormData = new FormData();
    loginFormData.append('username', testUser.username);
    loginFormData.append('password', testUser.password);

    const loginResult = await login(loginFormData);

    expect(loginResult).toEqual({
      token: 'jwt-token-123',
      refreshToken: 'refresh-token-123',
      username: testUser.username,
      usertag: '1234',
      roles: ['user']
    });
  });

  it('should handle forgot password flow correctly', async () => {
    const testEmail = '<EMAIL>';

    // Create form data for forgot password
    const formData = new FormData();
    formData.append('email', testEmail);

    // Test forgot password request
    const forgotPasswordResult = await forgotPassword(formData);
    expect(forgotPasswordResult).toEqual({ success: true });

    // Simulate reset password API call
    const newPassword = 'NewPassword123!';
    const resetToken = 'reset-token-123';

    const resetPasswordResult = await fetch('https://api.test.com/api/reset-password', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        password: newPassword,
        confirmpassword: newPassword,
        token: resetToken
      })
    }).then(res => res.json());

    expect(resetPasswordResult).toEqual({ success: true });

    // Test login with new password
    const loginFormData = new FormData();
    loginFormData.append('username', 'testuser');
    loginFormData.append('password', newPassword);

    const loginResult = await login(loginFormData);

    expect(loginResult).toEqual({
      token: 'jwt-token-123',
      refreshToken: 'refresh-token-123',
      username: 'testuser',
      usertag: '1234',
      roles: ['user']
    });
  });

  it('should maintain session across page reloads', async () => {
    // Simulate checking session validity (as would happen on page load)
    const sessionCheckResult = await fetch('https://api.test.com/api/validate-session', {
      headers: {
        'Authorization': `Bearer ${sessionData.accessToken}`,
        'X-Session-Token': sessionData.sessionToken
      }
    }).then(res => res.json());

    expect(sessionCheckResult).toEqual({
      valid: true,
      username: 'testuser',
      usertag: '1234',
      roles: ['user']
    });

    // Simulate token refresh
    const refreshResult = await fetch('https://api.test.com/api/refresh-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${sessionData.accessToken}`
      },
      body: JSON.stringify({ refreshToken: sessionData.refreshToken })
    }).then(res => res.json());

    expect(refreshResult).toEqual({
      accessToken: 'new-access-token-123',
      refreshToken: 'new-refresh-token-123'
    });
  });

  it('should handle OAuth login flow correctly', async () => {
    // Mock OAuth response data
    const oauthData = {
      at: 'oauth-access-token',
      rt: 'oauth-refresh-token',
      sessionID: 'oauth-session-id'
    };

    // Reset session data to empty state
    Object.assign(sessionData, {
      accessToken: '',
      refreshToken: '',
      sessionToken: ''
    });

    // Test OAuth handler
    await handleOAuth2Response(oauthData);

    // Verify session was updated with OAuth tokens
    expect(sessionUpdateFn).toHaveBeenCalled();
    expect(sessionData).toEqual({
      accessToken: 'oauth-access-token',
      refreshToken: 'oauth-refresh-token',
      sessionToken: 'rst-id=oauth-session-id'
    });

    // Simulate fetching user info after OAuth login
    const userInfoResult = await fetch('https://api.test.com/api/user-info', {
      headers: {
        'Authorization': `Bearer ${sessionData.accessToken}`,
        'X-Session-Token': sessionData.sessionToken
      }
    }).then(res => res.json());

    expect(userInfoResult).toEqual({
      username: 'oauth-user',
      usertag: '5678',
      roles: ['user']
    });
  });
});