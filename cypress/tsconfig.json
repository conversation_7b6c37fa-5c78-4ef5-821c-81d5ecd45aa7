{
  "extends": "../tsconfig.json",
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    "strict": true,
    "allowJs": true,
    "lib": [
      "esnext",
      "dom",
      "webworker"
    ],
    "types": [
      "vite/client",
      "cypress",
    ],
    "sourceMap": false,
    "jsx": "preserve",
    "jsxImportSource": "solid-js",
    "noEmit": true,
    "isolatedModules": false,
    "moduleResolution": "node",
    "esModuleInterop": true,
    "experimentalDecorators": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
  },
  "exclude": [
    "node_modules/**/*",
    "pianorhythm_synth",
    "pianorhythm_core",
    "dist",
    "**/dist/*",
    "public",
    ".tauri",
    ".vscode",
    "src-tauri",
    "**/.vscode/*"
  ],
}