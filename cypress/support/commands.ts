/// <reference types="cypress" />

Cypress.Commands.add("setCoreServiceMethodStub", (fn: object) => {
  cy.window().its("coreService").should("exist");
  cy.window().its("setCoreService").then((setCoreService) => {
    setCoreService((c: any) => ({ ...c, ...fn }));
  });
});

Cypress.Commands.addQuery("setCoreServiceMethodStubCB", (fn: (cb: (v: unknown) => void) => object) => {
  return () => {
    return new Promise((resolve) => {
      cy.window().its("coreService").should("exist");
      cy.window().its("setCoreService").then((setCoreService) => {
        setCoreService((c: any) => ({ ...c, ...fn(resolve) }));
      });
    });
  };
});

Cypress.Commands.add("sendAppStateEvent", (event: number) => {
  cy.window().its("appStateEvents").should("exist");
  cy.window().its("appStateEvents").then((appStateEvents) => {
    appStateEvents?.emit(event);
  });
});

type SendAppStateEffect = {
  action: number;
  joinedRoomData?: any;
  instrumentsList?: any;
};

Cypress.Commands.add("sendAppStateEffect", (effect: SendAppStateEffect) => {
  cy.window().its("appStateEffects").should("exist");
  cy.window().its("appStateEffects").then((appStateEffects) => {
    appStateEffects?.emit(effect);
  });
});

Cypress.Commands.add("dispatchAppStateEffect", (effect: Uint8Array) => {
  cy.window().then((win) => {
    win.dispatchEvent(new CustomEvent("app_effects", { detail: effect }));
  });
});

Cypress.Commands.add("dispatchAppStateEvent", (event: number) => {
  cy.window().then((win) => {
    win.dispatchEvent(new CustomEvent("app_events", { detail: [event] }));
  });
});

Cypress.Commands.add("protobufEncodeAppStateEffect", (data: object, onComplete?: (encoded: Uint8Array) => void) => {
  cy.task('protobufEncode', {
    fixtureBody: data,
    message: "PianoRhythm.AppStateEffects.AppStateEffects",
    protoFilePath: "./pianorhythm_core/proto/raw/pianorhythm-effects.proto",
  }).then((encoded: any) => {
    onComplete?.(encoded.data);
    cy.dispatchAppStateEffect(encoded.data);
  });
});

Cypress.Commands.add("LoginReturnError", () => {
  cy.intercept('POST', '/_server', (req) => {
    if (req.headers["x-server-id"]?.includes("$$function4")) req.reply(500, undefined);
  });
});

Cypress.Commands.add("LoginReturnSuccess", () => {
  // Get Member Session Info
  cy.intercept('GET', '/_server/**', (req) => {
    if (req.headers["x-server-id"]?.includes("$$function2")) req.reply(200, {});
  });

  // Login
  cy.intercept('POST', '/_server', (req) => {
    if (req.headers["x-server-id"]?.includes("$$function4")) req.reply(200, {
      username: "dummy",
      usertag: "1234",
      token: "123456",
      roles: [5],
    });
  });
});

const emptyBase64Img = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg==";

const COMMON_HEADERS = {
  "cross-origin-opener-policy": "same-origin",
  "cross-origin-embedder-policy": "require-corp",
  "cross-origin-resource-policy": "cross-origin",
  "x-pianorhythm-client-version": "0.9.10",
  "x-pianorhythm-latest-client-version": "0.9.10",
  "content-type": "text/javascript",
};

const calculateByteLength = (str: string) => new Blob([str]).size;
const decimalToHex = (d: number) => d.toString(16).padStart(8, '0');

Cypress.Commands.add("onHandleServerFunction", (key: string, value: any, statusCode = 200) => {
  cy.intercept('POST', '/_server', (req) => {
    let serverID = req.headers["x-server-id"];
    let serverInstance = req.headers["x-server-instance"];

    let generateFunctionValue = (value: any) => {
      let output = `((self.$R=self.$R||{})["${serverInstance}"]=[],${value})`;
      return `;0x${decimalToHex(calculateByteLength(output))};${output}`;
    };

    if (serverID?.includes(key)) {
      req.reply(statusCode, generateFunctionValue(value), COMMON_HEADERS);
    }
  });
});

Cypress.Commands.add("stubGetPlayersOnline", (value: number = 0) => {
  cy.onHandleServerFunction("getPlayersOnline", value);
});

Cypress.Commands.add("EnterRoom", () => {
  cy.LoginReturnSuccess();

  cy.intercept('GET', '**/soundfonts/**', (req) => {
    req.reply(200, new ArrayBuffer(100), {
      'content-length': '0',
      'content-type': 'application/octet-stream'
    });
  });

  cy.intercept('POST', '/sync', (req) => {
    let { id } = req.body;
    req.reply(200, { "jsonrpc": "2.0", "id": id, "result": Date.now() });
  });

  // Authentication
  cy.intercept('GET', '/_server/**', (req) => {
    if (req.headers["x-server-id"]?.includes("$$function1")) req.reply(200, {});
  });

  cy.intercept('GET', 'http://localhost:7000/api/users/profile-background-image/**', (req) => {
    req.reply(200, emptyBase64Img);
  });

  cy.intercept('POST', '/_server', (req) => {
    let serverID = req.headers["x-server-id"];
    let serverInstance = req.headers["x-server-instance"];

    let generateFunctionValue = (value: any) => {
      if (typeof value === "object") value = JSON.stringify(value);
      let output = `((self.$R=self.$R||{})["${serverInstance}"]=[],${value})`;
      return `;0x${decimalToHex(calculateByteLength(output))};${output}`;
    };

    const responses: { [key: string]: any; } = {
      "getWebsocketIdentity": generateFunctionValue(({ "identity": "123456" })),
      "getCrownImage": generateFunctionValue(`"${emptyBase64Img}"`),
      "getAssetImage": generateFunctionValue(`"${emptyBase64Img}"`),
      "getUserProfileImage": generateFunctionValue(`"${emptyBase64Img}"`),
      "getPlayersOnline": generateFunctionValue(5)
    };

    for (const key in responses) {
      if (serverID?.includes(key)) {
        req.reply(200, responses[key], COMMON_HEADERS);
        break;
      }
    }
  });

  cy.visit('/');
  cy.contains("Enter").click();
  cy.get('div[role="status"]').should('contain', 'Initializing');

  cy.setCoreServiceMethodStub({
    websocket_connect: async (_: any, onConnect: () => void) => { onConnect(); }
  });

  cy.setCoreServiceMethodStub(({
    synth_load_soundfont: async () => { }
  }));

  cy.window().its("websocketEvents").should("exist");
  cy.window().its("websocketEvents")
    .then((websocketEvents) => {
      return new Promise((resolve) => {
        websocketEvents?.listen((event: any) => {
          if (event == 'connected') resolve(event);
        });
      });
    })
    .then(() => {
      cy.sendAppStateEvent(12); //AddedClientSynthUser

      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          cy.protobufEncodeAppStateEffect({
            action: 18, //OnWelcome
            welcomeDto: {
              userClientDto: user,
              settings: undefined
            }
          });

          cy.contains("Parsing soundfont", { timeout: 10_000 * 3 }).should("be.visible");
          cy.wait(250);
          cy.protobufEncodeAppStateEffect({
            action: 39, //SoundfontPresetsLoaded
            instrumentsList: {
              instruments: []
            }
          });

          cy.contains('Initialization complete! Joining room...').should('be.visible');
          cy.dispatchJoinedRoomSuccess('testroom');

          cy.contains('Initialization complete! Joining room...', { timeout: 30 * 1000 }).should('not.exist');
          cy.get('div[id="pageLoaderTitle"]').should('not.exist');
          cy.addUser(user);
        });
    });
});

Cypress.Commands.add("dispatchJoinedRoomSuccess", (roomName: string) => {
  cy.protobufEncodeAppStateEffect({
    action: 5, //JoinedRoomSuccess
    joinedRoomData: {
      roomID: '123456',
      roomName: roomName,
      roomType: 6,
      roomStatus: 5,
      roomSettings: undefined,
      roomOwner: 'system'
    }
  }, () => {
    cy.dispatchAppStateEvent(2002);
  });
});

Cypress.Commands.add("setUsers", (users: object[] = []) => {
  cy.protobufEncodeAppStateEffect({
    action: 4,
    clientSideUserDtoList: { list: users ?? [] }
  });
});

Cypress.Commands.add("addUser", (user: object) => {
  if (!user) return;

  cy.protobufEncodeAppStateEffect({
    action: 2,
    clientSideUserDto: user
  });
});

Cypress.Commands.add("addClientToRoom", () => {
  cy.fixture('client_side_user_dto.json')
    .then((user) => {
      cy.addUser(user);
    });
});

Cypress.Commands.add("addRoom", (room: object) => {
  if (!room) return;

  cy.protobufEncodeAppStateEffect({
    action: 11, //AddRoom
    basicRoomDto: room
  });
});

Cypress.Commands.add("setRooms", (rooms: any[]) => {
  if (!rooms) return;

  cy.protobufEncodeAppStateEffect({
    action: 9, //SetRooms
    roomsList: {
      list: rooms
    }
  });
});

Cypress.Commands.add("addRoomByDto", (modifyRoom?: (object: object) => (object)) => {
  cy.fixture('normal_room_dto.json')
    .then((fixture) => {
      let newRoom = modifyRoom ? modifyRoom(fixture) : fixture;
      cy.addRoom(newRoom);
    });
});

Cypress.Commands.add("updateRoom", (room: object) => {
  if (!room) return;
  cy.protobufEncodeAppStateEffect({
    action: 12, //UpdateRoom
    basicRoomDto: room
  });
});

Cypress.Commands.add("deleteRoom", (roomId: string) => {
  if (!roomId) return;
  cy.protobufEncodeAppStateEffect({
    action: 13, //UpdateRoom
    roomId
  });
});

Cypress.Commands.add("addLobby", (name?: string) => {
  cy.fixture('lobby_room_dto.json')
    .then((room) => {
      if (name) room.roomName = name;
      cy.addRoom(room);
    });
});

export { };

declare global {
  namespace Cypress {
    interface Chainable {
      setCoreServiceMethodStub(fn: object): Chainable<void>;
      sendAppStateEvent(event: number): Chainable<void>;
      sendAppStateEffect(effect: SendAppStateEffect): Chainable<void>;
      dispatchAppStateEffect(effect: Uint8Array): Chainable<void>;
      dispatchAppStateEvent(event: number): Chainable<void>;
      protobufEncodeAppStateEffect(data: object, onComplete?: (encoded: Uint8Array) => void): Chainable<void>;
      setCoreServiceMethodStubCB(fn: (cb: (v: unknown) => void) => object): Chainable<void>;
      setUsers(users?: object[]): Chainable<void>;
      dispatchJoinedRoomSuccess(roomName: string): Chainable<void>;
      addClientToRoom(): Chainable<void>;
      addUser(user: object): Chainable<void>;
      EnterRoom(): Chainable<void>;
      LoginReturnError(): Chainable<void>;
      LoginReturnSuccess(): Chainable<void>;
      addRoom(room: object): Chainable<void>;
      updateRoom(room: object): Chainable<void>;
      deleteRoom(roomId: string): Chainable<void>;
      addLobby(name?: string): Chainable<void>;
      addRoomByDto(modifyRoom?: (object: any) => (object)): Chainable<object>;
      setRooms(rooms: any[]): Chainable<void>;
      onHandleServerFunction(key: string, value: any, statusCode?: number): Chainable<void>;
      stubGetPlayersOnline(value?: number): Chainable<void>;
    }
  }
}