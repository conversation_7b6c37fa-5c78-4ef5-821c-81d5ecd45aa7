describe('Room Page E2E Tests', () => {
  beforeEach(() => {
    cy.stubGetPlayersOnline();
    cy.EnterRoom();
  });

  describe('Smoke Test', () => {
    it.skip('validate page elements', () => {
      cy.EnterRoom();
    });
  });

  describe('Users List', () => {
    it('should display added user', () => {
      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          cy.addUser(user);
          cy.contains(user.userDto.nickname).should('be.visible');
        });
    });

    it('should display list of users', () => {
      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          let list = [];
          list.push(user);

          let targetAmount = Math.max(2 - list.length, 0);
          for (let i = 0; i < targetAmount; i++) {
            let newUser = structuredClone({ ...user });
            newUser.userDto.nickname = newUser.userDto.nickname + i;
            newUser.userDto.username = newUser.userDto.username + i;
            newUser.userDto.socketID = newUser.userDto.socketID + i;
            newUser.userDto.usertag = newUser.userDto.usertag + i;
            newUser.socketIDHashed = newUser.socketIDHashed + i;
            newUser.socketID = newUser.userDto.socketID;
            list.push(newUser);
          }
          cy.setUsers(list);
          cy.get("div[id^='pr-sidebar-user-element-']").should('have.length', list.length);
        });
    });

    it('should display mini profile card on hover', () => {
      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          cy.addUser(user);
          cy.get("div[id^='pr-sidebar-user-element-']").trigger('mouseover');
          cy.get("div[id^='user-mini-profile-card-']").should('be.visible');
        });
    });

    it('should hide mini profile card on mouse out', () => {
      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          cy.addUser(user);
          cy.get("div[id^='pr-sidebar-user-element-']").trigger('mouseover');
          cy.get("div[id^='user-mini-profile-card-']").should('be.visible');
          cy.get("div[id^='pr-sidebar-user-element-']").trigger('mouseleave');
          cy.get("div[id^='user-mini-profile-card-']").should('not.exist');
        });
    });

    it('should hide mini profile card on click', () => {
      cy.fixture('client_side_user_dto.json')
        .then((user) => {
          cy.addUser(user);
          cy.get("div[id^='pr-sidebar-user-element-']").trigger('mouseover');
          cy.get("div[id^='user-mini-profile-card-']").should('be.visible');
          cy.get("div[id^='pr-sidebar-user-element-']").click();
          cy.get("div[id^='user-mini-profile-card-']").should('not.exist');
        });
    });
  });

  describe('Rooms List', () => {
    it('should display added room', () => {
      let targetRoomName = 'Test Room';
      cy.addRoomByDto((room) => {
        room.roomName = targetRoomName;
        return room;
      });

      cy.get("#sidebar-tab-rooms").click();
      cy.contains(targetRoomName).should('be.visible');
    });

    it('should display list of rooms', () => {
      let list = [];
      let targetAmount = 2;

      cy.fixture('normal_room_dto.json')
        .then((normalRoomDto) => {
          for (let i = 0; i < targetAmount; i++) {
            let newRoom = structuredClone(normalRoomDto);
            newRoom.roomName = `Test Room ${i}`;
            newRoom.roomID = `room-${i}`;
            list.push(newRoom);
          }
          cy.setRooms(list);
          cy.get("#sidebar-tab-rooms").click();
          cy.get("div[id^='pr-sidebar-room-element-']").should('have.length', list.length);
        });
    });

    it('should display mini profile card on hover', () => {
      let targetRoomName = 'Test Room';
      cy.addRoomByDto((room) => {
        room.roomName = targetRoomName;
        return room;
      });

      cy.get("#sidebar-tab-rooms").click();
      cy.get("div[id^='pr-sidebar-room-element-']").trigger('mouseover');
      cy.get("div[id^='room-mini-profile-card-']").should('be.visible');
    });

    it('should hide mini profile card on mouse out', () => {
      let targetRoomName = 'Test Room';
      cy.addRoomByDto((room) => {
        room.roomName = targetRoomName;
        return room;
      });

      cy.get("#sidebar-tab-rooms").click();
      cy.get("div[id^='pr-sidebar-room-element-']").trigger('mouseover');
      cy.get("div[id^='room-mini-profile-card-']").should('be.visible');
      cy.get("div[id^='pr-sidebar-room-element-']").trigger('mouseleave');
      cy.get("div[id^='room-mini-profile-card-']").should('not.exist');
    });
  });

  describe('Bottom Bar', () => {
    it('should display bottom bar', () => {
      cy.get('*[class^="_bottomBar"]').should('be.visible');
    });

    it('should have newRoom button', () => {
      cy.get('*[name^="bottomBarButton-newRoom"]').should('be.visible');
    });

    it('should have settings button', () => {
      cy.get('*[name^="bottomBarButton-settings"]').should('be.visible');
    });

    it('should have midi button', () => {
      cy.get('*[name^="bottomBarButton-midi"]').should('be.visible');
    });

    it('should have volume button', () => {
      cy.get('*[name^="bottomBarButton-volume"]').should('be.visible');
    });

    it('should have sustain button', () => {
      cy.get('*[name^="bottomBarButton-sustain"]').should('be.visible');
    });

    it('should have logout button', () => {
      cy.get('*[name^="bottomBarButton-logout"]').should('be.visible');
    });

    context('Logout Button', () => {
      it('should logout user', () => {
        cy.get('*[name^="bottomBarButton-logout"]').click();
        cy.get('.swal2-popup').should('be.visible');
        cy.get('.swal2-confirm').click();
        cy.url().should('include', '/login');
      });
    });

    context('New Room Button', () => {
      it('should create new room', () => {
        const targetRoomName = 'Test Room';
        cy.get('*[name^="bottomBarButton-newRoom"]').click();
        cy.get('.hope-modal__content').should('be.visible');
        cy.get('input[placeholder="name"]').clear().type(targetRoomName);
        cy.get('.hope-button').contains('Create Room').click();
        cy
          .dispatchJoinedRoomSuccess(targetRoomName)
          .addClientToRoom();
        cy.url().should('include', encodeURI('/room/' + targetRoomName));
        cy.get("div[id^='pr-sidebar-user-element-']").should('have.length', 1);
      });
    });
  });
});