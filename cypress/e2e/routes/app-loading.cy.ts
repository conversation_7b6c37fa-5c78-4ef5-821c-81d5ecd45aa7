describe('AppLoading E2E Tests', () => {
  beforeEach(() => {
    cy.stubGetPlayersOnline();
  });

  before(() => {
    Cypress.on('uncaught:exception', (_err, _runnable) => {
      return false;
    });
  });

  describe('Default', () => {
    beforeEach(() => {
      cy.visit('/app-loading');
    });

    it("should not proceed without user login", () => {
      cy.url().should('include', '/login');
    });
  });

  describe('Success', () => {
    it('should navigate to the lobby after initialization', () => {
      cy.EnterRoom();
    });
  });
});