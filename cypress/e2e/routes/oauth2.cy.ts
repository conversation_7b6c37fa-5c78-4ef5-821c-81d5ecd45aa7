describe('OAuth2 Route', () => {
  beforeEach(() => {
    cy.stubGetPlayersOnline();
  });

  it('should show notification and navigate to home on OAuth login failure', () => {
    cy.visit('/oauth2');
    cy.get('div[role="status"]').should('contain', 'Login failed');
    cy.url().should('eq', `${Cypress.config().baseUrl}/`);
  });

  it('should handle OAuth response correctly and start initializing ', () => {
    cy.visit('/oauth2?success=true&at=accessToken&rt=refreshToken&sessionID=sessionID&roomName=testRoom');
    cy.url().should('include', '/app-loading');
    cy.get('div[role="status"]').should('contain', 'Initializing');
  });
});