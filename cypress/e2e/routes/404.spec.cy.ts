describe('404 Route', () => {
  beforeEach(() => {
    cy.stubGetPlayersOnline();
  });

  it('Validate Page', () => {
    cy.visit("/unknown_route");
    cy.contains('Page Not Found').should('exist');
    cy.contains('Go Home').should('exist');
  });

  it('Should go to Main Form', () => {
    cy.visit("/unknown_route");
    cy.contains('Go Home').click();
    cy.contains('Register').should('exist');
    cy.contains('Login').should('exist');
  });
});