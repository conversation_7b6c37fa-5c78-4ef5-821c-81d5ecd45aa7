describe('Forgot Password Route', () => {
  const forgotPasswordFunctionID = 'function8';

  beforeEach(() => {
    cy.stubGetPlayersOnline();
    cy.visit("/forgot-password");
  });

  it('Validate fields', () => {
    cy.get('button').contains('Submit').should('be.disabled');
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('button').contains('Submit').should('not.be.disabled');
  });

  it('Submit form - Invalid Field', () => {
    cy.get('input[name="email"]').type('dummy');
    cy.get('button').contains('Submit').should('be.disabled');
  });

  it('Submit form - Success', () => {
    cy.intercept('POST', '/_server', (req) => {
      if (req.headers["x-server-id"]?.includes(forgotPasswordFunctionID)) req.reply(200, {});
    });

    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('button').contains('Submit').click();
    cy.contains('Forgot Password Complete').should('exist');
  });

  it('Submit form - Error', () => {
    cy.intercept('/_server', (req) => {
      if (req.headers["x-server-id"]?.includes(forgotPasswordFunctionID)) req.reply(200,
        `;eval('');`, { "content-type": "text/javascript" });
    });

    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('button').contains('Submit').click();
    cy.contains('Forgot Password form Failed').should('exist');
  });

  it('Navigate to Register', () => {
    cy.get('a').contains('Create an account').click();
    cy.url().should('include', '/register');
  });

  it('Navigate to Login', () => {
    cy.get('a').contains('Sign in').click();
    cy.url().should('include', '/login');
  });

  it('Navigate to Home', () => {
    cy.get('a').contains('Enter as a guest').click();
    cy.url().should('include', '/');
  });
});