describe('Resend Verification Route', () => {
  const targetEmail = "<EMAIL>";
  const resendVerificationFunctionID = "function9";

  beforeEach(() => {
    cy.stubGetPlayersOnline();
    cy.visit("/resend-verification");
  });

  it('Validate fields', () => {
    cy.get('button').contains('Submit').should('be.disabled');
    cy.get('input[name="email"]').type(targetEmail);
    cy.get('button').contains('Submit').should('not.be.disabled');
  });

  it('Submit form - Success', () => {
    cy.intercept('POST', '/_server', (req) => {
      if (req.headers["x-server-id"]?.includes(resendVerificationFunctionID)) req.reply(200, {});
    });

    cy.get('input[name="email"]').type(targetEmail);
    cy.get('button').contains('Submit').click();
    cy.contains('Email Verification').should('exist');;
  });

  it('Submit form - Error', () => {
    cy.intercept('/_server', (req) => {
      if (req.headers["x-server-id"]?.includes(resendVerificationFunctionID)) req.reply(200,
        `;eval('');`, { "content-type": "text/javascript" });
    });

    cy.get('input[name="email"]').type(targetEmail);
    cy.get('button').contains('Submit').click();
    cy.contains('Resend Verification Form Failed').should('exist');
    cy.get('button').contains('Submit').should('not.be.disabled');

    cy.get('input[name="email"]').should('have.value', targetEmail);
  });

  it('Navigate to Register', () => {
    cy.get('a').contains('Create an account').click();
    cy.url().should('include', '/register');
  });

  it('Navigate to Login', () => {
    cy.get('a').contains('Sign in').click();
    cy.url().should('include', '/login');
  });

  it('Navigate to Home', () => {
    cy.get('a').contains('Enter as a guest').click();
    cy.url().should('include', '/');
  });
});
