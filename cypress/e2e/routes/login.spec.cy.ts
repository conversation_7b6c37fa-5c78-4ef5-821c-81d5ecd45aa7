describe('Login Route', () => {
  const targetUsername = 'dummy';
  const targetPassword = 'password';
  const registrationHrefName = 'Create an account';
  const forgotPasswordHrefName = 'Forgot password';
  const guestHrefName = 'Enter as a guest';
  const resendVerificationHrefName = 'Resend verification email';

  beforeEach(() => {
    cy.stubGetPlayersOnline();
    cy.visit("/");
    cy.LoginReturnSuccess();
  });

  describe('Main Form', () => {
    it('should have a nickname in the input field', () => {
      cy.get('input[name="nickname"]').type(targetUsername);
      cy.get('input[name="nickname"]').should('have.value', targetUsername);
    });

    it('should enter as a guest', () => {
      cy.get('button').contains('Enter').should('exist');
      cy.get('button').contains('Enter').click();
      cy.url().should('include', '/app-loading');
    });

    it.only('should show error message if login fails', () => {
      cy.LoginReturnError();
      cy.get('button').contains('Enter').should('exist');
      cy.get('button').contains('Enter').click();
      cy.contains('Error').should('exist');
    });
  });

  describe('Registration Form', () => {
    it('Validate fields', () => {
      cy.contains('Register').click();

      // Wait for the form to load
      cy.get('form').should('be.visible');

      // Test input fields
      cy.get('input[name="username"]').type('dummy');

      const targetEmail = '<EMAIL>';
      cy.get('input[name="email"]').type(targetEmail);
      cy.get('input[name="email"]').should('have.value', targetEmail);

      const targetPassword = 'password';
      cy.get('input[name="password"]').type(targetPassword);
      cy.get('input[name="password2"]').type(targetPassword);
      cy.get('input[name="password"]').should('have.value', targetPassword);
      cy.get('input[name="password2"]').should('have.value', targetPassword);

      // Click on TOS check box
      cy.get('.hope-checkbox__control').click();
      cy.get('button').contains('Register').should('exist');
      cy.get('button').contains('Register').should('not.be.disabled');
    });

    it('Should go to Login Form', () => {
      cy.contains('Register').click();
      cy.get('form').should('be.visible');
      cy.contains('Sign in').click();
      cy.get('form').should('be.visible');
      cy.get('input[autocomplete="username"]').type(targetUsername);
      cy.get('input[autocomplete="current-password"]').type(targetPassword);
      cy.get('button').contains('Submit').should('exist');
    });

    it('Should go to Main Form', () => {
      cy.contains('Register').click();
      cy.get('form').should('be.visible');
      cy.contains('Enter as a guest').click();
      cy.get('button').contains('Enter').should('exist');
      cy.get('button').contains('Register').should('exist');
      cy.get('button').contains('Login').should('exist');
    });
  });

  describe('Login Form', () => {
    it('Validate fields', () => {
      cy.contains('Login').click();

      // Wait for the form to load
      cy.get('form').should('be.visible');

      // Test input fields
      cy.get('input[autocomplete="username"]').type('dummy');
      cy.get('input[autocomplete="current-password"]').type('password');
    });

    it('Should go to Registration Form', () => {
      cy.contains('Login').click();
      cy.get('form').should('be.visible');
      cy.contains(registrationHrefName).click();
      cy.get('form').should('be.visible');
      cy.get('button').contains('Register').should('exist');
    });

    it('Should go to Main Form', () => {
      cy.contains('Login').click();
      cy.get('form').should('be.visible');
      cy.contains(guestHrefName).click();
      cy.get('button').contains('Enter').should('exist');
      cy.get('button').contains('Register').should('exist');
      cy.get('button').contains('Login').should('exist');
    });

    it('Should go to Forgot Password Form', () => {
      cy.contains('Login').click();
      cy.get('form').should('be.visible');
      cy.contains(forgotPasswordHrefName).click();
      cy.get('form').should('be.visible');
      cy.contains('Forgot password').should('exist');
      cy.get('button').contains('Submit').should('exist');
    });

    it('Should go to Resend Verification Form', () => {
      cy.contains('Login').click();
      cy.get('form').should('be.visible');
      cy.contains(resendVerificationHrefName).click();
      cy.get('form').should('be.visible');
      cy.contains('Resend verification email').should('exist');
      cy.get('button').contains('Submit').should('exist');
    });
  });
});