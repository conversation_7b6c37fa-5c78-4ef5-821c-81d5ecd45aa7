describe('Register Route', () => {
  const targetUsername = 'dummy';
  const targetPassword = 'password';
  const targetEmail = '<EMAIL>';
  const registerFunctionID = 'function7';

  beforeEach(() => {
    cy.stubGetPlayersOnline();
    cy.visit("/register");
  });

  function enterRegistrationInfo() {
    cy.get('form').should('be.visible');
    cy.get('button').contains('Register').should('be.disabled');
    cy.get('input[name="username"]').type(targetUsername);
    cy.get('input[name="email"]').type(targetEmail);
    cy.get('input[name="password"]').type(targetPassword);
    cy.get('input[name="password2"]').type(targetPassword);
    cy.get('.hope-checkbox__control').click();
    cy.get('button').contains('Register').click();
  }

  it('should succesfully login and tell user they can login if email is already verified', () => {
    cy.onHandleServerFunction(registerFunctionID, true).as('register');
    enterRegistrationInfo();
    cy.get('div[role="status"]').should('contain', 'Registration Success');
    cy.get('div[role="status"]').should('contain', 'You may now login');
  });

  it('should succesfully login and tell user verification email will be sent if email is not yet verified', () => {
    cy.onHandleServerFunction(registerFunctionID, false).as('register');
    enterRegistrationInfo();
    cy.get('div[role="status"]').should('contain', 'Registration Success');
    cy.get('div[role="status"]').should('contain', 'verification email has been sent');
  });

  it('should not succesfully login', () => {
    cy.onHandleServerFunction(registerFunctionID, {}).as('register');
    enterRegistrationInfo();
    cy.get('div[role="status"]').should('contain', 'Registration Failed');
  });
});