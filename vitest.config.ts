import solid from "vite-plugin-solid";
import path from 'path';
import { defineConfig } from "vitest/config";
import replace from '@rollup/plugin-replace';
import { configDefaults } from 'vitest/config'

export default defineConfig({
  test: {
    globals: true,
    // environment: 'jsdom',
    // deps: {
    //   optimizer: {
    //     web: {
    //       enabled: false,
    //     }
    //   }
    // },
    setupFiles: [
      '@vitest/web-worker',
      'fake-indexeddb/auto',
      './tests/vitest.setup.ts'
    ],
    server: {
      deps: {
        inline: [
          "solid-markdown"
          // "@solid-primitives/local-store"
        ]
      }
    },
    exclude: [
      ...configDefaults.exclude,
      "**/__tests__/**",
      "**/__mocks__/**",
      "**/pianorhythm_core/**",
      "**/cypress/**",
      "**/build/**",
      "**/public/**",
      "**/node_modules/**",
      "**/dist/**",
      "**/build/**",
      "**/coverage/**",
      "**/out/**",
      "**/lib/**",
      "**/es/**",
    ],
  },
  plugins: [
    solid({}),
    replace({
      preventAssignment: true,
      __DATE__: Date.now(),
      __APP_VERSION__: "0.0.0"
    })
  ],
  resolve: {
    alias: {
      '~': path.resolve(__dirname, './src'),
      '@core': path.resolve(__dirname, './pianorhythm_core'),
      '@test': path.resolve(__dirname, './tests'),
    },
    // conditions: ["development", "browser"],
  },
});
