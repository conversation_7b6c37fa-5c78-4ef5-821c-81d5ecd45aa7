# AudioWorkletNode-Based MIDI Scheduling Solution

## Problem Statement

Your piano app currently uses `gloo_timers::callback::Timeout` for scheduling MIDI notes, which has significant timing precision issues:

- **Imprecise timing**: JavaScript timers have ~4-16ms jitter
- **Main thread blocking**: Timers run on main thread, subject to UI blocking
- **No audio synchronization**: Not aligned with audio processing timeline
- **High overhead**: One timer per MIDI event

For a piano app where timing is critical, this leads to noticeable timing drift and poor musical precision.

## Solution: AudioWorkletNode-Based Scheduling

The solution leverages your existing AudioWorkletNode to provide **sample-accurate MIDI scheduling** with sub-millisecond precision.

### Key Improvements

1. **Precision**: ~0.023ms (1 sample @ 44.1kHz) vs ~4-16ms jitter
2. **Real-time thread**: Audio thread has real-time priority
3. **Audio synchronization**: Events scheduled relative to audio context time
4. **Batch processing**: Multiple events in single message
5. **No main thread blocking**: Scheduling happens in audio thread

## Implementation Overview

### 1. Audio Scheduler (`audio_scheduler.rs`)

```rust
pub struct AudioContextScheduler {
    audio_worklet_node: Option<AudioWorkletNode>,
    sample_rate: f32,
}

impl AudioContextScheduler {
    pub fn schedule_midi_event(&self, delay_ms: f64, note: ScheduledNote);
    pub fn schedule_midi_batch(&self, notes: Vec<(f64, ScheduledNote)>);
}
```

### 2. AudioWorkletProcessor (`wasm-audio-worklet-processor.js`)

```javascript
class WasmProcessor extends AudioWorkletProcessor {
    process(inputs, outputs, parameters) {
        // Process scheduled events with sample-accurate timing
        this.processScheduledEvents(bufferLength);
        
        // Process audio through WASM
        this.wasmProcessor.process_stereo(leftBuffer, rightBuffer);
        
        return true;
    }
    
    processScheduledEvents(bufferLength) {
        const endSample = this.currentSample + bufferLength;
        
        // Execute events that are due in this buffer
        while (this.scheduledEvents.length > 0 && 
               this.scheduledEvents[0].targetSample < endSample) {
            this.executeScheduledEvent(this.scheduledEvents.shift());
        }
    }
}
```

### 3. Updated MIDI Handler (`wasm_handle_ws_midi.rs`)

```rust
impl<'c> HandleWebsocketMidiMessage for WasmHandleMidiMessage<'c> {
    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        // Use audio-context-synchronized scheduling instead of timeouts
        let notes_with_timing: Vec<(f64, ScheduledNote)> = batch.notes
            .into_iter()
            .map(|note| (note.delay_ms, note))
            .collect();

        // Schedule the entire batch using sample-accurate timing
        schedule_midi_batch_global(notes_with_timing);
    }
}
```

## Technical Benefits

### Timing Precision Comparison

| Approach | Precision | Thread | Synchronization | Overhead |
|----------|-----------|--------|-----------------|----------|
| setTimeout | ~4-16ms jitter | Main thread | Wall clock | High (1 timer/event) |
| AudioWorklet | ~0.023ms | Audio thread | Audio context | Low (batch messages) |

### Performance Improvements

1. **200-700x better timing precision**
2. **Reduced CPU overhead** from fewer timer objects
3. **Better memory usage** with batch processing
4. **No main thread blocking** for time-critical events
5. **Consistent latency** regardless of UI activity

## Usage Example

```rust
// Old approach (in wasm_handle_ws_midi.rs)
let timeout = gloo_timers::callback::Timeout::new(delay_ms, move || {
    for note in notes {
        Self::execute_note_optimized(note);
    }
});
timeout.forget(); // Creates timing jitter

// New approach
schedule_midi_batch_global(notes_with_timing); // Sample-accurate
```

## Integration Steps

1. **Initialize scheduler** when AudioWorkletNode is created
2. **Register AudioWorkletProcessor** with sample-accurate scheduling
3. **Replace timeout calls** with audio scheduler calls
4. **Test timing precision** with provided test suite

## Testing

The solution includes comprehensive tests in `audio_scheduler_test.rs`:

- Scheduler initialization
- MIDI event serialization
- Timing precision comparison
- Batch scheduling efficiency
- Integration with existing MIDI handler

## Browser Compatibility

- **Chrome/Edge**: Full support (AudioWorklet since v66)
- **Firefox**: Full support (AudioWorklet since v76)
- **Safari**: Full support (AudioWorklet since v14.1)

## Migration Path

1. **Phase 1**: Add AudioWorkletProcessor and scheduler
2. **Phase 2**: Update MIDI handler to use scheduler
3. **Phase 3**: Remove timeout-based scheduling
4. **Phase 4**: Optimize for your specific use cases

## Expected Results

After implementing this solution, you should see:

- **Dramatically improved timing precision** for MIDI notes
- **More consistent musical timing** across different devices
- **Better performance** under heavy UI load
- **Reduced audio dropouts** during complex sequences
- **Professional-grade timing** suitable for serious musical applications

## Files Modified/Created

- `pianorhythm_core/core/src/midi/audio_scheduler.rs` - New scheduler implementation
- `pianorhythm_core/synth/src/wasm-audio-worklet-processor.js` - AudioWorkletProcessor
- `pianorhythm_core/core/src/midi/wasm_handle_ws_midi.rs` - Updated to use scheduler
- `pianorhythm_core/synth/src/pianorhythm_synth.rs` - AudioWorklet registration
- `pianorhythm_core/core/src/midi/audio_scheduler_test.rs` - Test suite

This solution transforms your piano app from using imprecise JavaScript timers to professional-grade, sample-accurate MIDI scheduling that's suitable for serious musical applications.
