// @refresh reload
import { create<PERSON><PERSON><PERSON>, StartServer } from "@solidjs/start/server";
import dns from 'node:dns';
import { COMMON } from "./util/const.common";
import { Database } from "./lib/db/db-store";

dns.setDefaultResultOrder('ipv4first');

console.log("🚀 Starting server with env:", process.env.BUILD_ENV ?? process.env.NODE_ENV);
console.log("✔ API Server:", process.env.PIANORHYTHM_SERVER_URL);
console.log("✔ Assets Server:", process.env.PR_ASSETS_URL);
console.log("✔ Client Version:", process.env.PR_CLIENT_VERSION);

Database.getInstance().init();

export default createHandler(() => {
  return <StartServer
    document={({ assets, children, scripts }) => (
      <html lang="en">
        <head>
          <meta charset="utf-8" />
          <meta name="viewport" content="width=device-width, height=device-height, initial-scale=1" />
          <meta name="keywords"
            content="HTML5, WEBGL, Piano, Rust, Bevy, PianoRhythm, PR, Multiplayer, Game, Music, Synthesizer, Instruments" />
          <meta name="description"
            content="PianoRhythm is a multiplayer social web app to play music and casual games with people all over the world!" />
          <meta name="theme-color" content="#363942" />
          <meta name="author" content="Oak" />
          <meta name="app-version" content={COMMON.CLIENT_VERSION} />
          <link rel="icon" href="/favicon.ico" />

          {/* <!-- Open Graph Tags --> */}
          <meta property="og:url" content={COMMON.FULL_HOST} />
          <meta property="og:type" content="website" />
          <meta property="og:title" content="PianoRhythm" />
          <meta property="og:description"
            content="PianoRhythm is a multiplayer social web app to play music and casual games with people all over the world!" />
          <meta property="og:image" content="https://assets.pianorhythm.io/images/logo.png" />

          {assets}
        </head>
        <body>
          <noscript>Hey, you need to enable JavaScript to run this app!</noscript>
          <div id="root">{children}</div>
          {scripts}
        </body>
      </html>
    )}
  />;
});
