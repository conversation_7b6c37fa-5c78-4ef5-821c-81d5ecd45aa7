import AppSettingsService from "~/services/settings-storage.service";
// import SoundEffectsService from "@services/sound-effects.service";
// import { getSelectedText } from "@util/common.dom";
import { onMount } from "solid-js";
import { useService } from "solid-services";
import { AppSoundFx } from "../types/app.types";

export default function inputSFX(el: HTMLElement) {
  // const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);

  const isSfxEnabled = () => appSettingsService().getSetting("AUDIO_SFX_ENABLE");
  const ALPHA_NUMERIC_REGEX = /^[A-Za-z0-9_@./#&+-]*$/;

  onMount(() => {
    el.addEventListener("keydown", (event: Event) => {
      if (!isSfxEnabled()) return;
      let evt = event as KeyboardEvent;
      if (evt.repeat) return;

      // let hasSelectedText = getSelectedText();
      // if (evt.ctrlKey && evt.key === "a") {
      //   if (!hasSelectedText) sfxService().playSFX(AppSoundFx.SELECT_ALL_TEXT);
      // }
    });

    el.addEventListener("input", (event: Event) => {
      if (!isSfxEnabled()) return;
      let evt = event as InputEvent;
      if (!evt.data) return;
      // if (ALPHA_NUMERIC_REGEX.test(evt.data)) sfxService().playInputTypeSFX();
    });
  });
}