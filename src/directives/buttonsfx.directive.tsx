// import SoundEffectsService from "@services/sound-effects.service";
import { onMount } from "solid-js";
import { useService } from "solid-services";
import { AppSoundFx } from "../types/app.types";

export function buttonConfirmSFX(el: HTMLElement) {
  // const sfxService = useService(SoundEffectsService);

  // onMount(() => {
  //   // el.addEventListener("mousedown", (evt: MouseEvent) => {
  //   //   // if (evt.button == 0) sfxService().playSFX(AppSoundFx.CONFIRM);
  //   // });
  // });
}

export function buttonClickSFX(el: HTMLElement) {
  // const sfxService = useService(SoundEffectsService);

  // onMount(() => {
  //   // el.addEventListener("mousedown", (evt: MouseEvent) => {
  //   //   // if (evt.button == 0) sfxService().playClickSFX();
  //   // });
  // });
}

export function buttonHoverSFX(el: HTMLElement) {
  // const sfxService = useService(SoundEffectsService);

  // onMount(() => {
  //   // el.addEventListener("mouseenter", () => {
  //   //   // sfxService().playHoverSFX();
  //   // });
  // });
}

export const buttonSFX = (el: HTMLButtonElement | HTMLElement | undefined) => {
  // onMount(() => {
    // buttonHoverSFX(el);
    // buttonClickSFX(el);
  // });
}
