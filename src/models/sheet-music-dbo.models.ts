import { z } from "zod";

export namespace SheetMusicConst {
  export const MaxDescriptionLength = 1024;
  export const MaxDisapprovalReasonLength = 1024;
  export const MaxSongName = 128;
  export const MaxArtistNameLength = 128;
  export const MaxArtistAlbumLength = 128;
  export const MaxTags = 5;
  export const MaxTagLength = 24;
}

export enum SheetMusicDifficultyLevel {
  "Beginner" = "Beginner",
  "BeginnerPlus" = "BeginnerPlus",
  "Intermediate" = "Intermediate",
  "IntermediatePlus" = "IntermediatePlus",
  "Advanced" = "Advanced",
  "AdvancedPlus" = "AdvancedPlus",
  "Unknown" = "Unknown",
}

export enum SheetMusicCategory {
  "MusicXML" = "MusicXML",
  "VirtualPiano" = "VirtualPiano",
  "MultiplayerPiano" = "MultiplayerPiano",
  "ABCMusicNotation" = "ABCMusicNotation",
  "Unknown" = "Unknown",
}

export const SheetMusicDomainZ = z.object({
  id: z.string(),
  title: z.string(),
  creatorUsername: z.string(),
  difficultyLevel: z.nativeEnum(SheetMusicDifficultyLevel),
  privacyStatus: z.string(),
  category: z.nativeEnum(SheetMusicCategory),
  sheetFilePath: z.string().nullish(),
  backgroundImageFilePath: z.string().nullish(),
  tags: z.array(z.string()).default([]),
  songArtist: z.string().nullish(),
  songAlbum: z.string().nullish(),
  views: z.number().default(0),
  createdDate: z.string(),
  modifiedDate: z.string().nullish(),
  favorites: z.number(),
  description: z.string().nullish(),
  approved: z.boolean().nullish().default(false),
  lastApprovedBy: z.string().nullish(),
  lastApprovedDate: z.string().nullish(),
  lastDisapprovedBy: z.string().nullish(),
  lastDisapprovedDate: z.string().nullish(),
  disapprovalReason: z.string().nullish(),
  lastEditedBy: z.string().nullish(),
  lastEditedDate: z.string().nullish(),
  bpm: z.number().nullish().default(0),
});

export type SheetMusicDomain = z.infer<typeof SheetMusicDomainZ>;

export namespace SheetMusicDtoHelpers {
  export const DifficultyLevelToColor = (difficulty: SheetMusicDifficultyLevel | string) => {
    if (typeof difficulty === "string") {
      difficulty = SheetMusicDifficultyLevel[difficulty as keyof typeof SheetMusicDifficultyLevel] as SheetMusicDifficultyLevel;
    }

    switch (difficulty) {
      case SheetMusicDifficultyLevel.Beginner: return "#60C689";
      case SheetMusicDifficultyLevel.BeginnerPlus: return "#57DCBE";
      case SheetMusicDifficultyLevel.Intermediate: return "#57ACDC";
      case SheetMusicDifficultyLevel.IntermediatePlus: return "#276BB0";
      case SheetMusicDifficultyLevel.Advanced: return "#9C27B0";
      case SheetMusicDifficultyLevel.AdvancedPlus: return "#E91E63";
      default: return "#000";
    }
  };
}

export type SheetMusicRequest = {
  title: string;
  songArtist?: string;
  songAlbum?: string;
  tags?: string[];
  description?: string;
  category?: string;
  privacy?: "Public" | "Unlisted" | "Private";
  difficultyLevel?: string;
  data?: string;
  tempo?: number;
  totalTime?: number;
};

export type SheetMusicDisapproveRequest = {
  reason: string;
  reasonDetails: string;
};

export type SheetMusicUploadResponse = {
  data?: SheetMusicDto;
  error?: string;
  error_description?: string;
};

export const SheetMusicDetailResponseSchema = z.object({
  data: SheetMusicDomainZ,
  file: z.object({
    contentType: z.string(),
    data: z.instanceof(Array).default([]),
  }),
  error: z.string().optional(),
  error_description: z.string().optional()
});

export type SheetMusicDetailResponse = z.infer<typeof SheetMusicDetailResponseSchema>;

export type SheetMusicViewerProps = {
  data: string;
  title: string;
  isMusicXml: boolean;
};

export enum SheetMusicDisapprovalReasons {
  "InvalidScoreDetails" = "InvalidScoreDetails",
  "InvalidFileFormat" = "InvalidFileFormat",
  "EditChange" = "EditChange",
  "Unknown" = "Unknown",
}

export const SheetMusicGenreTags = [
  "Classical",
  "Anime",
  "Blues",
  // "Children's Music",
  "Comedy",
  "Country",
  "Dance",
  "Disney",
  "Easy Listening",
  "Electronic",
  "French Pop",
  "German Pop",
  "German Folk",
  "Fitness",
  "HipHop",
  "Rap",
  "Holiday",
  "Indie Pop",
  "Industrial",
  "Christian Gospel",
  "Instrumental",
  "J-Pop",
  "Jazz",
  "Latino",
  "New Age",
  "K-Pop",
  "Karaoke",
  "Opera",
  "Pop",
  "RnB",
  "Soul",
  "Reggae",
  "Rock",
  "Soundtrack",
  "Vocal",
  "Video Game",
  "Other",
];

const DifficultyLevelZ = z.object({
  Case: z.string(),
  Fields: z.array(z.any()).default([]),
});

const CategoryZ = z.object({
  Case: z.string(),
  Fields: z.array(z.any()).default([]),
});

const PrivacyStatusZ = z.object({
  Case: z.string(),
  Fields: z.array(z.any()).default([]),
});

const ApprovedMetaZ = z.object({
  uuid: z.string().optional(),
  lastApprovedDate: z.date().nullish(),
  lastApprovedBy: z.string().nullish(),
});

const MetaZ = z.object({
  sheetFilePath: z.string().nullish(),
  backgroundImageFilePath: z.string().nullish(),
  views: z.number().default(0),
  favorites: z.number().default(0),
  privacyStatus: PrivacyStatusZ,
  approved: z.boolean().default(false),
  approvedMeta: ApprovedMetaZ.optional(),
});

export const SheetMusicDboZ = z.object({
  _id: z.any(),
  uuid: z.string(),
  title: z.string(),
  creatorUsername: z.string(),
  difficultyLevel: DifficultyLevelZ,
  category: CategoryZ,
  tags: z.array(z.string()),
  songArtist: z.string().nullish(),
  songAlbum: z.string().nullish(),
  createdDate: z.date(),
  modifiedDate: z.date().nullish(),
  favorites: z.array(z.string()).nullish(),
  meta: MetaZ,
});

export const SheetMusicDtoZ = z.object({
  id: z.string(),
  title: z.string(),
  creatorUsername: z.string(),
  difficultyLevel: z.string(),
  category: z.string(),
  songArtist: z.string().nullish(),
  backgroundImage: z.array(z.number()).nullish(),
  views: z.number().default(0),
  createdDate: z.string(),
  favorites: z.number().default(0),
  approved: z.boolean().default(false),
  privacyStatus: z.string(),
});

export type SheetMusicDbo = z.infer<typeof SheetMusicDboZ>;
export type SheetMusicDto = z.infer<typeof SheetMusicDtoZ>;

// Map SheetMusicDbo to SheetMusicDto
export const mapSheetMusicDboToDto = (sheet: SheetMusicDbo): SheetMusicDto => {
  return {
    id: sheet.uuid,
    title: sheet.title,
    creatorUsername: sheet.creatorUsername,
    difficultyLevel: sheet.difficultyLevel.Case,
    category: sheet.category.Case,
    songArtist: sheet.songArtist,
    backgroundImage: sheet.meta.backgroundImageFilePath?.split("").map((c) => c.charCodeAt(0)),
    views: sheet.meta.views,
    createdDate: sheet.createdDate.toISOString(),
    favorites: sheet.meta.favorites,
    approved: sheet.meta.approved ?? false,
    privacyStatus: sheet.meta.privacyStatus.Case,
  };
};