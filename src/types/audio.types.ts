import { ActiveChannelsMode, AudioChannel as ProtoAudioChannel } from "~/proto/midi-renditions";
import { AUDIO, COMMON } from "~/util/const.common";
import { MIDI, mapMidiChannelToColor } from "~/util/const.midi";
import convertSize from "convert-size";
import { clamp, isNumber, isString, memoize, sortBy, uniqBy } from "lodash-es";
import { MidiNoteSource, WebMidiParseEvent, WebMidiPayload } from "./midi.types";
import { PianoRhythmWebSocketMidiNoteOn, PianoRhythmWebSocketMidiPitchBend } from "@core/pkg/pianorhythm_core";
import { decode } from "@msgpack/msgpack/dist/decode";

/** @ignore */
export const CUSTOM_SOUNDFONT_PREFIX = "CUSTOM:";

// export type AudioChannel = {
//   channel: number;
//   active: boolean;
//   instrument?: Instrument;
//   volume: number;
//   pan: number;
// };

export namespace AudioChannelHelper {
  export const getSlotColor = (midiChannel: number, slotMode: ActiveChannelsMode, multiModeMaxChannel = MIDI.SLOT_MODE_MULTI_MAX_CHANNEL) => {
    if (
      slotMode == ActiveChannelsMode.SPLIT2 && midiChannel < MIDI.SLOT_MODE_SPLIT2_MAX_CHANNEL
      || slotMode == ActiveChannelsMode.SPLIT4 && midiChannel < MIDI.SLOT_MODE_SPLIT4_MAX_CHANNEL
      || slotMode == ActiveChannelsMode.SPLIT8 && midiChannel < MIDI.SLOT_MODE_SPLIT8_MAX_CHANNEL
      || slotMode == ActiveChannelsMode.ALL
      || slotMode == ActiveChannelsMode.MULTI && midiChannel < multiModeMaxChannel
    ) {
      return mapMidiChannelToColor(midiChannel);
    }
    return null;
  };

  export function getChannel(channels: ProtoAudioChannel[], channel: number): ProtoAudioChannel | undefined {
    return channels[channel];
  }

  export function isInstrumentInChannel(channels: ProtoAudioChannel[], bank: number, preset: number, active: boolean = false) {
    return channels.find(x => {
      let output = x.instrument?.bank == bank && x.instrument.preset == preset;
      if (active) output = output && x.active;
      return output;
    }) != null;
  }

  export function getChannelFromInstrument(channels: ProtoAudioChannel[], bank: number, preset: number, active: boolean = false) {
    return channels.findIndex(x => x.instrument?.bank == bank && x.instrument.preset == preset);
  }

  export const allChannelsAreInactive = (channels: ProtoAudioChannel[]) => channels.map(x => !x.active).every(Boolean);

  export const getChannelNumbers = (channels: ProtoAudioChannel[]) => channels.map(x => x.channel);

  export const getChannelsWithInstrumentsLoaded = (channels: ProtoAudioChannel[]) => channels.filter(x => x.instrument).map(x => x.channel);

  export function getActiveChannelsFromSplit(_slotMode?: ActiveChannelsMode) {
    switch (_slotMode) {
      case ActiveChannelsMode.SPLIT2:
        return MIDI.SLOT_MODE_SPLIT2_MAX_CHANNEL;
      case ActiveChannelsMode.SPLIT4:
        return MIDI.SLOT_MODE_SPLIT4_MAX_CHANNEL;
      case ActiveChannelsMode.SPLIT8:
        return MIDI.SLOT_MODE_SPLIT8_MAX_CHANNEL;
      case ActiveChannelsMode.SINGLE:
        return MIDI.SLOT_MODE_SINGLE_MAX_CHANNEL;
      default:
        return -1;
    }
  }

  export const getDisabledChannels = (channels: ProtoAudioChannel[], _slotMode?: ActiveChannelsMode, multiModeMaxChannel = MIDI.SLOT_MODE_MULTI_MAX_CHANNEL) => {
    if (_slotMode == ActiveChannelsMode.MULTI) return getChannelNumbers(channels).filter(x => x > (clamp(multiModeMaxChannel, 1, AUDIO.MAX_CHANNEL) - 1));
    let splitSize = getActiveChannelsFromSplit(_slotMode);
    return getChannelNumbers(channels).filter(x => x >= splitSize && splitSize != -1);
  };

  export function isChannelDisabled(channel: number, _slotMode?: ActiveChannelsMode) {
    let splitSize = getActiveChannelsFromSplit(_slotMode);
    return (splitSize != -1 && channel >= splitSize);
  }
}

export type SoundfontSetting = {
  name: string;
  isDefault?: boolean;
  path?: string | null;
  custom?: boolean;
};

/** @ignore */
export const EQUALIZER_FREQUENCIES: number[] = [
  16, 32, 64, 125, 250, 500, 1000, 2000, 4000, 8000, 16000, 20000
];

export type EqualizerBand = {
  chnl: number,
  freq: number,
  rsnce: number,
  gain: number,
  curve: EqualizerCurve;
};

export enum EqualizerPreset {
  Default,
  GrandPiano,
  Flat,
  Acoustic,
  Electronic,
  Latin,
  Piano,
  Pop,
  Rock,
  BassBooster,
  Custom
}

/** @ignore */
export const EQUALIZER_PRESETS = [
  EqualizerPreset.Default,
  EqualizerPreset.GrandPiano,
  EqualizerPreset.Flat,
  EqualizerPreset.Acoustic,
  // EqualizerPreset.Electronic,
  // EqualizerPreset.Latin,
  EqualizerPreset.Piano,
  EqualizerPreset.Pop,
  // EqualizerPreset.Rock,
  EqualizerPreset.BassBooster,
  EqualizerPreset.Custom
];

export enum EqualizerCurve {
  Lowpass = 0,
  Highpass = 1,
  Bandpass = 2,
  Notch = 3,
  Peak = 4,
  LowShelf = 5,
  Highshelf = 6,
}

/** @ignore */
export namespace Equalizer {
  export const DEFAULT_PRESET = EqualizerPreset.Default;
  export const DEFAULT_CURVE = EqualizerCurve.Peak;
  export const DEFAULT_BANDS: EqualizerBand[] = EQUALIZER_FREQUENCIES.map((freq, idx) => ({
    chnl: idx,
    freq: freq,
    rsnce: 0.25, //Math.sqrt(0.5),
    gain: 0,
    curve: DEFAULT_CURVE
  }));
  const DEFAULT_CURVES = [
    EqualizerCurve.LowShelf,   // Channel 0
    EqualizerCurve.LowShelf,   // Channel 1
    EqualizerCurve.Peak,       // Channel 2
    EqualizerCurve.Peak,       // Channel 3
    EqualizerCurve.Peak,       // Channel 4
    EqualizerCurve.Peak,       // Channel 5
    EqualizerCurve.Peak,       // Channel 6
    EqualizerCurve.Peak,       // Channel 7
    EqualizerCurve.Peak,       // Channel 8
    EqualizerCurve.Highshelf,  // Channel 9
    EqualizerCurve.Highshelf,  // Channel 10
    EqualizerCurve.Peak        // Channel 11
  ];

  export const FromPreset = (preset: EqualizerPreset) => {
    const mapGainForRreq = (
      mappedGain: number[] = [],
      curves: (EqualizerCurve | undefined)[] = [],
      resonance: number[] = []
    ) => {
      return EQUALIZER_FREQUENCIES.map((freq, idx) => ({
        chnl: idx,
        freq: freq,
        rsnce: resonance?.[idx] ?? Math.sqrt(0.5),
        gain: mappedGain?.[idx] ?? 0,
        curve: curves?.[idx] ?? DEFAULT_CURVES[idx] ?? DEFAULT_CURVE
      }));
    };

    switch (preset) {
      case EqualizerPreset.Flat: {
        return [...DEFAULT_BANDS];
      }

      case EqualizerPreset.GrandPiano: {
        return mapGainForRreq(
          [3, 3, 2, 1, 0, 1, -2, -1, 1, -1, -2, -3],
          [
            EqualizerCurve.LowShelf,   // Channel 0
            EqualizerCurve.LowShelf,   // Channel 1
            EqualizerCurve.Peak,       // Channel 2
            EqualizerCurve.Peak,       // Channel 3
            EqualizerCurve.Peak,       // Channel 4
            EqualizerCurve.Peak,       // Channel 5
            EqualizerCurve.Peak,       // Channel 6
            EqualizerCurve.Peak,       // Channel 7
            EqualizerCurve.Peak,       // Channel 8
            EqualizerCurve.Highshelf,  // Channel 9
            EqualizerCurve.Highshelf,  // Channel 10
            EqualizerCurve.Peak        // Channel 11
          ],
          [0.7, 0.8, 1.0, 1.2, 1.0, 1.5, 1.8, 1.4, 1.3, 1.5, 1.6, 1.8]
        );
      }

      case EqualizerPreset.Default:
      case EqualizerPreset.Acoustic: {
        return mapGainForRreq([4, 4, 4, 2, 1, 1, 1, 3, 3, 2, 1, 1]);
      }

      case EqualizerPreset.Piano: {
        return mapGainForRreq([4, 4, 2, 0, 3, 4, 2, 3, 4, 2, 3, 3]);
      }

      case EqualizerPreset.Pop: {
        return mapGainForRreq([-2, -2, -1, 0, 2, 4, 4, 2, 0, 2, 2, 2]);
      }

      case EqualizerPreset.BassBooster: {
        return mapGainForRreq([5, 5, 4, 3, 2, 1, 0, 0, 0, 0, 0, 0]);
      }

      default:
        return [...DEFAULT_BANDS];
    }
  };
}

export enum AudioReverbPreset {
  Minimal,
  High,
  SmallRoom,
  MediumRoom,
  ConcertHall,
  Church,
  Cave,
  Custom
}

export const AUDIO_REVERB_PRESETS = [
  AudioReverbPreset.Minimal,
  AudioReverbPreset.SmallRoom,
  AudioReverbPreset.MediumRoom,
  AudioReverbPreset.ConcertHall,
  AudioReverbPreset.High,
  AudioReverbPreset.Church,
  AudioReverbPreset.Cave,
  AudioReverbPreset.Custom
];

export type AudioReverParams = {
  AUDIO_REVERB_LEVEL: number;
  AUDIO_REVERB_ROOMSIZE: number;
  AUDIO_REVERB_DAMP: number;
  AUDIO_REVERB_WIDTH: number;
};

export namespace AudioReverb {
  export const DEFAULT_PRESET = AudioReverbPreset.Minimal;
  export const FromPreset = (preset: AudioReverbPreset) => {
    switch (preset) {
      case AudioReverbPreset.Minimal: {
        return AUDIO.REVERB.MINIMAL as AudioReverParams;
      }
      case AudioReverbPreset.SmallRoom: {
        return {
          AUDIO_REVERB_LEVEL: 0.3,
          AUDIO_REVERB_ROOMSIZE: 0.2,
          AUDIO_REVERB_DAMP: 0.3,
          AUDIO_REVERB_WIDTH: 0.5
        } as AudioReverParams;
      }
      case AudioReverbPreset.MediumRoom: {
        return {
          AUDIO_REVERB_LEVEL: 0.4,
          AUDIO_REVERB_ROOMSIZE: 0.4,
          AUDIO_REVERB_DAMP: 0.2,
          AUDIO_REVERB_WIDTH: 0.7
        } as AudioReverParams;
      }
      case AudioReverbPreset.ConcertHall: {
        return {
          AUDIO_REVERB_LEVEL: 0.6,
          AUDIO_REVERB_ROOMSIZE: 0.8,
          AUDIO_REVERB_DAMP: 0.1,
          AUDIO_REVERB_WIDTH: 0.9
        } as AudioReverParams;
      }
      case AudioReverbPreset.High: {
        return AUDIO.REVERB.HIGH as AudioReverParams;
      }
      case AudioReverbPreset.Church: {
        return {
          AUDIO_REVERB_LEVEL: 0.7,
          AUDIO_REVERB_ROOMSIZE: 0.9,
          AUDIO_REVERB_DAMP: 0.2,
          AUDIO_REVERB_WIDTH: 1.0
        } as AudioReverParams;
      }
      case AudioReverbPreset.Cave: {
        return {
          AUDIO_REVERB_LEVEL: 0.9,
          AUDIO_REVERB_ROOMSIZE: 1.0,
          AUDIO_REVERB_DAMP: 0.1,
          AUDIO_REVERB_WIDTH: 1.0
        } as AudioReverParams;
      }
      default:
        return AUDIO.REVERB.MINIMAL as AudioReverParams;
    }
  };
}

export type SetInstrumentChannel = "Add" | "NextInactive" | "NextEmpty";

export type AudioServiceNoteActivity = {
  channel: number;
  type: "On" | "Off";
  socketID?: string,
  socketIDHashed?: number,
  isClient: boolean;
  velocity?: number;
  source?: number;
};

export type MidiNoteOn = {
  channel: number;
  note: number;
  velocity: number;
  program?: number;
  volume?: number;
  bank?: number;
  pitch?: number;
  expression?: number;
  pan?: number;
};

export type MidiNoteOff = {
  channel: number;
  note: number;
};

export type MidiSustain = {
  channel: number;
  value: number;
};

export type MidiSustainProto = {
  channel: number;
  value: boolean;
};

export type MidiAllSoundOff = {
  channel: number;
};

export type MidiChannelEvent = {
  channel: number;
  data1?: number;
};

export type MidiControlChange = {
  channel: number;
  data1?: number;
  data2?: number;
};

export type MidiDto =
  | ["NoteOn", MidiNoteOn]
  | ["NoteOff", MidiNoteOff]
  | ["Sustain", MidiSustain]
  | ["AllSoundOff", MidiAllSoundOff]
  | "Invalid";

export type SynthInterpolationMethod = "None" | "Linear" | "FourthOrder" | "SeventhOrder";

/** @ignore */
export namespace SynthInterpolationMethod {
  export const ToID = (method: SynthInterpolationMethod): number => {
    switch (method) {
      case "None":
        return 0;
      case "Linear":
        return 1;
      case "FourthOrder":
        return 2;
      case "SeventhOrder":
        return 3;
      default:
        return ToID("FourthOrder");
    }
  };
}

/** @ignore */
export const DEFAULT_SOUNDFONT = "PR_GM_v2.sf2";

/** @ignore */
export const DEFAULT_DRUM_SOUNDFONT = "Drum_Set.sf2";

/** @ignore */
export type SoundfontDetails = {
  /** Data size in KB */
  size: string;
};

export enum AudioSynthesizerEngine {
  OXISYNTH = "OXISYNTH",
  // RUSTYSYNTH = "RUSTYSYNTH",
}

export const FULLY_FEATURED_SYNTHS = [
  AudioSynthesizerEngine.OXISYNTH
];

/** @ignore */
const SoundfontDetailsDefault: SoundfontDetails = {
  size: "0 KB"
};

/** @ignore */
const getSize = (size: string) => convertSize(size, "MB", { stringify: true }) as string;

/** @ignore */
const soundfonts = [
  [DEFAULT_SOUNDFONT, { ...SoundfontDetailsDefault, size: getSize("85.27 mb") }],

  [`1Bit.sf2`, { ...SoundfontDetailsDefault, size: getSize("6.4 mb") }],
  [`STAIN_SOUNDFONT_gm_EXP.sf2`, { ...SoundfontDetailsDefault, size: getSize("28.9 mb") }],
  [`GMGSX.sf2`, { ...SoundfontDetailsDefault, size: getSize("6 mb") }],
  // [`GM2_Map_Soundfont.sf2`, { ...SoundfontDetailsDefault, size: getSize("61.8 mb") }],
  [`grand-piano-YDP-20160804.sf2`, { ...SoundfontDetailsDefault, size: getSize("112.9 mb") }],
  [`Yamaha_Grand_Piano.sf2`, { ...SoundfontDetailsDefault, size: getSize("125.7 mb") }],
  [`Arachno.sf2`, { ...SoundfontDetailsDefault, size: getSize("148.2 mb") }],
  [`KBH-Real-Choir-V2.5.sf2`, { ...SoundfontDetailsDefault, size: getSize("16.6 mb") }],
  [`VS_Upright_Piano_lite.sf2`, { ...SoundfontDetailsDefault, size: getSize("33.3 mb") }],
  [`jRhodes3.sf2`, { ...SoundfontDetailsDefault, size: getSize("75.3 mb") }],
  [`Yamaha XG Sound Set Ver.2.0.sf2`, { ...SoundfontDetailsDefault, size: getSize("55.3 mb") }],
  [`FluidR3_GM.sf2`, { ...SoundfontDetailsDefault, size: getSize("141.5 mb") }],
  [`Yamaha C5 Grand-v2.3.sf2`, { ...SoundfontDetailsDefault, size: getSize("232.1 mb") }],
  [`Touhou.sf2`, { ...SoundfontDetailsDefault, size: getSize("249.1 mb") }],
  [`PR_V2_SF.sf2`, { ...SoundfontDetailsDefault, size: getSize("55.4 mb") }],
  [`MPP_Loud_and_Proud.sf2`, { ...SoundfontDetailsDefault, size: getSize("76.98 mb") }],

  [`Musica_Theoria_v2_(GM).sf2`, { ...SoundfontDetailsDefault, size: getSize("29.11 mb") }],
  [`ChoriumRevA.SF2`, { ...SoundfontDetailsDefault, size: getSize("27.59 mb") }],
  [`41.8mg_saphyr_two_thousand_gm_gs_bank.sf2`, { ...SoundfontDetailsDefault, size: getSize("40.89 mb") }],
  [`Acapella GM.sf2`, { ...SoundfontDetailsDefault, size: getSize("19.65 mb") }],
  [`GeneralUser GS v1.471.sf2`, { ...SoundfontDetailsDefault, size: getSize("29.83 mb") }],
  [`SGM-v2.01-NicePianosGuitarsBass-V1.2.sf2`, { ...SoundfontDetailsDefault, size: getSize("309 mb") }],

  [`Arco Strings.sf2`, { ...SoundfontDetailsDefault, size: getSize("1.59 mb") }],
  [`[GD] Custom Grand 1.sf2`, { ...SoundfontDetailsDefault, size: getSize("491.89 mb") }],
  [`[GD] The Grandeur D.sf2`, { ...SoundfontDetailsDefault, size: getSize("562.62 mb") }],
  [`[GD] Steinway Model D274.sf2`, { ...SoundfontDetailsDefault, size: getSize("730.34 mb") }],
  [`[GD] Kawai ES100 Concert Grand 1 [V3].sf2`, { ...SoundfontDetailsDefault, size: getSize("356.75 mb") }],
  [`[GD] Grotrian Concert Royal.sf2`, { ...SoundfontDetailsDefault, size: getSize("468.63 mb") }],
  [`[GD] Bluthner Model One.sf2`, { ...SoundfontDetailsDefault, size: getSize("427.45 mb") }],
  [`Spitfire_LABS_Autograph_Grand.sf2`, { ...SoundfontDetailsDefault, size: getSize("77.85 mb") }],
  [`OT_Ratio_Grand_Piano.sf2`, { ...SoundfontDetailsDefault, size: getSize("225.76 mb") }],
  [`General Montage.sf2`, { ...SoundfontDetailsDefault, size: getSize("1.55 gb") }],
  [`General Montage GS XG.sf2`, { ...SoundfontDetailsDefault, size: getSize("1.55 gb") }]
];

/** @ignore */
const drumSoundfonts = [
  [DEFAULT_DRUM_SOUNDFONT, { ...SoundfontDetailsDefault, size: getSize("1.5 mb") }],
  ["Hip-Hop.sf2", { ...SoundfontDetailsDefault, size: getSize("49.9 mb") }],
  ["U20_Drums.sf2", { ...SoundfontDetailsDefault, size: getSize("5.9 mb") }]
];

/** @ignore */
const deprecated_soundfonts = [
  [`GM2_Map_Soundfont.sf2`, { ...SoundfontDetailsDefault }]
];

/** @ignore */
export const DeprecatedSoundfonts = new Map<string, SoundfontDetails>(
  uniqBy(
    //@ts-ignore
    sortBy(deprecated_soundfonts, (i) => convertSize(i[1].size))
    , (i) => i[0]) as any
);

/** @ignore */
export const Soundfonts = new Map<string, SoundfontDetails>(
  uniqBy(
    //@ts-ignore
    sortBy(soundfonts, (i) => convertSize(i[1].size))
    , (i) => i[0]) as any
);

/** @ignore */
export const DrumSoundfonts = new Map<string, SoundfontDetails>(
  uniqBy(
    //@ts-ignore
    sortBy(drumSoundfonts, (i) => convertSize(i[1].size))
    , (i) => i[0]) as any
);

/**
 * Represents a WebAssembly Synthesizer.
 * This interface provides methods for controlling and interacting with the synthesizer.
 */
export type WasmSynth = {
  get_wasm_module(): any;
  get_wasm_memory(): any;
  set_slot_mode(mode: number): void;
  hash_device_id(input: string): number;
  all_sounds_off(channel: number, socketID?: number): void;
  all_notes_off(channel: number, socketID?: number): void;
  reset_all_controllers(channel: number, socketID?: number): void;
  set_channel_active(channel: number, value: boolean, socketID?: number): void;
  volume_change(channel: number, value: number, socketID?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  pan_change(channel: number, value: number, socketID?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  damper_pedal(channel: number, value: number, socketID?: number): void;
  bank_select(channel: number, bank: number, socketID?: number): void;
  program_select(channel: number, preset: number, socketID?: number): void;
  from_socket_note_on(event: any, socketID?: number): void;
  clear_program_on_channel(channel: number, socketID?: number): void;
  note_on(channel: number, key: number, velocity: number, socketID?: number, sourceType?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  note_off(channel: number, key: number, socketID?: number, sourceType?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  from_socket_note_off(channel: number, key: number, socketID?: number, sourceType?: number): void;
  from_socket_pitch: (event: any, socketID?: number) => void;
  load_soundfont(fileName: string | Uint8Array, is_local_file: boolean, saveFile?: boolean, data?: Uint8Array): Promise<void>;
  set_max_multi_mode_channels(value: number): void;
  synth_set_gain(value: number): void;
  synth_set_user_gain(value: number, socketID?: number): void;
  synth_set_polyphony(value: number): void;
  synth_set_sample_rate(value: number): void;
  synth_set_chorus(value: boolean): void;
  synth_set_reverb(value: boolean): void;
  synth_ws_socket_note_on(event: PianoRhythmWebSocketMidiNoteOn, socketID?: number): void;
  parse_midi_data(value: Uint8Array, socketID?: number, sourceType?: number, deviceID?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  parse_midi_data_non_proxy(value: Uint8Array, socketID?: number, sourceType?: number, deviceID?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void;
  synth_ws_socket_pitch: (event: PianoRhythmWebSocketMidiPitchBend, socketID?: number) => void;
  instrument_exists(bank: number, program: number): Promise<boolean>;
  get_program(channel: number, socketID?: number): Promise<PianoRhythmCurrentProgram | undefined>;
  set_client_socket_id(socketID: number): void;
  mute_user(socketID: number, value: boolean): void;
  add_socket(socketID: number): Promise<boolean>;
  remove_socket(socketID: number): void;
  set_octave_offset(value: number): void;
  set_transpose_offset(value: number): void;
  set_interpolation_method(value: SynthInterpolationMethod): void;
  get_synth_users(): Promise<string[]>;
  synth_get_reverb_level(): Promise<number>;
  synth_get_reverb_room_size(): Promise<number>;
  synth_get_reverb_width(): Promise<number>;
  init_audio_scheduler(sampleRate: number, node: AudioWorkletNode): void;
  init_audio_scheduler_global(sampleRate: number): SharedArrayBuffer;
  init_worker_scheduler_global(sampleRate: number, buffer: SharedArrayBuffer): void;
  synth_get_reverb_damp(): Promise<number>;
  synth_set_reverb_width(value: number): void;
  synth_set_reverb_level(value: number): void;
  set_primary_channel(value: number): void;
  synth_set_reverb_room_size(value: number): void;
  synth_set_reverb_damp(value: number): void;
  set_disable_velocity_for_client(value: boolean): void;
  dispose(): void;
  disconnect(): void;
  reset(): void;
  open_midi_input_connection(midiID: string): void;
  open_midi_output_connection(midiID: string): void;
  emit_to_midi_output(midiID: string, data: Uint8Array): void;
  close_midi_input_connection(midiID: string): void;
  close_midi_output_connection(midiID: string): void;
  list_midi_input_connections(): Map<number, string> | null;
  list_midi_output_connections(): Map<number, string> | null;
  load_midi_file(data: Uint8Array, fileName?: string): void;
  load_midi_file_by_path(path: string): void;
  stop_midi_file(): void;
  resume_midi_file(): void;
  pause_midi_file(): void;
  synth_set_auto_fill_channels_with_default(value: boolean): void;
  set_use_default_instrument_when_missing_for_other_users(value: boolean): void;
  bypassall_equalizer(value: boolean): void;
  set_drum_channel_muted(value: boolean): void;
  set_equalizer_enabled(value: boolean): void;
  set_midi_output_only(value: boolean): void;
  set_max_velocity(value?: number): void;
  set_min_velocity(value?: number): void;
  set_max_note_on_time(value?: number): void;
  set_channel_volume(channel: number, value: number, socketID?: number): void;
  reset_equalizer(): void;
  get_all_presets_from_sf(): Uint8Array[];
  get_synth_audio_channels(): Promise<Uint8Array[]>;
  set_equalizer_resonance(idx: number, value: number): void;
  set_equalizer_bypass(idx: number, value: boolean): void;
  set_equalizer_freq(idx: number, value: number): void;
  set_equalizer_gain(idx: number, value: number): void;
  set_equalizer_band(idx: number, curve: number, frequency: number, resonance: number, gain: number): void;
  set_user_volume(value: number, socketID: number): void;
  set_user_velocity_percentage(value: number, socketID: number): void;
  set_apply_velocity_curve(value: boolean): void;
};

/** @ignore */
export namespace WasmSynth {
  export const DEFAULT: WasmSynth = {
    init_audio_scheduler: () => {
    },
    init_audio_scheduler_global: () => {
      return new SharedArrayBuffer(0);
    },
    init_worker_scheduler_global: () => {
    },
    get_wasm_module: () => null,
    get_wasm_memory: () => null,
    hash_device_id: () => -1,
    get_all_presets_from_sf: () => [],
    set_max_multi_mode_channels: () => {
    },
    set_channel_volume: () => {
    },
    set_max_velocity: () => {
    },
    set_midi_output_only: () => {
    },
    set_drum_channel_muted: () => {
    },
    set_equalizer_enabled: () => {
    },
    set_min_velocity: () => {
    },
    set_max_note_on_time: () => {
    },
    set_slot_mode: () => {
    },
    set_primary_channel: () => {
    },
    clear_program_on_channel: () => {
    },
    synth_set_auto_fill_channels_with_default: () => {
    },
    set_use_default_instrument_when_missing_for_other_users: () => {
    },
    resume_midi_file: () => {
    },
    pause_midi_file: () => {
    },
    stop_midi_file: () => {
    },
    load_midi_file_by_path: () => {
    },
    load_midi_file: () => {
    },
    set_octave_offset: () => {
    },
    set_transpose_offset: () => {
    },
    list_midi_input_connections: () => null,
    list_midi_output_connections: () => null,
    open_midi_output_connection: () => {
    },
    open_midi_input_connection: () => {
    },
    close_midi_input_connection: () => {
    },
    close_midi_output_connection: () => {
    },
    set_interpolation_method: () => {
    },
    emit_to_midi_output: () => {
    },
    get_program: () => {
      return Promise.resolve(undefined);
    },
    note_on: () => {
      return Promise.resolve(undefined);
    },
    note_off: () => {
      return Promise.resolve(undefined);
    },
    get_synth_users: () => Promise.resolve([]),
    synth_get_reverb_level: () => Promise.resolve(0),
    synth_get_reverb_room_size: () => Promise.resolve(0),
    synth_get_reverb_damp: () => Promise.resolve(0),
    synth_get_reverb_width: () => Promise.resolve(0),
    set_client_socket_id: () => {
    },
    from_socket_note_on: () => {
    },
    from_socket_note_off: () => {
    },
    from_socket_pitch: () => {
    },
    set_channel_active: () => {
    },
    synth_set_chorus: () => {
    },
    synth_set_reverb: () => {
    },
    volume_change: () => {
    },
    pan_change: () => {
    },
    mute_user: () => {
    },
    load_soundfont: () => {
      return Promise.resolve();
    },
    synth_set_gain: () => {
    },
    synth_set_user_gain: () => {
    },
    synth_set_polyphony: () => {
    },
    synth_set_sample_rate: () => {
    },
    set_disable_velocity_for_client: () => {
    },
    program_select: () => {
    },
    bank_select: () => {
    },
    damper_pedal: () => {
    },
    all_notes_off: () => {
    },
    all_sounds_off: () => {
    },
    reset_all_controllers: () => {
    },
    parse_midi_data: () => {
    },
    dispose: () => {
    },
    disconnect: () => {
    },
    reset: () => {
    },
    synth_set_reverb_width: () => {
    },
    synth_set_reverb_level: () => {
    },
    synth_set_reverb_room_size: () => {
    },
    synth_set_reverb_damp: () => {
    },
    add_socket: () => Promise.resolve(false),
    remove_socket: () => {
    },
    instrument_exists: () => Promise.resolve(false),
    bypassall_equalizer: () => {
    },
    reset_equalizer: () => {
    },
    set_equalizer_resonance: () => {
    },
    set_equalizer_bypass: () => {
    },
    set_equalizer_freq: () => {
    },
    set_equalizer_gain: () => {
    },
    set_equalizer_band: () => {
    },
    set_apply_velocity_curve: () => {
    },
    get_synth_audio_channels: function (): Promise<Uint8Array[]> {
      return Promise.resolve([]);
    },
    set_user_volume: function (value: number, socketID: number): void {
    },
    set_user_velocity_percentage: function (value: number, socketID: number): void {
    },
    parse_midi_data_non_proxy: function (value: Uint8Array, socketID?: number, sourceType?: number, deviceID?: number): Promise<PianoRhythmSynthEvent[] | undefined> | void {
    },
    synth_ws_socket_note_on: function (event: PianoRhythmWebSocketMidiNoteOn, socketID?: number): void {
    },
    synth_ws_socket_pitch: function (event: PianoRhythmWebSocketMidiPitchBend, socketID?: number): void {
    }
  };
}

export type WasmSynth_SoundfontPreset = {
  name: string;
  bank: number;
  preset: number;
  key_low: number;
  key_high: number;
};

export type PianoRhythmSynthEventName =
  | "NOTE_ON"
  | "NOTE_OFF"
  | "PROGRAM_CHANGE"
  | "CHANNEL_PRESSURE"
  | "SYSTEM_RESET"
  | "POLYPHONIC_KEY_PRESSURE"
  | "ALL_NOTES_OFF"
  | "ALL_SOUND_OFF"
  | "OMNI_MODE_OFF"
  | "MONO_MODE_ON"
  | "POLY_MODE_ON"
  | "OMNI_MODE_ON"
  | "MAIN_VOLUME_MSB"
  | "MAIN_VOLUME_LSB"
  | "FOOT_CONTROLLER_MSB"
  | "MODULATION_MSB"
  | "MODULATION_LSB"
  | "BANK_SELECT_MSB"
  | "BANK_SELECT_LSB"
  | "FOOT_CONTROLLER_LSB"
  | "RESET_ALL_CONTROLLERS"
  | "PAN_MSB"
  | "PAN_LSB"
  | "DAMPER_PEDAL"
  | "PORTAMENTO"
  | "SUSTENUTO"
  | "SOFT_PEDAL"
  | "LEGATO_FOOTSWITCH"
  | "HOLD_2"
  | "EFFECTS_1_DEPTH"
  | "TREMELO_EFFECT"
  | "CHORUS_EFFECT"
  | "CELESTE_EFFECT"
  | "PHASER_EFFECT"
  | "PITCH_BEND"
  | "CONTROL_CHANGE"
  | "SOCKET_USER_GAIN_CHANGE"
  | "UNKNOWN";

export type PianoRhythmSynthEventMessageTypeRaw =
  | "NoteOn"
  | "NoteOff"
  | "ControlChange"
  | "AllNotesOff"
  | "AllSoundOff"
  | "ProgramChange"
  | "Unknown";

export type DrumKit = {
  name: string;
  bank: number;
  program: number;
};

export enum DRUM_PRESETS {
  // Basic Patterns
  BASIC_PATTERN_FUNKY_DRUMMER = "The Funky Drummer",
  BASIC_PATTERN_BOOTS_N_CATS = "Boots N' Cats",
  BASIC_PATTERN_TINY_HOUSE = "Tiny House",
  BASIC_PATTERN_GOOD_TO_GO = "Good To Go",
  BASIC_PATTERN_HIP_HOP = "Hip Hop",

  //Standard Break
  STANDARD_BREAK_1 = "Standard Break 1",
  STANDARD_BREAK_2 = "Standard Break 2",
  STANDARD_BREAK_ROLLING_BREAK = "Rolling Break",
  STANDARD_BREAK_UNKNOWN_DRUMMER = "The Unknown Drummer",

  //Rock
  ROCK_1 = "Rock 1",
  ROCK_2 = "Rock 2",
  ROCK_3 = "Rock 3",
  ROCK_4 = "Rock 4",

  //ELECTRO
  ELECTRO_1_A = "Electro 1-A",
  ELECTRO_1_B = "Electro 1-B",
  ELECTRO_3_A = "Electro 3-A",
  ELECTRO_SIBERIAN_NIGHTS = "Siberian Nights",
  ELECTRO_NEW_WAVE = "New Wave",
}

type PianoRhythmSynthEventMessageType =
  { NoteOn: { channel: number, key: number, vel: number; }; } |
  { NoteOff: { channel: number, key: number; }; } |
  { ControlChange: { channel: number, ctrl: number, value: number; }; } |
  { AllNotesOff: { channel: number; }; } |
  { AllSoundOff: { channel: number; }; } |
  { ProgramChange: { channel: number, program_id: number; }; };

export type PianoRhythmWebSocketSynthEvent = {
  note_on?: MidiNoteOn;
  note_off?: MidiNoteOff;
  all_sound_off?: number;
  all_notes_off?: number;
  sustain?: MidiSustain;
};

type PianoRhythmCurrentProgram = {
  channel: number;
  bank: number;
  program: number;
};

export type PianoRhythmMidiEventKind =
  | { kind: "NoteOn"; }
  | { kind: "NoteOff"; }
  | { kind: "ControlChange"; }
  | { kind: "AllNotesOff"; }
  | { kind: "AllSoundOff"; }
  | { kind: "PitchBend"; }
  | { kind: "ProgramChange"; }
  | { kind: "ChannelPressure"; }
  | { kind: "PolyphonicKeyPressure"; }
  | { kind: "SocketUserGainChange"; }
  | { kind: "SystemReset"; }
  | { kind: "SystemResetWithChannel"; }
  | { kind: "Unknown"; };

export type PianoRhythmSynthEvent = {
  raw_bytes: Uint8Array,
  current_program?: number,
  current_bank?: number,
  current_volume?: number,
  current_pitch?: number,
  current_expression?: number,
  current_pan?: number,
  source?: MidiNoteSource,
  channel: number,
  message?: PianoRhythmSynthEventMessageType;
  message_type?: number | PianoRhythmSynthEventMessageTypeRaw | PianoRhythmSynthEventName;
  is_client: boolean,
  socket_id?: number;
  device_id?: number;
  // These fields are populated back on the client side
  socketIDMappedOnClientSide?: string;
  deviceIDMappedOnClientSide?: string;
};

export namespace PianoRhythmSynthEvent {
  export function fromMessageTypeU8(type: number): PianoRhythmSynthEventName {
    switch (type) {
      case 1:
        return "NOTE_ON";
      case 2:
        return "NOTE_OFF";
      case 3:
        return "CONTROL_CHANGE";
      case 4:
        return "ALL_NOTES_OFF";
      case 5:
        return "ALL_SOUND_OFF";
      case 6:
        return "PITCH_BEND";
      case 7:
        return "PROGRAM_CHANGE";
      case 8:
        return "CHANNEL_PRESSURE";
      case 9:
        return "POLYPHONIC_KEY_PRESSURE";
      case 10:
        return "SOCKET_USER_GAIN_CHANGE";
      case 11:
        return "SYSTEM_RESET";
      // case 12: return "SYSTEM_RESET_WITH_CHANNEL";
      default:
        return "UNKNOWN";
    }
  }

  function tryClamp(value: number | undefined, min: number, max: number) {
    return value != null && isFinite(value) ? clamp(value!, min, max) : undefined;
  }

  const ToBytes = (event: WebMidiPayload): number[] => {
    let channel = event.channel;
    switch (event.eventName) {
      case "note-on": {
        if (event.note1 == null || event.note2 == null) return [];
        return [MIDI.NOTE_ON_BYTE + channel, event.note1 || 0, event.note2 || 0];
      }
      case "note-off": {
        if (event.note1 == null) return [];
        return [MIDI.NOTE_OFF_BYTE + channel, event.note1 || 0, 0];
      }
      case "damper-on": {
        if (event.note1 == null) return [];
        return [MIDI.CONTROLLER_BYTE + channel, 64, event.note1 || 0];
      }
      case "damper-off": {
        if (event.note1 == null) return [];
        return [MIDI.CONTROLLER_BYTE + channel, 64, event.note1 || 0];
      }
      case "all-sound-off": {
        if (event.note1 == null) return [];
        return [MIDI.CONTROLLER_BYTE + channel, 123, 0];
      }
      default:
        return [];
    }
  };

  const SanitizeWebMidiPayload = (data: WebMidiPayload): WebMidiPayload => {
    let sanitizedChannel = tryClamp(data.channel, 0, MIDI.MAX_CHANNEL);
    data.channel = sanitizedChannel != null ? sanitizedChannel : -1;
    data.volume = tryClamp(data.volume, 0, MIDI.MAX_VELOCITY);
    data.note1 = tryClamp(data.note1, 0, MIDI.MAX_VELOCITY);
    data.note2 = tryClamp(data.note2, 0, MIDI.MAX_VELOCITY);
    data.program = tryClamp(data.program, 0, MIDI.MAX_VELOCITY);
    data.bank = tryClamp(data.bank, 0, MIDI.MAX_BANK);
    data.pan = tryClamp(data.pan, 0, MIDI.MAX_VELOCITY);
    data.expression = tryClamp(data.expression, 0, MIDI.MAX_VELOCITY);
    data.pitch = tryClamp(data.pitch, 0, MIDI.MAX_VELOCITY);
    return data;
  };

  const camelToSnake = memoize((str: string) => {
    return !str ? undefined : str.replace(/([a-z])([A-Z])/g, '$1_$2').toUpperCase();
  });

  export const SanitizeEventName = (message_type?: string) => {
    return camelToSnake(message_type || "");
  };

  const WebMidiParseEventToPianoRhythmSynthEventName = (eventName: WebMidiParseEvent): PianoRhythmSynthEventName => {
    let message_type: PianoRhythmSynthEventName = "UNKNOWN";
    switch (eventName) {
      case "note-on": {
        message_type = "NOTE_ON";
        break;
      }
      case "note-off": {
        message_type = "NOTE_OFF";
        break;
      }
      case "program-change": {
        message_type = "PROGRAM_CHANGE";
        break;
      }
      case "all-sound-off": {
        message_type = "ALL_SOUND_OFF";
        break;
      }
      case "all-notes-off": {
        message_type = "ALL_NOTES_OFF";
        break;
      }
      case "damper-on":
      case "damper-off": {
        message_type = "DAMPER_PEDAL";
        break;
      }
    }

    return message_type;
  };

  export const ToSynthEvent = (_event: WebMidiPayload): PianoRhythmSynthEvent => {
    let event = SanitizeWebMidiPayload(_event);

    return {
      raw_bytes: event.bytes || new Uint8Array(ToBytes(event)),
      current_program: event.program,
      current_bank: event.bank,
      current_volume: event.volume,
      current_pitch: event.pitch,
      current_expression: event.expression,
      current_pan: event.pan,
      source: event.source ?? 3,
      channel: event.channel,
      message: undefined,
      device_id: event.deviceID as number,
      // socket_id: HashSocketID(event.socketID),
      message_type: WebMidiParseEventToPianoRhythmSynthEventName(event.eventName),
      socketIDMappedOnClientSide: event.socketID,
      is_client: false
    };
  };

  export const ToMidiNoteOn = (event: PianoRhythmSynthEvent): MidiNoteOn => ({
    channel: event.channel,
    note: event.raw_bytes?.[1] ?? -1,
    velocity: event.raw_bytes?.[2] ?? -1,
    program: event.current_program,
    volume: event.current_volume,
    bank: event.current_bank,
    pitch: event.current_pitch,
    expression: event.current_expression,
    pan: event.current_pan
  });

  export const ToMidiNoteOff = (event: PianoRhythmSynthEvent): MidiNoteOff => ({
    channel: event.channel,
    note: event.raw_bytes?.[1] ?? -1
  });
}

export enum AudioSampleCategories {
  EightZeroEight = "808",
  Bass = "Bass",
  ReeseBass = "Reese Bass",
  HiHat = "Hi-hat",
  OpenHihat = "Open Hi-hat",
  ClosedHihat = "Closed Hi-hat",
  Kick = "Kick",
  Percussion = "Percussion",
  RimShot = "Rim Shot",
  Shaker = "Shaker",
  Snap = "Snap",
  Snare = "Snare",
  Clap = "Clap",
  Crash = "Crash",
  Ride = "Ride",
}

export type AudioSample = {
  name: string;
  category?: AudioSampleCategories;
};

export type AudioSampleCollection = {
  folderName: string;
  sampleNames: AudioSample[];
};

export type AudioDrumKit = {
  kitName: string,
  resources: AudioSampleCollection[];
  defaultBassDrum: string;
  defaultSnare: string;
  defaultOpenHiHat: string;
  defaultClosedHiHat: string;
  defaultCrash?: string;
};

export namespace DrumKits {
  export const Cymatics: AudioDrumKit = {
    kitName: "Cymatics - Orchid Sample Pack",
    defaultBassDrum: "Cymatics - Orchid 808 Standard (C)",
    defaultOpenHiHat: "Cymatics - Orchid Hihat - Open 1",
    defaultClosedHiHat: "Cymatics - Orchid Hihat - Closed 1",
    defaultSnare: "Cymatics - Orchid Snare - Tonal 1 (F)",
    defaultCrash: "Cymatics - Orchid Ride - Flanged",
    resources: [
      {
        folderName: "/808s & Basses",
        sampleNames: [
          { name: "Cymatics - Orchid 808 Cyclical (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid 808 Heavyweight (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid 808 Offset (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid 808 Rum (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid 808 Short (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid 808 Standard (C)", category: AudioSampleCategories.EightZeroEight },
          { name: "Cymatics - Orchid BASS Brooding (C)", category: AudioSampleCategories.Bass },
          { name: "Cymatics - Orchid BASS Perfect Donk (C)", category: AudioSampleCategories.Bass },
          { name: "Cymatics - Orchid BASS Reaper (C)", category: AudioSampleCategories.Bass },
          { name: "Cymatics - Orchid BASS Unstable (C)", category: AudioSampleCategories.Bass },
          { name: "Cymatics - Orchid REESE Eternity (C)", category: AudioSampleCategories.ReeseBass },
          { name: "Cymatics - Orchid REESE Faith (C)", category: AudioSampleCategories.ReeseBass },
          { name: "Cymatics - Orchid REESE Punchy (C)", category: AudioSampleCategories.ReeseBass },
          { name: "Cymatics - Orchid REESE Toronto (C)", category: AudioSampleCategories.ReeseBass },
          { name: "Cymatics - Orchid REESE Whale (C)", category: AudioSampleCategories.ReeseBass }
        ]
      },
      {
        folderName: "/Drum One Shots",
        sampleNames: [
          { name: "Cymatics - Orchid Clap - Basic", category: AudioSampleCategories.Clap },
          { name: "Cymatics - Orchid Clap - Flam", category: AudioSampleCategories.Clap },
          { name: "Cymatics - Orchid Clap - Loose", category: AudioSampleCategories.Clap },
          { name: "Cymatics - Orchid Clap - Pop", category: AudioSampleCategories.Clap },
          { name: "Cymatics - Orchid Clap - Tone (C)", category: AudioSampleCategories.Clap },
          { name: "Cymatics - Orchid Crash - Light Highs", category: AudioSampleCategories.Crash },
          { name: "Cymatics - Orchid Crash - Perfect", category: AudioSampleCategories.Crash },
          { name: "Cymatics - Orchid Hihat - Closed 1", category: AudioSampleCategories.ClosedHihat },
          { name: "Cymatics - Orchid Hihat - Closed 2", category: AudioSampleCategories.ClosedHihat },
          { name: "Cymatics - Orchid Hihat - Closed 3", category: AudioSampleCategories.ClosedHihat },
          { name: "Cymatics - Orchid Hihat - Flam", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Hihat - Halftime", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Hihat - Open 1", category: AudioSampleCategories.OpenHihat },
          { name: "Cymatics - Orchid Hihat - Open 2", category: AudioSampleCategories.OpenHihat },
          { name: "Cymatics - Orchid Hihat - Open 3", category: AudioSampleCategories.OpenHihat },
          { name: "Cymatics - Orchid Hihat - Roll 1", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Hihat - Roll 2", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Hihat - Roll 3", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Hihat - Tick", category: AudioSampleCategories.HiHat },
          { name: "Cymatics - Orchid Kick - Clean (F)", category: AudioSampleCategories.Kick },
          { name: "Cymatics - Orchid Kick - Dancehall (A#)", category: AudioSampleCategories.Kick },
          { name: "Cymatics - Orchid Kick - Layered (F#)", category: AudioSampleCategories.Kick },
          { name: "Cymatics - Orchid Kick - Tight (G)", category: AudioSampleCategories.Kick },
          { name: "Cymatics - Orchid Percussion - Dry 1", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 2", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 3 (C)", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 4 (C)", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 5", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 6", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 7", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Dry 8", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Wet 1 (C)", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Wet 2", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Wet 3", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Percussion - Wet 4", category: AudioSampleCategories.Percussion },
          { name: "Cymatics - Orchid Ride - Flanged", category: AudioSampleCategories.Ride },
          { name: "Cymatics - Orchid Ride - Mysterious", category: AudioSampleCategories.Ride },
          { name: "Cymatics - Orchid Rimshot - Basic", category: AudioSampleCategories.RimShot },
          { name: "Cymatics - Orchid Rimshot - Bunker", category: AudioSampleCategories.RimShot },
          { name: "Cymatics - Orchid Rimshot - Underwater", category: AudioSampleCategories.RimShot },
          { name: "Cymatics - Orchid Rimshot - Wiz", category: AudioSampleCategories.RimShot },
          { name: "Cymatics - Orchid Shaker - Banana", category: AudioSampleCategories.Shaker },
          { name: "Cymatics - Orchid Shaker - Crisp", category: AudioSampleCategories.Shaker },
          { name: "Cymatics - Orchid Shaker - Drew", category: AudioSampleCategories.Shaker },
          { name: "Cymatics - Orchid Shaker - Tight", category: AudioSampleCategories.Shaker },
          { name: "Cymatics - Orchid Snap - Cream", category: AudioSampleCategories.Snap },
          { name: "Cymatics - Orchid Snap - Hefty", category: AudioSampleCategories.Snap },
          { name: "Cymatics - Orchid Snap - Single", category: AudioSampleCategories.Snap },
          { name: "Cymatics - Orchid Snare - Arrow (G#)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Breeze (E)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Heft (D#)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Lustbug (C)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Tonal 1 (F)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Tonal 2 (C)", category: AudioSampleCategories.Snare },
          { name: "Cymatics - Orchid Snare - Wanted (A#)", category: AudioSampleCategories.Snare }
        ]
      }
    ]
  };

  export const getSanitizedSampleName = (name: string): string | undefined => {
    if (!name) return undefined;
    return name.toLowerCase().replaceAll(" ", "_").replaceAll("(", "_").replaceAll(")", "_");
  };

  export const LIST_OF_DRUM_KITS = [
    Cymatics
  ];

  export const DEFAULT_DRUM_KIT = Cymatics;
}

type onMessageParse = (eventName: PianoRhythmSynthEventName, event: PianoRhythmSynthEvent, source?: string) => void;

export const decodeSynthEvent = (data: Uint8Array): PianoRhythmSynthEvent | undefined => {
  try {
    if (!data || data.length == 0) return;
    return decode(data) as PianoRhythmSynthEvent;
  } catch (ex) {
    if (COMMON.IS_DEV_MODE) console.error("DECODE EVENT ERROR", typeof data, data, ex);
    return undefined;
  }
};

export function SynthEventsProcessor(onParsed?: onMessageParse) {
  let _onParsed: onMessageParse | undefined = onParsed;
  const dispose = () => {
  };

  const processEvent = (onParsed: onMessageParse) => (event: PianoRhythmSynthEvent | undefined, source?: string) => {
    if (!event) return;
    let eventName = event.message_type as string | undefined;
    if (isNumber(event.message_type)) {
      eventName = PianoRhythmSynthEvent.SanitizeEventName(PianoRhythmSynthEvent.fromMessageTypeU8(event.message_type));
    } else if (isString(event.message_type)) {
      eventName = PianoRhythmSynthEvent.SanitizeEventName(event.message_type);
    }
    if (!eventName) return;
    onParsed(eventName as PianoRhythmSynthEventName, event, source);
  };

  const onAudioChannelMessage = (onParsed: onMessageParse) => ((evt: MessageEvent) => {
    if (!evt.data) return;
    let data = evt.data as Uint8Array;

    try {
      processEvent(onParsed)(decodeSynthEvent(data));
    } catch (ex) {
      if (COMMON.IS_DEV_MODE) console.error("onAudioChannelMessage ERROR", typeof data, data, ex);
    }
  });

  const handleSynthEventBytes = (data: Uint8Array, source?: string) => {
    if (_onParsed) processEvent(_onParsed)(decodeSynthEvent(data), source);
  };

  const processSynthEvent = (event: PianoRhythmSynthEvent, source?: string) => {
    if (_onParsed) processEvent(_onParsed)(event, source);
  };

  return {
    dispose,
    handleSynthEventBytes,
    processEvent,
    processSynthEvent,
    decodeEvent: decodeSynthEvent,
    onAudioChannelMessage
  };
}

/** @ignore */
// export function mapMidiDtoToWebMidiPayload(dto: MidiDto) {
//   return match(dto)
//     .with([ "NoteOn", P.any ], ([, noteOn]) =>
//     ({
//       eventName: "note-on",
//       channel: noteOn.channel || 0,
//       note1: noteOn.note,
//       note2: noteOn.velocity || 0,
//       program: noteOn.program || 0,
//       volume: noteOn.volume,
//       bank: noteOn.bank || 0,
//       pitch: noteOn.pitch,
//       expression: noteOn.expression,
//       pan: noteOn.pan,
//     } as WebMidiPayload))
//     .with([ "NoteOff", P.not(P.nullish) ], ([, noteOff]) =>
//       ({ eventName: "note-off", channel: noteOff.channel || 0, note1: noteOff.note } as WebMidiPayload)
//     )
//     .with(["Sustain", P.when(x => x.value)], ([, sustain]) =>
//       ({ eventName: "damper-on", channel: sustain.channel || 0, note1: 64 } as WebMidiPayload)
//     )
//     .with(["Sustain", P.when(x => !x.value)], ([, sustain]) =>
//       ({ eventName: "damper-off", channel: sustain.channel || 0, note1: 0 } as WebMidiPayload)
//     )
//     .with(["AllSoundOff", P.when(x => x.channel)], ([, allSoundOff]) =>
//       ({ eventName: "all-sound-off", channel: allSoundOff.channel || 0 } as WebMidiPayload)
//     )
//     .otherwise(() => ({ eventName: "unknown", channel: -1 }) as WebMidiPayload);
// }