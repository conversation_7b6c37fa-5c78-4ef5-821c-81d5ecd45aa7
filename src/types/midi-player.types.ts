export type PlayerEvent = {
  "track": number,
  "delta": number,
  "tick": number,
  "byteIndex": number,
  "channel": number,
  "noteNumber": number,
  "velocity": number;
  "noteName": string,
  "name": string,
};

export enum MidiPlayerState {
  "Stopped",
  "NotLoaded",
  "Paused",
  "Playing",
  "Resumed",
}

export type MidiPlayerCurrentState = {
  noteScaleModifier?: number;
  playbackSpeed?: number;
  currentTime?: number;
  playerState: MidiPlayerState;
  currentTick?: number;
  totalTime?: number;
  fileName?: string;
  lyrics?: string[];
  trackNames?: string[];
  copyRightNotice?: string[];
  texts?: string[];
  markerTexts?: string[];
  programChanges?: { channel: number, bank: number, program: number, tick: number; time: number; }[];
  tempoChanges?: { time: number, tempo: number; }[];
  playing?: boolean;
  paused?: boolean;
  autoScroll?: boolean;
  enableSustain?: boolean;
  broadcastNotes?: boolean;
  looping?: boolean;
  bpm?: number;
  ppq?: number;
};

export const DefaultMidiPlayerCurrentState: MidiPlayerCurrentState = {
  playbackSpeed: 1,
  playerState: MidiPlayerState.NotLoaded,
  currentTime: 0,
  currentTick: 0,
  totalTime: 0,
  broadcastNotes: true,
  fileName: undefined,
  lyrics: [],
  trackNames: [],
  copyRightNotice: [],
  texts: [],
  markerTexts: [],
  programChanges: [],
  tempoChanges: [],
  playing: false,
  autoScroll: true,
  enableSustain: false,
  paused: false,
  looping: false,
  bpm: undefined,
  ppq: undefined,
};