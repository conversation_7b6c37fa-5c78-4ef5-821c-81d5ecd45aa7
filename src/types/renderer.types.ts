import { AppSceneMode, AppSettings } from "~/proto/pianorhythm-app-renditions";
import { WasmSynth } from "./audio.types";
import { RoomStages, RoomStageVisualEffects, RoomType } from "~/proto/room-renditions";
import { ClientSideUserDto, UserClientDto } from "~/proto/user-renditions";
import { ActiveChannelsMode } from "~/proto/midi-renditions";
import { AppStateActions } from "~/proto/pianorhythm-actions";
import { AppStateEffects } from "~/proto/pianorhythm-effects";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import { GameModeState } from "./app.types";

export type RenderingStatsInfo = {
  drawCalls: number;
  totalVertices: number;
  activeTriangles: number;
  totalMeshes: number;
  activeIndices: number;
  activeMeshes: number;
  totalTextures: number;
  totalMaterials: number;
  totalLights: number;
  activeBones: number;
  deltaTime: number;
  version: string;
  rendererInfo: string;
};

export interface IRenderingEngine {
  loadMidiByData(data: Uint8Array, fileName?: string): void;
  initialize: (webGpuSupported?: boolean, rootURL?: string) => void;
  onSetupApplication: () => void;
  setupInstrumentation: () => void;
  onRoomLoaded: (stage: RoomStages, roomType: RoomType) => void;
  onShowFPS: () => void;
  emitPicked: () => void;
  onResize?: (width?: number, height?: number) => void;
  getCanvasDimensions: () => { width: number, height: number };
  onInitialized: () => void;
  addProxyElement: (canvas: HTMLCanvasElement, proxyObj?: any) => Promise<void>;
  onLoadDesktop: (synth?: WasmSynth) => void;
  onLoadWeb: (payload: any, isDesktop?: boolean, targetFPS?: number) => Promise<IRenderingEngine>;
  setInitialAppSettings: (payload: any) => void;
  onCleanup: () => void;
  showKeyboardVisualMapping: (input: Uint8Array) => void;
  hideKeyboardVisualMapping: () => void;
  setRoomOwner: (input: string) => void;
  setThemeColors: (input: Uint8Array) => void;
  getFps: () => void;
  setSceneMode: (input: AppSceneMode) => void;
  setHashedUsers: (input: { socketID: string, hash: number; }[]) => void;
  setChannelsWithInstrumentLoaded: (input: number[]) => void;
  setDisabledChannels: (input: number[]) => void;
  setDrumChannelMuted: (input: boolean) => void;
  setSpecialEffects: (input: RoomStageVisualEffects) => void;
  enableAmbientOcclusion: (value: boolean) => void;
  enableDepthOfFieldLensEffect: (value: boolean) => void;
  setClient: (user: UserClientDto) => void;
  setCanInteract: (input: boolean) => void;
  setCanPlayKeys: (input: boolean) => void;
  setDrumChannelActive: (input: boolean) => void;
  getGPUStats: () => void;
  setAudioServiceInitialized: (input: boolean) => void;
  SERVER_TIME_OFFSET: () => void;
  onAvatarCreatorUpdate: (input: any) => void;
  onAvatarCreatorSetCameraAngle: (input: string) => void;
  setAvatarCreatorAnimation: (input: string) => void;
  setAvatarCreatorScreenActive: (input: boolean) => void;
  setOrchestraModelCustomizationScreenActive: (input: boolean) => void;
  updateOrchestraModelDecalAngle: (input: any) => void;
  updateOrchestraModel: (input: any) => void;
  updateOrchestraModelDecalSize: (input: any) => void;
  updateDevicePixelRatio: (dpr: number) => void;
  getStats: () => RenderingStatsInfo | null;
  updateOrchestraModelDecalColor: (input: any) => void;
  setOrchestraModelDecal: (input: any) => void;
  setOrchestraModelDecalEditMode: (input: any) => void;
  resetCameraPosition: () => void;
  setCinemaModeActive: (input: boolean) => void;
  setDisplayRenderingStats: (input: boolean) => void;
  onReset: () => void;
  setIsMobile: (input: boolean) => void;
  setSlotMode: (input: ActiveChannelsMode) => void;
  setPrimaryChannel: (channel: number) => void;
  setDocumentLocation: (input: any) => void;
  setSettings: (input: AppSettings) => void;
  updateCharacter: (input: any) => void;
  users: () => ClientSideUserDto[];
  setUsers: (users: ClientSideUserDto[]) => void;
  setKeysWithNoSound: (input: number[]) => void;
  updateCommonConst: (input: any) => void;
  setRoomID: (input?: string) => void;
  toggleLockCamera: () => void;
  onAppStateEffect: (effect: AppStateEffects) => void;
  onAppRenderEffect: (effect: Uint8Array) => void;
  onAppStateEvent: (event: AppStateEvents) => void;
  onAppStateActions: (action: AppStateActions) => void;
  setCameraToDefaultView: () => void;
  setMidiPlayerTime_Done: () => void;
  decreaseMidiPlayerPlaybackSpeed: () => void;
  increaseMidiPlayerPlaybackSpeed: () => void;
  resumeMidiPlayer: () => void;
  stopMidiPlayer: () => void;
  emitMidiPlayerCompleteState: () => void;
  setMidiPlayerTime: (input: number) => void;
  pauseMidiPlayer: () => void;
  setMidiPlayerTime_Start: (input: number) => void;
  setGameModeState: (mode: GameModeState) => void;
  handleSynthEventBytes: (data: Uint8Array, source?: string) => void;
  handleRenderEventBytes: (data: Uint8Array) => void;
  handleSynthEvent: (raw: Uint8Array, socket_id: number | undefined, source: number | undefined) => void;
  emit_core_app_actions(bytes: Uint8Array): void;
}

export const DefaultRenderingEngine: IRenderingEngine = {
  initialize: () => { },
  setThemeColors: (_: Uint8Array) => { },
  showKeyboardVisualMapping: (_: Uint8Array) => { },
  hideKeyboardVisualMapping: () => { },
  handleSynthEvent: () => { },
  onSetupApplication: () => { },
  toggleLockCamera: () => { },
  setupInstrumentation: () => { },
  setCameraToDefaultView: () => { },
  onRoomLoaded: () => { },
  onShowFPS: () => { },
  emitPicked: () => { },
  enableAmbientOcclusion: () => { },
  enableDepthOfFieldLensEffect: () => { },
  onResize: (width?: number, height?: number) => { },
  onInitialized: () => { },
  addProxyElement: (canvas: HTMLCanvasElement, proxyObj?: any) => Promise.resolve(),
  onLoadDesktop: (synth?: WasmSynth) => { },
  onLoadWeb: (payload: any) => Promise.resolve(null!),
  setInitialAppSettings: (payload: any) => { },
  onCleanup: () => { },
  setRoomOwner: () => { },
  getFps: () => { },
  setSceneMode: () => { },
  setChannelsWithInstrumentLoaded: () => { },
  setDisabledChannels: () => { },
  setDrumChannelMuted: () => { },
  setSpecialEffects: () => { },
  setClient: (user: UserClientDto) => { },
  setCanInteract: () => { },
  getGPUStats: () => { },
  setAudioServiceInitialized: () => { },
  SERVER_TIME_OFFSET: () => { },
  onAvatarCreatorUpdate: () => { },
  onAvatarCreatorSetCameraAngle: () => { },
  setAvatarCreatorAnimation: () => { },
  setAvatarCreatorScreenActive: () => { },
  setOrchestraModelCustomizationScreenActive: () => { },
  updateOrchestraModelDecalAngle: () => { },
  updateOrchestraModel: () => { },
  updateOrchestraModelDecalSize: () => { },
  updateDevicePixelRatio: () => { },
  getStats: () => { return null; },
  updateOrchestraModelDecalColor: () => { },
  setOrchestraModelDecal: () => { },
  setOrchestraModelDecalEditMode: () => { },
  resetCameraPosition: () => { },
  setCinemaModeActive: () => { },
  setDisplayRenderingStats: () => { },
  onReset: () => { },
  setIsMobile: () => { },
  setSlotMode: () => { },
  setPrimaryChannel: () => { },
  setDocumentLocation: () => { },
  setSettings: () => { },
  updateCharacter: () => { },
  users: () => [],
  setUsers: (users: ClientSideUserDto[]) => { },
  setKeysWithNoSound: () => { },
  updateCommonConst: (input: any) => { },
  setRoomID: () => { },
  setGameModeState: function (mode: GameModeState): void {
  },
  setCanPlayKeys: function (input: boolean): void {
  },
  setDrumChannelActive: function (input: boolean): void {
  },
  setMidiPlayerTime_Done: function (): void {
  },
  decreaseMidiPlayerPlaybackSpeed: function (): void {
  },
  increaseMidiPlayerPlaybackSpeed: function (): void {
  },
  resumeMidiPlayer: function (): void {
  },
  stopMidiPlayer: function (): void {
  },
  emitMidiPlayerCompleteState: function (): void {
  },
  setMidiPlayerTime: function (input: number): void {
  },
  pauseMidiPlayer: function (): void {
  },
  setMidiPlayerTime_Start: function (input: number): void {
  },
  loadMidiByData: function (data: Uint8Array, fileName: string): void {
  },
  handleSynthEventBytes: function (data: Uint8Array, source?: string | undefined): void {
  },
  setHashedUsers: function (input: { socketID: string; hash: number; }[]): void {
  },
  onAppStateEffect: function (effect: AppStateEffects): void {
  },
  onAppStateEvent: function (effect: AppStateEvents): void {
  },
  onAppStateActions: function (effect: AppStateActions): void {
  },
  handleRenderEventBytes: function (data: Uint8Array): void {
  },
  onAppRenderEffect: function (effect: Uint8Array): void {
  },
  emit_core_app_actions: function (bytes: Uint8Array): void {
  },
  getCanvasDimensions: function (): { width: number; height: number; } {
    return { width: 0, height: 0 };
  }
};

export type BevyRenderer = {
  create_window_by_canvas: (handle: bigint, canvas: string, devicePixelRatio: number) => void;
  create_window_by_offscreen_canvas: (handle: bigint, canvas: OffscreenCanvas, devicePixelRatio: number) => void;
  default: () => Promise<void>;
  enter_frame: (handle: bigint) => void;
  get_wasm_memory: () => any;
  get_wasm_module: () => any;
  initSync: () => void;
  create_app: (disable_canvas_view: boolean) => bigint;
  is_preparation_completed: (handle: bigint) => number;
  left_bt_down: (handle: bigint) => void;
  left_bt_up: (handle: bigint) => void;
  main_wasm: () => void;
  window_resize: (handle: bigint, width: number, height: number, width2: number, height2: number) => void;
  mouse_bt_down: (handle: bigint, index: number, x: number, y: number) => void;
  mouse_bt_up: (handle: bigint, index: number) => void;
  mouse_move: (handle: bigint, x: number, y: number, deltaX: number, deltaY: number) => void;
  mouse_wheel: (handle: bigint, x: number, y: number, delta: number) => void;
  release_app: (handle: bigint) => void;
  scale_factor_change: (handle: bigint, scale_factor: number) => void;
  note_on: (handle: bigint, channel: number, note: number, velocity: number) => void;
  note_off: (handle: bigint, channel: number, note: number) => void;
  key_down: (handle: bigint, code: string, key: string,) => void;
  key_up: (handle: bigint, code: string, key: string,) => void;
  window_cursor_entered: (handle: bigint) => void;
  window_cursor_left: (handle: bigint) => void;
  window_occluded: (handle: bigint, occluded: boolean) => void;
  window_focused: (handle: bigint, focused: boolean) => void;
  canvas_keyboard_focus_lost: (handle: bigint) => void;
};

export const DefaultBevyRenderer: BevyRenderer = {
  create_window_by_canvas: () => { },
  create_window_by_offscreen_canvas: () => { },
  default: () => Promise.resolve(),
  enter_frame: () => { },
  get_wasm_memory: () => null,
  get_wasm_module: () => null,
  initSync: () => { },
  create_app: () => BigInt(0),
  is_preparation_completed: () => 0,
  left_bt_down: () => { },
  left_bt_up: () => { },
  main_wasm: () => { },
  window_resize: () => { },
  mouse_bt_down: () => { },
  mouse_bt_up: () => { },
  mouse_move: () => { },
  mouse_wheel: () => { },
  release_app: () => { },
  scale_factor_change: () => { },
  note_on: () => { },
  note_off: () => { },
  key_down: () => { },
  key_up: () => { },
  window_cursor_entered: () => { },
  window_cursor_left: () => { },
  window_occluded: () => { },
  window_focused: () => { },
  canvas_keyboard_focus_lost: () => { },
}