export type WebMidiParseEvent =
  | "note-on"
  | "note-off"
  | "damper-on"
  | "damper-off"
  | "bank-select"
  | "bank-select-fine"
  | "expression-controller"
  | "program-change"
  | "volume"
  | "volume-fine"
  | "pitch-bend"
  | "pan"
  | "attack-time"
  | "decay-time"
  | "reverb-depth"
  | "mod-wheel"
  | "all-sound-off"
  | "all-notes-off"
  | "timbre-intensity"
  | "reset-all-controllers"
  | "raw-bytes"
  | "unknown"
  // Custom
  | "display-instrument"
  | "synth-note-on"
  | "synth-note-off"
  | "synth-reset-all-controllers"
  | "synth-all-sound-off"
  | "synth-all-notes-off"
  | "synth-program-change"
  | "synth-bank-select"
  | "synth-damper-on"
  | "synth-damper-off"
  | "synth-pan"
  | "synth-volume"
  | "load-wasm-module";

export enum MIDIChannelMessages {
  unknown = -1,
  noteoff = 0x8,  // 8
  noteon = 0x9,  // 9
  channelaftertouch = 0xD,  // 13 (AKA Mono Pressure)
  keyaftertouch = 0xA,  // 10 (AKA Poly Pressure)
  programchange = 0xC,  // 12
  pitchbend = 0xE,  // 14
  controlchange = 0xB,  // 11
}

export enum MidiControlChangeMessages {
  bankselectcoarse = 0,
  modulationwheelcoarse = 1,
  breathcontrollercoarse = 2,
  footcontrollercoarse = 4,
  portamentotimecoarse = 5,
  dataentrycoarse = 6,
  volumecoarse = 7,
  balancecoarse = 8,
  pancoarse = 10,
  expressioncoarse = 11,
  effectcontrol1coarse = 12,
  effectcontrol2coarse = 13,
  generalpurposeslider1 = 16,
  generalpurposeslider2 = 17,
  generalpurposeslider3 = 18,
  generalpurposeslider4 = 19,
  bankselectfine = 32,
  modulationwheelfine = 33,
  breathcontrollerfine = 34,
  footcontrollerfine = 36,
  portamentotimefine = 37,
  dataentryfine = 38,
  volumefine = 39,
  balancefine = 40,
  panfine = 42,
  expressionfine = 43,
  effectcontrol1fine = 44,
  effectcontrol2fine = 45,
  holdpedal = 64,
  portamento = 65,
  sustenutopedal = 66,
  softpedal = 67,
  legatopedal = 68,
  hold2pedal = 69,
  soundvariation = 70,
  resonance = 71,
  soundreleasetime = 72,
  soundattacktime = 73,
  brightness = 74,
  soundcontrol6 = 75,
  soundcontrol7 = 76,
  soundcontrol8 = 77,
  soundcontrol9 = 78,
  soundcontrol10 = 79,
  generalpurposebutton1 = 80,
  generalpurposebutton2 = 81,
  generalpurposebutton3 = 82,
  generalpurposebutton4 = 83,
  reverblevel = 91,
  tremololevel = 92,
  choruslevel = 93,
  celestelevel = 94,
  phaserlevel = 95,
  databuttonincrement = 96,
  databuttondecrement = 97,
  nonregisteredparametercoarse = 98,
  nonregisteredparameterfine = 99,
  registeredparametercoarse = 100,
  registeredparameterfine = 101,
  allsoundoff = 120,
  resetallcontrollers = 121,
  localcontrol = 122,
  allnotesoff = 123,
  omnimodeoff = 124,
  omnimodeon = 125,
  monomodeon = 126,
  polymodeon = 127,
  unknown = -1
}

/// 1 - Keyboard, 2 - Mouse, 3 - Midi, 4 - MidiPlayerPreview, 5 - Keyboard Wasm, Ignored - 255
export type MidiNoteSource = 0 | 1 | 2 | 3 | 4 | 5 | 255;

export type WebMidiPayload = {
  eventName: WebMidiParseEvent;
  channel: number;
  bytes?: Uint8Array;
  socketID?: string;
  deviceID?: number;
  note1?: number;
  note2?: number;
  program?: number;
  volume?: number;
  bank?: number;
  pitch?: number;
  expression?: number;
  pan?: number;
  source?: MidiNoteSource;
};