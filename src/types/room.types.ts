import { RoomHostDetails, RoomStageAudioEffects, RoomStageDetails, RoomStages, RoomStatus, RoomType, roomTypeToJSON } from "~/proto/room-renditions";
import { CreateRoomParam } from "~/proto/server-message";

/** @ignore */
export const ROOM_CONST = {
  MAX_ROOMNAME_LENGTH: 24,
  MAX_PASSWORD_LENGTH: 16,
  MAX_WELCOME_MESSAGE_LENGTH: 1024,
  MAX_LOBBY_PLAYERS: 30,
  MAX_TOTAL_PLAYERS: 20,
  MAX_SELF_HOSTED_TOTAL_PLAYERS: 50,
};


/** @ignore */
export namespace RoomModes {
  export const IGNORED_MODES = [
    // Ignored until migration is complete in core
    RoomType.Orchestra,
    RoomType.LobbyOrchestra,
  ];
}

/** @ignore */
export namespace RoomStagesNS {
  export const DEFAULT_STAGE = RoomStages.THE_VOID;
  export const IGNORED_STAGES = [
    RoomStages.GRASSLAND,
    RoomStages.TORII_GATE,
    RoomStages.KINGS_HALL,
    RoomStages.CLOUD_PALACE,
    // RoomStages.UNKNOWN,
    RoomStages.UNRECOGNIZED,
  ];
}

export type RoomDto = {
  roomName: string;
  roomID: string;
  roomOwner: string;
  users: string[];
};

export type RoomSettingsBasicDto = {
  MaxPlayers: number;
  MaxChatHistory: number;
  RoomStatus: RoomStatus;
  OnlyOwnerCanPlay: boolean;
  OnlyOwnerCanChat: boolean;
  AllowBlackMidi: boolean;
  AllowGuests: boolean;
  AllowBots: boolean;
  OnlyMods: boolean;
  NoChatAllowed: boolean;
  NoPlayingAllowed: boolean;
  FilterProfanity: boolean;
  HostDetails?: RoomHostDetails;
  stageDetailsJSON?: string;
};

export type RoomProfileBasicInfoDto = {
  roomName: string;
  roomID: string;
  roomOwner: string;
  settings: RoomSettingsBasicDto;
  stage: RoomStages;
};

export type RoomDtoAPI = {
  roomName: string;
  roomId: string;
  roomOwner: string;
  roomType: RoomType;
  created: string;
  users: string[];
  maxPlayers: number;
  isPasswordProtected: boolean;
  stageDetailsJSON?: string;
  selfHostedCountryCode?: string;
};

/** @ignore */
export namespace RoomTypeHelper {
  export const IsOrchestra = (x: RoomType) => roomTypeToJSON(x)?.toLowerCase().includes("orchestra");

  export const IsWorld = (x: RoomType) => x == RoomType.World || x == RoomType.WorldLobby;

  export const IsLobby = (x: RoomType) => roomTypeToJSON(x)?.toLowerCase().includes("lobby");

  export const List: RoomType[] = [
    RoomType.Lobby,
    RoomType.LobbyTurnBased,
    RoomType.World,
    RoomType.WorldLobby,
    RoomType.Normal,
    RoomType.TurnBased,
    RoomType.Game,
    // RoomType.GuestLobby,
    // RoomType.SoloTraining,
    // RoomType.Orchestra
  ];

  export const CreateRoomList = [
    "Normal",
    "TurnBased",
    "Game",
    "SoloTraining",
    "Orchestra"
  ];
}

// export type RoomStageVisualEffects = {
//   rain?: boolean;
//   grass?: boolean;
//   postProcess_blackWhite?: boolean;
//   postProcess_downSample?: boolean;
//   postProcess_toneMap?: boolean;
//   postProcess_emboss?: boolean;
//   postProcess_vignette?: boolean;
// };

// export type RoomStageAudioEffect = {
//   volume: number;
//   loop: boolean;
//   playbackRate: number;
//   pan: number;
// };

// export type RoomStageAudioEffects = {
//   rain?: RoomStageAudioEffect;
//   wind?: RoomStageAudioEffect;
//   birds?: RoomStageAudioEffect;
// };

// export type RoomStageDetails = {
//   stage?: RoomStages;
//   effects?: RoomStageVisualEffects;
//   audioEffects?: RoomStageAudioEffects;
// };

export namespace RoomStageAudioEffectsNS {
  export const DEFAULT_VOLUME = 0.5;
  export const DEFAULT_LOOP = true;
  export const DEFAULT_PLAYBACK_RATE = 1;
  export const DEFAULT_STEREO = 0;
  export const DEFAULT_PAN = 0;

  export const DEFAULT: RoomStageAudioEffects = {
    rain: {
      volume: DEFAULT_VOLUME,
      loop: DEFAULT_LOOP,
      playbackRate: DEFAULT_PLAYBACK_RATE,
      pan: DEFAULT_PAN
    },
    wind: {
      volume: DEFAULT_VOLUME,
      loop: DEFAULT_LOOP,
      playbackRate: DEFAULT_PLAYBACK_RATE,
      pan: DEFAULT_PAN
    },
    birds: {
      volume: DEFAULT_VOLUME,
      loop: DEFAULT_LOOP,
      playbackRate: DEFAULT_PLAYBACK_RATE,
      pan: DEFAULT_PAN
    }
  };
}

/** @ignore */
export const DEFAULT_CREATE_ROOM_PARAM: CreateRoomParam = {
  WelcomeMessage: "Welcome to my room, %%user%%!",
  RoomName: "",
  Password: "",
  RoomType: RoomType.Normal,
  RoomStatus: RoomStatus.Public,
  RoomOwner: "system",
  MaxPlayers: ROOM_CONST.MAX_TOTAL_PLAYERS,
  OnlyOwnerCanPlay: false,
  OnlyOwnerCanChat: false,
  AllowGuests: true,
  AllowBots: true,
  AllowBlackMidi: false,
  OnlyMods: false,
  FilterProfanity: true,
  AutoRemove: true,
  StageDetailsPROTO: RoomStageDetails.encode(RoomStageDetails.create({
    stage: RoomStagesNS.DEFAULT_STAGE
  })).finish(),
  HostDetails: undefined,
  RoomID: undefined,
  NoChatAllowed: false,
  NoPlayingAllowed: false
};

export enum RoomSortOptions {
  Default = "Default",
  Newest = "Newest",
  Oldest = "Oldest",
  MostUsers = "Most Users",
  LeastUsers = "Least Users",
}