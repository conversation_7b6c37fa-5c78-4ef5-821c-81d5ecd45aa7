export type ValidationError = { Reason: string; };

export type WebpageMetaDataOutput = {
  url: string;
  site?: string;
  title?: string;
  type?: string;
  imageUrl?: string;
  imageWidth?: string;
  imageHeight?: string;
  description?: string;
  videoUrl?: string;
  videoType?: string;
  videoWidth?: string;
  videoHeight?: string;
};

export type RegisterInput = {
  username: string,
  email: string,
  password: string,
  confirmedpassword: string,
  acceptedtos: boolean;
};

export type RegistrationResponse = {
  validationError: ValidationError[];
};

export type RegistrationResponseSuccess = {
  ///Email verified?
  output: boolean;
};

export type LoginResponse = {
  response: "OK" | "Error";
  validationError: ValidationError[];
  token?: string;
  refreshToken?: string;
  tokenExp?: number;
  username?: string;
  usertag?: string;
  roles?: string[];
};

export type LoginActionResponse = {
  token?: string;
  username?: string;
  usertag?: string;
  roles: string[];
  error?: string;
};

export type GoogleOAuthValidationResponse = {
  name: string;
  email: string;
  profile_image_data?: string;
};

export type ClientValidationErrorResponse = {
  validationError?: ValidationError[];
};

export type ForgotPasswordData = { email: string; };

export type ResendEmailVerificationData = { email: string; };

export type ResetPasswordData = { password: string, confirmedpassword: string, token: string; };

export type ImageUploadResponse =
  | "InvalidBase64"
  | "InvalidSession"
  | "InvalidRequest"
  | "InvalidPayload"
  | "NoUserFound"
  | ["UploadError", string]
  | ["FileSizeTooBig", number]
  | "UnknownError"
  | "Success"
  | "GuestsNotAllowed"
  | "FailedToUpdateUser"
  | "ImageDimensionsExceedsMax"
  | "PermissionDenied";

export type SentFriendRequestDto = {
  sentDate: Date;
  usertag: string;
  profileImageLastModified?: Date;
  profileBackgroundImageLastModified?: Date;
};

type PaginatedMetadata = {
  totalCount: number;
  totalPages: number;
};

export type QueryApiInput = {
  query: string;
  sort?: string;
};

export type PaginatedDataResponse<T> = {
  data: T[];
  metaData: PaginatedMetadata;
};

export type ExpressAPIResponse<T> = {
  status: "success" | "error";
  data: T;
};

type AuditLogAction =
  | "RoomCreate"
  | "RoomUpdate"
  | "RoomDelete"
  | "MemberKick"
  | "MemberBanAdd"
  | "MemberBanRemove"
  | "MemberUpdate"
  | "IPUnban"
  | "MemberRoleUpdate"
  | "MemberPermissionsAdd"
  | "MemberPermissionsRemove"
  | "MemberGlobalChatMuteAdd"
  | "MemberGlobalChatMuteRemove"
  | "MemberGlobalNotesMuteAdd"
  | "MemberGlobalNotesMuteRemove"
  | "ChatMessageUpdate"
  | "ChatMessageDelete"
  | "ChatMessageBulkDelete"
  | "SheetMusicUpdate"
  | "SheetMusicDelete"
  | "SheetMusicBulkDelete"
  | "MidiUpdate"
  | "MidiDelete"
  | "MidiBulkDelete"
  | "AccountDeleted"
  | "Unknown";

type AuditLogObjectChange =
  | "User"
  | "SheetMusic"
  | "Midi"
  | "ChatMessage"
  | "Room"
  | "Unknown";

type AuditLogChangeKey =
  | ["Nickname", string]
  | ["RoomDescription", string]
  | ["Permissions", string]
  | ["BadgeAdd", string]
  | ["BadgeRemove", string];

type AuditLogChangeObject = {
  newValue?: AuditLogChangeKey;
  oldValue?: AuditLogChangeKey;
  key: AuditLogObjectChange;
};

type AuditEntryRoomMetaInfo = {
  roomID?: string;
  roomName?: string;
};

type AuditEntryTargerUserMetaInfo = {
  userTag?: string;
  userID?: string;
  userIP?: string;
  socketIP?: string;
};

type AuditEntryInfo = {
  messageID?: string;
  roomMeta?: AuditEntryRoomMetaInfo;
  userMeta?: AuditEntryTargerUserMetaInfo;
  count: number;
};

export type AuditLogDto = {
  id: string;
  userTag: string;
  targetID?: string;
  actionType: AuditLogAction;
  reason?: string;
  changes?: AuditLogChangeObject[];
  createdDate?: Date;
  options?: AuditEntryInfo;
};

export type BannedIPDto = {
  addedBy: string;
  created: string;
  ip: string;
};

export enum MessageApiError {
  InvalidUser = "INVALID_USER",
  Duplicate = "DUPLICATE_RECORD",
  DeleteFailed = "FAILED_TO_DELETE",
  RequestError = "REQUEST_ERROR",
  Default = "UNKNOWN_ERROR",
}

export type BannedAccountDto = {
  usertag: string;
  uuid?: string;
  is_member: boolean;
  ip: string;
  addedBy?: string;
  created: string;
  duration?: number;
  reason?: string;
};