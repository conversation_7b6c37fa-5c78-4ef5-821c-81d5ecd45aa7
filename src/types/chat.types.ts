import { generateUUID, stringToColor } from "~/util/helpers";
import { tryDecodeURI } from '~/util/helpers.dom';

//@ts-ignore
// import emojiSn from 'emoji-to-short-name';
import { ChatMessageDto } from "~/proto/client-message";
import isString from "lodash-es/isString";
import { Roles } from "~/proto/user-renditions";

export type ChatMessageMeta = {
  isBot?: boolean;
  isMod?: boolean;
  isSystem?: boolean;
  isAdmin?: boolean;
  isDev?: boolean;
  isRoomOwner?: boolean;
  isGuest?: boolean;
  isProMember?: boolean;
  autoDelete?: boolean;
};

export type ChatMessageDtoRoles = {
  isBot: boolean;
  isMod: boolean;
  isPlugin: boolean;
  isSystem: boolean;
  isAdmin: boolean;
  isDev: boolean;
  isRoomOwner: boolean;
  isGuest: boolean;
  isProMember: boolean;
};

/** @ignore */
export class ChatMessageRecord {
  highlighted: boolean = false;
  private modified: boolean = false;
  private dto: ChatMessageDto;
  private parsedTimeStamp: Date;
  private _sanitizedMessage: string | null = null;
  private _canBeIgnoredOnScroll = false;

  constructor(_dto: ChatMessageDto) {
    this.dto = _dto;
    this.modified = !isString(this.dto.modifiedDate) && this.dto.modifiedDate != null;

    try {
      this.parsedTimeStamp = new Date(Date.parse(this.dto.ts));
    } catch {
      this.parsedTimeStamp = new Date(-8640000000000000);
    }

    this.sanitizeMessage();
  }

  setMessage(message: string) {
    if (!message) return;
    this.dto.message = message;
    this.sanitizeMessage();
  }

  setUsername(username: string) {
    if (!username) return;
    this.dto.username = username;
  }

  setUsertag(usertag: string) {
    if (!usertag) return;
    this.dto.usertag = usertag;
  }

  updateMessage(message: string) {
    if (!message) return;
    this.dto.message = message;
    this.modified = true;
    this.sanitizeMessage();
  }

  private sanitizeMessage() {
    if (this.dto.message) {
      if (this.dto.message.indexOf("&gt;") == 0) this.dto.message = this.dto.message.replace(/&gt;/g, '>');
      // this._sanitizedMessage = emojiSn.decode(tryDecodeURI(this.dto.message));
      this._sanitizedMessage = (tryDecodeURI(this.dto.message));

      this._canBeIgnoredOnScroll =
        (this.dto.isBot || this.dto.isSystem) && (
          this._sanitizedMessage?.toLowerCase()?.includes("has left the room") ||
          this._sanitizedMessage?.toLowerCase()?.includes("has joined the room") ||
          false
        );
    }
  }

  getDto() { return { ...this.dto } as ChatMessageDto; }

  get wasModified() { return this.modified; }

  get displayName() { return this.dto.nickname || this.username; }

  get id() { return this.dto.messageID; }

  get autoDelete() { return this.dto.autoDelete; }

  get username() { return this.dto.username; }

  get usertag() { return this.dto.usertag; }

  get message() { return this.dto.message; }

  get sanitizedMessage() { return this._sanitizedMessage || this.message; }

  get messageReplyID() { return this.dto.messageReplyID; }

  get socketID() { return this.dto.socketID; }

  get timestamp() { return this.parsedTimeStamp; }

  get isSystem() { return this.dto.isSystem; }

  get isBot() { return this.dto.isBot; }

  get isPlugin() { return this.dto.isPlugin; }

  public canBeIgnoredOnScroll = () => this._canBeIgnoredOnScroll;

  public isNonMember = () => this.roles.isGuest || this.roles.isSystem || this.roles.isBot;

  public isMember = () => !this.isNonMember();

  get roles(): ChatMessageDtoRoles {
    return {
      isPlugin: this.dto.isPlugin,
      isBot: this.dto.isBot,
      isMod: this.dto.isMod,
      isSystem: this.dto.isSystem,
      isAdmin: this.dto.isAdmin,
      isDev: this.dto.isDev,
      isRoomOwner: this.dto.isRoomOwner,
      isGuest: this.dto.isGuest,
      isProMember: this.dto.isProMember,
    };
  }

  get usernameColor() {
    return this.dto.userColor ?? stringToColor(this.username);
  }

  public getRoles = (): Roles[] => {
    let roles: Roles[] = [];
    if (this.dto.isBot) roles.push(Roles.BOT);
    if (this.dto.isMod) roles.push(Roles.MODERATOR);
    if (this.dto.isSystem) roles.push(Roles.SYSTEM);
    if (this.dto.isAdmin) roles.push(Roles.ADMIN);
    if (this.dto.isDev) roles.push(Roles.DEVELOPER);
    if (this.dto.isRoomOwner) roles.push(Roles.ROOMOWNER);
    if (this.dto.isGuest) roles.push(Roles.GUEST);
    if (this.dto.isProMember) roles.push(Roles.PRO);
    if (this.dto.isPlugin) roles.push(Roles.PLUGIN);
    return roles;
  }
}

/** @ignore */
export function CreateChatMessageDtoDefault(message: string, username: string = ""): ChatMessageDto {
  return ChatMessageDto.create({
    id: -1,
    message,
    username,
    messageID: generateUUID(),
    ts: new Date().toString(),
  });
}

export type CommandArgument = {
  name: string;
  optional?: boolean;
  description?: string;
  minLength?: number;
  maxLength?: number;
  type: "number" | "string" | "user" | "boolean" | "command";
};

export type Command = {
  command: string;
  moduleID: string;
  disabled?: boolean;
  disabledReason?: boolean;
  membersOnly?: boolean;
  modOnly?: boolean;
  roomOwnerOnly?: boolean;
  proMemberOnly?: boolean;
  clientSideOnly?: boolean;
  shortDescription?: string;
  helpDescription?: string;
  arguments?: CommandArgument[];
};

export type CommandModule = {
  id: string;
  name: string;
  shortDescription?: string;
  imagePath?: string;
  customModule?: boolean;
};

/** @ignore */
export type ServerRoomChatCommandDetail = {
  Command: string;
  RawMessage?: string;
  Description?: string;
  DetailedDescription?: string;
  ModOnly: boolean;
  RoomOwnerOnly: boolean;
  PrintUsage?: string;
};

/** @ignore */
export namespace ServerRoomChatCommandDetail {
  export const ConvertToCommand = (detail: ServerRoomChatCommandDetail, moduleID: string): Command => {
    return {
      command: detail.Command.toLowerCase(),
      moduleID,
      modOnly: detail.ModOnly,
      roomOwnerOnly: detail.RoomOwnerOnly,
      shortDescription: detail.Description,
      helpDescription: detail.DetailedDescription,
    };
  };
}

/** @ignore */
export const DefaultPianoRhythmChatCommandModule: CommandModule = {
  id: "pianorhythm",
  name: "PianoRhythm",
  shortDescription: "Local Commands",
  imagePath: undefined,
  customModule: false
};

/** @ignore */
export const ModChatCommandsModule: CommandModule = {
  id: "pianorhythm_moderator",
  name: "Moderator Commands",
  imagePath: undefined,
  customModule: false
};

/** @ignore */
export const RoomOwnerChatCommandsModule: CommandModule = {
  id: "pianorhythm_room_owner",
  name: "Room Owner Commands",
  imagePath: undefined,
  customModule: false
};