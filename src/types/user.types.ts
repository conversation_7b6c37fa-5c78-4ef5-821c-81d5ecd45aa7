import { ClientSideUserDto, Roles, UserDto, UserUpdateCommand, WorldData, userStatusFromJSON } from '~/proto/user-renditions';
import { cloneDeep, isArray } from 'lodash-es';
import { AudioSynthesizerEngine } from './audio.types';
import { Instrument } from '~/proto/midi-renditions';
import { parseZipzon } from '~/util/zipzon.util';

export type SocketID = string;
export type RoomID = string;

/** @ignore */
export const USER_CONST = {
  MIN_USERNAME_LENGTH: 3,
  MAX_BASE_USERNAME_LENGTH: 32,
  MAX_EMAIL_LENGTH: 254,
  MAX_CUSTOM_BADGE_LENGTH: 24,
};

export type UserSettings = {
  color?: string;
  nickname?: string;
};


/** @ignore */
export function mapRoleNameToHeader(role: Roles) {
  switch (role) {
    case Roles.ADMIN: return "Creator";
    case Roles.MODERATOR: return "Moderator";
    case Roles.TRIAL_MODERATOR: return "Trial Moderator";
    case Roles.ROOMOWNER: return "Room Owner";
    case Roles.DEVELOPER: return "Developer";
    case Roles.GUEST: return "Guest";
    case Roles.BOT: return "Bot";
    default: return "Member";
  }
}

export type Permissions = "PROFILE_IMAGE_UPLOAD" |
  "SET_NICKNAME" |
  "SET_STATUS_TEXT" |
  "SET_PROFILE_DESCRIPTION" |
  "SHEETMUSIC_UPLOAD" |
  "SELF_HOST_ROOMS";

export enum StatusColor {
  Online = "limegreen",
  Idle = "orange",
  DoNotDisturb = "red",
  AFK = "white",
  Unknown = "gray"
}

/** @ignore */
export class Email {
  static DiscordOAuthEmailSuffix = "-discord";
  static GoogleOAuthEmailSuffix = "-google";
}

/** @ignore */
export const MOD_USER_ROLES: Roles[] = [Roles.ADMIN, Roles.DEVELOPER, Roles.MODERATOR, Roles.TRIAL_MODERATOR];

// export type Badges =
//   "V3_CLOSED_ALPHA_TESTER" |
//   "V2_OG_MEMBER" |
//   "PRO_MEMBER" |
//   "TranslationMasterContributor" |
//   ["TranslationContributor", string] |
//   ["CUSTOM", string];

export type UserNotificationSetting =
  ["FriendsOnline", boolean] |
  ["FriendsOffline", boolean] |
  ["EmitMyStatusToFriends", boolean] |
  ["GetSheetMusicEmails", boolean];

export type UserNotificationSettings = {
  getEmailsForSheetMusicChanges: boolean;
  getEmailsForMidiMusicChanges: boolean;
  getNotificationsForFriendsComingOnline: boolean;
  getNotificationsForFriendsGoingOffline: boolean;
  allowEmittingMyStatusToFriends: boolean;
};

/** @ignore */
export const DEFAULT_UserNotificationSettings: UserNotificationSettings = {
  getEmailsForSheetMusicChanges: false,
  getEmailsForMidiMusicChanges: false,
  getNotificationsForFriendsComingOnline: true,
  getNotificationsForFriendsGoingOffline: false,
  allowEmittingMyStatusToFriends: false,
};

// /**
//  * This represents a user DTO only on the client side.
//  * It'll have a lot of fields that are in common in with
//  * the server-side user DTO.
//  */
// export type ClientSideUserDto = {
//   username: string;
//   usertag: string;
//   socketID: string;
//   isProMember: boolean;
//   selfMuted: boolean;
//   roles: UserRoles[];
//   badges: Badges[];
//   nickname?: string;
//   color?: string;
//   meta?: {
//     //For local development only
//     /** @ignore */
//     is_client_side_dummy_bot: boolean;

//     // Server side
//     bot: boolean;
//     discordBot: boolean;
//     enteredRoomDateTime?: Date;
//     modifiedDate?: Date;
//     clientMetaDetails?: string; //json

//     /// Client side only
//     clientMetaDetailsParsed?: ClientMetaDetails;
//   };
//   /// Client side only
//   localNotesMuted: boolean;
//   /// Client side only
//   localChatMuted: boolean;
//   serverNotesMuted: boolean;
//   serverChatMuted: boolean;
//   displayCursor: boolean;
//   hasProfileImage: boolean;
//   hasProfileBackgroundImage: boolean;
//   ProfileDescription?: string;
//   profileImageLastModified?: Date;
//   profileBackgroundImageLastModified?: Date;
//   worldData?: { characterDataJSON?: string; orchestraModelCustomizationDataJSON?: string; };
//   status: UserStatus;
//   statusText?: string;
// };

export namespace ClientSideUserDtoHelper {
  export const Default = (): ClientSideUserDto => {
    let dto = ClientSideUserDto.create();
    let userDto = UserDto.create();
    userDto.roles = [Roles.GUEST];
    dto.userDto = userDto;
    return dto;
  };

  export const DEFAULT: ClientSideUserDto = ClientSideUserDto.create();

  export const ToNonProxy = (dto: ClientSideUserDto) => {
    let badges = dto.userDto!.badges.map(x => isArray(x) ? [...x] : x).flat();
    let arraySanitized = { ...dto, roles: [...dto.userDto!.roles], badges: [...badges] };
    return cloneDeep(arraySanitized) as ClientSideUserDto;
  };

  export const GetParsedMetaDetails = (dto?: string) => {
    return parseZipzon<ClientMetaDetails>(dto);
  };

  export const updateUserDtoByCommand = (_user: ClientSideUserDto, command: UserUpdateCommand) => {
    if (!_user.userDto) return _user;

    let input = cloneDeep(_user);
    let user = cloneDeep(_user.userDto);
    if (user.meta && command.clientMetaDetails) {
      user.meta.clientMetaDetails = command.clientMetaDetails;
    }

    if (command.userColor != undefined) user.color = command.userColor;
    if (command.userStatus != undefined) user.status = userStatusFromJSON(command.userStatus);
    if (command.profileDescription != undefined) user.ProfileDescription = command.profileDescription as any;
    if (command.selfMuted != undefined) user.selfMuted = command.selfMuted;
    if (command.nickname != undefined) user.nickname = command.nickname;
    if (command.statusText != undefined) user.statusText = command.statusText;
    if (command.serverNotesMuted != undefined) user.serverNotesMuted = command.serverNotesMuted;
    if (command.serverChatMuted != undefined) user.serverChatMuted = command.serverChatMuted;

    if (command.orchestraModel != undefined) {
      user.worldData = user.worldData || WorldData.create({});
      user.worldData.orchestraModelCustomizationDataJSON = command.orchestraModel;
    }

    if (command.profileImageUpdated !== undefined) {
      user.profileImageLastModified = command.profileImageUpdated;
    }

    if (command.profileImageCleared === true) {
      user.profileImageLastModified = undefined;
    }

    if (command.profileBackgroundImageUpdated !== undefined) {
      user.profileBackgroundImageLastModified = command.profileBackgroundImageUpdated;
    }

    if (command.profileBackgroundImageCleared === true) {
      user.profileBackgroundImageLastModified = undefined;
    }

    if (command.badges !== undefined && command.badges.length > 0) {
      user.badges = cloneDeep(command.badges);
    }

    input.userDto = user;
    return input;
  };
}

export type ClientType = "DESKTOP" | "WEB" | "UNKNOWN";

export type ClientMetaDetails = {
  activeSoundfont?: string;
  currentInstrument?: Instrument | Instrument[];
  clientType?: ClientType;
  synthEngine?: AudioSynthesizerEngine;
  graphicsEngine?: "2D" | "3D";
  currentMidiFilePlaying?: string;
  currentSlotMode?: string;
  currentKeyboardLayout?: string;
  environment?: string;
};

export type ApiUserDto = {
  username: string;
  nickname?: string;
  usertag: string;
  roles: string[];
  badges: string[];
  color?: string;
  statusText?: string;
  profileDescription?: string;
  lastOnline?: Date;
  profileImageLastModified?: Date;
  profileBackgroundImageLastModified?: Date;
};

export namespace ApiUserDto {
  export const Default = (): ApiUserDto => ({
    username: "",
    usertag: "",
    roles: [],
    badges: [],
    nickname: undefined,
    color: undefined,
    statusText: undefined,
    profileDescription: undefined,
    lastOnline: undefined,
    profileImageLastModified: undefined,
    profileBackgroundImageLastModified: undefined,
  });

  export const DefaultWithUsername = (username: string, usertag: string) => {
    let user = ApiUserDto.Default();
    user.username = username;
    user.usertag = usertag;
    return user;
  };

  export const DefaultWithUsername2 = (usertag: string) => {
    let user = ApiUserDto.Default();
    user.username = usertag;
    user.usertag = usertag;
    return user;
  };
}