import { ActiveChannelsMode } from "~/proto/midi-renditions";
import { GraphicShadowFilteringMethod, GraphicsMsaaSamples, GraphicsPresets } from "~/proto/pianorhythm-app-renditions";
import { AUDIO, DEFAULT_LANGUAGE } from "~/util/const.common";
import { AppThemes, CustomKeyboardKeyMap, KeyboardLayout } from "./app.types";
import { AudioSynthesizerEngine, DEFAULT_SOUNDFONT, Equalizer, EqualizerBand, EqualizerPreset, SoundfontSetting, SynthInterpolationMethod } from "./audio.types";

// @ignore is for ts-docs
/** @ignore */
export enum KeyboardShortcuts {
  "ToggleInstrumentSelectionDisplay" = "ToggleInstrumentSelectionDisplay"
  , "ToggleInstrumentDockDisplay" = "ToggleInstrumentDockDisplay"
  , "ToggleChatBarFocus" = "ToggleChatBarFocus"
  , "ActivateSustain" = "ActivateSustain"
  , "ToggleSustain" = "ToggleSustain"
  , "TransposeUp" = "TransposeUp"
  , "TransposeDown" = "TransposeDown"
  , "OctaveUp" = "OctaveUp"
  , "OctaveDown" = "OctaveDown"
  , "ToggleMetronome" = "ToggleMetronome"
  , "ToggleKeyboardMappingOverlay" = "ToggleKeyboardMappingOverlay"
  , "ToggleSheetMusicViewer" = "ToggleSheetMusicViewer"
  , "ToggleSheetMusicUpload" = "ToggleSheetMusicUpload"
  , "ToggleSceneWidgets" = "ToggleSceneWidgets"
  // , "ToggleCommandPalette" = "ToggleCommandPalette"
  , "TogglShortcutBindingDisplay" = "TogglShortcutBindingDisplay"
  , "ToggleSettingsDisplay" = "ToggleSettingsDisplay"
  , "ResetOctave" = "ResetOctave"
  , "ResetTranspose" = "ResetTranspose"
  , "ResetChannelsToDefault" = "ResetChannelsToDefault"
  , "ToggleSheetMusicRepoDisplay" = "ToggleSheetMusicRepoDisplay"
  , "ResetInstruments" = "ResetInstruments"
  , "ToggleChatMessagesDisplay" = "ToggleChatMessagesDisplay"
  , "ToggleMidiInputs&OuputsDisplay" = "ToggleMidiInputs&OuputsDisplay"
  , "OpenNewRoomModal" = "OpenNewRoomModal"
  , "MinimizeMaximizeChatMessages" = "MinimizeMaximizeChatMessages"
  , "DebugStats" = "DebugStats"
}

/** @ignore */
export namespace KeyboardShortcuts {
  export function toString(dir: KeyboardShortcuts) {
    return KeyboardShortcuts[dir];
  }

  export function fromString(dir: string): KeyboardShortcuts {
    return (KeyboardShortcuts as any)[dir];
  }
}

/** @ignore */
export type Keybind = {
  command: KeyboardShortcuts;
  description?: string;
  binding?: string;
  altBinding?: string;
};

export type AppSettings = {
  DEFAULT_LANGUAGE: string;
  DISPLAY_PIANO: boolean;
  DISPLAY_FPS: boolean;
  DISCORD_SYNC: boolean;
  DISPLAY_PING: boolean;
  DISPLAY_3D_STATS: boolean;
  DISPLAY_CURSORS: boolean;
  DISPLAY_INST_DOCK: boolean;
  DISPLAY_CHAT: boolean;
  DISPLAY_SCENE_WIDGET_BUTTONS: boolean;
  DISPLAY_WHOISTYPING: boolean;
  KEEP_CHAT_IN_FOCUS: boolean;
  INST_DOCK_TRANSPOSE: number;
  INST_DOCK_OCTAVE: number;
  INST_DOCK_OPACITY: number;
  VOLUME_SAVED: number;
  AUDIO_MAX_POLYPHONY: number;
  AUDIO_VEL_MUTING: number;
  AUDIO_SUSTAIN_CUTOFF: number;
  AUDIO_SUSTAINED_NOTE_FADEOUT_DURATION: number;
  AUDIO_VEL_BOOST: number;
  AUDIO_ENABLE_VEL_BOOST: boolean;
  AUDIO_ENABLE_VEL: boolean;
  AUDIO_ENABLE_REVERB: boolean;
  AUDIO_MIDI_OUTPUT_ONLY: boolean;
  AUDIO_MOUSE_POS_SETS_VELOCITY: boolean;
  AUDIO_ENABLE_DRUM_CHANNEL: boolean;
  AUDIO_BUFFER_SIZE: number;
  AUDIO_OUTPUT_OWN_NOTES_TO_MIDI: boolean;
  CHAT_AUTO_SCROLL: boolean;
  SHOW_EMBEDDED_LINKS: boolean;
  MIDI_LISTEN_TO_PROGRAM_CHANGES: boolean;
  MIDI_AUTO_FILL_EMPTY_CHANNELS: boolean;
  MIDI_USE_DEFAULT_BANK_WHEN_MISSING: boolean;
  AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS: boolean;
  MIDI_ENABLE_STEREO_PANNING: boolean;
  KEYBINDS: Keybind[];
  SLOT_MODE: ActiveChannelsMode;
  MUTED_NOTES_SELF: boolean;
  SEND_WHOISTYPING: boolean;
  KEYBOARD_LAYOUT: KeyboardLayout;
  CUSTOM_KEYBOARD_LAYOUT_KEYBINDS: CustomKeyboardKeyMap[];
  AUTOHIDE_BOTTOMBAR: boolean;
  AUDIO_MAX_NOTE_ON_TIME: number;
  AUDIO_MAX_VELOCITY: number;
  AUDIO_MIN_VELOCITY: number;
  AUDIO_MIN_VOLUME_RELEASE: number;
  SELECTED_SOUNDFONT: SoundfontSetting | string | null;
  CHAT_MESSAGES_MINIMIZED: boolean;
  CHAT_MESSAGES_MAXIMIZED: boolean;
  GRAPHICS_ENABLE_MOTION_BLUR: boolean;
  GRAPHICS_ENABLE_ALL_PARTICLES: boolean;
  GRAPHICS_ENABLE_POST_PROCESSING: boolean;
  GRAPHICS_ENABLE_SHADOWS: boolean;
  GRAPHICS_ENABLE_SOFT_SHADOWS: boolean;
  GRAPHICS_ENABLE_FOG: boolean;
  GRAPHICS_ENABLE_GLOW: boolean;
  GRAPHICS_ENABLE_PIANO: boolean;
  GRAPHICS_ENABLE_STAGE: boolean;
  GRAPHICS_ENABLE_DRUMS: boolean;
  GRAPHICS_ENABLE_ENGINE: boolean;
  GRAPHICS_ENABLE_WEBGPU: boolean;
  GRAPHICS_ONLY_SHOW_PIANO_KEYS: boolean;
  GRAPHICS_DISPLAY_RENDER_STATS: boolean;
  GRAPHICS_ENABLE_AVATARS: boolean;
  GRAPHICS_ENABLE_PHYSICS: boolean;
  GRAPHICS_ENABLE_SPECIAL_EFFECTS: boolean;
  GRAPHICS_ENABLE_AUTO_ANIMATE_TO_INSTRUMENTS: boolean;
  GRAPHICS_ENABLE_ANTIALIAS: boolean;
  GRAPHICS_ENABLE_ORCHESTRA_MODELS: boolean;
  GRAPHICS_RENDER_EVEN_IN_BACKGROUND: boolean;
  GRAPHICS_ENABLE_AMBIENT_OCCLUSION: boolean;
  GRAPHICS_ENABLE_DEPTH_OF_FIELD: boolean;
  GRAPHICS_USE_OFFSCREEN_CANVAS: boolean;
  CHAT_DISABLE_MARKDOWN: boolean;
  CHAT_ENABLE_IMAGE_URL_PREVIEW: boolean;
  ENABLE_DESKTOP_NOTIFICATIONS: boolean;
  ALLOW_USERS_TO_NOTIFY_ME: boolean;
  AUDIO_SAMPLE_RATE: number;
  AUDIO_IGNORE_SOUNDFONT_VOLUME_ENV_DELAY: boolean;
  AUDIO_USE_SEPARATE_DRUM_KIT: boolean;
  AUDIO_REVERB_LEVEL: number;
  AUDIO_REVERB_DAMP: number;
  AUDIO_REVERB_WIDTH: number;
  AUDIO_REVERB_ROOMSIZE: number;
  AUDIO_CACHE_SOUNDFONTS_WEB: boolean;
  DESKTOP_SAVE_SOUNDFONTS: boolean;
  UI_THEME: AppThemes;
  UI_ENABLE_USER_NOTE_ACTIVITIES: boolean;
  UI_ENABLE_SYNTH_NOTE_ACTIVITIES: boolean;
  AUDIO_CHANNEL_INTERPOLATION_METHOD: SynthInterpolationMethod;
  WEBGL_POWER_PREFERENCE: "high-performance" | "low-power" | "default";
  ENABLE_DEBUG_MODE: boolean;
  AUTO_CHECK_FOR_UPDATES: boolean;
  AUDIO_EQUALIZER_PRESET: EqualizerPreset,
  AUDIO_EQUALIZER_BANDS: EqualizerBand[],
  AUDIO_ENABLE_EQUALIZER: boolean,
  GAME_MANIA_DISABLE_BACKGROUND: boolean,
  AUDIO_SFX_ENABLE: boolean,
  AUDIO_SFX_GLOBAL_VOLUME: number,
  AUDIO_SFX_STAGE_EFFECTS_GLOBAL_VOLUME: number,
  AUDIO_SFX_HOVER_VOLUME: number,
  AUDIO_SFX_CHAT_ENABLE: boolean,
  AUDIO_ENABLE_STAGE_SFX: boolean,
  AUDIO_SYNTH_ENGINE: AudioSynthesizerEngine,
  AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE: number,
  AUDIO_MULTIMODE_MAX_CHANNELS: number,
  APP_AUTO_LOGIN_ENABLE: boolean,
  AUDIO_BG_MUSIC_ENABLE: boolean,
  AUDIO_BG_MUSIC_GLOBAL_VOLUME: number,
  ONLINE_WARN_ABOUT_EXTERNAL_LINKS: boolean,
  ENABLE_PLUGINS: boolean,
  KEYBOARD_SHIFT_KEY_AUTO_NOTE_OFF: boolean,
  AUDIO_USE_WORKLET: boolean,
  AUDIO_USE_MAIN_THREAD: boolean,
  INPUT_CTRL_KEY_LOWERS_OCTAVE: boolean,
  INPUT_MIDI_TO_QWERTY_MOD: boolean,
  INPUT_MIDI_TO_QWERTY_MOD_USE_CAPSLOCK: boolean,
  GRAPHICS_ENABLE_LIGHTS: boolean;
  GRAPHICS_ENABLE_BLOOM: boolean;
  GRAPHICS_ENABLE_TONE_MAPPING: boolean;
  GRAPHICS_ENABLE_HDR: boolean;
  GRAPHICS_ENABLE_ANIMATIONS: boolean;
  GRAPHICS_MSAA_SAMPLES: GraphicsMsaaSamples;
  GRAPHICS_SHADOW_FILTER: GraphicShadowFilteringMethod;
  GRAPHICS_PRESET: GraphicsPresets;
  GRAPHICS_USE_LOW_POLY_MODELS: boolean;
  GRAPHICS_ENABLE_GUITARS: boolean;
  AUDIO_USE_VELOCITY_CURVE: boolean;

  // Migrations
  AUDIO_REVERB_DEFAULT_MIGRATED_VERSION?: string;
  GRAPHICS_TARGET_FPS: string;
};

/** @ignore */
export const DefaultAppSettings: AppSettings = {
  KEYBOARD_SHIFT_KEY_AUTO_NOTE_OFF: true,
  INPUT_CTRL_KEY_LOWERS_OCTAVE: false,
  INPUT_MIDI_TO_QWERTY_MOD_USE_CAPSLOCK: false,
  INPUT_MIDI_TO_QWERTY_MOD: true,
  SELECTED_SOUNDFONT: { name: DEFAULT_SOUNDFONT, custom: false } as SoundfontSetting,
  AUDIO_MAX_NOTE_ON_TIME: AUDIO.DEFAULT_NOTE_ON_TIME,
  AUDIO_MAX_VELOCITY: AUDIO.MAX_VELOCITY,
  AUDIO_MIN_VELOCITY: AUDIO.MIN_VELOCITY,
  AUDIO_MIN_VOLUME_RELEASE: 0.25,
  AUDIO_BUFFER_SIZE: 1024,
  DEFAULT_LANGUAGE: DEFAULT_LANGUAGE[0],
  DISPLAY_PIANO: true,
  DISPLAY_FPS: true,
  DISCORD_SYNC: true,
  DISPLAY_PING: false,
  DISPLAY_3D_STATS: false,
  DISPLAY_CURSORS: false,
  DISPLAY_INST_DOCK: true,
  DISPLAY_WHOISTYPING: true,
  DISPLAY_CHAT: true,
  DISPLAY_SCENE_WIDGET_BUTTONS: true,
  KEEP_CHAT_IN_FOCUS: false,
  INST_DOCK_TRANSPOSE: AUDIO.DEFAULT_TRANSPOSE,
  INST_DOCK_OCTAVE: AUDIO.DEFAULT_OCTAVE,
  INST_DOCK_OPACITY: 0.1,
  VOLUME_SAVED: AUDIO.DEFAULT_CHANNEL_VOLUME,
  AUDIO_MAX_POLYPHONY: AUDIO.DEFAULT_POLYPHONY,
  AUDIO_VEL_MUTING: 5,
  AUDIO_SUSTAIN_CUTOFF: 0.5,
  AUDIO_SUSTAINED_NOTE_FADEOUT_DURATION: 98,
  AUDIO_VEL_BOOST: 0,
  AUDIO_ENABLE_VEL_BOOST: false,
  AUDIO_ENABLE_VEL: true,
  AUDIO_MIDI_OUTPUT_ONLY: false,
  AUDIO_MOUSE_POS_SETS_VELOCITY: false,
  AUDIO_ENABLE_REVERB: false,
  AUDIO_ENABLE_DRUM_CHANNEL: true,
  CHAT_AUTO_SCROLL: true,
  SHOW_EMBEDDED_LINKS: true,
  MIDI_LISTEN_TO_PROGRAM_CHANGES: true,
  MIDI_AUTO_FILL_EMPTY_CHANNELS: true,
  MIDI_USE_DEFAULT_BANK_WHEN_MISSING: true,
  MIDI_ENABLE_STEREO_PANNING: false,
  KEYBINDS: [],
  CUSTOM_KEYBOARD_LAYOUT_KEYBINDS: [],
  MUTED_NOTES_SELF: false,
  SEND_WHOISTYPING: true,
  KEYBOARD_LAYOUT: "MPP",
  AUTOHIDE_BOTTOMBAR: false,
  //TODO: Convert old string slot mode value to proto enum
  SLOT_MODE: ActiveChannelsMode.ALL,
  CHAT_MESSAGES_MINIMIZED: false,
  CHAT_MESSAGES_MAXIMIZED: false,
  GRAPHICS_ENABLE_MOTION_BLUR: false,
  GRAPHICS_ENABLE_ALL_PARTICLES: true,
  GRAPHICS_ENABLE_POST_PROCESSING: true,
  GRAPHICS_ENABLE_FOG: true,
  GRAPHICS_ENABLE_GLOW: true,
  GRAPHICS_ENABLE_AVATARS: true,
  GRAPHICS_ENABLE_ENGINE: true,
  GRAPHICS_ENABLE_WEBGPU: true,
  GRAPHICS_ENABLE_ANTIALIAS: true,
  GRAPHICS_DISPLAY_RENDER_STATS: false,
  CHAT_DISABLE_MARKDOWN: false,
  CHAT_ENABLE_IMAGE_URL_PREVIEW: true,
  ENABLE_DESKTOP_NOTIFICATIONS: true,
  ALLOW_USERS_TO_NOTIFY_ME: true,
  AUDIO_SAMPLE_RATE: 0,
  AUDIO_IGNORE_SOUNDFONT_VOLUME_ENV_DELAY: true,
  AUDIO_USE_SEPARATE_DRUM_KIT: true,
  AUDIO_REVERB_LEVEL: AUDIO.REVERB.MINIMAL.AUDIO_REVERB_LEVEL,
  AUDIO_REVERB_DAMP: AUDIO.REVERB.MINIMAL.AUDIO_REVERB_ROOMSIZE,
  AUDIO_REVERB_WIDTH: AUDIO.REVERB.MINIMAL.AUDIO_REVERB_DAMP,
  AUDIO_REVERB_ROOMSIZE: AUDIO.REVERB.MINIMAL.AUDIO_REVERB_WIDTH,
  AUDIO_OUTPUT_OWN_NOTES_TO_MIDI: true,
  UI_THEME: AppThemes.DEFAULT,
  UI_ENABLE_USER_NOTE_ACTIVITIES: true,
  UI_ENABLE_SYNTH_NOTE_ACTIVITIES: true,
  DESKTOP_SAVE_SOUNDFONTS: true,
  AUDIO_CHANNEL_INTERPOLATION_METHOD: "SeventhOrder",
  WEBGL_POWER_PREFERENCE: "default",
  ENABLE_DEBUG_MODE: false,
  AUTO_CHECK_FOR_UPDATES: true,
  GRAPHICS_ENABLE_PIANO: true,
  GRAPHICS_ONLY_SHOW_PIANO_KEYS: false,
  GRAPHICS_ENABLE_STAGE: true,
  GRAPHICS_ENABLE_SHADOWS: true,
  GRAPHICS_ENABLE_SOFT_SHADOWS: true,
  GRAPHICS_ENABLE_DRUMS: true,
  GRAPHICS_ENABLE_SPECIAL_EFFECTS: true,
  GRAPHICS_ENABLE_ORCHESTRA_MODELS: true,
  GRAPHICS_RENDER_EVEN_IN_BACKGROUND: true,
  GRAPHICS_ENABLE_AMBIENT_OCCLUSION: false,
  GRAPHICS_ENABLE_DEPTH_OF_FIELD: false,
  GRAPHICS_USE_OFFSCREEN_CANVAS: true,
  AUDIO_EQUALIZER_PRESET: Equalizer.DEFAULT_PRESET,
  AUDIO_EQUALIZER_BANDS: Equalizer.DEFAULT_BANDS,
  AUDIO_ENABLE_EQUALIZER: true,
  GRAPHICS_ENABLE_PHYSICS: false,
  GAME_MANIA_DISABLE_BACKGROUND: false,
  AUDIO_SFX_ENABLE: true,
  APP_AUTO_LOGIN_ENABLE: true,
  AUDIO_SFX_GLOBAL_VOLUME: 0.1,
  AUDIO_SFX_HOVER_VOLUME: 0.1,
  AUDIO_SFX_STAGE_EFFECTS_GLOBAL_VOLUME: 1.0,
  AUDIO_SFX_CHAT_ENABLE: true,
  AUDIO_ENABLE_STAGE_SFX: true,
  AUDIO_BG_MUSIC_ENABLE: true,
  AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS: true,
  AUDIO_CACHE_SOUNDFONTS_WEB: true,
  AUDIO_BG_MUSIC_GLOBAL_VOLUME: 0.1,
  AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE: AUDIO.MAX_VELOCITY_USER_PERCENTAGE,
  GRAPHICS_ENABLE_AUTO_ANIMATE_TO_INSTRUMENTS: false,
  ONLINE_WARN_ABOUT_EXTERNAL_LINKS: true,
  ENABLE_PLUGINS: true,
  AUDIO_REVERB_DEFAULT_MIGRATED_VERSION: undefined,
  AUDIO_MULTIMODE_MAX_CHANNELS: 3,
  AUDIO_SYNTH_ENGINE: AudioSynthesizerEngine.OXISYNTH,
  AUDIO_USE_WORKLET: true,
  AUDIO_USE_MAIN_THREAD: false,
  GRAPHICS_TARGET_FPS: "unlimited",
  GRAPHICS_ENABLE_LIGHTS: true,
  GRAPHICS_ENABLE_BLOOM: false,
  GRAPHICS_ENABLE_TONE_MAPPING: true,
  GRAPHICS_ENABLE_HDR: false,
  GRAPHICS_ENABLE_ANIMATIONS: true,
  GRAPHICS_MSAA_SAMPLES: GraphicsMsaaSamples.Msaa_Sample4,
  GRAPHICS_SHADOW_FILTER: GraphicShadowFilteringMethod.ShadowFiltering_Gaussian,
  GRAPHICS_PRESET: GraphicsPresets.Preset_Medium,
  GRAPHICS_USE_LOW_POLY_MODELS: false,
  GRAPHICS_ENABLE_GUITARS: true,
  AUDIO_USE_VELOCITY_CURVE: false,
};