import { match } from "ts-pattern";
import { MidiDto } from "./audio.types";
import { ServerRoomChatCommandDetail } from "./chat.types";
import { RoomDto } from "./room.types";
import { UserNotificationSetting, UserNotificationSettings } from "./user.types";
import { BasicRoomDto, RoomFullDetails, RoomSettings } from "~/proto/room-renditions";
import { CreateRoomParam, ServerMessage } from "~/proto/server-message";
import { ChatMessageDto, JoinedRoomData, JoinRoomFailType, joinRoomFailTypeToJSON, WelcomeDto } from "~/proto/client-message";
import { ClientSideUserDto, FriendDto, KickedUserData, PendingFriendRequest, UserBadges, UserClientDto, UserUpdateCommand } from "~/proto/user-renditions";

export type ConnectionState = "Online" | "Pending" | "Offline" | "Retrying";

export type WebSocketDataEvent<T> = {
  type: "ClientMsg" | "ServiceMsg" | "SynthEmitMidiMsg" | "MidiSequencerEffect"
  | "worker.error-message"
  | "worker.notification-message";
  message?: string;
  data?: T;
};

export type ServiceMsg =
  | { event: "ConnectionState", state: ConnectionState, message?: string; }
  | { event: "WorkerReady"; }
  | { event: "WorkerFailed"; }
  | { event: "SocketError"; }
  | { event: "CoreAppEvents"; payload: Array<number>; }
  | { event: "CoreAppEffects"; payload: Uint8Array; };

export type RoomUpdateEvent =
  | ["UserJoined", ClientSideUserDto]
  | ["UserUpdated", ClientSideUserDto]
  | ["UserUpdateCommand", UserUpdateCommand]
  | ["UserLeft", string]
  | ["Updated", RoomDto]
  | ["OwnerUpdate", string]
  | ["UsersTyping", string[]];

export type ServerEvent =
  | ["NewRoom", BasicRoomDto]
  | ["UpdateRoom", BasicRoomDto]
  | ["DeleteRoom", string]
  | ["RoomEvent", RoomUpdateEvent]
  | ["JoinedRoom", JoinedRoomData]
  | "ForceKicked"
  | ["ForceKickedWithReason", string]
  | ["PlayersOnline", number]
  | ["ForceDisconnect", string];

export type ServerEventInfo = {
  // id: number;
  // ts: Date;
  evt: ServerEvent;
};

export type MidiMessageInputBuffer =
  { data: MidiDto; delay?: number; };

export type MidiMessageInputDto = {
  time: string;
  data: MidiMessageInputBuffer[];
};

export type APIResponseMsg =
  | "CanCreateRoom"
  | "CanJoinRoom"
  | "Unauthorized"
  | "ServerError"
  | "MaintenanceMode"
  | "RecreateLobby";

export type ClientErrMsg =
  ["CannotProcess", string];

export type ClientValidationError = {
  FieldName: string;
  Reason: string;
};

export type ClientValidationErrorList = ClientValidationError[];

export type ClientValidationErrorMsg =
  | ["CreateRoomValidationError", ClientValidationErrorList]
  | ["UpdateRoomValidationError", ClientValidationErrorList]
  | ["RegistrationValidationError", ClientValidationErrorList]
  | ["LoginValidationError", ClientValidationErrorList]
  | ["ForgotPasswordValidationError", ClientValidationErrorList]
  | ["ResetPasswordValidationError", ClientValidationErrorList]
  | ["ResendEmailVerificationValidationError", ClientValidationErrorList];

export function joinRoomFailTypeToMessage(type: JoinRoomFailType) {
  return match(type)
    .with(JoinRoomFailType.QueuedToJoinRoom, () => "You are already queued to join the room. Please wait.")
    .with(JoinRoomFailType.AlreadyInRoom, () => "You are already in this room.")
    .with(JoinRoomFailType.RoomFull, () => "Room is full. Please try again later.")
    .with(JoinRoomFailType.NoGuests, () => "Sorry, this room does not allow guests.")
    .with(JoinRoomFailType.ProMembersOnly, () => "Sorry, you must be a PRO subscriber to join this room.")
    .with(JoinRoomFailType.NotAllowed, () => "Sorry, you are not allowed to join this room.")
    .with(JoinRoomFailType.RoomNotFound, () => "Sorry, the room was not found.")
    .with(JoinRoomFailType.IncorrectPassword, () => "Please enter the correct password.")
    .with(JoinRoomFailType.MaintenanceMode, () => "Sorry, access is currently disabled while the server is going through maintenance.")
    // .with(["ServerError", P.string], ([, error]) => `Server Error: ${error}`)
    .otherwise(x => `Unknown error: ${joinRoomFailTypeToJSON(x)}`);
}

export type CommandResponse =
  ["Error", ClientErrMsg]
  | ["ValidationError", ClientValidationErrorMsg]
  | ["ClientUpdated", UserClientDto]
  | ["ClientUpdatedByCommand", UserUpdateCommand]
  | ["QueuedToJoinRoom", string]
  | ["JoinedRoom", RoomDto]
  | [type: "JoinRoomFail", roomID: string, failType: JoinRoomFailType]
  | "LeftRoom"
  | "LeftRoomFail"
  | ["RoomsList", BasicRoomDto[]]
  | ["UsersInRoomList", ClientSideUserDto[]]
  | ["KickedUsersInRoomList", KickedUserData[]]
  | [type: "RoomSettings", settings: RoomSettings, param: CreateRoomParam]
  | "Pong"
  | "RegistrationSuccess"
  | ["Toast", { message: string; }]
  | [type: "EnterRoomPassword", roomID: string, roomname: string]
  | "ExceptionOccurred"
  | "UserUnregistered";

export type RoomAnnouncement = { id: string, ts: string, user: string, message: string; };

export type ClientMsg =
  ["Welcome", WelcomeDto]
  | "None"
  | "ClearChat"
  | "RoomStoreServiceUp"
  | ["RoomChatMessage", ChatMessageDto]
  | ["EditMessageByID", string, string]
  | ["ServerEventInfo", ServerEventInfo]
  | ["CmdResponse", CommandResponse]
  | ["ApiResponse", APIResponseMsg]
  | ["MidiMessage", any]
  | ["WorldCommand", any]
  | ["GetUserNotificationSettings", UserNotificationSettings]
  | ["GetFriendsList", FriendDto[]]
  | ["GetPendingFriendRequestsList", PendingFriendRequest[]]
  | ["AddPendingFriendRequest", PendingFriendRequest]
  | ["RemovePendingFriendRequest", string]
  | ["FriendUpdate", FriendDto]
  | ["RoomSettingsUpdated", RoomSettings]
  | ["GetRoomFullDetails", RoomFullDetails]
  | ["LoadServerCommands", ServerRoomChatCommandDetail[]]
  | ["LoadRoomOwnerCommands", ServerRoomChatCommandDetail[]]
  | "UnloadRoomOwnerCommands"
  | ["RejoinRecoveredRoom", string]
  | "SessionExpired"
  | ["ClearChatByMessageID", string]
  | ["ClearChatBySocketID", string]
  | ["ClearChatByUsername", string]
  | ["RoomOwnerUpdated", string]
  | ["ClearChatByAmount", number]
  | ["Announcements", RoomAnnouncement[]]
  | ["LoadRoomChatHistory", ChatMessageDto[]]
  | ["LoadRoomWelcomeMessage", string]
  | ["MaintenanceModeActive", boolean]
  | ["OnJoinedRoom", JoinedRoomData];

export type UserUpdate =
  | ["Color", string]
  | ["MutedSelf", boolean]
  | ["DisplayCursor", boolean]
  | ["NotificationSetting", UserNotificationSetting]
  | ["ClientMetaDetails", string?]
  | ["Status", string]
  | ["StatusText", string?]
  | ["Nickname", string?]
  | ["ProfileDescription", string?];

export type ServerCommand =
  ["RoomLoaded"]
  | ["Ping"]
  | ["GetAllRooms"]
  | ["GetAllUsersInRoom"]
  | ["GetKickedUsersList"]
  | ["GetRoomSettings"]
  | ["LeaveRoom"]
  | ["CreateRoom", any]
  | ["UpdateRoom", any]
  | ["Login", any]
  | ["Register", any]
  | ["ResendEmailVerification", any]
  | ["ForgotPassword", any]
  | ["ResetPassword", any]
  | ["IsTyping", boolean]
  | ["UploadClientSettings", any]
  | ["UserUpdateCommand", UserUpdate]
  | ["Join", string]
  | ["CreateOrJoinRoom", string]
  | ["UploadOrchestraModelCustomizationData", string]
  | ["UploadCharacterData", string]
  | ["DeleteChatMessageById", string]
  | ["DeleteChatMessageByUsertag", string]
  | [type: "SendFriendRequest", usertag: string]
  | [type: "SendUnfriendRequest", usertag: string]
  | [type: "AcceptFriendRequest", usertag: string]
  | [type: "DenyFriendRequest", usertag: string]
  | [type: "DeleteSentFriendRequest", usertag: string]
  | [type: "JoinRoomWithPassword", roomID: string, password: string];

export type Permissions =
  | "PROFILE_IMAGE_UPLOAD"
  | "SET_NICKNAME"
  | "SET_STATUS_TEXT"
  | "SET_PROFILE_DESCRIPTION"
  | "SHEETMUSIC_UPLOAD"
  | "SELF_HOST_ROOMS";

export type PermissionUpdate =
  | ["Added", Permissions]
  | ["Removed", Permissions];

export type ServerModCommand =
  | [type: "ClearChat"]
  | [type: "BanUser", usertag: string]
  | [type: "UnbanAccount", user: string]
  | [type: "UnbanIp", user: string]
  | [type: "AddBadge", badge: UserBadges]
  | [type: "RemoveBadge", badge: UserBadges]
  | [type: "PermissionUpdatedCommand", permission: PermissionUpdate];

export type ChatMessageModificationType =
  | [type: "Edit", id?: string]
  | [type: "Reply", id?: string];

type ChatMessageInputDataOptions = {
  syncToDiscord: boolean;
  isFromPlugin?: boolean;
  modificationType?: ChatMessageModificationType;
};

export type ChatMessageInputData = {
  text: string;
  options: ChatMessageInputDataOptions;
};

export const ChatMessageInputDataOptionsDefault: ChatMessageInputDataOptions = {
  syncToDiscord: false,
  isFromPlugin: undefined,
  modificationType: undefined,
};

export type ServerMsg =
  ["Hello"]
  | ["Disconnect"]
  | ["RoomChatMessage", ChatMessageInputData]
  | ["RoomChatServerCommand", { command: string; }]
  | ["MidiMessage", MidiMessageInputDto]
  | [type: "ServerModCommand", usertag: string, command: ServerModCommand]
  | ["ServerCommand", ServerCommand]
  | ["CreateRoomCommand", CreateRoomParam]
  | ["UpdateRoomCommand", CreateRoomParam]
  | [type: "JoinRoomByName", roomName: string, createIfNotExists: boolean]
  | [type: "RawProto", proto: ServerMessage];

export type WebWorkerEvent =
  | ["Connect", boolean]
  | ["Disconnect"]
  | ["OfflineMode", boolean]
  | ["ServiceReady", boolean]
  | ["EmitMessage", ServerMsg]
  | ["SendAppCoreActionToWorker", Uint8Array]
  | ["SetServerTimeOffset", number]
  | ["SetMutedUsers", string[]]
  | ["SetMutedEveryoneElse", boolean]
  | ["SetPluginsEnabled", boolean]
  | ["SetGlobalVelocityPercentage", number]
  | ["LoadSoundfont", ArrayBufferLike]
  | ["SetUserVelocityPercentage", [string, number]];

export enum MidiDtoTypeEnum {
  Invalid = 0,
  NoteOn = 1,
  NoteOff = 2,
  Sustain = 3,
  AllSoundOff = 4,
}