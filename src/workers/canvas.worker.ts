/// <reference lib="webworker" />

import * as webWorkerProxy from 'web-worker-proxy';
import { logInfo } from '~/util/logger';

// @ts-ignore
import * as webgpuRenderer from '@core/pkg/webgpu/pianorhythm_bevy_renderer';

// @ts-ignore
import * as webgl2Renderer from '@core/pkg/webgl2/pianorhythm_bevy_renderer';

let appHandle: bigint = BigInt(0);
let initFinished = 0;
let isPaused = false;
let targetRenderer: any | undefined = undefined;
let animationHandle: number = 0;

function onRender(renderer: any) {
  if (!renderer || isPaused) return;

  function getPreparationState() {
    initFinished = renderer.is_preparation_completed(appHandle);
  }

  function enterFrame() {
    if (appHandle <= 0 || isPaused) return;

    if (initFinished) {
      renderer.enter_frame(appHandle);
    } else {
      getPreparationState();
    }

    animationHandle = requestAnimationFrame(enterFrame);
  }

  animationHandle = requestAnimationFrame(enterFrame);
}

const onLoad = async (renderer: any, canvas: OffscreenCanvas, devicePixelRatio: number, sharedBuffer: SharedArrayBuffer) => {
  targetRenderer = renderer;
  appHandle = renderer.create_app(false, sharedBuffer);
  renderer.create_window_by_offscreen_canvas(appHandle, canvas, devicePixelRatio);
  onRender(renderer);
  webWorkerProxy.proxy(renderer);
};

self.onmessage = async (evt: MessageEvent) => {
  if (evt.data.event == "pause") {
    isPaused = true;
    cancelAnimationFrame(animationHandle);
  }

  if (evt.data.event == "resume") {
    isPaused = false;
    onRender(targetRenderer);
  }

  if (evt.data.event == "load-module") {
    let canvas = evt.data.payload.offscreenCanvas;
    let useWebGpu = evt.data.payload.useWebGPU;
    let devicePixelRatio = evt.data.payload.devicePixelRatio;
    let synthEventsSharedBuffer = evt.data.payload.synthEventsSharedBuffer;

    if (useWebGpu) {
      logInfo("Using WebGPU renderer");
      await webgpuRenderer.default();
    } else {
      logInfo("Using WebGL2 renderer");
      await webgl2Renderer.default();
    }

    await onLoad(useWebGpu ? webgpuRenderer : webgl2Renderer, canvas, devicePixelRatio, synthEventsSharedBuffer);

    self.addEventListener("renderer_effects", (event) => {
      let bytes = ((event as any).detail as Uint8Array);
      self.postMessage({ event: "renderer_effects", payload: bytes });
    });

    self.addEventListener("renderer_events", (event) => {
      let data = ((event as any)?.detail as number);
      self.postMessage({ event: "renderer_events", payload: data });
    });

    self.addEventListener("renderer_actions", (event) => {
      let bytes = ((event as any).detail as Uint8Array);
      self.postMessage({ event: "renderer_actions", payload: bytes });
    });

    let isFinishedInterval = 0;
    isFinishedInterval = self.setInterval(() => {
      if (initFinished > 0) {
        self.postMessage({
          event: "wasm-module-loaded",
          payload: { handle: appHandle }
        });
        clearInterval(isFinishedInterval);
      }
    }, 50);
  }
};
