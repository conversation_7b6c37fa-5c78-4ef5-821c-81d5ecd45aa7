$delay: 0.3s;
@keyframes solid-contextmenu__scaleIn {
  from {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }

  to {
    opacity: 1;
  }
}

@keyframes solid-contextmenu__scaleOut {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
    transform: scale3d(0.3, 0.3, 0.3);
  }
}

.solid-contextmenu-scale-enter-active {
  transform-origin: top left;
  animation: solid-contextmenu__scaleIn $delay;
}

.solid-contextmenu-scale-exit-active {
  transform-origin: top left;
  animation: solid-contextmenu__scaleOut $delay;
}
