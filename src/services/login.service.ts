import { useAction, useNavigate } from "@solidjs/router";
import { createSignal, useTransition } from "solid-js";
import { useService } from "solid-services";
import { clearServerCookies, logout as serverLogout } from "~/lib";
import { COMMON, IDS } from "~/util/const.common";
import { logError } from "~/util/logger";
import SwalPR from "~/util/sweetalert";
import AppService from "./app.service";
import NotificationService from "./notification.service";
import { LoginActionResponse } from "~/types/api.types";

export enum CurrentForm {
  Main,
  Login,
  Register,
  ForgotPassword,
  ResendVerificationEmail,
  LoggedIn
}

/**
 * LoginService is a service responsible for handling login-related functionality.
 * It provides methods for logging in, logging out, and managing user authentication state.
 *
 * @returns An object containing various methods and state variables related to login functionality.
 */
export default function LoginService() {
  const appService = useService(AppService);
  const [pending, start] = useTransition();
  const [currentLoggedInUsername, setCurrentLoggedInUsername] = createSignal<{ username: string, usertag: string; }>();
  const [currentForm, setCurrentForm] = createSignal(CurrentForm.Main);
  const [checkingAutoLogin, setCheckingAutoLogin] = createSignal(false);
  const [loggedIn, setLoggedIn] = createSignal(false);
  const updateForm = (form: CurrentForm) => start(() => setCurrentForm(form));
  const logoutAction = useAction(serverLogout);
  const clearCookie = useAction(clearServerCookies);
  const navigate = useNavigate();

  const onEnterAppLoadingAfterLogin = async (targetRoom?: string) => {
    await updateForm(CurrentForm.Main);

    let path = "/app-loading";
    if (targetRoom) path += `?roomName=${targetRoom}`;
    navigate(path, { replace: true });
  };

  const onBeforeLogin = () => {
    appService().setActivatePageLoader(true);

    NotificationService.show({
      id: IDS.USER_LOGGING_IN,
      description: "Logging in...",
      type: "info",
    });
  };

  const onLogin = async (action: Promise<LoginActionResponse>) => {
    try {
      let result = await action;
      if (!result) throw new Error("No result returned from login.");

      if (result.error) {
        NotificationService.show({
          title: "Login Error",
          description: result.error,
          type: "danger",
        });

        return undefined;
      }

      if (COMMON.IS_DEV_MODE) console.log("Login result:", result);
      if (!result.username || !result.usertag) throw new Error("Invalid login result.");

      setCurrentLoggedInUsername({ username: result.username, usertag: result.usertag });
      setLoggedIn(true);

      return {
        username: result.username,
        usertag: result.usertag,
        roles: result.roles ?? []
      };
    } catch (error) {
      console.error(error);
      NotificationService.show({
        title: "Login Error",
        description: "An error occurred while trying to login. Please check the console logs for more info.",
        type: "danger",
      });

      return undefined;
    }
  };

  const onLoggedIn = () => {
    appService().setActivatePageLoader(false);
    NotificationService.show({
      id: IDS.USER_LOGGED_IN,
      description: "Logged in successfully!",
      type: "success",
    });
  };

  const onLoginFail = () => {
    appService().setActivatePageLoader(false);
  };

  const tryLogout = () => {
    SwalPR().fire({
      title: "Logout",
      text: "Are you sure you want to logout?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yep!",
    }).then(async (result) => {
      if (result.isConfirmed) await logout();
    });
  };

  const logout = async (redirectToLogin = true) => {
    try {
      if (redirectToLogin) appService().setActivatePageLoader(true);
      setLoggedIn(false);
      setCurrentLoggedInUsername(undefined);
      await logoutAction();

      // Note: We need to clear the cookies manually here because the server-side logout
      // for setting multiple cookies is not working as expected.
      // It only clears the first cookie and ignores the rest.
      await clearCookie("rst-id");
      await clearCookie("rst-rt");
      await clearCookie("rst-at");

      if (redirectToLogin) navigate("/login", { replace: true });

      NotificationService.show({
        description: "Logged out successfully!",
        type: "success",
      });
    } catch (e) {
      logError(`[logout] ${e}`);
      NotificationService.show({
        title: "Failed to logout!",
        description: "Please check the console logs for more info.",
        type: "danger",
      });

      appService().setActivatePageLoader(false);
    }
  };

  const onUserAutoLoginNotification = (username: string) => {
    if (!username) return;

    NotificationService.show({
      id: IDS.USER_LOGGING_IN,
      type: "success",
      description: `Welcome back, ${username}!`,
      duration: 5000,
    });
  };

  return ({
    onUserAutoLoginNotification,
    onBeforeLogin,
    onLoggedIn,
    onLogin,
    onEnterAppLoadingAfterLogin,
    pending,
    tryLogout,
    logout,
    currentLoggedInUsername,
    currentForm,
    onLoginFail,
    checkingAutoLogin,
    updateForm,
    setCurrentLoggedInUsername,
    setCheckingAutoLogin,
    loggedIn, setLoggedIn
  });
}