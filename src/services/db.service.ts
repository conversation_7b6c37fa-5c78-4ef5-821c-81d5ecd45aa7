import { values, entries, keys, set, clear as _clear, del, get as _get, createStore, UseStore } from 'idb-keyval';

function DatabaseServiceController() {
  async function initialize() {

  }

  function dispose() {

  }

  const onDisconnect = () => {

  };

  function getAllValues<T>(customStore?: UseStore): Promise<T[]> {
    try {
      return values<T>(customStore);
    } catch {
      return Promise.resolve([]);
    }
  }

  function getAllEntries<T>(customStore?: UseStore): Promise<[IDBValidKey, T][]> {
    try {
      return entries<IDBValidKey, T>(customStore);
    } catch {
      return Promise.resolve([]);
    }
  }

  function getAllKeys(customStore?: UseStore): Promise<string[]> {
    try {
      return keys(customStore).then((values) => values.map(x => x.toLocaleString().toLowerCase()));
    } catch {
      return Promise.resolve([]);
    }
  }

  function deleteKey(key: string, customStore?: UseStore) {
    return del(key, customStore);
  }

  function put<T>(key: string, value: T, customStore?: UseStore) {
    return set(key, value, customStore);
  }

  function get<T>(key: string, customStore?: UseStore) {
    return _get<T>(key, customStore);
  }

  function clear(customStore?: UseStore) {
    return _clear(customStore);
  }

  function hasKey(key: string, customStore?: UseStore) {
    if (key == null) return Promise.resolve(false);
    return keys(customStore).then((values) => values.map(x => x.toLocaleString().toLowerCase()).includes(key.toLowerCase()));
  }

  const countDB = async (db: any, table: any) => {
    return new Promise((resolve, reject) => {
      const tx = db.transaction([table], 'readonly');
      const store = tx.objectStore(table);
      const cursorReq = store.openCursor();
      let count = 0;
      let size = 0;

      cursorReq.onsuccess = function (e: any) {
        const cursor = cursorReq.result;
        if (cursor) {
          count++;
          size = size + cursor.value.blob.size;
          cursor.continue();
        }
      };

      cursorReq.onerror = function (e: any) {
        reject(e);
      };

      tx.oncomplete = function (e: any) {
        resolve({
          count: count,
          size: size
        });
      };

      tx.onabort = function (e: any) {
        reject(e);
      };

      tx.onerror = function (e: any) {
        reject(e);
      };
    });
  };

  return {
    initialize,
    hasKey,
    dispose,
    clear,
    deleteKey,
    put,
    get,
    getAllKeys,
    getAllValues,
    getAllEntries,
    createStore,
    onDisconnect
  };
}

export default DatabaseServiceController;