/**
 * ChatService - A service for managing chat functionality in the PianoRhythm application.
 * This service handles chat messages, commands, user interactions, and chat state management.
 * It integrates with various other services and utilities to provide a comprehensive chat experience.
 */

import { createImmerSignal } from "solid-immer";
import { createSignal } from "solid-js";
import { DefaultAppSettings } from "~/types/settings.types";
import {
  ChatMessageRecord,
  Command,
  CommandArgument,
  CommandModule,
  CreateChatMessageDtoDefault,
  DefaultPianoRhythmChatCommandModule,
  ModChatCommandsModule,
  RoomOwnerChatCommandsModule,
  ServerRoomChatCommandDetail
} from "~/types/chat.types";
import { useService } from "solid-services";
import AppService from "./app.service";
import I18nService from "./i18n.service";
import isEqual from "lodash-es/isEqual";
import sortBy from "lodash-es/sortBy";
import { Subject } from "rxjs";
import isEmpty from "lodash-es/isEmpty";
import { COMMANDS, USER_INPUT } from "~/util/const.common";
import { EventBus } from "@solid-primitives/event-bus";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import cloneDeep from "lodash-es/cloneDeep";
import { ChatMessageDto } from "~/proto/client-message";
import { sanitizeText } from "~/util/sanitizeText";
import { isDefined } from "~/util/helpers";
import { getDesktopAppURL } from "~/server/general.api";
import PlatformService from "./platform.service";
import SwalPR from "~/util/sweetalert";
import uniqBy from "lodash-es/uniqBy";
import SoundEffectsService from "./sound-effects.service";
import EmojifyService from "./emojify.service";
import WebsocketService from "./websocket.service";

const DEFAULT_CHAT_OPACITY = 0.5;
const ContainerWidth = 400;
const DEFAULT_CHAT_LIST_WIDTH = 408;

type DeletedChatMessageRecord = {
  id: string;
  nextMessageID?: string;
  index: number;
};

/**
 * Main function for the ChatService.
 * Provides methods and state management for chat functionality.
 */
export default function ChatService() {
  const appService = useService(AppService);
  const i18nService = useService(I18nService);
  const platformService = useService(PlatformService);
  const sfxService = useService(SoundEffectsService);
  const emojifyService = useService(EmojifyService);
  const websocketService = useService(WebsocketService);

  const [messages, setMessages] = createSignal<ChatMessageRecord[]>([], { equals: isEqual });
  const [usersTyping, setUsersTyping] = createImmerSignal<string[]>([]);
  const [chatMessagesSetToBeDeleted, setChatMessagesSetToBeDeleted] = createImmerSignal<string[]>([]);
  const [chatMessageBeingRepliedTo, setChatMessageBeingRepliedTo] = createSignal<string>();
  const [chatMessageBeingEdited, setChatMessageBeingEdited] = createSignal<string>();
  const [chatMessageToHighlight, setChatMessageToHighlight] = createSignal<string>();
  const [chatBarValue, setChatBarValue] = createSignal<string>();
  const [chatContainerElement, setChatContainerElement] = createSignal<HTMLDivElement>();
  const [activeChatCtxID, setActiveChatCtxID] = createSignal<string | null>(null);
  const [ctxMenuActive, setCtxMenuActive] = createSignal(false);
  const [chatMinimized, setChatMinimized] = createSignal(false);
  const [chatMaximized, setChatMaximized] = createSignal(false);
  const [showChatWindowButtons, setShowChatWindowButtons] = createSignal(true);
  const [showChatBar, setShowChatBar] = createSignal(true);
  const [alwaysAutoScroll, setAlwaysAutoScroll] = createSignal(DefaultAppSettings.CHAT_AUTO_SCROLL);
  const [chatCommandMode, setChatCommandMode] = createSignal(false);
  const [chatCommandsDisplayed, setChatCommandsDisplayed] = createSignal(false);
  const [anyListCommandsDisplayed, setAnyListCommandsDisplayed] = createSignal(false);
  const [chatOpacity, setChatOpacity] = createSignal(DEFAULT_CHAT_OPACITY);
  const [chatBarElement, setChatBarElement] = createSignal<HTMLDivElement>();
  const [chatBarInputElement, setChatBarInputElement] = createSignal<HTMLTextAreaElement>();
  const [activeMessageItemIndexToShowOptionsMenu, setActiveMessageItemIndexToShowOptionsMenu] = createSignal<string>();
  const [recentMentionedUsers, setRecentMentionedUsers] = createSignal<string[]>([]);
  const [selectedMentionedUser, setSelectedMentionedUser] = createSignal<string | null>(null);

  const _chatMessages = new Map<string, ChatMessageRecord>();

  const deletedMessagesEvents = new Subject<DeletedChatMessageRecord>();
  const addedMessagesEvents = new Subject<ChatMessageRecord>();
  const editedMessagesEvents = new Subject<ChatMessageRecord>();

  const [chatCommandModules] = createSignal<CommandModule[]>([
    DefaultPianoRhythmChatCommandModule,
    ModChatCommandsModule,
    RoomOwnerChatCommandsModule
  ]);

  let initialized = false;

  /**
   * Initializes the ChatService with event listeners for app state effects and disconnection events.
   * @param appStateEffects - Event bus for app state effects.
   * @param onDisconnectEvents - Event bus for disconnection events.
   */
  const initialize = (
    appStateEffects: EventBus<AppStateEffects>,
    onDisconnectEvents: EventBus<void>
  ) => {
    if (initialized) return;
    initialized = true;

    appStateEffects.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.SetChatMessages: {
          let data = effect.chatMessageDtoList;
          if (!data) return;
          if (data.messages.length == 0) {
            clearAllMessages();
            addMessage("Chat was cleared...", true, true);
          } else {
            setChatMessages(data.messages);
          }
          break;
        }
        case AppStateEffects_Action.SetRoomChatHistory: {
          let chatMessages = effect.chatMessageDtoList;
          if (!chatMessages) return;
          setChatMessages(chatMessages.messages);
          break;
        }
        case AppStateEffects_Action.AddChatMessage: {
          let dto = cloneDeep(effect.chatMessageDto);
          if (!dto) return;
          if (dto.isSystem) dto.message = i18nService().t_server(dto.message);
          addMessagesByDto(dto);
          break;
        }
        case AppStateEffects_Action.EditChatMessage: {
          let dto = effect.chatMessageDto;
          if (!dto) return;
          editMessageByID(dto.messageID, dto.message);
          break;
        }
        case AppStateEffects_Action.DeleteChatMessage: {
          let messageID = effect.messageId;
          if (!messageID) return;
          removeMessageByID(messageID);
          break;
        }
        case AppStateEffects_Action.UsersTypingSet: {
          let value = effect.socketIdList;
          setUsersTyping(value?.socketIDs ?? []);
          break;
        }
      }
    });

    onDisconnectEvents.listen(() => {
      onDisconnect();
    });
  };

  const chatLocalClientCommands: Command[] = [
    {
      command: "help",
      disabled: false,
      shortDescription: "View all available commands"
    },
    {
      command: "me",
      shortDescription: "View user info"
    },
    {
      command: "discord",
      shortDescription: "Get the link to PianoRhythm's Community Discord Server!"
    },
    {
      command: "youtube",
      shortDescription: "Get the link to PianoRhythm's YouTube Channel!"
    },
    {
      command: "twitch",
      shortDescription: "Get the link to PianoRhythm's Twitch Channel!"
    },
    {
      command: "youtrack",
      shortDescription: "Get the link to PianoRhythm's Issue Tracker!"
    },
    {
      command: "download",
      shortDescription: "Get the latest desktop app version!"
    },
    {
      command: "mute_self",
      shortDescription: "Mute your own notes to prevent others from hearing you"
    },
    {
      command: "unmute_self",
      shortDescription: "Unmute your notes"
    },
    {
      command: "offline_mode",
      shortDescription: "Disconnect from the server and play offline",
      disabled: true
    }
  ].map(x => ({
    ...x,
    moduleID: DefaultPianoRhythmChatCommandModule.id,
    clientSideOnly: true,
    arguments: []
  }));

  const chatLocalModCommands: Command[] = [
    {
      command: "ban_user",
      shortDescription: "(Not Implemented Yet | Use context menu on user in sidebar) Ban a user from the server.",
      arguments: [{ name: "user", type: "user" }] as CommandArgument[]
    },
    {
      command: "edit_badges",
      shortDescription: "Edit badges for a user.",
      arguments: [{ name: "user", type: "user" }] as CommandArgument[]
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "clear_chat",
      shortDescription: "Clear the chat in the room for everyone",
      arguments: []
    }
  ].map(x => ({
    ...x,
    moduleID: ModChatCommandsModule.id,
    clientSideOnly: false,
    membersOnly: true,
    modOnly: true
  }));

  const [chatCommands, setChatCommands] = createSignal<Command[]>(sortBy([
    ...chatLocalClientCommands,
    ...chatLocalModCommands,
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "whisper",
      disabled: true,
      shortDescription: "Send a private message to a user",
      arguments: [
        {
          name: "user",
          type: "user"
        },
        {
          name: "message",
          type: "string"
        }
      ]
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "status_text",
      membersOnly: true,
      shortDescription: "Set your status text",
      arguments: [
        {
          name: "text",
          type: "string",
          optional: true
        }
      ]
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "clear_status_text",
      membersOnly: true,
      shortDescription: "Clear your status text"
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "nickname",
      membersOnly: true,
      shortDescription: "Set your nickname",
      arguments: [
        {
          name: "text",
          type: "string",
          optional: true
        }
      ]
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "clear_nickname",
      membersOnly: true,
      shortDescription: "Clear your nickname"
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "profile_description",
      membersOnly: true,
      shortDescription: "Set your profile description",
      arguments: [
        {
          name: "text",
          type: "string",
          optional: true
        }
      ]
    },
    {
      moduleID: DefaultPianoRhythmChatCommandModule.id,
      command: "clear_profile_description",
      membersOnly: true,
      shortDescription: "Clear your profile description"
    }
  ], 'command') as Command[]);

  function preventDefaultIfChatCommandsDisplayed(event: KeyboardEvent) {
    if (chatCommandsDisplayed() || anyListCommandsDisplayed()) {
      event.preventDefault();
      return true;
    }
  }

  function disposeContextMenu() {
    setCtxMenuActive(false);
  }

  function addMessagesByRecord(record: ChatMessageRecord) {
    if (_chatMessages.has(record.id)) return;

    if (record.isSystem) record.setMessage(i18nService().t_server(record.message));
    record.setMessage(sanitizeText(record.message));
    record.setUsername(sanitizeText(record.username));
    _chatMessages.set(record.id, record);

    if (record.autoDelete) {
      window.setTimeout(() => removeMessageByID(record.id), 10_000);
    }

    updateInternalMessages();
    addedMessagesEvents.next(record);
  }

  function addMessagesByDto(dto: ChatMessageDto) {
    if (!dto) return;
    addMessagesByRecord(new ChatMessageRecord(dto));
  }

  function removeMessageByID(messageID: string) {
    if (!messageID) return;
    let index = getIndexByMessageID(messageID);
    let nextMessage = getMessageByIndex(index + 1);

    _chatMessages.delete(messageID);
    updateInternalMessages();

    addToDeletedMessages(messageID, index, nextMessage);
  }

  function editMessageByID(messageID: string, message: string) {
    let existingMessage = _chatMessages.get(messageID);

    if (existingMessage != null) {
      message = sanitizeText(message);
      existingMessage.updateMessage(message);
      _chatMessages.set(messageID, existingMessage);
      editedMessagesEvents.next(cloneDeep(existingMessage));
      updateInternalMessages();
    }
  }

  function updateInternalMessages() {
    let chatHistory = appService().roomSettings()?.MaxChatHistory || 40;
    setMessages(Array.from(_chatMessages.values()).slice(-chatHistory));
  }

  function clearAllMessages() {
    _chatMessages.clear();
    updateInternalMessages();
  }

  function setChatMessages(messages: ChatMessageDto[]) {
    clearAllMessages();
    messages.forEach(addMessagesByDto);
  }

  function addMessage(message: string, isSystem: boolean = false, autoDelete = false) {
    if (!message) return;

    let dto = CreateChatMessageDtoDefault(message);
    if (isSystem) {
      dto.isSystem = isSystem;
      dto.isBot = true;
      dto.isMod = true;
      dto.autoDelete = autoDelete;
      dto.username = dto.username || "System";
      dto.socketID = "bot.system";
      dto.usertag = dto.usertag || `${dto.username.toLowerCase()}#${dto.socketID.substring(0, 6)}`;
      dto.userColor = "#4cd4f3";
    }

    addMessagesByDto(dto);
  }

  function addToDeletedMessages(messageID: string, index: number, nextMessage?: ChatMessageRecord) {
    deletedMessagesEvents.next({ id: messageID, index, nextMessageID: nextMessage?.id });
  }

  const isSystemMessage = (record: ChatMessageRecord) => record.isSystem || record.roles.isBot;

  const canEditMessage = (record: ChatMessageRecord) => {
    return appService().client().usertag == record.usertag
      && appService().client().isMember
      && !isSystemMessage(record);
  };

  const canDeleteMessage = (record: ChatMessageRecord) => (canEditMessage(record) || appService().client().isMod) && !isSystemMessage(record);

  const canReplyToMessage = (record: ChatMessageRecord) => !canEditMessage(record) && !isSystemMessage(record) && !isEmpty(record.socketID);

  const doesChatMessageBelongToClient = (record: ChatMessageRecord) =>
    appService().client().usertag == record.usertag || appService().client().socketID == record.socketID;

  const canReportMessage = (record: ChatMessageRecord) =>
    appService().canReport() && !doesChatMessageBelongToClient(record) && !isSystemMessage(record);

  function findMessagesBy(predicate: (_: ChatMessageRecord) => boolean) {
    return messages().map(x => x as ChatMessageRecord).filter(predicate);
  }

  function getIndexByMessageID(id: string) {
    if (!id) return -1;
    return messages().findIndex(x => x.id.toLowerCase() == id.toLowerCase());
  }

  function getMessageByIndex(index: number) {
    return messages().find((_, idx) => idx == index);
  }

  function getMessageByID(messageID: string) {
    return messages().find(x => x.id == messageID);
  }

  function messsageHasTaggedUser(message: string, target: string) {
    if (!message || !target) return false;
    let partitionedMessage = message.split(" ").map(x => x.toLowerCase());

    return partitionedMessage.length > 0 &&
      partitionedMessage[0]?.toLowerCase() != "/whisper" &&
      partitionedMessage.some(x =>
        x.indexOf(COMMANDS.PREFIX.User) == 0 && x.includes(target?.toLowerCase())
      );
  }

  function runModChatCommand(command: Command, args?: string[]) {
    if (!command || command.disabled) return;

    const displayMessage = (message: string, autoDelete = false) => {
      addMessage(message, true, autoDelete);
    };

    switch (command.command.toLowerCase()) {
      case "edit_badges": {
        if (command.arguments && command.arguments?.length != 0 && args?.length == 0) {
          displayMessage(`Failed to run command. Missing argument: ${command.arguments?.[0]?.name}`, true);
          return;
        }

        // TODO
        // displayUserBadges(args?.join(" "));
        break;
      }
      case "clear_chat": {
        SwalPR().fire({
          icon: "question",
          title: "Clear Chat",
          html: `Are you sure you want to clear the chat for room ${appService().roomName()}?`,
          showCancelButton: true,
          confirmButtonText: "Yes, I do!",
          cancelButtonText: "Oh, woops. Nevermind!"
        }).then((result) => {
          if (result.isConfirmed) {
            websocketService().emitServerModCommand("", ["ClearChat"]);
          }
        });
        break;
      }
      default: {
        displayMessage(`Unknown mod command: ${command.command}`, true);
        break;
      }
    }
  }

  function runServerModChatCommand(command: Command, args?: string[]) {
    if (!command || command.disabled || !command.modOnly) return;
    if (command.modOnly && !appService().isClientMod()) return;

    command.command = command.command.replace("//", "");
    let commandPrefix = `${COMMANDS.PREFIX.ServerCommand}${command.command}`.toLowerCase();

    switch (command.command.toLowerCase()) {
      case "roominfo": {
        let roomID: string = (args || [])[0] as any || "";
        websocketService().emitRoomChatServerCommand(`${commandPrefix} ${roomID}`);
        break;
      }
      case "roomdelete": {
        let roomID: string = (args || [])[0] as any || "";
        websocketService().emitRoomChatServerCommand(`${commandPrefix} ${roomID}`);
        break;
      }
      default: {
        let output = (args || []).join(" ");
        websocketService().emitRoomChatServerCommand(`${commandPrefix} ${output}`);
        break;
      }
    }
  }

  function runRoomOwnerChatCommand(command: Command, args?: string[]) {
    if (!command || command.disabled || !command.roomOwnerOnly) return;
    if (command.roomOwnerOnly && !appService().isClientRoomOwner()) return;

    let commandPrefix = `$${command.command}`.toLowerCase();
    switch (command.command.toLowerCase()) {
      default: {
        let output = (args || []).join(" ");
        websocketService().emitRoomChatServerCommand(`${commandPrefix} ${output}`);
        break;
      }
    }
  }

  function runClientChatCommand(command: Command, args?: string[]) {
    if (command.modOnly && appService().isClientMod()) return runModChatCommand(command, args);
    if (!command || command.disabled || !command.clientSideOnly) return;
    if (command.membersOnly && !appService().isClientMember()) return;

    switch (command.command.toLowerCase()) {
      case "discord": {
        addMessage(`PianoRhythm's Discord: https://discord.gg/Pm2xXxb`, true);
        break;
      }
      case "twitch": {
        addMessage(`PianoRhythm's Twitch: https://www.twitch.tv/pianorhythm`, true);
        break;
      }
      case "youtube": {
        addMessage(`PianoRhythm's YouTube: https://www.youtube.com/@pianorhythm_io`, true);
        break;
      }
      case "issues": {
        addMessage(`PianoRhythm's Issue Tracker: https://pianorhythm.myjetbrains.com/youtrack/agiles/100-9/current`, true);
        break;
      }
      case "mute_self": {
        websocketService().triggerClientSelfMute(true);
        break;
      }
      case "unmute_self": {
        websocketService().triggerClientSelfMute(false);
        break;
      }
      // case "offline_mode": {
      //   triggerOfflineMode();
      //   break;
      // }
      case "help": {
        let targetCommandStr = (args || [])[0];
        let targetCommand = chatCommands().find(x => {
          return x.command.toLowerCase() == targetCommandStr?.toLowerCase();
        });

        const mapArgs = (args: CommandArgument[]) => args.map(x => `${x.name}:${x.type}`).join(", ");

        if (targetCommand) {
          let values = [
            ["", ""],
            ["-", "-"],
            ["Cmd", targetCommand.command],
            (targetCommand.shortDescription && ["Desc", targetCommand.shortDescription]),
            ((targetCommand.arguments || []).length > 0 && ["Args", mapArgs(targetCommand.arguments || [])])
          ]
            .filter(isDefined)
            // @ts-ignore
            .map(([f, v]) => `| ${f} | ${v} |`)
            .join("\n");

          addMessage(values, true);
        } else {
          let commands = chatCommands().map(x => [`**/${x.command}**`, mapArgs(x.arguments || [])]);
          let values = [
            ["", ""],
            ["-", "-"],
            ...commands
          ].filter(isDefined)
            .map(([f, v]) => `| ${f} | ${v} |`)
            .join("\n");

          addMessage("Available Commands:", true);
          addMessage(values, true);
        }
        break;
      }
      case "download": {
        addMessage("Getting link. Please wait...", true, true);
        let platform = platformService().osPlatform();
        let onFail = () => addMessage(`Sorry! Your platform (${platform}) is currently not supported.`, true);

        getDesktopAppURL(platform)
          .then((output) => {
            if (output) {
              addMessage(`Download Desktop App: ${output?.url}`, true);
            } else {
              onFail();
            }
          }).catch(onFail);
        break;
      }
      case "me":
        let client = appService().client();
        let dto = client.getDto().userDto;

        let values = [
          ["Field", "Value"],
          ["-", "-"],
          ["Username", client.username],
          ["Usertag", client.usertag],
          ["SocketID", client.socketID],
          (dto && dto.nickname && ["Nickname", dto.nickname as string]),
          (client.usercolor && ["Color", `<text border-radius="5px" padding="3px" background="${client.usercolor}">${client.usercolor}</text>`])
        ]
          .filter(isDefined)
          .map(([f, v]) => `| ${f} | ${v} |`)
          .join("\n");

        addMessage(values, true);
        break;
      default: {
        addMessage(`Unknown command: ${command.command}`, true, true);
        break;
      }
    }
  }

  function runRoomChatServerCommand(command: Command, args?: string[]) {
    if (!command || !command.membersOnly) return Promise.reject("Command is not available to guests.");
    if (command.membersOnly && !appService().isClientMember()) return Promise.reject("Command is not available to guests.");
    let output = (args || []).join(" ");

    switch (command.command.toLowerCase()) {
      case "clear_nickname":
      case "nickname": {
        return output ? triggerSetNickname(output) : clearNickname();
      }
      case "clear_status_text":
      case "status_text": {
        return output ? triggerSetStatusText(output) : clearStatusText();
      }
      case "clear_profile_description":
      case "profile_description": {
        return output ? triggerSetProfileDescription(output) : clearProfileDescription();
      }
      default: {
        websocketService().emitRoomChatServerCommand(`/${command.command} ${output}`);
        return Promise.resolve();
      }
    }
  }

  function checkIfCommandCanBeRan(command?: Command) {
    if (!command || command.disabled) return false;
    if (command.membersOnly && !appService().isClientMember()) return false;
    if (command.modOnly && !appService().isClientMod()) return false;
    if (command.roomOwnerOnly && !appService().isClientRoomOwner()) return false;
    if (command.proMemberOnly && !appService().isClientProMember()) return false;

    return true;
  }

  const onEditMessage = (record: ChatMessageRecord) => {
    setChatMessageBeingEdited(record.id);
    setChatBarValue(record.message);
  };

  function loadServerModCommands(commands: ServerRoomChatCommandDetail[]) {
    let output =
      commands
        .map(x => ServerRoomChatCommandDetail.ConvertToCommand(x, ModChatCommandsModule.id))
        .map(x => ({ ...x, membersOnly: true, modOnly: true }));
    setChatCommands(v => uniqBy([...v, ...output], v => [v.command, v.moduleID].join()));
  }

  function loadRoomOwnerCommands(commands: ServerRoomChatCommandDetail[]) {
    let output = commands.map(x => ServerRoomChatCommandDetail.ConvertToCommand(x, RoomOwnerChatCommandsModule.id));
    setChatCommands(v => uniqBy([...v, ...output], v => [v.command, v.moduleID].join()));
  }

  function unloadRoomOwnerCommands() {
    let output = chatCommands().filter(x => x.moduleID != RoomOwnerChatCommandsModule.id);
    setChatCommands(uniqBy(output, v => [v.command, v.moduleID].join()));
  }

  function triggerSetNickname(currentNickname?: string) {
    if (!currentNickname) {
      addMessage("You don't have a nickname to set.", true, true);
      return Promise.resolve();
    }

    return SwalPR(sfxService).fire({
      title: "Set Nickname",
      input: "text",
      showCancelButton: true,
      inputValue: currentNickname,
      confirmButtonText: "Submit",
      inputPlaceholder: "nickname",
      inputAttributes: {
        max: USER_INPUT.MaxUsernameLength.toString()
      }
    }).then((result) => {
      if (result.isConfirmed && result.value) {
        let value = result.value as string;
        value = value != null ? value.trim() : "";
        websocketService().emitUserUpdateCommand(["Nickname", emojifyService().encode(value)]);
      }
    });
  }

  function clearNickname() {
    if (!appService().client().getDto().userDto?.nickname) {
      addMessage("You don't have a nickname to clear.", true, true);
      return Promise.resolve();
    }

    return SwalPR(sfxService).fire({
      title: "Clear Nickname",
      text: "Are you sure you want to clear your nickname?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Clear it!"
    }).then((result) => {
      if (result.isConfirmed) {
        websocketService().emitUserUpdateCommand(["Nickname", ""]);
      }
    });
  }

  async function triggerSetStatusText(currentStatusText?: string) {
    if (!currentStatusText) {
      addMessage("You don't have a status text to set.", true, true);
      return Promise.resolve();
    }

    let result = await SwalPR(sfxService).fire({
      title: "Set Status Text",
      input: "text",
      showCancelButton: true,
      showDenyButton: true,
      denyButtonText: "Clear Status Text",
      confirmButtonText: "Submit",
      inputPlaceholder: "enter text (Markdown allowed)",
      inputValue: emojifyService().encode(currentStatusText),
      inputAttributes: {
        max: USER_INPUT.MaxStatusTextLength.toString()
      }
    });
    if (result.isConfirmed) {
      websocketService().emitUserUpdateCommand(["StatusText", emojifyService().encode(result.value.trim())]);
    } else if (result.isDenied) {
      clearStatusText();
    }
  }

  async function clearStatusText() {
    if (!appService().client().getDto().userDto?.statusText) {
      addMessage("You don't have a status text to clear.", true, true);
      return Promise.resolve();
    }

    let result = await SwalPR(sfxService).fire({
      title: "Clear Status Text",
      text: "Are you sure you want to clear your status text?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Clear it!"
    });
    if (result.isConfirmed) {
      websocketService().emitUserUpdateCommand(["StatusText", ""]);
    }
  }

  async function triggerSetProfileDescription(currentDescription?: string) {
    if (!currentDescription) {
      addMessage("You don't have a profile description to set.", true, true);
      return Promise.resolve();
    }

    let result = await SwalPR(sfxService).fire({
      title: "Set Profile Description",
      input: "textarea",
      showCancelButton: true,
      showDenyButton: currentDescription != null,
      denyButtonText: "Clear Profile Description",
      confirmButtonText: "Submit",
      inputPlaceholder: "enter description (Markdown allowed)",
      inputValue: emojifyService().encode(currentDescription),
      inputAttributes: {
        max: USER_INPUT.MaxProfileDescriptionLength.toString()
      }
    });
    if (result.isConfirmed && result.value) {
      websocketService().emitUserUpdateCommand(["ProfileDescription",
        emojifyService().encode(result.value.trim())
      ]);
    } else if (result.isDenied) {
      clearProfileDescription();
    }
  }

  function clearProfileDescription() {
    if (!appService().client().getDto().userDto?.ProfileDescription) {
      addMessage("You don't have a profile description to clear.", true, true);
      return Promise.resolve();
    }

    return SwalPR(sfxService).fire({
      title: "Clear Profile Description",
      text: "Are you sure you want to clear your profile description?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Clear it!"
    }).then((result) => {
      if (result.isConfirmed) websocketService().emitUserUpdateCommand(["ProfileDescription", ""]);
    });
  }

  function clearRecentMentionedUsers() {
    setRecentMentionedUsers([]);
    setSelectedMentionedUser(null);
  }

  function onDisconnect() {
    setMessages([]);
    setUsersTyping([]);
    setChatMinimized(false);
    setChatMaximized(false);
    setChatMessagesSetToBeDeleted([]);
    setChatMessageBeingRepliedTo();
    setChatMessageBeingEdited();
    setChatMessageToHighlight();
    setChatBarValue();
    setActiveChatCtxID(null);
    setShowChatBar(true);
    setShowChatWindowButtons(true);
    setChatCommandMode(false);
    setChatCommandsDisplayed(false);
    setAnyListCommandsDisplayed(false);
    setChatOpacity(DEFAULT_CHAT_OPACITY);
    setChatBarElement();
    setChatBarInputElement();
    setActiveMessageItemIndexToShowOptionsMenu();
    disposeContextMenu();
    clearAllMessages();
    clearRecentMentionedUsers();
  }

  return {
    initialize,
    loadServerModCommands,
    loadRoomOwnerCommands,
    unloadRoomOwnerCommands,
    addMessage,
    messages,
    chatBarElement,
    checkIfCommandCanBeRan,
    findMessagesBy,
    runRoomOwnerChatCommand,
    runServerModChatCommand,
    runModChatCommand,
    runClientChatCommand,
    isSystemMessage,
    canDeleteMessage,
    canEditMessage,
    canReplyToMessage,
    onEditMessage,
    addedMessagesEvents,
    editedMessagesEvents,
    deletedMessagesEvents,
    DEFAULT_CHAT_LIST_WIDTH,
    runRoomChatServerCommand,
    chatCommands,
    chatCommandModules,
    setChatBarElement,
    setChatBarInputElement,
    activeChatCtxID,
    chatMessageToHighlight,
    anyListCommandsDisplayed,
    setAnyListCommandsDisplayed,
    triggerClearProfileDescription: clearProfileDescription,
    clearNickname,
    clearProfileDescription,
    clearStatusText,
    chatBarValue,
    setChatBarValue,
    setChatCommandsDisplayed,
    chatMessageBeingRepliedTo,
    chatMessageBeingEdited,
    chatMessagesSetToBeDeleted,
    messsageHasTaggedUser,
    getIndexByMessageID,
    getMessageByIndex,
    canReportMessage,
    getMessageByID,
    setChatMessagesSetToBeDeleted,
    setChatMessageToHighlight,
    setChatMessageBeingRepliedTo,
    setChatMessageBeingEdited,
    setActiveMessageItemIndexToShowOptionsMenu,
    activeMessageItemIndexToShowOptionsMenu,
    setChatMinimized,
    setChatOpacity,
    setShowChatBar,
    alwaysAutoScroll,
    setShowChatWindowButtons,
    setChatMaximized,
    chatMaximized,
    chatBarInputElement,
    showChatBar,
    disposeContextMenu,
    setChatContainerElement,
    showChatWindowButtons,
    chatMinimized,
    chatOpacity,
    ctxMenuActive,
    chatCommandsDisplayed,
    preventDefaultIfChatCommandsDisplayed,
    defaultChatOpacity: DEFAULT_CHAT_OPACITY,
    ContainerWidth,
    usersTyping,
    setAlwaysAutoScroll,
    recentMentionedUsers,
    selectedMentionedUser, setSelectedMentionedUser,
    clearRecentMentionedUsers,
    // Keep track of the last x users mentioned
    setLastSelectedUser: (usertag: string) => {
      let _users = cloneDeep(recentMentionedUsers());
      if (_users.length >= 5) {
        _users.shift();
      }
      _users.push(usertag);
      setRecentMentionedUsers(_users);
    }
  };
}