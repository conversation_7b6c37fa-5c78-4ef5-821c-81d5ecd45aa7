import { createEventBus, EventBus } from "@solid-primitives/event-bus";
import { createMemo, createSignal } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { ClientSideUserDto } from "~/proto/user-renditions";
import { getActiveUser, getUserBasicAccount } from "~/server/general.api";
import { CoreService } from "~/types/app.types";
import { SocketID } from "~/types/user.types";
import DatabaseServiceController from "./db.service";

export type UserServiceEvents =
  [type: "MutedNotes", value: string[]]
  | [type: "MutedChat", value: string[]];

/**
 * UsersService is responsible for managing user-related operations and data.
 * It provides methods for initializing, loading data, and manipulating user information.
 * @returns An object containing various methods for interacting with user data.
 */
export default function UsersService() {
  const dbService = useService(DatabaseServiceController);

  const [users, setUsers] = createStore<ClientSideUserDto[]>([]);
  const [initialized, setInitialized] = createSignal(false);

  const mutedNotesUsersStore = dbService().createStore("muted-note-users", "muted");
  const mutedChatUsersStore = dbService().createStore("muted-chat-users", "muted");

  const events = createEventBus<UserServiceEvents>();
  let coreService: CoreService | undefined;

  const loadMutedNotes = async () => {
    try {
      let mutedNotes = await dbService().getAllKeys(mutedNotesUsersStore);
      events.emit(["MutedNotes", mutedNotes]);

      mutedNotes.forEach((sourceSocketID) => {
        coreService?.send_app_action(
          AppStateActions.create({
            action: AppStateActions_Action.SetUserMuted,
            boolValue: true,
            sourceSocketID
          })
        );
      });
    } catch (ex) {
      console.error("Failed to load users with muted notes:", ex);
    }
  };

  const loadMutedChat = async () => {
    try {
      let mutedChat = await dbService().getAllKeys(mutedChatUsersStore);
      events.emit(["MutedChat", mutedChat]);

      mutedChat.forEach((sourceSocketID) => {
        coreService?.send_app_action(
          AppStateActions.create({
            action: AppStateActions_Action.SetUserChatMuted,
            boolValue: true,
            sourceSocketID
          })
        );
      });
    } catch { }
  };

  const loadMutedFromDb = async () => {
    await loadMutedChat();
    await loadMutedNotes();
  };

  async function initialize(
    coreService_: CoreService,
    appStateEffectsBus: EventBus<AppStateEffects>
  ) {
    if (initialized()) return;
    coreService = coreService_;
    await loadMutedFromDb();

    appStateEffectsBus.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.UsersSet:
          if (effect.clientSideUserDtoList?.list) setUsers(effect.clientSideUserDtoList.list);
          break;

        case AppStateEffects_Action.AddUser:
          if (effect.clientSideUserDto) setUsers([...users, effect.clientSideUserDto]);
          break;

        case AppStateEffects_Action.UpdateUser: {
          if (!effect.clientSideUserDto) return;
          // const newUsers = users.filter(user => user.socketID !== effect.clientSideUserDto?.socketID);
          // setUsers([...newUsers, effect.clientSideUserDto]);
          setUsers(
            user => user.socketID === effect.clientSideUserDto?.socketID,
            effect.clientSideUserDto
          );
          break;
        }
        case AppStateEffects_Action.RemoveUser:
          if (effect.socketId) setUsers(users.filter(user => user.socketID !== effect.socketId));
          break;
      }
    });
    setInitialized(true);
  }

  async function onUserConnect() {
    await loadMutedFromDb();
  }

  async function addMutedNotesUser(socketID: SocketID) {
    await dbService().put(socketID.toLowerCase(), true, mutedNotesUsersStore);
    await loadMutedNotes();
  }

  async function removeMutedNotesUser(socketID: SocketID) {
    await dbService().deleteKey(socketID.toLowerCase(), mutedNotesUsersStore);
    await loadMutedNotes();
  }

  async function addMutedChatUser(socketID: SocketID) {
    await dbService().put(socketID.toLowerCase(), true, mutedChatUsersStore);
    await loadMutedChat();
  }

  async function removeMutedChatUser(socketID: SocketID) {
    await dbService().deleteKey(socketID.toLowerCase(), mutedChatUsersStore);
    await loadMutedChat();
  }

  const getUser = (predicate: (user: ClientSideUserDto) => boolean) => users.find(predicate);

  const getUserBySocketID = (socketId?: string) =>
    getUser(user => user.socketID.toLowerCase() === socketId?.toLowerCase());

  const getUserByUsertag = (usertag?: string) => getUser(user => user.userDto?.usertag.toLowerCase() === usertag?.toLowerCase());

  const getUserVolumeBySocketID = (socketID: string) => 100;

  const fetchActiveUser = getActiveUser;

  const fetchUserBasicAccount = getUserBasicAccount;

  return {
    initialize,
    getUserBySocketID,
    fetchUserBasicAccount,
    fetchActiveUser,
    onUserConnect,
    addMutedNotesUser,
    getUserByUsertag,
    removeMutedNotesUser,
    addMutedChatUser,
    removeMutedChatUser,
    getUserVolumeBySocketID,
    users,
    /// Get users as non proxy objects
    getUsers: () => users.map(x => ({ ...x }) as ClientSideUserDto),
  };
}