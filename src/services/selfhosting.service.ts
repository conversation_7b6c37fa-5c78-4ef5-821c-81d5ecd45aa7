/**
 * SelfHostingService is a service class that provides functionality related to self-hosting.
 * It includes methods for managing countries, continents, and country selection options.
 */
import { TCountries, countries as _countries, continents, getCountryData, getEmojiFlag } from 'countries-list';
import uniq from 'lodash-es/uniq';
import { createSignal, untrack } from 'solid-js';
import { iso1A2Code } from '@rapideditor/country-coder';
import { EventBus } from '@solid-primitives/event-bus';
import { AppStateEvents } from '~/proto/pianorhythm-events';
import { AppStateEffects, AppStateEffects_Action } from '~/proto/pianorhythm-effects';
import { Peer, DataConnection } from "peerjs";
import { useService } from 'solid-services';
import AppService from './app.service';
import { logError } from '~/util/logger';
import { CHANNELS, COMMON } from '~/util/const.common';
import { MidiDto, PianoRhythmSynthEvent } from '~/types/audio.types';
import { MidiMessageInputBuffer, MidiMessageInputDto } from '~/types/websocket.types';
import { Subject, Subscription } from 'rxjs';

export const DEFAULT_CODE = "??";
export const DEFAULT_COUNTRY = "Unknown";
export const DEFAULT_CONTINENT = "Unknown";

let countries: TCountries = { ..._countries };

let countriesEntries = Object.entries(countries);
export let CountriesOptions =
  uniq([
    DEFAULT_COUNTRY,
    ...Object.values(countries).map(x => x.name)
  ]);

export function SelfHostingService() {
  const appService = useService(AppService);

  let countriesOptionsMeta = uniq([
    ...Object.entries(countries).map(([x, y]) => ({
      id: y.name,
      tagLine: getEmojiFlag(x as any)
    }))
  ]);

  let continentOptions = uniq([
    DEFAULT_CONTINENT,
    ...Object.values(continents)
  ]);

  let continentEntries = uniq([
    [DEFAULT_CODE, DEFAULT_CONTINENT],
    ...Object.entries(continents)
  ]);

  let getContinentCode = (continent: string) => {
    if (!continent) return DEFAULT_CODE;
    let code = continentEntries.find(x => x[1] == continent);
    return code?.[0] || DEFAULT_CODE;
  };

  let continentOptionsMeta = continentEntries.map(([x, y]) => ({ id: y, tagLine: x }));

  type TC = { code: string; name: string; };

  const [country, setCountry] = createSignal<TC>({ code: DEFAULT_CODE, name: DEFAULT_COUNTRY });
  const [continent, setContinent] = createSignal<TC>({ code: DEFAULT_CODE, name: DEFAULT_CONTINENT });
  const [countrySelectOptions, setCountrySelectOptions] = createSignal<string[]>(CountriesOptions);

  const resetCountryAndContinent = () => {
    setCountry({ code: DEFAULT_CODE, name: DEFAULT_COUNTRY });
    setContinent({ code: DEFAULT_CODE, name: DEFAULT_CONTINENT });
  };

  const areDefaultCountryAndContinent = () => {
    return country().code == DEFAULT_CODE && continent().code == DEFAULT_CODE;
  };

  const getCountryByLongAndLat = (long: number, lat: number) => {
    let code = iso1A2Code([long, lat], { level: 'territory' }) ?? DEFAULT_COUNTRY;
    return getCountryData(code as any);
  };

  const getContinentNameByCode = (code: string) => {
    return (continents as any)?.[code] || DEFAULT_CONTINENT;
  };

  const [inSelfHostedRoom, setInSelfHostedRoom] = createSignal(false);
  const [peerOpened, setPeerOpened] = createSignal(false);
  const [queuedConnections, setQueuedConnections] = createSignal<string[]>([]);
  const [useNoteBuffer, setUseNoteBuffer] = createSignal(false);

  let peer: Peer | undefined;
  let connectionMap: Map<string, DataConnection> = new Map<string, DataConnection>();

  const onDestroyClientPeer = () => {
    peer?.destroy();
    peer = undefined;
  };

  const getPeerSocketID = (socketID: string) => `pianorhythm-${COMMON.HOST}-${socketID}`;

  const onClientPeerServiceCleanup = () => {
    connectionMap.forEach((connection) => {
      connection.close();
    });
    connectionMap.clear();
    setInSelfHostedRoom(false);
    setQueuedConnections([]);
    onDestroyClientPeer();
    setUseNoteBuffer(false);
    appService()?.coreService()?.note_buffer_engine_set_self_hosted(false);
  };

  const onClientPeerServiceConnect = () => {
    setInSelfHostedRoom(true);
    appService()?.coreService()?.note_buffer_engine_set_self_hosted(true);
    onDestroyClientPeer();

    peer = new Peer(getPeerSocketID(appService().getSocketID()));
    if (COMMON.IS_DEV_MODE) {
      (window as any).peer = peer;
      (window as any).connectionMap = connectionMap;
    }
    console.log("[SelfHostService] My peer ID:", peer.id);

    peer.on("open", (id) => {
      setPeerOpened(true);
      queuedConnections().forEach(onPeerConnect);
      setQueuedConnections([]);
      console.log("[SelfHostService] Peer opened:", id, appService().roomOwner()!);
    });

    peer.on("error", (error) => {
      console.error("[SelfHostService] Peer error: ", error);
    });

    peer.on("disconnected", (e) => {
      console.warn("[SelfHostService] Peer disconnected: ", e);
      connectionMap.delete(e);
    });

    peer.on('connection', (connection) => {
      console.log("[SelfHostService] Connection received:", connection.peer);

      connection.on("data", onPeerData);

      connection.on("close", () => {
        connectionMap.delete(connection.peer);
      });

      connection.on("error", (error) => {
        connectionMap.delete(connection.peer);
      });

      connection.on("open", () => {
        connectionMap.set(connection.peer, connection);
        console.log("[SelfHostService] Connection opened for:", connection.peer);
      });
    });
  };

  const onPeerData = (input: any) => {
    if (!input) return;
    try {
      let data = input as PianoRhythmSynthEvent;

      switch (data.message_type) {
        case 1: {
          let event = {
            channel: data.channel,
            note: data.raw_bytes[1] ?? 0,
            velocity: data.raw_bytes[2] ?? 0,
            program: data.current_program,
            volume: data.current_volume,
            bank: data.current_bank,
            expression: data.current_expression,
            pan: data.current_pan,
            source: data.source
          };
          appService().coreService()?.from_socket_note_on(event, data.socket_id);
          break;
        }
        default: {
          if (data.raw_bytes.length == 0 && data.message_type == null && data.current_pitch) {
            appService().coreService()?.from_socket_pitch({
              channel: data.channel,
              value: data.current_pitch
            }, data.socket_id);
            break;
          }

          appService().coreService()?.parse_midi_data(
            data.raw_bytes, data.socket_id, data.source, data.device_id
          );
          break;
        }
      }
    } catch (e) {
      console.error("Error parsing peer data: ", e);
    }
  };

  const onPeerConnect = (socketID: string) => {
    if (!inSelfHostedRoom() || !peer) return;
    if (socketID == appService().getSocketID()) return;
    if (connectionMap.has(getPeerSocketID(socketID))) return;

    // No need to connect to other peers if client is the host
    // They'll connect to the host instead
    if (appService().isClientRoomOwner()) return;

    if (!peerOpened()) {
      setQueuedConnections([...queuedConnections(), socketID]);
      return;
    }

    let connection = peer?.connect(getPeerSocketID(socketID), {
      reliable: true,
      metadata: {
        roomID: appService().roomID()
      }
    });

    if (connection) {
      connection.on("data", onPeerData);

      connection.on("error", (error) => {
        console.error("[SelfHostService] error", error);
        connectionMap.delete(getPeerSocketID(socketID));
      });

      connection.on("close", () => {
        console.log("[SelfHostService] Connection closed for socketID: ", socketID);
        connectionMap.delete(getPeerSocketID(socketID));
      });

      connection.on('open', () => {
        console.log("[SelfHostService] Connection opened for socketID: ", socketID);
        connectionMap.set(getPeerSocketID(socketID), connection);
      });
    } else {
      logError(`Connection failed for socketID: ${socketID}`);
    }
  };

  const initialize = (appEvents: EventBus<AppStateEvents>, appEffects: EventBus<AppStateEffects>) => {
    const bufferEngine = NoteBufferEngine();

    appEvents.listen((event) => {
      switch (event) {
        case AppStateEvents.LeftSelfHostedRoom:
        case AppStateEvents.AppStateReset: {
          onClientPeerServiceCleanup();
          break;
        }
      }
    });

    appEffects.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.JoinedRoomSuccess: {
          let roomData = effect.joinedRoomData;
          if (!roomData) return;
          if (!roomData.selfHostedCountryCode) {
            onClientPeerServiceCleanup();
            return;
          }

          onClientPeerServiceConnect();
          break;
        }
        case AppStateEffects_Action.SetRoomSettings: {
          let roomData = effect.roomSettings;
          if (!roomData) return;
          if (!roomData.HostDetails?.CountryCode) {
            onClientPeerServiceCleanup();
            return;
          }

          onClientPeerServiceConnect();
          break;
        }
        case AppStateEffects_Action.UsersSet: {
          effect.clientSideUserDtoList?.list
            .map(x => x.socketID)
            .forEach(onPeerConnect);

          // Remove any connections that are not in the user list
          connectionMap.forEach((connection, key) => {
            if (!effect.clientSideUserDtoList?.list.map(x => getPeerSocketID(x.socketID)).includes(key)) {
              connection.close();
              connectionMap.delete(key);
            }
          });

          break;
        }
        case AppStateEffects_Action.AddUser: {
          if (effect.clientSideUserDto) onPeerConnect(effect.clientSideUserDto.socketID);
          break;
        }
        case AppStateEffects_Action.RemoveUser: {
          if (effect.socketId) {
            let target = getPeerSocketID(effect.socketId);
            connectionMap.get(target)?.close();
            connectionMap.delete(target);
          }
          break;
        }
      }
    });

    let synthEvents = new BroadcastChannel(CHANNELS.PIANORHYTHM_SYNTH_EVENTS);
    synthEvents.onmessage = (e) => {
      let synthEvent = e.data as PianoRhythmSynthEvent;
      if (!synthEvent.is_client) return;
      connectionMap.forEach((connection) => {
        connection.send(e.data);
      });
    };
  };

  return {
    initialize,
    areDefaultCountryAndContinent,
    resetCountryAndContinent,
    getCountryByLongAndLat,
    getContinentNameByCode,
    countries: _countries,
    continentOptionsMeta,
    continentOptions,
    countriesOptionsMeta,
    countriesEntries,
    getCountryData,
    getContinentCode,
    country, setCountry,
    continent, setContinent,
    countrySelectOptions, setCountrySelectOptions,
    setUseNoteBuffer
  };
}

export function NoteBufferEngine() {
  const [noteBuffer, setNoteBuffer] = createSignal<MidiMessageInputBuffer[]>([]);
  const [noteBufferTime, setNoteBufferTime] = createSignal<number | null>(null);
  const [serverTimeOffset, setServerTimeOffset] = createSignal(0);
  const [subscriptions, setSubscriptions] = createSignal<Subscription[]>([]);
  const MAX_NOTE_BUFFER_SIZE = 300;
  const [noteBufferInterval, setNoteBufferInterval] = createSignal(-1);
  const [roomIsSelfHosted, setRoomIsSelfHosted] = createSignal(false);
  let noteBufferingStarted = false;
  const incomingMidiMessageEvents = new Subject<MidiDto>();
  const midiMessageReadyToBeEmitted = new Subject<MidiMessageInputDto>();
  let broadcastChannel: BroadcastChannel | undefined;

  function initNoteBuffering() {
    // if (noteBufferingStarted) return;
    // noteBufferingStarted = true;
    // console.debug("Note Buffering started.");

    // clearSubs();
    // setSubscriptions([
    //   incomingMidiMessageEvents
    //     .subscribe(evt => {
    //       if (roomIsSelfHosted()) {
    //         let output: MidiMessageInputDto = {
    //           data: [{ data: evt }],
    //           time: "0"
    //         };
    //         onHandleInputDto(output);
    //       } else {
    //         let nbft = untrack(noteBufferTime);
    //         if (nbft == null) {
    //           setNoteBufferTime(Date.now());
    //           let delay = 0;
    //           // Add a minimum delay
    //           if (evt[0] == "NoteOff") delay = 35;
    //           pushToNoteBuffer({ data: evt, delay });
    //         } else {
    //           let time = Date.now() - (nbft as number);
    //           pushToNoteBuffer({ data: evt, delay: time });
    //         }
    //       }
    //     })
    // ]);

    // clearInterval(noteBufferInterval());
    // setNoteBufferInterval(
    //   setInterval(() => {
    //     let bufferTime = untrack(noteBufferTime);
    //     if (bufferTime == null) return;

    //     let currentBuffer = untrack(noteBuffer) as MidiMessageInputBuffer[];
    //     if (currentBuffer.length <= 0) return;

    //     let output: MidiMessageInputDto = {
    //       data: currentBuffer,
    //       time: (bufferTime + serverTimeOffset()).toString()
    //     };

    //     onHandleInputDto(output);
    //     setNoteBufferTime(null);
    //     setNoteBuffer([]);
    //   }, 200) as any
    // );
  }

  function onHandleInputDto(output: MidiMessageInputDto) {
    if (broadcastChannel) {
      broadcastChannel.postMessage(output);
    } else {
      midiMessageReadyToBeEmitted.next(output);
    }
  }

  function pushToNoteBuffer(buffer: MidiMessageInputBuffer) {
    let currentBuffer = untrack(noteBuffer);
    if (currentBuffer.length <= MAX_NOTE_BUFFER_SIZE)
      setNoteBuffer([...noteBuffer(), buffer]);
  }

  function start(useBroadcastChannel = true) {
    if (!broadcastChannel && useBroadcastChannel) {
      broadcastChannel = new BroadcastChannel(CHANNELS.NOTEBUFFER_EMIT);
    }
    initNoteBuffering();
  }

  function onSetRoomIsSelfHosted(value: boolean) {
    setRoomIsSelfHosted(value);
    if (value) {
      setNoteBuffer([]);
      clearInterval(noteBufferInterval());
      noteBufferingStarted = false;
    } else {
      initNoteBuffering();
    }
  }

  function clearSubs() {
    subscriptions().forEach(x => x.unsubscribe());
    setSubscriptions([]);
  }

  function stop() {
    broadcastChannel?.close();
    broadcastChannel = undefined;
    clearInterval(noteBufferInterval());
    noteBufferingStarted = false;
    setNoteBufferTime(null);
    setNoteBuffer([]);
    setNoteBufferInterval(-1);
    clearSubs();
  }

  function emitMidiMessage(payload: MidiDto) {
    incomingMidiMessageEvents.next(payload);
  }

  return {
    start,
    stop,
    emitMidiMessage,
    serverTimeOffset,
    setServerTimeOffset,
    onSetRoomIsSelfHosted
  };
}
