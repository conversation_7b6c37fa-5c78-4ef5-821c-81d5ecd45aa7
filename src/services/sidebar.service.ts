import { createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import DisplaysService from "./displays.service";

export function SidebarService() {
  const displayService = useService(DisplaysService);
  const [ctxMenuActive, setCtxMenuActive] = createSignal(false);
  const [sideBarFullyShown, setSideBarFullyShown] = createSignal(false);
  const [containerElement, setContainerElement] = createSignal<HTMLDivElement>();
  const [userProfileCardActive, setUserProfileCardActive] = createSignal<string | null>(null);
  const [actionWidgetsContainer, setActionWidgetsContainer] = createSignal<HTMLDivElement>();

  function initialize() {

  }

  function disposeContextMenu() {
    setCtxMenuActive(false);
  }

  function onDisconnect() {
    disposeContextMenu();
  }

  function dispose() {
    onDisconnect();
  }

  createEffect(() => {
    // Hide the user profile card if any modal is opened
    if (displayService().anyModalsOpened()) setUserProfileCardActive(null);
  });

  return ({
    onDisconnect,
    initialize,
    actionWidgetsContainer, setActionWidgetsContainer,
    ctxMenuActive,
    containerElement,
    setContainerElement,
    setCtxMenuActive,
    disposeContextMenu,
    dispose,
    userProfileCardActive,
    setUserProfileCardActive,
    setSideBarFullyShown,
    sideBarFullyShown
  });
}