import { createMediaQuery } from "@solid-primitives/media";

/**
 * A service for handling media queries.
 *
 * @returns An object containing properties for different screen sizes.
 */
export default function MediaQueryService() {
  const isMobileScreen = createMediaQuery("(min-width: 320px)", true);
  const isSmallScreen = createMediaQuery("(min-width: 640px)", true);

  return {
    isMobileScreen,
    isSmallScreen
  };
}