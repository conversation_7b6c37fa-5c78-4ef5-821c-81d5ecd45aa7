import { AppSettings } from "~/types/settings.types";
import { Roles, UserClientDto, rolesToJSO<PERSON> } from "~/proto/user-renditions";
// import * as Sentry from "@sentry/browser";
// import { Scope } from "@sentry/browser";
import { COMMON } from "~/util/const.common";
import { createSignal } from "solid-js";
import { <PERSON>sHelper } from "~/types/user-helper";

//@ts-ignore
import * as ackeeTracker from "ackee-tracker";
//@ts-ignore
import * as CookieConsent from "vanilla-cookieconsent";

const USE_SENTRY = true && COMMON.IS_PRODUCTION && COMMON.IS_WEB_APP;
const USE_ACKEE = !COMMON.IS_DEV_MODE || COMMON.IS_PRODUCTION || COMMON.IS_STAGING;

export const TRACKING_EVENT_IDS = {
  INSTRUMENT_SELECTION: "a22b6b7a-72ff-4056-a2b6-bb07ccb58eab",
  LOGIN_BY_TIME_OF_DAY: "143a49ea-f9f7-4853-a8c0-7c5d161cefc8",
  ROOM_TYPE_CREATED: "494eddd4-2b83-4406-8053-958c9d9a65dc",
  ROOM_STAGE_CREATED: "558a200f-f6a1-49d3-bfe3-ccf4cc409f64",
  ROOM_STATUS_CREATED: "c33362ea-a493-453b-8d35-3a1ba89d7710",
  MIDI_PLAYER_PLAYED: "617ec110-fc59-419a-afae-9376168e0267",
  VP_SEQUENCER_PLAYED: "06a34b54-d8bf-47a0-b1e5-44a5a6e84884",
  GLOBAL_SHEET_MUSIC_VIEWS: "03aa27d8-f20a-4437-b1aa-3362bbaf7f84",
  GLOBAL_SHEET_MUSIC_CREATED: "99773e94-1ad5-4f1b-854b-a6d1df77b211",
  GLOBAL_SHEET_MUSIC_UPDATED: "65a900e0-d9fd-4f3b-8afc-7de142ea9cc5",
  GLOBAL_NOTES_SENT: "eea9d7fd-3cc1-4300-8b4b-4a31c2a714b2",
  GLOBAL_CHAT_MESSAGES_SENT: "3503b08d-876f-48d9-b699-87312b131d7f",
};

/**
 * MonitorTrackingService is a service responsible for tracking user events and sending analytics data.
 * It provides methods for initializing the tracker, tracking events, updating tracked events, and managing user settings.
 * @returns An object containing various methods for interacting with the tracking service.
 */
function MonitorTrackingService() {
  const [isInitialized, setIsInitialized] = createSignal(false);
  const [offlineMode, setOfflineMode] = createSignal(Boolean(localStorage.getItem("offlineMode")));

  let tracker: undefined | any = undefined;
  let trackerStop: undefined | (() => void) = undefined;
  const eventTimeMap = new Map();

  const initialize = () => {
    if (isInitialized() || offlineMode()) return;

    const isProduction: boolean = COMMON.IS_PRODUCTION;
    let environment = isProduction ? "production" : COMMON.MODE;
    environment += ` (${COMMON.IS_DESKTOP_APP ? "DESKTOP" : "WEB"})`;

    if (USE_SENTRY) {
      // Sentry.init({
      //   dsn: COMMON.SENTRY_DSN,
      //   ignoreErrors: [
      //     'ResizeObserver loop limit exceeded'
      //   ],
      //   environment,
      //   release: `${COMMON.CLIENT_VERSION}-0`,
      //   integrations: [
      //     Sentry.browserTracingIntegration(),
      //     Sentry.replayIntegration()
      //   ],
      //   // Performance Monitoring
      //   tracesSampleRate: isProduction ? 0.25 : 1.0, // Capture 100% of the transactions, reduce in production!
      //   // Session Replay
      //   replaysSessionSampleRate: 0.1, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
      //   replaysOnErrorSampleRate: 0.5, // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
      // });

      // Sentry.withScope((scope: Scope) => {
      //   let mode = COMMON.MODE;
      //   if (mode == "local-dev") mode = "development";
      //   scope.setExtra("mode", mode);
      //   scope.setExtra("client-version", COMMON.CLIENT_VERSION);
      //   scope.setExtra("client-build-date", COMMON.CLIENT_BUILD_DATE);
      // });
    }

    if (USE_ACKEE) {
      document.documentElement.classList.add('cc--dark-pianorhythm');

      CookieConsent.run({
        guiOptions: {
          consentModal: {
            layout: "box inline",
            position: "bottom right",
            equalWeightButtons: false,
            flipButtons: false
          },
          preferencesModal: {
            layout: "box",
            position: "right",
            equalWeightButtons: true,
            flipButtons: false
          }
        },
        categories: {
          necessary: {
            enabled: true,
            readOnly: true
          },
          analytics: {}
        },
        language: {
          default: "en",
          autoDetect: "browser",
          translations: {
            en: {
              consentModal: {
                title: 'Yes, we use cookies! 🍪',
                description: `
                This website utilizes cookies to enable essential site functionality and analytics.
                You may change your settings at any time or accept the default settings.
                <br/><br/>
                You may close this banner to continue with only essential cookies.
                <br/>
                (After entering) You can also go to <b>Settings > Advanced > Cookie Management</b> to change your preferences.
                `,
                acceptAllBtn: 'Accept all',
                acceptNecessaryBtn: 'Reject all',
                showPreferencesBtn: 'Manage Individual preferences'
              },
              preferencesModal: {
                title: 'Manage cookie preferences',
                acceptAllBtn: 'Accept all',
                acceptNecessaryBtn: 'Reject all',
                savePreferencesBtn: 'Accept current selection',
                closeIconLabel: 'Close modal',
                sections: [
                  {
                    title: 'Somebody said ... cookies?',
                    description: `I want one!
                    You can also modify these preferences after entering the site.<br/>
                    Go to <b>Settings > Advanced > Cookie Management</b> to change your preferences.
                    `,
                  },
                  {
                    title: 'Strictly Necessary cookies',
                    description: 'These cookies are essential for the proper functioning of the website and cannot be disabled.',

                    //this field will generate a toggle linked to the 'necessary' category
                    linkedCategory: 'necessary'
                  },
                  {
                    title: 'Performance and Analytics',
                    description: 'These cookies collect information about how you use our website. All of the data is anonymized and cannot be used to identify you.',
                    linkedCategory: 'analytics'
                  },
                  {
                    title: 'More information',
                    description: 'For any queries in relation to my policy on cookies and your choices, please <a href="#contact-page">contact us</a>'
                  }
                ]
              }
            }
          }
        },
        onConsent: (cookies) => {
          createTracker(cookies.cookie.categories.includes('analytics'));
        },
        onChange: function ({ changedCategories }) {
          if (changedCategories.includes('analytics')) {
            if (CookieConsent.acceptedCategory('analytics')) {
              return createTracker(true);
            }
          }

          stopTracker();
        }
      });
    }

    setIsInitialized(true);
  };

  const stopTracker = () => {
    trackerStop?.();
    trackerStop = undefined;
    tracker = undefined;
  };

  const createTracker = (withConsent = false) => {
    if (offlineMode()) return;
    if (tracker) return;

    if (trackerStop) {
      stopTracker();
    }

    tracker = ackeeTracker.create(COMMON.ANALYTICS_URL, {
      detailed: withConsent,
      ignoreLocalhost: false,
      ignoreOwnVisits: false,
    });

    let { stop } = tracker.record(COMMON.ACKEE_DOMAIN_ID);
    trackerStop = stop;
  };

  const onConfigureScope = (func: (scope: any) => void) => {
    if (USE_SENTRY) {
      // Sentry.withScope((scope: Scope) => {
      //   func(scope);
      // });
    }
  };

  const onDisconnect = () => {
    eventTimeMap.clear();
  };

  const dispose = () => {

  };

  const trackUser = (dto: UserClientDto, settings: AppSettings) => {
    if (offlineMode()) return;
    if (!dto.userDto) return;

    let userRole = RolesHelper.getHighestRole(dto.userDto.roles as Roles[])?.role;

    if (USE_SENTRY) {
      // Sentry.setUser({
      //   id: dto.userDto.socketID,
      //   username: dto.userDto.usertag,
      //   segment: userRole ? rolesToJSON(userRole) : "unknown role"
      // });
    }

    onConfigureScope((scope) => {
      scope.setExtra("settings", settings);
      scope.setExtra("client-roles", (dto.userDto?.roles || []).map(rolesToJSON));
    });
  };

  const captureException = (error: Error) => {
    if (offlineMode()) return;
    if (USE_SENTRY) {
      // Sentry.captureException(error);
    }
  };

  const showCookiePreferences = () => {
    CookieConsent.showPreferences();
  };

  const trackEvent = (eventID: string, key: string, value = 1): Promise<string | null> => {
    return new Promise((resolve, reject) => {
      // if (!tracker) return reject("Tracker not initialized!");
      if (!tracker) return resolve(null);
      if (offlineMode()) return resolve(null);

      const eventTimeKey = `event-${eventID}-${key}`;
      const now = Date.now();
      const limit = 60_000; // Limit in milliseconds, adjust as needed
      const lastEventTime = eventTimeMap.get(eventTimeKey);

      if (lastEventTime && now - lastEventTime < limit) {
        // return reject("Too many requests!");
        return resolve(null);
      }

      eventTimeMap.set(eventTimeKey, now);

      tracker.action(eventID, {
        key, value: value
      }, (_actionID: string) => {
        resolve(_actionID);
      });
    });
  };

  const onLogin = () => {
    if (offlineMode()) return;

    trackEvent(
      TRACKING_EVENT_IDS.LOGIN_BY_TIME_OF_DAY,
      (new Date()).toUTCString(), 1
    );
  };

  const updateTrackedEvent = (actionID: string, key: string, data: any) => {
    if (offlineMode()) return;
    if (!tracker) return;

    tracker.updateAction(actionID, {
      key,
      value: data
    });
  };

  return {
    GET_EVENT_IDS: () => ({ ...TRACKING_EVENT_IDS }),
    initialize,
    trackEvent,
    updateTrackedEvent,
    dispose,
    onDisconnect,
    trackUser,
    onConfigureScope,
    captureException,
    onLogin,
    setOfflineMode,
    showCookiePreferences
  };
}

export default MonitorTrackingService;