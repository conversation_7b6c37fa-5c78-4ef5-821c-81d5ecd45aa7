import Bowser from "bowser";
import { createSignal } from "solid-js";
import { COMMON } from "~/util/const.common";
import { isBrowser, isMobile as isMob<PERSON><PERSON>elper } from "~/util/helpers.dom";
import { logDebug } from "~/util/logger";
import SwalPR from "~/util/sweetalert";
import SoundEffectsService from "./sound-effects.service";

// type OsName = Lowercase<"Windows" | "MacOs" | "Linux" | "Android" | "iOS" | "ChromeOS" | "Windows Phone" | "BlackBerry" | "Other">;

/**
 * Represents a platform service that provides information about the browser, platform, and operating system.
 */
export default function PlatformService() {
  let sfxService: (() => ReturnType<typeof SoundEffectsService>) | undefined = undefined;

  const [isMobile, setIsMobile] = createSignal(false);
  const [isFireFox, setIsFireFox] = createSignal(false);
  const [osPlatform, setOSPlatform] = createSignal<string>();
  const [is64Bit] = createSignal<boolean>(arch64.some(x => window.navigator.userAgent.includes(x)));
  const [initialized, setInitialized] = createSignal(false);

  const initialize = (_sfxService?: () => ReturnType<typeof SoundEffectsService>) => {
    if (initialized()) return;
    sfxService = _sfxService;

    let agent = parseBrowserAgent();
    onCheckBrowserVersion(agent);
    setInitialized(true);
  };

  /**
   * Parses the browser agent to extract information about the browser, platform, and operating system.
   *
   * @returns The BowserParser object containing the parsed browser agent information.
   */
  const parseBrowserAgent = () => {
    if (!isBrowser()) return;

    let bowserParser = Bowser.getParser(window.navigator.userAgent);
    let browserPlatform = bowserParser.getPlatformType().toLowerCase();
    let browserName = bowserParser.getBrowserName().toLowerCase();
    let osPlatform = bowserParser.getOSName(true);

    if (COMMON.IS_DEV_MODE)
      console.log("Browser Info:", bowserParser.getBrowserName(), bowserParser.getOSName(true), bowserParser.getPlatformType());

    setIsMobile(browserPlatform == "mobile" || isMobileHelper());
    setIsFireFox(browserName == "firefox");
    setOSPlatform(osPlatform);
    logDebug(`PlatformService - Browser: ${browserName} | Platform: ${osPlatform}`);

    return bowserParser;
  };

  /**
   * Checks the browser version and displays a warning if the browser is not recommended.
   *
   * @param bowserParser - Optional object with methods to get the browser engine name, browser name, and browser version.
   */
  const onCheckBrowserVersion = (bowserParser?: { getEngineName: () => string; getBrowserName: () => any; getBrowserVersion: () => any; } | undefined) => {
    if (!bowserParser) return;

    let engineName = bowserParser.getEngineName().toLowerCase();

    let isBrowserRecommended =
      engineName == "blink" ||
      engineName == "edge" ||
      engineName == "webkit" ||
      COMMON.IS_DESKTOP_APP;

    if (!isBrowserRecommended)
      SwalPR(sfxService).fire({
        title: "Browser not supported",
        icon: "warning",
        html: `
          <p>Hey! Just a heads up that this browser may not be fully supported.</p><br>
          Certain features like <b>WebMIDI</b> or <b>WebGL</b> may not be working so expect an impact to performance.
          <br><br>
          <p>We recommend using a chromium based browser such as Chrome, Edge, Opera, etc.</p>

          <br><br>
          <p>--Browser Info--<p>
          <p>Name: ${bowserParser.getBrowserName()}</p>
          <p>Engine: ${bowserParser.getEngineName()}</p>
          <p>Version: ${bowserParser.getBrowserVersion()}</p>
        `
      });
  };

  return {
    initialized,
    initialize,
    isMobile,
    isFireFox,
    osPlatform,
    is64Bit,
  };
}

const arch64 = [
  "x86_64",
  "x86-64",
  "Win64",
  "x64",
  "amd64",
  "AMD64",
  "WOW64",
  "x64_64",
];