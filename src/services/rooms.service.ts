import { EventBus } from "@solid-primitives/event-bus";
import { createSignal } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { JoinRoomFailType } from "~/proto/client-message";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import { BasicRoomDto } from "~/proto/room-renditions";
import { getRoomSettings } from "~/server/general.api";
import { CoreService } from "~/types/app.types";
import AppService from "./app.service";
import DisplaysService from "./displays.service";
import NotificationService from "./notification.service";

export default function RoomsService() {
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);

  const [initialized, setInitialized] = createSignal(false);
  const [roomIDofRoomWaitingForPassword, setRoomIDofRoomWaitingForPassword] = createSignal<{ newSession?: boolean, roomName: string, roomID?: string; } | null>(null);

  const [store, setStore] = createStore<{ rooms: BasicRoomDto[]; }>({ rooms: [] });
  let coreService: CoreService | undefined;

  function initialize(
    coreService_: CoreService,
    appStateEffectsBus: EventBus<AppStateEffects>,
    appStateEventsBus: EventBus<AppStateEvents>
  ) {
    if (initialized()) return;

    coreService = coreService_;
    appStateEffectsBus.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.SetRooms:
          if (effect.roomsList?.list) setStore("rooms", effect.roomsList.list);
          break;

        case AppStateEffects_Action.AddRoom:
        case AppStateEffects_Action.UpdateRoom:
          if (effect.basicRoomDto) {
            const newRooms = store.rooms.filter(room => room.roomID !== effect.basicRoomDto?.roomID);
            setStore("rooms", [...newRooms, effect.basicRoomDto]);
          }
          break;

        case AppStateEffects_Action.DeleteRoom:
          if (effect.roomId) setStore("rooms", store.rooms.filter(room => room.roomID !== effect.roomId));
          break;

        case AppStateEffects_Action.JoinRoomFailResponse: {
          let data = effect.joinRoomFailResponse;
          if (!data) return;

          if (data.reason == JoinRoomFailType.EnterPassword) {
            let newSession = !appService().roomID();
            if (newSession) {
              appService().setActivatePageLoader(true);
              displayService().setDisplay("ENTER_ROOM_PASSWORD_MODAL", true);
            }

            // Enter password modal is automatically listening for this signal
            setRoomIDofRoomWaitingForPassword({ newSession, roomID: data.roomID, roomName: data.roomName });
            return;
          }

          break;
        }
      }
    });

    appStateEventsBus.listen((event) => {
      switch (event) {
        case AppStateEvents.RoomMuted:
          NotificationService.show({
            type: "info",
            description: `You've <b>muted</b> everyone else in the room.`,
          });
          break;
        case AppStateEvents.RoomUnmuted:
          NotificationService.show({
            type: "info",
            description: `You've <b>unmuted</b> everyone else in the room.`,
          });
          break;
      }
    });

    setInitialized(true);
  }

  const fetchRoomSettings = getRoomSettings;

  return {
    initialize,
    fetchRoomSettings,
    roomIDofRoomWaitingForPassword, setRoomIDofRoomWaitingForPassword,
    rooms: () => store.rooms,
    store
  };
}