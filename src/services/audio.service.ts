import { decode } from "@msgpack/msgpack/dist/decode";
import { raceTimeout, until } from "@solid-primitives/promise";
import { invoke } from "@tauri-apps/api/core";
import { listen, UnlistenFn } from "@tauri-apps/api/event";
import { appDataDir } from "@tauri-apps/api/path";
import { BaseDirectory, exists, mkdir, remove } from "@tauri-apps/plugin-fs";
import convertSize from "convert-size";
import { UseStore } from "idb-keyval";
import clamp from "lodash-es/clamp";
import debounce from "lodash-es/debounce";
import isEmpty from "lodash-es/isEmpty";
import isEqual from "lodash-es/isEqual";
import isNumber from "lodash-es/isNumber";
import isString from "lodash-es/isString";
import uniqBy from "lodash-es/uniqBy";
import {
  bufferTime,
  debounceTime,
  distinctUntilKeyChanged,
  filter,
  groupBy,
  mergeAll,
  mergeMap,
  of,
  map as rxMap,
  Subject,
  switchMap
} from "rxjs";
import { batch, createEffect, createMemo, createResource, createSignal, JSXElement } from "solid-js";
import { useService } from "solid-services";
import {
  ActiveChannelsMode,
  activeChannelsModeFromJSON,
  AudioChannel,
  Instrument,
  SetChannelInstrumentPayload,
  SetChannelInstrumentType,
  SF2Program
} from "~/proto/midi-renditions";
import {
  AppStateActions,
  AppStateActions_Action,
  AudioSynthActions,
  AudioSynthActions_Action,
  ChannelWithUint32
} from "~/proto/pianorhythm-actions";
import { AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import { ClientSideUserDto } from "~/proto/user-renditions";
import UsersService from "~/services/users-service";
import {
  AudioChannelHelper,
  AudioDrumKit,
  AudioServiceNoteActivity,
  AudioSynthesizerEngine,
  CUSTOM_SOUNDFONT_PREFIX,
  Equalizer,
  EQUALIZER_FREQUENCIES,
  EqualizerBand,
  EqualizerPreset,
  PianoRhythmSynthEvent,
  Soundfonts,
  SynthInterpolationMethod
} from "~/types/audio.types";
import { MidiNoteSource } from "~/types/midi.types";
import { DefaultAppSettings } from "~/types/settings.types";
import { SocketID } from "~/types/user.types";
import { AUDIO, CHANNELS, COMMON, IDS } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";
import { canCreateSharedArrayBuffer, getFileNameFromPath, isSoundfontFilePath_windows, map } from "~/util/helpers";
import { memoizeDebounce } from "~/util/lodash-util";
import { logDebug, logError, logInfo } from "~/util/logger";
import { SynthAudioContext } from "~/util/synth-audio-ctx";
import WasmProcessorUrl from "~/util/wasm-processor.worklet?worker&url";
import AppService from "./app.service";
import DatabaseServiceController from "./db.service";
import NotificationService from "./notification.service";
import AppSettingsService from "./settings-storage.service";
import WebMidiService from "./webmidi.service";
import { EventBus } from "@solid-primitives/event-bus";

const soundfontStoreDbName = "custom-soundfonts";
const soundfontStoreName = "soundfonts";

type CustomSoundfontDB = {
  name: string,
  data: Uint8Array;
};

type SocketIDWitHash = { socketID: string, hash: number; };

/**
 * AudioService class responsible for managing audio settings and functionality.
 * This class provides methods for controlling volume, soundfonts, MIDI settings, and more.
 * It also handles loading and playing audio samples.
 */
export default function AudioService() {
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const webMidiService = useService(WebMidiService);
  const dbService = useService(DatabaseServiceController);
  const usersService = useService(UsersService);

  const [volumeMuted, setVolumeMuted] = createSignal(false);
  const [volume, setVolume] = createSignal<number>(AUDIO.DEFAULT_CHANNEL_VOLUME);
  const [lastSavedVolume, setLastSavedVolume] = createSignal<number>(-1);
  const [mappedVolume, setMappedVolume] = createSignal<number>(-1);
  const [mousePosSetsVelocity, setMousePosSetsVelocity] = createSignal<boolean>(DefaultAppSettings.AUDIO_MOUSE_POS_SETS_VELOCITY);
  const [loadingSoundfont, setLoadingSoundfont] = createSignal(false);
  const [loadedSoundfont, setLoadedSoundfont] = createSignal<string | null>(null);
  const [loadedSoundfontName, setLoadedSoundfontName] = createSignal<string>();
  const [sustainPress, setSustainPress] = createSignal(false);
  const [sustained, setSustained] = createSignal(false);
  const [synthSustain, setSynthSustain] = createSignal(false);
  const [sampleRate, setSampleRate] = createSignal<number | string>();
  const [audioSynthEngine, setAudioSynthEngine] = createSignal<AudioSynthesizerEngine>(DefaultAppSettings.AUDIO_SYNTH_ENGINE);
  const [loadedAudioSynthEngine, setLoadedAudioSynthEngine] = createSignal<AudioSynthesizerEngine>(audioSynthEngine());
  const [initialized, setInitialized] = createSignal(false);
  const [workletFailedToLoad, setWorkletFailedToLoad] = createSignal(false);
  const [clientAdded, setClientAdded] = createSignal(false);
  const [subscriptions, setSubscriptions] = createSignal<VoidFunction[]>([]);
  const [channels, setChannels] = createSignal<AudioChannel[]>([]);
  const [loadingSoundfontFileName, setLoadingSoundfontFileName] = createSignal<string>();
  const [loadedSoundfontIsCustom, setLoadedSoundfontIsCustom] = createSignal(false);
  const [hashedUsers, setHashedUsers] = createSignal<SocketIDWitHash[]>([]);
  const [programs, setPrograms] = createSignal<SF2Program[]>([], { equals: isEqual });
  const isSustained = () => sustainPress() || sustained();
  const [primaryChannel, setPrimaryChannel] = createSignal<number>(AUDIO.DEFAULT_PRIMARY_CHANNEL);
  const [mousePosVelocity, setMousePosVelocity] = createSignal<number>(AUDIO.DEFAULT_CHANNEL_VOLUME);
  const [slotMode, setSlotMode] = createSignal<ActiveChannelsMode>(DefaultAppSettings.SLOT_MODE);
  const [maxMultiModeChannels, _setMaxMultiModeChannels] = createSignal<number>(DefaultAppSettings.AUDIO_MULTIMODE_MAX_CHANNELS);
  const [roomIsSelfHosted, setRoomIsSelfHosted] = createSignal(false);
  const [enableVelocity, setEnableVelocity] = createSignal(DefaultAppSettings.AUDIO_ENABLE_VEL);
  const [transpose, setTranspose] = createSignal<number>(AUDIO.DEFAULT_TRANSPOSE);
  const [octave, setOctave] = createSignal<number>(AUDIO.DEFAULT_OCTAVE);
  const [activeDrumKit, setActiveDrumKit] = createSignal<AudioDrumKit>();
  const [useDefaultBankWhenMissing, setUseDefaultBankWhenMissing] = createSignal(DefaultAppSettings.MIDI_USE_DEFAULT_BANK_WHEN_MISSING);
  const [synthBufferSize, setSynthBufferSize] = createSignal<number>(DefaultAppSettings.AUDIO_BUFFER_SIZE);
  const [reverbEnabled, setReverbEnabled] = createSignal(DefaultAppSettings.AUDIO_ENABLE_REVERB);
  const [useVelocityCurve, setUseVelocityCurve] = createSignal(DefaultAppSettings.AUDIO_USE_VELOCITY_CURVE);
  const [maxNoteOnTime, setMaxNoteOnTime] = createSignal<number>(DefaultAppSettings.AUDIO_MAX_NOTE_ON_TIME);
  const [maxVelocity, setMaxVelocity] = createSignal<number>(DefaultAppSettings.AUDIO_MAX_VELOCITY);
  const [minVelocity, setMinVelocity] = createSignal<number>(DefaultAppSettings.AUDIO_MIN_VELOCITY);
  const [globalVelocityPercentage, setGlobalVelocityPercentage] = createSignal<number>(DefaultAppSettings.AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE);
  const [useDefaultInstrumentWhenMissingForOtherUsers, setUseDefaultInstrumentWhenMissingForOtherUsers] = createSignal<boolean>(DefaultAppSettings.AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS);
  const [noteVolumeRelease, setNoteVolumeRelease] = createSignal<number>(DefaultAppSettings.AUDIO_MIN_VOLUME_RELEASE);
  const [outputOwnNotesToOutput, setOutputOwnNotesToOutput] = createSignal(DefaultAppSettings.AUDIO_OUTPUT_OWN_NOTES_TO_MIDI);
  const [maxPolyphony, setMaxPolyphony] = createSignal<number>(DefaultAppSettings.AUDIO_MAX_POLYPHONY);
  const [reverbLevel, setReverbLevel] = createSignal<number>(DefaultAppSettings.AUDIO_REVERB_LEVEL);
  const [reverbWidth, setReverbWidth] = createSignal<number>(DefaultAppSettings.AUDIO_REVERB_WIDTH);
  const [reverbDamp, setReverbDamp] = createSignal<number>(DefaultAppSettings.AUDIO_REVERB_DAMP);
  const [equalizerEnabled, setEqualizerEnabled] = createSignal<boolean>(DefaultAppSettings.AUDIO_ENABLE_EQUALIZER);
  const [interpolationMethod, setInterpolationMethod] = createSignal(DefaultAppSettings.AUDIO_CHANNEL_INTERPOLATION_METHOD);
  const [reverbRoomSize, setReverbRoomSize] = createSignal(DefaultAppSettings.AUDIO_REVERB_ROOMSIZE);
  const [enableStereoPanning, setEnableStereoPanning] = createSignal(false);
  const [audioWorkletNode, setAudioWorkletNode] = createSignal<AudioWorkletNode>();
  const [midiOutputOnly, setMidiOutputOnly] = createSignal<boolean>(DefaultAppSettings.AUDIO_MIDI_OUTPUT_ONLY);
  const [midiAutoFillEmptyChannelsWithDefaultInstrument, setMidiAutoFillEmptyChannelsWithDefaultInstrument] = createSignal(DefaultAppSettings.MIDI_AUTO_FILL_EMPTY_CHANNELS);
  const [isDrumChannelMuted, setIsDrumChannelMuted] = createSignal(!DefaultAppSettings.AUDIO_ENABLE_DRUM_CHANNEL);
  const [currentChannelToEdit, setCurrentChannelToEdit] = createSignal<number>();
  const [listenToProgramChanges, setListenToProgramChanges] = createSignal(DefaultAppSettings.MIDI_LISTEN_TO_PROGRAM_CHANGES);
  const [unlistenFns, setUnlistenFns] = createSignal<UnlistenFn[]>([]);
  const [lastSavedDeviceID, setLastSavedDeviceID] = createSignal<string>();
  const [synth_events_shared_buffer] = createSignal(new SharedArrayBuffer(4 * 4 + 1024 * 64));

  let synth_stream_handle = 0;

  const MAX_GAIN = 1; //COMMON.IS_DESKTOP_APP ? 1.5 : 1.5;
  let initializing = false;
  let lastSustainValue: boolean | null = null;
  let soundfontStore: UseStore | null = null;
  const _userVolumes = new Map<SocketID, number>();

  const synthPlayer = () => appService().coreService();

  const onLoadSettings = () => {
    let _appSettingsService = appSettingsService();

    let _channelMode = _appSettingsService.getSetting("SLOT_MODE", true);
    setSlotMode(
      isString(_channelMode) ? activeChannelsModeFromJSON(_channelMode.toUpperCase()) :
        ((_channelMode as ActiveChannelsMode) ?? DefaultAppSettings.SLOT_MODE)
    );
    if (COMMON.IS_DEV_MODE) console.log("SLOT_MODE", slotMode());
    setMidiAutoFillEmptyChannelsWithDefaultInstrument(_appSettingsService.getSetting("MIDI_AUTO_FILL_EMPTY_CHANNELS"));
    setUseDefaultBankWhenMissing(_appSettingsService.getSetting("MIDI_USE_DEFAULT_BANK_WHEN_MISSING"));

    let volume = _appSettingsService.getSetting<number>("VOLUME_SAVED");
    if (isNumber(volume)) {
      setVolume(volume);
    }

    setListenToProgramChanges(_appSettingsService.getSetting("MIDI_LISTEN_TO_PROGRAM_CHANGES"));
    setEnableVelocity(_appSettingsService.getSetting("AUDIO_ENABLE_VEL"));
    setReverbEnabled(_appSettingsService.getSetting("AUDIO_ENABLE_REVERB"));
    setUseVelocityCurve(_appSettingsService.getSetting("AUDIO_USE_VELOCITY_CURVE"));
    setIsDrumChannelMuted(!_appSettingsService.getSetting("AUDIO_ENABLE_DRUM_CHANNEL"));
    setMaxMultiModeChannels(_appSettingsService.getSetting("AUDIO_MULTIMODE_MAX_CHANNELS"));
    setMaxNoteOnTime(_appSettingsService.getSetting("AUDIO_MAX_NOTE_ON_TIME"));
    setNoteVolumeRelease(_appSettingsService.getSetting("AUDIO_MIN_VOLUME_RELEASE"));
    setMidiOutputOnly(_appSettingsService.getSetting("AUDIO_MIDI_OUTPUT_ONLY"));
    setMousePosSetsVelocity(_appSettingsService.getSetting("AUDIO_MOUSE_POS_SETS_VELOCITY"));
    setMaxPolyphony(_appSettingsService.getSetting("AUDIO_MAX_POLYPHONY"));
    setMaxVelocity(_appSettingsService.getSetting("AUDIO_MAX_VELOCITY"));
    setMinVelocity(_appSettingsService.getSetting("AUDIO_MIN_VELOCITY"));
    setGlobalVelocityPercentage(_appSettingsService.getSetting("AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE"));
    setSynthBufferSize(_appSettingsService.getSetting("AUDIO_BUFFER_SIZE"));
    setReverbLevel(_appSettingsService.getSetting("AUDIO_REVERB_LEVEL"));
    setEqualizerEnabled(_appSettingsService.getSetting("AUDIO_ENABLE_EQUALIZER"));
    setReverbDamp(_appSettingsService.getSetting("AUDIO_REVERB_DAMP"));
    setReverbRoomSize(_appSettingsService.getSetting("AUDIO_REVERB_ROOMSIZE"));
    setReverbWidth(_appSettingsService.getSetting("AUDIO_REVERB_WIDTH"));
    setAudioSynthEngine(_appSettingsService.getSetting("AUDIO_SYNTH_ENGINE"));
    setOutputOwnNotesToOutput(_appSettingsService.getSetting("AUDIO_OUTPUT_OWN_NOTES_TO_MIDI"));
    setUseDefaultInstrumentWhenMissingForOtherUsers(_appSettingsService.getSetting("AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS"));
    setInterpolationMethod(_appSettingsService.getSetting("AUDIO_CHANNEL_INTERPOLATION_METHOD"));

    let bands = _appSettingsService.getSetting<EqualizerBand[]>("AUDIO_EQUALIZER_BANDS");
    let preset = _appSettingsService.getSetting<EqualizerPreset>("AUDIO_EQUALIZER_PRESET");
    if (bands.length != EQUALIZER_FREQUENCIES.length || !isEqual(bands.map(x => x.freq).sort(), EQUALIZER_FREQUENCIES)) {
      let defaultBands = Equalizer.FromPreset(preset);
      bands = defaultBands.map((x) => {
        let band = bands.find(y => y.freq == x.freq);
        if (band) {
          x.gain = band.gain;
          x.curve = band.curve;
          x.rsnce = band.rsnce;
        }
        ;
        return x;
      });
      appSettingsService().saveSetting("AUDIO_EQUALIZER_BANDS", bands);
    }

    if (preset != EqualizerPreset.Custom) {
      bands = Equalizer.FromPreset(preset);
      appSettingsService().saveSetting("AUDIO_EQUALIZER_BANDS", bands);
    }

    setSynthEqualizerSettings(preset, bands);

    // Update core's slot Mode
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetSlotMode,
      slotMode: slotMode()
    }));
  };

  createEffect(() => {
    if (isSustained() == lastSustainValue) return;

    appService().coreService()?.synth_sustain?.(isSustained() ? 64 : 0);
    lastSustainValue = isSustained();
  });

  createEffect(() => {
    let _volume = (volume() + 1) ** 2;
    setMappedVolume(_volume);
    setSynthMasterVolume(_volume);
    setGlicolGainNodeVolume(_volume);
  });

  const getSoundfontCacheName = (name: string) => {
    return name.indexOf(COMMON.SOUNDFONT_CACHE_PREFIX) == 0 ? name : `${COMMON.SOUNDFONT_CACHE_PREFIX} ${name}`;
  };

  const setMaxMultiModeChannels = (value: number) => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetMaxMultiModeChannels,
      uint32Value: value
    }));
  };

  async function getSampleRate() {
    let _sampleRate = parseInt(appSettingsService().getSetting("AUDIO_SAMPLE_RATE") as any);

    if (isNaN(_sampleRate) || !_sampleRate) {
      _sampleRate = (SynthAudioContext.Instance.context as AudioContext).sampleRate;
    }

    return clamp(_sampleRate, AUDIO.MIN_SAMPLE_RATE, AUDIO.MAX_SAMPLE_RATE);
  }

  async function hasCustomSoundfont(name: string) {
    if (!soundfontStore) return Promise.resolve(false);
    return dbService().hasKey(getSoundfontCacheName(name), soundfontStore);
  }

  async function saveCustomSoundfont(name: string, data: Uint8Array) {
    if (!soundfontStore) return Promise.resolve(undefined);
    return dbService().put(name, {
      name: name,
      data: data
    }, soundfontStore);
  }

  async function deleteCustomSoundfont(name: string) {
    if (!soundfontStore) return Promise.resolve(undefined);
    let key = getSoundfontCacheName(name);
    if (!(await hasCustomSoundfont(key))) return Promise.resolve(undefined);
    return dbService().deleteKey(key, soundfontStore);
  }

  const clearWebSoundfontCache = async () => {
    if (soundfontStore) await dbService().clear(soundfontStore);
  };

  const isSustainActive = () => sustainPress() || sustained() || synthSustain();

  function toggleSustained() {
    setSustained(v => !v);
    setSustainPress(false);
    setSynthSustain(false);
  }

  const getAudioChannels = async () => {
    let _channels = await appService().coreService()?.get_synth_audio_channels() || [];
    setChannels(_channels.map(x => AudioChannel.decode(x)));
  };

  const decodeSynthEvent = (data: Uint8Array): PianoRhythmSynthEvent | undefined => {
    try {
      if (!data || data.length == 0) return;
      return decode(data) as PianoRhythmSynthEvent;
    } catch (ex) {
      if (COMMON.IS_DEV_MODE) console.error("DECODE EVENT ERROR", typeof data, data, ex);
      return undefined;
    }
  };

  async function load_desktop_synth_events() {
    unlistenFns().forEach(x => x());
    setUnlistenFns([]);

    let synthEvents = new BroadcastChannel(CHANNELS.PIANORHYTHM_SYNTH_EVENTS);
    const midiMessageUnlisten = await listen<Array<number>>("midi_message", (evt) => {
      let data = new Uint8Array(evt.payload);
      let output = decodeSynthEvent(data);
      if (output) synthEvents?.postMessage(output);
    });
    setUnlistenFns(x => [...x, midiMessageUnlisten]);

    let totalSize = -1;

    // Downloading...
    const sfDownloadStart = await listen("soundfont_download_start", () => {
      soundfontLoadingNotify("Downloading Soundfont...");
    });
    setUnlistenFns(x => [...x, sfDownloadStart]);

    const sfDownloadComplete = await listen("soundfont_download_complete", () => {
      soundfontLoadingNotify("Soundfont download complete!");
    });
    setUnlistenFns(x => [...x, sfDownloadComplete]);

    const sfLoadProgressUnlisten = await listen<number>("soundfont_download_progress", (evt) => {
      let progress = Math.round(evt.payload);
      if (progress == -1) {
        let details = Soundfonts.get(loadingSoundfontFileName() ?? "");
        if (details) totalSize = convertSize(details.size);
      }

      if (totalSize > 0) {
        // _setProgress?.(
        //   Math.round(clamp((progress / totalSize) * 100, 0, 100))
        // );
      } else {
        // _setProgress?.(progress);
      }
    });
    setUnlistenFns(x => [...x, sfLoadProgressUnlisten]);

    // Caching...
    const sfSaveCacheStart = await listen("soundfont_save_cache_start", () => {
      soundfontLoadingNotify("Caching Soundfont...");
    });
    setUnlistenFns(x => [...x, sfSaveCacheStart]);

    const sfSaveCacheFinish = await listen("soundfont_save_cache_finish", () => {
      soundfontLoadingNotify("Soundfont cached!");
      totalSize = -1;
    });
    setUnlistenFns(x => [...x, sfSaveCacheFinish]);

    const sfSaveCacheFail = await listen<string>("soundfont_save_cache_fail", () => {
      soundfontLoadingNotify("Failed to cache soundfont.");
    });
    setUnlistenFns(x => [...x, sfSaveCacheFail]);

    // Parsing...
    const sfLoadStart = await listen("soundfont_load_start", () => {
      soundfontLoadingNotify("Parsing Soundfont...");
    });
    setUnlistenFns(x => [...x, sfLoadStart]);

    const sfLoadFinish = await listen("soundfont_load_finish", () => {
      soundfontLoadingNotify(`Soundfont loaded: ${loadedSoundfontName()}`, undefined, 3000, "success");
    });
    setUnlistenFns(x => [...x, sfLoadFinish]);

    const sfLoadFail = await listen("soundfont_load_fail", () => {
      soundfontLoadingFailedNotify("Failed to load soundfont.");
    });
    setUnlistenFns(x => [...x, sfLoadFail]);
  }

  async function load_desktop_synth(_sampleRate: number, _synth_engine?: number) {
    //Dispose any existing synthesizers
    try {
      await invoke("dispose");
    } catch {
    }

    let deviceId = appSettingsService().getLocalStorage("lastSavedAudioDevice") as string;

    try {
      await invoke("init_soundfont", {
        sampleRate: _sampleRate,
        backendSynth: _synth_engine,
        deviceId: deviceId
      });
      setLastSavedDeviceID(deviceId);
      await load_desktop_synth_events();
    } catch (ex) {
      logError(`Something went wrong trying to initialize the synthesizer. Error: ${ex}`);
    }
  }

  const initialize = async () => {
    console.info("Initializing audio service...");

    if (initialized()) {
      if (appSettingsService().isDebugMode()) logDebug("[AudioService] Already initialized...");
      return false;
    }

    if (initializing) {
      if (appSettingsService().isDebugMode()) logDebug("[AudioService] Already initializing...");
      return false;
    }

    initializing = true;

    try {
      const initNotification = (
        description: string, type?: string, duration = 10_000
      ) => {
        NotificationService.show({
          id: IDS.AUDIO_INITIALIZATION,
          loading: type != undefined,
          // @ts-ignore
          type: type,
          closable: false,
          title: "Audio Engine",
          duration: duration ?? 10_000,
          description: description
        });
        logDebug(`[AudioService] ${description}`);
      };

      if (COMMON.IS_DEV_MODE) logDebug("[AudioService] Getting sample rate...");
      let _sampleRate = await getSampleRate();
      setSampleRate(_sampleRate);
      logDebug(`[AudioService] Sample rate: ${_sampleRate}`);

      let synth_engine_idx = Object.values(AudioSynthesizerEngine).indexOf(audioSynthEngine()) + 1;
      setLoadedAudioSynthEngine(audioSynthEngine());
      initNotification(`Initializing audio engine: ${audioSynthEngine()}`);

      setSubscriptions([
        ...subscriptions(),
        appService().appStateEffects.listen(async (effect) => {
          switch (effect.action) {
            case AppStateEffects_Action.SoundfontPresetsLoaded: {
              let fileName = loadingSoundfontFileName() || "";

              if (fileName != null) {
                try {
                  let firstTimeLoad = (instruments()).length == 0;
                  onSoundfontPresetsLoaded(
                    fileName.replaceAll(COMMON.SOUNDFONT_CACHE_PREFIX, ""),
                    effect.instrumentsList?.instruments || []
                  );

                  if (firstTimeLoad) {
                    webMidiService().refreshMidiInputsState();
                    webMidiService().refreshMidiOutputsState();
                  }
                } catch (ex) {
                  // rejectLoadSoundfont("Failed to load soundfont presets.");
                  logError(`Failed to load soundfont presets. Error: ${ex}`);
                }
              } else {
                // rejectLoadSoundfont("Invalid soundfont file name.");
              }
              break;
            }
            case AppStateEffects_Action.RemoveUser: {
              if (!effect.socketId) return;
              setHashedUsers(v => v.filter(x => x.socketID != effect.socketId));
              break;
            }
            case AppStateEffects_Action.SynthChannelUpdated: {
              if (!effect.audioChannel) return;
              setChannels(channels().map(x => {
                return x.channel == effect.audioChannel!.channel ? effect.audioChannel! : x;
              }));
              break;
            }
            case AppStateEffects_Action.SetSlotMode: {
              if (effect.slotMode != null) {
                setSlotMode(effect.slotMode);
                appSettingsService().saveSetting("SLOT_MODE", effect.slotMode);
              }
              break;
            }
            case AppStateEffects_Action.SetPrimaryChannel: {
              if (effect.uint32Value != null) setPrimaryChannel(effect.uint32Value);
              break;
            }
            case AppStateEffects_Action.UsersSet: {
              let users = effect.clientSideUserDtoList?.list;
              onSetHashedUsers(users || []);
              break;
            }
            case AppStateEffects_Action.AddUser:
            case AppStateEffects_Action.UpdateUser: {
              onSetHashedUser(effect.clientSideUserDto);
              break;
            }
            case AppStateEffects_Action.EqualizerEnabled: {
              if (effect.boolValue != null) setEqualizerEnabled(effect.boolValue);
              break;
            }
            case AppStateEffects_Action.ReverbEnabled: {
              if (effect.boolValue != null) setReverbEnabled(effect.boolValue);
              break;
            }
            case AppStateEffects_Action.AudioApplyVelocityCurve: {
              if (effect.boolValue != null) setUseVelocityCurve(effect.boolValue);
              break;
            }
            case AppStateEffects_Action.DrumChannelIsMuted: {
              if (effect.boolValue != null) setIsDrumChannelMuted(effect.boolValue);
              break;
            }
            case AppStateEffects_Action.ListenToProgramChanges: {
              if (effect.boolValue != null) setListenToProgramChanges(effect.boolValue);
              break;
            }
            case AppStateEffects_Action.MaxMultiModeChannels: {
              if (effect.uint32Value != null) _setMaxMultiModeChannels(effect.uint32Value);
              break;
            }
          }
        }),
        appService().appStateEvents.listen(async (event) => {
          switch (event) {
            case AppStateEvents.AddedClientSynthUser: {
              NotificationService.hide(IDS.AUDIO_INITIALIZATION);
              updateClientSynthSettings();
              setClientAdded(true);

              synthPlayer()?.get_hash_socket_id(appService().getSocketID())
                .then(hash => {
                  audioWorkletNode()?.port.postMessage({
                    type: "set-client-socket-id",
                    socketID: hash ?? null
                  });
                });

              break;
            }
            case AppStateEvents.AppStateReset: {
              onDisconnect();
              break;
            }
            case AppStateEvents.AudioStateInitialized:
            case AppStateEvents.SynthSoundfontLoaded:
            case AppStateEvents.InstrumentsLoaded: {
              try {
                await getAudioChannels();
              } catch (ex) {
                logError(`Failed to get synth audio channels. Error: ${ex}`);
                NotificationService.show({
                  type: "danger",
                  title: "Failed to get synth audio channels.",
                  description: `Please try reloading the app.`,
                  duration: 10_000
                });
              }
              break;
            }
          }
        })]);

      if (COMMON.IS_DESKTOP_APP) {
        initNotification(`Loading synthesizer...`);
        await load_desktop_synth(_sampleRate, synth_engine_idx);
        initNotification(`Synthesizer ready.`);
      } else {
        let coreService_wasm = appService().coreService() as any;

        if (!coreService_wasm) {
          logError(`[AudioService] Failed to initialize audio service: Core wasm not loaded.`);
          NotificationService.show({
            id: IDS.AUDIO_INITIALIZATION,
            title: "Failed to initialize audio",
            description: "Core wasm not loaded.",
            type: "danger",
            duration: 5000
          });
          return false;
        }

        coreService_wasm.create_synth(synth_engine_idx, _sampleRate);
        initNotification("Synthesizer instance created.", "success");

        if (!appSettingsService().getSetting("AUDIO_USE_WORKLET") || !canCreateSharedArrayBuffer()) {
          logDebug(`[AudioService] Creating synth on main thread...`);
          SynthAudioContext.Dispose();
          synth_stream_handle = coreService_wasm.create_synth_stream(_sampleRate);
          initNotification("Audio context created on main thread.", "success");
        } else if (!audioWorkletNode()) {
          logDebug(`[AudioService] Creating audio worklet node...`);

          let workletNode: AudioWorkletNode | undefined = undefined;
          const audioContext = SynthAudioContext.Instance.context as AudioContext;
          await audioContext.audioWorklet.addModule(new URL(WasmProcessorUrl, import.meta.url));
          workletNode = await coreService_wasm.midi_io_start(audioContext, 2);

          if (workletNode == undefined || workletNode.port == null)
            throw new Error("Worklet node is not available.");

          let sharedBuffer: SharedArrayBuffer = coreService_wasm.init_audio_scheduler_global(_sampleRate);
          coreService_wasm.init_audio_scheduler(_sampleRate, workletNode);

          appService().canvasWorker()?.postMessage({
            event: "load-audio-scheduler",
            buffer: sharedBuffer,
            sampleRate: _sampleRate
          });

          appService().webMidiConnectionEvents.listen((event) => {
            workletNode.port.postMessage({
              type: "web-midi-event",
              event: event
            });
          });

          workletNode.port.postMessage({
            type: "set_synth_events_shared_buffer",
            buffer: synth_events_shared_buffer()
          })

          workletNode.port.onmessage = (messageEvent) => {
            let event = messageEvent.data;
            if (!event) return;

            switch (event.type) {
              case "worklet-initialized": {
                initNotification("Audio worklet initialized.", "success", 5000);

                // Worklet initialization complete - SynthAction dispatch is now handled by initialization service
                logInfo(`[AudioService] Worklet initialized successfully`);
                break;
              }
              case "worklet-failed": {
                NotificationService.show({
                  type: "danger",
                  title: "Audio Worklet Failed",
                  persistent: true,
                  description: `Please try reloading the app. The following error occurred: ${event.error}`
                });

                setWorkletFailedToLoad(true);
                break;
              }
            }
          };

          setAudioWorkletNode(workletNode);
        }
        // if (COMMON.IS_DEV_MODE) (window as any)["synth"] = webaudio_wasmSynth;
      }

      initializeSoundfontStore();
      if (soundfontStore) initNotification(`Soundfont store created: ${soundfontStoreName}`);

      if (clientAdded()) {
        NotificationService.hide(IDS.AUDIO_INITIALIZATION);
      } else {
        initNotification("Now waiting for synth client setup... [To receive the 'AddedClientSynthUser' event]");
      }

      // SynthAction dispatch is now handled by the initialization service
      // No need to listen for ClientLoaded event here anymore

      setInitialized(true);
      initializing = false;
      if (COMMON.IS_STAGING || COMMON.IS_DEV_MODE) console.info("Audio service initialize method complete.");
      return true;
    } catch (e) {
      NotificationService.show({
        id: IDS.AUDIO_INITIALIZATION,
        title: "Failed to initialize audio",
        description: `Error: ${e}`,
        type: "danger",
        duration: 10_000
      });
      logError(`[AudioService] Failed to initialize audio service: ${e}`);
      console.error(e);
    }

    logInfo("Audio service initialized.");

    initializing = false;
    return false;
  };

  const onDisconnect = () => {
    NotificationService.hide(IDS.LOADING_SOUNDFONT);

    batch(() => {
      setClientAdded(false);
      setLoadedSoundfont(null);
      setLoadedSoundfontName(undefined);
      setLoadedSoundfontIsCustom(false);
      // setLoadedDrumSoundfont(null);
      setInstruments([]);
      setPrograms([]);
      setMappedVolume(-1);
      setLastSavedVolume(-1);
      setVolumeMuted(false);
      setRoomIsSelfHosted(false);
      setSynthSustain(false);
      setSustained(false);
      unlistenFns().forEach(x => x());
      setUnlistenFns([]);
    });
  };

  /**
   * Dispatches the SynthAction to add the client to the synthesis engine
   * This is a critical step that must happen after both synth engine creation and client socket ID availability
   */
  const dispatchAddClientSynthAction = async (): Promise<void> => {
    const socketId = appService().getSocketID();
    if (!socketId) {
      throw new Error("Client socket ID not available for SynthAction dispatch");
    }

    if (!appService().coreService()) {
      throw new Error("Core service not available for SynthAction dispatch");
    }

    if (!initialized()) {
      throw new Error("Audio service not initialized for SynthAction dispatch");
    }

    logInfo(`[AudioService] Dispatching SynthAction to add client with socket ID: ${socketId}`);

    // Dispatch the SynthAction to add client to synth engine
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SynthAction,
      audioSynthAction: AudioSynthActions.create({
        action: AudioSynthActions_Action.AddClient,
        socketId: socketId
      })
    }));

    logInfo(`[AudioService] SynthAction dispatched successfully`);
  };

  const initializeSoundfontStore = () => {
    soundfontStore = dbService().createStore(soundfontStoreDbName, soundfontStoreName);
  };

  const onSetHashedUsers = (_users: ClientSideUserDto[]) => {
    if (synthPlayer()) {
      Promise
        .allSettled(_users.map(x => synthPlayer()?.get_hash_socket_id(x.socketID)))
        .then(results => {
          let usersResult = results.map((x, idx) => ({
            socketID: _users[idx]?.socketID,
            hash: x.status == "fulfilled" ? x.value : null
          }));
          setHashedUsers(uniqBy(usersResult.filter(x => x.hash != null) as SocketIDWitHash[], x => x.socketID));
        });
    }
  };

  const onSetHashedUser = (user?: ClientSideUserDto) => {
    if (!user) return;

    synthPlayer()?.get_hash_socket_id(user.socketID).then(hash => {
      if (user && hash != null) setHashedUsers(v => uniqBy([...v, {
        socketID: user!.socketID,
        hash
      }], x => x.socketID));
    });
  };

  function onSoundfontPresetsLoaded(file: string, instruments: Instrument[]) {
    if (!file) return;

    let programs = instruments.map(x =>
      ({ id: x.preset, bank: x.bank, name: x.name })
    );

    updateClientSynthSettings();
    setPrograms(programs);
    setInstruments(instruments);
    setLoadedSoundfont(file);
    let fileName = getFileNameFromPath(file);
    setLoadedSoundfontName(fileName);

    webMidiService().refreshMidiInputsState();
    webMidiService().refreshMidiOutputsState();
  }

  function setSynthTranspose(value: number) {
    if (value == null || !isNumber(value)) return;
    synthPlayer()?.set_transpose_offset(value);
  }

  function setSynthOctave(value: number) {
    if (value == null || !isNumber(value)) return;
    synthPlayer()?.set_octave_offset(value);
  }

  function setSynthMaxPolyphony(value: number) {
    if (value == null) return;
    synthPlayer()?.synth_set_polyphony(value);
  }

  function setSynthInterpolationMethod(value: SynthInterpolationMethod) {
    if (value == null) return;
    synthPlayer()?.set_interpolation_method(value);
  }

  function setSynthMasterVolume(volume: number) {
    if (volume == null) return;

    let gainValue = map(volume / AUDIO.MAX_VOLUME_RAW, 0, 1, 0, MAX_GAIN);
    synthPlayer()?.synth_set_gain(gainValue);
  }

  function setGlicolGainNodeVolume(volume: number) {
    if (volume == null) return;

    // let glicolGainValue = map(volume / AUDIO.MAX_VOLUME_RAW, 0, 1, 0, 0.7);
    // glicolGainNode()?.gain.setValueAtTime(glicolGainValue, glicolGainNode()!.context.currentTime);
  }

  function setSynthReverb(value: boolean) {
    if (value == null) return;
    synthPlayer()?.synth_set_reverb(value);
  }

  function setSynthVolumeRelease(value: number) {
  }

  const onSetEqualizerEnabled = (value: boolean) => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetEqualizerEnabled,
      boolValue: value
    }));
  };

  function setSynthEqualizerSettings(preset: EqualizerPreset = Equalizer.DEFAULT_PRESET, custom_bands: EqualizerBand[] = []) {
    if (preset == null) preset = Equalizer.DEFAULT_PRESET;
    onSetEqualizerEnabled(true);
    let targetBands = preset == EqualizerPreset.Custom ? (custom_bands ?? []) : Equalizer.FromPreset(preset);
    targetBands.forEach(x => {
      synthPlayer()?.set_equalizer_band(x.chnl, x.curve, x.freq, x.rsnce, x.gain ?? 0);
    });
  }

  const batchSynthSettings = () => {
    if (mappedVolume() >= 0) setSynthMasterVolume(mappedVolume());
    setSynthReverb(reverbEnabled());
    setSynthVolumeRelease(noteVolumeRelease());
    setSynthMaxPolyphony(maxPolyphony());
    setSynthOctave(octave());
    setSynthTranspose(transpose());
    setSynthInterpolationMethod(interpolationMethod());

    synthPlayer()?.synth_set_reverb_level(reverbLevel());
    synthPlayer()?.synth_set_reverb_damp(reverbDamp());
    synthPlayer()?.synth_set_reverb_room_size(reverbRoomSize());
    synthPlayer()?.synth_set_reverb_width(reverbWidth());
    synthPlayer()?.set_disable_velocity_for_client(!enableVelocity());
    synthPlayer()?.set_max_note_on_time(maxNoteOnTime());
    synthPlayer()?.set_max_velocity(maxVelocity());
    synthPlayer()?.set_min_velocity(minVelocity());
    synthPlayer()?.set_max_multi_mode_channels(maxMultiModeChannels());
    synthPlayer()?.set_midi_output_only(midiOutputOnly());
    synthPlayer()?.set_use_default_instrument_when_missing_for_other_users(useDefaultInstrumentWhenMissingForOtherUsers());
    synthPlayer()?.synth_set_auto_fill_channels_with_default(midiAutoFillEmptyChannelsWithDefaultInstrument());
  };

  const updateClientSynthSettings = debounce(() => {
    batchSynthSettings();
  });

  createEffect(() => {
    batch(batchSynthSettings);
  });

  function getCustomSoundfont(name: string, useWorker = true, fallbackViaService = true): Promise<CustomSoundfontDB | undefined> {
    return new Promise((resolve) => {
      if (!soundfontStore) {
        if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Soundfont store not available.`);
        return resolve(undefined);
      }

      const loadViaDbService = () => {
        if (!fallbackViaService) return resolve(undefined);
        if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Loading custom soundfont: ${name} via main thread.`);

        dbService().get<CustomSoundfontDB>(name, soundfontStore!)
          .then(resolve)
          .catch(() => resolve(undefined))
          .finally(() => {
            if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Loaded custom soundfont: ${name} via main thread.`);
          });
      };

      if (useWorker) {
        if (!("revokeObjectURL" in URL)) {
          loadViaDbService();
          return;
        }

        if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Loading custom soundfont: ${name} via worker.`);
        type WorkerInput = { fileName: string, dbName: string, storeName: string; };

        // Build a worker from an anonymous function body
        let blobURL = URL.createObjectURL(new Blob(['(',
          function () {
            self.onmessage = (event) => {
              const onClose = (data: any) => {
                self.postMessage(data);
                self.close();
              };

              try {
                let eventData = event.data as WorkerInput;
                let idbx = self.indexedDB;

                if (idbx && eventData) {
                  const request = idbx.open(eventData.dbName);
                  let onError = () => onClose(null);

                  request.onerror = onError;
                  request.onblocked = onError;
                  request.onupgradeneeded = onError;

                  request.onsuccess = () => {
                    let db = request.result;
                    let tran = db.transaction(eventData.storeName, "readonly");
                    let objectStore = tran.objectStore(eventData.storeName);
                    let storeGetResult = objectStore.get(eventData.fileName);

                    storeGetResult.onerror = onError;
                    storeGetResult.onsuccess = (data) => {
                      let target = data.target as any;
                      onClose(target.result);
                    };
                  };
                } else {
                  onClose(null);
                }
              } catch (ex) {
                console.error(`[CustomSoundfontWorker] Error`, ex);
                onClose(null);
              }
            };
          }.toString(),
          ')()'], { type: 'application/javascript' })), worker = new Worker(blobURL);

        worker.postMessage({
          fileName: name,
          dbName: soundfontStoreDbName,
          storeName: soundfontStoreName
        } as WorkerInput);

        worker.onmessage = async (event) => {
          let result = event.data;
          if (result) {
            if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Loaded custom soundfont: ${name} via worker.`);
            return resolve(result);
          }

          if (!soundfontStore) return resolve(undefined);

          // final attempt
          if (appSettingsService().isDebugMode()) logDebug(`[getCustomSoundfont] Failed to load custom soundfont: ${name} via worker.`);

          loadViaDbService();
        };

        URL.revokeObjectURL(blobURL);
      } else {
        loadViaDbService();
      }
    });
  }

  const soundfontLoadingNotify = (description: string, title?: string, duration?: number, type?: string) => {
    let props = {
      id: IDS.LOADING_SOUNDFONT,
      loading: type == undefined,
      persistent: true,
      title: title ?? "Audio Engine",
      description: description,
      duration: duration ?? 10_000
    };

    if (type != undefined) (props as any).type = type;
    NotificationService.show(props);
    logDebug(`[AudioService - Load Soundfont] ${description}`);
  };

  function soundfontLoadingFailedNotify(error: JSXElement) {
    NotificationService.hide(IDS.LOADING_SOUNDFONT);

    NotificationService.show({
      type: "danger",
      title: "Soundfont Load - Failed",
      description: error,
      duration: 10_000,
      closable: true
    });
  }

  async function downloadSoundfont(fileName: string, onProgress?: (progress: number) => void) {
    try {
      let getResponse = () => fetch(`${COMMON.ASSETS_URL}/soundfonts/${fileName}`);

      const getByBuffer = async () => {
        let response = await getResponse();
        let buffer = await response.arrayBuffer();
        return new Uint8Array(buffer);
      };

      if (onProgress) {
        const response = await getResponse();
        const reader = response.body?.getReader();
        if (!reader) return undefined;

        let contentLength = +(response.headers.get('Content-Length') ?? 0);

        if (contentLength == 0) {
          let details = Soundfonts.get(fileName ?? "");
          if (details) contentLength = convertSize(details.size);
        }

        if (contentLength == 0) {
          return await getByBuffer();
        }

        // Step 3: read the data
        let receivedLength = 0; // received that many bytes at the moment
        let chunks = []; // array of received binary chunks (comprises the body)
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            break;
          }

          chunks.push(value);
          receivedLength += value.length;

          let percentageDownloaded = Math.round((receivedLength / contentLength) * 100);
          onProgress(Math.min(percentageDownloaded, 100));
        }

        // Step 4: concatenate chunks into single Uint8Array
        let chunksAll = new Uint8Array(receivedLength); // (4.1)
        let position = 0;
        for (let chunk of chunks) {
          chunksAll.set(chunk, position); // (4.2)
          position += chunk.length;
        }

        return chunksAll;

      } else {
        return await getByBuffer();
      }
    } catch {
      return undefined;
    }
  }

  const downloadSoundfontDataWeb = async (props: {
    isCustomSoundfont: boolean,
    file: string,
  }) => {
    let data: CustomSoundfontDB | undefined;
    let { file, isCustomSoundfont } = props;

    if (!file) throw new Error("Invalid soundfont file name.");

    if (isCustomSoundfont) {
      if (appSettingsService().isDebugMode()) logDebug(`[loadSoundfont] Checking for custom soundfont in db.`);
      data = await getCustomSoundfont(file);
      if (appSettingsService().isDebugMode()) logDebug(`[loadSoundfont] ${data == null ? `No cache was found for '${file}'.` : `Cached file was found for '${file}'. Now loading.`}.`);
      if (data == null) throw (new Error(`Data not found in db.`));
    } else {
      //Check for a cached version
      soundfontLoadingNotify(`Checking for cached soundfont: ${file}`);
      if (appSettingsService().isDebugMode()) logDebug(`[loadSoundfont] Checking for cached soundfont: ${file} in db.`);
      data = await raceTimeout(
        until(() => getCustomSoundfont(getSoundfontCacheName(file), true, false)),
        60_000,
        false
      );

      let message = `${data == null ? `No cache was found for '${file}'. \nNow checking without prefix key: CACHED` : `Cached file was found for '${file}'. Now loading.`}.`;
      if (appSettingsService().isDebugMode()) logDebug(`[loadSoundfont] ${message}`);
      soundfontLoadingNotify(message);

      if (data == null) data = await raceTimeout(
        until(() => getCustomSoundfont(file.trim().replace("CACHED:", ""), true, false)),
        60_000,
        false
      );
    }

    // Attempt to download the soundfont if not found in cache
    if (!data) {
      if (appSettingsService().isDebugMode()) logDebug(`[loadSoundfont] Fetching soundfont: ${file} from CDN since it was not found in the local cache.`);

      let sfURL = `${COMMON.ASSETS_URL}/soundfonts/${file}`;

      try {
        NotificationService.show({
          id: IDS.LOADING_SOUNDFONT,
          loading: true,
          title: "Downloading Soundfont",
          description: `Downloading soundfont: ${file}`,
          duration: 10_000
        });

        let response = await downloadSoundfont(file, (progress) => {
          NotificationService.show({
            id: IDS.LOADING_SOUNDFONT,
            loading: true,
            title: "Downloading Soundfont",
            description: `Progress: (${progress}%)`,
            duration: 10_000
          });
        });
        if (!response) throw new Error(`Failed to fetch soundfont: ${sfURL}`);

        return new Uint8Array(response);
      } catch (ex) {
        console.error("[AudioService][Fetch Soundfont URL]", ex);
        throw new Error(`Failed to fetch soundfont: ${sfURL}`);
      }
    }

    if (data == null) throw new Error("No data");

    if (data.data.length == 0) throw new Error("No data");

    return data.data;
  };

  const onLoadSoundfont = async (fileName: string, _: any, saveFile: any, data: Uint8Array | null) => {
    if (data) {
      if (typeof fileName == "string" && saveFile) {
        try {
          if (!await hasCustomSoundfont(fileName)) await saveCustomSoundfont(getSoundfontCacheName(fileName), data);
        } catch (ex) {
          logError(`[Soundfont Cache] Error: ${ex}`);
        }
      }

      // @ts-ignore
      data = null;
    } else {
      throw new Error("Invalid soundfont data.");
    }
  };

  const onErrorEvent = (error: ErrorEvent) => {
    console.error(`[AudioService] Error: ${error.message}`);
  };

  const onUnhandledRejection = (reject: (reason?: string) => void) => (promiseRejectionEvent: PromiseRejectionEvent) => {
    let stack = promiseRejectionEvent.reason?.stack;
    if (stack?.includes("RuntimeError: unreachable") && stack?.includes("pianorhythm_core.wasm.soundfont")) {
      reject("Failed to parse soundfont.");
    }
  };

  const processSoundfontDataWeb = async (props: {
    data: Uint8Array,
    file: string,
    cacheFile: boolean,
  }) => {
    let { data, cacheFile, file } = props;

    soundfontLoadingNotify(`Parsing soundfont: ${file}`, undefined, Infinity);
    let cacheFileWeb = appSettingsService().getSetting<boolean>("AUDIO_CACHE_SOUNDFONTS_WEB") && COMMON.IS_WEB_APP && cacheFile;
    await onLoadSoundfont(file, false, cacheFileWeb, data);
    appService().coreService()?.synth_load_soundfont(data);

    let sub: VoidFunction = () => {
    };
    let loadSoudfontViaWorker = new Promise(async (resolve, reject) => {
      sub = appService().appStateEffects.listen(async (effect) => {
        switch (effect.action) {
          case AppStateEffects_Action.SoundfontPresetsLoaded: {
            sub?.();
            resolve(true);
            break;
          }
          case AppStateEffects_Action.SynthSoundfontFailedToLoad: {
            sub?.();
            reject("Failed to load soundfont.");
            break;
          }
        }
      });
    });

    try {
      await raceTimeout(loadSoudfontViaWorker, 60_000 * 5, true, "Failed to load soundfont via worker.");
    } catch (ex) {
      throw ex;
    } finally {
      sub?.();
      NotificationService.hide(IDS.LOADING_SOUNDFONT);
    }

    soundfontLoadingNotify(`Synth loaded: ${file}`);
  };

  const downloadSoundfontDesktop = async (props: {
    file: string,
    isCustomSoundfont: boolean,
  }) => {
    let { file, isCustomSoundfont } = props;

    if (appSettingsService().getSetting("DESKTOP_SAVE_SOUNDFONTS")) {
      try {
        // Make sure soundfonts folder is created
        let storedSoundfontsFolderExists = await (exists(COMMON.DESKTOP_SOUNDFONTS_FOLDER, { baseDir: BaseDirectory.AppData }) as any as Promise<boolean>);
        if (!storedSoundfontsFolderExists) {
          logInfo(`Creating soundfonts folder in app data.`);
          await mkdir(COMMON.DESKTOP_SOUNDFONTS_FOLDER, { baseDir: BaseDirectory.AppData, recursive: true });
        }

        // Check if the file exists
        let fileName = getFileNameFromPath(file);
        let soundfontExists = await (exists(`${COMMON.DESKTOP_SOUNDFONTS_FOLDER}\\${fileName}`, { baseDir: BaseDirectory.AppData }) as any as Promise<boolean>);

        if (soundfontExists) {
          logInfo(`Cached soundfont exists for '${fileName}'`);
          const appDataDirPath = await appDataDir();
          file = `${appDataDirPath}\\${COMMON.DESKTOP_SOUNDFONTS_FOLDER}\\${fileName}`;
          return { file, isCachedLocally: true };
        }
      } catch (ex) {
        logError(`[Process soundfont] ${ex}`);
        throw ("Something went wrong. Failed to process soundfont.");
      }
    }

    if (!isSoundfontFilePath_windows(file) && !isCustomSoundfont) {
      file = `${COMMON.ASSETS_URL}/soundfonts/${file}`;
    }

    return { file, isCachedLocally: false };
  };

  const processSoundfontDataDesktop = async (props: {
    isCustomSoundfont: boolean,
    isCachedLocally: boolean,
    file: string,
  }) => {
    let { file, isCachedLocally, isCustomSoundfont } = props;

    let isLocalFile = COMMON.IS_DESKTOP_APP && (isCustomSoundfont || isCachedLocally);
    let cacheFileDesktop = COMMON.IS_DESKTOP_APP && appSettingsService().getSetting<boolean>("DESKTOP_SAVE_SOUNDFONTS");

    await synthPlayer()?.load_soundfont(file, isLocalFile, cacheFileDesktop);
  };

  function loadSoundfont(_file: string, isCustomSoundfont = false, cacheFile = false) {
    return new Promise<void>(async (resolve, reject) => {
      let file = _file?.trim();
      if (appSettingsService().isDebugMode()) console.debug(`[loadSoundfont] Loading soundfont: ${file} | Currenty loading:`, loadingSoundfont());

      if (workletFailedToLoad()) return (reject("Audio Worklet failed to load. Please try reloading the app."));
      if (isEmpty(file)) return (reject("Invalid soundfont filepath."));
      if (!synthPlayer()) return (reject("Synth Player not initialized."));
      if (loadingSoundfont()) return (reject("Please wait for the current soundfont to load..."));
      if (loadedSoundfont() == file) return (reject(`Soundfont: ${file} already loaded.`));
      if (!isCustomSoundfont && file.indexOf(CUSTOM_SOUNDFONT_PREFIX) == 0) isCustomSoundfont = true;

      soundfontLoadingNotify(`Loading soundfont: ${file}`);
      setLoadingSoundfontFileName(file);
      setLoadingSoundfont(true);
      setLoadedSoundfontIsCustom(isCustomSoundfont);

      const onRejection = (reason?: string) => {
        soundfontLoadingFailedNotify(reason);
        setLoadingSoundfont(false);
        reject(reason);
        window.removeEventListener("unhandledrejection", onUnhandledRejection(onRejection));
      };

      try {
        if (COMMON.IS_WEB_APP) {
          window.addEventListener("unhandledrejection", onUnhandledRejection(onRejection));

          let data = await raceTimeout(
            until(() => downloadSoundfontDataWeb({ isCustomSoundfont, file })),
            60_000 * 30,
            true,
            "Failed to fetch soundfont data."
          );
          await processSoundfontDataWeb({ data, file, cacheFile });
          await raceTimeout(
            until(() => loadedSoundfontName()),
            60_000,
            true,
            "Failed to load soundfont presets."
          );
          soundfontLoadingNotify(`Soundfont loaded: ${file}`, undefined, 3000, "success");
          // @ts-ignore
          data = null;
        } else if (COMMON.IS_DESKTOP_APP) {
          //Reload synth if device ID has changed
          if (lastSavedDeviceID() != appSettingsService().getLocalStorage("lastSavedAudioDevice") as string) {
            // let _sampleRate = await getSampleRate();
            // await load_desktop_synth(_sampleRate);
          }

          //Reload listening events if empty
          if (unlistenFns().length == 0) await load_desktop_synth_events();

          if (!isCustomSoundfont && isSoundfontFilePath_windows(file)) isCustomSoundfont = true;
          cacheFile = false;

          let result = await downloadSoundfontDesktop({ file, isCustomSoundfont });
          await processSoundfontDataDesktop({
            file: result.file,
            isCustomSoundfont,
            isCachedLocally: result.isCachedLocally
          });
        }
      } catch (ex) {
        NotificationService.hide(IDS.LOADING_SOUNDFONT);
        setLoadingSoundfont(false);
        return reject(ex);
      } finally {
        NotificationService.hide(IDS.LOADING_SOUNDFONT);
        setLoadingSoundfont(false);
      }

      resolve();
    });
  }

  const resetChannelsToDefault = () => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.ResetAudioChannelsToDefault
    }));
  };

  const [_instrumentsCache, _setInstrumentsCache] = createSignal<Instrument[]>([]);

  const getInstruments = async (): Promise<Instrument[]> => {
    try {
      if (_instrumentsCache().length > 0) return _instrumentsCache();
      return [];
    } catch {
    }
    return [];
  };

  const [instruments, { refetch: refetchInstruments }] = createResource<Instrument[]>(() => {
    return getInstruments();
  }, { initialValue: [] });

  const setInstruments = (instruments: Instrument[]) => {
    _setInstrumentsCache(instruments);
    refetchInstruments();
  };

  const setChannelVolume = (channel: number, value: number, _socketID?: string) => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SynthAction,
      audioSynthAction: AudioSynthActions.create({
        action: AudioSynthActions_Action.SetChannelVolume,
        sourceSocketID: _socketID,
        channelWithUint32: ChannelWithUint32.create({
          channel: channel,
          uint32Value: value
        })
      })
    }));
  };

  const setChannelPan = (channel: number, value: number, _socketID?: string) => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SynthAction,
      audioSynthAction: AudioSynthActions.create({
        action: AudioSynthActions_Action.SetChannelPan,
        sourceSocketID: _socketID,
        channelWithUint32: ChannelWithUint32.create({
          channel: channel,
          uint32Value: value
        })
      })
    }));
  };

  const memoizesetChannelParamResolver = (channel: number, value: number, _socketID?: string | boolean) => `${channel}_${_socketID}`;

  const memoizeDebouncedSetChannelPan = memoizeDebounce(setChannelPan, 100, undefined, memoizesetChannelParamResolver);
  const memoizeDebouncedSetChannelVolume = memoizeDebounce(setChannelVolume, 100, undefined, memoizesetChannelParamResolver);

  const onUserUpdateVolume = (socketID?: string) => {
    if (!socketID) return;

    synthPlayer()?.get_hash_socket_id(socketID).then((hashId) => {
      let userVolume = usersService().getUserVolumeBySocketID(socketID);
      if (userVolume != null) {
        let prevValue = _userVolumes.get(socketID);
        if (prevValue != userVolume) {
          let volume = (userVolume + 1) ** 2;
          synthPlayer()?.synth_set_user_gain(
            map(volume / AUDIO.MAX_VOLUME_RAW, 0, 1, 0, 1), hashId
          );
          _userVolumes.set(socketID, userVolume);
        }
      }
    });
  };

  const allSoundOffAllChannels = (socketID: string) => {
    synthPlayer()?.get_hash_socket_id(socketID).then((socket_id) => {
      for (let channel = 0; channel <= MIDI.MAX_CHANNEL; ++channel) {
        synthPlayer()?.all_sounds_off(channel, socket_id);
      }
    });
  };

  const allNotesOffAllChannels = (socketID: string) => {
    synthPlayer()?.get_hash_socket_id(socketID).then((socket_id) => {
      for (let channel = 0; channel <= MIDI.MAX_CHANNEL; ++channel) {
        synthPlayer()?.all_notes_off(channel, socket_id);
      }
    });
  };

  function onMuteUser(socketID: string, mute = true, muteUserInSynth = true) {
    if (muteUserInSynth) {
      synthPlayer()?.get_hash_socket_id(socketID).then((hashId) => {
        if (hashId != null) synthPlayer()?.mute_user(hashId, mute);
      });
    }

    if (mute) {
      allNotesOffAllChannels(socketID);
      allSoundOffAllChannels(socketID);
    }
  }

  const onUserUpdate = (user: ClientSideUserDto) => {
    if (!user) return;

    if (user.userDto?.serverNotesMuted) {
      onMuteUser(user.socketID, true, false);
    }

    onUserUpdateVolume(user.socketID);
  };

  const tryAndClearCache = async (onSuccess?: () => void, onFail?: (ex: any) => void) => {
    if (!onSuccess) onSuccess = () => {
    };
    if (!onFail) onFail = () => {
    };

    try {
      if (COMMON.IS_DESKTOP_APP) {
        let storedSoundfontsFolderExists = await (exists(COMMON.DESKTOP_SOUNDFONTS_FOLDER, { baseDir: BaseDirectory.AppData }) as any as Promise<boolean>);
        if (storedSoundfontsFolderExists) {
          await remove(COMMON.DESKTOP_SOUNDFONTS_FOLDER, {
            baseDir: BaseDirectory.AppData,
            recursive: true
          }).then(onSuccess);
        }
      } else {
        await clearWebSoundfontCache().then(onSuccess);
      }
    } catch (ex) {
      logError(`[Soundfonts Cache Clear] Error: ${ex}`);
      onFail(ex);
    }
  };

  const getDefaultDrumInstrumentInSoundfont = async (): Promise<Instrument | null> => {
    let instruments = await getInstruments();
    return instruments.find(x => {
      return x.bank == MIDI.DEFAULT_DRUM_BANK_1 || x.bank == MIDI.DEFAULT_DRUM_BANK_2;
    }) || null;
  };

  const noteActivitiesSubject = new Subject<AudioServiceNoteActivity>();
  const noteActivitiesNonPressured =
    noteActivitiesSubject
      .asObservable()
      .pipe(
        groupBy(m => m.socketIDHashed),
        mergeMap(group => group)
      );

  const noteActivitiesNonPressured_SendOff = noteActivitiesNonPressured
    .pipe(
      groupBy(m => m.channel),
      rxMap(group => group.pipe(
        debounceTime(5000),
        filter(x => x.type == "On"),
        distinctUntilKeyChanged("velocity")
      )),
      mergeAll(),
      switchMap((value) => {
        return of({
          channel: value.channel,
          type: "Off",
          source: value.source,
          socketID: value.socketID,
          socketIDHashed: value.socketIDHashed,
          isClient: true
        });
      })
    );

  const noteActivitiesPressured = noteActivitiesSubject
    .asObservable()
    .pipe(
      bufferTime(1000, undefined, 10),
      filter(buffer => buffer.length > 0),
      mergeMap(x => x)
    )
    .pipe(
      groupBy(m => m.socketID),
      mergeMap(group => group.pipe(
        distinctUntilKeyChanged("type")
      ))
    );

  const clientEnabledSynthNoteActivities = createMemo(() => {
    appSettingsService().settingSaved();
    return appSettingsService().getSetting<boolean>("UI_ENABLE_SYNTH_NOTE_ACTIVITIES");
  });

  return {
    initialize,
    onLoadSettings,
    resetChannelsToDefault,
    hasCustomSoundfont,
    setChannelPan,
    setChannelVolume,
    noteActivitiesSubjectNonPressured: noteActivitiesNonPressured,
    noteActivitiesSubject: noteActivitiesPressured,
    clientEnabledSynthNoteActivities,
    instruments,
    channels,
    isInstrumentInChannel: (bank: number, preset: number, active: boolean = false) => {
      return AudioChannelHelper.isInstrumentInChannel(channels(), bank, preset, active);
    },
    clearAllChannels: () => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.ClearAllAudioChannels
      }));
    },
    getChannelFromInstrument: (bank: number, preset: number, active: boolean = false): number => {
      return AudioChannelHelper.getChannelFromInstrument(channels(), bank, preset, active);
    },
    setPrimaryChannel: (channel: number) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetPrimaryChannel,
        uint32Value: channel
      }));
    },
    toggleChannelActive: (channel: number) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.ToggleChannelActive,
        uint32Value: channel
      }));
    },
    removeInstrumentFromChannel: (channel: number) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.RemoveInstrumentFromChannel,
        uint32Value: channel
      }));
    },
    setInstrumentOnChannel: (channel: number, bank: number, preset: number, type: SetChannelInstrumentType = SetChannelInstrumentType.Add, setActive: boolean = true) => {
      let targetChannel = channels().find(x => x.channel == channel);
      if (targetChannel && targetChannel.instrument?.bank == bank && targetChannel.instrument.preset == preset) return;

      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetInstrumentOnChannel,
        setChannelInstrumentPayload: SetChannelInstrumentPayload.create({
          channel: channel,
          bank: bank,
          preset: preset,
          type,
          setActive
        })
      }));
    },
    setListenToProgramChanges: (v: boolean) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetListenToProgramChanges,
        boolValue: v
      }));
    },
    setIsDrumChannelMuted: (v: boolean) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetIsDrumChannelMuted,
        boolValue: v
      }));
    },
    setIsPlayingDrumsMode: (v: boolean = true) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetIsPlayingDrumsMode,
        boolValue: v
      }));
    },
    setSlotMode: (slotMode: ActiveChannelsMode) => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetSlotMode,
        slotMode: activeChannelsModeFromJSON(slotMode)
      }));
    },
    incrementSlotMode: () => {
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.IncrementSlotMode
      }));
    },
    setReverbEnabled: (value: boolean) => {
      setReverbEnabled(value);
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.SetReverbEnabled,
        boolValue: value
      }));
    },
    setApplyVelocityCurve: (value: boolean) => {
      setUseVelocityCurve(value);
      appService().coreService()?.send_app_action(AppStateActions.create({
        action: AppStateActions_Action.AudioSetApplyVelocityCurve,
        boolValue: value
      }));
    },
    hasAtleastADrumInstrumentInSoundfont: async () => {
      return await getDefaultDrumInstrumentInSoundfont() != null;
    },
    setOctave,
    setTranspose,
    slotMode,
    maxMultiModeChannels,
    sustained,
    setSustained,
    isSustainActive,
    setSustainPress,
    soundfontLoadingFailedNotify,
    currentChannelToEdit, setCurrentChannelToEdit,
    clientAdded,
    onDisconnect,
    dispatchAddClientSynthAction,
    initialized,
    sampleRate,
    transpose,
    octave,
    customSoundfontExist(name: string) {
      if (!soundfontStore) return Promise.resolve(undefined);
      return dbService().hasKey(name, soundfontStore);
    },
    audioSynthEngine,
    loadSoundfont,
    loadingSoundfont,
    loadedSoundfont,
    saveCustomSoundfont,
    equalizerEnabled,
    setEqualizerEnabled: onSetEqualizerEnabled,
    mousePosSetsVelocity,
    setMousePosSetsVelocity,
    primaryChannel,
    maxNoteOnTime,
    tryAndClearCache,
    reverbEnabled,
    mousePosVelocity,
    maxPolyphony,
    loadedSoundfontName,
    isDrumChannelMuted,
    clearWebSoundfontCache,
    minVelocity,
    maxVelocity,
    volume,
    hashedUsers,
    lastSavedVolume,
    reverbLevel,
    reverbRoomsize: reverbRoomSize,
    setReverbLevel,
    setReverbRoomsize: setReverbRoomSize,
    setReverbDamp,
    setReverbWidth,
    reverbDamp,
    reverbWidth,
    setVolume,
    setVolumeMuted,
    setLastSavedVolume,
    globalVelocityPercentage,
    setMousePosVelocity,
    parseMidiData: (value: Uint8Array, socketID?: number, sourceType?: MidiNoteSource, deviceID?: string) => {
      let hash_device_id: number | undefined = undefined;
      if (deviceID) hash_device_id = synthPlayer()?.hash_device_id(deviceID);
      if (COMMON.IS_WEB_APP) {
        synthPlayer()?.parse_midi_data_non_proxy(value, socketID, sourceType, hash_device_id);
      } else {
        synthPlayer()?.parse_midi_data(value, socketID, sourceType, hash_device_id);
      }
    },
    getChannelsWithInstrumentsLoaded: () => AudioChannelHelper.getChannelsWithInstrumentsLoaded(channels()),
    getChannelNumbers: () => AudioChannelHelper.getChannelNumbers(channels()),
    getChannel: (channel: number) => AudioChannelHelper.getChannel(channels(), channel),
    getDisabledChannels: () => AudioChannelHelper.getDisabledChannels(channels(), slotMode(), maxMultiModeChannels()),
    allChannelsAreInactive: () => AudioChannelHelper.allChannelsAreInactive(channels()),
    synth_events_shared_buffer
  };
};