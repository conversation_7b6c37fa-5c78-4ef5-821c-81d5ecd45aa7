import isEqual from "lodash-es/isEqual";
import isString from "lodash-es/isString";
import Mousetra<PERSON> from "mousetrap";
import { createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import { MidiNoteSource } from "~/proto/midi-renditions";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppKeyboardMappingVisualize } from "~/proto/pianorhythm-app-renditions";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import { CustomKeyboardKeyMap, KeyboardLayout } from "~/types/app.types";
import { Keybind, KeyboardShortcuts } from "~/types/settings.types";
import { MIDI } from "~/util/const.midi";
import { isMobile } from "~/util/helpers";
import { keyCode_to_note_mpp, keyCode_to_note_vp, keyCode_to_note_vp_mod } from '~/util/v2.keyboard-helper';
import AppService from "./app.service";
import AudioService from "./audio.service";
import DisplaysService from "./displays.service";
import AppSettingsService from "./settings-storage.service";

type ActiveKey = {
  keyCode: number;
  note: number;
};

export default function KeyboardService() {
  const appService = useService(AppService);
  const audioService = useService(AudioService);
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);

  let defaultBinds: Keybind[] =
    Object.values(KeyboardShortcuts)
      .filter(isString)
      .map(x => ({
        command: x as KeyboardShortcuts,
        description: undefined,
        binding: getDefaultShortcut(x as KeyboardShortcuts),
        altBinding: undefined
      }))
      .map(x => ({ ...x, binding: x.binding?.toLowerCase() }));

  const [defaultKeybinds] = createSignal<Keybind[]>(defaultBinds);
  const [keybinds, setKeybinds] = createSignal<Keybind[]>(defaultBinds, { equals: isEqual });
  const [customLayoutKeybinds, setCustomLayoutKeybinds] = createSignal<CustomKeyboardKeyMap[]>([], { equals: isEqual });
  const [keyboardLayout, setKeyboardLayout] = createSignal<KeyboardLayout>("VP");
  const [altKeyPressed, setAltKeyPressed] = createSignal(false);
  const [shiftKeyPressed, setShiftKeyPressed] = createSignal(false);
  const [playAreaIsFocused, setPlayAreaIsFocused] = createSignal(false);
  const [editingKeybinds, setEditingKeybinds] = createSignal(false);
  const [initialized, setInitialized] = createSignal(false);
  const [shiftKeyAutoNoteOff, setShiftKeyAutoNoteOff] = createSignal(false);
  const [isInCustomizeLayoutKeysMode, setIsInCustomizeLayoutKeysMode] = createSignal(false);
  const keysDown = new Map<number, ActiveKey>();

  const ms = new Mousetrap(document.body);
  let capsLockDown = false;

  const canPlayKeys = () => {
    return Boolean
      (
        appService().isRoomCurrentPage() &&
        playAreaIsFocused() &&
        !displayService().anyModalsOpened()
      ) || isInCustomizeLayoutKeysMode();
  };

  createEffect(() => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: AppStateActions_Action.SetCanPlayKeys, boolValue: canPlayKeys()
    }));
  });

  createEffect(() => {
    //TODO: Look into a way of improving this so the 'settingSaved'
    // signal wouldn't be need to detect a change.
    appSettingsService().settingSaved();
    setCustomLayoutKeybinds(appSettingsService().getSetting("CUSTOM_KEYBOARD_LAYOUT_KEYBINDS"));
    setShiftKeyAutoNoteOff(appSettingsService().getSetting("KEYBOARD_SHIFT_KEY_AUTO_NOTE_OFF"));
  });

  createEffect(() => {
    if (!canPlayKeys()) {
      keysDown.forEach(x => { audioService().parseMidiData(new Uint8Array([128, x.note, 0]), undefined, MidiNoteSource.KEYBOARD); });
      keysDown.clear();
    }
  });

  const getNoteFromLayout = (layout: KeyboardLayout, keycode: number) => {
    switch (layout) {
      case "VP": return keyCode_to_note_vp(keycode);
      case "MPP": return keyCode_to_note_mpp(keycode);
      case "CUSTOM": return customLayoutKeybinds().find(x => x.keyCode == keycode)?.note || -1;
      default: return -1;
    }
  };

  const getNote = (evt: KeyboardEvent): number | null => {
    let layout = keyboardLayout();
    let keycode = evt.keyCode;

    let isVP = layout == "VP";
    let isCustom = layout == "CUSTOM";
    let note = getNoteFromLayout(layout, keycode);

    if (note == -1) return null;

    if (isCustom) {
      if (evt.shiftKey) note += 12;
      if (evt.altKey) note = note + 24;
      if (capsLockDown || evt.ctrlKey) note -= 12;
      return note;
    }

    let octave = isVP ? (12 * 2) : (12 * 3);

    if (evt.shiftKey) note += isVP ? 1 : 12;
    if (evt.altKey) note = isVP ? note - 1 : note + 24;

    let useQwertyMod = appSettingsService().getSetting("INPUT_MIDI_TO_QWERTY_MOD");
    let useCapslock = capsLockDown && appSettingsService().getSetting("INPUT_MIDI_TO_QWERTY_MOD_USE_CAPSLOCK");

    if (useCapslock || evt.ctrlKey) {
      if (useQwertyMod && isVP) {
        let isMod = keyCode_to_note_vp_mod(keycode);
        note = isMod || note;
        if (isMod || note == -1) return note;
      } else if (appSettingsService().getSetting("INPUT_CTRL_KEY_LOWERS_OCTAVE")) {
        note -= 12;
      }
    }

    if (!isVP && capsLockDown) note -= 12;

    return note + octave;
  };

  const initializeKeybinds = () => {
    ms.reset();
    keysDown.clear();

    //A-Z, 0-9
    for (let i = 39; i <= 95; i++) setupPianoKeyBind(i);

    function setupPianoKeyBind(i: number) {
      let charCode = String.fromCharCode(i).toLowerCase();

      // Prevent default behaviors for most browser shortcut actions
      ms.bind([
        "ctrl+" + charCode,
        ...(new Array(12).fill(0).map((x, idx) => `f${idx + 1}`))
      ]
        .filter(x => x != "ctrl+a")
        .filter(x => x != "ctrl+c")
        .filter(x => x != "ctrl+v")
        .filter(x => x != "ctrl+x")
        , (e: KeyboardEvent) => {
          e.preventDefault();
        });

      ms.bind([
        charCode,
        "capslock",
        "shift+" + charCode,
        "shift+option+" + charCode,
        "option+" + charCode,
        "ctrl+" + charCode,
      ], (e: KeyboardEvent) => {
        if (e.key == "CapsLock" || e.keyCode == 20) capsLockDown = true;
        if (e.repeat) return;
        if (e.altKey) setAltKeyPressed(true);
        if (e.shiftKey) setShiftKeyPressed(true);
        if (keysDown.has(e.keyCode) || !canPlayKeys()) return;
        if (e.ctrlKey) { e.preventDefault(); }

        let note = getNote(e);
        if (note == null) return;

        let velocity = audioService().mousePosSetsVelocity() && !isMobile() ? audioService().mousePosVelocity() : 100;
        audioService().parseMidiData(new Uint8Array([MIDI.NOTE_ON_BYTE + audioService().primaryChannel(), note, velocity]), undefined, MidiNoteSource.KEYBOARD);

        keysDown.set(e.keyCode, { keyCode: e.keyCode, note: note });
      }, "keydown");

      ms.bind([
        charCode,
        "capslock",
        "shift+" + charCode,
        "shift+option+" + charCode,
        "option+" + charCode,
        "ctrl+" + charCode,
      ], (e: KeyboardEvent) => {
        if (e.key == "CapsLock" || e.keyCode == 20) capsLockDown = false;
        if (e.repeat || !keysDown.has(e.keyCode) || !canPlayKeys()) return;
        if (e.altKey) setAltKeyPressed(false);
        if (e.shiftKey) setShiftKeyPressed(false);
        if (e.ctrlKey) { e.preventDefault(); }

        let existingKeyDown = keysDown.get(e.keyCode);
        let note = getNote(e);
        let primary_channel = audioService().primaryChannel();
        let channel = MIDI.NOTE_OFF_BYTE + primary_channel;

        // TODO: Implement this in core wasm
        if (existingKeyDown && note && note != existingKeyDown.note) {
          if (shiftKeyAutoNoteOff()) {
            audioService().parseMidiData(new Uint8Array([channel, existingKeyDown.note, 0]), undefined, MidiNoteSource.KEYBOARD);
          } else {
            // let event = PianoRhythmSynthEvent.ToSynthEvent({ eventName: "note-off", channel: primary_channel, note1: existingKeyDown.note });
            // canvasSynthEventsChannel?.postMessage({ eventName: event.message_type, event });
          }
        }

        if (note == null) return;
        audioService().parseMidiData(new Uint8Array([channel, note, 0]), undefined, MidiNoteSource.KEYBOARD);
        keysDown.delete(e.keyCode);
      }, "keyup");
    }
  };

  let appStateEventsListener: VoidFunction | null = null;

  function initialize() {
    setInitialized(true);
    initializeKeybinds();
    appStateEventsListener?.();
    appStateEventsListener = appService().appStateEvents.listen(async (event) => {
      switch (event) {
        case AppStateEvents.AppStateReset: {
          onDisconnect();
          break;
        }
      }
    });
  }

  const getAllMappings = (octave: number = 0, transpose: number = 0, shiftKey = false, altKey = false) => {
    let allKeyboardEvents: AppKeyboardMappingVisualize[] = [];

    for (let i = 0; i <= 256; i++) {
      let key = String.fromCharCode(i);
      let event = new KeyboardEvent("keydown", { keyCode: i, key, altKey, shiftKey });

      let note = getNote(event);
      if (note == null) continue;

      note += octave * 12 + transpose;

      allKeyboardEvents.push(AppKeyboardMappingVisualize.create({ key, note }));
    }

    return allKeyboardEvents;
  };

  const onDisconnect = () => {
    setIsInCustomizeLayoutKeysMode(false);
    appStateEventsListener?.();
    ms.reset();
    keysDown.clear();
  };

  function getDefaultShortcut(shortcut: KeyboardShortcuts) {
    switch (shortcut) {
      case KeyboardShortcuts.ActivateSustain: return "Space";
      case KeyboardShortcuts.ToggleInstrumentSelectionDisplay: return "F1";
      case KeyboardShortcuts['ToggleMidiInputs&OuputsDisplay']: return "F2";
      case KeyboardShortcuts.ToggleChatMessagesDisplay: return "F3";
      case KeyboardShortcuts.ToggleMetronome: return "F5";
      case KeyboardShortcuts.ToggleKeyboardMappingOverlay: return "F6";
      case KeyboardShortcuts.ToggleSheetMusicViewer: return "F7";
      case KeyboardShortcuts.ToggleSceneWidgets: return "F8";
      case KeyboardShortcuts.TogglShortcutBindingDisplay: return "F9";
      case KeyboardShortcuts.ToggleSettingsDisplay: return "F10";
      case KeyboardShortcuts.DebugStats: return "F12";
      case KeyboardShortcuts.ToggleInstrumentDockDisplay: return "`";
      case KeyboardShortcuts.ToggleChatBarFocus: return "Enter";
      case KeyboardShortcuts.ToggleSustain: return "Backspace";
      case KeyboardShortcuts.TransposeUp: return "Insert";
      case KeyboardShortcuts.TransposeDown: return "Delete";
      case KeyboardShortcuts.OctaveUp: return "Home";
      case KeyboardShortcuts.OctaveDown: return "End";

      default: return null;
    }
  }

  function getShortcutForCommand(command: KeyboardShortcuts) {
    return keybinds().find(x => x.command == command)?.binding;
  }

  return {
    initialize,
    keyboardLayout,
    setKeyboardLayout,
    getShortcutForCommand,
    playAreaIsFocused,
    setPlayAreaIsFocused,
    canPlayKeys,
    customLayoutKeybinds,
    setCustomLayoutKeybinds,
    getDefaultShortcut,
    keybinds,
    editingKeybinds,
    setEditingKeybinds,
    setKeybinds,
    defaultKeybinds,
    onDisconnect,
    altKeyPressed,
    shiftKeyPressed,
    getAllMappings,
    isInCustomizeLayoutKeysMode,
    setIsInCustomizeLayoutKeysMode
  };
}