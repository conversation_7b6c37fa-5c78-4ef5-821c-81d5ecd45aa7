import { DEFAULT_LANGUAGE, LANGUAGES, LanguageType } from "~/util/const.common";
import { createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import { useI18n } from "~/i18n/i18nProvider";
import AppSettingsService from "./settings-storage.service";
import { EventBus } from "@solid-primitives/event-bus";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import NotificationService from "./notification.service";

function I18nService() {
  const appSettingsService = useService(AppSettingsService);
  const [initialized, setInitialized] = createSignal(false);

  const i18n = useI18n();
  const i18nCommon = (key: string, options: any = {}) => { return i18n.t(key, { ...options, ns: 'common' }) as string; };
  const i18nServer = (key: string, options: any = {}) => { return i18n.t(key, { ...options, ns: 'server.messages' }) as string; };
  const i18nRoomPage = (key: string, options: any = {}) => { return i18n.t(key, { ...options, ns: 'roomPage' }) as string; };
  const i18nRoomPageSettings = (key: string, options: any = {}) => { return i18n.t(key, { ...options, ns: 'roomPage.settingsModal' }) as string; };
  const i18nLoginPage = (key: string, options: any = {}) => { return i18n.t(key, { ...options, ns: 'loginPage' }) as string; };
  const [activeLanguage, setActiveLanguage] = createSignal<LanguageType>(
    LANGUAGES.find(x => x[0] == i18n.language) || DEFAULT_LANGUAGE
  );

  const t_roomPageSettings = (key: string, options: any = {}) => activeLanguage() && i18nRoomPageSettings(key, options);
  const t_roomPage = (key: string, options: any = {}) => activeLanguage() && i18nRoomPage(key, options);
  const t_server = (key: string, options: any = {}) => activeLanguage() && i18nServer(key, options);
  const t_loginPage = (key: string, options: any = {}) => activeLanguage() && i18nLoginPage(key, options);
  const t_common = (key: string, options: any = {}) => activeLanguage() && i18nCommon(key, options);

  createEffect(() => {
    let lang = LANGUAGES.find(x => x[0] == appSettingsService().getSetting<string>("DEFAULT_LANGUAGE"));
    if (lang) changeLange(lang);
  });

  function initialize(appStateEffects: EventBus<AppStateEffects>) {
    if (initialized()) return;
    setInitialized(true);

    appStateEffects.listen((effect) => {
      switch (effect.action) {
        case AppStateEffects_Action.Toast: {
          if (!effect.message) return;
          NotificationService.show({
            type: "info",
            description: t_server(effect.message),
            duration: 3000
          });
          break;
        }
      }
    });
  }

  function dispose() {

  }

  const t_roomPageSettingSubMenuLabel = (category: string, menu: string) =>
    t_roomPageSettings(`menus.${category}.subMenus.${menu}.label`);

  const t_roomPageSettingSubMenuElement = (category: string, menu: string, label: string) =>
    t_roomPageSettings(`menus.${category}.subMenus.${menu}.elements.${label}`);

  const t_roomPageSettingHeader = (category: string) =>
    t_roomPageSettings(`menus.${category}.header`);

  const t_roomPageSettingTooltip = (category: string, menu: string, label: string) =>
    t_roomPageSettings(`menus.${category}.subMenus.${menu}.tooltips.${label}`);

  const t_roomPageActionWidgets = (label: string, other: string = "") => t_roomPage(`actionWidgets.${label}${other ? `.${other}` : ""}`);
  const t_roomPageBottomBarButtons = (label: string) => activeLanguage() ? t_roomPage(`bottomBar.buttons.${label}`) : undefined;
  const t_roomPageBottomBarTooltips = (label: string) => t_roomPage(`bottomBar.tooltips.${label}`);
  const t_roomPageBottomBarMessages = (label: string) => t_roomPage(`bottomBar.messages.${label}`);
  const t_roomPageInstDockButtons = (label: string) => t_roomPage(`instrumentDock.inputButtons.${label}`);
  const t_roomPageInstDockToolNames = (label: string) => t_roomPage(`instrumentDock.toolNames.${label}`);
  const t_roomPageInstDockToolNamesToolTips = (label: string, options: any = {}) => t_roomPage(`instrumentDock.toolNames.tooltips.${label}`, options);

  function changeLange(lang: LanguageType, onSuccess?: (lang: LanguageType) => void) {
    if (!lang || activeLanguage()[0] == lang[0]) return;

    i18n.changeLanguage(lang[0]).then(() => {
      setActiveLanguage(lang);
      onSuccess?.(lang);
    });
  }

  function _setActiveLanguage(lang: LanguageType) {
    changeLange(lang, (lang) => { appSettingsService().saveSetting("DEFAULT_LANGUAGE", lang[0]); });
  }

  return {
    initialize,
    dispose,
    setActiveLanguage: _setActiveLanguage,
    activeLanguage,
    t_roomPageActionWidgets,
    t_roomPageSettings,
    t_roomPage,
    t_server,
    t_common,
    t_loginPage,
    t_roomPageSettingSubMenuLabel,
    t_roomPageSettingHeader,
    t_roomPageSettingTooltip,
    t_roomPageSettingSubMenuElement,
    t_roomPageBottomBarButtons,
    t_roomPageBottomBarTooltips,
    t_roomPageBottomBarMessages,
    t_roomPageInstDockButtons,
    t_roomPageInstDockToolNames,
    t_roomPageInstDockToolNamesToolTips,
  };
}

export default I18nService;