import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";
import isEqual from "lodash-es/isEqual";
import { createImmerSignal } from "solid-immer";
import { createMemo, createSignal } from "solid-js";
import { useService } from "solid-services";
import { WasmSynth } from "~/types/audio.types";
import { COMMON, ERROR_MESSAGES, IDS } from "~/util/const.common";
import { isWebMidiSupported } from "~/util/helpers.dom";
import { logError } from "~/util/logger";
import AppService from "./app.service";
import DatabaseServiceController from "./db.service";
import notificationService from "./notification.service";
import { EventBus } from "@solid-primitives/event-bus";
import AppSettingsService from "./settings-storage.service";

type WebMidiIO<T> = {
  id: string;
  hashedID?: number;
  name?: string;
  active: boolean;
  type: T;
};

type MidiConnectionError = {
  midi_id: string,
  error_message: string;
};

/**
 * WebMidiService is a service that handles MIDI input and output connections.
 * It provides functions for initializing the MIDI access, managing MIDI input and output devices,
 * and sending MIDI data to the connected output devices.
 */
export default function WebMidiService() {
  const appService = useService(AppService);

  const [synthPlayer, setSynthPlayer] = createSignal<WasmSynth>();
  const [initialized, setInitialized] = createSignal(false);
  const [hasMidiPermission, setHasMidiPermission] = createSignal(false);
  const [midiAccessRef, setMidiAccessRef] = createSignal<MIDIAccess>();
  const [midiInputs, setMidiInputs] = createImmerSignal<WebMidiIO<WebMidi.MIDIInput>[]>([]);
  const [midiOutputs, setMidiOutputs] = createImmerSignal<WebMidiIO<WebMidi.MIDIOutput>[]>([]);
  const dbService = useService(DatabaseServiceController);
  const savedConnectionsStore = dbService().createStore("web-midi", "connections");
  const getIDFromMidiInput = (input: WebMidi.MIDIInput) => `${input.manufacturer || "device"}-${input.name || input.id}-${input.version || "1.0"}`;
  const getIDFromMidiOutput = (output: WebMidi.MIDIOutput) => `${output.manufacturer || "device"}-${output.name || output.id}-${output.version || "1.0"}`;
  const getActiveMidiInputIDs = createMemo(() => midiInputs().filter(x => x.active).map(x => x.id), [], { equals: isEqual });
  const getActiveMidiOutputIDs = createMemo(() => midiOutputs().filter(x => x.active).map(x => x.name), [], { equals: isEqual });
  const getInactiveMidiInputIDs = createMemo(() => midiInputs().filter(x => !x.active).map(x => x.id), [], { equals: isEqual });
  const getInactiveMidiOutputIDs = createMemo(() => midiOutputs().filter(x => !x.active).map(x => x.id), [], { equals: isEqual });

  function initialize(): Promise<boolean> {
    return new Promise(async (resolve, reject) => {
      if (!isWebMidiSupported()) return reject(ERROR_MESSAGES.WebMidiNotSupported);
      if (initialized()) return resolve(false);

      if (COMMON.IS_AUTOMATED_TEST_MODE) {
        setInitialized(true);
        return resolve(true);
      }

      try {
        if (!synthPlayer()) setSynthPlayer(appService().coreService());

        setInitialized(true);

        if (COMMON.IS_DESKTOP_APP) {
          await listen<MidiConnectionError>("midi_input_fail", (evt) => {
            let { midi_id: id, error_message } = evt.payload;
            setMidiInputs(draft => {
              const input = draft.find((d) => d.id === id || d.name == id);
              if (input) {
                input.active = false;
              }
            });
            notificationService.show({
              type: "danger",
              title: `Midi Input Fail - ${id}`,
              description: error_message
            });
          });

          await listen<MidiConnectionError>("midi_output_fail", (evt) => {
            let { midi_id: id, error_message } = evt.payload;
            setMidiOutputs(draft => {
              const output = draft.find((d) => d.id === id || d.name == id);
              if (output) {
                output.active = false;
                notificationService.show({
                  type: "danger",
                  title: `Midi Output Fail - ${id}`,
                  description: error_message
                });
              }
            });
          });
        }

        if (COMMON.IS_WEB_APP) {
          let message = "Requesting web midi access...";
          notificationService.show({
            type: "info",
            title: "WebMidi",
            description: message,
            persistent: true,
            loading: true,
            duration: Infinity,
            id: IDS.WEBMIDI_ACCESS
          });

          // Check if navigator is defined
          if (!window.navigator) {
            notificationService.hide(IDS.WEBMIDI_ACCESS);
            return reject(`${ERROR_MESSAGES.WebMidiNotSupported} | Environment not supported.`);
          }

          // Use a shorter timeout for better retry behavior
          const WEBMIDI_REQUEST_TIMEOUT = 10_000; // 10 seconds
          let requestAccessTimer = setTimeout(() => {
            setInitialized(false);
            notificationService.hide(IDS.WEBMIDI_ACCESS);
            reject(ERROR_MESSAGES.WebMidiRequestTimedOut);
          }, WEBMIDI_REQUEST_TIMEOUT);

          const midiAccess = await window.navigator.requestMIDIAccess({ sysex: false });
          setMidiAccessRef(midiAccess);
          clearTimeout(requestAccessTimer);

          notificationService.hide(IDS.WEBMIDI_ACCESS);
          notificationService.show({
            description: "WebMidi Access Enabled.",
            loading: false,
            persistent: false,
            duration: 3000,
            type: "success",
            id: IDS.WEBMIDI_ACCESS
          });

          midiAccess.onstatechange = async () => {
            await onStateChange();
          };
        }

        await refreshMidiState(true);

        await dbService().put("inactive-midi-inputs", midiInputs().filter(x => !x.active).map(x => x.id), savedConnectionsStore);
        await dbService().put("inactive-midi-outputs", midiOutputs().filter(x => !x.active).map(x => x.id), savedConnectionsStore);

        setHasMidiPermission(true);
        resolve(true);
      } catch (ex) {
        console.error("[WebMidi]", ex);
        notificationService.hide(IDS.WEBMIDI_ACCESS);

        // Handle specific error types for better retry logic
        if (typeof ex == "object" && ex !== null) {
          const error = ex as Error;

          // Permission denied - don't retry
          if (error.name?.includes("NotAllowedError")) {
            setHasMidiPermission(false);
            resolve(false);
            return;
          }

          // Timeout error - can be retried
          if (error.name?.includes("TimeoutError") || error.message?.includes("timeout")) {
            reject(ERROR_MESSAGES.WebMidiRequestTimedOut);
            return;
          }

          // Security error - don't retry
          if (error.name?.includes("SecurityError")) {
            setHasMidiPermission(false);
            resolve(false);
            return;
          }
        }

        // Generic error - can be retried
        reject(ex instanceof Error ? ex.message : "WebMidi initialization failed");
      }
    });
  }

  /**
   * Handles the state change of MIDI devices.
   *
   * @param firstTimeLoad - Indicates whether it is the first time loading.
   * @returns A promise that resolves when the state change is complete.
   */
  async function onStateChange(firstTimeLoad: boolean = false) {
    if (!midiAccessRef()) return;
    let midiAccess = midiAccessRef()!;
    await onMidiStateChange_Inputs(Array.from(midiAccess.inputs.values()), firstTimeLoad);
    await onMidiStateChange_Outputs(Array.from(midiAccess.outputs.values()), firstTimeLoad);
  }

  /**
   * Retrieves the existing output state of a MIDI output.
   *
   * @param _output - The MIDI output to retrieve the state from.
   * @returns The existing output state, which is either `true` if the output is active, or `false` if it is not.
   */
  const getExistingOutputState = (_output: WebMidi.MIDIOutput) => {
    let output = midiOutputs().find(y => y.id == getIDFromMidiOutput(_output))?.active;
    return output != null ? output : false;
  };

  async function onMidiStateChange_Inputs(inputs: WebMidi.MIDIInput[], firstTimeLoad = false) {
    let inactiveInputs: string[] = [];

    try {
      inactiveInputs = await dbService().get<string[]>("inactive-midi-inputs", savedConnectionsStore) || [];
    } catch (ex) {
      logError(`Failed to get inactive midi inputs. Error: ${ex}`);
    }

    inputs.forEach(input => {
      let id = getIDFromMidiInput(input);
      let active = !inactiveInputs.includes(id);

      // Add to midi inputs if not found
      if (!midiInputs().find(x => x.name == input.name)) {
        setMidiInputs(i => i.push({
          id,
          hashedID: input.name ? synthPlayer()?.hash_device_id(input.name) : undefined,
          name: input.name,
          active,
          type: input
        }));
      }

      setInputActive(id, active, firstTimeLoad);
    });
  }

  async function onMidiStateChange_Outputs(outputs: WebMidi.MIDIOutput[], firstTimeLoad: boolean = false) {
    let inactiveOutputs: string[] = [];
    try {
      inactiveOutputs = firstTimeLoad ? await dbService().get<string[]>("inactive-midi-outputs", savedConnectionsStore) || [] : [];
    } catch (ex) {
      logError(`Failed to get inactive midi outputs. Error: ${ex}`);
    }

    setMidiOutputs(outputs.map((x) => ({
      name: x.name,
      id: getIDFromMidiOutput(x),
      hashedID: x.name ? synthPlayer()?.hash_device_id(x.name) : undefined,
      active: inactiveOutputs.length > 0 ? !inactiveOutputs.includes(getIDFromMidiOutput(x)) : getExistingOutputState(x),
      type: x
    })));
  }

  /**
   * Refreshes the MIDI state.
   *
   * @param firstTimeLoad - Indicates whether it is the first time loading.
   * @returns A promise that resolves when the MIDI state has been refreshed.
   */
  async function refreshMidiState(firstTimeLoad: boolean = false) {
    if (!initialized()) return;

    let midiInputs: any = {};
    let midiOutputs: any = {};

    if (COMMON.IS_WEB_APP) {
      midiInputs = appService().coreService()?.list_midi_input_connections();
    } else {
      midiInputs = await invoke('list_midi_input_connections');
      midiOutputs = await invoke('list_midi_output_connections');
    }

    let inputs: string[] = Object.values((midiInputs || {}) as {});
    let outputs: string[] = Object.values((midiOutputs || {}) as {});

    await onMidiStateChange_Inputs(inputs.map(x => ({
      name: x,
      type: "input",
      connection: "open"
    }) as WebMidi.MIDIInput));

    // Rust web midi outputs doesn't work for some reason?
    // Keeps emitting an error when 'sending' data.
    if (COMMON.IS_DESKTOP_APP)
      await onMidiStateChange_Outputs(outputs.map(x => ({ name: x }) as WebMidi.MIDIOutput), firstTimeLoad);
  }

  function dispose() {
    setInitialized(false);
    midiInputs()?.forEach(x => x.type.close && x.type.close());
    midiOutputs()?.forEach(x => x.type.close && x.type.close());
  }

  /**
   * Emits MIDI data to the MIDI output devices.
   *
   * @param data - The MIDI data to send, represented as an array of numbers or a Uint8Array.
   * @param timestamp - Optional. The timestamp at which the MIDI data should be sent.
   * @param deviceID - Optional. The ID of the MIDI output device to send the data to.
   */
  function emitToMidiOutput(data: number[] | Uint8Array, timestamp?: number, deviceID?: number) {
    for (let entry of midiOutputs()) {
      if (!entry.type || !entry.active || !entry.name) continue;
      // console.log("emitToMidiOutput", entry.name, getActiveMidiOutputIDs(), deviceID, synthPlayer()?.hash_device_id(entry.name));

      // Prevent feedback loop by preventing the same input device id to an output
      if (deviceID) {
        if (deviceID == entry.hashedID) continue;
      }

      try {
        if (entry.type.send) entry.type.send(data, timestamp);
      } catch (ex) {
        console.error("[Midi Output Error]", data, ex);
      }
    }
  }

  /**
   * Handles the active state of a MIDI input.
   *
   * @param input - The WebMidiIO object representing the MIDI input.
   * @returns A Promise that resolves when the active state of the input is handled.
   * @throws An error if the input name is undefined.
   */
  async function handleInputActiveState(input: WebMidiIO<WebMidi.MIDIInput>) {
    try {
      if (!input.name)
        throw new Error("Input name is undefined.");

      if (COMMON.IS_DESKTOP_APP) {
        let command = input.active ? "open_midi_input_connection" : "close_midi_input_connection";
        await invoke(command, { midiId: input.name });
      } else {
        // TODO: Uncomment once midi I/O is available in audio worklets
        // if (appSettingsService().getSetting("AUDIO_USE_WORKLET")) {
        //   appService().webMidiConnectionEvents.emit({
        //     name: input.name,
        //     id: input.id,
        //     active: input.active,
        //     type: "input"
        //   });
        // }

        if (input.active) {
          appService().coreService()?.open_midi_input_connection(input.name);
        } else {
          appService().coreService()?.close_midi_input_connection(input.name);
        }
      }
    } catch (ex: any) {
      let errorMessage = ex?.Message ?? ex;
      if (typeof errorMessage == "string" && errorMessage?.toLowerCase()?.includes("already connected")) return;
      logError(`[setInputActive] Failed to set input ('${input.id}') to active state. Error: ${ex}`);
      notificationService.show({
        type: "danger",
        title: "Midi Input Error",
        description: `Failed to enable Midi Input: ('${input.id}').`
      });

      // Set input to inactive state
      setInputActive(input.id, false, true);
    }
  }

  /**
   * Handles the active state of a MIDI output.
   *
   * @param output - The MIDI output to handle.
   * @returns A promise that resolves when the handling is complete.
   */
  async function handleOutputActiveState(output: WebMidiIO<WebMidi.MIDIOutput>) {
    if (!output.name) return;

    if (COMMON.IS_DESKTOP_APP) {
      let command = output.active ? "open_midi_output_connection" : "close_midi_output_connection";
      await invoke(command, { midiId: output.name });
    }
  }

  /**
   * Sets the active state of a MIDI input.
   *
   * @param id - The ID of the MIDI input.
   * @param active - The desired active state of the MIDI input.
   * @param forceState - Optional. Whether to force the state change even if the input is not open.
   */
  function setInputActive(id: string, active: boolean, forceState = false) {
    setMidiInputs(async draft => {
      const input = draft.find((d) => d.id === id);

      if (input && (input.type.connection == "open" || forceState)) {
        input.active = active;
        await handleInputActiveState(input);
      }
    });
  }

  /**
   * Sets the active state of a MIDI output.
   *
   * @param id - The ID of the MIDI output.
   * @param active - The desired active state of the MIDI output.
   */
  function setOutputActive(id: string, active: boolean) {
    setMidiOutputs(async draft => {
      const output = draft.find((d) => d.id === id);
      if (output) {
        output.active = active;
        await handleOutputActiveState(output);
      }
    });
  }

  /**
   * Sets the active state of multiple MIDI inputs.
   *
   * @param ids - An array of MIDI input IDs.
   * @param active - A boolean indicating whether to set the inputs active or inactive.
   */
  function setMultipleInputsActive(ids: string[], active: boolean) {
    if (!ids) return;
    ids.forEach(x => setInputActive(x, active));
  }

  /**
   * Sets the active state of multiple MIDI outputs.
   *
   * @param ids - An array of MIDI output IDs.
   * @param active - The desired active state (true for active, false for inactive).
   */
  function setMultipleOutputsActive(ids: string[], active: boolean) {
    if (!ids) return;
    ids.forEach(x => setOutputActive(x, active));
  }

  /**
   * Refreshes the state of MIDI inputs.
   */
  function refreshMidiInputsState() {
    midiInputs().forEach(async input => {
      await handleInputActiveState(input);
    });
  }

  /**
   * Refreshes the state of MIDI outputs.
   *
   * This function iterates through all available MIDI outputs and handles their active state.
   * If an error occurs while refreshing the state of an output, it will be logged as an error.
   */
  function refreshMidiOutputsState() {
    midiOutputs().forEach(async output => {
      try {
        await handleOutputActiveState(output);
      } catch (ex) {
        logError(`[refreshMidiOutputsState] Failed to refresh midi outputs state. Error: ${ex}`);
      }
    });
  }

  return {
    initialize,
    dispose,
    midiInputs,
    midiOutputs,
    refreshMidiInputsState,
    refreshMidiOutputsState,
    getActiveMidiInputIDs,
    getActiveMidiOutputIDs,
    getInactiveMidiInputIDs: getInactiveMidiInputIDs,
    getInactiveMidiOutputIDs: getInactiveMidiOutputIDs,
    hasMidiPermission,
    initialized,
    refreshMidiState,
    setInputActive,
    setOutputActive,
    onStateChange,
    toggleInput: (id: string) => {
      setMidiInputs(async draft => {
        const input = draft.find((d) => d.id === id);
        if (input) {
          input.active = !input.active;
          await handleInputActiveState(input);
        }
      });
    },
    toggleOutput: (id: string) => {
      setMidiOutputs(async draft => {
        const output = draft.find((d) => d.id === id);
        if (output) {
          output.active = !output.active;
          await handleOutputActiveState(output);
        }
      });
    }
  };
}