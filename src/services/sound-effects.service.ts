import { useService } from "solid-services";
import { AppSoundFx, AppSoundFx2, ROOT_FOLDER_LOCATION, SoundPlayOptions, StageAudioSfx } from "~/types/app.types";
import AppSettingsService from "./settings-storage.service";
import { createSignal } from "solid-js";

export default function SoundEffectsService() {
  const appSettingsService = useService(AppSettingsService);
  const [globalVolume, setGlobalVolume] = createSignal(appSettingsService().getSetting<number>("AUDIO_SFX_GLOBAL_VOLUME"));

  const ROOT_FOLDER = "./sound_fx";
  const ROOT_FOLDER_DEV = "./assets/sound_fx";
  const UI_FOLDER = `${ROOT_FOLDER}/ui`;
  const UI_FOLDER2 = `${ROOT_FOLDER}/ui2`;
  const FX_FOLDER = `${ROOT_FOLDER}/fx`;
  const STAGE_FX_FOLDER = `${ROOT_FOLDER}/stage_fx`;
  const DEFAULT_PLAY_OPTIONS: SoundPlayOptions = {
    loop: false,
    autoPlay: true,
    autoUnload: false,
    rootFolder: ROOT_FOLDER_LOCATION.UI,
    rate: 1,
    volume: 0.05,
  };

  const DEFAULT_PLAY_SFX_OPTIONS: SoundPlayOptions = {
    ...DEFAULT_PLAY_OPTIONS,
  };

  const DEFAULT_STAGE_FX_PLAY_OPTIONS: SoundPlayOptions = {
    ...DEFAULT_PLAY_OPTIONS,
    loop: true,
    autoUnload: false,
    rootFolder: ROOT_FOLDER_LOCATION.STAGE_AUDIO_FX,
  };

  const initialize = () => {
  };

  const playHoverSFX = () => {
  };


  /**
   * Plays a sound effect based on the provided key and options.
   *
   * @param key - The key representing the sound effect to be played. It can be of type `AppSoundFx`, `AppSoundFx2`, or `StageAudioSfx`.
   * @param play_options - Optional. The options for playing the sound effect. Defaults to `DEFAULT_PLAY_OPTIONS`.
   */
  function playSFX(key: AppSoundFx | AppSoundFx2 | StageAudioSfx, play_options = DEFAULT_PLAY_OPTIONS) {
  };

  function playSFX_fx(key: AppSoundFx, play_options = DEFAULT_PLAY_SFX_OPTIONS) {
    return playSFX(key, { ...play_options, rootFolder: ROOT_FOLDER_LOCATION.FX });
  }

  function playSFX_ui2(key: AppSoundFx2, play_options = DEFAULT_PLAY_SFX_OPTIONS) {
    return playSFX(key, { ...play_options, rootFolder: ROOT_FOLDER_LOCATION.UI_2 });
  }

  /**
   * Plays a sound effect for a chat message if the chat sound effects are enabled in the application settings.
   *
   * @param play_options - Optional parameter to specify the play options. Defaults to `DEFAULT_PLAY_OPTIONS`.
   * @returns The result of the `playSFX_fx` function if the sound effect is played, otherwise returns undefined.
   */
  function playChatMessageInSFX(play_options = DEFAULT_PLAY_OPTIONS) {
    if (!appSettingsService().getSetting("AUDIO_SFX_CHAT_ENABLE")) return;
    return playSFX_fx(AppSoundFx.POP, play_options);
  }

  /**
   * Plays a sound effect when a chat message is sent.
   *
   * @param play_options - The options for playing the sound effect. Defaults to `DEFAULT_PLAY_OPTIONS`.
   * @returns The result of the `playSFX_fx` function if the sound effect is enabled, otherwise returns `undefined`.
   */
  function playChatMessageSendSFX(play_options = DEFAULT_PLAY_OPTIONS) {
    if (!appSettingsService().getSetting("AUDIO_SFX_CHAT_ENABLE")) return;
    return playSFX_fx(AppSoundFx.ARCADE_JUMP, play_options);
  }

  return ({
    initialize,
    playHoverSFX,
    playChatMessageSendSFX,
    playChatMessageInSFX,
    playSFX,
    playSFX_ui2,
    stageEffectsVolume: () => 0,
    playModalOpenSFX: () => { },
    playModalCloseSFX: () => { },
    playNotificationSuccessSFX: () => { },
    playClickSFX: () => { },
    playErrorSFX: () => { },
  });
}