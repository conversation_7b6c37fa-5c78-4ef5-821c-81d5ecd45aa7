import { createStore } from "solid-js/store";
import { AppThemeColors } from "~/proto/pianorhythm-app-renditions";
import { AppThemes } from "~/types/app.types";
import { setUIThemeColors } from "~/util/helpers.dom";
import ThemeConfig from "~/util/theme-config";

export default function AppThemesService() {
  const [themeColors, setThemeColors] = createStore<AppThemeColors>(AppThemeColors.create({
    primary: ThemeConfig.darkTheme?.colors?.primary1 as string || "#363942",
    accent: ThemeConfig.darkTheme?.colors?.accent1 as string || "#00d1b2",
    tertiary: ThemeConfig.darkTheme?.colors?.["tertiary1"] as string || "#ef9e08"
  }));

  const setTheme = (selectedTheme: AppThemes) => {
    if (selectedTheme == null) return;
    if (isNaN(Number(selectedTheme))) selectedTheme = AppThemes[selectedTheme] as any;
    let root = document.querySelector(".hope-ui-dark")! as HTMLElement || document.querySelector(".hope-ui-light")! as HTMLElement;

    switch (selectedTheme) {
      case AppThemes.THEME_1: setThemeColors(setUIThemeColors("#1B2430")); break;
      case AppThemes.THEME_2: setThemeColors(setUIThemeColors("#16213E")); break;
      case AppThemes.THEME_3: setThemeColors(setUIThemeColors("#4C3A51")); break;
      case AppThemes.THEME_4: setThemeColors(setUIThemeColors("#472D2D")); break;
      case AppThemes.THEME_5: setThemeColors(setUIThemeColors("#9A1663", "#E0144C")); break;
      case AppThemes.THEME_6: setThemeColors(setUIThemeColors("#46244C", "#C74B50")); break;
      case AppThemes.WHITE_AND_BLACK: setThemeColors(setUIThemeColors("#e3e3e3", "darkgray")); break;
      case AppThemes.BLACK_AND_WHITE: setThemeColors(setUIThemeColors("#111", "#e3e3e3")); break;
      case AppThemes.HALLOWEEN: setThemeColors(setUIThemeColors("#191919", "#C84B31", "#9A0680")); break;

      default: {
        Object.entries(ThemeConfig.darkTheme?.colors || {}).forEach(([key, val]) => {
          if (root) root.style.setProperty(`--hope-colors-${key}`, val as string);
        });

        setThemeColors({
          primary: ThemeConfig.darkTheme?.colors?.primary1 as string || "#363942",
          accent: ThemeConfig.darkTheme?.colors?.accent1 as string || "#00d1b2",
          tertiary: ThemeConfig.darkTheme?.colors?.["tertiary1"] as string || "#ef9e08"
        });
        break;
      }
    }
  };

  return {
    themeColors,
    setTheme
  };
}