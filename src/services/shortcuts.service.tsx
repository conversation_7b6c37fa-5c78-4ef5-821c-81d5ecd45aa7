import clamp from "lodash-es/clamp";
import isEqual from "lodash-es/isEqual";
import isString from "lodash-es/isString";
import Mousetrap from "mousetrap";
import { Component, createEffect, createSignal, onCleanup } from "solid-js";
import { useService } from "solid-services";
import { Keybind, KeyboardShortcuts } from "~/types/settings.types";
import { AUDIO } from "~/util/const.common";
import AudioService from "./audio.service";
import ChatService from "./chat.service";
import DisplaysService from "./displays.service";
import KeyboardService from "./keyboard.service";
import AppSettingsService from "./settings-storage.service";
import WebsocketService from "./websocket.service";

/**
 * Component that manages keyboard shortcuts for various commands.
 * @component
 */
const ShortcutsManager: Component = () => {
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);
  const keyboardService = useService(KeyboardService);
  const chatService = useService(ChatService);
  const audioService = useService(AudioService);

  const ms = new Mousetrap(document.body);

  const cannotExecuteShortcut = () => (keyboardService().editingKeybinds());

  onCleanup(() => {
    ms.reset();
  });

  const getMSKey = (key: string) => {
    if (!isString(key)) return key;

    return key.toLowerCase()
      .replaceAll("control", "ctrl")
      .replaceAll("insert", "ins")
      .replaceAll("delete", "del")
      .replaceAll("control", "ctrl")
      .replaceAll("option", "alt")
      .replaceAll("arrowup", "up")
      .replaceAll("arrowdown", "down")
      .replaceAll("arrowleft", "left")
      .replaceAll("arrowright", "right")
      .replaceAll(" ", "");
  };

  const ignoredElements = ["input", "textarea", "select"];
  function msBind(binding: string | string[], func: (evnt: Mousetrap.ExtendedKeyboardEvent) => void, action?: string, allowRepeat?: boolean) {
    ms.bind(binding, (event) => {
      if (!allowRepeat && event.repeat) return;
      if (cannotExecuteShortcut()) return;

      // Ignore input elements
      let tagName = (event.target as HTMLElement).tagName.toLowerCase();
      let divElement = (event.target as HTMLDivElement);

      if (ignoredElements.includes(tagName) || tagName == "div" && divElement.contentEditable == "plaintext-only") return;

      event.preventDefault();
      func(event);
    }, action);
  }

  function bindEvent(keybind: Keybind) {
    if (!isString(keybind.binding)) return;
    let binding = getMSKey(keybind.binding as string);

    switch (keybind.command) {
      case KeyboardShortcuts.MinimizeMaximizeChatMessages: {
        msBind(binding, () => chatService().setChatMinimized(v => !v));
        break;
      }
      case KeyboardShortcuts.ToggleChatBarFocus: {
        msBind(binding, (event) => {
          if (chatService().preventDefaultIfChatCommandsDisplayed(event)) return false;
          chatService().setChatOpacity((prev) => prev == 1 ? chatService().defaultChatOpacity : 1);
          chatService().chatBarInputElement()?.focus({ preventScroll: false });
        });
        break;
      }
      case KeyboardShortcuts.OpenNewRoomModal: {
        msBind(binding, () => displayService().toggleDisplay("NEW_ROOM_MODAL"));
        break;
      }
      case KeyboardShortcuts.ResetChannelsToDefault: {
        msBind(binding, () => audioService().resetChannelsToDefault());
        break;
      }
      case KeyboardShortcuts.ResetInstruments: {
        msBind(binding, () => audioService().clearAllChannels());
        break;
      }
      case KeyboardShortcuts["ToggleMidiInputs&OuputsDisplay"]: {
        msBind(binding, () => displayService().toggleDisplay("MIDI_IO_MODAL"));
        break;
      }
      case KeyboardShortcuts.ToggleSheetMusicViewer: {
        msBind(binding, () => {
          displayService().toggleDisplay("SHEET_MUSIC_VIEWER");
        });
        break;
      }
      case KeyboardShortcuts.ToggleSceneWidgets: {
        msBind(binding, () => {
          displayService().toggleDisplay("SCENE_WIDGET_BUTTONS");
        });
        break;
      }
      case KeyboardShortcuts.ToggleSheetMusicUpload: {
        msBind(binding, () => displayService().toggleDisplay("SHEET_MUSIC_UPLOAD_MODAL"));
        break;
      }
      case KeyboardShortcuts.ToggleSettingsDisplay: {
        msBind(binding, () => displayService().toggleDisplay("SETTINGS_MODAL"));
        break;
      }
      case KeyboardShortcuts.ToggleChatMessagesDisplay: {
        msBind(binding, () => {
          displayService().toggleDisplay("CHAT_MESSAGES");
          appSettingsService().saveSetting("DISPLAY_CHAT", displayService().getDisplay("CHAT_MESSAGES"));
        });
        break;
      }
      case KeyboardShortcuts.ToggleInstrumentSelectionDisplay: {
        msBind(binding, () => displayService().toggleDisplay("INSTRUMENT_SELECTION"));
        break;
      }
      case KeyboardShortcuts.DebugStats:
        msBind(binding, () => displayService().toggleDisplay("DEBUG_STATS"));
        break;
      case KeyboardShortcuts.ToggleInstrumentDockDisplay: {
        msBind(binding, () => {
          if (!appSettingsService().getSetting("DISPLAY_INST_DOCK")) return;
          displayService().toggleDisplay("INSTRUMENT_DOCK");
        });
        break;
      }
      case KeyboardShortcuts.ToggleKeyboardMappingOverlay: {
        msBind(binding, () => displayService().toggleDisplay("KEYBOARD_MAPPING_OVERLAY"));
        break;
      }
      case KeyboardShortcuts.TransposeUp: {
        msBind(binding, () => audioService().setTranspose(v => clamp(v + 1, AUDIO.MIN_TRANSPOSE, AUDIO.MAX_TRANSPOSE)));
        break;
      }
      case KeyboardShortcuts.TransposeDown: {
        msBind(binding, () => audioService().setTranspose(v => clamp(v - 1, AUDIO.MIN_TRANSPOSE, AUDIO.MAX_TRANSPOSE)));
        break;
      }
      case KeyboardShortcuts.ResetTranspose: {
        msBind(binding, () => audioService().setTranspose(AUDIO.DEFAULT_TRANSPOSE));
        break;
      }
      case KeyboardShortcuts.OctaveUp: {
        msBind(binding, () => audioService().setOctave(v => clamp(v + 1, AUDIO.MIN_OCTAVE, AUDIO.MAX_OCTAVE)));
        break;
      }
      case KeyboardShortcuts.OctaveDown: {
        msBind(binding, () => audioService().setOctave(v => clamp(v - 1, AUDIO.MIN_OCTAVE, AUDIO.MAX_OCTAVE)));
        break;
      }
      case KeyboardShortcuts.ResetOctave:
        msBind(binding, () => audioService().setOctave(AUDIO.DEFAULT_OCTAVE));
        break;
      case KeyboardShortcuts.ToggleSustain: {
        let input = [binding, `shift+${binding}`, `shift+option+${binding}`, `option+${binding}`];
        msBind(input, () => {
          if (!keyboardService().canPlayKeys()) return;
          audioService().setSustained(v => !v);
        });
        break;
      }
      case KeyboardShortcuts.ActivateSustain: {
        let sustainToggleActive = false;
        let input = [binding, `shift+${binding}`, `shift+option+${binding}`, `option+${binding}`];
        msBind(input, () => {
          if (!keyboardService().canPlayKeys()) return;
          if (audioService().sustained()) {
            audioService().setSustained(v => !v);
            sustainToggleActive = true;
          } else {
            audioService().setSustainPress(true);
            sustainToggleActive = false;
          }
        }, "keydown");

        msBind(input, () => {
          if (!keyboardService().canPlayKeys()) return;
          if (audioService().sustained() || sustainToggleActive) {
            audioService().setSustained(v => !v);
          } else {
            audioService().setSustainPress(false);
            sustainToggleActive = false;
          }
        }, "keyup");
        break;
      }
      default:
        break;
    }
  }

  createEffect(() => {
    ms.reset();
    let binds = keyboardService().keybinds();
    ([...binds]).filter(x => x.binding).forEach(bindEvent);
  });

  return undefined;
};

export default ShortcutsManager;