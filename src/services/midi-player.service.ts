import { createEventBus } from "@solid-primitives/event-bus";
import { createSignal } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AppMidiSequencerEvent, AppMidiSequencerEventType } from "~/proto/pianorhythm-app-renditions";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { DefaultMidiPlayerCurrentState, MidiPlayerCurrentState } from "~/types/midi-player.types";
import { COMMON } from "~/util/const.common";
import AppService from "./app.service";
import DisplaysService from "./displays.service";

export default function MidiPlayerService() {
  const displayService = useService(DisplaysService);
  const appService = useService(AppService);

  const [initialized, setInitialized] = createSignal(false);
  const [loadedMidiFileName, setLoadedMidiFileName] = createSignal<string>();
  const [midiSequencerState, setMidiSequencerState] = createStore<MidiPlayerCurrentState>({ ...DefaultMidiPlayerCurrentState });
  const appMidiSequencerEvents = createEventBus<AppMidiSequencerEvent>();
  const [totalTime, setTotalTime] = createSignal<number>();
  const [totalTimeFormatted, setTotalTimeFormatted] = createSignal<string>("0:00");

  const initialize = () => {
    if (initialized()) return;

    if (COMMON.IS_DESKTOP_APP) {

    } else {
      appService().wasmMidiSequencerEffects.listen((payload) => {
        handleMidiSequencerEffects(payload);
      });

      self.addEventListener("midi_sequencer_effects", (event) => {
        let bytes = ((event as any).detail as Uint8Array);
        handleMidiSequencerEffects(bytes);
      });
    }

    setInitialized(true);
  };

  const onDisconnect = () => {
    setMidiSequencerState({ ...DefaultMidiPlayerCurrentState });
    setLoadedMidiFileName();
  }

  const onUpdateTotalTime = (input?: number) => {
    if (!input) return;

    setTotalTime((input || 0) * 1000);
    let time = formatTime((totalTime() || 0) / 1000, true);
    if (time) setTotalTimeFormatted(time);
  };

  const handleMidiSequencerEffects = (payload: Uint8Array) => {
    try {
      let effect = AppStateEffects.decode(payload);

      if (effect.action == AppStateEffects_Action.MidiSequencerEvent && effect.midiSequencerEvent != null) {
        if (effect.midiSequencerEvent.eventType == AppMidiSequencerEventType.FILE_OUTPUT) {
          let output = effect.midiSequencerEvent;
          if (COMMON.IS_DEV_MODE) console.log("[dev] FILE OUTPUT", output);

          if (!output.isVPSheet) {
            displayService().setDisplay("MIDI_PLAYER_UI", true);
          }

          if (COMMON.IS_DEV_MODE) console.log("[dev] FILE OUTPUT", output);

          setMidiSequencerState("playbackSpeed", 1);

          if (output.fileName) {
            setLoadedMidiFileName(output.fileName);
            setMidiSequencerState("fileName", output.fileName);
          }

          if (output.totalTime != null) {
            setMidiSequencerState("totalTime", output.totalTime);
          }

          setMidiSequencerState("lyrics", output.lyrics);
          setMidiSequencerState("trackNames", output.trackNames);
          setMidiSequencerState("bpm", output.currentBPM);
          setMidiSequencerState("ppq", output.ppq);
          setMidiSequencerState("programChanges", output.programChanges);
          setMidiSequencerState("tempoChanges", output.tempoChanges);
          setMidiSequencerState("copyRightNotice", output.copyrightNotice);
          setMidiSequencerState("markerTexts", output.markerTexts);
          setMidiSequencerState("texts", output.texts);
        }

        appMidiSequencerEvents.emit(effect.midiSequencerEvent);

        switch (effect.midiSequencerEvent.eventType) {
          case AppMidiSequencerEventType.TOTAL_TIME_CHANGE: {
            setMidiSequencerState("totalTime", effect.midiSequencerEvent.totalTime);
            break;
          }
          case AppMidiSequencerEventType.FINISHED:
          case AppMidiSequencerEventType.STOPPED: {
            setLoadedMidiFileName();
            setMidiSequencerState({ ...DefaultMidiPlayerCurrentState });
            break;
          }
        }
      }
    } catch (ex) {
      console.error("midi_sequencer_effects error", ex);
    }
  };

  function formatTime(seconds: number, showMilis: boolean) {
    let isNegative = seconds < 0;
    seconds = Math.max(isNegative ? Math.abs(seconds) : seconds, 0);
    let date = new Date(seconds * 1000);
    let timeStrLength = showMilis ? 11 : 8;

    try {
      let timeStr = date.toISOString().substr(11, timeStrLength);
      if (timeStr.substring(0, 2) == "00") {
        timeStr = timeStr.substring(3);
      }
      return `${isNegative ? "-" : ""}${timeStr}`;
    } catch (e) {
      console.error("[formatTime] Error:", e);
      //ignore this. only seems to happen when messing with breakpoints in devtools
    }
  }

  return {
    initialize,
    onDisconnect,
    onUpdateTotalTime,
    formatTime,
    totalTime, setTotalTime,
    loadedMidiFileName, setLoadedMidiFileName,
    totalTimeFormatted, setTotalTimeFormatted,
    appMidiSequencerEvents,
    handleMidiSequencerEffects,
    midiSequencerState, setMidiSequencerState,
    DEFAULT_BPM: 120,
  };
}