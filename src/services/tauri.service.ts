import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { isNumber } from 'lodash-es';
import { AppStateActions, AudioSynthActions, AppStateActions_Action } from '~/proto/pianorhythm-actions';
import { CoreService, ITauriService } from '~/types/app.types';
import { PianoRhythmSynthEvent, SynthInterpolationMethod, WasmSynth } from '~/types/audio.types';
import { logDebug, logError } from '~/util/logger';

export class TauriService implements ITauriService {
  private createWasmSynth(): WasmSynth {
    let desktop_synth: WasmSynth = {
      ...WasmSynth.DEFAULT,
      //@ts-ignore
      note_on: async (channel: number, key: number, velocity: number, socketId: number, source: number) => {
        return Promise.resolve(await invoke("note_on", { channel, key, vel: velocity, socketId, source }));
      },
      //@ts-ignore
      note_off: async (channel: number, key: number, socketId: number, source: number) => {
        return Promise.resolve(await invoke("note_off", { channel, key, socketId, source }));
      },
      load_soundfont: async (file, isLocalFile, saveLocally = false) => {
        let command = isLocalFile ? "load_soundfont_by_path" : "load_soundfont_by_url";
        await invoke(command, { resourcePath: file, saveLocally }).catch((err) => {
          logError(`[Load Soundfont] Command: ${command} | Error: ${err}`);
          throw new Error("fail");
        });
      },
      disconnect: () => invoke("disconnect"),
      dispose: () => invoke("dispose"),
      reset: () => invoke("reset"),
      set_max_multi_mode_channels: (value) => {
        if (value != null && isNumber(value)) invoke("set_max_multi_mode_channels", { value });
      },
      set_octave_offset: (value) => {
        if (value != null && isNumber(value)) invoke("set_octave_offset", { value });
      },
      set_transpose_offset: (value) => {
        // PRFP-1047
        if (value != null && isNumber(value)) invoke("set_transpose_offset", { value });
      },
      set_disable_velocity_for_client: (value) => invoke("set_disable_velocity_for_client", { value }),
      synth_set_reverb_width: (value) => invoke("synth_set_reverb_width", { value }),
      synth_set_reverb_level: (value) => invoke("synth_set_reverb_level", { value }),
      set_apply_velocity_curve: (value) => invoke("set_apply_velocity_curve", { value }),
      load_midi_file: (data, fileName) => invoke("load_midi_file", { bytes: data, fileName }),
      load_midi_file_by_path: (path) => invoke("load_midi_file_by_path", { resourcePath: path }),
      synth_set_reverb_room_size: (value) => {
        invoke("synth_set_reverb_room_size", { value });
      },
      synth_set_reverb_damp: (value) => invoke("synth_set_reverb_damp", { value }),
      parse_midi_data: async (data, socketID, sourceType, deviceID) => {
        let results = await invoke<PianoRhythmSynthEvent[] | undefined>("parse_midi_data", { data: Array.from(data), socketId: socketID, source: sourceType, deviceId: deviceID });
        return results;
      },
      volume_change: async (channel, value, socketID) => {
        let results = await invoke<PianoRhythmSynthEvent[] | undefined>("volume_change", { channel, value, socketId: socketID });
        return results;
      },
      pan_change: async (channel, value, socketID) => {
        let results = await invoke<PianoRhythmSynthEvent[] | undefined>("pan_change", { channel, value, socketId: socketID });
        return results;
      },
      program_select: (channel, presetId, socketId) => {
        invoke("program_select", { channel, presetId, socketId });
      },
      bank_select: (channel, bankId, socketId) => {
        invoke("bank_select", { channel, bankId, socketId });
      },
      all_notes_off: (channel, socketID) => invoke("all_notes_off", { channel, socketId: socketID }),
      all_sounds_off: (channel, socketID) => invoke("all_sounds_off", { channel, socketId: socketID }),
      set_channel_active: (channel, value, socketID) => {
        invoke("set_channel_active", { channel, value, socketId: socketID });
      },
      remove_socket: (socketId) => {
        invoke("remove_socket", { socketId });
      },
      set_client_socket_id: (socketId) => {
        invoke("set_client_socket_id", { socketId });
      },
      set_interpolation_method: (_interpolation_method) => {
        let method = SynthInterpolationMethod.ToID(_interpolation_method);
        invoke("set_interpolation_method", { method });
      },
      add_socket: async (socketId) => {
        let result: boolean = await invoke("add_socket", { socketId, isClient: false });
        return result;
      },
      damper_pedal: (channel, value, socketID) => {
        invoke("damper_pedal", { channel, value, socketId: socketID });
      },
      synth_set_reverb: (value) => {
        invoke("synth_set_reverb", { value });
      },
      synth_set_gain: (value) => {
        invoke("synth_set_gain", { value });
      },
      synth_set_user_gain: (value, socketID) => {
        invoke("synth_set_user_gain", { value, socketId: socketID });
      },
      synth_set_polyphony: (value) => {
        invoke("synth_set_polyphony", { value });
      },
      instrument_exists: async (banknum, prognum) => {
        let result: boolean = await invoke("instrument_exists", { banknum, prognum });
        return result;
      },
      mute_user: (socketID, value) => {
        invoke("mute_user", { socketId: socketID, value });
      },
      get_synth_users: async () => {
        let result: string[] = await invoke("get_synth_users");
        return Promise.resolve(result);
      },
      synth_get_reverb_room_size: async () => {
        return Promise.resolve(await invoke("synth_get_reverb_room_size") as number);
      },
      synth_get_reverb_level: async () => {
        return Promise.resolve(await invoke("synth_get_reverb_level") as number);
      },
      synth_get_reverb_damp: async () => {
        return Promise.resolve(await invoke("synth_get_reverb_damp") as number);
      },
      synth_get_reverb_width: async () => {
        return Promise.resolve(await invoke("synth_get_reverb_width") as number);
      },
      clear_program_on_channel: (channel, socketID) => {
        invoke("clear_program_on_channel", { channel, socketId: socketID });
      },
      get_program: async (channel: number, socketId?: number) => {
        return Promise.resolve(await invoke("get_program", { channel, socketId }));
      },
      reset_all_controllers: (channel, socketId) => {
        if (socketId != null && socketId < 0) return;
        invoke("reset_all_controllers", { channel, socketId });
      },
      from_socket_note_on: (event: any, socketID?: number) => {
        invoke("from_socket_note_on", { data: event, socketId: socketID });
      },
      from_socket_note_off: (channel: number, key: number, socketId: number, source: number) => {
        invoke("from_socket_note_off", { channel, key, socketId });
      },
      from_socket_pitch: (event: any, socketID?: number) => {
        invoke("from_socket_pitch", { data: event, socketId: socketID });
      },
      synth_set_auto_fill_channels_with_default: (value: boolean) => {
        invoke("synth_set_auto_fill_channels_with_default", { value });
      },
      get_synth_audio_channels: async () => {
        return await invoke("get_synth_audio_channels") as Uint8Array[];
      },
      bypassall_equalizer: (value: boolean) => invoke("bypassall_equalizer", { value }),
      reset_equalizer: () => invoke("reset_equalizer"),
      set_slot_mode: (value: number) => { if (value != null && isNumber(value)) invoke("set_slot_mode", { value }); },
      set_max_note_on_time: (value: number) => invoke("set_max_note_on_time", { value }),
      set_max_velocity: (value: number) => invoke("set_max_velocity", { value }),
      set_min_velocity: (value: number) => invoke("set_min_velocity", { value }),
      set_drum_channel_muted: (value: boolean) => invoke("set_drum_channel_muted", { value }),
      set_equalizer_enabled: (value: boolean) => invoke("set_equalizer_enabled", { value }),
      set_midi_output_only: (value: boolean) => invoke("set_midi_output_only", { value }),
      set_use_default_instrument_when_missing_for_other_users: (value: boolean) => invoke("set_use_default_instrument_when_missing_for_other_users", { value }),
      set_primary_channel: (value: number) => invoke("set_primary_channel", { value }),
      set_equalizer_resonance: (idx: number, value: number) => invoke("set_equalizer_resonance", { idx, value }),
      set_equalizer_bypass: (idx: number, value: boolean) => invoke("set_equalizer_bypass", { idx, value }),
      set_equalizer_freq: (idx: number, value: number) => invoke("set_equalizer_freq", { idx, value }),
      set_equalizer_gain: (idx: number, value: number) => invoke("set_equalizer_gain", { idx, value }),
      set_equalizer_band: (idx: number, curve: number, frequency: number, resonance: number, gain: number) => invoke("set_equalizer_band", { idx, curve, frequency, resonance, gain }),
    };

    return desktop_synth;
  }

  public createCoreService(
    createAppAction: (action: AppStateActions) => Uint8Array,
    on_emit_core_app_actions?: (bytes: Uint8Array) => void
  ): CoreService {
    let desktop_synth = this.createWasmSynth();

    let _coreService: CoreService = {
      ...desktop_synth,
      websocket_connect: function (url: string, on_connect?: (() => void) | undefined, on_error?: (() => void) | undefined, on_close?: (() => void) | undefined): Promise<void> {
        logDebug("called [websocket_connect]", url);
        return invoke("connect_websocket", { "url": url });
      },
      websocket_send_binary: function (bytes: Uint8Array): void {
        invoke("emit_websocket_binary", { "bytes": Array.from(bytes) });
      },
      websocket_disconnect: function (): void {
        invoke("disconnect_websocket");
      },
      core_to_renderer_effects: function (bytes: Uint8Array): void {
        invoke("core_to_renderer_effects", { "bytes": Array.from(bytes) });
      },
      core_to_renderer_events: function (bytes: Uint32Array): void {
        invoke("core_to_renderer_events", { "bytes": Array.from(bytes) });
      },
      emit_core_app_actions: function (bytes: Uint8Array): void {
        invoke("emit_core_app_action", { "bytes": Array.from(bytes) });
        on_emit_core_app_actions?.(bytes);
      },
      send_app_action: function (action: AppStateActions): void {
        this.emit_core_app_actions(createAppAction(action));
      },
      send_app_action_bytes: function (action: Uint8Array): void {
        this.emit_core_app_actions(action);
      },
      send_app_synth_action: function (action: AudioSynthActions): void {
        this.send_app_action(AppStateActions.create({
          action: AppStateActions_Action.SynthAction,
          audioSynthAction: AudioSynthActions.create(action)
        }));
      },
      reset_app_state: function (): void {
        invoke("reset_app_state");
      },
      init_note_buffer_engine: function (): void {
      },
      synth_load_soundfont: function (bytes: Uint8Array): void {
      },
      get_core_version: function (): Promise<string> {
        return invoke("get_core_version");
      },
      get_synth_version: function (): Promise<string> {
        return invoke("get_synth_version");
      },
      get_renderer_version: function (): Promise<string> {
        return invoke("get_renderer_version");
      },
      synth_sustain: function (value: number, socket_id?: string | undefined): void {
        invoke("synth_sustain", { value, socketId: socket_id });
      },
      // Not needed for desktop
      init_wasm: function (): void { },
      get_hash_socket_id: async (socket_id: string): Promise<number | undefined> => {
        return await invoke<number>("get_hash_socket_id", { socketId: socket_id });
      },
      flush_note_buffer_engine: function (): void {
      },
      create_synth: function (synth_engine_idx: number, _sampleRate: number): void {
      },
      create_synth_stream: function (sampleRate: number): void {
      },
      midi_io_start: function (ctx: AudioContext, channels: number): void {
      },
      webrtc_connect: function (roomID: string): void {
      },
      webrtc_disconnect: function (): void {
      },
      note_buffer_engine_set_self_hosted: function (value: boolean): void {
        //TODO: Implement in desktop
        invoke("note_buffer_engine_set_self_hosted", { value });
      }
    };

    return _coreService;
  }

  public async listenEvents(
    onHandleAppEvent: (evt: number) => void,
    onHandleAppEffects: (evt: Uint8Array) => void
  ): Promise<void> {
    await listen<Array<number>>("app_events", (evt) => {
      evt.payload.forEach(onHandleAppEvent);
    });

    await listen<Array<number>>("app_effects", (evt) => {
      onHandleAppEffects(new Uint8Array(evt.payload));
    });

    await invoke("init_core");
  }
}