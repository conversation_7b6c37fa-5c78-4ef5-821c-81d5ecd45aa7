import { describe, it, expect, beforeEach } from 'vitest';
import AudioService from '~/services/audio.service';
import AppService from '~/services/app.service';
import { useService } from 'solid-services';
import { MockSoundEffectsService, MockAppService, MockAppSettingsService, MockDbService } from '@test/mocks/service.mocks';
import AppSettingsService from '../settings-storage.service';
import SoundEffectsService from '../sound-effects.service';
import DatabaseServiceController from '../db.service';
import { AudioWorkletNodeMock } from '@test/mocks/audio-context.mock';

vi.mock("solid-services", () => ({
  useService: vi.fn()
}));

describe.skip('AudioService', () => {
  let audioService: ReturnType<typeof AudioService>;
  let mockAppService: ReturnType<typeof MockAppService>;

  beforeEach(() => {
    vi.clearAllMocks();

    let mockSoundEffectsService = MockSoundEffectsService();
    mockAppService = MockAppService();
    let mockAppSettingsService = MockAppSettingsService();
    let mockDbService = MockDbService();

    (useService as any).mockImplementation((service: any) => {
      if (service === SoundEffectsService) return () => mockSoundEffectsService;
      if (service === AppService) return () => mockAppService;
      if (service === AppSettingsService) return () => mockAppSettingsService;
      if (service === DatabaseServiceController) return () => mockDbService;
    });

    audioService = AudioService();
  });

  describe('initialize', () => {
    it('should initialize the audio service correctly', async () => {
      let core = { ...mockAppService.coreService() };
      mockAppService.coreService = vi.fn().mockImplementation(() => {
        return {
          ...core,
          midi_io_start: vi.fn().mockReturnValue(new AudioWorkletNodeMock()),
        };
      });

      const result = await audioService.initialize();
      expect(result).toBe(true);
      expect(audioService.initialized()).toBe(true);
    });

    it('should handle initialization errors', async () => {
      vi.spyOn(AppService.prototype, 'coreService').mockImplementation(() => {
        throw new Error('Initialization error');
      });
      const result = await audioService.initialize();
      expect(result).toBe(false);
      expect(audioService.initialized()).toBe(false);
    });
  });

  describe('onLoadSettings', () => {
    it('should load settings correctly', () => {
      audioService.onLoadSettings();
      expect(audioService.slotMode()).toBeDefined();
      expect(audioService.volume()).toBeDefined();
    });
  });

  describe('loadSoundfont', () => {
    it('should load soundfont correctly', async () => {
      const fileName = 'test.sf2';
      await audioService.loadSoundfont(fileName);
      expect(audioService.loadedSoundfont()).toBe(fileName);
    });

    it('should handle soundfont loading errors', async () => {
      vi.spyOn(global, 'fetch').mockImplementation(() => {
        throw new Error('Fetch error');
      });
      await expect(audioService.loadSoundfont('test.sf2')).rejects.toThrow('Fetch error');
    });
  });

  describe('setChannelVolume', () => {
    it('should set channel volume correctly', () => {
      const channel = 1;
      const volume = 100;
      audioService.setChannelVolume(channel, volume);
      expect(audioService.channels()?.[channel]?.volume).toBe(volume);
    });
  });

  // describe('setChannelPan', () => {
  //   it('should set channel pan correctly', () => {
  //     const channel = 1;
  //     const pan = 50;
  //     audioService.setChannelPan(channel, pan);
  //     expect(audioService.channels()[channel].pan).toBe(pan);
  //   });
  // });

  describe('resetChannelsToDefault', () => {
    it('should reset channels to default', () => {
      audioService.resetChannelsToDefault();
      expect(audioService.channels()).toEqual([]);
    });
  });

  // describe('onUserUpdateVolume', () => {
  //   it('should update user volume correctly', () => {
  //     const socketID = 'user1';
  //     audioService.onUserUpdateVolume(socketID);
  //     expect(audioService.usersService().getUserVolumeBySocketID(socketID)).toBeDefined();
  //   });
  // });

  // describe('onMuteUser', () => {
  //   it('should mute user correctly', () => {
  //     const socketID = 'user1';
  //     audioService.onMuteUser(socketID, true);
  //     expect(audioService.synthPlayer().mute_user).toHaveBeenCalledWith(socketID, true);
  //   });
  // });

  // describe('getSampleRate', () => {
  //   it('should return the correct sample rate', async () => {
  //     const sampleRate = await audioService.getSampleRate();
  //     expect(sampleRate).toBeGreaterThan(0);
  //   });
  // });

  describe('hasCustomSoundfont', () => {
    it('should check if custom soundfont exists', async () => {
      const name = 'custom.sf2';
      const result = await audioService.hasCustomSoundfont(name);
      expect(result).toBe(true);
    });
  });

  describe('saveCustomSoundfont', () => {
    it('should save custom soundfont correctly', async () => {
      const name = 'custom.sf2';
      const data = new Uint8Array();
      const result = await audioService.saveCustomSoundfont(name, data);
      expect(result).toBeDefined();
    });
  });
});