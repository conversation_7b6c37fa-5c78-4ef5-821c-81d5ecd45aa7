import { describe, it, expect, vi, beforeEach } from 'vitest';
import LoginService, { CurrentForm } from '~/services/login.service';
import NotificationService from '~/services/notification.service';
import SwalPR from '~/util/sweetalert';
import { logError } from '~/util/logger';
import { useService } from 'solid-services';
import { useNavigate, useAction } from '@solidjs/router';
import { MockAppService } from '@test/mocks/service.mocks';
import AppService from '~/services/app.service';

vi.mock('~/util/sweetalert');
vi.mock('~/util/logger');
vi.mock('solid-services');
vi.mock('@solidjs/router');
vi.mock('~/services/notification.service');

describe('LoginService', () => {
  let loginService: ReturnType<typeof LoginService>;
  let mockAppService: ReturnType<typeof AppService>;
  let mockNavigate: any;
  let mockLogoutAction: any;

  beforeEach(() => {
    mockAppService = MockAppService();
    mockNavigate = vi.fn();
    mockLogoutAction = vi.fn();

    (useService as any).mockReturnValue(() => mockAppService);
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useAction as any).mockReturnValue(mockLogoutAction);

    loginService = LoginService();
  });

  it('should initialize with default states', () => {
    expect(loginService.currentForm()).toBe(CurrentForm.Main);
    expect(loginService.checkingAutoLogin()).toBe(false);
    expect(loginService.loggedIn()).toBe(false);
    expect(loginService.currentLoggedInUsername()).toBeUndefined();
  });

  it('should handle onBeforeLogin correctly', () => {
    loginService.onBeforeLogin();

    expect(mockAppService.setActivatePageLoader).toHaveBeenCalledWith(true);
    expect(NotificationService.show).toHaveBeenCalledWith({
      id: expect.any(String),
      description: expect.any(String),
      type: 'info',
    });
  });

  it('should handle onLoggedIn correctly', () => {
    loginService.onLoggedIn();
    expect(mockAppService.setActivatePageLoader).toHaveBeenCalledWith(false);
    expect(NotificationService.show).toHaveBeenCalledWith({
      id: expect.any(String),
      description: expect.any(String),
      type: 'success',
    });
  });

  it('should handle tryLogout correctly', () => {
    const mockFire = vi.fn().mockResolvedValue({ isConfirmed: true });
    (SwalPR as any).mockReturnValue({ fire: mockFire });

    loginService.tryLogout();

    expect(mockFire).toHaveBeenCalledWith({
      title: expect.any(String),
      text: expect.any(String),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: expect.any(String),
    });
  });

  it('should handle logout correctly', async () => {
    await loginService.logout();

    expect(mockAppService.setActivatePageLoader).toHaveBeenCalledWith(true);
    expect(loginService.loggedIn()).toBe(false);
    expect(loginService.currentLoggedInUsername()).toBeUndefined();
    expect(mockLogoutAction).toHaveBeenCalled();
    expect(NotificationService.show).toHaveBeenCalledWith({
      description: expect.any(String),
      type: 'success',
    });
  });

  it('should handle logout error correctly', async () => {
    mockLogoutAction.mockRejectedValue(new Error('Logout failed'));

    await loginService.logout();

    expect(logError).toHaveBeenCalledWith('[logout] Error: Logout failed');
    expect(NotificationService.show).toHaveBeenCalledWith({
      title: expect.any(String),
      description: expect.any(String),
      type: 'danger',
    });
    expect(mockAppService.setActivatePageLoader).toHaveBeenCalledWith(false);
  });
});