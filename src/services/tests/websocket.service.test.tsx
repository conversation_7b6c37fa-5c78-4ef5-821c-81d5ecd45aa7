import { MockAppService, MockAppSettingsService, MockSoundEffectsService } from '@test/mocks/service.mocks';
import { useService } from 'solid-services';
import { beforeEach, describe, expect, it } from 'vitest';
import { CreateRoomParam } from '~/proto/server-message';
import AppService from '~/services/app.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import WebsocketService from '~/services/websocket.service';
import { ServerMsg } from '~/types/websocket.types';
import { handleServerMessageEncode } from '~/util/websocket.util';

vi.mock("solid-services", () => ({
  useService: vi.fn()
}));

vi.mock('~/services/app.service');
vi.mock('~/services/notification.service');
vi.mock('~/services/settings-storage.service');
vi.mock('~/services/sound-effects.service');

describe('WebsocketService', () => {
  let websocketService: ReturnType<typeof WebsocketService>;
  let appServiceMock: ReturnType<typeof AppService>;
  let sfxServiceMock: ReturnType<typeof SoundEffectsService>;
  let appSettingsServiceMock: ReturnType<typeof AppSettingsService>;

  beforeEach(() => {
    appServiceMock = MockAppService();
    sfxServiceMock = MockSoundEffectsService();
    appSettingsServiceMock = MockAppSettingsService();

    vi.mocked(useService as any).mockImplementation((service: any) => {
      if (service === AppService) return () => appServiceMock;
      if (service === SoundEffectsService) return () => sfxServiceMock;
      if (service === AppSettingsService) return () => appSettingsServiceMock;
    });

    websocketService = WebsocketService();
  });

  it('should initialize correctly', () => {
    websocketService.initialize();
    expect(appSettingsServiceMock.persistSettingsEvent.listen).toHaveBeenCalled();
    expect(appServiceMock.appStateEvents.listen).toHaveBeenCalled();
  });

  it('should connect correctly', async () => {
    const onCompleteMock = vi.fn();
    appServiceMock.coreService = vi.fn().mockImplementation(() => {
      return {
        websocket_connect: vi.fn().mockImplementation((_url, onConnect, _onError, _onClose) => {
          onConnect();
        }),
      };
    });

    await websocketService.connect('wsIdentity', onCompleteMock);
    expect(onCompleteMock).toHaveBeenCalled();
    // expect(NotificationService.hide).toHaveBeenCalledWith(IDS.SERVER_DISCONNECT);
    // expect(NotificationService.show).toHaveBeenCalled();
  });

  it.skip('should join room by name correctly', () => {
    websocketService.joinRoomByName('roomName');
    expect(appServiceMock.coreService()?.send_app_action).toHaveBeenCalledWith(expect.any(Object));
  });

  it.skip('should join next available lobby correctly', () => {
    websocketService.joinNextAvailableLobby();
    expect(appServiceMock.coreService()?.send_app_action).toHaveBeenCalledWith(expect.any(Object));
  });

  it.skip('should join room by ID correctly', () => {
    websocketService.joinRoomByID('roomID');
    expect(appServiceMock.coreService()?.send_app_action).toHaveBeenCalledWith(expect.any(Object));
  });

  it.skip('should disconnect correctly', () => {
    websocketService.disconnect();
    expect(appSettingsServiceMock.persistSettings).toHaveBeenCalled();
    expect(appServiceMock.coreService()?.websocket_disconnect).toHaveBeenCalled();
  });

  it.skip('should emit messages correctly', () => {
    const msg = ["Hello"] as ServerMsg;
    websocketService.postEmitMessage(msg);
    expect(handleServerMessageEncode).toHaveBeenCalledWith(msg);
  });

  it('should create or update room correctly', async () => {
    let params = CreateRoomParam.create({ RoomName: 'testRoom' });
    await websocketService.createOrUpdateRoom(params);
  });

  it.skip('should emit typing status correctly', () => {
    websocketService.emitIsTyping(true);
    // expect(isTypingSubjectMock.next).toHaveBeenCalledWith(true);
  });
});