import { <PERSON><PERSON><PERSON>ider } from "@hope-ui/solid";
import { ParentComponent } from "solid-js";
import { ServiceRegistry } from "solid-services";
import ThemeConfig from "../../../util/theme-config";

const TestBed: ParentComponent = (props) => {
  return (
    <HopeProvider config={ThemeConfig}>
      <ServiceRegistry>
        {props.children}
      </ServiceRegistry>
    </HopeProvider>
  );
};

export default TestBed;