import {useService} from "solid-services";
import {AppStateEffects, AppStateEffects_Action} from "~/proto/pianorhythm-effects";
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {AppStateActions, AppStateActions_Action} from "~/proto/pianorhythm-actions";
import UsersService from "~/services/users-service";
import DatabaseServiceController from "~/services/db.service";
import {CoreService} from "~/types/app.types";
import {createEventBus, EventBus} from "@solid-primitives/event-bus";
import {MockCoreService, MockDatabaseServiceController} from "@test/mocks/service.mocks";

vi.mock('solid-services');

describe('UsersService', () => {
    let usersService: ReturnType<typeof UsersService>;
    let mockDbService: ReturnType<typeof MockDatabaseServiceController>;
    let mockCoreService: CoreService;
    let mockEventBus: EventBus<AppStateEffects>;

    beforeEach(() => {
        mockDbService = MockDatabaseServiceController();
        mockCoreService = MockCoreService();

        mockEventBus = createEventBus();

        (useService as any).mockImplementation((service: any) => {
            if (service === DatabaseServiceController) return () => mockDbService;
        });

        usersService = UsersService();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    it('should initialize and load muted users from the database', async () => {
        mockDbService.getAllKeys.mockResolvedValueOnce(['socket1']).mockResolvedValueOnce(['socket2']);
        await usersService.initialize(mockCoreService, mockEventBus);

        expect(mockDbService.getAllKeys).toHaveBeenCalledTimes(2);

        expect(mockCoreService.send_app_action).toHaveBeenCalledWith(
            AppStateActions.create({
                action: AppStateActions_Action.SetUserChatMuted,
                boolValue: true,
                sourceSocketID: 'socket1'
            })
        );

        expect(mockCoreService.send_app_action).toHaveBeenCalledWith(
            AppStateActions.create({
                action: AppStateActions_Action.SetUserMuted,
                boolValue: true,
                sourceSocketID: 'socket2'
            })
        );
    });

    it('should add a muted notes user and reload muted notes', async () => {
        mockDbService.getAllKeys.mockResolvedValueOnce(['socket1']);
        await usersService.addMutedNotesUser('socket1');

        expect(mockDbService.put).toHaveBeenCalledWith('socket1', true, expect.anything());
        expect(mockDbService.getAllKeys).toHaveBeenCalledWith(expect.anything());
    });

    it('should remove a muted notes user and reload muted notes', async () => {
        mockDbService.getAllKeys.mockResolvedValueOnce([]);
        await usersService.removeMutedNotesUser('socket1');

        expect(mockDbService.deleteKey).toHaveBeenCalledWith('socket1', expect.anything());
        expect(mockDbService.getAllKeys).toHaveBeenCalledWith(expect.anything());
    });

    it('should add a muted chat user and reload muted chat', async () => {
        mockDbService.getAllKeys.mockResolvedValueOnce(['socket2']);
        await usersService.addMutedChatUser('socket2');

        expect(mockDbService.put).toHaveBeenCalledWith('socket2', true, expect.anything());
        expect(mockDbService.getAllKeys).toHaveBeenCalledWith(expect.anything());
    });

    it('should remove a muted chat user and reload muted chat', async () => {
        mockDbService.getAllKeys.mockResolvedValueOnce([]);
        await usersService.removeMutedChatUser('socket2');

        expect(mockDbService.deleteKey).toHaveBeenCalledWith('socket2', expect.anything());
        expect(mockDbService.getAllKeys).toHaveBeenCalledWith(expect.anything());
    });

    it('should return a user by socket ID', async () => {
        const mockUsers = [{socketID: 'socket1'}, {socketID: 'socket2'}];
        mockDbService.getAllKeys.mockResolvedValue([]);
        await usersService.initialize(mockCoreService, mockEventBus);

        // set users via the mock event bus
        mockEventBus.emit({
            action: AppStateEffects_Action.UsersSet,
            clientSideUserDtoList: {list: mockUsers as any}
        });

        const user = usersService.getUserBySocketID('socket1');
        expect(user).toEqual(mockUsers[0]);
    });

    it('should return undefined if no user matches the socket ID', () => {
        (usersService as any).users = [{socketID: 'socket1'}];

        const user = usersService.getUserBySocketID('socket3');
        expect(user).toBeUndefined();
    });
});