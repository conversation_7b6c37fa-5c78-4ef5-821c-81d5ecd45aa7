import { beforeEach, describe, expect, it, vi } from 'vitest';
import I18nService from '../i18n.service';
import { useService } from 'solid-services';
import { MockAppSettingsService } from '@test/mocks/service.mocks';
import AppSettingsService from '../settings-storage.service';

// Mock dependencies
vi.mock('~/i18n/i18nProvider', () => ({
  useI18n: () => ({
    t: vi.fn((key, options) => `translated:${options.ns}:${key}`),
    language: 'en',
    changeLanguage: vi.fn().mockResolvedValue(undefined)
  })
}));

vi.mock('solid-services', () => ({
  useService: vi.fn(() => ({
    getSetting: vi.fn(() => 'en'),
    saveSetting: vi.fn()
  }))
}));

vi.mock('./notification.service', () => ({
  default: {
    show: vi.fn()
  }
}));

describe('I18nService', () => {
  let service: ReturnType<typeof I18nService>;

  beforeEach(() => {
    vi.clearAllMocks();

    let mockAppSettingsService = MockAppSettingsService();
    (useService as any).mockImplementation((service: any) => {
      if (service === AppSettingsService) return () => mockAppSettingsService;
    });

    service = I18nService();
  });

  describe('t_roomPageBottomBarButtons', () => {
    it('should correctly format bottomBar button translation keys', () => {
      // Test with a simple label
      const result = service.t_roomPageBottomBarButtons('play');
      expect(result).toBe('translated:roomPage:bottomBar.buttons.play');
    });

    it('should handle empty string label', () => {
      const result = service.t_roomPageBottomBarButtons('');
      expect(result).toBe('translated:roomPage:bottomBar.buttons.');
    });

    it('should handle complex labels with dots and special characters', () => {
      const result = service.t_roomPageBottomBarButtons('pause.special');
      expect(result).toBe('translated:roomPage:bottomBar.buttons.pause.special');
    });

    it('should pass along additional parameters correctly', () => {
      // Test by checking internal implementation
      const t_roomPageSpy = vi.spyOn(service, 't_roomPage');

      let result = service.t_roomPageBottomBarButtons('test');

      expect(result).toBe('translated:roomPage:bottomBar.buttons.test');
    });

    it.skip('should be affected by active language', () => {
      // Mock the activeLanguage to return undefined/null to test the guard clause
      const result = service.t_roomPageBottomBarButtons('test');
      expect(result).toBeUndefined();
    });
  });
});