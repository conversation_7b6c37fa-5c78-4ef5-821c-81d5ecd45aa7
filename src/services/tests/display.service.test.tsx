import { ServiceGetter, useService } from "solid-services";
import { beforeEach, describe, expect, it, vi } from "vitest";
import AppService from "~/services/app.service";
import DisplaysService, { DEFAULT_DISPLAYS, SOLO_DISPLAYS } from "~/services/displays.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { AppSoundFx2 } from "~/types/app.types";
import { MockAppService, MockAppSettingsService, MockSoundEffectsService } from "../../../tests/mocks/service.mocks";

vi.mock("solid-services", () => ({
  useService: vi.fn()
}));

describe("DisplaysService", () => {
  let displaysService: ServiceGetter<ReturnType<typeof DisplaysService>>;
  let mockSoundEffectsService: ReturnType<typeof SoundEffectsService>;
  let mockAppService: ReturnType<typeof AppService>;
  let mockAppSettingsService: ReturnType<typeof AppSettingsService>;

  beforeEach(() => {
    mockSoundEffectsService = MockSoundEffectsService();
    mockAppService = MockAppService();
    mockAppSettingsService = MockAppSettingsService();

    (useService as any).mockImplementation((service: any) => {
      if (service === SoundEffectsService) return () => mockSoundEffectsService;
      if (service === AppService) return () => mockAppService;
      if (service === AppSettingsService) return () => mockAppSettingsService;
    });

    let service = DisplaysService();
    displaysService = () => service;
  });

  it("should initialize correctly", () => {
    displaysService().initialize();
    expect(displaysService().initialized()).toBe(true);
    expect(mockAppService.appStateEvents.listen).toHaveBeenCalled();
  });

  it("should set display correctly", () => {
    displaysService().setDisplay("CHAT_MESSAGES", true);
    expect(displaysService().displays.CHAT_MESSAGES).toBe(true);
  });

  it.skip("should set main UI elements correctly", () => {
    displaysService().setDisplayMainUI(true);
    expect(displaysService().displays.SIDEBAR_LIST).toBe(true);
    expect(displaysService().displays.INSTRUMENT_DOCK).toBe(true);
    expect(displaysService().displays.CHAT_MESSAGES).toBe(true);
    expect(displaysService().displays.BOTTOM_BAR).toBe(true);
    expect(displaysService().displays.SCENE_WIDGET_BUTTONS).toBe(true);
  });

  it("should hide all displays", () => {
    displaysService().hideAll();
    Object.entries(displaysService().displays).forEach(([key, value]) => {
      expect(value, `Display ${key}`).toBe(false);
    });
  });

  it("should close all solo displays when one is opened", () => {
    displaysService().setDisplay("NEW_ROOM_MODAL", true);
    SOLO_DISPLAYS.forEach(key => {
      if (key !== "NEW_ROOM_MODAL") {
        expect(displaysService().displays[key]).toBe(false);
      }
    });
  });

  it("should reset state on disconnect", () => {
    displaysService().onDisconnect();
    expect(displaysService().autoHideBottomBar()).toBe(false);
    expect(displaysService().docsURLParameters()).toBeUndefined();
    expect(displaysService().docsModalTitle()).toBeUndefined();
    expect(displaysService().displays).toEqual(DEFAULT_DISPLAYS);
  });

  it("should play sound effects on display change", () => {
    displaysService().setDisplay("NEW_ROOM_MODAL", true);
    expect(mockSoundEffectsService.playSFX_ui2).toHaveBeenCalledWith(AppSoundFx2.OPEN_MENU, { volume: 0.07 });

    displaysService().setDisplay("NEW_ROOM_MODAL", false);
    expect(mockSoundEffectsService.playSFX_ui2).toHaveBeenCalledWith(AppSoundFx2.CLOSE_MENU, { volume: 0.07 });
  });

  it("should update anyModalsOpened state correctly", () => {
    displaysService().setDisplay("NEW_ROOM_MODAL", true);
    expect(displaysService().anyModalsOpened()).toBe(true);

    displaysService().setDisplay("NEW_ROOM_MODAL", false);
    expect(displaysService().anyModalsOpened()).toBe(false);
  });

  it.skip("should update settings based on app settings service", () => {
    vi.mocked(mockAppSettingsService.getSetting).mockImplementation((key: string) => {
      const settings: Record<string, boolean> = {
        "DISPLAY_CHAT": true,
        "DISPLAY_FPS": true,
        "DISPLAY_INST_DOCK": true,
        "GRAPHICS_DISPLAY_RENDER_STATS": true,
        "DISPLAY_SCENE_WIDGET_BUTTONS": true,
        "AUTOHIDE_BOTTOMBAR": true
      };
      return settings[key];
    });

    displaysService().initialize();

    expect(displaysService().displays.CHAT_MESSAGES).toBe(true);
    expect(displaysService().displays.FPS).toBe(true);
    expect(displaysService().displays.INSTRUMENT_DOCK).toBe(true);
    expect(displaysService().displays.RENDERING_STATS).toBe(true);
    expect(displaysService().displays.SCENE_WIDGET_BUTTONS).toBe(true);
    expect(displaysService().autoHideBottomBar()).toBe(true);
  });
});