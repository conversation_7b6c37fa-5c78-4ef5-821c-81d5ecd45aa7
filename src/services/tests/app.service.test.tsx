import {createComponent, onMount} from "solid-js";
import {ServiceGetter, ServiceRegistry, useService} from 'solid-services';
import {describe, expect, it, test, vi} from 'vitest';
import {WelcomeDto} from "~/proto/client-message";
import {AppStateActions, AppStateActions_Action} from "~/proto/pianorhythm-actions";
import {AppStateEffects, AppStateEffects_Action} from "~/proto/pianorhythm-effects";
import {AppStateEvents} from "~/proto/pianorhythm-events";
import {DeepPartial, Roles, UserClientDto, UserDto, UserStatus} from "~/proto/user-renditions";
import {CurrentPage, PianoRhythmSceneMode} from "~/types/app.types";
import {DEFAULT_UserNotificationSettings, UserNotificationSettings} from "~/types/user.types";
import AppService from '../app.service';

type AppServiceSutType = () => ReturnType<typeof AppService>;

const createUserClientDto = (dto: DeepPartial<UserDto>) => {
    return UserClientDto.create({userDto: UserDto.create(dto)});
};

const createWelcomeDto = (userDto?: DeepPartial<UserDto>, dto?: DeepPartial<WelcomeDto>) => {
    let welcomeDto = WelcomeDto.create({
        userClientDto: createUserClientDto(userDto ?? {}),
        ...dto
    });

    let effect = AppStateEffects.create({
        action: AppStateEffects_Action.OnWelcome,
        welcomeDto: welcomeDto
    });

    return {welcomeDto: welcomeDto, effect};
};

export const AppServiceTest = test.extend({
    service: async ({}, use: (arg0: ServiceGetter<ReturnType<typeof AppService>>) => Promise<any>) => {
        const spy = vi.fn();
        const SutService = () => {
            spy();
            return AppService();
        };

        const Test = () => {
            let service = useService(SutService);
            onMount(async () => {
                await use(service);
            });
            return undefined;
        };

        createComponent(ServiceRegistry, {
            get children() {
                return createComponent(Test, {});
            }
        });
    }
});

describe('AppService', () => {
    // Skip for now. Error: WebAssembly.instantiate(): BufferSource argument is empty
    AppServiceTest.skip('should load Core wasm', async ({service}: { service: AppServiceSutType; }) => {
        // await service().loadCoreWasm();
        expect(service().coreWasmLoaded()).toBe(true);
    });

    // Should be moved to theme service tests
    AppServiceTest.skip('should update theme colors', async ({service}: { service: AppServiceSutType; }) => {
        // service().setTheme(AppThemes.THEME_1);
        // expect(service().themeColors.primary.toUpperCase()).toBe("#1B2430".toUpperCase());
    });

    AppServiceTest('should set client after welcome event', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let targetUsername = "test";
        let {welcomeDto, effect} = createWelcomeDto({username: targetUsername});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        let serviceClient = service().client();
        expect(serviceClient).toBeDefined();
        expect(serviceClient.username).toBe(targetUsername);
        expect(service().welcomeEvent()).toBeDefined();
        expect(WelcomeDto.create(service().welcomeEvent())).toEqual(welcomeDto);
    });

    AppServiceTest('should detect member after welcome event', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({roles: [Roles.MEMBER]});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().isClientMember()).toBe(true);
    });

    AppServiceTest('should deactivate page loader after room stage loaded event', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        let event = AppStateEvents.RoomStageLoaded;
        service().setActivatePageLoader(true);

        // act
        service().onHandleAppEvent(event);

        // assert
        expect(service().activatePageLoader()).toBe(false);
    });

    AppServiceTest('should detect admin after welcome event', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({roles: [Roles.ADMIN]});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().isClientAdmin()).toBe(true);
    });

    AppServiceTest('should detect pro member after welcome event', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        let {welcomeDto, effect} = createWelcomeDto({roles: [Roles.MEMBER]});
        // Set isProMember directly on the UserDto
        welcomeDto.userClientDto!.userDto!.isProMember = true;
        // Re-encode with the updated welcomeDto
        effect.welcomeDto = welcomeDto;
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().isClientProMember()).toBe(true);
    });

    AppServiceTest('should detect moderator after welcome event', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        let {effect} = createWelcomeDto({roles: [Roles.MODERATOR]});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().isClientMod()).toBe(true);
    });

    AppServiceTest('should detect sheet music editor access', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({roles: [Roles.SHEETMUSICEDITOR]});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().doesClientHaveSheetMusicFullAccess()).toBe(true);
    });

    AppServiceTest('should detect midi music editor access', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({roles: [Roles.MIDIMUSICEDITOR]});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().doesClientHaveMidiMusicFullAccess()).toBe(true);
    });

    AppServiceTest('should set and get current page', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const targetPage = CurrentPage.Room;

        // act
        service().setCurrentPage(targetPage);

        // assert
        expect(service().currentPage()).toBe(targetPage);
        expect(service().isRoomCurrentPage()).toBe(true);
    });

    AppServiceTest('should set and get scene mode', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const targetMode = PianoRhythmSceneMode.TWO_D;

        // act
        service().setSceneMode(targetMode);

        // assert
        expect(service().sceneMode()).toBe(targetMode);
        expect(service().is2DMode()).toBe(true);
    });

    AppServiceTest('should check if client is in room', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const testRoomId = "test-room-123";

        // Create a JoinedRoomSuccess effect to set the room ID
        const effect = AppStateEffects.create({
            action: AppStateEffects_Action.JoinedRoomSuccess,
            joinedRoomData: {
                roomID: testRoomId,
                roomName: "Test Room",
                roomOwner: "owner-id",
                roomSettings: {}
            }
        });

        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().roomID()).toBe(testRoomId);
        expect(service().isClientInRoom(testRoomId)).toBe(true);
    });

    AppServiceTest('should set and get page loader tooltip', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const testTooltip = "Loading room...";

        // act
        service().setActivePageLoaderToolTip(testTooltip);

        // assert
        expect(service().activePageLoaderToolTip()).toBe(testTooltip);
    });

    AppServiceTest('should detect Do Not Disturb status', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({status: UserStatus.DoNotDisturb});
        let bytes = AppStateEffects.encode(effect).finish();

        // act
        service().onHandleAppEffects(bytes);

        // assert
        expect(service().isClientInDoNotDisturb()).toBe(true);
        expect(service().getClientStatus()).toBe(UserStatus.DoNotDisturb);
    });

    AppServiceTest('should create and encode app action', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const action = AppStateActions.create({
            action: AppStateActions_Action.SetIsMobile,
            boolValue: true
        });

        // act
        const result = service().createAppAction(action);

        // assert
        expect(Buffer.isBuffer(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);

        // Verify we can decode it back
        const decoded = AppStateActions.decode(result);
        expect(decoded.action).toBe(AppStateActions_Action.SetIsMobile);
        expect(decoded.boolValue).toBe(true);
    });

    AppServiceTest('should encode and decode app effects', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const {welcomeDto, effect} = createWelcomeDto({username: "testuser"});

        // act
        const encoded = service().encodeAppEffect(effect);
        const decoded = service().decodeAppEffect(encoded);

        // assert
        expect(Buffer.isBuffer(encoded)).toBe(true);
        expect(decoded.action).toBe(AppStateEffects_Action.OnWelcome);
        expect(decoded.welcomeDto?.userClientDto?.userDto?.username).toBe("testuser");
    });

    AppServiceTest('should toggle page loader state', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setActivatePageLoader(true);

        // assert
        expect(service().activatePageLoader()).toBe(true);

        // act again
        service().setActivatePageLoader(false);

        // assert again
        expect(service().activatePageLoader()).toBe(false);
    });

    AppServiceTest('should toggle page loader animation state', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setPagerLoaderAnimating(true);

        // assert
        expect(service().pagerLoaderAnimating()).toBe(true);

        // act again
        service().setPagerLoaderAnimating(false);

        // assert again
        expect(service().pagerLoaderAnimating()).toBe(false);
    });

    AppServiceTest('should clear client data', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        let {effect} = createWelcomeDto({username: "testuser"});
        let bytes = AppStateEffects.encode(effect).finish();
        service().onHandleAppEffects(bytes);
        expect(service().client().username).toBe("testuser");

        // act
        service().clearClient();

        // assert
        expect(service().client().username).toBeUndefined();
        expect(service().clientLoaded()).toBe(false);
    });

    AppServiceTest('should update client data with onClientLoaded', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        const userDto = UserDto.create({username: "initialuser"});
        const initialClient = UserClientDto.create({userDto});

        // act - first load
        service().onClientLoaded(initialClient);

        // assert
        expect(service().client().username).toBe("initialuser");
        expect(service().clientLoaded()).toBe(true);

        // act - update existing client
        const updatedUserDto = UserDto.create({username: "updateduser"});
        const updatedClient = UserClientDto.create({userDto: updatedUserDto});
        service().onClientLoaded(updatedClient);

        // assert
        expect(service().client().username).toBe("updateduser");
    });

    AppServiceTest('should toggle maintenance mode', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setMaintenanceModeActive(true);

        // assert
        expect(service().maintenanceModeActive()).toBe(true);

        // act again
        service().setMaintenanceModeActive(false);

        // assert again
        expect(service().maintenanceModeActive()).toBe(false);
    });

    AppServiceTest('should toggle server service down status', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setServerServiceDown(true);

        // assert
        expect(service().serverServiceDown()).toBe(true);

        // act again
        service().setServerServiceDown(false);

        // assert again
        expect(service().serverServiceDown()).toBe(false);
    });

    AppServiceTest('should toggle app version mismatch status', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setAppVersionMismatched(true);

        // assert
        expect(service().appVersionMismatched()).toBe(true);

        // act again
        service().setAppVersionMismatched(false);

        // assert again
        expect(service().appVersionMismatched()).toBe(false);
    });

    AppServiceTest('should set and get user notification settings', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        const testSettings: UserNotificationSettings = {
            allowEmittingMyStatusToFriends: false,
            getEmailsForMidiMusicChanges: false,
            getEmailsForSheetMusicChanges: false,
            getNotificationsForFriendsComingOnline: false,
            getNotificationsForFriendsGoingOffline: false
        };

        // act
        service().setUserNotificationSettings(testSettings);

        // assert
        expect(service().userNotificationSettings()).toEqual(testSettings);
    });

    AppServiceTest('should toggle client self notes muted state', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange & act
        service().setClientIsSelfNotesMuted(true);

        // assert
        expect(service().clientIsSelfNotesMuted()).toBe(true);

        // act again
        service().setClientIsSelfNotesMuted(false);

        // assert again
        expect(service().clientIsSelfNotesMuted()).toBe(false);
    });

    AppServiceTest('should toggle client self chat muted state', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setClientIsSelfChatMuted(true);

        // assert
        expect(service().clientIsSelfChatMuted()).toBe(true);

        // act again
        service().setClientIsSelfChatMuted(false);

        // assert again
        expect(service().clientIsSelfChatMuted()).toBe(false);
    });

    AppServiceTest('should check if user is room owner', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const ownerSocketId = "owner-socket-123";
        service().setRoomOwner(ownerSocketId);

        // act & assert
        expect(service().isUserRoomOwner(ownerSocketId)).toBe(true);
        expect(service().isUserRoomOwner("different-socket")).toBe(false);
    });

    AppServiceTest('should check if client is room owner', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const ownerSocketId = "owner-socket-123";
        service().setRoomOwner(ownerSocketId);

        // Create a client with matching socket ID
        const userDto = UserDto.create({socketID: ownerSocketId});
        const clientDto = UserClientDto.create({userDto});
        service().onClientLoaded(clientDto);

        // act & assert
        expect(service().isClientRoomOwner()).toBe(true);

        // Change room owner
        service().setRoomOwner("different-owner");
        expect(service().isClientRoomOwner()).toBe(false);
    });

    AppServiceTest('should check if a socket ID matches client socket ID', async ({service}: {
        service: AppServiceSutType;
    }) => {
        // arrange
        const socketId = "test-socket-123";
        const userDto = UserDto.create({socketID: socketId});
        const clientDto = UserClientDto.create({userDto});
        service().onClientLoaded(clientDto);

        // act & assert
        expect(service().isClient(socketId)).toBe(true);
        expect(service().isClient("different-socket")).toBe(false);
    });

    AppServiceTest('should toggle initialized state', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setInitialized(true);

        // assert
        expect(service().initialized()).toBe(true);

        // act again
        service().setInitialized(false);

        // assert again
        expect(service().initialized()).toBe(false);
    });

    AppServiceTest('should toggle first time join room state', async ({service}: { service: AppServiceSutType; }) => {
        // arrange & act
        service().setFirstTimeJoinRoom(false);

        // assert
        expect(service().firstTimeJoinRoom()).toBe(false);

        // act again
        service().setFirstTimeJoinRoom(true);

        // assert again
        expect(service().firstTimeJoinRoom()).toBe(true);
    });

    AppServiceTest('should get room name', async ({service}: { service: AppServiceSutType; }) => {
        // We can't directly test setting the room name since setRoomName is not exposed
        // in the service's return object. It's only used internally.
        // Instead, we'll test that the roomName getter exists and works

        // assert
        expect(typeof service().roomName).toBe('function');
    });

    AppServiceTest('should set and get current room param', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const testRoomParam = {RoomID: "test-room-123", RoomName: "Test Room"};

        // act
        service().setCurrentRoomParam(testRoomParam as any);

        // assert
        expect(service().currentRoomParam()).toEqual(testRoomParam);
    });

    AppServiceTest('should determine if client can interact', async ({service}: { service: AppServiceSutType; }) => {
        // arrange - initially all conditions are false
        expect(service().canInteract()).toBe(false);

        // Set clientLoaded to true
        const userDto = UserDto.create({username: "testuser"});
        const clientDto = UserClientDto.create({userDto});
        service().onClientLoaded(clientDto);
        expect(service().clientLoaded()).toBe(true);

        // Still false because other conditions aren't met
        expect(service().canInteract()).toBe(false);

        // Set initialized to true
        service().setInitialized(true);

        // Still false because coreWasmLoaded is false
        expect(service().canInteract()).toBe(false);

        // We can't directly test the full canInteract() functionality
        // since we can't mock coreWasmLoaded in the test environment
    });

    AppServiceTest('should handle app events', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const eventSpy = vi.fn();
        service().appStateEvents.listen(eventSpy);

        // act
        const result = service().onHandleAppEvent(AppStateEvents.RoomStageLoaded);

        // assert
        expect(result).toBe(AppStateEvents.RoomStageLoaded);
        expect(eventSpy).toHaveBeenCalledWith(AppStateEvents.RoomStageLoaded);
    });

    AppServiceTest('should clear client on disconnect', async ({service}: { service: AppServiceSutType; }) => {
        // arrange
        const userDto = UserDto.create({username: "testuser"});
        const clientDto = UserClientDto.create({userDto});
        service().onClientLoaded(clientDto);
        expect(service().clientLoaded()).toBe(true);

        // act
        service().onDisconnect();

        // assert - onDisconnect is debounced, so we can only test the end result
        // after the debounce period, which we can't easily do in a unit test
        // Instead, we'll test that the method exists and is callable
        expect(typeof service().onDisconnect).toBe('function');
    });

    it('registers a service if it does not exist', () => {
        const spy = vi.fn();
        const SutService = () => {
            spy();
            return AppService();
        };

        const Test = () => {
            const service = useService(SutService);
            service();
            expect(spy).toBeCalledTimes(1);
            const myServiceAgain = useService(SutService);
            myServiceAgain();
            expect(spy).toBeCalledTimes(1);
            return undefined;
        };

        createComponent(ServiceRegistry, {
            get children() {
                return createComponent(Test, {});
            }
        });
    });
});