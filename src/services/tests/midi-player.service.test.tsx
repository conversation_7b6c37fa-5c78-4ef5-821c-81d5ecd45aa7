import {MockAppService, MockDisplaysService} from "@test/mocks/service.mocks";
import MidiPlayerService from "../midi-player.service";
import {useService} from "solid-services";
import DisplaysService from "../displays.service";
import AppService from "../app.service";
import {AppStateEffects, AppStateEffects_Action} from "~/proto/pianorhythm-effects";
import {AppMidiSequencerEventType} from "~/proto/pianorhythm-app-renditions";
import {DefaultMidiPlayerCurrentState} from "~/types/midi-player.types";
import {describe, it, expect, vi, beforeEach} from 'vitest';

vi.mock('solid-services');

describe('MidiPlayerService', () => {
    let midiPlayerService: ReturnType<typeof MidiPlayerService>;
    let mockAppService: ReturnType<typeof MockAppService>;
    let mockDisplaysService: ReturnType<typeof MockDisplaysService>;

    beforeEach(() => {
        mockAppService = MockAppService();
        mockDisplaysService = MockDisplaysService();

        (useService as any).mockImplementation((service: any) => {
            if (service === AppService) return () => mockAppService;
            if (service === DisplaysService) return () => mockDisplaysService;
        });

        midiPlayerService = MidiPlayerService();
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('initialize', () => {
        it('should initialize only once', () => {
            midiPlayerService.initialize();
            expect(mockAppService.wasmMidiSequencerEffects.listen).toHaveBeenCalledTimes(1);

            // Second call should not re-initialize
            midiPlayerService.initialize();
            expect(mockAppService.wasmMidiSequencerEffects.listen).toHaveBeenCalledTimes(1);
        });
    });

    describe('onDisconnect', () => {
        it('should reset state and clear loaded MIDI file name', () => {
            midiPlayerService.onDisconnect();
            expect(midiPlayerService.midiSequencerState).toEqual(DefaultMidiPlayerCurrentState);
            expect(midiPlayerService.loadedMidiFileName()).toBeUndefined();
        });
    });

    describe('onUpdateTotalTime', () => {
        it('should update total time and formatted time when input is valid', () => {
            midiPlayerService.onUpdateTotalTime(120);
            expect(midiPlayerService.totalTime()).toBe(120000);
            expect(midiPlayerService.totalTimeFormatted()).toBe('02:00.00');
        });

        it('should not update total time when input is undefined', () => {
            midiPlayerService.onUpdateTotalTime(undefined);
            expect(midiPlayerService.totalTime()).toBeUndefined();
            expect(midiPlayerService.totalTimeFormatted()).toBe('0:00');
        });
    });

    describe('handleMidiSequencerEffects', () => {
        it('should handle FILE_OUTPUT event and update state', () => {
            const payload = AppStateEffects.encode({
                action: AppStateEffects_Action.MidiSequencerEvent,
                midiSequencerEvent: {
                    eventType: AppMidiSequencerEventType.FILE_OUTPUT,
                    fileName: 'test.mid',
                    totalTime: 300,
                    lyrics: ['lyric1', 'lyric2'],
                    trackNames: ['track1', 'track2'],
                    currentBPM: 120,
                    ppq: 480,
                    programChanges: [],
                    tempoChanges: [],
                    copyrightNotice: ['Copyright'],
                    markerTexts: [],
                    texts: [],
                    isVPSheet: false,
                },
            }).finish();

            midiPlayerService.handleMidiSequencerEffects(payload);

            expect(mockDisplaysService.setDisplay).toHaveBeenCalledWith('MIDI_PLAYER_UI', true);
            expect(midiPlayerService.midiSequencerState.fileName).toBe('test.mid');
            expect(midiPlayerService.midiSequencerState.totalTime).toBe(300);
            expect(midiPlayerService.midiSequencerState.lyrics).toEqual(['lyric1', 'lyric2']);
        });

        it('should reset state on FINISHED event', () => {
            const payload = AppStateEffects.encode({
                action: AppStateEffects_Action.MidiSequencerEvent,
                midiSequencerEvent: {
                    eventType: AppMidiSequencerEventType.FINISHED,
                    fileName: 'test.mid',
                    totalTime: 300,
                    lyrics: ['lyric1', 'lyric2'],
                    trackNames: ['track1', 'track2'],
                    currentBPM: 120,
                    ppq: 480,
                    programChanges: [],
                    copyrightNotice: [],
                    texts: [],
                    markerTexts: [],
                    tempoChanges: []
                },
            }).finish();

            midiPlayerService.handleMidiSequencerEffects(payload);

            expect(midiPlayerService.midiSequencerState).toEqual(DefaultMidiPlayerCurrentState);
            expect(midiPlayerService.loadedMidiFileName()).toBeUndefined();
        });
    });

    describe('formatTime', () => {
        it('should format positive time correctly', () => {
            const formattedTime = midiPlayerService.formatTime(125, false);
            expect(formattedTime).toBe('02:05');
        });

        it('should format negative time correctly', () => {
            const formattedTime = midiPlayerService.formatTime(-125, false);
            expect(formattedTime).toBe('-02:05');
        });

        it('should handle zero time correctly', () => {
            const formattedTime = midiPlayerService.formatTime(0, false);
            expect(formattedTime).toBe('00:00');
        });
    });
});