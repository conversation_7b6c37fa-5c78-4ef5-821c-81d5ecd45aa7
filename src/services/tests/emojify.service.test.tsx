import { describe, it, expect } from 'vitest';
import EmojifyService from '../emojify.service';

// emojify.service.test.ts

describe('EmojifyService', () => {
  const emojifyService = EmojifyService();

  describe('encode method', () => {
    it('should return the input string unchanged', () => {
      const input = 'Hello world';
      expect(emojifyService.encode(input)).toBe(input);
    });

    it('should handle empty string', () => {
      expect(emojifyService.encode('')).toBe('');
    });

    it('should handle undefined input', () => {
      expect(emojifyService.encode(undefined)).toBeUndefined();
    });
  });

  describe('decode method', () => {
    it('should return the input string unchanged', () => {
      const input = 'Hello 😀 world';
      expect(emojifyService.decode(input)).toBe(input);
    });

    it('should handle empty string', () => {
      expect(emojifyService.decode('')).toBe('');
    });

    it('should handle undefined input', () => {
      expect(emojifyService.decode(undefined)).toBeUndefined();
    });
  });

  describe('decodeRaw method', () => {
    it('should return the input string unchanged', () => {
      const input = 'Hello 🌍 world';
      expect(emojifyService.decodeRaw(input)).toBe(input);
    });

    it('should handle empty string', () => {
      expect(emojifyService.decodeRaw('')).toBe('');
    });

    it('should handle undefined input', () => {
      expect(emojifyService.decodeRaw(undefined)).toBeUndefined();
    });
  });
});