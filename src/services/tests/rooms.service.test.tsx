import { EventBus } from "@solid-primitives/event-bus";
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { JoinRoomFailType } from "~/proto/client-message";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { AppStateEvents } from "~/proto/pianorhythm-events";
import * as generalApi from "~/server/general.api";
import { CoreService } from '~/types/app.types';
import NotificationService from '../notification.service';
import RoomsService from '../rooms.service';
import {undefined} from "zod";
import {MockAppService, MockCoreService, MockDisplaysService} from "@test/mocks/service.mocks";
import {useService} from "solid-services";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";

// rooms.service.test.ts

// Mock dependencies
vi.mock('solid-services', () => ({
  useService: vi.fn(() => ({
    roomID: vi.fn().mockReturnValue(null),
    setActivatePageLoader: vi.fn(),
    setDisplay: vi.fn()
  }))
}));

vi.mock('../notification.service', () => ({
  default: {
    show: vi.fn()
  }
}));

vi.mock('~/server/general.api', () => ({
  getRoomSettings: vi.fn()
}));

describe('RoomsService', () => {
  let roomsService: ReturnType<typeof RoomsService>;
  let mockCoreService: CoreService;
  let mockEffectsBus: EventBus<AppStateEffects>;
  let mockEventsBus: EventBus<AppStateEvents>;
  let effectCallback: (arg0: { action: AppStateEffects_Action; roomsList?: { list: { roomID: string; name: string; }[]; } | { list: { roomID: string; name: string; }[]; }; basicRoomDto?: { roomID: string; name: string; } | { roomID: string; name: string; } | { roomID: string; name: string; } | { roomID: string; name: string; }; roomId?: string; joinRoomFailResponse?: { reason: JoinRoomFailType; roomID: string; roomName: string; }; }) => void;
  let eventCallback: (arg0: AppStateEvents) => void;

  beforeEach(() => {
    (useService as any).mockImplementation((service: any) => {
      if (service === AppService) return () => MockAppService();
      if (service === DisplaysService) return () => MockDisplaysService();
    });

    mockCoreService = MockCoreService();

    // Create event bus mocks
    mockEffectsBus = {
      clear: undefined,
      emit(_: any): void {
      },
      listen: vi.fn((callback) => {
        effectCallback = callback;
        return () => { };
      })
    };

    mockEventsBus = {
      clear: undefined, emit(_: any): void {
      },
      listen: vi.fn((callback) => {
        eventCallback = callback;
        return () => { };
      })
    };

    roomsService = RoomsService();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize only once', () => {
      roomsService.initialize(mockCoreService, mockEffectsBus, mockEventsBus);
      expect(mockEffectsBus.listen).toHaveBeenCalledTimes(1);
      expect(mockEventsBus.listen).toHaveBeenCalledTimes(1);

      // Second call should not re-initialize
      roomsService.initialize(mockCoreService, mockEffectsBus, mockEventsBus);
      expect(mockEffectsBus.listen).toHaveBeenCalledTimes(1);
      expect(mockEventsBus.listen).toHaveBeenCalledTimes(1);
    });
  });

  describe('event handling', () => {
    beforeEach(() => {
      roomsService.initialize(mockCoreService, mockEffectsBus, mockEventsBus);
    });

    it('should handle SetRooms event', () => {
      const mockRooms = [{ roomID: '1', name: 'Room 1' }];
      effectCallback({
        action: AppStateEffects_Action.SetRooms,
        roomsList: { list: mockRooms }
      });

      expect(roomsService.rooms()).toEqual(mockRooms);
    });

    it('should handle AddRoom event', () => {
      const mockRoom = { roomID: '2', name: 'Room 2' };
      effectCallback({
        action: AppStateEffects_Action.AddRoom,
        basicRoomDto: mockRoom
      });

      expect(roomsService.rooms()).toContainEqual(mockRoom);
    });

    it('should handle UpdateRoom event', () => {
      // Add initial room
      const initialRoom = { roomID: '3', name: 'Original Room' };
      effectCallback({
        action: AppStateEffects_Action.AddRoom,
        basicRoomDto: initialRoom
      });

      // Update the room
      const updatedRoom = { roomID: '3', name: 'Updated Room' };
      effectCallback({
        action: AppStateEffects_Action.UpdateRoom,
        basicRoomDto: updatedRoom
      });

      expect(roomsService.rooms()).toContainEqual(updatedRoom);
      expect(roomsService.rooms()).not.toContainEqual(initialRoom);
      expect(roomsService.rooms().length).toBe(1);
    });

    it('should handle DeleteRoom event', () => {
      // Add a room
      const mockRoom = { roomID: '4', name: 'Room to Delete' };
      effectCallback({
        action: AppStateEffects_Action.AddRoom,
        basicRoomDto: mockRoom
      });

      expect(roomsService.rooms()).toContainEqual(mockRoom);

      // Delete the room
      effectCallback({
        action: AppStateEffects_Action.DeleteRoom,
        roomId: '4'
      });

      expect(roomsService.rooms()).not.toContainEqual(mockRoom);
      expect(roomsService.rooms().length).toBe(0);
    });

    it('should handle JoinRoomFailResponse for password protected rooms', () => {
      effectCallback({
        action: AppStateEffects_Action.JoinRoomFailResponse,
        joinRoomFailResponse: {
          reason: JoinRoomFailType.EnterPassword,
          roomID: '5',
          roomName: 'Password Protected Room'
        }
      });

      expect(roomsService.roomIDofRoomWaitingForPassword()).toEqual({
        newSession: true,
        roomID: '5',
        roomName: 'Password Protected Room'
      });
    });

    it('should handle RoomMuted event', () => {
      eventCallback(AppStateEvents.RoomMuted);
      expect(NotificationService.show).toHaveBeenCalledWith({
        type: 'info',
        description: `You've <b>muted</b> everyone else in the room.`
      });
    });

    it('should handle RoomUnmuted event', () => {
      eventCallback(AppStateEvents.RoomUnmuted);
      expect(NotificationService.show).toHaveBeenCalledWith({
        type: 'info',
        description: `You've <b>unmuted</b> everyone else in the room.`
      });
    });
  });

  describe('public API', () => {
    beforeEach(() => {
      roomsService.initialize(mockCoreService, mockEffectsBus, mockEventsBus);
    });

    it('should expose fetchRoomSettings', () => {
      expect(roomsService.fetchRoomSettings).toBe(generalApi.getRoomSettings);
    });

    it('should allow setting roomIDofRoomWaitingForPassword', () => {
      const passwordRoomData = { roomID: '6', roomName: 'Test Room' };
      roomsService.setRoomIDofRoomWaitingForPassword(passwordRoomData);
      expect(roomsService.roomIDofRoomWaitingForPassword()).toEqual(passwordRoomData);
    });

    it('should provide access to rooms', () => {
      effectCallback({
        action: AppStateEffects_Action.SetRooms,
        roomsList: { list: [{ roomID: '7', name: 'Test Access Room' }] }
      });

      expect(roomsService.rooms()).toHaveLength(1);
    });

    it('should provide access to the store', () => {
      expect(roomsService.store).toBeDefined();
      expect(roomsService.store.rooms).toEqual([]);
    });
  });
});