/// <reference types="solid-start/env" />

export type Many<T> = T | ReadonlyArray<T>;
export type NotVoid = unknown;
export type IterateeShorthand<T> = PropertyName | [PropertyName, any] | PartialShallow<T>;
export type ArrayIterator<T, TResult> = (value: T, index: number, collection: T[]) => TResult;
export type ListIterator<T, TResult> = (value: T, index: number, collection: List<T>) => TResult;
export type ListIteratee<T> = ListIterator<T, NotVoid> | IterateeShorthand<T>;
export type UnionToIntersection<U> =
  (U extends any ? (k: U) => void : never) extends ((k: infer I) => void) ? I : never;
