import { query } from "@solidjs/router";
import { mapSheetMusicDboToDto, SheetMusicDetailResponseSchema } from "~/models/sheet-music-dbo.models";
import { SheetMusicDBService } from "../db/services/db-sheet-music";
import { getSession, UserSessionHelper } from "../server";

export const getSheetMusicQuery = query(async (id: string) => {
  "use server";

  try {
    let dbService = SheetMusicDBService.getInstance();
    let sheet = await dbService.getById(id);
    if (!sheet) { return null; }

    return mapSheetMusicDboToDto(sheet);
  } catch (e) {
    if (process.env.DEBUG) console.error(e);
    return null;
  }
}, "getSheetMusic");

export const getSheetMusicDetailsQuery = query(async (id: string) => {
  "use server";

  try {
    const session = await getSession();
    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/sheet_music/${id}?cb=${Date.now()}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      },
      credentials: "include"
    });

    if (!response.ok) {
      try { let text = await response.text(); console.error(text); } catch { }
      throw new Error(`Failed to fetch data. Status: ${response.statusText}`);
    }

    let json = await response.json();
    if (json == null) throw new Error("Invalid response from server.");
    if (json?.error) throw new Error("Not authenticated.");

    return SheetMusicDetailResponseSchema.parse(json);
  } catch (e) {
    if (process.env.DEBUG) console.error(e);
    return null;
  }
}, "getSheetMusicDetails");