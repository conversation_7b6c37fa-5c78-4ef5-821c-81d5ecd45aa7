import { action, CustomResponse, json, query, redirect } from "@solidjs/router";
import { getRequestEvent } from "solid-js/web";
import { LoginActionResponse, LoginResponse, RegistrationResponse, RegistrationResponseSuccess } from "~/types/api.types";
import { userLoginSchema, userRegisterSchema } from "./schema";
import { getSession, logout as logoutSession, UserSessionHelper } from "./server";
import clsx from "clsx";

export type GetMemberSessionInfo = { username: string; usertag: string; roles: string[]; };

const checkForRefreshToken = async () => {
  "use server";
  try {
    const session = await getSession();

    if (session.data.refreshToken) return;
    const event = getRequestEvent();

    let rt = event?.request?.headers.get("Cookie")?.match(/rst-rt=([^;]+)/)?.[1];
    if (!rt) return;

    // Check if token hasn't expired
    if (!UserSessionHelper.hasTokenExpired(rt))
      await session.update(d => ({ ...d, refreshToken: rt }));
  } catch { }
};

/**
 * Validates the user session information by checking for the presence of an access token or refresh token.
 *
 * @async
 * @function validateUserSessionInfo
 * @returns {Promise<boolean>} Returns a promise that resolves to `true` if the user is authenticated.
 * @throws {Error} Throws an error if the user is not authenticated.
 */
export const validateUserSessionInfo = query(async () => {
  "use server";

  await checkForRefreshToken();
  const session = await getSession();

  const token = session.data.accessToken ?? session.data.refreshToken;
  if (token === undefined) throw new Error("Not authenticated.");

  return true;
}, "validate-user-session");

/**
 * Retrieves the session information for the current member.
 *
 * @returns {Promise<GetMemberSessionInfo | null>} The session information for the current member, or null if an error occurred.
 */
export const getMemberSessionInfo = query(async () => {
  "use server";

  try {
    await checkForRefreshToken();
    const session = await getSession();

    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/members/me`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      },
    });

    if (!response.ok) throw new Error(`Failed to fetch user data. Status: ${response.statusText}`);

    let parsedJson = await response.json();

    if (parsedJson == null) throw new Error("Invalid response from server.");
    if (parsedJson?.error) throw new Error("Not authenticated.");
    if (parsedJson?.usertag == null) throw new Error("Invalid user data.");

    await handleResponseCookies(response).catch(() => { });

    return parsedJson as GetMemberSessionInfo;
  } catch (err) {
    // if (process.env.DEBUG) console.error("[getMemberSessionInfo] Error:", err);
  }

  return undefined;
}, "member-session");

/**
 * Performs a session restore operation.
 *
 * @returns {Promise<void>} A promise that resolves when the session restore operation is complete.
 */
export const onSessionRestore = query(async () => {
  "use server";

  try {
    await checkForRefreshToken();
    const session = await getSession();

    UserSessionHelper.validateTokens(session.data);

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/session-restore`, {
      method: "GET",
      credentials: "include",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      },
    });

    await handleResponseCookies(response).catch(() => { });
    return await handleLoginResponse(response);
  } catch (err) {
    console.error("[onSessionRestore]", err instanceof Error ? err.message : err?.toString());
  }

  return redirect("/login");
}, "member-session-restore");

/**
 * Performs a login operation using the provided form data.
 *
 * @param formData - The form data containing the login information.
 * @returns A promise that resolves to the login response.
 * @throws If there is an error during the login process.
 */
export const login = action(async (formData: FormData) => {
  "use server";

  try {
    if (!formData) throw new Error("Invalid form data.");
    let username: string | undefined = String(formData.get("username"))?.trim();
    if (username === 'null') username = undefined;

    const password = String(formData.get("password")) || undefined;
    const isGuest = Boolean(String(formData.get("isGuest")));

    const parseResult = userLoginSchema.safeParse({ username, password });
    if (!parseResult.success) {
      throw new Error(parseResult.error?.message ?? "Invalid input.");
    }

    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/login`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ username, password, isGuest })
    });

    // Extract cookies from response
    await handleResponseCookies(response).catch(() => { });

    return await handleLoginResponse(response);
  } catch (e: any) {
    let error = e instanceof Error ? e.message : e?.toString();
    console.error(`[login] Username: ${formData?.get("username") ?? "unknown"} |Error:`, error);
    return json<LoginActionResponse>({ token: undefined, roles: [], error });
  }

});

/**
 * Logs out the user and redirects to the login page.
 */
export const logout = action(async (redirectToLogin = false) => {
  "use server";

  try {
    let session = await getSession();

    await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/logout`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      },
    });

  } catch (e) {
    console.error("[logout] Error:", e);
  }

  await logoutSession();

  if (redirectToLogin) throw redirect("/login");
  return json({});
});

export const clearServerCookies = action(async (name: string) => {
  "use server";

  try {
    let hostName = new URL(process.env.PIANORHYTHM_SERVER_URL).hostname;
    const headers = new Headers();
    headers.append("Set-Cookie", `${name}=; Max-Age=0; Path=/; SameSite=None; Secure; HttpOnly; Partitioned=${process.env.PIANORHYTHM_SERVER_URL} Domain=${hostName}; Expires=Thu, 01 Jan 1970 00:00:00 GMT`);
    return json({}, { headers });
  } catch (e) {
    console.error("[clearServerCookies] Error:", e);
  }
});

/**
 * Registers a user with the provided form data.
 *
 * @param formData - The form data containing the user's information.
 * @returns A Promise that resolves to a boolean indicating the success of the registration.
 * @throws If there is an error during the registration process.
 */
export const register = action(async (formData: FormData) => {
  "use server";

  try {
    let username = String(formData.get("username"));
    let email = String(formData.get("email"));
    let password = String(formData.get("password"));
    let password2 = String(formData.get("password2"));
    let tos = Boolean(formData.get("tos"));

    let parseResult = userRegisterSchema.safeParse({ username, email, password, password2, tos });
    if (!parseResult.success) {
      throw new Error(parseResult.error?.message ?? "Invalid input.");
    }

    let data = parseResult.data;
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/register`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        username: data.username,
        email: data.email,
        password: data.password,
        confirmedpassword: data.password2,
        acceptedtos: data.tos
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to register. Status: ${response.statusText}`);
    }

    let result: RegistrationResponse | RegistrationResponseSuccess | null = await response.json();;

    if (result === null) throw new Error("Invalid response from server.");

    if ("validationError" in result && result?.validationError?.length > 0) {
      throw new Error(result.validationError?.[0]?.Reason ?? "Unknown error. Failed to register...");
    }

    return ("output" in result && result.output) || false;
  } catch (e: any) {
    console.error("[register] Error:", e);
    throw e instanceof Error ? new Error(e.message) : new Error(e?.toString());
  }
});

/**
 * Sends a forgot password request to the server.
 *
 * @param {FormData} formData - The form data containing the email address.
 * @returns {Promise<void>} - A promise that resolves when the request is complete.
 *
 * @throws {Error} - Throws an error if the request fails or if there is an issue with the response.
 */
export const forgotPassword = action(async (formData: FormData) => {
  "use server";

  try {
    let email = String(formData.get("email"));
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/forgot-password`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email })
    });

    if (!response.ok) {
      if (response.status == 400) {
        try {
          let json: { error: string; } = await response.json();
          if (json?.error) return;
        } catch { }
      }

      throw new Error(`Failed to send forgot password email. Status: ${response.statusText}`);
    }

  } catch (e: any) {
    console.error("[forgotPassword] Error:", e);
    throw e instanceof Error ? new Error(e.message) : new Error(e?.toString());
  }
});

/**
 * Resends a verification email to the user.
 *
 * @param {FormData} formData - The form data containing the user's email address.
 * @returns {Promise<void>} A promise that resolves when the email has been sent.
 * @throws {Error} Throws an error if the email could not be sent.
 *
 * @example
 * const formData = new FormData();
 * formData.append("email", "<EMAIL>");
 * await resendVerificationEmail(formData);
 */
export const resendVerificationEmail = action(async (formData: FormData) => {
  "use server";

  try {
    let email = String(formData.get("email"));
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/resend-email-verification`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email })
    });

    if (!response.ok) {
      if (response.status == 400) {
        try {
          let json: { error: string; } = await response.json();
          if (json?.error) return;
        } catch { }
      }

      throw new Error(`Failed to send verification email. Status: ${response.statusText}`);
    }

  } catch (e: any) {
    console.error("[resendVerificationEmail] Error:", e);
    throw e instanceof Error ? new Error(e.message) : new Error(e?.toString());
  }
});

/**
 * Handles the response cookies from a server response.
 *
 * @param response - The response object received from the server.
 * @throws Error if the response is not successful or if the cookies cannot be extracted.
 * @returns Promise<void> - A promise that resolves when the cookies are handled successfully.
 */
export async function handleResponseCookies(response: Response) {
  "use server";
  if (!response.ok) throw new Error(`[handleResponseCookies] Failed to fetch response. Status: ${response.statusText}`);

  let cookies = response.headers.get("Set-Cookie");
  if (cookies === null) throw new Error("Failed to extract cookies from response");

  // Extract the cookie with key 'rst-id' and value
  let splitCookies = cookies.split(", ");
  let rstId = extractSessionID(splitCookies);
  if (rstId === undefined) throw new Error("Failed to extract rst-id from cookies");

  let { rstAt, rstRt } = extractAccessTokens(splitCookies);

  const session = await getSession();
  await session.update(d => {
    return {
      ...d,
      accessToken: rstAt?.trim() ?? d.accessToken,
      refreshToken: rstRt?.trim() ?? d.refreshToken,
      sessionToken: rstId ? `rst-id=${rstId.trim()}` : d.sessionToken,
    };
  });
}

/**
 * Handles the OAuth2 response by updating the session with the provided tokens.
 *
 * @param response - The OAuth2 response object containing the tokens.
 * @param response.at - The access token.
 * @param response.rt - The optional refresh token.
 * @param response.sessionID - The optional session ID.
 *
 * @returns A promise that resolves when the session is updated.
 *
 * @throws Will log an error to the console if the session update fails.
 */
export const handleOAuth2Response = action(async (input: {
  at: string;
  rt?: string;
  sessionID?: string;
}) => {
  "use server";

  try {
    let session = await getSession();

    await session.update(d => {
      return {
        ...d,
        accessToken: input.at.trim(),
        refreshToken: input.rt ? input.rt.trim() : d.refreshToken,
        sessionToken: input.sessionID ?
          `rst-id=${input.sessionID.trim()}` : d.sessionToken,
      };
    });

  } catch (e) {
    console.error("[handleOAuth2Response] Error:", e);
  }
});

function extractAccessTokens(splitCookies: string[]) {
  let rstAt = splitCookies.find(c => c.startsWith("rst-at"))?.split(";")[0]?.split("=")[1];
  let rstRt = splitCookies.find(c => c.startsWith("rst-rt"))?.split(";")[0]?.split("=")[1];
  return { rstAt, rstRt };
}

function extractSessionID(splitCookies: string[]) {
  let sessionID = splitCookies.find(c => c.startsWith("rst-id"))?.split(";")[0]?.split("=")[1];
  return sessionID;
}

async function handleLoginResponse(response: Response): Promise<CustomResponse<LoginActionResponse>> {
  "use server";

  let result: LoginResponse | null = await response.json();
  let headers = new Headers();

  if (result === null) throw new Error("Invalid response from server.");
  if (result.response == "Error") {
    throw new Error(result.validationError?.[0]?.Reason ?? "Unknown error. Failed to login...");
  }

  if (result.token === null && result.refreshToken === null)
    return json<LoginActionResponse>({ token: undefined, roles: [] });

  let splitCookies = [result.token, result.refreshToken].filter(Boolean) as string[];
  let { rstAt, rstRt } = extractAccessTokens(splitCookies);

  const session = await getSession();
  await session.update(d => {
    return {
      ...d,
      accessToken: rstAt ?? d.accessToken,
      refreshToken: rstRt ?? d.refreshToken,
    };
  });

  // Only save refresh token if user is not logged in as a guest.
  let roles = result.roles ?? [];
  if (roles.length > 0 && !roles.map(x => x.toLowerCase()).includes("guest"))
    headers.append("Set-Cookie", splitCookies.find(c => c.startsWith("rst-rt")) ?? '');

  return json({
    token: result.token,
    refreshToken: result.refreshToken,
    usertag: result.usertag,
    username: result.username,
    roles,
  }, { headers });
}
