import { useSession } from "vinxi/http";
import { z } from "zod";
import { zod<PERSON><PERSON><PERSON> } from "../util/zod.helper";
import { jwtDecode } from "jwt-decode";

const envVariables = z.object({
  DEBUG: z.string().default("false"),
  NODE_ENV: z.string().default("dev"),
  PR_CLIENT_VERSION: z.string().default("0.0.0"),
  PR_ASSETS_URL: z.string().default("https://assets.pianorhythm.io"),
  PIANORHYTHM_GITHUB_APP_ID: z.string().default("925579"),
  PIANORHYTHM_GITHUB_ACCESS_TOKEN: z.string().default(""),
  PIANORHYTHM_MONGODB_API_HOST: z.string().default(""),
  PIANORHYTHM_MONGODB_URI: z.string().default("mongodb://localhost:27017"),
  PIANORHYTHM_MONGODB_API_KEY_EXPRESS: z.string().default(""),
  PIANORHYTHM_SERVER_URL: z.string().default("http://localhost:7000"),
  PIANORHYTHM_GRAPHQL_USERS_ENDPOINT: z.string().default(""),
  PIANORHYTHM_MONGODB_GRAPHQL_ENDPOINT_HOST: z.string().default("https://us-east-1.aws.realm.mongodb.com"),
  PIANORHYTHM_MONGODB_GRAPHQL_ENDPOINT_PATH: z.string().default(""),
  PIANORHYTHM_ANALYTICS_HOST: z.string().default("https://analytics.pianorhythm.io"),
  PIANORHYTHM_ANALYTICS_TOKEN: z.string().default(""),
  PIANORHYTHM_MONGODB_API_DATASOURCE: z.string().default("Cluster0"),
});

const envVariablesDefault = zodHelper.getDefaults(envVariables);
const envVariablesTransform = envVariables
  .transform((v => {
    if (v.NODE_ENV == "dev") {
      v.DEBUG = "true";
      v.PIANORHYTHM_SERVER_URL = envVariablesDefault.PIANORHYTHM_SERVER_URL;
      // v.PIANORHYTHM_MONGODB_API_HOST = "https://us-east-1.aws.data.mongodb-api.com/app/data-iiktx/endpoint/data/v1";
      // v.PIANORHYTHM_GRAPHQL_USERS_ENDPOINT = "https://us-east-1.aws.realm.mongodb.com/api/client/v2.0/app/data-iiktx/graphql";
      v.PIANORHYTHM_MONGODB_GRAPHQL_ENDPOINT_PATH = "/api/client/v2.0/app/data-iiktx/graphql";
    }

    if (v.NODE_ENV == "production" || v.NODE_ENV == "staging") {
      // Prevent default values from being used in production
      if (v.PIANORHYTHM_GITHUB_ACCESS_TOKEN == envVariablesDefault.PIANORHYTHM_GITHUB_ACCESS_TOKEN) {
        // throw new Error("PIANORHYTHM_GITHUB_ACCESS_TOKEN must be set.");
      }

      if (v.PIANORHYTHM_MONGODB_API_KEY_EXPRESS == envVariablesDefault.PIANORHYTHM_MONGODB_API_KEY_EXPRESS) {
        // throw new Error("PIANORHYTHM_MONGODB_API_KEY_EXPRESS must be set.");
      }

      if (!v.PIANORHYTHM_MONGODB_API_HOST) {
        // throw new Error("PIANORHYTHM_MONGODB_API_HOST must be set.");
      }
    }

    return v;
  }));

process.env = { ...envVariablesTransform.parse(process.env), ...process.env };

declare global {
  namespace NodeJS {
    interface ProcessEnv extends z.infer<typeof envVariables> { }
  }
}

export type UserSession = {
  refreshToken?: string;
  sessionToken?: string;
  accessToken?: string;
};

export namespace UserSessionHelper {
  /**
   * Retrieves the access and refresh tokens from the given user session.
   *
   * @param session - The user session object.
   * @returns An object containing the access and refresh tokens.
   */
  export function getTokens(session: UserSession) {
    return { accessToken: session.accessToken, refreshToken: session.refreshToken };
  }

  /**
   * Validates the tokens in the given user session.
   *
   * @param session - The user session containing the access token and refresh token.
   * @throws Error - Throws an error if the session is not authenticated (i.e., if both access token and refresh token are undefined).
   */
  export function validateTokens(session: UserSession) {
    const token = session.accessToken ?? session.refreshToken;
    if (token === undefined) throw new Error("Not authenticated.");
  }

  /**
   * Retrieves the headers for a given user session.
   *
   * @param session - The user session object.
   * @returns An object containing the headers.
   */
  export function getHeaders(session: UserSession) {
    return {
      "rst-at": session.accessToken!,
      "rst-rt": session.refreshToken!,
      "cookie": session.sessionToken!,
    };
  }

  /**
   * Checks if the given token has expired.
   *
   * @param token - The token to check.
   * @return {boolean} - Returns true if the token has expired, false otherwise.
   * */
  export function hasTokenExpired(token: string): boolean {
    if (!token) { return true; }

    try {
      const decodedHeader = jwtDecode(token, { header: false });
      // Check the expiration time of the token
      const currentTime = Math.floor(Date.now() / 1000);
      if (decodedHeader.exp && decodedHeader.exp < currentTime) {
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error decoding token:", error);
      return true;
    }
  }
}

/**
 * Retrieves the user session.
 * @returns The user session.
 */
export function getSession() {
  return useSession<UserSession>({
    password: process.env.SESSION_SECRET ?? "sc3ExRWJWrozjnH8ZsZe815iPkpzUYDu"
  });
}

/**
 * Logs out the current user by clearing the session.
 *
 * @returns {Promise<void>} A promise that resolves when the logout is successful.
 * @throws {Error} If an error occurs during the logout process.
 */
export async function logout(): Promise<void> {
  try {
    const session = await getSession();
    await session.clear();
  } catch (e) {
    console.error("[logout] Error:", e);
  }
}
