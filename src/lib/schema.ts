import { z } from "zod";
import { USER_INPUT } from "~/util/const.common";

export const userLoginSchema = z.object({
  username: z
    .union([
      z.string().min(USER_INPUT.MinUsernameLength).max(USER_INPUT.MaxUsernameLength),
      z.string().length(0),
    ])
    .optional()
    .transform(e => e === "" ? undefined : e),
  password: z.string().min(USER_INPUT.MinPasswordLength).max(USER_INPUT.MaxPasswordLength).optional(),
});

/**
 * TSDoc comment for userRegisterSchema.
 *
 * This schema represents the validation rules for user registration.
 * It includes the following properties:
 * - `username`: A string with a minimum length of `MinUsernameLength` and a maximum length of `MaxUsernameLength`.
 * - `email`: A string that must be a valid email address and has a maximum length of `MaxEmailLength`.
 * - `password`: A string with a minimum length of `MinPasswordLength` and a maximum length of `MaxPasswordLength`.
 * - `password2`: A string with a minimum length of `MinPasswordLength` and a maximum length of `MaxPasswordLength`.
 * - `tos`: A boolean value that must be `true` to indicate acceptance of the terms and conditions.
 *
 * @remarks
 * This schema is used for validating user registration data.
 */
export const userRegisterSchema = z.object({
  username: z.string().min(USER_INPUT.MinUsernameLength).max(USER_INPUT.MaxUsernameLength),
  email: z.string().email().max(USER_INPUT.MaxEmailLength),
  password: z.string().min(USER_INPUT.MinPasswordLength).max(USER_INPUT.MaxPasswordLength),
  password2: z.string().min(USER_INPUT.MinPasswordLength).max(USER_INPUT.MaxPasswordLength),
  tos: z.boolean().default(false).refine((val) => val === true, {
    message: "Please read and accept the terms and conditions",
  }),
});

export type UserRegisterForm = z.infer<typeof userRegisterSchema>;