import { Db } from "mongodb";
import { SheetMusicDbo, SheetMusicDboZ } from "~/models/sheet-music-dbo.models";
import { Database, IBaseDBService } from "../db-store";

export class SheetMusicDBService implements IBaseDBService<SheetMusicDbo> {
  db: Db;
  public static collectionName = "sheet-music";
  private static instance: SheetMusicDBService;

  private constructor() {
    this.db = Database.getInstance().getDb();
  }

  public static getInstance() {
    if (!SheetMusicDBService.instance) {
      SheetMusicDBService.instance = new SheetMusicDBService();
    }

    return SheetMusicDBService.instance;
  }

  private getCollection() {
    return this.db.collection<SheetMusicDbo>(SheetMusicDBService.collectionName);
  }

  private parseSheetMusic(sheet: SheetMusicDbo | any) {
    let parsed = SheetMusicDboZ.safeParse(sheet);
    if (parsed.success) { return parsed.data; }
    return null;
  }

  private async getSheetMusicById(uuid: string) {
    return this.getCollection().findOne({ uuid }).then(this.parseSheetMusic);
  }

  private async aggregateSheetMusic<T = any>(pipeline: object[]) {
    try {
      return this.getCollection().aggregate(pipeline).toArray() as Promise<T[]>;
    } catch {
      return Promise.resolve([]);
    }
  }

  parseManySheets(sheets: any[]) {
    return sheets.map(this.parseSheetMusic).filter(Boolean) as SheetMusicDbo[];
  }

  async aggregateData<R = any>(pipeline: object[]): Promise<R[]> {
    return this.aggregateSheetMusic<R>(pipeline);
  }

  async getById(id: string): Promise<SheetMusicDbo | null> {
    return this.getSheetMusicById(id);
  };
}