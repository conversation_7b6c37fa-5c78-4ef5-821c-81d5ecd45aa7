import { Db, MongoClient } from "mongodb";

const MONGODB_HOST = "localhost";
const MONGODB_PORT = 27017;

export class Database {
  private db?: Db;
  private static instance: Database;

  private constructor() {}

  init(url: string = process.env.PIANORHYTHM_MONGODB_URI, database: string = "pianorhythm") {
    const client = new MongoClient(url ?? `mongodb://${MONGODB_HOST}:${MONGODB_PORT}`);
    this.db = client.db(database);
    if (process.env.DEBUG) console.log(`✅ Client created: ${database}`);
  }

  public static getInstance() {
    if (!Database.instance) {
      Database.instance = new Database();
    }

    return Database.instance;
  }

  getDb() {
    // if (!this.db) { throw new Error("DB is not init"); }
    return this.db!;
  }
}

export interface IBaseDBService<T> {
  aggregateData<R = any>(pipeline: object[]): Promise<R[]>;
  getById(id: string): Promise<T | null>;
}

Database.getInstance().init();