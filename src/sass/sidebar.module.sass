$listPadding: 5px

.parentContainer
  background: linear-gradient(180deg, var(--hope-colors-primary1) 0%, var(--hope-colors-primaryAlpha3) 0%, rgba(255,255,255,0) 100%)
  position: absolute
  top: 0
  left: 0
  width: calc(var(--sidebarListWidth))
  transition: opacity 0.3s, width 0.3s, height 0.3s
  height: calc(100dvh - var(--bottomBarHeight) - var(--sidebarSortByHeight))
  pointer-events: none

.tabsGroup
  width: 100%

.clickableTab
  background: var(--hope-colors-primaryDarkAlpha)
  cursor: pointer
  color: rgba(gray, 25%)
  transition: opacity 0.3s, transform 0.2s, color 0.1s

  &:hover
    color: var(--hope-colors-accent1)

.activeTab
  color: var(--hope-colors-neutral12) !important
  background: var(--hope-colors-primaryAlpha3)

.listContainer
  width: 100%
  transition: opacity 0.3s, width 0.3s, height 0.3s
  pointer-events: all

.mainList
  padding: var(--sidebarListPadding)
  border-bottom-right-radius: 5px
  overflow: hidden
  scroll-behavior: smooth
  overflow-y: scroll
  // min-height: 20px
  max-height: calc(100dvh - 30px - var(--bottomBarHeight) - var(--sidebarSortByHeight))
  transition: opacity 0.3s, width 0.3s, height 0.3s
  padding-bottom: 20px
  --mask: linear-gradient(to bottom,rgba(0,0,0, 1) 0, rgba(0,0,0, 1) 90%,rgba(0,0,0, 0) 95%, rgba(0,0,0, 0) 0 ) 100% 50% / 100% 100% repeat-x
  -webkit-mask: var(--mask)
  mask: var(--mask)

.mainList::-webkit-scrollbar-thumb
  background: var(--hope-colors-primary5) !important

.mainList::-webkit-scrollbar
  width: 5px !important

.listItem
  z-index: var(--profileCardZindex)
  width: 100%
  transition: background 0.2s
  cursor: pointer
  border-radius: 5px

  &:hover
    background: var(--hope-colors-primary2)

.userProfileImage
  border-radius: 5px
  transition: transform 0.2s, filter 0.2s
  cursor: pointer

  img
    position: relative
    z-index: 1
    border-radius: 5px
    height: 100%
    width: 100%
    max-height: 100%

  &:hover
    transform: scale(1.03)
    filter: brightness(1.2)

  &:active
    transform: scale(1.05)

.elementIconsContainer
  font-size: 12px
  position: absolute
  right: 1px
  top: 1px

.elementIcon
  transition: transform 0.2s ease

  &:hover
    cursor: pointer
    transform: scale(1.15)

.noteSourceIcon
  @extend .elementIcon
  transition: opacity 0.2s ease-out, transform 0.2s ease

.rolesIconsContainer
  position: absolute
  left: -7px
  bottom: -5px
  transform: scale(0.8)
  z-index: 2

.roleIcon
  font-size: 7px
  padding: 1px
  border-radius: 3px
  text-align: center
  width: 23px
  height: 15px
  z-index: 2
  font-weight: bold
  overflow: hidden

.roleIconRainbow
  font-weight: bolder
  font-size: 8px
  padding: 0
  margin-left: -1px
  height: 15px

.userElementMEIcon
  font-size: 8px
  font-weight: bolder
  // position: absolute
  // top: 0
  // right: 0
  transition: color 0.1s
  cursor: pointer
  padding: 1px
  border-bottom-left-radius: 10px
  border-top-right-radius: 10px

  &:hover
    color: var(--hope-colors-accent1)

.userStatusText
  color: var(--hope-colors-neutral12)
  text-overflow: ellipsis
  overflow: hidden
  width: 98px
  white-space: nowrap
  font-size: 12px

// create an animation for a music icon bouncing up and down with some rotation and squash and stretch
.playingMidiFile
  animation: playingMidiFileAnim 0.9s infinite
  transform-origin: bottom
  transition: transform 0.1s, color 0.2s
  color: white

@keyframes playingMidiFileAnim
  0%
    transform: translateY(0) scale(1) rotate(0deg)
    color: var(--hope-colors-accent10)
  70%
    transform: translateY(-1px) scale(0.9) rotate(0deg)
    color: white
  90%
    transform: translateY(1px) scale(1.1) rotate(0deg)
  100%
    transform: translateY(0) scale(1) rotate(0deg)
    color: var(--hope-colors-accent1)

/*/********************
// Sidebar Rooms
/*/---------------------/
.roomElement
  user-select: none
  // padding: 3px
  border-radius: 5px
  cursor: pointer
  transition: transform 0.1s, border-color 0.2s, background 0.2s, filter 0.2s
  overflow: hidden
  position: relative
  background: var(--hope-colors-primaryDarkAlpha)

  .roomElementName
    height: 22px
    display: block
    overflow: hidden
    text-overflow: ellipsis
    width: 90px
    white-space: nowrap

  &:hover
    background: rgba(0,0,0,0.2)
    border-color: var(--hope-colors-accent1)

.roomElement_canBeActive
  &:active
    transform: scale(1.02)
    background: var(--hope-colors-accent1)
    // filter: brightness(1.2)

.roomUserCountSlash
  height: 1px
  width: 20px
  background: var(--hope-colors-neutral12)
  top: 0
  left: 0
  right: 0
  bottom: 0
  margin: auto
  transform: rotate(-25deg)
