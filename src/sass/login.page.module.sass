.background
  position: absolute
  top: 0
  width: 100vw
  height: 100dvh
  opacity: 0.5
  translateZ: 0px
  will-change: transform
  backface-visibility: hidden
  backdrop-filter: blur(3px)
  transition: backdrop-filter 0.4s ease-in-out

.mainForm
  position: absolute
  top: 0
  width: 100vw
  height: 100dvh

.mainFormInput
  background-color: transparent !important
  border: none !important
  border-radius: 0 !important
  border-bottom: 2px solid #fff !important
  color: #fff !important
  font-size: 200% !important
  font-weight: 100 !important
  height: 60px !important
  outline: none !important
  padding-bottom: 10px !important
  text-align: center !important
  width: 350px !important

.mainFormButtons
  margin-top: 10px !important

.mainFormButton
  border-radius: 0 !important
  border: solid 1px white !important
  width: 130px !important

.welcomeMessage
  font-size: large !important
  font-weight: 100 !important

.welcomeTitle
  font-size: 200% !important
  font-weight: 100 !important

.navText
  transition: color 0.2s !important
  font-size: small !important
  color: gray !important

  &:hover
    color: var(--hope-colors-neutral12) !important

.loginFormContainer
  width: 300px !important

.loginFormInputContainer
  width: 100% !important

.meta
  position: absolute
  bottom: 2px
  left: 7px
  z-index: 2
  font-size: x-small
  align-items: flex-start !important

  @media screen and (max-height: 300px) and (max-width: 500px), screen and (max-height: 230px)
    display: none

$elementFadeIn: 0.5s

@keyframes fadeIn
  0%
    opacity: 0
  100%
    opacity: 1

.formFadeIn
  -webkit-animation: fadeIn $elementFadeIn
  -moz-animation: fadeIn $elementFadeIn
  -o-animation: fadeIn $elementFadeIn
  -ms-animation: fadeIn $elementFadeIn
  animation: fadeIn $elementFadeIn

.pending
  pointer-events: none
  transition: opacity .3s
  transition-timing-function: ease-in
  opacity: 0.1

.downloadAppContainer
  .mainFormButton
    width: 100%