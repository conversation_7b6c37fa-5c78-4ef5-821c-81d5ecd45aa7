@use "sass:math";

/*/*********************/
// Page Loader
/*/---------------------/*/
$pageLoaderTransitionTime: 1s;
$pageLoaderIconOffSetY: -70px;
$pageLoaderContainerY: $pageLoaderIconOffSetY + 50px;
$pageLoaderEasing: cubic-bezier(0.22, 0.61, 0.36, 1);

.pageloader {
  transition: all $pageLoaderTransitionTime $pageLoaderEasing, -webkit-all $pageLoaderTransitionTime $pageLoaderEasing !important;
  overflow: hidden;
  z-index: var(--hope-zIndices-toast, 1800);
  position: fixed;
  top: 0;
  left: 0;
  background: var(--hope-colors-primary1);
  width: 100%;
  pointer-events: none;
}

.pageloader.ispianorhythm {
  display: block;
  .title {
    font-size: 30px;
    text-transform: capitalize;
  }
  &::after {
    display: none;
  }
}

.pageloaderProTipsContainer {
  color: var(--hope-colors-primary1);
  font-size: 0.9em;
  letter-spacing: 0.1em;
  line-height: 1.5em;
}

.pageloaderProTipsContent {
  overflow-wrap: break-word;
  text-align: center;
  color: #e4e4e4;

  img {
    box-shadow: 5px 10px 10px rgba(0, 0, 0, 0.25);
    max-width: 500px;
  }
}

$preloadWidth: 65px;
$preloadKeyHeight: 50px;
$preloadBlackKeyHeight: $preloadKeyHeight - math.div($preloadKeyHeight,3);
$preloadKeyWidth: 10px;
$page-loader-key-color-b: white;

.pageloaderAnimatedLogoContainer {
  position: relative;
  width: 75px;
  span {
    background: var(--hope-colors-primary1);
    color: var(--hope-colors-neutral12);
    position: absolute;
    display: block;
    bottom: 0;
    left: 0;
    width: $preloadKeyWidth;
    height: $preloadKeyHeight;
    border-radius: 5px;
    line-height: $preloadKeyHeight * 2 - 12;
    font-size: 10px;
    text-align: center;
    &:nth-child(2) {
      color: var(--hope-colors-primary1);
      left: math.div($preloadKeyWidth, 2);
      height: $preloadBlackKeyHeight;
      top: -$preloadBlackKeyHeight - ($preloadKeyHeight - $preloadBlackKeyHeight);
      line-height: $preloadBlackKeyHeight * 2 - 12;
      background: var(--hope-colors-neutral12);
      -webkit-animation-delay: 200ms;
      animation-delay: 200ms;
      z-index: 1;
    }
    &:nth-child(3) {
      left: $preloadKeyWidth + 1;
      -webkit-animation-delay: 400ms;
      animation-delay: 400ms;
    }
    &:nth-child(4) {
      color: var(--hope-colors-primary1);
      left: (math.div($preloadKeyWidth, 2) * 3) + 2;
      top: -$preloadBlackKeyHeight - ($preloadKeyHeight - $preloadBlackKeyHeight);
      height: $preloadBlackKeyHeight;
      line-height: $preloadBlackKeyHeight * 2 - 12;
      background: var(--hope-colors-neutral12);
      z-index: 1;
      -webkit-animation-delay: 600ms;
      animation-delay: 600ms;
    }
    &:nth-child(5) {
      left: $preloadKeyWidth * 2 + 2;
      -webkit-animation-delay: 800ms;
      animation-delay: 800ms;
    }
    &:nth-child(6) {
      left: $preloadKeyWidth * 3 + 2.5;
      -webkit-animation-delay: 1000ms;
      animation-delay: 1000ms;
    }
    &:nth-child(7) {
      left: $preloadKeyWidth * 4 + 3.5;
      -webkit-animation-delay: 1200ms;
      animation-delay: 1200ms;
    }
    &:nth-child(8) {
      left: $preloadKeyWidth * 5 + 4.5;
      -webkit-animation-delay: 1600ms;
      animation-delay: 1600ms;
    }
    &:nth-child(12) {
      left: $preloadKeyWidth * 6 + 5.5;
      -webkit-animation-delay: 2000ms;
      animation-delay: 2000ms;
    }
    &:nth-child(9) {
      color: var(--hope-colors-primary1);
      left: (math.div($preloadKeyWidth, 2)* 7) + 2;
      top: -$preloadBlackKeyHeight - ($preloadKeyHeight - $preloadBlackKeyHeight);
      height: $preloadBlackKeyHeight;
      line-height: $preloadBlackKeyHeight * 2 - 12;
      background: var(--hope-colors-neutral12);
      z-index: 1;
      -webkit-animation-delay: 1200ms;
      animation-delay: 1200ms;
    }
    &:nth-child(10) {
      color: var(--hope-colors-primary1);
      left: (math.div($preloadKeyWidth, 2) * 10) - 1;
      top: -$preloadBlackKeyHeight - ($preloadKeyHeight - $preloadBlackKeyHeight);
      height: $preloadBlackKeyHeight;
      line-height: $preloadBlackKeyHeight * 2 - 12;
      background: var(--hope-colors-neutral12);
      z-index: 1;
      -webkit-animation-delay: 1400ms;
      animation-delay: 1400ms;
    }
    &:nth-child(11) {
      color: var(--hope-colors-primary1);
      left: (math.div($preloadKeyWidth, 2) * 12) + 1;
      top: -$preloadBlackKeyHeight - ($preloadKeyHeight - $preloadBlackKeyHeight);
      height: $preloadBlackKeyHeight;
      line-height: $preloadBlackKeyHeight * 2 - 12;
      background: var(--hope-colors-neutral12);
      z-index: 1;
      -webkit-animation-delay: 1800ms;
      animation-delay: 1800ms;
    }
  }
}

.pageloaderLogoAnimation {
  -webkit-animation: preloader 2.5s infinite ease-in-out;
  animation: preloader 2.5s infinite ease-in-out;
}

.pageloaderAnimatedLogoAnimate {
  // -webkit-animation: preloader 2.5s infinite ease-in-out
  // animation: preloader 2.5s infinite ease-in-out
  box-shadow: 5px 10px 10px rgba(0, 0, 0, 0.25);
}

.pageNotFoundContainer,
.pageFailedToLoadContainer {
  color: var(--hope-colors-neutral12);
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 530px;
  min-width: 400px;
  padding: 40px;
  padding-bottom: 10px;
  text-align: center;
  font-size: 24px;
  box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
  transition: all 0.2s;
}

.pageNotFoundHeader,
.pageFailedToLoadHeader {
  color: var(--hope-colors-neutral12);
  margin-top: -25px;
  margin-bottom: 10px;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1.2;
}

@-webkit-keyframes preloader {
  0% {
    -webkit-transform: translateY(0);
    transform: translateY(0) scaleY(-1);
  }
  25% {
    -webkit-transform: translateY(15px);
    transform: translateY(15px);
  }
  50%,
  100% {
    -webkit-transform: translateY(0);
    transform: translateY(0) scaleY(1);
  }
};