.prDockTrashButton
  position: absolute
  z-index: 3
  right: 3px
  top: 2px
  background: var(--hope-colors-neutral12)
  padding: 0.5px
  border-radius: 3px
  opacity: 0.1
  transition: opacity 0.3s, transform 0.1s, color 0.3s
  color: var(--hope-colors-neutral12)

  &:hover
    opacity: 1
    cursor: pointer
    transform: scale(1.2)
    color: red

.prDockSlot_ImageWrapper
  z-index: 2
  position: absolute
  transition: opacity 0.3s, filter 0.3s

.prDockSlot_ImageWrapperFade
  filter: blur(1px)

.prDockSlot
  justify-content: center
  transition: background-color 0.3s, transform 0.1s, border 0.2s, border-color 0.2s
  margin-left: 5px
  margin-right: 5px
  position: relative
  overflow: hidden
  cursor: pointer
  -webkit-backface-visibility: hidden

  span, div, img
    cursor: inherit

  &:hover
    border: 2px solid white
    z-index: 0 !important
    border-color: var(--hope-colors-accent1) !important

  .prDockSlot_Muted
    color: var(--hope-colors-neutral11)
    position: absolute
    left: 4px
    top: -5px
    z-index: 2

  .prDockSlot_Volume
    display: block
    width: 100%
    bottom: 0
    position: absolute
    transition: height 200ms ease-in-out
    opacity: 0.8

  .prDockSlot_Image
    padding: 5px
    min-width: 50px
    left: 0
    z-index: 2
    transition: opacity 0.3s, transform 0.1s, color 0.3s

  .prDockSlot_Triangle
    display: inline-block
    margin-top: 21px
    width: 0
    height: 0
    border-style: solid
    position: absolute
    border-width: 0 15px 10px 0
    z-index: 2

  .prDockSlot_Index
    color: var(--hope-colors-neutral12)
    font-size: 10px
    width: 16px
    position: absolute
    bottom: -3px
    left: 2px
    z-index: 2
    text-shadow: 0 0 2px black

.prDockButtonGroups
  justify-content: center

  input
    width: 45px
    padding-inline-start: 5px !important
    padding-inline-end: 3px !important

  button
    border: 1px solid transparent
    cursor: pointer
    font-size: 10px
    padding: 5px
    transition: background-color 0.3s, transform 0.1s, border 0.2s, border-color 0.2s

    &:active
      background: var(--hope-colors-accent1)
      transform: scale(0.99)

.prDockButtonBase
  font-size: 12px
  padding: 3px

.dockSlotFlash
  animation-name: flash

@keyframes flash
  0%, 50%, 100%
    background: white
  25%, 75%
    background: transparent
