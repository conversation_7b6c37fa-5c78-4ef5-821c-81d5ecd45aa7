$color_1: var(--bs-tooltip-color);
$background-color_1: #ffffff;
$background-color_2: var(--bs-light-light);
$background-color_3: var(--bs-light);
$background-color_4: var(--hope-colors-primaryLight);
$background-color_5: var(--hope-colors-primary1);
$background-color_6: var(--hope-colors-accent5);
$background-color_7: var(--hope-colors-accent1);
$background-color_8: var(--hope-colors-success11);
$background-color_9: var(--hope-colors-success10);
$background-color_10: var(--bs-info-light);
$background-color_11: var(--bs-info);
$background-color_12: var(--hope-colors-warning12);
$background-color_13: var(--hope-colors-warning10);
$background-color_14: var(--hope-colors-danger11);
$background-color_15: var(--hope-colors-danger10);
$background-color_16: var(--hope-colors-primaryDark2);
$background-color_17: var(--hope-colors-primaryDark1);

.noUi-target {
  --target-color: var(--hope-colors-primaryLight);
  border: none !important;
  // box-shadow: none;
  box-shadow: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.05) !important;
  background: var(--target-color);

  .noUi-flash-background {
    background: var(--hope-colors-tertiary1) !important;
  }

  .noUi-connects {
    background: var(--target-color);
    transition: background 0.3s ease, transform 0.3s ease-in-out;
    transform-origin: bottom;
    transform: scaleY(1);
  }

  .noUi-handle {
    background-color: $background-color_1;
    border: 3px solid var(--target-color);
    box-shadow: 0 0.1rem 1rem 0.25rem rgba(0, 0, 0, 0.15);
    cursor: pointer;
  }
}

$handle_bar_height: 12px;
$handle_bar_width: 12px;
$handle_dimensions: 20px;

.noUi-target.noUi-horizontal {
  height: $handle_bar_height;

  .noUi-handle {
    top: -4px;
    width: $handle_dimensions;
    height: $handle_dimensions;
    border-radius: 50%;
    outline: none;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }

    &:active {
      transform: scale(1.1);
      background: var(--hope-colors-accent1);
    }
  }
}

.noUi-target.noUi-vertical {
  height: 150px;
  width: $handle_bar_width;

  .noUi-handle {
    width: $handle_dimensions;
    height: $handle_dimensions;
    right: -4.25px;
    border-radius: 50%;
    outline: none;

    &::before {
      display: none;
    }

    &::after {
      display: none;
    }
  }
}

.noUi-target.noUi-sm {
  height: 6px;
  .noUi-handle {
    width: 20px;
    height: 20px;
    top: -7px;
  }
}

.noUi-target.noUi-lg {
  height: 18px;
  .noUi-handle {
    width: 30px;
    height: 30px;
    top: -6px;
  }
}

.noUi-target.noUi-target-light {
  .noUi-connects {
    background-color: $background-color_2;
    .noUi-connect {
      background-color: $background-color_3;
    }
  }
  .noUi-handle {
    border: 1px solid var(--bs-light);
    box-shadow: 0 3px 6px -3px rgba(var(--bs-light), 0.7);
    background-color: $background-color_3;
  }
}

.noUi-target.noUi-target-primary {
  .noUi-connects {
    background-color: $background-color_4;
    .noUi-connect {
      background-color: $background-color_5;
    }
  }
  .noUi-handle {
    border: 1px solid var(--hope-colors-primary1);
    box-shadow: 0 3px 6px -3px rgba(var(--hope-colors-primary1), 0.7);
    background-color: $background-color_5;
  }
}

.noUi-target.noUi-target-secondary {
  .noUi-connects {
    background-color: $background-color_6;
    .noUi-connect {
      background-color: $background-color_7;
    }
  }
  .noUi-handle {
    border: 1px solid var(--hope-colors-accent1);
    box-shadow: 0 3px 6px -3px rgba(var(--hope-colors-accent1), 0.7);
    background-color: $background-color_7;
  }
}

.noUi-target.noUi-target-success {
  .noUi-connects {
    background-color: $background-color_8;
    .noUi-connect {
      background-color: $background-color_9;
    }
  }
  .noUi-handle {
    border: 1px solid var(--hope-colors-success10);
    box-shadow: 0 3px 6px -3px rgba(var(--hope-colors-success10), 0.7);
    background-color: $background-color_9;
  }
}

.noUi-target.noUi-target-info {
  .noUi-connects {
    background-color: $background-color_10;
    .noUi-connect {
      background-color: $background-color_11;
    }
  }
  .noUi-handle {
    border: 1px solid var(--bs-info);
    box-shadow: 0 3px 6px -3px rgba(var(--bs-info), 0.7);
    background-color: $background-color_11;
  }
}

.noUi-target.noUi-target-warning {
  .noUi-connects {
    background-color: $background-color_12;
    .noUi-connect {
      background-color: $background-color_13;
    }
  }
  .noUi-handle {
    border: 1px solid var(--bs-warning);
    box-shadow: 0 3px 6px -3px rgba(var(--bs-warning), 0.7);
    background-color: $background-color_13;
  }
}

.noUi-target.noUi-target-danger {
  .noUi-connects {
    background-color: $background-color_14;
    .noUi-connect {
      background-color: $background-color_15;
    }
  }
  .noUi-handle {
    border: 1px solid var(--hope-colors-danger10);
    box-shadow: 0 3px 6px -3px rgba(var(--hope-colors-danger10), 0.7);
    background-color: $background-color_15;
  }
}

.noUi-target.noUi-target-dark {
  .noUi-connects {
    background-color: $background-color_16;
    .noUi-connect {
      background-color: $background-color_17;
    }
  }
  .noUi-handle {
    border: 1px solid var(--bs-dark);
    box-shadow: 0 3px 6px -3px rgba(var(--bs-dark), 0.7);
    background-color: $background-color_17;
  }
}

.noUi-tooltip {
  // display: none;
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.15);
  background: var(--hope-colors-primaryDarkAlpha2);
  color: $color_1;
  font-size: 1rem;
  border: 0;
  padding: 0.5rem 0.75rem;
  border-radius: 0.475rem;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease-in-out;
}

.noUi-active .noUi-tooltip {
  opacity: 1;
  display: block;
}
