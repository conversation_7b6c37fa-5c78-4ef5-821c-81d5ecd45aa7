.glassBackground {
  backdrop-filter: blur(5px);
}

/*/********************
// Rainbow
//********************/
.rainbow-text {
  background-image: linear-gradient(to left, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82, #ffff99, #f79533, #f37055, #ef4e7b);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.rainbow-border {
  --borderWidth: 3px;
  position: relative;
  border-radius: var(--borderWidth) !important;
}

.rainbow-border:after {
  content: '';
  position: absolute;
  top: calc(-1 * var(--borderWidth));
  left: calc(-1 * var(--borderWidth));
  height: calc(100% + var(--borderWidth) * 2);
  width: calc(100% + var(--borderWidth) * 2);
  background: linear-gradient(90deg, #a166ab, #5073b8, #1098ad, #07b39b, #6fba82, #ffff99, #f79533, #f37055, #ef4e7b);
  border-radius: 9px;
  z-index: -1;
  animation: hue-rotate 5s ease normal infinite;
  background-size: 300% 300%;
}

.rainbow-border-shine {
  @extend .rainbow-border;
}

.rainbow-border-shine-no-anim {
  @extend .rainbow-border;
  animation: none !important;
}

.rainbow-border-shine:after {
  animation: rainbowBorderAnimatedgradient 15s ease normal infinite;
  height: 17px;
  top: 0;
  border-radius: 3px;
}

.rainbow-border-shine-no-anim:after {
  background-position: 0% 50%;
  height: 17px;
  top: 0;
  border-radius: 3px;
  animation: none !important;
}

.rainbow-border-image {
  --borderWidth: 3px;
  position: relative;
  background-clip: padding-box;
  border: solid var(--borderWidth) transparent;
  border-radius: 1em;

  &:before {
    content: '';
    position: absolute;
    top: 0; right: 0; bottom: 0; left: 0;
    z-index: -1;
    margin: var(--borderWidth);
    border-radius: inherit;
    background: linear-gradient(to right, red, orange);
  }
}

.hidden-file-input {
  position: absolute;
  display: none;
  top: 0;
  left: 0;
  width: 0;
  height: 0;
  opacity: 0;
}

.ellipsis-anim span {
  opacity: 0;
  -webkit-animation: ellipsis-dot 1.5s infinite;
  animation: ellipsis-dot 1.5s infinite;

  &:nth-child(1) {
    -webkit-animation-delay: 0.0s;
    animation-delay: 0.0s;
  }

  &:nth-child(2) {
    -webkit-animation-delay: 0.1s;
    animation-delay: 0.1s;
  }

  &:nth-child(3) {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
  }
}

@keyframes ellipsis-dot {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes hue-rotate {
  0% {
    filter: hue-rotate(0);
  }
  to {
    filter: hue-rotate(-360deg);
  }
};

/*/********************
// Animations
//********************/
@keyframes rainbowBorderAnimatedgradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
};

@keyframes pulse-border {
  0% {
    border-color: var(--hope-colors-tertiary1);
  }
  50% {
    border-color: white;
  }
  100% {
    border-color: var(--hope-colors-tertiary1);
  }
};
