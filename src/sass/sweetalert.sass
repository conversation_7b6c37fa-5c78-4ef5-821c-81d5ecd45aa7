/*/********************
// Sweet Alert
/*/---------------------/
@import 'sweetalert2/src/sweetalert2'
@import 'sweetalert2/src/variables'

$swal2-dark-theme-black: white
$swal2-dark-theme-white: #e1e1e1
$swal2-outline-color: rgba(50, 100, 150, .4)
$swal2-outline-color: lighten($swal2-outline-color, 10%)

$swal2-background: $swal2-dark-theme-black
$swal2-content-color: $swal2-dark-theme-white
$swal2-title-color: $swal2-dark-theme-white

//Sweet Alert - FOOTER
$swal2-footer-border-color: #555
$swal2-footer-color: darken($swal2-content-color, 15%)

//Sweet Alert - INPUT
$swal2-input-color: $swal2-dark-theme-white
$swal2-input-background: lighten($swal2-dark-theme-black, 10%)

//Sweet Alert - VALIDATION MESSAGE
$swal2-validation-message-background: lighten($swal2-dark-theme-black, 10%)
$swal2-validation-message-color: $swal2-dark-theme-white

//Sweet Alert - QUEUE
$swal2-progress-step-background: lighten($swal2-dark-theme-black, 25%)

//Sweet Alert - COMMON VARIABLES FOR CONFIRM AND CANCEL BUTTONS
$swal2-button-focus-box-shadow: 0 0 0 1px $swal2-background, 0 0 0 3px $swal2-outline-color

//Sweet Alert - TOAST
$swal2-toast-button-focus-box-shadow: 0 0 0 1px $swal2-background, 0 0 0 3px $swal2-outline-color
$swal2-toast-background: $swal2-dark-theme-black

.swal2-container
  z-index: calc(var(--hope-zIndices-modal) + 501) !important
  pointer-events: all !important

  .swal2-popup
    background: var(--hope-colors-primary1)
    // backdrop-filter: blur(5px);

  .swal2-styled.swal2-confirm
    background-color: var(--hope-colors-accent1)

.swal2-range
  background: transparent !important
  flex-direction: column
  align-items: center
  margin-bottom: 0 !important

  output
    position: relative
    display: block
    text-align: center
    font-size: 6em
    color: #999
    font-weight: 400

  #swal2-input
    width: 400px
    height: 15px
    -webkit-appearance: none
    background: #111
    outline: none
    border-radius: 15px
    overflow: hidden
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 1)

  &::-webkit-slider-thumb
    // @include theme('background', secondary);
    background: var(--hope-colors-accent1)
    -webkit-appearance: none
    width: 15px
    height: 15px
    border-radius: 50%
    cursor: pointer
    border: 4px solid #333
    box-shadow: -407px 0 0 400px themeValue(secondary)

.swal2-title
  color: #e1e1e1 !important
  font-weight: 600 !important

.swal2-html-container
  color: #e1e1e1 !important

.swal2-content
  color: #e1e1e1 !important

.swal2-toast
  box-shadow: 0 0 5px #d9d9d9 !important

.swal2-popup
  border: 3px solid #ffffff42 !important

.swal2-nopadding
  .swal2-content, .swal2-modal
    margin: 0
    padding: 0

.swal2-textarea
  color: var(--hope-colors-neutral12) !important
  resize: none
  border-color: var(--hope-colors-primaryDark1) !important
  background: var(--hope-colors-primaryDark1) !important

  &::placeholder
    color: gray !important

  &:focus
    box-shadow: none !important

  &:active, &:focus
    border-color: var(--hope-colors-accent1) !important

.swal2-input
  height: 45px !important
  // width: 100% !important
  color: var(--hope-colors-neutral12) !important
  border-color: var(--hope-colors-primaryDark1) !important
  background: var(--hope-colors-primaryDark1)!important

  &::placeholder
    color: gray !important

  &:focus
    box-shadow: none !important

  &:active, &:focus
    border-color: var(--hope-colors-accent1) !important

.swal2-select
  border: 1px solid white
  color: var(--hope-colors-neutral12) !important
  cursor: pointer !important

  option
    color: var(--hope-colors-neutral11) !important
