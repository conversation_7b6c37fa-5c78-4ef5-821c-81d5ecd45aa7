$font-family_1: Arial, Helvetica, sans-serif;
$background-color_1: rgb(197, 197, 197);
$background-color_2: var(--hope-colors-primaryDark1);

.keyboard-base {
  width: 100%;
  padding: 20px;
  background-color: var(--hope-colors-primaryLight);
  border-radius: 10px;
}

.key {
  transition: border 100ms ease-in-out, transform 100ms ease;
  background-color: $background-color_2;
  border: 2px solid var(--hope-colors-primaryLight);
  border-radius: 5px;
  grid-column: span 2;
  font-size: 20px;
  padding: 5px;
  text-align: center;
  user-select: none;
  text-transform: uppercase;
  overflow: hidden;
  position: relative;
  cursor: pointer;
  &:active {
    transform: scale(1.01);
  }
  &:hover {
    border: 1px solid var(--hope-colors-accent1);
  }
}

.key::after {
  content: attr(data-assigned-note);
  font-size: 10px;
  color: gray;
  position: absolute;
  top: 0;
  opacity: 0.5;
}

.key::before {
  content: attr(data-assigned-key);
  font-size: 10px;
  color: gray;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0.5;
}

.delete {
  grid-column: span 4;
}

.backspace {
  grid-column: span 4;
  font-size: 10px;
}

.tab {
  grid-column: span 3;
}

.backslash {
  grid-column: span 3;
}

.capslock {
  grid-column: span 4;
}

.return {
  grid-column: span 4;
}

.leftshift {
  grid-column: span 5;
}

.rightshift {
  grid-column: span 5;
}

.leftctrl, .rightctrl, .alt {
  grid-column: span 3;
}

.command {
  grid-column: span 3;
  font-size: 14px;
}

.space {
  grid-column: span 13;
}

.unassigned {
  opacity: 0.7
}

.disabled {
  pointer-events: none;
  background-color: gray !important;
}

.piano-key {
  background-color: white;
  height: 150px;
  width: 100%;
  position: relative;
  cursor: pointer;
  &:hover {
    background-color: var(--hope-colors-accent1) !important;
  }
}

.piano-key-black {
  top: 0;
  position: absolute;
  right: -7px;
  z-index: 2;
  width: 80%;
  height: 65%;
  cursor: pointer;
  background-color: black;
  &:hover {
    background-color: var(--hope-colors-tertiary1) !important;
  }
}

.piano-key-assigned {
  background-color: red !important;
}
