$borderRadius: 5px
$commandsMatchingContainerHeight: 30px

.container
  position: absolute
  left: 0
  bottom: calc(var(--readOnly_ChatBarHeight) + 2px)
  border-radius: $borderRadius
  width: 100%
  background: var(--hope-colors-primary1)
  border: 2px solid var(--hope-colors-primary5)
  z-index: 2

.containerHeader
  font-weight: bold
  margin-left: 5px
  text-transform: uppercase
  font-size: 12px
  color: var(--hope-colors-neutral12)

.itemsContainer
  max-height: 200px
  width: 100%
  overflow-y: scroll
  padding: 5px
  padding-right: 0px

.itemRowIsActive
  cursor: pointer
  background: var(--hope-colors-primaryDark1)

.itemRow
  padding: 5px
  transition: background 0.1s ease
  border-radius: $borderRadius

  &:hover
    @extend .itemRowIsActive