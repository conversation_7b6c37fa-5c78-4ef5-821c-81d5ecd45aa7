.instrumentSelectionList
  position: absolute
  right: 0
  z-index: 2
  overflow-y: scroll
  overflow-x: hidden
  padding-right: 5px
  --mask: linear-gradient(to bottom,rgba(0,0,0, 1) 0, rgba(0,0,0, 1) 90%,rgba(0,0,0, 0) 95%, rgba(0,0,0, 0) 0 ) 100% 50% / 100% 100% repeat-x
  -webkit-mask: var(--mask)
  mask: var(--mask)
  transition: transform 0.2s, height 0.2s

.instrumentCardParent
  width: 100%
  pointer-events: none

.instrumentSelectionCard
  position: absolute
  right: 0
  overflow: hidden
  transition: transform 0.2s, width 0.2s, filter 0.2s
  pointer-events: all
  transform: scale(.95) translate(5px, 0px)

  &:active
    filter: brightness(90%) !important

  &:hover
    z-index: 5
    transform: scale(.97) translate(5px, 0px)
    filter: brightness(120%)
    cursor: pointer

  $instrumentImageSize: 25px
  .instrumentImage
    width: $instrumentImageSize
    height: $instrumentImageSize
    top: 10px
    left: 10px
    margin-left: 10px
    margin-bottom: 4px
    cursor: inherit

  .instrumentIndex
    color: var(--hope-colors-neutral12)
    font-weight: bold
    font-size: 12px
    cursor: inherit
    position: absolute
    right: 10px
    top: 0

  .instrumentContainer
    height: 40px
    margin-top: 5px
    border-bottom: solid white 3px
    padding-bottom: 5px
    overflow: hidden
    -webkit-box-sizing: border-box
    cursor: inherit

    .instrumentTitle
      text-transform: capitalize
      font-weight: bold
      font-size: 16px
      transition: color 0.2s
      width: 100%

      &:hover
        color: var(--hope-colors-accent1)
