@font-face {
  font-family: 'Now';
  font-style: normal;
  src: url(https://assets.pianorhythm.io/fonts/Now-Regular.otf) format('opentype');
  font-display: block;
}

#root {
  position: relative;
  height: 100dvh;
}

html {
  overflow-y: hidden;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Roboto', 'Segoe UI', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
  padding: .2em .4em;
  margin: 0;
  font-size: 85%;
  background-color: var(--hope-colors-primaryDark2);
  border-radius: 6px;
}

code br {
  display: none;
}

*:focus {
  outline: none;
}

h1 {
  font-family: 'Helvetica Neue', sans-serif;
  font-weight: bold;
  letter-spacing: -1px;
  line-height: 1;
  text-align: center;
}

.disabled {
  opacity: 0.3;
  cursor: not-allowed;
  pointer-events: none;
  user-select: none;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: var(--hope-colors-primary5);
  box-shadow: inset 0 0 6px rgba(100, 100, 100, 0.5);
}

::-webkit-scrollbar-corner {
  background: transparent;
}

a:hover {
  text-decoration: underline;
}

blockquote {
  background: var(--hope-colors-primaryDark2);
  border-left: 10px solid var(--hope-colors-accent1);
  padding: 0.5em 10px;
  border-radius: 10px;
  quotes: "\201C" "\201D" "\2018" "\2019";
}

main {
  text-align: center;
  padding: 1em;
  margin: 0 auto;
}

h1 {
  color: #335d92;
  text-transform: uppercase;
  font-size: 4rem;
  font-weight: 100;
  line-height: 1.1;
  margin: 4rem auto;
  max-width: 14rem;
}

.unselectable {
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;

  /*
    Introduced in Internet Explorer 10.
    See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
  -ms-user-select: none;
  user-select: none;
}

.undraggable {
  -webkit-user-drag: none;
  user-drag: none;
  -moz-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

h1 {
  display: block;
  font-size: 2em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

h2 {
  display: block;
  font-size: 1.5em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

h3 {
  display: block;
  font-size: 1.17em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

h4 {
  display: block;
  font-size: 1em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

h5 {
  display: block;
  font-size: .83em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

h6 {
  display: block;
  font-size: .67em !important;
  margin-left: 0;
  margin-right: 0;
  font-weight: bold !important;
}

/* Transition Animations */
.slide-fade-enter-active,
.slide-fade-exit-active {
  transition: opacity 0.3s, transform 0.3s;
}

.slide-fade-enter,
.slide-fade-exit-to {
  transform: translateX(10px);
  opacity: 0;
}

.slide-fade-enter {
  transform: translateX(-10px);
}

.fade-enter-active,
.fade-exit-active {
  position: absolute;
  transition: opacity 0.5s;
}

.fade-enter,
.fade-exit-to {
  opacity: 0;
}

/* ------------------ */
/* Slide Fade Down */
/* ------------------ */
.slide-fade-down-enter,
.slide-fade-down-exit-to {
  height: 0vh;
}

.slide-fade-down-exit,
.slide-fade-down-enter-to {
  height: 100vh;
}

.slide-fade-down-enter-active,
.slide-fade-down-exit-active {
  transition: height 1s cubic-bezier(0.22, 0.61, 0.36, 1), opacity 2s cubic-bezier(1, 0.5, 0.8, 1);
}

/* Transition Animations - End */

.circle-effect {
  position: absolute;
  width: 0px;
  height: 0px;
  padding: 5px;
  border-radius: 50%;
  animation: scaleIn 2s infinite cubic-bezier(.36, .11, .89, .32);
}

@keyframes scaleIn {
  0% {
    transform: scale3d(.5, .5, .5) translateZ(0);
    opacity: .05;
  }

  50% {
    opacity: 0.2;
  }

  100% {
    transform: scale3d(2.5, 2.5, 2.5) translateZ(0);
    opacity: 0;
  }
}