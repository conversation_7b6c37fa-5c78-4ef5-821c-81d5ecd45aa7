/*/********************
// General
/*/---------------------/
td, th
  text-align: start
  padding-right: 10px

.plain-text-file
  overflow-x: hidden
  overflow-y: scroll

.glassBackgroundFull
  position: absolute
  top: 0
  width: 100vw
  height: 100vh
  opacity: 0.5
  translateZ: 0px
  will-change: transform
  backface-visibility: hidden
  backdrop-filter: blur(3px)
  transition: backdrop-filter 0.4s ease-in-out

$elementFadeIn: 3s
.elementFadeIn
  -webkit-animation: fadeIn $elementFadeIn
  -moz-animation: fadeIn $elementFadeIn
  -o-animation: fadeIn $elementFadeIn
  -ms-animation: fadeIn $elementFadeIn
  animation: fadeIn $elementFadeIn

@keyframes fadeIn
  0%
    opacity: 0
  100%
    opacity: 1

/*/********************
// Active Element
/*/---------------------/
.elementActive
  animation: elementActiveAnimation 200ms linear

.elementInactive
  animation: elementActiveAnimation 200ms linear

@keyframes elementActiveAnimation
  10%
    transform: scale(1)
  20%
    transform: scale(1.5, 0.95)
  100%
    transform: scale(1)

// @keyframes elementClick
//   10%
//     transform: translate(-50px, 0px) scale(1)
//   20%
//     transform: translate(-50px, 0px) scale(1.05, 0.95)
//   100%
//     transform: translate(-50px, 0px) scale(1)
/*/********************
// Tags
/*/---------------------/
.pr-tag-chatmsg
  color: var(--hope-colors-neutral12)
  font-size: 0.65em
  font-weight: 500
  padding: 0.072rem 0.275rem
  border-radius: 3px
  text-transform: uppercase
  vertical-align: baseline
  flex-shrink: 0
  line-height: 1.3

.pr-tag-chatmsg.rainbow-border-shine
  --borderWidth: 1px

/*/********************
// Mod Dashboard
/*/---------------------/
.mod-dashboard-iframe
  border: solid var(--hope-colors-accent1) 2px
  border-radius: 5px
  width: 100%
  height: 700px

/*/********************
// Pagination
/*/---------------------/
.pagination
  background: var(--hope-colors-primaryAlpha)
  padding: 5px

  .page-btn
    cursor: pointer

    &:hover
      color: var(--hope-colors-accent1)

    &:active
      color: var(--hope-colors-neutral12)
      transform: scale(1.05)

  .page-item
    @extend .page-btn
    border-radius: 3px
    width: 20px
    background: transparent
    text-align: center
    cursor: pointer

  .page-item.--active
    background: var(--hope-colors-accent1) !important
    &:hover
      color: var(--hope-colors-neutral12)
