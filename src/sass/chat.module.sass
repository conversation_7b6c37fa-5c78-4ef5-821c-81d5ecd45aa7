$borderRadius: 5px

.userAvatar
  background: var(--hope-colors-primaryDarkAlpha2)
  border-radius: 15px
  margin-right: 4px

  img
    border-radius: 15px

.chatMinimizedBadge
  cursor: default
  position: absolute
  left: 2px
  top: 1px
  transition: background 0.2s

  &:hover
    background: var(--hope-colors-primaryDark1)

.chatMinimizedText
  user-select: none
  transition: margin-left 0.2s

.chatWindowButtons
  position: absolute
  top: -5px
  right: 10px
  z-index: 5

.chatContainer
  position: absolute
  top: 10px
  left: calc(var(--sidebarListWidth) + 10px)
  transition: opacity 0.3s ease-in-out, bottom 0.3s, height 0.3s, left 0.3s, top 0.2s
  z-index: 2

.chatContainerList::-webkit-scrollbar
  width: 8px !important

.chatContainerList::-webkit-scrollbar-thumb
  background: var(--hope-colors-primary5) !important

.chatContainerList
  scroll-behavior: smooth
  border-radius: 5px
  overflow: hidden
  position: relative
  transition: background 0.3s ease-in-out, width 0.2s ease-in-out
  margin-bottom: 9px

.chatMessageWrapper
  transition: opacity 0.2s, background-color 0.05s ease
  cursor: context-menu !important
  border-radius: 3px
  position: relative
  --options-visibility: hidden
  --timestamp-opacity: 0.25

  &:hover
    background: rgba(255,255,255,0.1)
    --options-visibility: visible
    --timestamp-opacity: 1

  .timeStampOpacity
    opacity: var(--timestamp-opacity)

  .showOptionsContainer
    overflow: hidden
    min-width: 50px
    visibility: var(--options-visibility)

.chatBar
  position: absolute
  bottom: 0
  width: 100%
  transition: opacity 0.2s ease-in-out, width 0.4s
  pointer-events: all !important
  z-index: 2

  .chatBarWrapper
    width: 100%
    height: 100%
    background: var(--hope-colors-primaryAlpha2)
    border-radius: 5px
    border-bottom-right-radius: 5px

  .chatBarInput
    border: none
    overflow: auto
    outline: none !important
    box-shadow: none
    bottom: 0
    cursor: text
    transition: background 0.2s ease-in-out
    resize: none
    font-size: smaller
    height: 30px
    min-height: 0px
    line-height: 14px

    &:focus
      box-shadow: 0 0 0 2px var(--hope-colors-primary5)

  .chatBarInputCommandsDisplayed
    box-shadow: 0px 0px 0px 2px var(--hope-colors-primary5) !important
    border-top-left-radius: 0px !important
    border-top-right-radius: 0px !important
    border-bottom-right-radius: 0px !important

.chatBarIconsContainer
  height: 100%
  transition: color 0.2s, transform 0.2s
  padding: 5px
  padding-right: 10px

  svg
    cursor: pointer

    &:hover
      color: var(--hope-colors-accent1)
      transform: scale(1.1)

    &:active
      transform: scale(1.2)

.chatBarReplyToMessageContainer
  position: absolute
  background: var(--hope-colors-accent1)
  height: 20px
  top: -15px
  width: 100%
  left: 0
  border-top-left-radius: 10px
  border-top-right-radius: 10px
  z-index: 2
  font-size: 12px
  font-weight: bold
  padding-left: 10px
  line-height: 20px

.chatBarReplyToMessageTarget
  cursor: pointer
  color: var(--hope-colors-primaryDark1)

  &:hover
    color: var(--hope-colors-neutral12)
    // font-weight: bolder
    // text-decoration: underline

.chatMessage
  cursor: auto
  position: relative
  padding-left: 3px
  margin-bottom: 3px
  max-height: 300px
  font-size: 0.9em
  letter-spacing: 0.1px
  white-space: normal
  word-wrap: break-word
  transition: opacity 0.2s, background-color 0.25s ease
  word-break: break-word
  pointer-events: all
  align-items: flex-start

  a:link
    color: var(--hope-colors-tertiary1)
    text-decoration: underline

    &:hover
      color: var(--hope-colors-neutral12)

  a:visited
    color: #888
    &:hover
      color: var(--hope-colors-neutral11)

  .markDownContainer
    --marginLeft: 0px
    max-height: 250px
    overflow: hidden
    overflow-y: auto
    width: 100%
    font-weight: 300
    padding-bottom: 5px
    padding-right: 5px

    p
      margin-block-start: 0.1em
      margin-block-end: 0.1em

    blockquote
      margin-bottom: 5px
      padding: 0.1em 10px
      border-radius: 5px

    div
      display: inline-block

.chatMessageClientTagged
  border-radius: $borderRadius
  background-color: #ecde1c32
  border-left: 3px solid var(--hope-colors-warning10)

.chatMessageHighlighted
  background-color: #ecde1c75

.chatMessageSetToBeDeleted
  background-color: #ec1c5791

.chatMessageBeingRepliedTo
  background-color: #1c87ec91

.chatMessageBeingEdited
  background-color: var(--hope-colors-primaryDark1)

.chatMessageAnimateIn
  -webkit-animation: MessageIn 0.3s
  -moz-animation: MessageIn 0.3s
  animation: MessageIn 0.3s
  -webkit-animation-timing-function: ease-in-out
  -moz-animation-timing-function: ease-in-out
  animation-timing-function: ease-in-out

.chatUsersTyping
  width: 100%
  height: 15px
  position: absolute
  color: gray
  font-size: xx-small
  left: 3px

.chatEmojisContainer
  position: absolute
  top: 0
  right: -20px

.chatTimestamp
  font-size: 8px
  font-weight: bold
  color: var(--hope-colors-neutral11)
  white-space: nowrap

.usertagsContainer
  font-size: 0.8em

.chatMentionedUser
  cursor: pointer
  width: 50px
  display: -webkit-box
  -webkit-line-clamp: 1
  line-clamp: 1
  -webkit-box-orient: vertical
  overflow: hidden
  font-weight: bold
  font-size: 0.7em
  color: var(--hope-colors-tertiary1)

// Set usertagsContainer scroll bar width
.usertagsContainer::-webkit-scrollbar
  height: 3px !important

.userName
  font-weight: 600
  white-space: nowrap

  &:hover
    cursor: pointer
    text-decoration: underline

.chatMessageItemOptionsContainer
  z-index: 2
  position: absolute
  right: 5px
  top: -15px

.chatmessageReply
  color: darkgray
  font-size: small
  height: 20px
  margin-left: 40px
  white-space: nowrap
  display: flex !important
  transition: color 0.2s ease
  max-width: 275px

  &:before
    --background-accent: gray
    --avatar-size: 10px
    --spine-width: 2px
    --reply-spacing: -16px
    --gutter: -24px
    z-index: 0
    content: ""
    display: block
    position: absolute
    -webkit-box-sizing: border-box
    box-sizing: border-box
    height: calc(50% - 24px)
    top: 13px
    right: 100%
    bottom: 55%
    left: calc(var(--avatar-size)/2*-1 + var(--gutter)*-1)
    margin: calc(var(--spine-width) * -1 / 2) var(--reply-spacing) calc(0.125rem - 4px) calc(var(--spine-width) * -1 / 2)
    border-left: var(--spine-width) solid var(--background-accent)
    border-bottom: 0 solid var(--background-accent)
    border-right: 0 solid var(--background-accent)
    border-top: var(--spine-width) solid var(--background-accent)
    border-top-left-radius: 6px

  &:hover
    color: var(--hope-colors-neutral11)

  span, div
    cursor: pointer

  .replyMessageUser
    margin-right: 5px
    font-weight: bold

  .replyMessageText
    min-width: 0
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis

@keyframes MessageIn
  0%
    transform: translateX(1%)
    -ms-transform: translateX(1%)
    -webkit-transform: translateX(1%)
    opacity: 0

  50%
    transform: translateX(5%)
    -ms-transform: translateX(5%)
    -webkit-transform: translateX(5%)

  100%
    transform: translateX(0%)
    -ms-transform: translateX(0%)
    -webkit-transform: translateX(0%)
    opacity: 1

.messageFlash
  background: var(--hope-colors-tertiary1)
  animation-name: flash
  animation-duration: 400ms
  animation-iteration-count: 8
  animation-timing-function: ease
  animation-direction: alternate
  transition: opacity 0.3s

@keyframes flash
  0%
    opacity: 0
  100%
    opacity: 1
