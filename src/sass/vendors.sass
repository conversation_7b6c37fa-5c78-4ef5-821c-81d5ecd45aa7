/*/********************
// My Context Menu
/*/---------------------/
.__my-context-menu
  z-index: 9999
  border: 1px #e6e6e624 solid !important
  background: var(--hope-colors-primaryDark1) !important
  box-shadow: 5px 5px 5px 2px #00000066

  .type-undefined
    color: var(--hope-colors-neutral12) !important

  .type-button, .type-submenu, .type-undefined
    font-weight: 600 !important
    cursor: pointer
    &:hover
      background: var(--hope-colors-accent1) !important

/*/********************
// Croppie
/*/---------------------/
.cr-boundary
  border-radius: 10px !important

  .cr-viewport
    border-radius: 20px
    box-shadow: 0 0 2000px 2000px rgb(0 0 0 / 70%)

/*/********************
// Hope Solid
/*/---------------------/
.hope-divider
  color: gray

.hope-notification__list
  max-width: var(--hope-sizes-xs) !important

/*/********************
// Sweet Alert - Account Info Settings
/*/---------------------/
.pr-changeemail-subtext, .pr-changepassword-subtext, .pr-deleteaccount-subtext
  color: var(--hope-colors-neutral12)
  opacity: 0.8
  margin-top: -10px
  margin-bottom: 20px

.pr-changepassword-label,.pr-onlinepassword-label
  float: left
  text-transform: uppercase
  font-size: 14px
  color: var(--hope-colors-neutral12)
  font-weight: 550

.pr-modal-text-input
  margin: 0 !important
  margin-top: 10px !important
  margin-bottom: 10px !important
  height: 36px
  // @include theme('border-color', primary-dark)
  // @include theme('background', primary-dark2)

  &:focus
    box-shadow: none

  &:active, &:focus
    // @include theme('border-color', secondary)

/*/********************
// Ace Editor
/*/---------------------/
.ace_editor.ace_autocomplete
  width: 400px !important

/*/********************
// Solid Context-Menu
/*/---------------------/
.solid-contextmenu
  border-radius: 0 !important
  z-index: calc(var(--hope-zIndices-overlay) + 101) !important
  border: 1px #e6e6e624 solid !important
  background: var(--hope-colors-primaryDark1) !important
  box-shadow: 1px 1px 1px 0px #00000066 !important

  .solid-contextmenu__item__content
    &:hover
      background-color: var(--hope-colors-accent1) !important

/*/********************
// Solid Markdown
/*/---------------------/
.solid-markdown
  hr
    border-style: inset
    border-width: 1px
  li
    margin-left: 2em
  // p
  //   margin-block-start: 1em
  //   margin-block-end: 1em
  h1
    margin-top: 0.67em
    margin-bottom: 0.67em
  h2
    margin-top: 0.83em
    margin-bottom: 0.83em
  h3
    margin-top: 1em
    margin-bottom: 1em
  h4
    margin-top: 1.33em
    margin-bottom: 1.33em
  h5
    margin-top: 1.67em
    margin-bottom: 1.67em
  h6
    margin-top: 2.33em
    margin-bottom: 2.33em

/*/********************
// Solid Tooltip
/*/---------------------/
[data-corvu-tooltip-content]
  font-size: 0.8rem
  filter: drop-shadow(0 0 3px var(--hope-colors-neutral1))

/*/********************
// Solid Toast
/*/---------------------/
.pr-solid-toast, .pr-solid-toast-custom
  background: var(--hope-colors-primaryAlpha2) !important
  color: var(--hope-colors-neutral12) !important
  border: 2px solid var(--hope-colors-neutral12)

.pr-solid-toast-custom
  padding: 10px
  border-radius: 5px
