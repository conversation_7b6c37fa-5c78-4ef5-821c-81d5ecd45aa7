$profileCardWidth: 300px
$profileCardBorderRadius: 5px
$profileCardZindex: 15

:root
  --profileCardZindex: #{$profileCardZindex}

.profileCardBackgroundOverlay
  position: absolute
  top: 0
  left: 0
  width: 100vw
  height: 100dvh
  z-index: calc(var(--profileCardZindex) - 1)

.pr-profile-user-badge
  background: var(--hope-colors-neutral12)
  color: var(--hope-colors-primary1)
  font-size: 10px

.profileCardUserVolumeIcons
  transition: transform 0.1s ease
  cursor: pointer
  &:hover
    color: var(--hope-colors-tertiary1)

.usernameHeader
  font-weight: 600

.usertagText
  color: var(--hope-colors-neutral12)

.profileCardFooterDivider
  color: gray
  margin-top: 5px
  margin-bottom: 5px

.footerMetaContainer
  width: 100%
  white-space: nowrap
  overflow: hidden

  .metaText
    text-overflow: ellipsis
    overflow: hidden
    width: 100%
    font-size: 10px

.miniProfileBG
  position: absolute
  top: 0
  left: 0
  z-index: -1
  width: 100%
  height: 100%

  img
    // max-height: 100%
    height: 100%
    filter: blur(1.5px) brightness(0.3)

.pr-profile-card
  position: absolute
  width: $profileCardWidth
  border-radius: $profileCardBorderRadius
  z-index: $profileCardZindex
  box-shadow: 5px 5px 5px 2px #00000066
  overflow: hidden
  overflow-y: auto
  transition: top 0.1s ease, height 0.5s ease//, transform 0.1s

  .profileCardHeader
    position: relative
    padding-top: 10px
    padding-bottom:10px
    background: var(--hope-colors-primaryDarkAlpha2)

    .userColorBG
      --borderWidth: 10px
      border-top-right-radius: $profileCardBorderRadius
      border-top-left-radius: $profileCardBorderRadius
      position: absolute
      width: 100%
      top: 0
      z-index: 0

    .userBGIsClient
      &:hover
        cursor: pointer

        &::before
          transition: background 10.2s ease
          content: ""
          position: absolute
          background: rgba(0,0,0,0.5)
          overflow: hidden
          width: 100%
          height: 100%

        &::after
          content: "Upload"
          position: absolute
          font-size: 14px
          font-weight: bold
          top: 0
          right: 5px
          overflow: hidden

    .userAvatar
      margin-top: 10px
      color: var(--hope-colors-neutral12)
      outline: solid 5px var(--hope-colors-primaryDark2)
      filter: drop-shadow(2px 4px 6px black)

    .userAvatarIsClient
      &:active
        transform: scale(1.05)

      &:hover
        cursor: pointer

        &::before
          transition: background 10.2s ease
          content: ""
          position: absolute
          background: rgba(0,0,0,0.5)
          overflow: hidden
          border-radius: 200px
          width: 100%
          height: 100%

        &::after
          content: "Upload Profile Image"
          position: absolute
          font-size: 14px
          top: 0
          width: 100%
          height: 100%
          overflow: hidden

  .profileCardBody
    color: var(--hope-colors-neutral12)
    padding: 10px
    border-top: 1px solid white
    background: var(--hope-colors-primaryDarkAlpha2)
    overflow: hidden

    .textHeader
      font-weight: bold
      font-size: 14px !important
      text-transform: uppercase
      color: var(--hope-colors-tertiary1)
      margin-bottom: -5px
      opacity: 0.9

    .userAboutContainer
      max-height: 150px
      overflow: hidden
      overflow-y: scroll
      white-space: pre-wrap
      width: 100%

  .profileCardFooter
    padding: 10px
    color: var(--hope-colors-neutral12)
    background: var(--hope-colors-primaryDarkAlpha2)

    .footerButton
      color: var(--hope-colors-neutral12)