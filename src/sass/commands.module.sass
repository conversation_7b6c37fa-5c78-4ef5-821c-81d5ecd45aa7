$borderRadius: 5px
$commandsMatchingContainerHeight: 30px

.container
  position: absolute
  left: -2px
  bottom: calc(var(--readOnly_ChatBarHeight) + 2px)
  border-radius: $borderRadius
  border-bottom-left-radius: 0
  width: 101%
  background: var(--hope-colors-primary1)
  border: 2px solid var(--hope-colors-primary5)
  border-bottom: 0
  z-index: 2

.containerSideBar
  width: 40px
  border-top-left-radius: $borderRadius
  background: var(--hope-colors-primary1)

  .commandModuleContainer
    padding: 4px

  .moduleAvatar
    &:hover
      cursor: pointer

.commandsMatchingContainer
  width: 100%
  padding: 5px
  font-weight: bold
  background: var(--hope-colors-primaryDark1)

.commandsContainer
  max-height: 200px
  padding: 5px
  width: 100%
  overflow-y: scroll
  background: var(--hope-colors-primaryDark1)

  .commandContainer
    color: var(--hope-colors-neutral12)
    width: 100%
    padding: 5px
    padding-left: 10px
    cursor: pointer
    border-radius: 5px
    font-size: 14px
    height: 100%

    &:hover
      background: var(--hope-colors-primary1)

    .commandName
      font-weight: bold

    .commandShortDesc
      font-weight: 300
      color: var(--hope-colors-neutral11)