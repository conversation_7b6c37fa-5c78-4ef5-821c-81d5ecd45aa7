import { action, json } from "@solidjs/router";
import { getSession, UserSessionHelper } from "~/lib/server";
import { AuditLogDto, PaginatedDataResponse } from "~/types/api.types";
import { RoomDtoAPI } from "~/types/room.types";
import { ApiUserDto } from "~/types/user.types";

export type QuerySortInput = {
  limit: number, skip?: number,
  query?: string;
  sort?: string;
};

// ---------------------- User ----------------- //

/**
 * Retrieves a paginated list of active users from the PianoRhythm server.
 *
 * @param limit - The maximum number of users to retrieve
 * @param skip - Optional parameter to skip a number of users (for pagination)
 * @returns A Promise containing paginated user data with metadata
 *
 * @throws {Error} When the server request fails
 *
 * @example
 * ```typescript
 * const result = await getActiveUsers(10, 0);
 * console.log(result.data); // Array of active users
 * console.log(result.metaData.totalCount); // Total count of active users
 * ```
 */
export async function getActiveUsers(limit: number, skip?: number): Promise<PaginatedDataResponse<ApiUserDto>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/active?limit=${limit}&skip=${skip ?? 0}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getActiveUsers]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}

export async function getActiveUsersCount(): Promise<{ count: number; }> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/active/count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getActiveUsersCount]", e);
    return { count: 0 };
  }
}

export async function getMembersCount(): Promise<{ count: number; }> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/members/count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getMembersCount]", e);
    return { count: 0 };
  }
}

export async function getNewestMembers(limit: number = 5): Promise<PaginatedDataResponse<ApiUserDto>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/members/newest?limit=${limit}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getNewestMembers]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}

/**
 * Retrieves all members with pagination support
 * @param input - Query and sorting parameters for fetching members
 * @param input.limit - Maximum number of members to return
 * @param input.skip - Number of members to skip (for pagination)
 * @param input.query - Optional search query string
 * @param input.sort - Optional sorting parameter
 * @returns Promise containing paginated member data with metadata
 * @throws {Error} When the API request fails
 */
export async function getAllMembers(input: QuerySortInput): Promise<PaginatedDataResponse<ApiUserDto>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/users/members?limit=${input.limit}&skip=${input.skip ?? 0}`, {
      method: "POST",
      body: JSON.stringify({ query: input.query ?? "", sort: input.sort ?? "" }),
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getAllMembers]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}

// ---------------------- Rooms ----------------- //

/**
 * Retrieves a paginated list of active rooms from the server.
 *
 * @param limit - The maximum number of rooms to return
 * @param skip - Optional. The number of rooms to skip for pagination purposes
 * @returns A Promise that resolves to a PaginatedDataResponse containing RoomDtoAPI objects
 *
 * @throws Will log error to console and return empty paginated response if request fails
 */
export async function getActiveRooms(limit: number, skip?: number): Promise<PaginatedDataResponse<RoomDtoAPI>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/rooms/active?limit=${limit}&skip=${skip ?? 0}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getActiveRooms]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}

/**
 * Retrieves the count of currently active rooms from the server.
 *
 * This server action fetches the number of active rooms by making a GET request
 * to the PIANORHYTHM_SERVER_URL endpoint.
 *
 * @returns {Promise<{ count: number }>} A promise that resolves to an object containing
 * the count of active rooms. Returns { count: 0 } if the request fails.
 *
 * @throws Will catch and log any errors during the fetch operation,
 * but will not throw them to the caller.
 */
export async function getActiveRoomsCount(): Promise<{ count: number; }> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/rooms/active/count`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getActiveRoomsCount]", e);
    return { count: 0 };
  }
}

/**
 * Fetches the most recently created active rooms from the server.
 *
 * @param limit - The maximum number of rooms to retrieve (defaults to 5)
 * @returns A promise that resolves to a paginated response containing room data
 * @throws Will log error and return empty paginated response if fetch fails
 *
 * @example
 * ```typescript
 * const rooms = await getNewestRooms(10);
 * console.log(rooms.data); // Array of most recent rooms
 * console.log(rooms.metaData.totalCount); // Total count of rooms
 * ```
 */
export async function getNewestRooms(limit: number = 5): Promise<PaginatedDataResponse<RoomDtoAPI>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/rooms/active/newest?limit=${limit}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getNewestRooms]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}

export const deleteRoom = action(async (roomId: string) => {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/rooms/${roomId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });

    let jsonResponse = await response.json();
    return json({ message: jsonResponse.message });
  } catch (e) {
    console.error("[deleteRoom]", e);
    return json({ message: "Failed to delete room" }, { status: 400 });
  }
});

// ---------------------- Server ----------------- //

export async function getAuditLogs(
  limit: number,
  skip?: number,
  query?: string,
  sort?: string
): Promise<PaginatedDataResponse<AuditLogDto>> {
  "use server";
  try {
    let session = await getSession();
    let response = await fetch(`${process.env.PIANORHYTHM_SERVER_URL}/api/server/audit-logs/query?limit=${limit}&skip=${skip ?? 0}`, {
      method: "POST",
      body: JSON.stringify({
        query: query ?? "",
        sort: sort ?? ""
      }),
      headers: {
        "Content-Type": "application/json",
        ...UserSessionHelper.getHeaders(session.data),
      }
    });
    return await response.json();
  } catch (e) {
    console.error("[getAuditLogs]", e);
    return { data: [], metaData: { totalCount: 0, totalPages: 0 } };
  }
}