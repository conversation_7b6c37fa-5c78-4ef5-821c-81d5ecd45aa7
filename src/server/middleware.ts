"use server";

import { createMiddleware } from "@solidjs/start/middleware";
import { setResponseHeader } from "vinxi/http";

export default createMiddleware({
  onRequest: [
    _event => {
    }
  ],
  onBeforeResponse: [
    _event => {
      setResponseHeader("Cross-Origin-Embedder-Policy", "require-corp");
      setResponseHeader("Cross-Origin-Opener-Policy", "same-origin");
      setResponseHeader("Cross-Origin-Resource-Policy", "cross-origin");
    }
  ]
});
