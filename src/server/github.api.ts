/// <reference types="node" />
// @refresh reload
import { Octokit } from "@octokit/rest";
import { z } from "zod";

const octokit = new Octokit({
  auth: process.env.PIANORHYTHM_GITHUB_ACCESS_TOKEN,
});

export async function getGithubProjects() {
  const { data } = await octokit.search.issuesAndPullRequests({
    q: `repo:PianoRhythm/pianorhythm is:issue is:open`,
    sort: "created",
    order: "desc",
    per_page: 1,
  });

  return data;
}

const getGitHubIssuesSchema = z.object({
  owner: z.string().optional(),
  repo: z.string().optional(),
  state: z.string().optional().default("open"),
  labels: z.string().optional(),
  sort: z.string().optional().default("created"),
  direction: z.string().optional(),
  per_page: z.number().optional(),
  page: z.number().optional(),
  in_progress: z.boolean().optional(),
});

/**
 * Retrieves GitHub issues based on the provided parameters.
 *
 * @param params - The parameters for retrieving GitHub issues.
 * @returns An object containing the retrieved GitHub issues and the total count.
 */
export async function getGitHubIssues(params: z.infer<typeof getGitHubIssuesSchema>) {
  "use server";
  try {
    let input = { input: getGitHubIssuesSchema.parse(params) };

    let labels = input.input.labels ? input.input.labels.split(",") : [];
    labels = labels.map(x => `"${x}"`);

    let graphql = await octokit.graphql<{
      repository: {
        issues: {
          totalCount: number;
          pageInfo: {
            endCursor: string;
            hasNextPage: boolean;
            hasPreviousPage: boolean;
          };
          nodes: {
            id: number;
            number: number;
            title: string;
            body: string;
            state: string;
            createdAt: string;
            updatedAt: string;
            closedAt: string;
            author: {
              login: string;
            };
            milestone: {
              title: string;
              description: string;
              dueOn: string;
            };
            assignees: {
              nodes: {
                login: string;
                avatarUrl: string;
              }[];
            };
            labels: {
              nodes: {
                name: string;
                color: string;
              }[];
            };
          }[];
        };
      };
    }>(
      `
      query {
        repository(owner: "PianoRhythm", name: "pianorhythm") {
          issues(
            ${input.input.direction == "asc" ? "first" : "last"}: ${input.input.per_page || 20},
            ${input.input.sort == "created" ? "orderBy: {field: CREATED_AT, direction: DESC}" : ""}
            labels: [${labels}],
            states: ${input.input.state == "open" ? "OPEN" : "CLOSED"}
          ) {
            totalCount,
            pageInfo {
              endCursor
              hasNextPage
              hasPreviousPage
            }
            nodes {
              id
              number
              title
              body
              state
              createdAt
              updatedAt
              closedAt
              author {
                login
              }
              milestone {
                title
                description
                dueOn
              }
              assignees(first: 10) {
                nodes {
                  login
                  avatarUrl
                }
              }
              labels(first: 10) {
                nodes {
                  name
                  color
                }
              }
            }
          }
        }
      }
      `
    );

    let promises: Promise<any>[] = [];

    for (let issue of graphql.repository.issues.nodes) {
      let assignees = issue.assignees.nodes.map(x => {
        return {
          login: x.login,
          avatar_url: x.avatarUrl,
        };
      });

      let labels = issue.labels.nodes.map(x => {
        return {
          name: x.name,
          color: x.color,
        };
      });

      //@ts-ignore
      issue.assignees = assignees;
      //@ts-ignore
      issue.labels = labels;

      if (input.input.in_progress === true) {
        const branchName = `${issue.number}-${issue.title.replace(/[^a-zA-Z0-9]/g, "-")}`.toLowerCase();
        try {
          promises.push(
            octokit.repos.getBranch({
              owner: "PianoRhythm",
              repo: "pianorhythm",
              branch: branchName
            })
          );
        } catch (error) { }
      }
    }

    await Promise.allSettled(promises).then((results) => {
      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          //@ts-ignore
          graphql.repository.issues.nodes[index].branch = result.value.data;
        }
      });
    });

    return {
      data: graphql.repository.issues.nodes,
      total_count: graphql.repository.issues.totalCount
    };
  } catch (error) {
    console.error(error);

    //@ts-ignore
    return { error: error?.message ?? "Unknown error" };
  }
}