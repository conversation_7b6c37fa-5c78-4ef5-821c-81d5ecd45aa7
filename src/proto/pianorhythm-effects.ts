// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: pianorhythm-effects.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";
import {
  ChatMessageDto,
  ChatMessageDtoList,
  ClientSideUserDtoList,
  CommandResponse_ClientValidationErrorList,
  CommandResponse_JoinRoomFailResponse,
  CommandResponse_RoomsList,
  JoinedRoomData,
  MidiMessageOutputDto,
  MidiMessageOutputDto_MidiMessageBuffer,
  RoomChatHistory,
  SocketIdList,
  UserDtoList,
  WelcomeDto,
} from "./client-message";
import {
  ActiveChannelsMode,
  activeChannelsModeFromJSON,
  activeChannelsModeToJSON,
  AudioChannel,
  InstrumentsList,
} from "./midi-renditions";
import {
  AppMidiSequencerEvent,
  AppMidiTrack,
  AppNotificationConfig,
  AppPageloaderDetail,
  AppPianoKey,
  AppPianoPedal,
  AppRenderableEntity,
  AppRoomStageLoaded,
  AppSettings,
} from "./pianorhythm-app-renditions";
import {
  BasicRoomDto,
  RoomFullDetails,
  RoomSettings,
  RoomStageDetails,
  RoomStages,
  roomStagesFromJSON,
  roomStagesToJSON,
  RoomType,
  roomTypeFromJSON,
  roomTypeToJSON,
} from "./room-renditions";
import { ClientSideUserDto, UserClientDto, UserDto } from "./user-renditions";

export const protobufPackage = "PianoRhythm.AppStateEffects";

export interface AppStateEffects {
  action: AppStateEffects_Action;
  sourceSocketID?: string | undefined;
  processedMidiMessageOutput?: AppStateEffects_ProcessedMidiMessageOutput | undefined;
  clientSideUserDto?: ClientSideUserDto | undefined;
  socketId?: string | undefined;
  userDtoList?: UserDtoList | undefined;
  clientSideUserDtoList?: ClientSideUserDtoList | undefined;
  joinedRoomData?: JoinedRoomData | undefined;
  roomChatHistory?: RoomChatHistory | undefined;
  chatMessageDto?: ChatMessageDto | undefined;
  midiMessageOutputDto?: MidiMessageOutputDto | undefined;
  socketIdList?: SocketIdList | undefined;
  message?: string | undefined;
  joinRoomFailResponse?: CommandResponse_JoinRoomFailResponse | undefined;
  roomsList?: CommandResponse_RoomsList | undefined;
  basicRoomDto?: BasicRoomDto | undefined;
  roomId?: string | undefined;
  chatMessageDtoList?: ChatMessageDtoList | undefined;
  messageId?: string | undefined;
  welcomeDto?: WelcomeDto | undefined;
  clientValidationErrorList?: CommandResponse_ClientValidationErrorList | undefined;
  roomSettings?: RoomSettings | undefined;
  roomFullDetails?: RoomFullDetails | undefined;
  userDto?: UserDto | undefined;
  audioChannel?: AudioChannel | undefined;
  slotMode?: ActiveChannelsMode | undefined;
  int32Value?: number | undefined;
  uint32Value?: number | undefined;
  boolValue?: boolean | undefined;
  userClientDto?: UserClientDto | undefined;
  instrumentsList?: InstrumentsList | undefined;
  loadRoomStageDetails?: AppStateEffects_LoadRoomStageDetails | undefined;
  bytesPayload?: AppStateEffects_BytesPayload | undefined;
  stringValue?:
    | string
    | undefined;
  /** App Related */
  appPianoKey?: AppPianoKey | undefined;
  appPianoPedal?: AppPianoPedal | undefined;
  appRenderableEntity?: AppRenderableEntity | undefined;
  appPageLoaderDetail?: AppPageloaderDetail | undefined;
  appNotificationConfig?: AppNotificationConfig | undefined;
  appSettings?: AppSettings | undefined;
  midiSequencerEvent?: AppMidiSequencerEvent | undefined;
  appMidiTrack?: AppMidiTrack | undefined;
  appRoomStageLoaded?: AppRoomStageLoaded | undefined;
}

export enum AppStateEffects_Action {
  Unknown = 0,
  UpdateUser = 1,
  AddUser = 2,
  RemoveUser = 3,
  UsersSet = 4,
  JoinedRoomSuccess = 5,
  UsersTypingSet = 6,
  JoinRoomFailResponse = 7,
  Toast = 8,
  SetRooms = 9,
  AddRoom = 11,
  UpdateRoom = 12,
  DeleteRoom = 13,
  SetRoomChatHistory = 14,
  AddChatMessage = 15,
  EditChatMessage = 16,
  DeleteChatMessage = 17,
  OnWelcome = 18,
  SetChatMessages = 19,
  CreateRoomValidationErrors = 20,
  UpdateRoomValidationErrors = 21,
  SetRoomSettings = 22,
  SetRoomFullDetails = 23,
  SetRoomOwner = 24,
  MidiMessageOutput = 25,
  MuteEveryoneElse = 26,
  SynthSoundfontFailedToLoad = 27,
  SynthChannelUpdated = 28,
  SetSlotMode = 29,
  SetPrimaryChannel = 30,
  ListenToProgramChanges = 31,
  ClientIsMuted = 32,
  DrumChannelIsMuted = 33,
  MousePositionSetsVelocity = 34,
  MaxMultiModeChannels = 35,
  EqualizerEnabled = 36,
  ReverbEnabled = 37,
  ClientUpdated = 38,
  SoundfontPresetsLoaded = 39,
  SetPageLoaderDetails = 40,
  LoadRoomStage = 41,
  AppSettingsUpdated = 42,
  WebsocketDisconnected = 43,
  RoomStageLoaded = 44,
  EmittingNotesToServer = 45,
  ConnectionState = 46,
  UserChatMuted = 47,
  UserNotesMuted = 48,
  /** RendererPianoKeyUpdate - Renderering */
  RendererPianoKeyUpdate = 1002,
  RendererPianoPedalUpdate = 1003,
  RendererEntityUpdate = 1004,
  RendererMeshLoaded = 1005,
  RendererCameraLock = 1006,
  RendererCameraReset = 1007,
  RendererLoaded = 1008,
  /** MidiSequencerEvent - Midi Sequencer */
  MidiSequencerEvent = 2000,
  MidiSequencerDownloadMidi = 2001,
  MidiSequencerFailedToLoadMidi = 2002,
  /** AudioApplyVelocityCurve - Audio */
  AudioApplyVelocityCurve = 3000,
  /** AppMidiLooperTrackUpdated - Midi Looper */
  AppMidiLooperTrackUpdated = 4000,
  UNRECOGNIZED = -1,
}

export function appStateEffects_ActionFromJSON(object: any): AppStateEffects_Action {
  switch (object) {
    case 0:
    case "Unknown":
      return AppStateEffects_Action.Unknown;
    case 1:
    case "UpdateUser":
      return AppStateEffects_Action.UpdateUser;
    case 2:
    case "AddUser":
      return AppStateEffects_Action.AddUser;
    case 3:
    case "RemoveUser":
      return AppStateEffects_Action.RemoveUser;
    case 4:
    case "UsersSet":
      return AppStateEffects_Action.UsersSet;
    case 5:
    case "JoinedRoomSuccess":
      return AppStateEffects_Action.JoinedRoomSuccess;
    case 6:
    case "UsersTypingSet":
      return AppStateEffects_Action.UsersTypingSet;
    case 7:
    case "JoinRoomFailResponse":
      return AppStateEffects_Action.JoinRoomFailResponse;
    case 8:
    case "Toast":
      return AppStateEffects_Action.Toast;
    case 9:
    case "SetRooms":
      return AppStateEffects_Action.SetRooms;
    case 11:
    case "AddRoom":
      return AppStateEffects_Action.AddRoom;
    case 12:
    case "UpdateRoom":
      return AppStateEffects_Action.UpdateRoom;
    case 13:
    case "DeleteRoom":
      return AppStateEffects_Action.DeleteRoom;
    case 14:
    case "SetRoomChatHistory":
      return AppStateEffects_Action.SetRoomChatHistory;
    case 15:
    case "AddChatMessage":
      return AppStateEffects_Action.AddChatMessage;
    case 16:
    case "EditChatMessage":
      return AppStateEffects_Action.EditChatMessage;
    case 17:
    case "DeleteChatMessage":
      return AppStateEffects_Action.DeleteChatMessage;
    case 18:
    case "OnWelcome":
      return AppStateEffects_Action.OnWelcome;
    case 19:
    case "SetChatMessages":
      return AppStateEffects_Action.SetChatMessages;
    case 20:
    case "CreateRoomValidationErrors":
      return AppStateEffects_Action.CreateRoomValidationErrors;
    case 21:
    case "UpdateRoomValidationErrors":
      return AppStateEffects_Action.UpdateRoomValidationErrors;
    case 22:
    case "SetRoomSettings":
      return AppStateEffects_Action.SetRoomSettings;
    case 23:
    case "SetRoomFullDetails":
      return AppStateEffects_Action.SetRoomFullDetails;
    case 24:
    case "SetRoomOwner":
      return AppStateEffects_Action.SetRoomOwner;
    case 25:
    case "MidiMessageOutput":
      return AppStateEffects_Action.MidiMessageOutput;
    case 26:
    case "MuteEveryoneElse":
      return AppStateEffects_Action.MuteEveryoneElse;
    case 27:
    case "SynthSoundfontFailedToLoad":
      return AppStateEffects_Action.SynthSoundfontFailedToLoad;
    case 28:
    case "SynthChannelUpdated":
      return AppStateEffects_Action.SynthChannelUpdated;
    case 29:
    case "SetSlotMode":
      return AppStateEffects_Action.SetSlotMode;
    case 30:
    case "SetPrimaryChannel":
      return AppStateEffects_Action.SetPrimaryChannel;
    case 31:
    case "ListenToProgramChanges":
      return AppStateEffects_Action.ListenToProgramChanges;
    case 32:
    case "ClientIsMuted":
      return AppStateEffects_Action.ClientIsMuted;
    case 33:
    case "DrumChannelIsMuted":
      return AppStateEffects_Action.DrumChannelIsMuted;
    case 34:
    case "MousePositionSetsVelocity":
      return AppStateEffects_Action.MousePositionSetsVelocity;
    case 35:
    case "MaxMultiModeChannels":
      return AppStateEffects_Action.MaxMultiModeChannels;
    case 36:
    case "EqualizerEnabled":
      return AppStateEffects_Action.EqualizerEnabled;
    case 37:
    case "ReverbEnabled":
      return AppStateEffects_Action.ReverbEnabled;
    case 38:
    case "ClientUpdated":
      return AppStateEffects_Action.ClientUpdated;
    case 39:
    case "SoundfontPresetsLoaded":
      return AppStateEffects_Action.SoundfontPresetsLoaded;
    case 40:
    case "SetPageLoaderDetails":
      return AppStateEffects_Action.SetPageLoaderDetails;
    case 41:
    case "LoadRoomStage":
      return AppStateEffects_Action.LoadRoomStage;
    case 42:
    case "AppSettingsUpdated":
      return AppStateEffects_Action.AppSettingsUpdated;
    case 43:
    case "WebsocketDisconnected":
      return AppStateEffects_Action.WebsocketDisconnected;
    case 44:
    case "RoomStageLoaded":
      return AppStateEffects_Action.RoomStageLoaded;
    case 45:
    case "EmittingNotesToServer":
      return AppStateEffects_Action.EmittingNotesToServer;
    case 46:
    case "ConnectionState":
      return AppStateEffects_Action.ConnectionState;
    case 47:
    case "UserChatMuted":
      return AppStateEffects_Action.UserChatMuted;
    case 48:
    case "UserNotesMuted":
      return AppStateEffects_Action.UserNotesMuted;
    case 1002:
    case "RendererPianoKeyUpdate":
      return AppStateEffects_Action.RendererPianoKeyUpdate;
    case 1003:
    case "RendererPianoPedalUpdate":
      return AppStateEffects_Action.RendererPianoPedalUpdate;
    case 1004:
    case "RendererEntityUpdate":
      return AppStateEffects_Action.RendererEntityUpdate;
    case 1005:
    case "RendererMeshLoaded":
      return AppStateEffects_Action.RendererMeshLoaded;
    case 1006:
    case "RendererCameraLock":
      return AppStateEffects_Action.RendererCameraLock;
    case 1007:
    case "RendererCameraReset":
      return AppStateEffects_Action.RendererCameraReset;
    case 1008:
    case "RendererLoaded":
      return AppStateEffects_Action.RendererLoaded;
    case 2000:
    case "MidiSequencerEvent":
      return AppStateEffects_Action.MidiSequencerEvent;
    case 2001:
    case "MidiSequencerDownloadMidi":
      return AppStateEffects_Action.MidiSequencerDownloadMidi;
    case 2002:
    case "MidiSequencerFailedToLoadMidi":
      return AppStateEffects_Action.MidiSequencerFailedToLoadMidi;
    case 3000:
    case "AudioApplyVelocityCurve":
      return AppStateEffects_Action.AudioApplyVelocityCurve;
    case 4000:
    case "AppMidiLooperTrackUpdated":
      return AppStateEffects_Action.AppMidiLooperTrackUpdated;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppStateEffects_Action.UNRECOGNIZED;
  }
}

export function appStateEffects_ActionToJSON(object: AppStateEffects_Action): string {
  switch (object) {
    case AppStateEffects_Action.Unknown:
      return "Unknown";
    case AppStateEffects_Action.UpdateUser:
      return "UpdateUser";
    case AppStateEffects_Action.AddUser:
      return "AddUser";
    case AppStateEffects_Action.RemoveUser:
      return "RemoveUser";
    case AppStateEffects_Action.UsersSet:
      return "UsersSet";
    case AppStateEffects_Action.JoinedRoomSuccess:
      return "JoinedRoomSuccess";
    case AppStateEffects_Action.UsersTypingSet:
      return "UsersTypingSet";
    case AppStateEffects_Action.JoinRoomFailResponse:
      return "JoinRoomFailResponse";
    case AppStateEffects_Action.Toast:
      return "Toast";
    case AppStateEffects_Action.SetRooms:
      return "SetRooms";
    case AppStateEffects_Action.AddRoom:
      return "AddRoom";
    case AppStateEffects_Action.UpdateRoom:
      return "UpdateRoom";
    case AppStateEffects_Action.DeleteRoom:
      return "DeleteRoom";
    case AppStateEffects_Action.SetRoomChatHistory:
      return "SetRoomChatHistory";
    case AppStateEffects_Action.AddChatMessage:
      return "AddChatMessage";
    case AppStateEffects_Action.EditChatMessage:
      return "EditChatMessage";
    case AppStateEffects_Action.DeleteChatMessage:
      return "DeleteChatMessage";
    case AppStateEffects_Action.OnWelcome:
      return "OnWelcome";
    case AppStateEffects_Action.SetChatMessages:
      return "SetChatMessages";
    case AppStateEffects_Action.CreateRoomValidationErrors:
      return "CreateRoomValidationErrors";
    case AppStateEffects_Action.UpdateRoomValidationErrors:
      return "UpdateRoomValidationErrors";
    case AppStateEffects_Action.SetRoomSettings:
      return "SetRoomSettings";
    case AppStateEffects_Action.SetRoomFullDetails:
      return "SetRoomFullDetails";
    case AppStateEffects_Action.SetRoomOwner:
      return "SetRoomOwner";
    case AppStateEffects_Action.MidiMessageOutput:
      return "MidiMessageOutput";
    case AppStateEffects_Action.MuteEveryoneElse:
      return "MuteEveryoneElse";
    case AppStateEffects_Action.SynthSoundfontFailedToLoad:
      return "SynthSoundfontFailedToLoad";
    case AppStateEffects_Action.SynthChannelUpdated:
      return "SynthChannelUpdated";
    case AppStateEffects_Action.SetSlotMode:
      return "SetSlotMode";
    case AppStateEffects_Action.SetPrimaryChannel:
      return "SetPrimaryChannel";
    case AppStateEffects_Action.ListenToProgramChanges:
      return "ListenToProgramChanges";
    case AppStateEffects_Action.ClientIsMuted:
      return "ClientIsMuted";
    case AppStateEffects_Action.DrumChannelIsMuted:
      return "DrumChannelIsMuted";
    case AppStateEffects_Action.MousePositionSetsVelocity:
      return "MousePositionSetsVelocity";
    case AppStateEffects_Action.MaxMultiModeChannels:
      return "MaxMultiModeChannels";
    case AppStateEffects_Action.EqualizerEnabled:
      return "EqualizerEnabled";
    case AppStateEffects_Action.ReverbEnabled:
      return "ReverbEnabled";
    case AppStateEffects_Action.ClientUpdated:
      return "ClientUpdated";
    case AppStateEffects_Action.SoundfontPresetsLoaded:
      return "SoundfontPresetsLoaded";
    case AppStateEffects_Action.SetPageLoaderDetails:
      return "SetPageLoaderDetails";
    case AppStateEffects_Action.LoadRoomStage:
      return "LoadRoomStage";
    case AppStateEffects_Action.AppSettingsUpdated:
      return "AppSettingsUpdated";
    case AppStateEffects_Action.WebsocketDisconnected:
      return "WebsocketDisconnected";
    case AppStateEffects_Action.RoomStageLoaded:
      return "RoomStageLoaded";
    case AppStateEffects_Action.EmittingNotesToServer:
      return "EmittingNotesToServer";
    case AppStateEffects_Action.ConnectionState:
      return "ConnectionState";
    case AppStateEffects_Action.UserChatMuted:
      return "UserChatMuted";
    case AppStateEffects_Action.UserNotesMuted:
      return "UserNotesMuted";
    case AppStateEffects_Action.RendererPianoKeyUpdate:
      return "RendererPianoKeyUpdate";
    case AppStateEffects_Action.RendererPianoPedalUpdate:
      return "RendererPianoPedalUpdate";
    case AppStateEffects_Action.RendererEntityUpdate:
      return "RendererEntityUpdate";
    case AppStateEffects_Action.RendererMeshLoaded:
      return "RendererMeshLoaded";
    case AppStateEffects_Action.RendererCameraLock:
      return "RendererCameraLock";
    case AppStateEffects_Action.RendererCameraReset:
      return "RendererCameraReset";
    case AppStateEffects_Action.RendererLoaded:
      return "RendererLoaded";
    case AppStateEffects_Action.MidiSequencerEvent:
      return "MidiSequencerEvent";
    case AppStateEffects_Action.MidiSequencerDownloadMidi:
      return "MidiSequencerDownloadMidi";
    case AppStateEffects_Action.MidiSequencerFailedToLoadMidi:
      return "MidiSequencerFailedToLoadMidi";
    case AppStateEffects_Action.AudioApplyVelocityCurve:
      return "AudioApplyVelocityCurve";
    case AppStateEffects_Action.AppMidiLooperTrackUpdated:
      return "AppMidiLooperTrackUpdated";
    case AppStateEffects_Action.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AppStateEffects_ProcessedMidiMessageOutput {
  deviceName: string;
  ms: number;
  delay: number;
  socketId: string;
  data: MidiMessageOutputDto_MidiMessageBuffer | undefined;
}

export interface AppStateEffects_LoadRoomStageDetails {
  roomStage: RoomStages;
  roomType: RoomType;
  roomStageDetails?: RoomStageDetails | undefined;
}

export interface AppStateEffects_BytesPayload {
  payload: number[];
  id?: string | undefined;
}

function createBaseAppStateEffects(): AppStateEffects {
  return {
    action: 0,
    sourceSocketID: undefined,
    processedMidiMessageOutput: undefined,
    clientSideUserDto: undefined,
    socketId: undefined,
    userDtoList: undefined,
    clientSideUserDtoList: undefined,
    joinedRoomData: undefined,
    roomChatHistory: undefined,
    chatMessageDto: undefined,
    midiMessageOutputDto: undefined,
    socketIdList: undefined,
    message: undefined,
    joinRoomFailResponse: undefined,
    roomsList: undefined,
    basicRoomDto: undefined,
    roomId: undefined,
    chatMessageDtoList: undefined,
    messageId: undefined,
    welcomeDto: undefined,
    clientValidationErrorList: undefined,
    roomSettings: undefined,
    roomFullDetails: undefined,
    userDto: undefined,
    audioChannel: undefined,
    slotMode: undefined,
    int32Value: undefined,
    uint32Value: undefined,
    boolValue: undefined,
    userClientDto: undefined,
    instrumentsList: undefined,
    loadRoomStageDetails: undefined,
    bytesPayload: undefined,
    stringValue: undefined,
    appPianoKey: undefined,
    appPianoPedal: undefined,
    appRenderableEntity: undefined,
    appPageLoaderDetail: undefined,
    appNotificationConfig: undefined,
    appSettings: undefined,
    midiSequencerEvent: undefined,
    appMidiTrack: undefined,
    appRoomStageLoaded: undefined,
  };
}

export const AppStateEffects = {
  encode(message: AppStateEffects, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.action !== 0) {
      writer.uint32(8).int32(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      writer.uint32(7994).string(message.sourceSocketID);
    }
    if (message.processedMidiMessageOutput !== undefined) {
      AppStateEffects_ProcessedMidiMessageOutput.encode(message.processedMidiMessageOutput, writer.uint32(18).fork())
        .ldelim();
    }
    if (message.clientSideUserDto !== undefined) {
      ClientSideUserDto.encode(message.clientSideUserDto, writer.uint32(26).fork()).ldelim();
    }
    if (message.socketId !== undefined) {
      writer.uint32(34).string(message.socketId);
    }
    if (message.userDtoList !== undefined) {
      UserDtoList.encode(message.userDtoList, writer.uint32(42).fork()).ldelim();
    }
    if (message.clientSideUserDtoList !== undefined) {
      ClientSideUserDtoList.encode(message.clientSideUserDtoList, writer.uint32(50).fork()).ldelim();
    }
    if (message.joinedRoomData !== undefined) {
      JoinedRoomData.encode(message.joinedRoomData, writer.uint32(58).fork()).ldelim();
    }
    if (message.roomChatHistory !== undefined) {
      RoomChatHistory.encode(message.roomChatHistory, writer.uint32(66).fork()).ldelim();
    }
    if (message.chatMessageDto !== undefined) {
      ChatMessageDto.encode(message.chatMessageDto, writer.uint32(74).fork()).ldelim();
    }
    if (message.midiMessageOutputDto !== undefined) {
      MidiMessageOutputDto.encode(message.midiMessageOutputDto, writer.uint32(82).fork()).ldelim();
    }
    if (message.socketIdList !== undefined) {
      SocketIdList.encode(message.socketIdList, writer.uint32(90).fork()).ldelim();
    }
    if (message.message !== undefined) {
      writer.uint32(98).string(message.message);
    }
    if (message.joinRoomFailResponse !== undefined) {
      CommandResponse_JoinRoomFailResponse.encode(message.joinRoomFailResponse, writer.uint32(106).fork()).ldelim();
    }
    if (message.roomsList !== undefined) {
      CommandResponse_RoomsList.encode(message.roomsList, writer.uint32(114).fork()).ldelim();
    }
    if (message.basicRoomDto !== undefined) {
      BasicRoomDto.encode(message.basicRoomDto, writer.uint32(122).fork()).ldelim();
    }
    if (message.roomId !== undefined) {
      writer.uint32(130).string(message.roomId);
    }
    if (message.chatMessageDtoList !== undefined) {
      ChatMessageDtoList.encode(message.chatMessageDtoList, writer.uint32(138).fork()).ldelim();
    }
    if (message.messageId !== undefined) {
      writer.uint32(146).string(message.messageId);
    }
    if (message.welcomeDto !== undefined) {
      WelcomeDto.encode(message.welcomeDto, writer.uint32(154).fork()).ldelim();
    }
    if (message.clientValidationErrorList !== undefined) {
      CommandResponse_ClientValidationErrorList.encode(message.clientValidationErrorList, writer.uint32(162).fork())
        .ldelim();
    }
    if (message.roomSettings !== undefined) {
      RoomSettings.encode(message.roomSettings, writer.uint32(170).fork()).ldelim();
    }
    if (message.roomFullDetails !== undefined) {
      RoomFullDetails.encode(message.roomFullDetails, writer.uint32(178).fork()).ldelim();
    }
    if (message.userDto !== undefined) {
      UserDto.encode(message.userDto, writer.uint32(186).fork()).ldelim();
    }
    if (message.audioChannel !== undefined) {
      AudioChannel.encode(message.audioChannel, writer.uint32(194).fork()).ldelim();
    }
    if (message.slotMode !== undefined) {
      writer.uint32(200).int32(message.slotMode);
    }
    if (message.int32Value !== undefined) {
      writer.uint32(208).int32(message.int32Value);
    }
    if (message.uint32Value !== undefined) {
      writer.uint32(216).uint32(message.uint32Value);
    }
    if (message.boolValue !== undefined) {
      writer.uint32(224).bool(message.boolValue);
    }
    if (message.userClientDto !== undefined) {
      UserClientDto.encode(message.userClientDto, writer.uint32(234).fork()).ldelim();
    }
    if (message.instrumentsList !== undefined) {
      InstrumentsList.encode(message.instrumentsList, writer.uint32(242).fork()).ldelim();
    }
    if (message.loadRoomStageDetails !== undefined) {
      AppStateEffects_LoadRoomStageDetails.encode(message.loadRoomStageDetails, writer.uint32(250).fork()).ldelim();
    }
    if (message.bytesPayload !== undefined) {
      AppStateEffects_BytesPayload.encode(message.bytesPayload, writer.uint32(258).fork()).ldelim();
    }
    if (message.stringValue !== undefined) {
      writer.uint32(266).string(message.stringValue);
    }
    if (message.appPianoKey !== undefined) {
      AppPianoKey.encode(message.appPianoKey, writer.uint32(40002).fork()).ldelim();
    }
    if (message.appPianoPedal !== undefined) {
      AppPianoPedal.encode(message.appPianoPedal, writer.uint32(40010).fork()).ldelim();
    }
    if (message.appRenderableEntity !== undefined) {
      AppRenderableEntity.encode(message.appRenderableEntity, writer.uint32(40018).fork()).ldelim();
    }
    if (message.appPageLoaderDetail !== undefined) {
      AppPageloaderDetail.encode(message.appPageLoaderDetail, writer.uint32(40026).fork()).ldelim();
    }
    if (message.appNotificationConfig !== undefined) {
      AppNotificationConfig.encode(message.appNotificationConfig, writer.uint32(40034).fork()).ldelim();
    }
    if (message.appSettings !== undefined) {
      AppSettings.encode(message.appSettings, writer.uint32(40042).fork()).ldelim();
    }
    if (message.midiSequencerEvent !== undefined) {
      AppMidiSequencerEvent.encode(message.midiSequencerEvent, writer.uint32(40050).fork()).ldelim();
    }
    if (message.appMidiTrack !== undefined) {
      AppMidiTrack.encode(message.appMidiTrack, writer.uint32(40058).fork()).ldelim();
    }
    if (message.appRoomStageLoaded !== undefined) {
      AppRoomStageLoaded.encode(message.appRoomStageLoaded, writer.uint32(40066).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateEffects {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateEffects();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.action = reader.int32() as any;
          continue;
        case 999:
          if (tag !== 7994) {
            break;
          }

          message.sourceSocketID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.processedMidiMessageOutput = AppStateEffects_ProcessedMidiMessageOutput.decode(
            reader,
            reader.uint32(),
          );
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.clientSideUserDto = ClientSideUserDto.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.userDtoList = UserDtoList.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.clientSideUserDtoList = ClientSideUserDtoList.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.joinedRoomData = JoinedRoomData.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.roomChatHistory = RoomChatHistory.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.chatMessageDto = ChatMessageDto.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.midiMessageOutputDto = MidiMessageOutputDto.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.socketIdList = SocketIdList.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.message = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.roomsList = CommandResponse_RoomsList.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.basicRoomDto = BasicRoomDto.decode(reader, reader.uint32());
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.roomId = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.chatMessageDtoList = ChatMessageDtoList.decode(reader, reader.uint32());
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.messageId = reader.string();
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.welcomeDto = WelcomeDto.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.clientValidationErrorList = CommandResponse_ClientValidationErrorList.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.roomSettings = RoomSettings.decode(reader, reader.uint32());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.roomFullDetails = RoomFullDetails.decode(reader, reader.uint32());
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }

          message.userDto = UserDto.decode(reader, reader.uint32());
          continue;
        case 24:
          if (tag !== 194) {
            break;
          }

          message.audioChannel = AudioChannel.decode(reader, reader.uint32());
          continue;
        case 25:
          if (tag !== 200) {
            break;
          }

          message.slotMode = reader.int32() as any;
          continue;
        case 26:
          if (tag !== 208) {
            break;
          }

          message.int32Value = reader.int32();
          continue;
        case 27:
          if (tag !== 216) {
            break;
          }

          message.uint32Value = reader.uint32();
          continue;
        case 28:
          if (tag !== 224) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.userClientDto = UserClientDto.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.instrumentsList = InstrumentsList.decode(reader, reader.uint32());
          continue;
        case 31:
          if (tag !== 250) {
            break;
          }

          message.loadRoomStageDetails = AppStateEffects_LoadRoomStageDetails.decode(reader, reader.uint32());
          continue;
        case 32:
          if (tag !== 258) {
            break;
          }

          message.bytesPayload = AppStateEffects_BytesPayload.decode(reader, reader.uint32());
          continue;
        case 33:
          if (tag !== 266) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        case 5000:
          if (tag !== 40002) {
            break;
          }

          message.appPianoKey = AppPianoKey.decode(reader, reader.uint32());
          continue;
        case 5001:
          if (tag !== 40010) {
            break;
          }

          message.appPianoPedal = AppPianoPedal.decode(reader, reader.uint32());
          continue;
        case 5002:
          if (tag !== 40018) {
            break;
          }

          message.appRenderableEntity = AppRenderableEntity.decode(reader, reader.uint32());
          continue;
        case 5003:
          if (tag !== 40026) {
            break;
          }

          message.appPageLoaderDetail = AppPageloaderDetail.decode(reader, reader.uint32());
          continue;
        case 5004:
          if (tag !== 40034) {
            break;
          }

          message.appNotificationConfig = AppNotificationConfig.decode(reader, reader.uint32());
          continue;
        case 5005:
          if (tag !== 40042) {
            break;
          }

          message.appSettings = AppSettings.decode(reader, reader.uint32());
          continue;
        case 5006:
          if (tag !== 40050) {
            break;
          }

          message.midiSequencerEvent = AppMidiSequencerEvent.decode(reader, reader.uint32());
          continue;
        case 5007:
          if (tag !== 40058) {
            break;
          }

          message.appMidiTrack = AppMidiTrack.decode(reader, reader.uint32());
          continue;
        case 5008:
          if (tag !== 40066) {
            break;
          }

          message.appRoomStageLoaded = AppRoomStageLoaded.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateEffects {
    return {
      action: isSet(object.action) ? appStateEffects_ActionFromJSON(object.action) : 0,
      sourceSocketID: isSet(object.sourceSocketID) ? globalThis.String(object.sourceSocketID) : undefined,
      processedMidiMessageOutput: isSet(object.processedMidiMessageOutput)
        ? AppStateEffects_ProcessedMidiMessageOutput.fromJSON(object.processedMidiMessageOutput)
        : undefined,
      clientSideUserDto: isSet(object.clientSideUserDto)
        ? ClientSideUserDto.fromJSON(object.clientSideUserDto)
        : undefined,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : undefined,
      userDtoList: isSet(object.userDtoList) ? UserDtoList.fromJSON(object.userDtoList) : undefined,
      clientSideUserDtoList: isSet(object.clientSideUserDtoList)
        ? ClientSideUserDtoList.fromJSON(object.clientSideUserDtoList)
        : undefined,
      joinedRoomData: isSet(object.joinedRoomData) ? JoinedRoomData.fromJSON(object.joinedRoomData) : undefined,
      roomChatHistory: isSet(object.roomChatHistory) ? RoomChatHistory.fromJSON(object.roomChatHistory) : undefined,
      chatMessageDto: isSet(object.chatMessageDto) ? ChatMessageDto.fromJSON(object.chatMessageDto) : undefined,
      midiMessageOutputDto: isSet(object.midiMessageOutputDto)
        ? MidiMessageOutputDto.fromJSON(object.midiMessageOutputDto)
        : undefined,
      socketIdList: isSet(object.socketIdList) ? SocketIdList.fromJSON(object.socketIdList) : undefined,
      message: isSet(object.message) ? globalThis.String(object.message) : undefined,
      joinRoomFailResponse: isSet(object.joinRoomFailResponse)
        ? CommandResponse_JoinRoomFailResponse.fromJSON(object.joinRoomFailResponse)
        : undefined,
      roomsList: isSet(object.roomsList) ? CommandResponse_RoomsList.fromJSON(object.roomsList) : undefined,
      basicRoomDto: isSet(object.basicRoomDto) ? BasicRoomDto.fromJSON(object.basicRoomDto) : undefined,
      roomId: isSet(object.roomId) ? globalThis.String(object.roomId) : undefined,
      chatMessageDtoList: isSet(object.chatMessageDtoList)
        ? ChatMessageDtoList.fromJSON(object.chatMessageDtoList)
        : undefined,
      messageId: isSet(object.messageId) ? globalThis.String(object.messageId) : undefined,
      welcomeDto: isSet(object.welcomeDto) ? WelcomeDto.fromJSON(object.welcomeDto) : undefined,
      clientValidationErrorList: isSet(object.clientValidationErrorList)
        ? CommandResponse_ClientValidationErrorList.fromJSON(object.clientValidationErrorList)
        : undefined,
      roomSettings: isSet(object.roomSettings) ? RoomSettings.fromJSON(object.roomSettings) : undefined,
      roomFullDetails: isSet(object.roomFullDetails) ? RoomFullDetails.fromJSON(object.roomFullDetails) : undefined,
      userDto: isSet(object.userDto) ? UserDto.fromJSON(object.userDto) : undefined,
      audioChannel: isSet(object.audioChannel) ? AudioChannel.fromJSON(object.audioChannel) : undefined,
      slotMode: isSet(object.slotMode) ? activeChannelsModeFromJSON(object.slotMode) : undefined,
      int32Value: isSet(object.int32Value) ? globalThis.Number(object.int32Value) : undefined,
      uint32Value: isSet(object.uint32Value) ? globalThis.Number(object.uint32Value) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      userClientDto: isSet(object.userClientDto) ? UserClientDto.fromJSON(object.userClientDto) : undefined,
      instrumentsList: isSet(object.instrumentsList) ? InstrumentsList.fromJSON(object.instrumentsList) : undefined,
      loadRoomStageDetails: isSet(object.loadRoomStageDetails)
        ? AppStateEffects_LoadRoomStageDetails.fromJSON(object.loadRoomStageDetails)
        : undefined,
      bytesPayload: isSet(object.bytesPayload) ? AppStateEffects_BytesPayload.fromJSON(object.bytesPayload) : undefined,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      appPianoKey: isSet(object.appPianoKey) ? AppPianoKey.fromJSON(object.appPianoKey) : undefined,
      appPianoPedal: isSet(object.appPianoPedal) ? AppPianoPedal.fromJSON(object.appPianoPedal) : undefined,
      appRenderableEntity: isSet(object.appRenderableEntity)
        ? AppRenderableEntity.fromJSON(object.appRenderableEntity)
        : undefined,
      appPageLoaderDetail: isSet(object.appPageLoaderDetail)
        ? AppPageloaderDetail.fromJSON(object.appPageLoaderDetail)
        : undefined,
      appNotificationConfig: isSet(object.appNotificationConfig)
        ? AppNotificationConfig.fromJSON(object.appNotificationConfig)
        : undefined,
      appSettings: isSet(object.appSettings) ? AppSettings.fromJSON(object.appSettings) : undefined,
      midiSequencerEvent: isSet(object.midiSequencerEvent)
        ? AppMidiSequencerEvent.fromJSON(object.midiSequencerEvent)
        : undefined,
      appMidiTrack: isSet(object.appMidiTrack) ? AppMidiTrack.fromJSON(object.appMidiTrack) : undefined,
      appRoomStageLoaded: isSet(object.appRoomStageLoaded)
        ? AppRoomStageLoaded.fromJSON(object.appRoomStageLoaded)
        : undefined,
    };
  },

  toJSON(message: AppStateEffects): unknown {
    const obj: any = {};
    if (message.action !== 0) {
      obj.action = appStateEffects_ActionToJSON(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      obj.sourceSocketID = message.sourceSocketID;
    }
    if (message.processedMidiMessageOutput !== undefined) {
      obj.processedMidiMessageOutput = AppStateEffects_ProcessedMidiMessageOutput.toJSON(
        message.processedMidiMessageOutput,
      );
    }
    if (message.clientSideUserDto !== undefined) {
      obj.clientSideUserDto = ClientSideUserDto.toJSON(message.clientSideUserDto);
    }
    if (message.socketId !== undefined) {
      obj.socketId = message.socketId;
    }
    if (message.userDtoList !== undefined) {
      obj.userDtoList = UserDtoList.toJSON(message.userDtoList);
    }
    if (message.clientSideUserDtoList !== undefined) {
      obj.clientSideUserDtoList = ClientSideUserDtoList.toJSON(message.clientSideUserDtoList);
    }
    if (message.joinedRoomData !== undefined) {
      obj.joinedRoomData = JoinedRoomData.toJSON(message.joinedRoomData);
    }
    if (message.roomChatHistory !== undefined) {
      obj.roomChatHistory = RoomChatHistory.toJSON(message.roomChatHistory);
    }
    if (message.chatMessageDto !== undefined) {
      obj.chatMessageDto = ChatMessageDto.toJSON(message.chatMessageDto);
    }
    if (message.midiMessageOutputDto !== undefined) {
      obj.midiMessageOutputDto = MidiMessageOutputDto.toJSON(message.midiMessageOutputDto);
    }
    if (message.socketIdList !== undefined) {
      obj.socketIdList = SocketIdList.toJSON(message.socketIdList);
    }
    if (message.message !== undefined) {
      obj.message = message.message;
    }
    if (message.joinRoomFailResponse !== undefined) {
      obj.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.toJSON(message.joinRoomFailResponse);
    }
    if (message.roomsList !== undefined) {
      obj.roomsList = CommandResponse_RoomsList.toJSON(message.roomsList);
    }
    if (message.basicRoomDto !== undefined) {
      obj.basicRoomDto = BasicRoomDto.toJSON(message.basicRoomDto);
    }
    if (message.roomId !== undefined) {
      obj.roomId = message.roomId;
    }
    if (message.chatMessageDtoList !== undefined) {
      obj.chatMessageDtoList = ChatMessageDtoList.toJSON(message.chatMessageDtoList);
    }
    if (message.messageId !== undefined) {
      obj.messageId = message.messageId;
    }
    if (message.welcomeDto !== undefined) {
      obj.welcomeDto = WelcomeDto.toJSON(message.welcomeDto);
    }
    if (message.clientValidationErrorList !== undefined) {
      obj.clientValidationErrorList = CommandResponse_ClientValidationErrorList.toJSON(
        message.clientValidationErrorList,
      );
    }
    if (message.roomSettings !== undefined) {
      obj.roomSettings = RoomSettings.toJSON(message.roomSettings);
    }
    if (message.roomFullDetails !== undefined) {
      obj.roomFullDetails = RoomFullDetails.toJSON(message.roomFullDetails);
    }
    if (message.userDto !== undefined) {
      obj.userDto = UserDto.toJSON(message.userDto);
    }
    if (message.audioChannel !== undefined) {
      obj.audioChannel = AudioChannel.toJSON(message.audioChannel);
    }
    if (message.slotMode !== undefined) {
      obj.slotMode = activeChannelsModeToJSON(message.slotMode);
    }
    if (message.int32Value !== undefined) {
      obj.int32Value = Math.round(message.int32Value);
    }
    if (message.uint32Value !== undefined) {
      obj.uint32Value = Math.round(message.uint32Value);
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.userClientDto !== undefined) {
      obj.userClientDto = UserClientDto.toJSON(message.userClientDto);
    }
    if (message.instrumentsList !== undefined) {
      obj.instrumentsList = InstrumentsList.toJSON(message.instrumentsList);
    }
    if (message.loadRoomStageDetails !== undefined) {
      obj.loadRoomStageDetails = AppStateEffects_LoadRoomStageDetails.toJSON(message.loadRoomStageDetails);
    }
    if (message.bytesPayload !== undefined) {
      obj.bytesPayload = AppStateEffects_BytesPayload.toJSON(message.bytesPayload);
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.appPianoKey !== undefined) {
      obj.appPianoKey = AppPianoKey.toJSON(message.appPianoKey);
    }
    if (message.appPianoPedal !== undefined) {
      obj.appPianoPedal = AppPianoPedal.toJSON(message.appPianoPedal);
    }
    if (message.appRenderableEntity !== undefined) {
      obj.appRenderableEntity = AppRenderableEntity.toJSON(message.appRenderableEntity);
    }
    if (message.appPageLoaderDetail !== undefined) {
      obj.appPageLoaderDetail = AppPageloaderDetail.toJSON(message.appPageLoaderDetail);
    }
    if (message.appNotificationConfig !== undefined) {
      obj.appNotificationConfig = AppNotificationConfig.toJSON(message.appNotificationConfig);
    }
    if (message.appSettings !== undefined) {
      obj.appSettings = AppSettings.toJSON(message.appSettings);
    }
    if (message.midiSequencerEvent !== undefined) {
      obj.midiSequencerEvent = AppMidiSequencerEvent.toJSON(message.midiSequencerEvent);
    }
    if (message.appMidiTrack !== undefined) {
      obj.appMidiTrack = AppMidiTrack.toJSON(message.appMidiTrack);
    }
    if (message.appRoomStageLoaded !== undefined) {
      obj.appRoomStageLoaded = AppRoomStageLoaded.toJSON(message.appRoomStageLoaded);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateEffects>, I>>(base?: I): AppStateEffects {
    return AppStateEffects.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateEffects>, I>>(object: I): AppStateEffects {
    const message = createBaseAppStateEffects();
    message.action = object.action ?? 0;
    message.sourceSocketID = object.sourceSocketID ?? undefined;
    message.processedMidiMessageOutput =
      (object.processedMidiMessageOutput !== undefined && object.processedMidiMessageOutput !== null)
        ? AppStateEffects_ProcessedMidiMessageOutput.fromPartial(object.processedMidiMessageOutput)
        : undefined;
    message.clientSideUserDto = (object.clientSideUserDto !== undefined && object.clientSideUserDto !== null)
      ? ClientSideUserDto.fromPartial(object.clientSideUserDto)
      : undefined;
    message.socketId = object.socketId ?? undefined;
    message.userDtoList = (object.userDtoList !== undefined && object.userDtoList !== null)
      ? UserDtoList.fromPartial(object.userDtoList)
      : undefined;
    message.clientSideUserDtoList =
      (object.clientSideUserDtoList !== undefined && object.clientSideUserDtoList !== null)
        ? ClientSideUserDtoList.fromPartial(object.clientSideUserDtoList)
        : undefined;
    message.joinedRoomData = (object.joinedRoomData !== undefined && object.joinedRoomData !== null)
      ? JoinedRoomData.fromPartial(object.joinedRoomData)
      : undefined;
    message.roomChatHistory = (object.roomChatHistory !== undefined && object.roomChatHistory !== null)
      ? RoomChatHistory.fromPartial(object.roomChatHistory)
      : undefined;
    message.chatMessageDto = (object.chatMessageDto !== undefined && object.chatMessageDto !== null)
      ? ChatMessageDto.fromPartial(object.chatMessageDto)
      : undefined;
    message.midiMessageOutputDto = (object.midiMessageOutputDto !== undefined && object.midiMessageOutputDto !== null)
      ? MidiMessageOutputDto.fromPartial(object.midiMessageOutputDto)
      : undefined;
    message.socketIdList = (object.socketIdList !== undefined && object.socketIdList !== null)
      ? SocketIdList.fromPartial(object.socketIdList)
      : undefined;
    message.message = object.message ?? undefined;
    message.joinRoomFailResponse = (object.joinRoomFailResponse !== undefined && object.joinRoomFailResponse !== null)
      ? CommandResponse_JoinRoomFailResponse.fromPartial(object.joinRoomFailResponse)
      : undefined;
    message.roomsList = (object.roomsList !== undefined && object.roomsList !== null)
      ? CommandResponse_RoomsList.fromPartial(object.roomsList)
      : undefined;
    message.basicRoomDto = (object.basicRoomDto !== undefined && object.basicRoomDto !== null)
      ? BasicRoomDto.fromPartial(object.basicRoomDto)
      : undefined;
    message.roomId = object.roomId ?? undefined;
    message.chatMessageDtoList = (object.chatMessageDtoList !== undefined && object.chatMessageDtoList !== null)
      ? ChatMessageDtoList.fromPartial(object.chatMessageDtoList)
      : undefined;
    message.messageId = object.messageId ?? undefined;
    message.welcomeDto = (object.welcomeDto !== undefined && object.welcomeDto !== null)
      ? WelcomeDto.fromPartial(object.welcomeDto)
      : undefined;
    message.clientValidationErrorList =
      (object.clientValidationErrorList !== undefined && object.clientValidationErrorList !== null)
        ? CommandResponse_ClientValidationErrorList.fromPartial(object.clientValidationErrorList)
        : undefined;
    message.roomSettings = (object.roomSettings !== undefined && object.roomSettings !== null)
      ? RoomSettings.fromPartial(object.roomSettings)
      : undefined;
    message.roomFullDetails = (object.roomFullDetails !== undefined && object.roomFullDetails !== null)
      ? RoomFullDetails.fromPartial(object.roomFullDetails)
      : undefined;
    message.userDto = (object.userDto !== undefined && object.userDto !== null)
      ? UserDto.fromPartial(object.userDto)
      : undefined;
    message.audioChannel = (object.audioChannel !== undefined && object.audioChannel !== null)
      ? AudioChannel.fromPartial(object.audioChannel)
      : undefined;
    message.slotMode = object.slotMode ?? undefined;
    message.int32Value = object.int32Value ?? undefined;
    message.uint32Value = object.uint32Value ?? undefined;
    message.boolValue = object.boolValue ?? undefined;
    message.userClientDto = (object.userClientDto !== undefined && object.userClientDto !== null)
      ? UserClientDto.fromPartial(object.userClientDto)
      : undefined;
    message.instrumentsList = (object.instrumentsList !== undefined && object.instrumentsList !== null)
      ? InstrumentsList.fromPartial(object.instrumentsList)
      : undefined;
    message.loadRoomStageDetails = (object.loadRoomStageDetails !== undefined && object.loadRoomStageDetails !== null)
      ? AppStateEffects_LoadRoomStageDetails.fromPartial(object.loadRoomStageDetails)
      : undefined;
    message.bytesPayload = (object.bytesPayload !== undefined && object.bytesPayload !== null)
      ? AppStateEffects_BytesPayload.fromPartial(object.bytesPayload)
      : undefined;
    message.stringValue = object.stringValue ?? undefined;
    message.appPianoKey = (object.appPianoKey !== undefined && object.appPianoKey !== null)
      ? AppPianoKey.fromPartial(object.appPianoKey)
      : undefined;
    message.appPianoPedal = (object.appPianoPedal !== undefined && object.appPianoPedal !== null)
      ? AppPianoPedal.fromPartial(object.appPianoPedal)
      : undefined;
    message.appRenderableEntity = (object.appRenderableEntity !== undefined && object.appRenderableEntity !== null)
      ? AppRenderableEntity.fromPartial(object.appRenderableEntity)
      : undefined;
    message.appPageLoaderDetail = (object.appPageLoaderDetail !== undefined && object.appPageLoaderDetail !== null)
      ? AppPageloaderDetail.fromPartial(object.appPageLoaderDetail)
      : undefined;
    message.appNotificationConfig =
      (object.appNotificationConfig !== undefined && object.appNotificationConfig !== null)
        ? AppNotificationConfig.fromPartial(object.appNotificationConfig)
        : undefined;
    message.appSettings = (object.appSettings !== undefined && object.appSettings !== null)
      ? AppSettings.fromPartial(object.appSettings)
      : undefined;
    message.midiSequencerEvent = (object.midiSequencerEvent !== undefined && object.midiSequencerEvent !== null)
      ? AppMidiSequencerEvent.fromPartial(object.midiSequencerEvent)
      : undefined;
    message.appMidiTrack = (object.appMidiTrack !== undefined && object.appMidiTrack !== null)
      ? AppMidiTrack.fromPartial(object.appMidiTrack)
      : undefined;
    message.appRoomStageLoaded = (object.appRoomStageLoaded !== undefined && object.appRoomStageLoaded !== null)
      ? AppRoomStageLoaded.fromPartial(object.appRoomStageLoaded)
      : undefined;
    return message;
  },
};

function createBaseAppStateEffects_ProcessedMidiMessageOutput(): AppStateEffects_ProcessedMidiMessageOutput {
  return { deviceName: "", ms: 0, delay: 0, socketId: "", data: undefined };
}

export const AppStateEffects_ProcessedMidiMessageOutput = {
  encode(message: AppStateEffects_ProcessedMidiMessageOutput, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.deviceName !== "") {
      writer.uint32(10).string(message.deviceName);
    }
    if (message.ms !== 0) {
      writer.uint32(21).float(message.ms);
    }
    if (message.delay !== 0) {
      writer.uint32(25).double(message.delay);
    }
    if (message.socketId !== "") {
      writer.uint32(34).string(message.socketId);
    }
    if (message.data !== undefined) {
      MidiMessageOutputDto_MidiMessageBuffer.encode(message.data, writer.uint32(42).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateEffects_ProcessedMidiMessageOutput {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateEffects_ProcessedMidiMessageOutput();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.deviceName = reader.string();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }

          message.ms = reader.float();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.delay = reader.double();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.data = MidiMessageOutputDto_MidiMessageBuffer.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateEffects_ProcessedMidiMessageOutput {
    return {
      deviceName: isSet(object.deviceName) ? globalThis.String(object.deviceName) : "",
      ms: isSet(object.ms) ? globalThis.Number(object.ms) : 0,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : 0,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : "",
      data: isSet(object.data) ? MidiMessageOutputDto_MidiMessageBuffer.fromJSON(object.data) : undefined,
    };
  },

  toJSON(message: AppStateEffects_ProcessedMidiMessageOutput): unknown {
    const obj: any = {};
    if (message.deviceName !== "") {
      obj.deviceName = message.deviceName;
    }
    if (message.ms !== 0) {
      obj.ms = message.ms;
    }
    if (message.delay !== 0) {
      obj.delay = message.delay;
    }
    if (message.socketId !== "") {
      obj.socketId = message.socketId;
    }
    if (message.data !== undefined) {
      obj.data = MidiMessageOutputDto_MidiMessageBuffer.toJSON(message.data);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateEffects_ProcessedMidiMessageOutput>, I>>(
    base?: I,
  ): AppStateEffects_ProcessedMidiMessageOutput {
    return AppStateEffects_ProcessedMidiMessageOutput.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateEffects_ProcessedMidiMessageOutput>, I>>(
    object: I,
  ): AppStateEffects_ProcessedMidiMessageOutput {
    const message = createBaseAppStateEffects_ProcessedMidiMessageOutput();
    message.deviceName = object.deviceName ?? "";
    message.ms = object.ms ?? 0;
    message.delay = object.delay ?? 0;
    message.socketId = object.socketId ?? "";
    message.data = (object.data !== undefined && object.data !== null)
      ? MidiMessageOutputDto_MidiMessageBuffer.fromPartial(object.data)
      : undefined;
    return message;
  },
};

function createBaseAppStateEffects_LoadRoomStageDetails(): AppStateEffects_LoadRoomStageDetails {
  return { roomStage: 0, roomType: 0, roomStageDetails: undefined };
}

export const AppStateEffects_LoadRoomStageDetails = {
  encode(message: AppStateEffects_LoadRoomStageDetails, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomStage !== 0) {
      writer.uint32(8).int32(message.roomStage);
    }
    if (message.roomType !== 0) {
      writer.uint32(16).int32(message.roomType);
    }
    if (message.roomStageDetails !== undefined) {
      RoomStageDetails.encode(message.roomStageDetails, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateEffects_LoadRoomStageDetails {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateEffects_LoadRoomStageDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.roomStage = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.roomType = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomStageDetails = RoomStageDetails.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateEffects_LoadRoomStageDetails {
    return {
      roomStage: isSet(object.roomStage) ? roomStagesFromJSON(object.roomStage) : 0,
      roomType: isSet(object.roomType) ? roomTypeFromJSON(object.roomType) : 0,
      roomStageDetails: isSet(object.roomStageDetails) ? RoomStageDetails.fromJSON(object.roomStageDetails) : undefined,
    };
  },

  toJSON(message: AppStateEffects_LoadRoomStageDetails): unknown {
    const obj: any = {};
    if (message.roomStage !== 0) {
      obj.roomStage = roomStagesToJSON(message.roomStage);
    }
    if (message.roomType !== 0) {
      obj.roomType = roomTypeToJSON(message.roomType);
    }
    if (message.roomStageDetails !== undefined) {
      obj.roomStageDetails = RoomStageDetails.toJSON(message.roomStageDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateEffects_LoadRoomStageDetails>, I>>(
    base?: I,
  ): AppStateEffects_LoadRoomStageDetails {
    return AppStateEffects_LoadRoomStageDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateEffects_LoadRoomStageDetails>, I>>(
    object: I,
  ): AppStateEffects_LoadRoomStageDetails {
    const message = createBaseAppStateEffects_LoadRoomStageDetails();
    message.roomStage = object.roomStage ?? 0;
    message.roomType = object.roomType ?? 0;
    message.roomStageDetails = (object.roomStageDetails !== undefined && object.roomStageDetails !== null)
      ? RoomStageDetails.fromPartial(object.roomStageDetails)
      : undefined;
    return message;
  },
};

function createBaseAppStateEffects_BytesPayload(): AppStateEffects_BytesPayload {
  return { payload: [], id: undefined };
}

export const AppStateEffects_BytesPayload = {
  encode(message: AppStateEffects_BytesPayload, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.payload) {
      writer.uint32(v);
    }
    writer.ldelim();
    if (message.id !== undefined) {
      writer.uint32(18).string(message.id);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateEffects_BytesPayload {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateEffects_BytesPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.payload.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.payload.push(reader.uint32());
            }

            continue;
          }

          break;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.id = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateEffects_BytesPayload {
    return {
      payload: globalThis.Array.isArray(object?.payload) ? object.payload.map((e: any) => globalThis.Number(e)) : [],
      id: isSet(object.id) ? globalThis.String(object.id) : undefined,
    };
  },

  toJSON(message: AppStateEffects_BytesPayload): unknown {
    const obj: any = {};
    if (message.payload?.length) {
      obj.payload = message.payload.map((e) => Math.round(e));
    }
    if (message.id !== undefined) {
      obj.id = message.id;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateEffects_BytesPayload>, I>>(base?: I): AppStateEffects_BytesPayload {
    return AppStateEffects_BytesPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateEffects_BytesPayload>, I>>(object: I): AppStateEffects_BytesPayload {
    const message = createBaseAppStateEffects_BytesPayload();
    message.payload = object.payload?.map((e) => e) || [];
    message.id = object.id ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
