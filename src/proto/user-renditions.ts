// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: user-renditions.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";

export const protobufPackage = "PianoRhythm.Serialization.ServerToClient.UserRenditions";

export enum Roles {
  ADMIN = 0,
  SYSTEM = 1,
  DEVELOPER = 2,
  MODERATOR = 3,
  TRIAL_MODERATOR = 4,
  MEMBER = 5,
  ROOMOWNER = 6,
  BOT = 7,
  HELPBOT = 8,
  DISCORDBOT = 9,
  GUEST = 10,
  SHEETMUSICEDITOR = 11,
  MIDIMUSICEDITOR = 12,
  PLUGIN = 13,
  PRO = 14,
  UNKNOWN = 15,
  UNRECOGNIZED = -1,
}

export function rolesFromJSON(object: any): Roles {
  switch (object) {
    case 0:
    case "ADMIN":
      return Roles.ADMIN;
    case 1:
    case "SYSTEM":
      return Roles.SYSTEM;
    case 2:
    case "DEVELOPER":
      return Roles.DEVELOPER;
    case 3:
    case "MODERATOR":
      return Roles.MODERATOR;
    case 4:
    case "TRIAL_MODERATOR":
      return Roles.TRIAL_MODERATOR;
    case 5:
    case "MEMBER":
      return Roles.MEMBER;
    case 6:
    case "ROOMOWNER":
      return Roles.ROOMOWNER;
    case 7:
    case "BOT":
      return Roles.BOT;
    case 8:
    case "HELPBOT":
      return Roles.HELPBOT;
    case 9:
    case "DISCORDBOT":
      return Roles.DISCORDBOT;
    case 10:
    case "GUEST":
      return Roles.GUEST;
    case 11:
    case "SHEETMUSICEDITOR":
      return Roles.SHEETMUSICEDITOR;
    case 12:
    case "MIDIMUSICEDITOR":
      return Roles.MIDIMUSICEDITOR;
    case 13:
    case "PLUGIN":
      return Roles.PLUGIN;
    case 14:
    case "PRO":
      return Roles.PRO;
    case 15:
    case "UNKNOWN":
      return Roles.UNKNOWN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Roles.UNRECOGNIZED;
  }
}

export function rolesToJSON(object: Roles): string {
  switch (object) {
    case Roles.ADMIN:
      return "ADMIN";
    case Roles.SYSTEM:
      return "SYSTEM";
    case Roles.DEVELOPER:
      return "DEVELOPER";
    case Roles.MODERATOR:
      return "MODERATOR";
    case Roles.TRIAL_MODERATOR:
      return "TRIAL_MODERATOR";
    case Roles.MEMBER:
      return "MEMBER";
    case Roles.ROOMOWNER:
      return "ROOMOWNER";
    case Roles.BOT:
      return "BOT";
    case Roles.HELPBOT:
      return "HELPBOT";
    case Roles.DISCORDBOT:
      return "DISCORDBOT";
    case Roles.GUEST:
      return "GUEST";
    case Roles.SHEETMUSICEDITOR:
      return "SHEETMUSICEDITOR";
    case Roles.MIDIMUSICEDITOR:
      return "MIDIMUSICEDITOR";
    case Roles.PLUGIN:
      return "PLUGIN";
    case Roles.PRO:
      return "PRO";
    case Roles.UNKNOWN:
      return "UNKNOWN";
    case Roles.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum Badges {
  V3_CLOSED_ALPHA_TESTER = 0,
  V2_OG_MEMBER = 1,
  PRO_MEMBER = 2,
  TranslationMasterContributor = 3,
  TranslationContributor = 4,
  CUSTOM = 5,
  UNRECOGNIZED = -1,
}

export function badgesFromJSON(object: any): Badges {
  switch (object) {
    case 0:
    case "V3_CLOSED_ALPHA_TESTER":
      return Badges.V3_CLOSED_ALPHA_TESTER;
    case 1:
    case "V2_OG_MEMBER":
      return Badges.V2_OG_MEMBER;
    case 2:
    case "PRO_MEMBER":
      return Badges.PRO_MEMBER;
    case 3:
    case "TranslationMasterContributor":
      return Badges.TranslationMasterContributor;
    case 4:
    case "TranslationContributor":
      return Badges.TranslationContributor;
    case 5:
    case "CUSTOM":
      return Badges.CUSTOM;
    case -1:
    case "UNRECOGNIZED":
    default:
      return Badges.UNRECOGNIZED;
  }
}

export function badgesToJSON(object: Badges): string {
  switch (object) {
    case Badges.V3_CLOSED_ALPHA_TESTER:
      return "V3_CLOSED_ALPHA_TESTER";
    case Badges.V2_OG_MEMBER:
      return "V2_OG_MEMBER";
    case Badges.PRO_MEMBER:
      return "PRO_MEMBER";
    case Badges.TranslationMasterContributor:
      return "TranslationMasterContributor";
    case Badges.TranslationContributor:
      return "TranslationContributor";
    case Badges.CUSTOM:
      return "CUSTOM";
    case Badges.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum UserStatus {
  None = 0,
  Unknown = 1,
  Online = 2,
  Idle = 3,
  DoNotDisturb = 4,
  AFK = 5,
  UNRECOGNIZED = -1,
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case "None":
      return UserStatus.None;
    case 1:
    case "Unknown":
      return UserStatus.Unknown;
    case 2:
    case "Online":
      return UserStatus.Online;
    case 3:
    case "Idle":
      return UserStatus.Idle;
    case 4:
    case "DoNotDisturb":
      return UserStatus.DoNotDisturb;
    case 5:
    case "AFK":
      return UserStatus.AFK;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

export function userStatusToJSON(object: UserStatus): string {
  switch (object) {
    case UserStatus.None:
      return "None";
    case UserStatus.Unknown:
      return "Unknown";
    case UserStatus.Online:
      return "Online";
    case UserStatus.Idle:
      return "Idle";
    case UserStatus.DoNotDisturb:
      return "DoNotDisturb";
    case UserStatus.AFK:
      return "AFK";
    case UserStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UserBadges {
  badge: Badges;
  value?: string | undefined;
}

export interface UserMetaDto {
  bot: boolean;
  discordBot: boolean;
  modifiedDate?: string | undefined;
  clientMetaDetails?: string | undefined;
  enteredRoomDateTime?: string | undefined;
}

export interface AvatarWorldDataDto {
  worldPosition?: AvatarWorldDataDto_AvatarMessageWorldPosition | undefined;
  pianoBenchSeat?: number | undefined;
}

export interface AvatarWorldDataDto_AvatarMessageWorldPosition {
  x: number;
  y: number;
  z: number;
}

export interface WorldData {
  characterDataJSON?: string | undefined;
  orchestraModelCustomizationDataJSON?: string | undefined;
  avatarWorldData?: AvatarWorldDataDto | undefined;
}

export interface UserDto {
  username: string;
  usertag: string;
  socketID: string;
  roles: Roles[];
  badges: UserBadges[];
  nickname?: string | undefined;
  color: string;
  meta: UserMetaDto | undefined;
  selfMuted: boolean;
  serverNotesMuted: boolean;
  serverChatMuted: boolean;
  status: UserStatus;
  ProfileDescription?: string | undefined;
  profileImageLastModified?: string | undefined;
  profileBackgroundImageLastModified?: string | undefined;
  worldData: WorldData | undefined;
  isProMember: boolean;
  statusText?: string | undefined;
  uuid: string;
}

export interface UserBillingSettings {
  currentPlan: string;
  cancelationInProcess: boolean;
  meta: UserBillingSettings_BillingMeta | undefined;
}

export enum UserBillingSettings_SubscriptionPlan {
  Free = 0,
  Level1 = 1,
  UNRECOGNIZED = -1,
}

export function userBillingSettings_SubscriptionPlanFromJSON(object: any): UserBillingSettings_SubscriptionPlan {
  switch (object) {
    case 0:
    case "Free":
      return UserBillingSettings_SubscriptionPlan.Free;
    case 1:
    case "Level1":
      return UserBillingSettings_SubscriptionPlan.Level1;
    case -1:
    case "UNRECOGNIZED":
    default:
      return UserBillingSettings_SubscriptionPlan.UNRECOGNIZED;
  }
}

export function userBillingSettings_SubscriptionPlanToJSON(object: UserBillingSettings_SubscriptionPlan): string {
  switch (object) {
    case UserBillingSettings_SubscriptionPlan.Free:
      return "Free";
    case UserBillingSettings_SubscriptionPlan.Level1:
      return "Level1";
    case UserBillingSettings_SubscriptionPlan.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface UserBillingSettings_BillingMeta {
  nextBillingDate: string;
  currencyCode: string;
  pricePerUnit: string;
  priceID: string;
}

export interface ClientMetaDto {
  email?: string | undefined;
  billingSettings: UserBillingSettings | undefined;
}

export interface KickedUserData {
  socketID: string;
  usertag: string;
  time: string;
  createdDate: string;
}

export interface UserClientDto {
  userDto: UserDto | undefined;
  meta?: ClientMetaDto | undefined;
  canApproveSheetMusic: boolean;
  has2faEnabled: boolean;
  isOAuthAccount: boolean;
}

export interface ClientSideUserDto {
  userDto: UserDto | undefined;
  localNotesMuted: boolean;
  localChatMuted: boolean;
  clientMetaDetailsParsed?: string | undefined;
  socketID: string;
  socketIDHashed?: number | undefined;
}

export interface UserUpdateCommand {
  socketID: string;
  userColor?: string | undefined;
  userStatus?: UserStatus | undefined;
  clientMetaDetails?: string | undefined;
  profileDescription?: string | undefined;
  selfMuted?: boolean | undefined;
  statusText?: string | undefined;
  nickname?: string | undefined;
  profileImageUpdated?: string | undefined;
  profileBackgroundImageUpdated?: string | undefined;
  profileImageCleared?: boolean | undefined;
  profileBackgroundImageCleared?: boolean | undefined;
  orchestraModel?: string | undefined;
  characterModel?: string | undefined;
  serverNotesMuted?: boolean | undefined;
  serverChatMuted?: boolean | undefined;
  avatarWorldPosition?: AvatarWorldDataDto_AvatarMessageWorldPosition | undefined;
  avatarPianoBenchSeat?: number | undefined;
  badges: UserBadges[];
}

export interface FriendDto {
  username: string;
  usertag: string;
  socketID?: string | undefined;
  roomID?: string | undefined;
  roles: Roles[];
  nickname?: string | undefined;
  color?: string | undefined;
  statusText?: string | undefined;
  profileDescription?: string | undefined;
  becameFriendsDate?: string | undefined;
  profileImageLastModified?: string | undefined;
  profileBackgroundImageLastModified?: string | undefined;
  lastOnline?: string | undefined;
  onlineStatus?: FriendDto_FriendOnlineStatus | undefined;
}

export enum FriendDto_FriendOnlineStatus {
  None = 0,
  Unknown = 1,
  Online = 2,
  Idle = 3,
  Offline = 4,
  PendingAcceptance = 5,
  UNRECOGNIZED = -1,
}

export function friendDto_FriendOnlineStatusFromJSON(object: any): FriendDto_FriendOnlineStatus {
  switch (object) {
    case 0:
    case "None":
      return FriendDto_FriendOnlineStatus.None;
    case 1:
    case "Unknown":
      return FriendDto_FriendOnlineStatus.Unknown;
    case 2:
    case "Online":
      return FriendDto_FriendOnlineStatus.Online;
    case 3:
    case "Idle":
      return FriendDto_FriendOnlineStatus.Idle;
    case 4:
    case "Offline":
      return FriendDto_FriendOnlineStatus.Offline;
    case 5:
    case "PendingAcceptance":
      return FriendDto_FriendOnlineStatus.PendingAcceptance;
    case -1:
    case "UNRECOGNIZED":
    default:
      return FriendDto_FriendOnlineStatus.UNRECOGNIZED;
  }
}

export function friendDto_FriendOnlineStatusToJSON(object: FriendDto_FriendOnlineStatus): string {
  switch (object) {
    case FriendDto_FriendOnlineStatus.None:
      return "None";
    case FriendDto_FriendOnlineStatus.Unknown:
      return "Unknown";
    case FriendDto_FriendOnlineStatus.Online:
      return "Online";
    case FriendDto_FriendOnlineStatus.Idle:
      return "Idle";
    case FriendDto_FriendOnlineStatus.Offline:
      return "Offline";
    case FriendDto_FriendOnlineStatus.PendingAcceptance:
      return "PendingAcceptance";
    case FriendDto_FriendOnlineStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface PendingFriendRequest {
  usertag: string;
  uuid: string;
  createdDate: string;
}

function createBaseUserBadges(): UserBadges {
  return { badge: 0, value: undefined };
}

export const UserBadges = {
  encode(message: UserBadges, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.badge !== 0) {
      writer.uint32(8).int32(message.badge);
    }
    if (message.value !== undefined) {
      writer.uint32(18).string(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserBadges {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserBadges();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.badge = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.value = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserBadges {
    return {
      badge: isSet(object.badge) ? badgesFromJSON(object.badge) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : undefined,
    };
  },

  toJSON(message: UserBadges): unknown {
    const obj: any = {};
    if (message.badge !== 0) {
      obj.badge = badgesToJSON(message.badge);
    }
    if (message.value !== undefined) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserBadges>, I>>(base?: I): UserBadges {
    return UserBadges.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBadges>, I>>(object: I): UserBadges {
    const message = createBaseUserBadges();
    message.badge = object.badge ?? 0;
    message.value = object.value ?? undefined;
    return message;
  },
};

function createBaseUserMetaDto(): UserMetaDto {
  return {
    bot: false,
    discordBot: false,
    modifiedDate: undefined,
    clientMetaDetails: undefined,
    enteredRoomDateTime: undefined,
  };
}

export const UserMetaDto = {
  encode(message: UserMetaDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.bot !== false) {
      writer.uint32(8).bool(message.bot);
    }
    if (message.discordBot !== false) {
      writer.uint32(16).bool(message.discordBot);
    }
    if (message.modifiedDate !== undefined) {
      writer.uint32(26).string(message.modifiedDate);
    }
    if (message.clientMetaDetails !== undefined) {
      writer.uint32(34).string(message.clientMetaDetails);
    }
    if (message.enteredRoomDateTime !== undefined) {
      writer.uint32(50).string(message.enteredRoomDateTime);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserMetaDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserMetaDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.bot = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.discordBot = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.modifiedDate = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.clientMetaDetails = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.enteredRoomDateTime = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserMetaDto {
    return {
      bot: isSet(object.bot) ? globalThis.Boolean(object.bot) : false,
      discordBot: isSet(object.discordBot) ? globalThis.Boolean(object.discordBot) : false,
      modifiedDate: isSet(object.modifiedDate) ? globalThis.String(object.modifiedDate) : undefined,
      clientMetaDetails: isSet(object.clientMetaDetails) ? globalThis.String(object.clientMetaDetails) : undefined,
      enteredRoomDateTime: isSet(object.enteredRoomDateTime)
        ? globalThis.String(object.enteredRoomDateTime)
        : undefined,
    };
  },

  toJSON(message: UserMetaDto): unknown {
    const obj: any = {};
    if (message.bot !== false) {
      obj.bot = message.bot;
    }
    if (message.discordBot !== false) {
      obj.discordBot = message.discordBot;
    }
    if (message.modifiedDate !== undefined) {
      obj.modifiedDate = message.modifiedDate;
    }
    if (message.clientMetaDetails !== undefined) {
      obj.clientMetaDetails = message.clientMetaDetails;
    }
    if (message.enteredRoomDateTime !== undefined) {
      obj.enteredRoomDateTime = message.enteredRoomDateTime;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserMetaDto>, I>>(base?: I): UserMetaDto {
    return UserMetaDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserMetaDto>, I>>(object: I): UserMetaDto {
    const message = createBaseUserMetaDto();
    message.bot = object.bot ?? false;
    message.discordBot = object.discordBot ?? false;
    message.modifiedDate = object.modifiedDate ?? undefined;
    message.clientMetaDetails = object.clientMetaDetails ?? undefined;
    message.enteredRoomDateTime = object.enteredRoomDateTime ?? undefined;
    return message;
  },
};

function createBaseAvatarWorldDataDto(): AvatarWorldDataDto {
  return { worldPosition: undefined, pianoBenchSeat: undefined };
}

export const AvatarWorldDataDto = {
  encode(message: AvatarWorldDataDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.worldPosition !== undefined) {
      AvatarWorldDataDto_AvatarMessageWorldPosition.encode(message.worldPosition, writer.uint32(10).fork()).ldelim();
    }
    if (message.pianoBenchSeat !== undefined) {
      writer.uint32(16).int32(message.pianoBenchSeat);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AvatarWorldDataDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAvatarWorldDataDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.pianoBenchSeat = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AvatarWorldDataDto {
    return {
      worldPosition: isSet(object.worldPosition)
        ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromJSON(object.worldPosition)
        : undefined,
      pianoBenchSeat: isSet(object.pianoBenchSeat) ? globalThis.Number(object.pianoBenchSeat) : undefined,
    };
  },

  toJSON(message: AvatarWorldDataDto): unknown {
    const obj: any = {};
    if (message.worldPosition !== undefined) {
      obj.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.toJSON(message.worldPosition);
    }
    if (message.pianoBenchSeat !== undefined) {
      obj.pianoBenchSeat = Math.round(message.pianoBenchSeat);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AvatarWorldDataDto>, I>>(base?: I): AvatarWorldDataDto {
    return AvatarWorldDataDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvatarWorldDataDto>, I>>(object: I): AvatarWorldDataDto {
    const message = createBaseAvatarWorldDataDto();
    message.worldPosition = (object.worldPosition !== undefined && object.worldPosition !== null)
      ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(object.worldPosition)
      : undefined;
    message.pianoBenchSeat = object.pianoBenchSeat ?? undefined;
    return message;
  },
};

function createBaseAvatarWorldDataDto_AvatarMessageWorldPosition(): AvatarWorldDataDto_AvatarMessageWorldPosition {
  return { x: 0, y: 0, z: 0 };
}

export const AvatarWorldDataDto_AvatarMessageWorldPosition = {
  encode(message: AvatarWorldDataDto_AvatarMessageWorldPosition, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.x !== 0) {
      writer.uint32(9).double(message.x);
    }
    if (message.y !== 0) {
      writer.uint32(17).double(message.y);
    }
    if (message.z !== 0) {
      writer.uint32(25).double(message.z);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AvatarWorldDataDto_AvatarMessageWorldPosition {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAvatarWorldDataDto_AvatarMessageWorldPosition();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.x = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.y = reader.double();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.z = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AvatarWorldDataDto_AvatarMessageWorldPosition {
    return {
      x: isSet(object.x) ? globalThis.Number(object.x) : 0,
      y: isSet(object.y) ? globalThis.Number(object.y) : 0,
      z: isSet(object.z) ? globalThis.Number(object.z) : 0,
    };
  },

  toJSON(message: AvatarWorldDataDto_AvatarMessageWorldPosition): unknown {
    const obj: any = {};
    if (message.x !== 0) {
      obj.x = message.x;
    }
    if (message.y !== 0) {
      obj.y = message.y;
    }
    if (message.z !== 0) {
      obj.z = message.z;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AvatarWorldDataDto_AvatarMessageWorldPosition>, I>>(
    base?: I,
  ): AvatarWorldDataDto_AvatarMessageWorldPosition {
    return AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvatarWorldDataDto_AvatarMessageWorldPosition>, I>>(
    object: I,
  ): AvatarWorldDataDto_AvatarMessageWorldPosition {
    const message = createBaseAvatarWorldDataDto_AvatarMessageWorldPosition();
    message.x = object.x ?? 0;
    message.y = object.y ?? 0;
    message.z = object.z ?? 0;
    return message;
  },
};

function createBaseWorldData(): WorldData {
  return { characterDataJSON: undefined, orchestraModelCustomizationDataJSON: undefined, avatarWorldData: undefined };
}

export const WorldData = {
  encode(message: WorldData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.characterDataJSON !== undefined) {
      writer.uint32(10).string(message.characterDataJSON);
    }
    if (message.orchestraModelCustomizationDataJSON !== undefined) {
      writer.uint32(18).string(message.orchestraModelCustomizationDataJSON);
    }
    if (message.avatarWorldData !== undefined) {
      AvatarWorldDataDto.encode(message.avatarWorldData, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): WorldData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWorldData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.characterDataJSON = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.orchestraModelCustomizationDataJSON = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.avatarWorldData = AvatarWorldDataDto.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WorldData {
    return {
      characterDataJSON: isSet(object.characterDataJSON) ? globalThis.String(object.characterDataJSON) : undefined,
      orchestraModelCustomizationDataJSON: isSet(object.orchestraModelCustomizationDataJSON)
        ? globalThis.String(object.orchestraModelCustomizationDataJSON)
        : undefined,
      avatarWorldData: isSet(object.avatarWorldData) ? AvatarWorldDataDto.fromJSON(object.avatarWorldData) : undefined,
    };
  },

  toJSON(message: WorldData): unknown {
    const obj: any = {};
    if (message.characterDataJSON !== undefined) {
      obj.characterDataJSON = message.characterDataJSON;
    }
    if (message.orchestraModelCustomizationDataJSON !== undefined) {
      obj.orchestraModelCustomizationDataJSON = message.orchestraModelCustomizationDataJSON;
    }
    if (message.avatarWorldData !== undefined) {
      obj.avatarWorldData = AvatarWorldDataDto.toJSON(message.avatarWorldData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WorldData>, I>>(base?: I): WorldData {
    return WorldData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WorldData>, I>>(object: I): WorldData {
    const message = createBaseWorldData();
    message.characterDataJSON = object.characterDataJSON ?? undefined;
    message.orchestraModelCustomizationDataJSON = object.orchestraModelCustomizationDataJSON ?? undefined;
    message.avatarWorldData = (object.avatarWorldData !== undefined && object.avatarWorldData !== null)
      ? AvatarWorldDataDto.fromPartial(object.avatarWorldData)
      : undefined;
    return message;
  },
};

function createBaseUserDto(): UserDto {
  return {
    username: "",
    usertag: "",
    socketID: "",
    roles: [],
    badges: [],
    nickname: undefined,
    color: "",
    meta: undefined,
    selfMuted: false,
    serverNotesMuted: false,
    serverChatMuted: false,
    status: 0,
    ProfileDescription: undefined,
    profileImageLastModified: undefined,
    profileBackgroundImageLastModified: undefined,
    worldData: undefined,
    isProMember: false,
    statusText: undefined,
    uuid: "",
  };
}

export const UserDto = {
  encode(message: UserDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.usertag !== "") {
      writer.uint32(18).string(message.usertag);
    }
    if (message.socketID !== "") {
      writer.uint32(26).string(message.socketID);
    }
    writer.uint32(34).fork();
    for (const v of message.roles) {
      writer.int32(v);
    }
    writer.ldelim();
    for (const v of message.badges) {
      UserBadges.encode(v!, writer.uint32(42).fork()).ldelim();
    }
    if (message.nickname !== undefined) {
      writer.uint32(50).string(message.nickname);
    }
    if (message.color !== "") {
      writer.uint32(58).string(message.color);
    }
    if (message.meta !== undefined) {
      UserMetaDto.encode(message.meta, writer.uint32(66).fork()).ldelim();
    }
    if (message.selfMuted !== false) {
      writer.uint32(72).bool(message.selfMuted);
    }
    if (message.serverNotesMuted !== false) {
      writer.uint32(80).bool(message.serverNotesMuted);
    }
    if (message.serverChatMuted !== false) {
      writer.uint32(88).bool(message.serverChatMuted);
    }
    if (message.status !== 0) {
      writer.uint32(96).int32(message.status);
    }
    if (message.ProfileDescription !== undefined) {
      writer.uint32(106).string(message.ProfileDescription);
    }
    if (message.profileImageLastModified !== undefined) {
      writer.uint32(114).string(message.profileImageLastModified);
    }
    if (message.profileBackgroundImageLastModified !== undefined) {
      writer.uint32(122).string(message.profileBackgroundImageLastModified);
    }
    if (message.worldData !== undefined) {
      WorldData.encode(message.worldData, writer.uint32(130).fork()).ldelim();
    }
    if (message.isProMember !== false) {
      writer.uint32(136).bool(message.isProMember);
    }
    if (message.statusText !== undefined) {
      writer.uint32(146).string(message.statusText);
    }
    if (message.uuid !== "") {
      writer.uint32(154).string(message.uuid);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 4:
          if (tag === 32) {
            message.roles.push(reader.int32() as any);

            continue;
          }

          if (tag === 34) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.roles.push(reader.int32() as any);
            }

            continue;
          }

          break;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.badges.push(UserBadges.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.nickname = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.color = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.meta = UserMetaDto.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.selfMuted = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.serverNotesMuted = reader.bool();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.serverChatMuted = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.ProfileDescription = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.profileImageLastModified = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.profileBackgroundImageLastModified = reader.string();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.worldData = WorldData.decode(reader, reader.uint32());
          continue;
        case 17:
          if (tag !== 136) {
            break;
          }

          message.isProMember = reader.bool();
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.statusText = reader.string();
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.uuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDto {
    return {
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      roles: globalThis.Array.isArray(object?.roles) ? object.roles.map((e: any) => rolesFromJSON(e)) : [],
      badges: globalThis.Array.isArray(object?.badges) ? object.badges.map((e: any) => UserBadges.fromJSON(e)) : [],
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : undefined,
      color: isSet(object.color) ? globalThis.String(object.color) : "",
      meta: isSet(object.meta) ? UserMetaDto.fromJSON(object.meta) : undefined,
      selfMuted: isSet(object.selfMuted) ? globalThis.Boolean(object.selfMuted) : false,
      serverNotesMuted: isSet(object.serverNotesMuted) ? globalThis.Boolean(object.serverNotesMuted) : false,
      serverChatMuted: isSet(object.serverChatMuted) ? globalThis.Boolean(object.serverChatMuted) : false,
      status: isSet(object.status) ? userStatusFromJSON(object.status) : 0,
      ProfileDescription: isSet(object.ProfileDescription) ? globalThis.String(object.ProfileDescription) : undefined,
      profileImageLastModified: isSet(object.profileImageLastModified)
        ? globalThis.String(object.profileImageLastModified)
        : undefined,
      profileBackgroundImageLastModified: isSet(object.profileBackgroundImageLastModified)
        ? globalThis.String(object.profileBackgroundImageLastModified)
        : undefined,
      worldData: isSet(object.worldData) ? WorldData.fromJSON(object.worldData) : undefined,
      isProMember: isSet(object.isProMember) ? globalThis.Boolean(object.isProMember) : false,
      statusText: isSet(object.statusText) ? globalThis.String(object.statusText) : undefined,
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
    };
  },

  toJSON(message: UserDto): unknown {
    const obj: any = {};
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.roles?.length) {
      obj.roles = message.roles.map((e) => rolesToJSON(e));
    }
    if (message.badges?.length) {
      obj.badges = message.badges.map((e) => UserBadges.toJSON(e));
    }
    if (message.nickname !== undefined) {
      obj.nickname = message.nickname;
    }
    if (message.color !== "") {
      obj.color = message.color;
    }
    if (message.meta !== undefined) {
      obj.meta = UserMetaDto.toJSON(message.meta);
    }
    if (message.selfMuted !== false) {
      obj.selfMuted = message.selfMuted;
    }
    if (message.serverNotesMuted !== false) {
      obj.serverNotesMuted = message.serverNotesMuted;
    }
    if (message.serverChatMuted !== false) {
      obj.serverChatMuted = message.serverChatMuted;
    }
    if (message.status !== 0) {
      obj.status = userStatusToJSON(message.status);
    }
    if (message.ProfileDescription !== undefined) {
      obj.ProfileDescription = message.ProfileDescription;
    }
    if (message.profileImageLastModified !== undefined) {
      obj.profileImageLastModified = message.profileImageLastModified;
    }
    if (message.profileBackgroundImageLastModified !== undefined) {
      obj.profileBackgroundImageLastModified = message.profileBackgroundImageLastModified;
    }
    if (message.worldData !== undefined) {
      obj.worldData = WorldData.toJSON(message.worldData);
    }
    if (message.isProMember !== false) {
      obj.isProMember = message.isProMember;
    }
    if (message.statusText !== undefined) {
      obj.statusText = message.statusText;
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDto>, I>>(base?: I): UserDto {
    return UserDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDto>, I>>(object: I): UserDto {
    const message = createBaseUserDto();
    message.username = object.username ?? "";
    message.usertag = object.usertag ?? "";
    message.socketID = object.socketID ?? "";
    message.roles = object.roles?.map((e) => e) || [];
    message.badges = object.badges?.map((e) => UserBadges.fromPartial(e)) || [];
    message.nickname = object.nickname ?? undefined;
    message.color = object.color ?? "";
    message.meta = (object.meta !== undefined && object.meta !== null)
      ? UserMetaDto.fromPartial(object.meta)
      : undefined;
    message.selfMuted = object.selfMuted ?? false;
    message.serverNotesMuted = object.serverNotesMuted ?? false;
    message.serverChatMuted = object.serverChatMuted ?? false;
    message.status = object.status ?? 0;
    message.ProfileDescription = object.ProfileDescription ?? undefined;
    message.profileImageLastModified = object.profileImageLastModified ?? undefined;
    message.profileBackgroundImageLastModified = object.profileBackgroundImageLastModified ?? undefined;
    message.worldData = (object.worldData !== undefined && object.worldData !== null)
      ? WorldData.fromPartial(object.worldData)
      : undefined;
    message.isProMember = object.isProMember ?? false;
    message.statusText = object.statusText ?? undefined;
    message.uuid = object.uuid ?? "";
    return message;
  },
};

function createBaseUserBillingSettings(): UserBillingSettings {
  return { currentPlan: "", cancelationInProcess: false, meta: undefined };
}

export const UserBillingSettings = {
  encode(message: UserBillingSettings, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.currentPlan !== "") {
      writer.uint32(10).string(message.currentPlan);
    }
    if (message.cancelationInProcess !== false) {
      writer.uint32(16).bool(message.cancelationInProcess);
    }
    if (message.meta !== undefined) {
      UserBillingSettings_BillingMeta.encode(message.meta, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserBillingSettings {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserBillingSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.currentPlan = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.cancelationInProcess = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.meta = UserBillingSettings_BillingMeta.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserBillingSettings {
    return {
      currentPlan: isSet(object.currentPlan) ? globalThis.String(object.currentPlan) : "",
      cancelationInProcess: isSet(object.cancelationInProcess)
        ? globalThis.Boolean(object.cancelationInProcess)
        : false,
      meta: isSet(object.meta) ? UserBillingSettings_BillingMeta.fromJSON(object.meta) : undefined,
    };
  },

  toJSON(message: UserBillingSettings): unknown {
    const obj: any = {};
    if (message.currentPlan !== "") {
      obj.currentPlan = message.currentPlan;
    }
    if (message.cancelationInProcess !== false) {
      obj.cancelationInProcess = message.cancelationInProcess;
    }
    if (message.meta !== undefined) {
      obj.meta = UserBillingSettings_BillingMeta.toJSON(message.meta);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserBillingSettings>, I>>(base?: I): UserBillingSettings {
    return UserBillingSettings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBillingSettings>, I>>(object: I): UserBillingSettings {
    const message = createBaseUserBillingSettings();
    message.currentPlan = object.currentPlan ?? "";
    message.cancelationInProcess = object.cancelationInProcess ?? false;
    message.meta = (object.meta !== undefined && object.meta !== null)
      ? UserBillingSettings_BillingMeta.fromPartial(object.meta)
      : undefined;
    return message;
  },
};

function createBaseUserBillingSettings_BillingMeta(): UserBillingSettings_BillingMeta {
  return { nextBillingDate: "", currencyCode: "", pricePerUnit: "", priceID: "" };
}

export const UserBillingSettings_BillingMeta = {
  encode(message: UserBillingSettings_BillingMeta, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.nextBillingDate !== "") {
      writer.uint32(10).string(message.nextBillingDate);
    }
    if (message.currencyCode !== "") {
      writer.uint32(18).string(message.currencyCode);
    }
    if (message.pricePerUnit !== "") {
      writer.uint32(26).string(message.pricePerUnit);
    }
    if (message.priceID !== "") {
      writer.uint32(34).string(message.priceID);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserBillingSettings_BillingMeta {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserBillingSettings_BillingMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.nextBillingDate = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.currencyCode = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.pricePerUnit = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.priceID = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserBillingSettings_BillingMeta {
    return {
      nextBillingDate: isSet(object.nextBillingDate) ? globalThis.String(object.nextBillingDate) : "",
      currencyCode: isSet(object.currencyCode) ? globalThis.String(object.currencyCode) : "",
      pricePerUnit: isSet(object.pricePerUnit) ? globalThis.String(object.pricePerUnit) : "",
      priceID: isSet(object.priceID) ? globalThis.String(object.priceID) : "",
    };
  },

  toJSON(message: UserBillingSettings_BillingMeta): unknown {
    const obj: any = {};
    if (message.nextBillingDate !== "") {
      obj.nextBillingDate = message.nextBillingDate;
    }
    if (message.currencyCode !== "") {
      obj.currencyCode = message.currencyCode;
    }
    if (message.pricePerUnit !== "") {
      obj.pricePerUnit = message.pricePerUnit;
    }
    if (message.priceID !== "") {
      obj.priceID = message.priceID;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserBillingSettings_BillingMeta>, I>>(base?: I): UserBillingSettings_BillingMeta {
    return UserBillingSettings_BillingMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBillingSettings_BillingMeta>, I>>(
    object: I,
  ): UserBillingSettings_BillingMeta {
    const message = createBaseUserBillingSettings_BillingMeta();
    message.nextBillingDate = object.nextBillingDate ?? "";
    message.currencyCode = object.currencyCode ?? "";
    message.pricePerUnit = object.pricePerUnit ?? "";
    message.priceID = object.priceID ?? "";
    return message;
  },
};

function createBaseClientMetaDto(): ClientMetaDto {
  return { email: undefined, billingSettings: undefined };
}

export const ClientMetaDto = {
  encode(message: ClientMetaDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.email !== undefined) {
      writer.uint32(10).string(message.email);
    }
    if (message.billingSettings !== undefined) {
      UserBillingSettings.encode(message.billingSettings, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ClientMetaDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientMetaDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.billingSettings = UserBillingSettings.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClientMetaDto {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : undefined,
      billingSettings: isSet(object.billingSettings) ? UserBillingSettings.fromJSON(object.billingSettings) : undefined,
    };
  },

  toJSON(message: ClientMetaDto): unknown {
    const obj: any = {};
    if (message.email !== undefined) {
      obj.email = message.email;
    }
    if (message.billingSettings !== undefined) {
      obj.billingSettings = UserBillingSettings.toJSON(message.billingSettings);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClientMetaDto>, I>>(base?: I): ClientMetaDto {
    return ClientMetaDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientMetaDto>, I>>(object: I): ClientMetaDto {
    const message = createBaseClientMetaDto();
    message.email = object.email ?? undefined;
    message.billingSettings = (object.billingSettings !== undefined && object.billingSettings !== null)
      ? UserBillingSettings.fromPartial(object.billingSettings)
      : undefined;
    return message;
  },
};

function createBaseKickedUserData(): KickedUserData {
  return { socketID: "", usertag: "", time: "", createdDate: "" };
}

export const KickedUserData = {
  encode(message: KickedUserData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.socketID !== "") {
      writer.uint32(10).string(message.socketID);
    }
    if (message.usertag !== "") {
      writer.uint32(18).string(message.usertag);
    }
    if (message.time !== "") {
      writer.uint32(26).string(message.time);
    }
    if (message.createdDate !== "") {
      writer.uint32(34).string(message.createdDate);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): KickedUserData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKickedUserData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.time = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.createdDate = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KickedUserData {
    return {
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      time: isSet(object.time) ? globalThis.String(object.time) : "",
      createdDate: isSet(object.createdDate) ? globalThis.String(object.createdDate) : "",
    };
  },

  toJSON(message: KickedUserData): unknown {
    const obj: any = {};
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.time !== "") {
      obj.time = message.time;
    }
    if (message.createdDate !== "") {
      obj.createdDate = message.createdDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KickedUserData>, I>>(base?: I): KickedUserData {
    return KickedUserData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickedUserData>, I>>(object: I): KickedUserData {
    const message = createBaseKickedUserData();
    message.socketID = object.socketID ?? "";
    message.usertag = object.usertag ?? "";
    message.time = object.time ?? "";
    message.createdDate = object.createdDate ?? "";
    return message;
  },
};

function createBaseUserClientDto(): UserClientDto {
  return {
    userDto: undefined,
    meta: undefined,
    canApproveSheetMusic: false,
    has2faEnabled: false,
    isOAuthAccount: false,
  };
}

export const UserClientDto = {
  encode(message: UserClientDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userDto !== undefined) {
      UserDto.encode(message.userDto, writer.uint32(10).fork()).ldelim();
    }
    if (message.meta !== undefined) {
      ClientMetaDto.encode(message.meta, writer.uint32(18).fork()).ldelim();
    }
    if (message.canApproveSheetMusic !== false) {
      writer.uint32(24).bool(message.canApproveSheetMusic);
    }
    if (message.has2faEnabled !== false) {
      writer.uint32(32).bool(message.has2faEnabled);
    }
    if (message.isOAuthAccount !== false) {
      writer.uint32(40).bool(message.isOAuthAccount);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserClientDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserClientDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userDto = UserDto.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.meta = ClientMetaDto.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.canApproveSheetMusic = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.has2faEnabled = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.isOAuthAccount = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserClientDto {
    return {
      userDto: isSet(object.userDto) ? UserDto.fromJSON(object.userDto) : undefined,
      meta: isSet(object.meta) ? ClientMetaDto.fromJSON(object.meta) : undefined,
      canApproveSheetMusic: isSet(object.canApproveSheetMusic)
        ? globalThis.Boolean(object.canApproveSheetMusic)
        : false,
      has2faEnabled: isSet(object.has2faEnabled) ? globalThis.Boolean(object.has2faEnabled) : false,
      isOAuthAccount: isSet(object.isOAuthAccount) ? globalThis.Boolean(object.isOAuthAccount) : false,
    };
  },

  toJSON(message: UserClientDto): unknown {
    const obj: any = {};
    if (message.userDto !== undefined) {
      obj.userDto = UserDto.toJSON(message.userDto);
    }
    if (message.meta !== undefined) {
      obj.meta = ClientMetaDto.toJSON(message.meta);
    }
    if (message.canApproveSheetMusic !== false) {
      obj.canApproveSheetMusic = message.canApproveSheetMusic;
    }
    if (message.has2faEnabled !== false) {
      obj.has2faEnabled = message.has2faEnabled;
    }
    if (message.isOAuthAccount !== false) {
      obj.isOAuthAccount = message.isOAuthAccount;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserClientDto>, I>>(base?: I): UserClientDto {
    return UserClientDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserClientDto>, I>>(object: I): UserClientDto {
    const message = createBaseUserClientDto();
    message.userDto = (object.userDto !== undefined && object.userDto !== null)
      ? UserDto.fromPartial(object.userDto)
      : undefined;
    message.meta = (object.meta !== undefined && object.meta !== null)
      ? ClientMetaDto.fromPartial(object.meta)
      : undefined;
    message.canApproveSheetMusic = object.canApproveSheetMusic ?? false;
    message.has2faEnabled = object.has2faEnabled ?? false;
    message.isOAuthAccount = object.isOAuthAccount ?? false;
    return message;
  },
};

function createBaseClientSideUserDto(): ClientSideUserDto {
  return {
    userDto: undefined,
    localNotesMuted: false,
    localChatMuted: false,
    clientMetaDetailsParsed: undefined,
    socketID: "",
    socketIDHashed: undefined,
  };
}

export const ClientSideUserDto = {
  encode(message: ClientSideUserDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userDto !== undefined) {
      UserDto.encode(message.userDto, writer.uint32(10).fork()).ldelim();
    }
    if (message.localNotesMuted !== false) {
      writer.uint32(16).bool(message.localNotesMuted);
    }
    if (message.localChatMuted !== false) {
      writer.uint32(24).bool(message.localChatMuted);
    }
    if (message.clientMetaDetailsParsed !== undefined) {
      writer.uint32(34).string(message.clientMetaDetailsParsed);
    }
    if (message.socketID !== "") {
      writer.uint32(42).string(message.socketID);
    }
    if (message.socketIDHashed !== undefined) {
      writer.uint32(48).uint32(message.socketIDHashed);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ClientSideUserDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientSideUserDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userDto = UserDto.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.localNotesMuted = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.localChatMuted = reader.bool();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.clientMetaDetailsParsed = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.socketIDHashed = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClientSideUserDto {
    return {
      userDto: isSet(object.userDto) ? UserDto.fromJSON(object.userDto) : undefined,
      localNotesMuted: isSet(object.localNotesMuted) ? globalThis.Boolean(object.localNotesMuted) : false,
      localChatMuted: isSet(object.localChatMuted) ? globalThis.Boolean(object.localChatMuted) : false,
      clientMetaDetailsParsed: isSet(object.clientMetaDetailsParsed)
        ? globalThis.String(object.clientMetaDetailsParsed)
        : undefined,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      socketIDHashed: isSet(object.socketIDHashed) ? globalThis.Number(object.socketIDHashed) : undefined,
    };
  },

  toJSON(message: ClientSideUserDto): unknown {
    const obj: any = {};
    if (message.userDto !== undefined) {
      obj.userDto = UserDto.toJSON(message.userDto);
    }
    if (message.localNotesMuted !== false) {
      obj.localNotesMuted = message.localNotesMuted;
    }
    if (message.localChatMuted !== false) {
      obj.localChatMuted = message.localChatMuted;
    }
    if (message.clientMetaDetailsParsed !== undefined) {
      obj.clientMetaDetailsParsed = message.clientMetaDetailsParsed;
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.socketIDHashed !== undefined) {
      obj.socketIDHashed = Math.round(message.socketIDHashed);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClientSideUserDto>, I>>(base?: I): ClientSideUserDto {
    return ClientSideUserDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientSideUserDto>, I>>(object: I): ClientSideUserDto {
    const message = createBaseClientSideUserDto();
    message.userDto = (object.userDto !== undefined && object.userDto !== null)
      ? UserDto.fromPartial(object.userDto)
      : undefined;
    message.localNotesMuted = object.localNotesMuted ?? false;
    message.localChatMuted = object.localChatMuted ?? false;
    message.clientMetaDetailsParsed = object.clientMetaDetailsParsed ?? undefined;
    message.socketID = object.socketID ?? "";
    message.socketIDHashed = object.socketIDHashed ?? undefined;
    return message;
  },
};

function createBaseUserUpdateCommand(): UserUpdateCommand {
  return {
    socketID: "",
    userColor: undefined,
    userStatus: undefined,
    clientMetaDetails: undefined,
    profileDescription: undefined,
    selfMuted: undefined,
    statusText: undefined,
    nickname: undefined,
    profileImageUpdated: undefined,
    profileBackgroundImageUpdated: undefined,
    profileImageCleared: undefined,
    profileBackgroundImageCleared: undefined,
    orchestraModel: undefined,
    characterModel: undefined,
    serverNotesMuted: undefined,
    serverChatMuted: undefined,
    avatarWorldPosition: undefined,
    avatarPianoBenchSeat: undefined,
    badges: [],
  };
}

export const UserUpdateCommand = {
  encode(message: UserUpdateCommand, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.socketID !== "") {
      writer.uint32(10).string(message.socketID);
    }
    if (message.userColor !== undefined) {
      writer.uint32(18).string(message.userColor);
    }
    if (message.userStatus !== undefined) {
      writer.uint32(24).int32(message.userStatus);
    }
    if (message.clientMetaDetails !== undefined) {
      writer.uint32(34).string(message.clientMetaDetails);
    }
    if (message.profileDescription !== undefined) {
      writer.uint32(42).string(message.profileDescription);
    }
    if (message.selfMuted !== undefined) {
      writer.uint32(48).bool(message.selfMuted);
    }
    if (message.statusText !== undefined) {
      writer.uint32(58).string(message.statusText);
    }
    if (message.nickname !== undefined) {
      writer.uint32(66).string(message.nickname);
    }
    if (message.profileImageUpdated !== undefined) {
      writer.uint32(74).string(message.profileImageUpdated);
    }
    if (message.profileBackgroundImageUpdated !== undefined) {
      writer.uint32(82).string(message.profileBackgroundImageUpdated);
    }
    if (message.profileImageCleared !== undefined) {
      writer.uint32(88).bool(message.profileImageCleared);
    }
    if (message.profileBackgroundImageCleared !== undefined) {
      writer.uint32(96).bool(message.profileBackgroundImageCleared);
    }
    if (message.orchestraModel !== undefined) {
      writer.uint32(106).string(message.orchestraModel);
    }
    if (message.characterModel !== undefined) {
      writer.uint32(114).string(message.characterModel);
    }
    if (message.serverNotesMuted !== undefined) {
      writer.uint32(120).bool(message.serverNotesMuted);
    }
    if (message.serverChatMuted !== undefined) {
      writer.uint32(128).bool(message.serverChatMuted);
    }
    if (message.avatarWorldPosition !== undefined) {
      AvatarWorldDataDto_AvatarMessageWorldPosition.encode(message.avatarWorldPosition, writer.uint32(138).fork())
        .ldelim();
    }
    if (message.avatarPianoBenchSeat !== undefined) {
      writer.uint32(144).int32(message.avatarPianoBenchSeat);
    }
    for (const v of message.badges) {
      UserBadges.encode(v!, writer.uint32(402).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserUpdateCommand {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserUpdateCommand();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.userColor = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.userStatus = reader.int32() as any;
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.clientMetaDetails = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.profileDescription = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.selfMuted = reader.bool();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.statusText = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.nickname = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.profileImageUpdated = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.profileBackgroundImageUpdated = reader.string();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.profileImageCleared = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.profileBackgroundImageCleared = reader.bool();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.orchestraModel = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.characterModel = reader.string();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.serverNotesMuted = reader.bool();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.serverChatMuted = reader.bool();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.avatarWorldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.decode(reader, reader.uint32());
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.avatarPianoBenchSeat = reader.int32();
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.badges.push(UserBadges.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserUpdateCommand {
    return {
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      userColor: isSet(object.userColor) ? globalThis.String(object.userColor) : undefined,
      userStatus: isSet(object.userStatus) ? userStatusFromJSON(object.userStatus) : undefined,
      clientMetaDetails: isSet(object.clientMetaDetails) ? globalThis.String(object.clientMetaDetails) : undefined,
      profileDescription: isSet(object.profileDescription) ? globalThis.String(object.profileDescription) : undefined,
      selfMuted: isSet(object.selfMuted) ? globalThis.Boolean(object.selfMuted) : undefined,
      statusText: isSet(object.statusText) ? globalThis.String(object.statusText) : undefined,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : undefined,
      profileImageUpdated: isSet(object.profileImageUpdated)
        ? globalThis.String(object.profileImageUpdated)
        : undefined,
      profileBackgroundImageUpdated: isSet(object.profileBackgroundImageUpdated)
        ? globalThis.String(object.profileBackgroundImageUpdated)
        : undefined,
      profileImageCleared: isSet(object.profileImageCleared)
        ? globalThis.Boolean(object.profileImageCleared)
        : undefined,
      profileBackgroundImageCleared: isSet(object.profileBackgroundImageCleared)
        ? globalThis.Boolean(object.profileBackgroundImageCleared)
        : undefined,
      orchestraModel: isSet(object.orchestraModel) ? globalThis.String(object.orchestraModel) : undefined,
      characterModel: isSet(object.characterModel) ? globalThis.String(object.characterModel) : undefined,
      serverNotesMuted: isSet(object.serverNotesMuted) ? globalThis.Boolean(object.serverNotesMuted) : undefined,
      serverChatMuted: isSet(object.serverChatMuted) ? globalThis.Boolean(object.serverChatMuted) : undefined,
      avatarWorldPosition: isSet(object.avatarWorldPosition)
        ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromJSON(object.avatarWorldPosition)
        : undefined,
      avatarPianoBenchSeat: isSet(object.avatarPianoBenchSeat)
        ? globalThis.Number(object.avatarPianoBenchSeat)
        : undefined,
      badges: globalThis.Array.isArray(object?.badges) ? object.badges.map((e: any) => UserBadges.fromJSON(e)) : [],
    };
  },

  toJSON(message: UserUpdateCommand): unknown {
    const obj: any = {};
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.userColor !== undefined) {
      obj.userColor = message.userColor;
    }
    if (message.userStatus !== undefined) {
      obj.userStatus = userStatusToJSON(message.userStatus);
    }
    if (message.clientMetaDetails !== undefined) {
      obj.clientMetaDetails = message.clientMetaDetails;
    }
    if (message.profileDescription !== undefined) {
      obj.profileDescription = message.profileDescription;
    }
    if (message.selfMuted !== undefined) {
      obj.selfMuted = message.selfMuted;
    }
    if (message.statusText !== undefined) {
      obj.statusText = message.statusText;
    }
    if (message.nickname !== undefined) {
      obj.nickname = message.nickname;
    }
    if (message.profileImageUpdated !== undefined) {
      obj.profileImageUpdated = message.profileImageUpdated;
    }
    if (message.profileBackgroundImageUpdated !== undefined) {
      obj.profileBackgroundImageUpdated = message.profileBackgroundImageUpdated;
    }
    if (message.profileImageCleared !== undefined) {
      obj.profileImageCleared = message.profileImageCleared;
    }
    if (message.profileBackgroundImageCleared !== undefined) {
      obj.profileBackgroundImageCleared = message.profileBackgroundImageCleared;
    }
    if (message.orchestraModel !== undefined) {
      obj.orchestraModel = message.orchestraModel;
    }
    if (message.characterModel !== undefined) {
      obj.characterModel = message.characterModel;
    }
    if (message.serverNotesMuted !== undefined) {
      obj.serverNotesMuted = message.serverNotesMuted;
    }
    if (message.serverChatMuted !== undefined) {
      obj.serverChatMuted = message.serverChatMuted;
    }
    if (message.avatarWorldPosition !== undefined) {
      obj.avatarWorldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.toJSON(message.avatarWorldPosition);
    }
    if (message.avatarPianoBenchSeat !== undefined) {
      obj.avatarPianoBenchSeat = Math.round(message.avatarPianoBenchSeat);
    }
    if (message.badges?.length) {
      obj.badges = message.badges.map((e) => UserBadges.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserUpdateCommand>, I>>(base?: I): UserUpdateCommand {
    return UserUpdateCommand.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserUpdateCommand>, I>>(object: I): UserUpdateCommand {
    const message = createBaseUserUpdateCommand();
    message.socketID = object.socketID ?? "";
    message.userColor = object.userColor ?? undefined;
    message.userStatus = object.userStatus ?? undefined;
    message.clientMetaDetails = object.clientMetaDetails ?? undefined;
    message.profileDescription = object.profileDescription ?? undefined;
    message.selfMuted = object.selfMuted ?? undefined;
    message.statusText = object.statusText ?? undefined;
    message.nickname = object.nickname ?? undefined;
    message.profileImageUpdated = object.profileImageUpdated ?? undefined;
    message.profileBackgroundImageUpdated = object.profileBackgroundImageUpdated ?? undefined;
    message.profileImageCleared = object.profileImageCleared ?? undefined;
    message.profileBackgroundImageCleared = object.profileBackgroundImageCleared ?? undefined;
    message.orchestraModel = object.orchestraModel ?? undefined;
    message.characterModel = object.characterModel ?? undefined;
    message.serverNotesMuted = object.serverNotesMuted ?? undefined;
    message.serverChatMuted = object.serverChatMuted ?? undefined;
    message.avatarWorldPosition = (object.avatarWorldPosition !== undefined && object.avatarWorldPosition !== null)
      ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(object.avatarWorldPosition)
      : undefined;
    message.avatarPianoBenchSeat = object.avatarPianoBenchSeat ?? undefined;
    message.badges = object.badges?.map((e) => UserBadges.fromPartial(e)) || [];
    return message;
  },
};

function createBaseFriendDto(): FriendDto {
  return {
    username: "",
    usertag: "",
    socketID: undefined,
    roomID: undefined,
    roles: [],
    nickname: undefined,
    color: undefined,
    statusText: undefined,
    profileDescription: undefined,
    becameFriendsDate: undefined,
    profileImageLastModified: undefined,
    profileBackgroundImageLastModified: undefined,
    lastOnline: undefined,
    onlineStatus: undefined,
  };
}

export const FriendDto = {
  encode(message: FriendDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.usertag !== "") {
      writer.uint32(18).string(message.usertag);
    }
    if (message.socketID !== undefined) {
      writer.uint32(26).string(message.socketID);
    }
    if (message.roomID !== undefined) {
      writer.uint32(34).string(message.roomID);
    }
    writer.uint32(42).fork();
    for (const v of message.roles) {
      writer.int32(v);
    }
    writer.ldelim();
    if (message.nickname !== undefined) {
      writer.uint32(50).string(message.nickname);
    }
    if (message.color !== undefined) {
      writer.uint32(58).string(message.color);
    }
    if (message.statusText !== undefined) {
      writer.uint32(66).string(message.statusText);
    }
    if (message.profileDescription !== undefined) {
      writer.uint32(74).string(message.profileDescription);
    }
    if (message.becameFriendsDate !== undefined) {
      writer.uint32(82).string(message.becameFriendsDate);
    }
    if (message.profileImageLastModified !== undefined) {
      writer.uint32(90).string(message.profileImageLastModified);
    }
    if (message.profileBackgroundImageLastModified !== undefined) {
      writer.uint32(98).string(message.profileBackgroundImageLastModified);
    }
    if (message.lastOnline !== undefined) {
      writer.uint32(106).string(message.lastOnline);
    }
    if (message.onlineStatus !== undefined) {
      writer.uint32(112).int32(message.onlineStatus);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FriendDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 5:
          if (tag === 40) {
            message.roles.push(reader.int32() as any);

            continue;
          }

          if (tag === 42) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.roles.push(reader.int32() as any);
            }

            continue;
          }

          break;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.nickname = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.color = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.statusText = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.profileDescription = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.becameFriendsDate = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.profileImageLastModified = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.profileBackgroundImageLastModified = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.lastOnline = reader.string();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.onlineStatus = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FriendDto {
    return {
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : undefined,
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : undefined,
      roles: globalThis.Array.isArray(object?.roles) ? object.roles.map((e: any) => rolesFromJSON(e)) : [],
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : undefined,
      color: isSet(object.color) ? globalThis.String(object.color) : undefined,
      statusText: isSet(object.statusText) ? globalThis.String(object.statusText) : undefined,
      profileDescription: isSet(object.profileDescription) ? globalThis.String(object.profileDescription) : undefined,
      becameFriendsDate: isSet(object.becameFriendsDate) ? globalThis.String(object.becameFriendsDate) : undefined,
      profileImageLastModified: isSet(object.profileImageLastModified)
        ? globalThis.String(object.profileImageLastModified)
        : undefined,
      profileBackgroundImageLastModified: isSet(object.profileBackgroundImageLastModified)
        ? globalThis.String(object.profileBackgroundImageLastModified)
        : undefined,
      lastOnline: isSet(object.lastOnline) ? globalThis.String(object.lastOnline) : undefined,
      onlineStatus: isSet(object.onlineStatus) ? friendDto_FriendOnlineStatusFromJSON(object.onlineStatus) : undefined,
    };
  },

  toJSON(message: FriendDto): unknown {
    const obj: any = {};
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.socketID !== undefined) {
      obj.socketID = message.socketID;
    }
    if (message.roomID !== undefined) {
      obj.roomID = message.roomID;
    }
    if (message.roles?.length) {
      obj.roles = message.roles.map((e) => rolesToJSON(e));
    }
    if (message.nickname !== undefined) {
      obj.nickname = message.nickname;
    }
    if (message.color !== undefined) {
      obj.color = message.color;
    }
    if (message.statusText !== undefined) {
      obj.statusText = message.statusText;
    }
    if (message.profileDescription !== undefined) {
      obj.profileDescription = message.profileDescription;
    }
    if (message.becameFriendsDate !== undefined) {
      obj.becameFriendsDate = message.becameFriendsDate;
    }
    if (message.profileImageLastModified !== undefined) {
      obj.profileImageLastModified = message.profileImageLastModified;
    }
    if (message.profileBackgroundImageLastModified !== undefined) {
      obj.profileBackgroundImageLastModified = message.profileBackgroundImageLastModified;
    }
    if (message.lastOnline !== undefined) {
      obj.lastOnline = message.lastOnline;
    }
    if (message.onlineStatus !== undefined) {
      obj.onlineStatus = friendDto_FriendOnlineStatusToJSON(message.onlineStatus);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FriendDto>, I>>(base?: I): FriendDto {
    return FriendDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendDto>, I>>(object: I): FriendDto {
    const message = createBaseFriendDto();
    message.username = object.username ?? "";
    message.usertag = object.usertag ?? "";
    message.socketID = object.socketID ?? undefined;
    message.roomID = object.roomID ?? undefined;
    message.roles = object.roles?.map((e) => e) || [];
    message.nickname = object.nickname ?? undefined;
    message.color = object.color ?? undefined;
    message.statusText = object.statusText ?? undefined;
    message.profileDescription = object.profileDescription ?? undefined;
    message.becameFriendsDate = object.becameFriendsDate ?? undefined;
    message.profileImageLastModified = object.profileImageLastModified ?? undefined;
    message.profileBackgroundImageLastModified = object.profileBackgroundImageLastModified ?? undefined;
    message.lastOnline = object.lastOnline ?? undefined;
    message.onlineStatus = object.onlineStatus ?? undefined;
    return message;
  },
};

function createBasePendingFriendRequest(): PendingFriendRequest {
  return { usertag: "", uuid: "", createdDate: "" };
}

export const PendingFriendRequest = {
  encode(message: PendingFriendRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.usertag !== "") {
      writer.uint32(10).string(message.usertag);
    }
    if (message.uuid !== "") {
      writer.uint32(18).string(message.uuid);
    }
    if (message.createdDate !== "") {
      writer.uint32(26).string(message.createdDate);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PendingFriendRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePendingFriendRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.uuid = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.createdDate = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PendingFriendRequest {
    return {
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      uuid: isSet(object.uuid) ? globalThis.String(object.uuid) : "",
      createdDate: isSet(object.createdDate) ? globalThis.String(object.createdDate) : "",
    };
  },

  toJSON(message: PendingFriendRequest): unknown {
    const obj: any = {};
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.uuid !== "") {
      obj.uuid = message.uuid;
    }
    if (message.createdDate !== "") {
      obj.createdDate = message.createdDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PendingFriendRequest>, I>>(base?: I): PendingFriendRequest {
    return PendingFriendRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PendingFriendRequest>, I>>(object: I): PendingFriendRequest {
    const message = createBasePendingFriendRequest();
    message.usertag = object.usertag ?? "";
    message.uuid = object.uuid ?? "";
    message.createdDate = object.createdDate ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
