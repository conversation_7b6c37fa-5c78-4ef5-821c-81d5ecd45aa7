// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: midi-renditions.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";

export const protobufPackage = "PianoRhythm.Serialization.Midi.Msgs";

export enum MidiDtoType {
  Invalid = 0,
  NoteOn = 1,
  NoteOff = 2,
  Sustain = 3,
  AllSoundOff = 4,
  PitchBend = 5,
  UNRECOGNIZED = -1,
}

export function midiDtoTypeFromJSON(object: any): MidiDtoType {
  switch (object) {
    case 0:
    case "Invalid":
      return MidiDtoType.Invalid;
    case 1:
    case "NoteOn":
      return MidiDtoType.NoteOn;
    case 2:
    case "NoteOff":
      return MidiDtoType.NoteOff;
    case 3:
    case "Sustain":
      return MidiDtoType.Sustain;
    case 4:
    case "AllSoundOff":
      return MidiDtoType.AllSoundOff;
    case 5:
    case "PitchBend":
      return MidiDtoType.PitchBend;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MidiDtoType.UNRECOGNIZED;
  }
}

export function midiDtoTypeToJSON(object: MidiDtoType): string {
  switch (object) {
    case MidiDtoType.Invalid:
      return "Invalid";
    case MidiDtoType.NoteOn:
      return "NoteOn";
    case MidiDtoType.NoteOff:
      return "NoteOff";
    case MidiDtoType.Sustain:
      return "Sustain";
    case MidiDtoType.AllSoundOff:
      return "AllSoundOff";
    case MidiDtoType.PitchBend:
      return "PitchBend";
    case MidiDtoType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum MidiNoteSource {
  IGNORED = 0,
  KEYBOARD = 1,
  MOUSE = 2,
  MIDI = 3,
  MIDI_PLAYER_PREVIEW = 4,
  MIDI_PLAYER = 5,
  UNRECOGNIZED = -1,
}

export function midiNoteSourceFromJSON(object: any): MidiNoteSource {
  switch (object) {
    case 0:
    case "IGNORED":
      return MidiNoteSource.IGNORED;
    case 1:
    case "KEYBOARD":
      return MidiNoteSource.KEYBOARD;
    case 2:
    case "MOUSE":
      return MidiNoteSource.MOUSE;
    case 3:
    case "MIDI":
      return MidiNoteSource.MIDI;
    case 4:
    case "MIDI_PLAYER_PREVIEW":
      return MidiNoteSource.MIDI_PLAYER_PREVIEW;
    case 5:
    case "MIDI_PLAYER":
      return MidiNoteSource.MIDI_PLAYER;
    case -1:
    case "UNRECOGNIZED":
    default:
      return MidiNoteSource.UNRECOGNIZED;
  }
}

export function midiNoteSourceToJSON(object: MidiNoteSource): string {
  switch (object) {
    case MidiNoteSource.IGNORED:
      return "IGNORED";
    case MidiNoteSource.KEYBOARD:
      return "KEYBOARD";
    case MidiNoteSource.MOUSE:
      return "MOUSE";
    case MidiNoteSource.MIDI:
      return "MIDI";
    case MidiNoteSource.MIDI_PLAYER_PREVIEW:
      return "MIDI_PLAYER_PREVIEW";
    case MidiNoteSource.MIDI_PLAYER:
      return "MIDI_PLAYER";
    case MidiNoteSource.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ActiveChannelsMode {
  SINGLE = 0,
  MULTI = 1,
  ALL = 2,
  SPLIT2 = 3,
  SPLIT4 = 4,
  SPLIT8 = 5,
  UNRECOGNIZED = -1,
}

export function activeChannelsModeFromJSON(object: any): ActiveChannelsMode {
  switch (object) {
    case 0:
    case "SINGLE":
      return ActiveChannelsMode.SINGLE;
    case 1:
    case "MULTI":
      return ActiveChannelsMode.MULTI;
    case 2:
    case "ALL":
      return ActiveChannelsMode.ALL;
    case 3:
    case "SPLIT2":
      return ActiveChannelsMode.SPLIT2;
    case 4:
    case "SPLIT4":
      return ActiveChannelsMode.SPLIT4;
    case 5:
    case "SPLIT8":
      return ActiveChannelsMode.SPLIT8;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ActiveChannelsMode.UNRECOGNIZED;
  }
}

export function activeChannelsModeToJSON(object: ActiveChannelsMode): string {
  switch (object) {
    case ActiveChannelsMode.SINGLE:
      return "SINGLE";
    case ActiveChannelsMode.MULTI:
      return "MULTI";
    case ActiveChannelsMode.ALL:
      return "ALL";
    case ActiveChannelsMode.SPLIT2:
      return "SPLIT2";
    case ActiveChannelsMode.SPLIT4:
      return "SPLIT4";
    case ActiveChannelsMode.SPLIT8:
      return "SPLIT8";
    case ActiveChannelsMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AudioServiceNoteActivityNoteType {
  ON = 0,
  OFF = 1,
  UNRECOGNIZED = -1,
}

export function audioServiceNoteActivityNoteTypeFromJSON(object: any): AudioServiceNoteActivityNoteType {
  switch (object) {
    case 0:
    case "ON":
      return AudioServiceNoteActivityNoteType.ON;
    case 1:
    case "OFF":
      return AudioServiceNoteActivityNoteType.OFF;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AudioServiceNoteActivityNoteType.UNRECOGNIZED;
  }
}

export function audioServiceNoteActivityNoteTypeToJSON(object: AudioServiceNoteActivityNoteType): string {
  switch (object) {
    case AudioServiceNoteActivityNoteType.ON:
      return "ON";
    case AudioServiceNoteActivityNoteType.OFF:
      return "OFF";
    case AudioServiceNoteActivityNoteType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum SetChannelInstrumentType {
  Add = 0,
  NextInactive = 1,
  NextEmpty = 2,
  UNRECOGNIZED = -1,
}

export function setChannelInstrumentTypeFromJSON(object: any): SetChannelInstrumentType {
  switch (object) {
    case 0:
    case "Add":
      return SetChannelInstrumentType.Add;
    case 1:
    case "NextInactive":
      return SetChannelInstrumentType.NextInactive;
    case 2:
    case "NextEmpty":
      return SetChannelInstrumentType.NextEmpty;
    case -1:
    case "UNRECOGNIZED":
    default:
      return SetChannelInstrumentType.UNRECOGNIZED;
  }
}

export function setChannelInstrumentTypeToJSON(object: SetChannelInstrumentType): string {
  switch (object) {
    case SetChannelInstrumentType.Add:
      return "Add";
    case SetChannelInstrumentType.NextInactive:
      return "NextInactive";
    case SetChannelInstrumentType.NextEmpty:
      return "NextEmpty";
    case SetChannelInstrumentType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum PianoRhythmSynthEventName {
  NOTE_ON = 0,
  NOTE_OFF = 1,
  PROGRAM_CHANGE = 2,
  CHANNEL_PRESSURE = 3,
  SYSTEM_RESET = 4,
  POLYPHONIC_KEY_PRESSURE = 5,
  ALL_NOTES_OFF = 6,
  ALL_SOUND_OFF = 7,
  OMNI_MODE_OFF = 8,
  MONO_MODE_ON = 9,
  POLY_MODE_ON = 10,
  OMNI_MODE_ON = 11,
  MAIN_VOLUME_MSB = 12,
  MAIN_VOLUME_LSB = 13,
  FOOT_CONTROLLER_MSB = 14,
  MODULATION_MSB = 15,
  MODULATION_LSB = 16,
  BANK_SELECT_MSB = 17,
  BANK_SELECT_LSB = 18,
  FOOT_CONTROLLER_LSB = 19,
  RESET_ALL_CONTROLLERS = 20,
  PAN_MSB = 21,
  PAN_LSB = 22,
  DAMPER_PEDAL = 23,
  PORTAMENTO = 24,
  SUSTENUTO = 25,
  SOFT_PEDAL = 26,
  LEGATO_FOOTSWITCH = 27,
  HOLD_2 = 28,
  EFFECTS_1_DEPTH = 29,
  TREMELO_EFFECT = 30,
  CHORUS_EFFECT = 31,
  CELESTE_EFFECT = 32,
  PHASER_EFFECT = 33,
  PITCH_BEND = 34,
  CONTROL_CHANGE = 35,
  SOCKET_USER_GAIN_CHANGE = 36,
  UNKNOWN = 37,
  UNRECOGNIZED = -1,
}

export function pianoRhythmSynthEventNameFromJSON(object: any): PianoRhythmSynthEventName {
  switch (object) {
    case 0:
    case "NOTE_ON":
      return PianoRhythmSynthEventName.NOTE_ON;
    case 1:
    case "NOTE_OFF":
      return PianoRhythmSynthEventName.NOTE_OFF;
    case 2:
    case "PROGRAM_CHANGE":
      return PianoRhythmSynthEventName.PROGRAM_CHANGE;
    case 3:
    case "CHANNEL_PRESSURE":
      return PianoRhythmSynthEventName.CHANNEL_PRESSURE;
    case 4:
    case "SYSTEM_RESET":
      return PianoRhythmSynthEventName.SYSTEM_RESET;
    case 5:
    case "POLYPHONIC_KEY_PRESSURE":
      return PianoRhythmSynthEventName.POLYPHONIC_KEY_PRESSURE;
    case 6:
    case "ALL_NOTES_OFF":
      return PianoRhythmSynthEventName.ALL_NOTES_OFF;
    case 7:
    case "ALL_SOUND_OFF":
      return PianoRhythmSynthEventName.ALL_SOUND_OFF;
    case 8:
    case "OMNI_MODE_OFF":
      return PianoRhythmSynthEventName.OMNI_MODE_OFF;
    case 9:
    case "MONO_MODE_ON":
      return PianoRhythmSynthEventName.MONO_MODE_ON;
    case 10:
    case "POLY_MODE_ON":
      return PianoRhythmSynthEventName.POLY_MODE_ON;
    case 11:
    case "OMNI_MODE_ON":
      return PianoRhythmSynthEventName.OMNI_MODE_ON;
    case 12:
    case "MAIN_VOLUME_MSB":
      return PianoRhythmSynthEventName.MAIN_VOLUME_MSB;
    case 13:
    case "MAIN_VOLUME_LSB":
      return PianoRhythmSynthEventName.MAIN_VOLUME_LSB;
    case 14:
    case "FOOT_CONTROLLER_MSB":
      return PianoRhythmSynthEventName.FOOT_CONTROLLER_MSB;
    case 15:
    case "MODULATION_MSB":
      return PianoRhythmSynthEventName.MODULATION_MSB;
    case 16:
    case "MODULATION_LSB":
      return PianoRhythmSynthEventName.MODULATION_LSB;
    case 17:
    case "BANK_SELECT_MSB":
      return PianoRhythmSynthEventName.BANK_SELECT_MSB;
    case 18:
    case "BANK_SELECT_LSB":
      return PianoRhythmSynthEventName.BANK_SELECT_LSB;
    case 19:
    case "FOOT_CONTROLLER_LSB":
      return PianoRhythmSynthEventName.FOOT_CONTROLLER_LSB;
    case 20:
    case "RESET_ALL_CONTROLLERS":
      return PianoRhythmSynthEventName.RESET_ALL_CONTROLLERS;
    case 21:
    case "PAN_MSB":
      return PianoRhythmSynthEventName.PAN_MSB;
    case 22:
    case "PAN_LSB":
      return PianoRhythmSynthEventName.PAN_LSB;
    case 23:
    case "DAMPER_PEDAL":
      return PianoRhythmSynthEventName.DAMPER_PEDAL;
    case 24:
    case "PORTAMENTO":
      return PianoRhythmSynthEventName.PORTAMENTO;
    case 25:
    case "SUSTENUTO":
      return PianoRhythmSynthEventName.SUSTENUTO;
    case 26:
    case "SOFT_PEDAL":
      return PianoRhythmSynthEventName.SOFT_PEDAL;
    case 27:
    case "LEGATO_FOOTSWITCH":
      return PianoRhythmSynthEventName.LEGATO_FOOTSWITCH;
    case 28:
    case "HOLD_2":
      return PianoRhythmSynthEventName.HOLD_2;
    case 29:
    case "EFFECTS_1_DEPTH":
      return PianoRhythmSynthEventName.EFFECTS_1_DEPTH;
    case 30:
    case "TREMELO_EFFECT":
      return PianoRhythmSynthEventName.TREMELO_EFFECT;
    case 31:
    case "CHORUS_EFFECT":
      return PianoRhythmSynthEventName.CHORUS_EFFECT;
    case 32:
    case "CELESTE_EFFECT":
      return PianoRhythmSynthEventName.CELESTE_EFFECT;
    case 33:
    case "PHASER_EFFECT":
      return PianoRhythmSynthEventName.PHASER_EFFECT;
    case 34:
    case "PITCH_BEND":
      return PianoRhythmSynthEventName.PITCH_BEND;
    case 35:
    case "CONTROL_CHANGE":
      return PianoRhythmSynthEventName.CONTROL_CHANGE;
    case 36:
    case "SOCKET_USER_GAIN_CHANGE":
      return PianoRhythmSynthEventName.SOCKET_USER_GAIN_CHANGE;
    case 37:
    case "UNKNOWN":
      return PianoRhythmSynthEventName.UNKNOWN;
    case -1:
    case "UNRECOGNIZED":
    default:
      return PianoRhythmSynthEventName.UNRECOGNIZED;
  }
}

export function pianoRhythmSynthEventNameToJSON(object: PianoRhythmSynthEventName): string {
  switch (object) {
    case PianoRhythmSynthEventName.NOTE_ON:
      return "NOTE_ON";
    case PianoRhythmSynthEventName.NOTE_OFF:
      return "NOTE_OFF";
    case PianoRhythmSynthEventName.PROGRAM_CHANGE:
      return "PROGRAM_CHANGE";
    case PianoRhythmSynthEventName.CHANNEL_PRESSURE:
      return "CHANNEL_PRESSURE";
    case PianoRhythmSynthEventName.SYSTEM_RESET:
      return "SYSTEM_RESET";
    case PianoRhythmSynthEventName.POLYPHONIC_KEY_PRESSURE:
      return "POLYPHONIC_KEY_PRESSURE";
    case PianoRhythmSynthEventName.ALL_NOTES_OFF:
      return "ALL_NOTES_OFF";
    case PianoRhythmSynthEventName.ALL_SOUND_OFF:
      return "ALL_SOUND_OFF";
    case PianoRhythmSynthEventName.OMNI_MODE_OFF:
      return "OMNI_MODE_OFF";
    case PianoRhythmSynthEventName.MONO_MODE_ON:
      return "MONO_MODE_ON";
    case PianoRhythmSynthEventName.POLY_MODE_ON:
      return "POLY_MODE_ON";
    case PianoRhythmSynthEventName.OMNI_MODE_ON:
      return "OMNI_MODE_ON";
    case PianoRhythmSynthEventName.MAIN_VOLUME_MSB:
      return "MAIN_VOLUME_MSB";
    case PianoRhythmSynthEventName.MAIN_VOLUME_LSB:
      return "MAIN_VOLUME_LSB";
    case PianoRhythmSynthEventName.FOOT_CONTROLLER_MSB:
      return "FOOT_CONTROLLER_MSB";
    case PianoRhythmSynthEventName.MODULATION_MSB:
      return "MODULATION_MSB";
    case PianoRhythmSynthEventName.MODULATION_LSB:
      return "MODULATION_LSB";
    case PianoRhythmSynthEventName.BANK_SELECT_MSB:
      return "BANK_SELECT_MSB";
    case PianoRhythmSynthEventName.BANK_SELECT_LSB:
      return "BANK_SELECT_LSB";
    case PianoRhythmSynthEventName.FOOT_CONTROLLER_LSB:
      return "FOOT_CONTROLLER_LSB";
    case PianoRhythmSynthEventName.RESET_ALL_CONTROLLERS:
      return "RESET_ALL_CONTROLLERS";
    case PianoRhythmSynthEventName.PAN_MSB:
      return "PAN_MSB";
    case PianoRhythmSynthEventName.PAN_LSB:
      return "PAN_LSB";
    case PianoRhythmSynthEventName.DAMPER_PEDAL:
      return "DAMPER_PEDAL";
    case PianoRhythmSynthEventName.PORTAMENTO:
      return "PORTAMENTO";
    case PianoRhythmSynthEventName.SUSTENUTO:
      return "SUSTENUTO";
    case PianoRhythmSynthEventName.SOFT_PEDAL:
      return "SOFT_PEDAL";
    case PianoRhythmSynthEventName.LEGATO_FOOTSWITCH:
      return "LEGATO_FOOTSWITCH";
    case PianoRhythmSynthEventName.HOLD_2:
      return "HOLD_2";
    case PianoRhythmSynthEventName.EFFECTS_1_DEPTH:
      return "EFFECTS_1_DEPTH";
    case PianoRhythmSynthEventName.TREMELO_EFFECT:
      return "TREMELO_EFFECT";
    case PianoRhythmSynthEventName.CHORUS_EFFECT:
      return "CHORUS_EFFECT";
    case PianoRhythmSynthEventName.CELESTE_EFFECT:
      return "CELESTE_EFFECT";
    case PianoRhythmSynthEventName.PHASER_EFFECT:
      return "PHASER_EFFECT";
    case PianoRhythmSynthEventName.PITCH_BEND:
      return "PITCH_BEND";
    case PianoRhythmSynthEventName.CONTROL_CHANGE:
      return "CONTROL_CHANGE";
    case PianoRhythmSynthEventName.SOCKET_USER_GAIN_CHANGE:
      return "SOCKET_USER_GAIN_CHANGE";
    case PianoRhythmSynthEventName.UNKNOWN:
      return "UNKNOWN";
    case PianoRhythmSynthEventName.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface MidiDto {
  messageType: MidiDtoType;
  noteSource: MidiNoteSource;
  noteOn?: MidiDto_MidiNoteOn | undefined;
  noteOff?: MidiDto_MidiNoteOff | undefined;
  sustain?: MidiDto_MidiNoteSustain | undefined;
  allSoundOff?: MidiDto_MidiAllSoundOff | undefined;
  pitchBend?: MidiDto_MidiPitchBend | undefined;
}

export interface MidiDto_MidiNoteOn {
  channel: number;
  note: number;
  velocity: number;
  program?: number | undefined;
  volume?: number | undefined;
  bank?: number | undefined;
  pan?: number | undefined;
  expression?: number | undefined;
}

export interface MidiDto_MidiNoteOff {
  channel: number;
  note: number;
}

export interface MidiDto_MidiNoteSustain {
  channel: number;
  value: boolean;
}

export interface MidiDto_MidiAllSoundOff {
  channel: number;
}

export interface MidiDto_MidiPitchBend {
  channel: number;
  value: number;
}

export interface MidiMessageInputBuffer {
  type: MidiDtoType;
  channel: number;
  note: number;
  velocity: number;
  delay: number;
  program: number;
  volume: number;
  bank: number;
  pan: number;
  pitch: number;
  expression: number;
}

export interface MidiMessageInputDto {
  time: string;
  data: MidiMessageInputBuffer[];
  source?: MidiNoteSource | undefined;
}

export interface SF2Program {
  id: number;
  name: string;
  bank: number;
}

export interface SoundfontPreset {
  name: string;
  bank: number;
  preset: number;
  keyLow: number;
  keyHigh: number;
}

export interface Instrument {
  name: string;
  displayName: string;
  bank: number;
  preset: number;
  isDrumKit: boolean;
  /** optional field */
  keyLow: number;
  /** optional field */
  keyHigh: number;
}

export interface InstrumentsList {
  instruments: Instrument[];
}

export interface AudioChannel {
  channel: number;
  active: boolean;
  /** optional field */
  instrument?: Instrument | undefined;
  volume: number;
  pan: number;
  bank: number;
  bankMsb: number;
  preset: number;
  expression: number;
  channelPressure: number;
  pitchBend: number;
  pitchWheelSensitivity: number;
}

export interface SoundfontSetting {
  name: string;
  /** optional field */
  isDefault: boolean;
  /** optional field */
  path: string;
  /** optional field */
  custom: boolean;
}

export interface AudioServiceNoteActivity {
  channel: number;
  type: AudioServiceNoteActivityNoteType;
  /** optional field */
  socketID: string;
  isClient: boolean;
  /** optional field */
  velocity: number;
}

export interface SetChannelInstrumentPayload {
  channel: number;
  bank: number;
  preset: number;
  type: SetChannelInstrumentType;
  setActive: boolean;
  socketId?: string | undefined;
  volume?: number | undefined;
  pan?: number | undefined;
  expression?: number | undefined;
}

export interface SetChannelDetailsPayload {
  channel: number;
}

export interface UpdateChannelPayload {
  channel: number;
  expression?: number | undefined;
  pan?: number | undefined;
  volume?: number | undefined;
  bank?: number | undefined;
  preset?: number | undefined;
  pitch?: number | undefined;
}

export interface SynthEventProgramChangePayload {
  channel: number;
  expression?: number | undefined;
  pan?: number | undefined;
  volume?: number | undefined;
  bank?: number | undefined;
  preset?: number | undefined;
  pitch?: number | undefined;
}

function createBaseMidiDto(): MidiDto {
  return {
    messageType: 0,
    noteSource: 0,
    noteOn: undefined,
    noteOff: undefined,
    sustain: undefined,
    allSoundOff: undefined,
    pitchBend: undefined,
  };
}

export const MidiDto = {
  encode(message: MidiDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.messageType !== 0) {
      writer.uint32(8).int32(message.messageType);
    }
    if (message.noteSource !== 0) {
      writer.uint32(7992).int32(message.noteSource);
    }
    if (message.noteOn !== undefined) {
      MidiDto_MidiNoteOn.encode(message.noteOn, writer.uint32(18).fork()).ldelim();
    }
    if (message.noteOff !== undefined) {
      MidiDto_MidiNoteOff.encode(message.noteOff, writer.uint32(26).fork()).ldelim();
    }
    if (message.sustain !== undefined) {
      MidiDto_MidiNoteSustain.encode(message.sustain, writer.uint32(34).fork()).ldelim();
    }
    if (message.allSoundOff !== undefined) {
      MidiDto_MidiAllSoundOff.encode(message.allSoundOff, writer.uint32(42).fork()).ldelim();
    }
    if (message.pitchBend !== undefined) {
      MidiDto_MidiPitchBend.encode(message.pitchBend, writer.uint32(50).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.messageType = reader.int32() as any;
          continue;
        case 999:
          if (tag !== 7992) {
            break;
          }

          message.noteSource = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.noteOn = MidiDto_MidiNoteOn.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.noteOff = MidiDto_MidiNoteOff.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.sustain = MidiDto_MidiNoteSustain.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.allSoundOff = MidiDto_MidiAllSoundOff.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.pitchBend = MidiDto_MidiPitchBend.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto {
    return {
      messageType: isSet(object.messageType) ? midiDtoTypeFromJSON(object.messageType) : 0,
      noteSource: isSet(object.noteSource) ? midiNoteSourceFromJSON(object.noteSource) : 0,
      noteOn: isSet(object.noteOn) ? MidiDto_MidiNoteOn.fromJSON(object.noteOn) : undefined,
      noteOff: isSet(object.noteOff) ? MidiDto_MidiNoteOff.fromJSON(object.noteOff) : undefined,
      sustain: isSet(object.sustain) ? MidiDto_MidiNoteSustain.fromJSON(object.sustain) : undefined,
      allSoundOff: isSet(object.allSoundOff) ? MidiDto_MidiAllSoundOff.fromJSON(object.allSoundOff) : undefined,
      pitchBend: isSet(object.pitchBend) ? MidiDto_MidiPitchBend.fromJSON(object.pitchBend) : undefined,
    };
  },

  toJSON(message: MidiDto): unknown {
    const obj: any = {};
    if (message.messageType !== 0) {
      obj.messageType = midiDtoTypeToJSON(message.messageType);
    }
    if (message.noteSource !== 0) {
      obj.noteSource = midiNoteSourceToJSON(message.noteSource);
    }
    if (message.noteOn !== undefined) {
      obj.noteOn = MidiDto_MidiNoteOn.toJSON(message.noteOn);
    }
    if (message.noteOff !== undefined) {
      obj.noteOff = MidiDto_MidiNoteOff.toJSON(message.noteOff);
    }
    if (message.sustain !== undefined) {
      obj.sustain = MidiDto_MidiNoteSustain.toJSON(message.sustain);
    }
    if (message.allSoundOff !== undefined) {
      obj.allSoundOff = MidiDto_MidiAllSoundOff.toJSON(message.allSoundOff);
    }
    if (message.pitchBend !== undefined) {
      obj.pitchBend = MidiDto_MidiPitchBend.toJSON(message.pitchBend);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto>, I>>(base?: I): MidiDto {
    return MidiDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto>, I>>(object: I): MidiDto {
    const message = createBaseMidiDto();
    message.messageType = object.messageType ?? 0;
    message.noteSource = object.noteSource ?? 0;
    message.noteOn = (object.noteOn !== undefined && object.noteOn !== null)
      ? MidiDto_MidiNoteOn.fromPartial(object.noteOn)
      : undefined;
    message.noteOff = (object.noteOff !== undefined && object.noteOff !== null)
      ? MidiDto_MidiNoteOff.fromPartial(object.noteOff)
      : undefined;
    message.sustain = (object.sustain !== undefined && object.sustain !== null)
      ? MidiDto_MidiNoteSustain.fromPartial(object.sustain)
      : undefined;
    message.allSoundOff = (object.allSoundOff !== undefined && object.allSoundOff !== null)
      ? MidiDto_MidiAllSoundOff.fromPartial(object.allSoundOff)
      : undefined;
    message.pitchBend = (object.pitchBend !== undefined && object.pitchBend !== null)
      ? MidiDto_MidiPitchBend.fromPartial(object.pitchBend)
      : undefined;
    return message;
  },
};

function createBaseMidiDto_MidiNoteOn(): MidiDto_MidiNoteOn {
  return {
    channel: 0,
    note: 0,
    velocity: 0,
    program: undefined,
    volume: undefined,
    bank: undefined,
    pan: undefined,
    expression: undefined,
  };
}

export const MidiDto_MidiNoteOn = {
  encode(message: MidiDto_MidiNoteOn, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    if (message.note !== 0) {
      writer.uint32(16).int32(message.note);
    }
    if (message.velocity !== 0) {
      writer.uint32(24).int32(message.velocity);
    }
    if (message.program !== undefined) {
      writer.uint32(32).int32(message.program);
    }
    if (message.volume !== undefined) {
      writer.uint32(40).int32(message.volume);
    }
    if (message.bank !== undefined) {
      writer.uint32(48).int32(message.bank);
    }
    if (message.pan !== undefined) {
      writer.uint32(56).int32(message.pan);
    }
    if (message.expression !== undefined) {
      writer.uint32(64).int32(message.expression);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto_MidiNoteOn {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto_MidiNoteOn();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.note = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.velocity = reader.int32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.program = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.volume = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.bank = reader.int32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.pan = reader.int32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.expression = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto_MidiNoteOn {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      note: isSet(object.note) ? globalThis.Number(object.note) : 0,
      velocity: isSet(object.velocity) ? globalThis.Number(object.velocity) : 0,
      program: isSet(object.program) ? globalThis.Number(object.program) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : undefined,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : undefined,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : undefined,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : undefined,
    };
  },

  toJSON(message: MidiDto_MidiNoteOn): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.note !== 0) {
      obj.note = Math.round(message.note);
    }
    if (message.velocity !== 0) {
      obj.velocity = Math.round(message.velocity);
    }
    if (message.program !== undefined) {
      obj.program = Math.round(message.program);
    }
    if (message.volume !== undefined) {
      obj.volume = Math.round(message.volume);
    }
    if (message.bank !== undefined) {
      obj.bank = Math.round(message.bank);
    }
    if (message.pan !== undefined) {
      obj.pan = Math.round(message.pan);
    }
    if (message.expression !== undefined) {
      obj.expression = Math.round(message.expression);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto_MidiNoteOn>, I>>(base?: I): MidiDto_MidiNoteOn {
    return MidiDto_MidiNoteOn.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto_MidiNoteOn>, I>>(object: I): MidiDto_MidiNoteOn {
    const message = createBaseMidiDto_MidiNoteOn();
    message.channel = object.channel ?? 0;
    message.note = object.note ?? 0;
    message.velocity = object.velocity ?? 0;
    message.program = object.program ?? undefined;
    message.volume = object.volume ?? undefined;
    message.bank = object.bank ?? undefined;
    message.pan = object.pan ?? undefined;
    message.expression = object.expression ?? undefined;
    return message;
  },
};

function createBaseMidiDto_MidiNoteOff(): MidiDto_MidiNoteOff {
  return { channel: 0, note: 0 };
}

export const MidiDto_MidiNoteOff = {
  encode(message: MidiDto_MidiNoteOff, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    if (message.note !== 0) {
      writer.uint32(16).int32(message.note);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto_MidiNoteOff {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto_MidiNoteOff();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.note = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto_MidiNoteOff {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      note: isSet(object.note) ? globalThis.Number(object.note) : 0,
    };
  },

  toJSON(message: MidiDto_MidiNoteOff): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.note !== 0) {
      obj.note = Math.round(message.note);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto_MidiNoteOff>, I>>(base?: I): MidiDto_MidiNoteOff {
    return MidiDto_MidiNoteOff.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto_MidiNoteOff>, I>>(object: I): MidiDto_MidiNoteOff {
    const message = createBaseMidiDto_MidiNoteOff();
    message.channel = object.channel ?? 0;
    message.note = object.note ?? 0;
    return message;
  },
};

function createBaseMidiDto_MidiNoteSustain(): MidiDto_MidiNoteSustain {
  return { channel: 0, value: false };
}

export const MidiDto_MidiNoteSustain = {
  encode(message: MidiDto_MidiNoteSustain, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    if (message.value !== false) {
      writer.uint32(16).bool(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto_MidiNoteSustain {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto_MidiNoteSustain();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.value = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto_MidiNoteSustain {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false,
    };
  },

  toJSON(message: MidiDto_MidiNoteSustain): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.value !== false) {
      obj.value = message.value;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto_MidiNoteSustain>, I>>(base?: I): MidiDto_MidiNoteSustain {
    return MidiDto_MidiNoteSustain.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto_MidiNoteSustain>, I>>(object: I): MidiDto_MidiNoteSustain {
    const message = createBaseMidiDto_MidiNoteSustain();
    message.channel = object.channel ?? 0;
    message.value = object.value ?? false;
    return message;
  },
};

function createBaseMidiDto_MidiAllSoundOff(): MidiDto_MidiAllSoundOff {
  return { channel: 0 };
}

export const MidiDto_MidiAllSoundOff = {
  encode(message: MidiDto_MidiAllSoundOff, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto_MidiAllSoundOff {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto_MidiAllSoundOff();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto_MidiAllSoundOff {
    return { channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0 };
  },

  toJSON(message: MidiDto_MidiAllSoundOff): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto_MidiAllSoundOff>, I>>(base?: I): MidiDto_MidiAllSoundOff {
    return MidiDto_MidiAllSoundOff.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto_MidiAllSoundOff>, I>>(object: I): MidiDto_MidiAllSoundOff {
    const message = createBaseMidiDto_MidiAllSoundOff();
    message.channel = object.channel ?? 0;
    return message;
  },
};

function createBaseMidiDto_MidiPitchBend(): MidiDto_MidiPitchBend {
  return { channel: 0, value: 0 };
}

export const MidiDto_MidiPitchBend = {
  encode(message: MidiDto_MidiPitchBend, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.value !== 0) {
      writer.uint32(16).uint32(message.value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto_MidiPitchBend {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto_MidiPitchBend();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.value = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto_MidiPitchBend {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0,
    };
  },

  toJSON(message: MidiDto_MidiPitchBend): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.value !== 0) {
      obj.value = Math.round(message.value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto_MidiPitchBend>, I>>(base?: I): MidiDto_MidiPitchBend {
    return MidiDto_MidiPitchBend.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto_MidiPitchBend>, I>>(object: I): MidiDto_MidiPitchBend {
    const message = createBaseMidiDto_MidiPitchBend();
    message.channel = object.channel ?? 0;
    message.value = object.value ?? 0;
    return message;
  },
};

function createBaseMidiMessageInputBuffer(): MidiMessageInputBuffer {
  return {
    type: 0,
    channel: 0,
    note: 0,
    velocity: 0,
    delay: 0,
    program: 0,
    volume: 0,
    bank: 0,
    pan: 0,
    pitch: 0,
    expression: 0,
  };
}

export const MidiMessageInputBuffer = {
  encode(message: MidiMessageInputBuffer, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.type !== 0) {
      writer.uint32(8).int32(message.type);
    }
    if (message.channel !== 0) {
      writer.uint32(16).uint32(message.channel);
    }
    if (message.note !== 0) {
      writer.uint32(24).uint32(message.note);
    }
    if (message.velocity !== 0) {
      writer.uint32(32).uint32(message.velocity);
    }
    if (message.delay !== 0) {
      writer.uint32(45).float(message.delay);
    }
    if (message.program !== 0) {
      writer.uint32(48).uint32(message.program);
    }
    if (message.volume !== 0) {
      writer.uint32(56).uint32(message.volume);
    }
    if (message.bank !== 0) {
      writer.uint32(64).uint32(message.bank);
    }
    if (message.pan !== 0) {
      writer.uint32(72).uint32(message.pan);
    }
    if (message.pitch !== 0) {
      writer.uint32(80).uint32(message.pitch);
    }
    if (message.expression !== 0) {
      writer.uint32(88).uint32(message.expression);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageInputBuffer {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageInputBuffer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.note = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.velocity = reader.uint32();
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.delay = reader.float();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.program = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.pitch = reader.uint32();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.expression = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageInputBuffer {
    return {
      type: isSet(object.type) ? midiDtoTypeFromJSON(object.type) : 0,
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      note: isSet(object.note) ? globalThis.Number(object.note) : 0,
      velocity: isSet(object.velocity) ? globalThis.Number(object.velocity) : 0,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : 0,
      program: isSet(object.program) ? globalThis.Number(object.program) : 0,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : 0,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : 0,
      pitch: isSet(object.pitch) ? globalThis.Number(object.pitch) : 0,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : 0,
    };
  },

  toJSON(message: MidiMessageInputBuffer): unknown {
    const obj: any = {};
    if (message.type !== 0) {
      obj.type = midiDtoTypeToJSON(message.type);
    }
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.note !== 0) {
      obj.note = Math.round(message.note);
    }
    if (message.velocity !== 0) {
      obj.velocity = Math.round(message.velocity);
    }
    if (message.delay !== 0) {
      obj.delay = message.delay;
    }
    if (message.program !== 0) {
      obj.program = Math.round(message.program);
    }
    if (message.volume !== 0) {
      obj.volume = Math.round(message.volume);
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    if (message.pan !== 0) {
      obj.pan = Math.round(message.pan);
    }
    if (message.pitch !== 0) {
      obj.pitch = Math.round(message.pitch);
    }
    if (message.expression !== 0) {
      obj.expression = Math.round(message.expression);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageInputBuffer>, I>>(base?: I): MidiMessageInputBuffer {
    return MidiMessageInputBuffer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageInputBuffer>, I>>(object: I): MidiMessageInputBuffer {
    const message = createBaseMidiMessageInputBuffer();
    message.type = object.type ?? 0;
    message.channel = object.channel ?? 0;
    message.note = object.note ?? 0;
    message.velocity = object.velocity ?? 0;
    message.delay = object.delay ?? 0;
    message.program = object.program ?? 0;
    message.volume = object.volume ?? 0;
    message.bank = object.bank ?? 0;
    message.pan = object.pan ?? 0;
    message.pitch = object.pitch ?? 0;
    message.expression = object.expression ?? 0;
    return message;
  },
};

function createBaseMidiMessageInputDto(): MidiMessageInputDto {
  return { time: "", data: [], source: undefined };
}

export const MidiMessageInputDto = {
  encode(message: MidiMessageInputDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.time !== "") {
      writer.uint32(10).string(message.time);
    }
    for (const v of message.data) {
      MidiMessageInputBuffer.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    if (message.source !== undefined) {
      writer.uint32(24).int32(message.source);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageInputDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageInputDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.time = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(MidiMessageInputBuffer.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.source = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageInputDto {
    return {
      time: isSet(object.time) ? globalThis.String(object.time) : "",
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MidiMessageInputBuffer.fromJSON(e))
        : [],
      source: isSet(object.source) ? midiNoteSourceFromJSON(object.source) : undefined,
    };
  },

  toJSON(message: MidiMessageInputDto): unknown {
    const obj: any = {};
    if (message.time !== "") {
      obj.time = message.time;
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MidiMessageInputBuffer.toJSON(e));
    }
    if (message.source !== undefined) {
      obj.source = midiNoteSourceToJSON(message.source);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageInputDto>, I>>(base?: I): MidiMessageInputDto {
    return MidiMessageInputDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageInputDto>, I>>(object: I): MidiMessageInputDto {
    const message = createBaseMidiMessageInputDto();
    message.time = object.time ?? "";
    message.data = object.data?.map((e) => MidiMessageInputBuffer.fromPartial(e)) || [];
    message.source = object.source ?? undefined;
    return message;
  },
};

function createBaseSF2Program(): SF2Program {
  return { id: 0, name: "", bank: 0 };
}

export const SF2Program = {
  encode(message: SF2Program, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== 0) {
      writer.uint32(8).uint32(message.id);
    }
    if (message.name !== "") {
      writer.uint32(18).string(message.name);
    }
    if (message.bank !== 0) {
      writer.uint32(24).uint32(message.bank);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SF2Program {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSF2Program();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = reader.uint32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.bank = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SF2Program {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
    };
  },

  toJSON(message: SF2Program): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SF2Program>, I>>(base?: I): SF2Program {
    return SF2Program.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SF2Program>, I>>(object: I): SF2Program {
    const message = createBaseSF2Program();
    message.id = object.id ?? 0;
    message.name = object.name ?? "";
    message.bank = object.bank ?? 0;
    return message;
  },
};

function createBaseSoundfontPreset(): SoundfontPreset {
  return { name: "", bank: 0, preset: 0, keyLow: 0, keyHigh: 0 };
}

export const SoundfontPreset = {
  encode(message: SoundfontPreset, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.bank !== 0) {
      writer.uint32(16).uint32(message.bank);
    }
    if (message.preset !== 0) {
      writer.uint32(24).uint32(message.preset);
    }
    if (message.keyLow !== 0) {
      writer.uint32(32).uint32(message.keyLow);
    }
    if (message.keyHigh !== 0) {
      writer.uint32(40).uint32(message.keyHigh);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SoundfontPreset {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoundfontPreset();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.keyLow = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.keyHigh = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SoundfontPreset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : 0,
      keyLow: isSet(object.keyLow) ? globalThis.Number(object.keyLow) : 0,
      keyHigh: isSet(object.keyHigh) ? globalThis.Number(object.keyHigh) : 0,
    };
  },

  toJSON(message: SoundfontPreset): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    if (message.preset !== 0) {
      obj.preset = Math.round(message.preset);
    }
    if (message.keyLow !== 0) {
      obj.keyLow = Math.round(message.keyLow);
    }
    if (message.keyHigh !== 0) {
      obj.keyHigh = Math.round(message.keyHigh);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SoundfontPreset>, I>>(base?: I): SoundfontPreset {
    return SoundfontPreset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoundfontPreset>, I>>(object: I): SoundfontPreset {
    const message = createBaseSoundfontPreset();
    message.name = object.name ?? "";
    message.bank = object.bank ?? 0;
    message.preset = object.preset ?? 0;
    message.keyLow = object.keyLow ?? 0;
    message.keyHigh = object.keyHigh ?? 0;
    return message;
  },
};

function createBaseInstrument(): Instrument {
  return { name: "", displayName: "", bank: 0, preset: 0, isDrumKit: false, keyLow: 0, keyHigh: 0 };
}

export const Instrument = {
  encode(message: Instrument, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.displayName !== "") {
      writer.uint32(18).string(message.displayName);
    }
    if (message.bank !== 0) {
      writer.uint32(24).uint32(message.bank);
    }
    if (message.preset !== 0) {
      writer.uint32(32).uint32(message.preset);
    }
    if (message.isDrumKit !== false) {
      writer.uint32(40).bool(message.isDrumKit);
    }
    if (message.keyLow !== 0) {
      writer.uint32(48).uint32(message.keyLow);
    }
    if (message.keyHigh !== 0) {
      writer.uint32(56).uint32(message.keyHigh);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): Instrument {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInstrument();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.displayName = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.isDrumKit = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.keyLow = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.keyHigh = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): Instrument {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      displayName: isSet(object.displayName) ? globalThis.String(object.displayName) : "",
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : 0,
      isDrumKit: isSet(object.isDrumKit) ? globalThis.Boolean(object.isDrumKit) : false,
      keyLow: isSet(object.keyLow) ? globalThis.Number(object.keyLow) : 0,
      keyHigh: isSet(object.keyHigh) ? globalThis.Number(object.keyHigh) : 0,
    };
  },

  toJSON(message: Instrument): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.displayName !== "") {
      obj.displayName = message.displayName;
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    if (message.preset !== 0) {
      obj.preset = Math.round(message.preset);
    }
    if (message.isDrumKit !== false) {
      obj.isDrumKit = message.isDrumKit;
    }
    if (message.keyLow !== 0) {
      obj.keyLow = Math.round(message.keyLow);
    }
    if (message.keyHigh !== 0) {
      obj.keyHigh = Math.round(message.keyHigh);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<Instrument>, I>>(base?: I): Instrument {
    return Instrument.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Instrument>, I>>(object: I): Instrument {
    const message = createBaseInstrument();
    message.name = object.name ?? "";
    message.displayName = object.displayName ?? "";
    message.bank = object.bank ?? 0;
    message.preset = object.preset ?? 0;
    message.isDrumKit = object.isDrumKit ?? false;
    message.keyLow = object.keyLow ?? 0;
    message.keyHigh = object.keyHigh ?? 0;
    return message;
  },
};

function createBaseInstrumentsList(): InstrumentsList {
  return { instruments: [] };
}

export const InstrumentsList = {
  encode(message: InstrumentsList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.instruments) {
      Instrument.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): InstrumentsList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseInstrumentsList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.instruments.push(Instrument.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): InstrumentsList {
    return {
      instruments: globalThis.Array.isArray(object?.instruments)
        ? object.instruments.map((e: any) => Instrument.fromJSON(e))
        : [],
    };
  },

  toJSON(message: InstrumentsList): unknown {
    const obj: any = {};
    if (message.instruments?.length) {
      obj.instruments = message.instruments.map((e) => Instrument.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<InstrumentsList>, I>>(base?: I): InstrumentsList {
    return InstrumentsList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InstrumentsList>, I>>(object: I): InstrumentsList {
    const message = createBaseInstrumentsList();
    message.instruments = object.instruments?.map((e) => Instrument.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAudioChannel(): AudioChannel {
  return {
    channel: 0,
    active: false,
    instrument: undefined,
    volume: 0,
    pan: 0,
    bank: 0,
    bankMsb: 0,
    preset: 0,
    expression: 0,
    channelPressure: 0,
    pitchBend: 0,
    pitchWheelSensitivity: 0,
  };
}

export const AudioChannel = {
  encode(message: AudioChannel, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.active !== false) {
      writer.uint32(16).bool(message.active);
    }
    if (message.instrument !== undefined) {
      Instrument.encode(message.instrument, writer.uint32(26).fork()).ldelim();
    }
    if (message.volume !== 0) {
      writer.uint32(32).uint32(message.volume);
    }
    if (message.pan !== 0) {
      writer.uint32(40).uint32(message.pan);
    }
    if (message.bank !== 0) {
      writer.uint32(48).uint32(message.bank);
    }
    if (message.bankMsb !== 0) {
      writer.uint32(56).uint32(message.bankMsb);
    }
    if (message.preset !== 0) {
      writer.uint32(64).uint32(message.preset);
    }
    if (message.expression !== 0) {
      writer.uint32(72).uint32(message.expression);
    }
    if (message.channelPressure !== 0) {
      writer.uint32(80).uint32(message.channelPressure);
    }
    if (message.pitchBend !== 0) {
      writer.uint32(88).uint32(message.pitchBend);
    }
    if (message.pitchWheelSensitivity !== 0) {
      writer.uint32(96).uint32(message.pitchWheelSensitivity);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AudioChannel {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAudioChannel();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.active = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.instrument = Instrument.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.bankMsb = reader.uint32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.expression = reader.uint32();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.channelPressure = reader.uint32();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.pitchBend = reader.uint32();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.pitchWheelSensitivity = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AudioChannel {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      active: isSet(object.active) ? globalThis.Boolean(object.active) : false,
      instrument: isSet(object.instrument) ? Instrument.fromJSON(object.instrument) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : 0,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : 0,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
      bankMsb: isSet(object.bankMsb) ? globalThis.Number(object.bankMsb) : 0,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : 0,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : 0,
      channelPressure: isSet(object.channelPressure) ? globalThis.Number(object.channelPressure) : 0,
      pitchBend: isSet(object.pitchBend) ? globalThis.Number(object.pitchBend) : 0,
      pitchWheelSensitivity: isSet(object.pitchWheelSensitivity) ? globalThis.Number(object.pitchWheelSensitivity) : 0,
    };
  },

  toJSON(message: AudioChannel): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.active !== false) {
      obj.active = message.active;
    }
    if (message.instrument !== undefined) {
      obj.instrument = Instrument.toJSON(message.instrument);
    }
    if (message.volume !== 0) {
      obj.volume = Math.round(message.volume);
    }
    if (message.pan !== 0) {
      obj.pan = Math.round(message.pan);
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    if (message.bankMsb !== 0) {
      obj.bankMsb = Math.round(message.bankMsb);
    }
    if (message.preset !== 0) {
      obj.preset = Math.round(message.preset);
    }
    if (message.expression !== 0) {
      obj.expression = Math.round(message.expression);
    }
    if (message.channelPressure !== 0) {
      obj.channelPressure = Math.round(message.channelPressure);
    }
    if (message.pitchBend !== 0) {
      obj.pitchBend = Math.round(message.pitchBend);
    }
    if (message.pitchWheelSensitivity !== 0) {
      obj.pitchWheelSensitivity = Math.round(message.pitchWheelSensitivity);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AudioChannel>, I>>(base?: I): AudioChannel {
    return AudioChannel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AudioChannel>, I>>(object: I): AudioChannel {
    const message = createBaseAudioChannel();
    message.channel = object.channel ?? 0;
    message.active = object.active ?? false;
    message.instrument = (object.instrument !== undefined && object.instrument !== null)
      ? Instrument.fromPartial(object.instrument)
      : undefined;
    message.volume = object.volume ?? 0;
    message.pan = object.pan ?? 0;
    message.bank = object.bank ?? 0;
    message.bankMsb = object.bankMsb ?? 0;
    message.preset = object.preset ?? 0;
    message.expression = object.expression ?? 0;
    message.channelPressure = object.channelPressure ?? 0;
    message.pitchBend = object.pitchBend ?? 0;
    message.pitchWheelSensitivity = object.pitchWheelSensitivity ?? 0;
    return message;
  },
};

function createBaseSoundfontSetting(): SoundfontSetting {
  return { name: "", isDefault: false, path: "", custom: false };
}

export const SoundfontSetting = {
  encode(message: SoundfontSetting, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.name !== "") {
      writer.uint32(10).string(message.name);
    }
    if (message.isDefault !== false) {
      writer.uint32(16).bool(message.isDefault);
    }
    if (message.path !== "") {
      writer.uint32(26).string(message.path);
    }
    if (message.custom !== false) {
      writer.uint32(32).bool(message.custom);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SoundfontSetting {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSoundfontSetting();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.isDefault = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.path = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.custom = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SoundfontSetting {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : "",
      isDefault: isSet(object.isDefault) ? globalThis.Boolean(object.isDefault) : false,
      path: isSet(object.path) ? globalThis.String(object.path) : "",
      custom: isSet(object.custom) ? globalThis.Boolean(object.custom) : false,
    };
  },

  toJSON(message: SoundfontSetting): unknown {
    const obj: any = {};
    if (message.name !== "") {
      obj.name = message.name;
    }
    if (message.isDefault !== false) {
      obj.isDefault = message.isDefault;
    }
    if (message.path !== "") {
      obj.path = message.path;
    }
    if (message.custom !== false) {
      obj.custom = message.custom;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SoundfontSetting>, I>>(base?: I): SoundfontSetting {
    return SoundfontSetting.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SoundfontSetting>, I>>(object: I): SoundfontSetting {
    const message = createBaseSoundfontSetting();
    message.name = object.name ?? "";
    message.isDefault = object.isDefault ?? false;
    message.path = object.path ?? "";
    message.custom = object.custom ?? false;
    return message;
  },
};

function createBaseAudioServiceNoteActivity(): AudioServiceNoteActivity {
  return { channel: 0, type: 0, socketID: "", isClient: false, velocity: 0 };
}

export const AudioServiceNoteActivity = {
  encode(message: AudioServiceNoteActivity, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    if (message.type !== 0) {
      writer.uint32(16).int32(message.type);
    }
    if (message.socketID !== "") {
      writer.uint32(26).string(message.socketID);
    }
    if (message.isClient !== false) {
      writer.uint32(32).bool(message.isClient);
    }
    if (message.velocity !== 0) {
      writer.uint32(40).int32(message.velocity);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AudioServiceNoteActivity {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAudioServiceNoteActivity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.isClient = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.velocity = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AudioServiceNoteActivity {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      type: isSet(object.type) ? audioServiceNoteActivityNoteTypeFromJSON(object.type) : 0,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      isClient: isSet(object.isClient) ? globalThis.Boolean(object.isClient) : false,
      velocity: isSet(object.velocity) ? globalThis.Number(object.velocity) : 0,
    };
  },

  toJSON(message: AudioServiceNoteActivity): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.type !== 0) {
      obj.type = audioServiceNoteActivityNoteTypeToJSON(message.type);
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.isClient !== false) {
      obj.isClient = message.isClient;
    }
    if (message.velocity !== 0) {
      obj.velocity = Math.round(message.velocity);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AudioServiceNoteActivity>, I>>(base?: I): AudioServiceNoteActivity {
    return AudioServiceNoteActivity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AudioServiceNoteActivity>, I>>(object: I): AudioServiceNoteActivity {
    const message = createBaseAudioServiceNoteActivity();
    message.channel = object.channel ?? 0;
    message.type = object.type ?? 0;
    message.socketID = object.socketID ?? "";
    message.isClient = object.isClient ?? false;
    message.velocity = object.velocity ?? 0;
    return message;
  },
};

function createBaseSetChannelInstrumentPayload(): SetChannelInstrumentPayload {
  return {
    channel: 0,
    bank: 0,
    preset: 0,
    type: 0,
    setActive: false,
    socketId: undefined,
    volume: undefined,
    pan: undefined,
    expression: undefined,
  };
}

export const SetChannelInstrumentPayload = {
  encode(message: SetChannelInstrumentPayload, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.bank !== 0) {
      writer.uint32(16).uint32(message.bank);
    }
    if (message.preset !== 0) {
      writer.uint32(24).uint32(message.preset);
    }
    if (message.type !== 0) {
      writer.uint32(32).int32(message.type);
    }
    if (message.setActive !== false) {
      writer.uint32(40).bool(message.setActive);
    }
    if (message.socketId !== undefined) {
      writer.uint32(50).string(message.socketId);
    }
    if (message.volume !== undefined) {
      writer.uint32(56).uint32(message.volume);
    }
    if (message.pan !== undefined) {
      writer.uint32(64).uint32(message.pan);
    }
    if (message.expression !== undefined) {
      writer.uint32(72).uint32(message.expression);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SetChannelInstrumentPayload {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetChannelInstrumentPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.setActive = reader.bool();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.expression = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetChannelInstrumentPayload {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : 0,
      type: isSet(object.type) ? setChannelInstrumentTypeFromJSON(object.type) : 0,
      setActive: isSet(object.setActive) ? globalThis.Boolean(object.setActive) : false,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : undefined,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : undefined,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : undefined,
    };
  },

  toJSON(message: SetChannelInstrumentPayload): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    if (message.preset !== 0) {
      obj.preset = Math.round(message.preset);
    }
    if (message.type !== 0) {
      obj.type = setChannelInstrumentTypeToJSON(message.type);
    }
    if (message.setActive !== false) {
      obj.setActive = message.setActive;
    }
    if (message.socketId !== undefined) {
      obj.socketId = message.socketId;
    }
    if (message.volume !== undefined) {
      obj.volume = Math.round(message.volume);
    }
    if (message.pan !== undefined) {
      obj.pan = Math.round(message.pan);
    }
    if (message.expression !== undefined) {
      obj.expression = Math.round(message.expression);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetChannelInstrumentPayload>, I>>(base?: I): SetChannelInstrumentPayload {
    return SetChannelInstrumentPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetChannelInstrumentPayload>, I>>(object: I): SetChannelInstrumentPayload {
    const message = createBaseSetChannelInstrumentPayload();
    message.channel = object.channel ?? 0;
    message.bank = object.bank ?? 0;
    message.preset = object.preset ?? 0;
    message.type = object.type ?? 0;
    message.setActive = object.setActive ?? false;
    message.socketId = object.socketId ?? undefined;
    message.volume = object.volume ?? undefined;
    message.pan = object.pan ?? undefined;
    message.expression = object.expression ?? undefined;
    return message;
  },
};

function createBaseSetChannelDetailsPayload(): SetChannelDetailsPayload {
  return { channel: 0 };
}

export const SetChannelDetailsPayload = {
  encode(message: SetChannelDetailsPayload, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SetChannelDetailsPayload {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSetChannelDetailsPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SetChannelDetailsPayload {
    return { channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0 };
  },

  toJSON(message: SetChannelDetailsPayload): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SetChannelDetailsPayload>, I>>(base?: I): SetChannelDetailsPayload {
    return SetChannelDetailsPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetChannelDetailsPayload>, I>>(object: I): SetChannelDetailsPayload {
    const message = createBaseSetChannelDetailsPayload();
    message.channel = object.channel ?? 0;
    return message;
  },
};

function createBaseUpdateChannelPayload(): UpdateChannelPayload {
  return {
    channel: 0,
    expression: undefined,
    pan: undefined,
    volume: undefined,
    bank: undefined,
    preset: undefined,
    pitch: undefined,
  };
}

export const UpdateChannelPayload = {
  encode(message: UpdateChannelPayload, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.expression !== undefined) {
      writer.uint32(16).uint32(message.expression);
    }
    if (message.pan !== undefined) {
      writer.uint32(24).uint32(message.pan);
    }
    if (message.volume !== undefined) {
      writer.uint32(32).uint32(message.volume);
    }
    if (message.bank !== undefined) {
      writer.uint32(40).uint32(message.bank);
    }
    if (message.preset !== undefined) {
      writer.uint32(48).uint32(message.preset);
    }
    if (message.pitch !== undefined) {
      writer.uint32(56).uint32(message.pitch);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UpdateChannelPayload {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUpdateChannelPayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.expression = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.pitch = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UpdateChannelPayload {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : undefined,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : undefined,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : undefined,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : undefined,
      pitch: isSet(object.pitch) ? globalThis.Number(object.pitch) : undefined,
    };
  },

  toJSON(message: UpdateChannelPayload): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.expression !== undefined) {
      obj.expression = Math.round(message.expression);
    }
    if (message.pan !== undefined) {
      obj.pan = Math.round(message.pan);
    }
    if (message.volume !== undefined) {
      obj.volume = Math.round(message.volume);
    }
    if (message.bank !== undefined) {
      obj.bank = Math.round(message.bank);
    }
    if (message.preset !== undefined) {
      obj.preset = Math.round(message.preset);
    }
    if (message.pitch !== undefined) {
      obj.pitch = Math.round(message.pitch);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UpdateChannelPayload>, I>>(base?: I): UpdateChannelPayload {
    return UpdateChannelPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateChannelPayload>, I>>(object: I): UpdateChannelPayload {
    const message = createBaseUpdateChannelPayload();
    message.channel = object.channel ?? 0;
    message.expression = object.expression ?? undefined;
    message.pan = object.pan ?? undefined;
    message.volume = object.volume ?? undefined;
    message.bank = object.bank ?? undefined;
    message.preset = object.preset ?? undefined;
    message.pitch = object.pitch ?? undefined;
    return message;
  },
};

function createBaseSynthEventProgramChangePayload(): SynthEventProgramChangePayload {
  return {
    channel: 0,
    expression: undefined,
    pan: undefined,
    volume: undefined,
    bank: undefined,
    preset: undefined,
    pitch: undefined,
  };
}

export const SynthEventProgramChangePayload = {
  encode(message: SynthEventProgramChangePayload, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.expression !== undefined) {
      writer.uint32(16).uint32(message.expression);
    }
    if (message.pan !== undefined) {
      writer.uint32(24).uint32(message.pan);
    }
    if (message.volume !== undefined) {
      writer.uint32(32).uint32(message.volume);
    }
    if (message.bank !== undefined) {
      writer.uint32(40).uint32(message.bank);
    }
    if (message.preset !== undefined) {
      writer.uint32(48).uint32(message.preset);
    }
    if (message.pitch !== undefined) {
      writer.uint32(56).uint32(message.pitch);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SynthEventProgramChangePayload {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSynthEventProgramChangePayload();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.expression = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.bank = reader.uint32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.preset = reader.uint32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.pitch = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SynthEventProgramChangePayload {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      expression: isSet(object.expression) ? globalThis.Number(object.expression) : undefined,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : undefined,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : undefined,
      preset: isSet(object.preset) ? globalThis.Number(object.preset) : undefined,
      pitch: isSet(object.pitch) ? globalThis.Number(object.pitch) : undefined,
    };
  },

  toJSON(message: SynthEventProgramChangePayload): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.expression !== undefined) {
      obj.expression = Math.round(message.expression);
    }
    if (message.pan !== undefined) {
      obj.pan = Math.round(message.pan);
    }
    if (message.volume !== undefined) {
      obj.volume = Math.round(message.volume);
    }
    if (message.bank !== undefined) {
      obj.bank = Math.round(message.bank);
    }
    if (message.preset !== undefined) {
      obj.preset = Math.round(message.preset);
    }
    if (message.pitch !== undefined) {
      obj.pitch = Math.round(message.pitch);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SynthEventProgramChangePayload>, I>>(base?: I): SynthEventProgramChangePayload {
    return SynthEventProgramChangePayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SynthEventProgramChangePayload>, I>>(
    object: I,
  ): SynthEventProgramChangePayload {
    const message = createBaseSynthEventProgramChangePayload();
    message.channel = object.channel ?? 0;
    message.expression = object.expression ?? undefined;
    message.pan = object.pan ?? undefined;
    message.volume = object.volume ?? undefined;
    message.bank = object.bank ?? undefined;
    message.preset = object.preset ?? undefined;
    message.pitch = object.pitch ?? undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
