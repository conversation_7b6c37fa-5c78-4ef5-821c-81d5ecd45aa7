// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: room-renditions.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";

export const protobufPackage = "PianoRhythm.Serialization.RoomRenditions";

export enum RoomStatus {
  Public = 0,
  Private = 1,
  UNRECOGNIZED = -1,
}

export function roomStatusFromJSON(object: any): RoomStatus {
  switch (object) {
    case 0:
    case "Public":
      return RoomStatus.Public;
    case 1:
    case "Private":
      return RoomStatus.Private;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RoomStatus.UNRECOGNIZED;
  }
}

export function roomStatusToJSON(object: RoomStatus): string {
  switch (object) {
    case RoomStatus.Public:
      return "Public";
    case RoomStatus.Private:
      return "Private";
    case RoomStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RoomType {
  Lobby = 0,
  LobbyTurnBased = 1,
  LobbyOrchestra = 2,
  LobbyPRO = 3,
  WorldLobby = 4,
  World = 5,
  Normal = 6,
  TurnBased = 7,
  Game = 8,
  LobbyGuest = 9,
  SoloGame = 10,
  Orchestra = 11,
  SoloTraining = 12,
  Unknown = 99,
  UNRECOGNIZED = -1,
}

export function roomTypeFromJSON(object: any): RoomType {
  switch (object) {
    case 0:
    case "Lobby":
      return RoomType.Lobby;
    case 1:
    case "LobbyTurnBased":
      return RoomType.LobbyTurnBased;
    case 2:
    case "LobbyOrchestra":
      return RoomType.LobbyOrchestra;
    case 3:
    case "LobbyPRO":
      return RoomType.LobbyPRO;
    case 4:
    case "WorldLobby":
      return RoomType.WorldLobby;
    case 5:
    case "World":
      return RoomType.World;
    case 6:
    case "Normal":
      return RoomType.Normal;
    case 7:
    case "TurnBased":
      return RoomType.TurnBased;
    case 8:
    case "Game":
      return RoomType.Game;
    case 9:
    case "LobbyGuest":
      return RoomType.LobbyGuest;
    case 10:
    case "SoloGame":
      return RoomType.SoloGame;
    case 11:
    case "Orchestra":
      return RoomType.Orchestra;
    case 12:
    case "SoloTraining":
      return RoomType.SoloTraining;
    case 99:
    case "Unknown":
      return RoomType.Unknown;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RoomType.UNRECOGNIZED;
  }
}

export function roomTypeToJSON(object: RoomType): string {
  switch (object) {
    case RoomType.Lobby:
      return "Lobby";
    case RoomType.LobbyTurnBased:
      return "LobbyTurnBased";
    case RoomType.LobbyOrchestra:
      return "LobbyOrchestra";
    case RoomType.LobbyPRO:
      return "LobbyPRO";
    case RoomType.WorldLobby:
      return "WorldLobby";
    case RoomType.World:
      return "World";
    case RoomType.Normal:
      return "Normal";
    case RoomType.TurnBased:
      return "TurnBased";
    case RoomType.Game:
      return "Game";
    case RoomType.LobbyGuest:
      return "LobbyGuest";
    case RoomType.SoloGame:
      return "SoloGame";
    case RoomType.Orchestra:
      return "Orchestra";
    case RoomType.SoloTraining:
      return "SoloTraining";
    case RoomType.Unknown:
      return "Unknown";
    case RoomType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum RoomStages {
  UNKNOWN = 0,
  THE_VOID = 1,
  CLOUD_PALACE = 2,
  KINGS_HALL = 3,
  GRASSLAND = 4,
  TORII_GATE = 5,
  MUSIC_STUDIO = 6,
  ARENA = 7,
  FOREST = 8,
  UNRECOGNIZED = -1,
}

export function roomStagesFromJSON(object: any): RoomStages {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return RoomStages.UNKNOWN;
    case 1:
    case "THE_VOID":
      return RoomStages.THE_VOID;
    case 2:
    case "CLOUD_PALACE":
      return RoomStages.CLOUD_PALACE;
    case 3:
    case "KINGS_HALL":
      return RoomStages.KINGS_HALL;
    case 4:
    case "GRASSLAND":
      return RoomStages.GRASSLAND;
    case 5:
    case "TORII_GATE":
      return RoomStages.TORII_GATE;
    case 6:
    case "MUSIC_STUDIO":
      return RoomStages.MUSIC_STUDIO;
    case 7:
    case "ARENA":
      return RoomStages.ARENA;
    case 8:
    case "FOREST":
      return RoomStages.FOREST;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RoomStages.UNRECOGNIZED;
  }
}

export function roomStagesToJSON(object: RoomStages): string {
  switch (object) {
    case RoomStages.UNKNOWN:
      return "UNKNOWN";
    case RoomStages.THE_VOID:
      return "THE_VOID";
    case RoomStages.CLOUD_PALACE:
      return "CLOUD_PALACE";
    case RoomStages.KINGS_HALL:
      return "KINGS_HALL";
    case RoomStages.GRASSLAND:
      return "GRASSLAND";
    case RoomStages.TORII_GATE:
      return "TORII_GATE";
    case RoomStages.MUSIC_STUDIO:
      return "MUSIC_STUDIO";
    case RoomStages.ARENA:
      return "ARENA";
    case RoomStages.FOREST:
      return "FOREST";
    case RoomStages.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RoomHostDetails {
  ContinentCode: string;
  CountryCode?: string | undefined;
  NoteBufferInterval?: number | undefined;
}

export interface RoomStageVisualEffects {
  rain?: boolean | undefined;
  grass?: boolean | undefined;
  postProcessBlackWhite?: boolean | undefined;
  postProcessDownSample?: boolean | undefined;
  postProcessToneMap?: boolean | undefined;
  postProcessEmboss?: boolean | undefined;
  postProcessVignette?: boolean | undefined;
}

export interface RoomStageAudioEffect {
  volume: number;
  loop: boolean;
  playbackRate: number;
  pan: number;
}

export interface RoomStageAudioEffects {
  rain?: RoomStageAudioEffect | undefined;
  wind?: RoomStageAudioEffect | undefined;
  birds?: RoomStageAudioEffect | undefined;
}

export interface RoomStageDetails {
  stage: RoomStages;
  effects: RoomStageVisualEffects | undefined;
  audioEffects: RoomStageAudioEffects | undefined;
}

export interface RoomSettingsMeta {
  AutoRemove: boolean;
  Unique: boolean;
  Persistent: boolean;
}

export interface BasicRoomDto {
  roomName: string;
  roomID: string;
  roomType: string;
  userCount: number;
  maxUsers: number;
  isPasswordProtected: boolean;
  hostDetails?: RoomHostDetails | undefined;
  roomOwner: string;
  roomStage: RoomStages;
  StageDetails?: RoomStageDetails | undefined;
  createdDate: string;
}

export interface RoomDto {
  roomName: string;
  roomID: string;
  roomOwner: string;
  users: string[];
}

export interface RoomSettings {
  WelcomeMessage?: string | undefined;
  MaxPlayers: number;
  MaxChatHistory: number;
  RoomOwner: string;
  RoomStatus: RoomStatus;
  RoomType: RoomType;
  Password?: string | undefined;
  OnlyOwnerCanPlay: boolean;
  OnlyOwnerCanChat: boolean;
  AllowBlackMidi: boolean;
  AllowGuests: boolean;
  AllowBots: boolean;
  OnlyMods: boolean;
  NoChatAllowed: boolean;
  NoPlayingAllowed: boolean;
  FilterProfanity: boolean;
  Meta: RoomSettingsMeta | undefined;
  StageDetailsJSON?: string | undefined;
  HostDetails?: RoomHostDetails | undefined;
  StageDetails: RoomStageDetails | undefined;
}

export interface RoomFullDetails {
  roomName: string;
  roomID: string;
  settings: RoomSettings | undefined;
}

export interface RoomSettingsBasicDto {
  MaxPlayers: number;
  MaxChatHistory: number;
  RoomStatus: RoomStatus;
  OnlyOwnerCanPlay: boolean;
  OnlyOwnerCanChat: boolean;
  AllowBlackMidi: boolean;
  AllowGuests: boolean;
  AllowBots: boolean;
  OnlyMods: boolean;
  NoChatAllowed: boolean;
  NoPlayingAllowed: boolean;
  FilterProfanity: boolean;
  HostDetails?: RoomHostDetails | undefined;
  StageDetails: RoomStageDetails | undefined;
}

export interface RoomProfileBasicInfoDto {
  roomName: string;
  roomID: string;
  roomOwner: string;
  settings: RoomSettingsBasicDto | undefined;
  stageDetailsJSON?: string | undefined;
  roomStage: RoomStages;
  StageDetails: RoomStageDetails | undefined;
}

function createBaseRoomHostDetails(): RoomHostDetails {
  return { ContinentCode: "", CountryCode: undefined, NoteBufferInterval: undefined };
}

export const RoomHostDetails = {
  encode(message: RoomHostDetails, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.ContinentCode !== "") {
      writer.uint32(10).string(message.ContinentCode);
    }
    if (message.CountryCode !== undefined) {
      writer.uint32(18).string(message.CountryCode);
    }
    if (message.NoteBufferInterval !== undefined) {
      writer.uint32(24).uint32(message.NoteBufferInterval);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomHostDetails {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomHostDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.ContinentCode = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.CountryCode = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.NoteBufferInterval = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomHostDetails {
    return {
      ContinentCode: isSet(object.ContinentCode) ? globalThis.String(object.ContinentCode) : "",
      CountryCode: isSet(object.CountryCode) ? globalThis.String(object.CountryCode) : undefined,
      NoteBufferInterval: isSet(object.NoteBufferInterval) ? globalThis.Number(object.NoteBufferInterval) : undefined,
    };
  },

  toJSON(message: RoomHostDetails): unknown {
    const obj: any = {};
    if (message.ContinentCode !== "") {
      obj.ContinentCode = message.ContinentCode;
    }
    if (message.CountryCode !== undefined) {
      obj.CountryCode = message.CountryCode;
    }
    if (message.NoteBufferInterval !== undefined) {
      obj.NoteBufferInterval = Math.round(message.NoteBufferInterval);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomHostDetails>, I>>(base?: I): RoomHostDetails {
    return RoomHostDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomHostDetails>, I>>(object: I): RoomHostDetails {
    const message = createBaseRoomHostDetails();
    message.ContinentCode = object.ContinentCode ?? "";
    message.CountryCode = object.CountryCode ?? undefined;
    message.NoteBufferInterval = object.NoteBufferInterval ?? undefined;
    return message;
  },
};

function createBaseRoomStageVisualEffects(): RoomStageVisualEffects {
  return {
    rain: undefined,
    grass: undefined,
    postProcessBlackWhite: undefined,
    postProcessDownSample: undefined,
    postProcessToneMap: undefined,
    postProcessEmboss: undefined,
    postProcessVignette: undefined,
  };
}

export const RoomStageVisualEffects = {
  encode(message: RoomStageVisualEffects, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.rain !== undefined) {
      writer.uint32(8).bool(message.rain);
    }
    if (message.grass !== undefined) {
      writer.uint32(16).bool(message.grass);
    }
    if (message.postProcessBlackWhite !== undefined) {
      writer.uint32(24).bool(message.postProcessBlackWhite);
    }
    if (message.postProcessDownSample !== undefined) {
      writer.uint32(32).bool(message.postProcessDownSample);
    }
    if (message.postProcessToneMap !== undefined) {
      writer.uint32(40).bool(message.postProcessToneMap);
    }
    if (message.postProcessEmboss !== undefined) {
      writer.uint32(48).bool(message.postProcessEmboss);
    }
    if (message.postProcessVignette !== undefined) {
      writer.uint32(56).bool(message.postProcessVignette);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomStageVisualEffects {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomStageVisualEffects();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.rain = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.grass = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.postProcessBlackWhite = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.postProcessDownSample = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.postProcessToneMap = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.postProcessEmboss = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.postProcessVignette = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomStageVisualEffects {
    return {
      rain: isSet(object.rain) ? globalThis.Boolean(object.rain) : undefined,
      grass: isSet(object.grass) ? globalThis.Boolean(object.grass) : undefined,
      postProcessBlackWhite: isSet(object.postProcessBlackWhite)
        ? globalThis.Boolean(object.postProcessBlackWhite)
        : undefined,
      postProcessDownSample: isSet(object.postProcessDownSample)
        ? globalThis.Boolean(object.postProcessDownSample)
        : undefined,
      postProcessToneMap: isSet(object.postProcessToneMap) ? globalThis.Boolean(object.postProcessToneMap) : undefined,
      postProcessEmboss: isSet(object.postProcessEmboss) ? globalThis.Boolean(object.postProcessEmboss) : undefined,
      postProcessVignette: isSet(object.postProcessVignette)
        ? globalThis.Boolean(object.postProcessVignette)
        : undefined,
    };
  },

  toJSON(message: RoomStageVisualEffects): unknown {
    const obj: any = {};
    if (message.rain !== undefined) {
      obj.rain = message.rain;
    }
    if (message.grass !== undefined) {
      obj.grass = message.grass;
    }
    if (message.postProcessBlackWhite !== undefined) {
      obj.postProcessBlackWhite = message.postProcessBlackWhite;
    }
    if (message.postProcessDownSample !== undefined) {
      obj.postProcessDownSample = message.postProcessDownSample;
    }
    if (message.postProcessToneMap !== undefined) {
      obj.postProcessToneMap = message.postProcessToneMap;
    }
    if (message.postProcessEmboss !== undefined) {
      obj.postProcessEmboss = message.postProcessEmboss;
    }
    if (message.postProcessVignette !== undefined) {
      obj.postProcessVignette = message.postProcessVignette;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomStageVisualEffects>, I>>(base?: I): RoomStageVisualEffects {
    return RoomStageVisualEffects.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomStageVisualEffects>, I>>(object: I): RoomStageVisualEffects {
    const message = createBaseRoomStageVisualEffects();
    message.rain = object.rain ?? undefined;
    message.grass = object.grass ?? undefined;
    message.postProcessBlackWhite = object.postProcessBlackWhite ?? undefined;
    message.postProcessDownSample = object.postProcessDownSample ?? undefined;
    message.postProcessToneMap = object.postProcessToneMap ?? undefined;
    message.postProcessEmboss = object.postProcessEmboss ?? undefined;
    message.postProcessVignette = object.postProcessVignette ?? undefined;
    return message;
  },
};

function createBaseRoomStageAudioEffect(): RoomStageAudioEffect {
  return { volume: 0, loop: false, playbackRate: 0, pan: 0 };
}

export const RoomStageAudioEffect = {
  encode(message: RoomStageAudioEffect, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.volume !== 0) {
      writer.uint32(13).float(message.volume);
    }
    if (message.loop !== false) {
      writer.uint32(16).bool(message.loop);
    }
    if (message.playbackRate !== 0) {
      writer.uint32(29).float(message.playbackRate);
    }
    if (message.pan !== 0) {
      writer.uint32(37).float(message.pan);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomStageAudioEffect {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomStageAudioEffect();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }

          message.volume = reader.float();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.loop = reader.bool();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.playbackRate = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.pan = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomStageAudioEffect {
    return {
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : 0,
      loop: isSet(object.loop) ? globalThis.Boolean(object.loop) : false,
      playbackRate: isSet(object.playbackRate) ? globalThis.Number(object.playbackRate) : 0,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : 0,
    };
  },

  toJSON(message: RoomStageAudioEffect): unknown {
    const obj: any = {};
    if (message.volume !== 0) {
      obj.volume = message.volume;
    }
    if (message.loop !== false) {
      obj.loop = message.loop;
    }
    if (message.playbackRate !== 0) {
      obj.playbackRate = message.playbackRate;
    }
    if (message.pan !== 0) {
      obj.pan = message.pan;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomStageAudioEffect>, I>>(base?: I): RoomStageAudioEffect {
    return RoomStageAudioEffect.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomStageAudioEffect>, I>>(object: I): RoomStageAudioEffect {
    const message = createBaseRoomStageAudioEffect();
    message.volume = object.volume ?? 0;
    message.loop = object.loop ?? false;
    message.playbackRate = object.playbackRate ?? 0;
    message.pan = object.pan ?? 0;
    return message;
  },
};

function createBaseRoomStageAudioEffects(): RoomStageAudioEffects {
  return { rain: undefined, wind: undefined, birds: undefined };
}

export const RoomStageAudioEffects = {
  encode(message: RoomStageAudioEffects, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.rain !== undefined) {
      RoomStageAudioEffect.encode(message.rain, writer.uint32(10).fork()).ldelim();
    }
    if (message.wind !== undefined) {
      RoomStageAudioEffect.encode(message.wind, writer.uint32(18).fork()).ldelim();
    }
    if (message.birds !== undefined) {
      RoomStageAudioEffect.encode(message.birds, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomStageAudioEffects {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomStageAudioEffects();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.rain = RoomStageAudioEffect.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.wind = RoomStageAudioEffect.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.birds = RoomStageAudioEffect.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomStageAudioEffects {
    return {
      rain: isSet(object.rain) ? RoomStageAudioEffect.fromJSON(object.rain) : undefined,
      wind: isSet(object.wind) ? RoomStageAudioEffect.fromJSON(object.wind) : undefined,
      birds: isSet(object.birds) ? RoomStageAudioEffect.fromJSON(object.birds) : undefined,
    };
  },

  toJSON(message: RoomStageAudioEffects): unknown {
    const obj: any = {};
    if (message.rain !== undefined) {
      obj.rain = RoomStageAudioEffect.toJSON(message.rain);
    }
    if (message.wind !== undefined) {
      obj.wind = RoomStageAudioEffect.toJSON(message.wind);
    }
    if (message.birds !== undefined) {
      obj.birds = RoomStageAudioEffect.toJSON(message.birds);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomStageAudioEffects>, I>>(base?: I): RoomStageAudioEffects {
    return RoomStageAudioEffects.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomStageAudioEffects>, I>>(object: I): RoomStageAudioEffects {
    const message = createBaseRoomStageAudioEffects();
    message.rain = (object.rain !== undefined && object.rain !== null)
      ? RoomStageAudioEffect.fromPartial(object.rain)
      : undefined;
    message.wind = (object.wind !== undefined && object.wind !== null)
      ? RoomStageAudioEffect.fromPartial(object.wind)
      : undefined;
    message.birds = (object.birds !== undefined && object.birds !== null)
      ? RoomStageAudioEffect.fromPartial(object.birds)
      : undefined;
    return message;
  },
};

function createBaseRoomStageDetails(): RoomStageDetails {
  return { stage: 0, effects: undefined, audioEffects: undefined };
}

export const RoomStageDetails = {
  encode(message: RoomStageDetails, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.stage !== 0) {
      writer.uint32(8).int32(message.stage);
    }
    if (message.effects !== undefined) {
      RoomStageVisualEffects.encode(message.effects, writer.uint32(18).fork()).ldelim();
    }
    if (message.audioEffects !== undefined) {
      RoomStageAudioEffects.encode(message.audioEffects, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomStageDetails {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomStageDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.stage = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.effects = RoomStageVisualEffects.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.audioEffects = RoomStageAudioEffects.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomStageDetails {
    return {
      stage: isSet(object.stage) ? roomStagesFromJSON(object.stage) : 0,
      effects: isSet(object.effects) ? RoomStageVisualEffects.fromJSON(object.effects) : undefined,
      audioEffects: isSet(object.audioEffects) ? RoomStageAudioEffects.fromJSON(object.audioEffects) : undefined,
    };
  },

  toJSON(message: RoomStageDetails): unknown {
    const obj: any = {};
    if (message.stage !== 0) {
      obj.stage = roomStagesToJSON(message.stage);
    }
    if (message.effects !== undefined) {
      obj.effects = RoomStageVisualEffects.toJSON(message.effects);
    }
    if (message.audioEffects !== undefined) {
      obj.audioEffects = RoomStageAudioEffects.toJSON(message.audioEffects);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomStageDetails>, I>>(base?: I): RoomStageDetails {
    return RoomStageDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomStageDetails>, I>>(object: I): RoomStageDetails {
    const message = createBaseRoomStageDetails();
    message.stage = object.stage ?? 0;
    message.effects = (object.effects !== undefined && object.effects !== null)
      ? RoomStageVisualEffects.fromPartial(object.effects)
      : undefined;
    message.audioEffects = (object.audioEffects !== undefined && object.audioEffects !== null)
      ? RoomStageAudioEffects.fromPartial(object.audioEffects)
      : undefined;
    return message;
  },
};

function createBaseRoomSettingsMeta(): RoomSettingsMeta {
  return { AutoRemove: false, Unique: false, Persistent: false };
}

export const RoomSettingsMeta = {
  encode(message: RoomSettingsMeta, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.AutoRemove !== false) {
      writer.uint32(8).bool(message.AutoRemove);
    }
    if (message.Unique !== false) {
      writer.uint32(16).bool(message.Unique);
    }
    if (message.Persistent !== false) {
      writer.uint32(24).bool(message.Persistent);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomSettingsMeta {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomSettingsMeta();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.AutoRemove = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.Unique = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.Persistent = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomSettingsMeta {
    return {
      AutoRemove: isSet(object.AutoRemove) ? globalThis.Boolean(object.AutoRemove) : false,
      Unique: isSet(object.Unique) ? globalThis.Boolean(object.Unique) : false,
      Persistent: isSet(object.Persistent) ? globalThis.Boolean(object.Persistent) : false,
    };
  },

  toJSON(message: RoomSettingsMeta): unknown {
    const obj: any = {};
    if (message.AutoRemove !== false) {
      obj.AutoRemove = message.AutoRemove;
    }
    if (message.Unique !== false) {
      obj.Unique = message.Unique;
    }
    if (message.Persistent !== false) {
      obj.Persistent = message.Persistent;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomSettingsMeta>, I>>(base?: I): RoomSettingsMeta {
    return RoomSettingsMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomSettingsMeta>, I>>(object: I): RoomSettingsMeta {
    const message = createBaseRoomSettingsMeta();
    message.AutoRemove = object.AutoRemove ?? false;
    message.Unique = object.Unique ?? false;
    message.Persistent = object.Persistent ?? false;
    return message;
  },
};

function createBaseBasicRoomDto(): BasicRoomDto {
  return {
    roomName: "",
    roomID: "",
    roomType: "",
    userCount: 0,
    maxUsers: 0,
    isPasswordProtected: false,
    hostDetails: undefined,
    roomOwner: "",
    roomStage: 0,
    StageDetails: undefined,
    createdDate: "",
  };
}

export const BasicRoomDto = {
  encode(message: BasicRoomDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomName !== "") {
      writer.uint32(10).string(message.roomName);
    }
    if (message.roomID !== "") {
      writer.uint32(18).string(message.roomID);
    }
    if (message.roomType !== "") {
      writer.uint32(26).string(message.roomType);
    }
    if (message.userCount !== 0) {
      writer.uint32(32).int32(message.userCount);
    }
    if (message.maxUsers !== 0) {
      writer.uint32(40).int32(message.maxUsers);
    }
    if (message.isPasswordProtected !== false) {
      writer.uint32(48).bool(message.isPasswordProtected);
    }
    if (message.hostDetails !== undefined) {
      RoomHostDetails.encode(message.hostDetails, writer.uint32(58).fork()).ldelim();
    }
    if (message.roomOwner !== "") {
      writer.uint32(66).string(message.roomOwner);
    }
    if (message.roomStage !== 0) {
      writer.uint32(72).int32(message.roomStage);
    }
    if (message.StageDetails !== undefined) {
      RoomStageDetails.encode(message.StageDetails, writer.uint32(82).fork()).ldelim();
    }
    if (message.createdDate !== "") {
      writer.uint32(90).string(message.createdDate);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): BasicRoomDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseBasicRoomDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomType = reader.string();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.userCount = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.maxUsers = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.isPasswordProtected = reader.bool();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.hostDetails = RoomHostDetails.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.roomOwner = reader.string();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.roomStage = reader.int32() as any;
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.StageDetails = RoomStageDetails.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.createdDate = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): BasicRoomDto {
    return {
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      roomType: isSet(object.roomType) ? globalThis.String(object.roomType) : "",
      userCount: isSet(object.userCount) ? globalThis.Number(object.userCount) : 0,
      maxUsers: isSet(object.maxUsers) ? globalThis.Number(object.maxUsers) : 0,
      isPasswordProtected: isSet(object.isPasswordProtected) ? globalThis.Boolean(object.isPasswordProtected) : false,
      hostDetails: isSet(object.hostDetails) ? RoomHostDetails.fromJSON(object.hostDetails) : undefined,
      roomOwner: isSet(object.roomOwner) ? globalThis.String(object.roomOwner) : "",
      roomStage: isSet(object.roomStage) ? roomStagesFromJSON(object.roomStage) : 0,
      StageDetails: isSet(object.StageDetails) ? RoomStageDetails.fromJSON(object.StageDetails) : undefined,
      createdDate: isSet(object.createdDate) ? globalThis.String(object.createdDate) : "",
    };
  },

  toJSON(message: BasicRoomDto): unknown {
    const obj: any = {};
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.roomType !== "") {
      obj.roomType = message.roomType;
    }
    if (message.userCount !== 0) {
      obj.userCount = Math.round(message.userCount);
    }
    if (message.maxUsers !== 0) {
      obj.maxUsers = Math.round(message.maxUsers);
    }
    if (message.isPasswordProtected !== false) {
      obj.isPasswordProtected = message.isPasswordProtected;
    }
    if (message.hostDetails !== undefined) {
      obj.hostDetails = RoomHostDetails.toJSON(message.hostDetails);
    }
    if (message.roomOwner !== "") {
      obj.roomOwner = message.roomOwner;
    }
    if (message.roomStage !== 0) {
      obj.roomStage = roomStagesToJSON(message.roomStage);
    }
    if (message.StageDetails !== undefined) {
      obj.StageDetails = RoomStageDetails.toJSON(message.StageDetails);
    }
    if (message.createdDate !== "") {
      obj.createdDate = message.createdDate;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<BasicRoomDto>, I>>(base?: I): BasicRoomDto {
    return BasicRoomDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BasicRoomDto>, I>>(object: I): BasicRoomDto {
    const message = createBaseBasicRoomDto();
    message.roomName = object.roomName ?? "";
    message.roomID = object.roomID ?? "";
    message.roomType = object.roomType ?? "";
    message.userCount = object.userCount ?? 0;
    message.maxUsers = object.maxUsers ?? 0;
    message.isPasswordProtected = object.isPasswordProtected ?? false;
    message.hostDetails = (object.hostDetails !== undefined && object.hostDetails !== null)
      ? RoomHostDetails.fromPartial(object.hostDetails)
      : undefined;
    message.roomOwner = object.roomOwner ?? "";
    message.roomStage = object.roomStage ?? 0;
    message.StageDetails = (object.StageDetails !== undefined && object.StageDetails !== null)
      ? RoomStageDetails.fromPartial(object.StageDetails)
      : undefined;
    message.createdDate = object.createdDate ?? "";
    return message;
  },
};

function createBaseRoomDto(): RoomDto {
  return { roomName: "", roomID: "", roomOwner: "", users: [] };
}

export const RoomDto = {
  encode(message: RoomDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomName !== "") {
      writer.uint32(10).string(message.roomName);
    }
    if (message.roomID !== "") {
      writer.uint32(18).string(message.roomID);
    }
    if (message.roomOwner !== "") {
      writer.uint32(26).string(message.roomOwner);
    }
    for (const v of message.users) {
      writer.uint32(34).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomOwner = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.users.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomDto {
    return {
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      roomOwner: isSet(object.roomOwner) ? globalThis.String(object.roomOwner) : "",
      users: globalThis.Array.isArray(object?.users) ? object.users.map((e: any) => globalThis.String(e)) : [],
    };
  },

  toJSON(message: RoomDto): unknown {
    const obj: any = {};
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.roomOwner !== "") {
      obj.roomOwner = message.roomOwner;
    }
    if (message.users?.length) {
      obj.users = message.users;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomDto>, I>>(base?: I): RoomDto {
    return RoomDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomDto>, I>>(object: I): RoomDto {
    const message = createBaseRoomDto();
    message.roomName = object.roomName ?? "";
    message.roomID = object.roomID ?? "";
    message.roomOwner = object.roomOwner ?? "";
    message.users = object.users?.map((e) => e) || [];
    return message;
  },
};

function createBaseRoomSettings(): RoomSettings {
  return {
    WelcomeMessage: undefined,
    MaxPlayers: 0,
    MaxChatHistory: 0,
    RoomOwner: "",
    RoomStatus: 0,
    RoomType: 0,
    Password: undefined,
    OnlyOwnerCanPlay: false,
    OnlyOwnerCanChat: false,
    AllowBlackMidi: false,
    AllowGuests: false,
    AllowBots: false,
    OnlyMods: false,
    NoChatAllowed: false,
    NoPlayingAllowed: false,
    FilterProfanity: false,
    Meta: undefined,
    StageDetailsJSON: undefined,
    HostDetails: undefined,
    StageDetails: undefined,
  };
}

export const RoomSettings = {
  encode(message: RoomSettings, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.WelcomeMessage !== undefined) {
      writer.uint32(10).string(message.WelcomeMessage);
    }
    if (message.MaxPlayers !== 0) {
      writer.uint32(16).int32(message.MaxPlayers);
    }
    if (message.MaxChatHistory !== 0) {
      writer.uint32(24).int32(message.MaxChatHistory);
    }
    if (message.RoomOwner !== "") {
      writer.uint32(34).string(message.RoomOwner);
    }
    if (message.RoomStatus !== 0) {
      writer.uint32(40).int32(message.RoomStatus);
    }
    if (message.RoomType !== 0) {
      writer.uint32(48).int32(message.RoomType);
    }
    if (message.Password !== undefined) {
      writer.uint32(58).string(message.Password);
    }
    if (message.OnlyOwnerCanPlay !== false) {
      writer.uint32(64).bool(message.OnlyOwnerCanPlay);
    }
    if (message.OnlyOwnerCanChat !== false) {
      writer.uint32(72).bool(message.OnlyOwnerCanChat);
    }
    if (message.AllowBlackMidi !== false) {
      writer.uint32(80).bool(message.AllowBlackMidi);
    }
    if (message.AllowGuests !== false) {
      writer.uint32(88).bool(message.AllowGuests);
    }
    if (message.AllowBots !== false) {
      writer.uint32(96).bool(message.AllowBots);
    }
    if (message.OnlyMods !== false) {
      writer.uint32(104).bool(message.OnlyMods);
    }
    if (message.NoChatAllowed !== false) {
      writer.uint32(112).bool(message.NoChatAllowed);
    }
    if (message.NoPlayingAllowed !== false) {
      writer.uint32(120).bool(message.NoPlayingAllowed);
    }
    if (message.FilterProfanity !== false) {
      writer.uint32(128).bool(message.FilterProfanity);
    }
    if (message.Meta !== undefined) {
      RoomSettingsMeta.encode(message.Meta, writer.uint32(138).fork()).ldelim();
    }
    if (message.StageDetailsJSON !== undefined) {
      writer.uint32(146).string(message.StageDetailsJSON);
    }
    if (message.HostDetails !== undefined) {
      RoomHostDetails.encode(message.HostDetails, writer.uint32(154).fork()).ldelim();
    }
    if (message.StageDetails !== undefined) {
      RoomStageDetails.encode(message.StageDetails, writer.uint32(170).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomSettings {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.WelcomeMessage = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.MaxPlayers = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.MaxChatHistory = reader.int32();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.RoomOwner = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.RoomStatus = reader.int32() as any;
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.RoomType = reader.int32() as any;
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.Password = reader.string();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.OnlyOwnerCanPlay = reader.bool();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.OnlyOwnerCanChat = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.AllowBlackMidi = reader.bool();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.AllowGuests = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.AllowBots = reader.bool();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.OnlyMods = reader.bool();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.NoChatAllowed = reader.bool();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.NoPlayingAllowed = reader.bool();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.FilterProfanity = reader.bool();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.Meta = RoomSettingsMeta.decode(reader, reader.uint32());
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.StageDetailsJSON = reader.string();
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.HostDetails = RoomHostDetails.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.StageDetails = RoomStageDetails.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomSettings {
    return {
      WelcomeMessage: isSet(object.WelcomeMessage) ? globalThis.String(object.WelcomeMessage) : undefined,
      MaxPlayers: isSet(object.MaxPlayers) ? globalThis.Number(object.MaxPlayers) : 0,
      MaxChatHistory: isSet(object.MaxChatHistory) ? globalThis.Number(object.MaxChatHistory) : 0,
      RoomOwner: isSet(object.RoomOwner) ? globalThis.String(object.RoomOwner) : "",
      RoomStatus: isSet(object.RoomStatus) ? roomStatusFromJSON(object.RoomStatus) : 0,
      RoomType: isSet(object.RoomType) ? roomTypeFromJSON(object.RoomType) : 0,
      Password: isSet(object.Password) ? globalThis.String(object.Password) : undefined,
      OnlyOwnerCanPlay: isSet(object.OnlyOwnerCanPlay) ? globalThis.Boolean(object.OnlyOwnerCanPlay) : false,
      OnlyOwnerCanChat: isSet(object.OnlyOwnerCanChat) ? globalThis.Boolean(object.OnlyOwnerCanChat) : false,
      AllowBlackMidi: isSet(object.AllowBlackMidi) ? globalThis.Boolean(object.AllowBlackMidi) : false,
      AllowGuests: isSet(object.AllowGuests) ? globalThis.Boolean(object.AllowGuests) : false,
      AllowBots: isSet(object.AllowBots) ? globalThis.Boolean(object.AllowBots) : false,
      OnlyMods: isSet(object.OnlyMods) ? globalThis.Boolean(object.OnlyMods) : false,
      NoChatAllowed: isSet(object.NoChatAllowed) ? globalThis.Boolean(object.NoChatAllowed) : false,
      NoPlayingAllowed: isSet(object.NoPlayingAllowed) ? globalThis.Boolean(object.NoPlayingAllowed) : false,
      FilterProfanity: isSet(object.FilterProfanity) ? globalThis.Boolean(object.FilterProfanity) : false,
      Meta: isSet(object.Meta) ? RoomSettingsMeta.fromJSON(object.Meta) : undefined,
      StageDetailsJSON: isSet(object.StageDetailsJSON) ? globalThis.String(object.StageDetailsJSON) : undefined,
      HostDetails: isSet(object.HostDetails) ? RoomHostDetails.fromJSON(object.HostDetails) : undefined,
      StageDetails: isSet(object.StageDetails) ? RoomStageDetails.fromJSON(object.StageDetails) : undefined,
    };
  },

  toJSON(message: RoomSettings): unknown {
    const obj: any = {};
    if (message.WelcomeMessage !== undefined) {
      obj.WelcomeMessage = message.WelcomeMessage;
    }
    if (message.MaxPlayers !== 0) {
      obj.MaxPlayers = Math.round(message.MaxPlayers);
    }
    if (message.MaxChatHistory !== 0) {
      obj.MaxChatHistory = Math.round(message.MaxChatHistory);
    }
    if (message.RoomOwner !== "") {
      obj.RoomOwner = message.RoomOwner;
    }
    if (message.RoomStatus !== 0) {
      obj.RoomStatus = roomStatusToJSON(message.RoomStatus);
    }
    if (message.RoomType !== 0) {
      obj.RoomType = roomTypeToJSON(message.RoomType);
    }
    if (message.Password !== undefined) {
      obj.Password = message.Password;
    }
    if (message.OnlyOwnerCanPlay !== false) {
      obj.OnlyOwnerCanPlay = message.OnlyOwnerCanPlay;
    }
    if (message.OnlyOwnerCanChat !== false) {
      obj.OnlyOwnerCanChat = message.OnlyOwnerCanChat;
    }
    if (message.AllowBlackMidi !== false) {
      obj.AllowBlackMidi = message.AllowBlackMidi;
    }
    if (message.AllowGuests !== false) {
      obj.AllowGuests = message.AllowGuests;
    }
    if (message.AllowBots !== false) {
      obj.AllowBots = message.AllowBots;
    }
    if (message.OnlyMods !== false) {
      obj.OnlyMods = message.OnlyMods;
    }
    if (message.NoChatAllowed !== false) {
      obj.NoChatAllowed = message.NoChatAllowed;
    }
    if (message.NoPlayingAllowed !== false) {
      obj.NoPlayingAllowed = message.NoPlayingAllowed;
    }
    if (message.FilterProfanity !== false) {
      obj.FilterProfanity = message.FilterProfanity;
    }
    if (message.Meta !== undefined) {
      obj.Meta = RoomSettingsMeta.toJSON(message.Meta);
    }
    if (message.StageDetailsJSON !== undefined) {
      obj.StageDetailsJSON = message.StageDetailsJSON;
    }
    if (message.HostDetails !== undefined) {
      obj.HostDetails = RoomHostDetails.toJSON(message.HostDetails);
    }
    if (message.StageDetails !== undefined) {
      obj.StageDetails = RoomStageDetails.toJSON(message.StageDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomSettings>, I>>(base?: I): RoomSettings {
    return RoomSettings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomSettings>, I>>(object: I): RoomSettings {
    const message = createBaseRoomSettings();
    message.WelcomeMessage = object.WelcomeMessage ?? undefined;
    message.MaxPlayers = object.MaxPlayers ?? 0;
    message.MaxChatHistory = object.MaxChatHistory ?? 0;
    message.RoomOwner = object.RoomOwner ?? "";
    message.RoomStatus = object.RoomStatus ?? 0;
    message.RoomType = object.RoomType ?? 0;
    message.Password = object.Password ?? undefined;
    message.OnlyOwnerCanPlay = object.OnlyOwnerCanPlay ?? false;
    message.OnlyOwnerCanChat = object.OnlyOwnerCanChat ?? false;
    message.AllowBlackMidi = object.AllowBlackMidi ?? false;
    message.AllowGuests = object.AllowGuests ?? false;
    message.AllowBots = object.AllowBots ?? false;
    message.OnlyMods = object.OnlyMods ?? false;
    message.NoChatAllowed = object.NoChatAllowed ?? false;
    message.NoPlayingAllowed = object.NoPlayingAllowed ?? false;
    message.FilterProfanity = object.FilterProfanity ?? false;
    message.Meta = (object.Meta !== undefined && object.Meta !== null)
      ? RoomSettingsMeta.fromPartial(object.Meta)
      : undefined;
    message.StageDetailsJSON = object.StageDetailsJSON ?? undefined;
    message.HostDetails = (object.HostDetails !== undefined && object.HostDetails !== null)
      ? RoomHostDetails.fromPartial(object.HostDetails)
      : undefined;
    message.StageDetails = (object.StageDetails !== undefined && object.StageDetails !== null)
      ? RoomStageDetails.fromPartial(object.StageDetails)
      : undefined;
    return message;
  },
};

function createBaseRoomFullDetails(): RoomFullDetails {
  return { roomName: "", roomID: "", settings: undefined };
}

export const RoomFullDetails = {
  encode(message: RoomFullDetails, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomName !== "") {
      writer.uint32(10).string(message.roomName);
    }
    if (message.roomID !== "") {
      writer.uint32(18).string(message.roomID);
    }
    if (message.settings !== undefined) {
      RoomSettings.encode(message.settings, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomFullDetails {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomFullDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.settings = RoomSettings.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomFullDetails {
    return {
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      settings: isSet(object.settings) ? RoomSettings.fromJSON(object.settings) : undefined,
    };
  },

  toJSON(message: RoomFullDetails): unknown {
    const obj: any = {};
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.settings !== undefined) {
      obj.settings = RoomSettings.toJSON(message.settings);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomFullDetails>, I>>(base?: I): RoomFullDetails {
    return RoomFullDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomFullDetails>, I>>(object: I): RoomFullDetails {
    const message = createBaseRoomFullDetails();
    message.roomName = object.roomName ?? "";
    message.roomID = object.roomID ?? "";
    message.settings = (object.settings !== undefined && object.settings !== null)
      ? RoomSettings.fromPartial(object.settings)
      : undefined;
    return message;
  },
};

function createBaseRoomSettingsBasicDto(): RoomSettingsBasicDto {
  return {
    MaxPlayers: 0,
    MaxChatHistory: 0,
    RoomStatus: 0,
    OnlyOwnerCanPlay: false,
    OnlyOwnerCanChat: false,
    AllowBlackMidi: false,
    AllowGuests: false,
    AllowBots: false,
    OnlyMods: false,
    NoChatAllowed: false,
    NoPlayingAllowed: false,
    FilterProfanity: false,
    HostDetails: undefined,
    StageDetails: undefined,
  };
}

export const RoomSettingsBasicDto = {
  encode(message: RoomSettingsBasicDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.MaxPlayers !== 0) {
      writer.uint32(8).int32(message.MaxPlayers);
    }
    if (message.MaxChatHistory !== 0) {
      writer.uint32(16).int32(message.MaxChatHistory);
    }
    if (message.RoomStatus !== 0) {
      writer.uint32(24).int32(message.RoomStatus);
    }
    if (message.OnlyOwnerCanPlay !== false) {
      writer.uint32(32).bool(message.OnlyOwnerCanPlay);
    }
    if (message.OnlyOwnerCanChat !== false) {
      writer.uint32(40).bool(message.OnlyOwnerCanChat);
    }
    if (message.AllowBlackMidi !== false) {
      writer.uint32(48).bool(message.AllowBlackMidi);
    }
    if (message.AllowGuests !== false) {
      writer.uint32(56).bool(message.AllowGuests);
    }
    if (message.AllowBots !== false) {
      writer.uint32(64).bool(message.AllowBots);
    }
    if (message.OnlyMods !== false) {
      writer.uint32(72).bool(message.OnlyMods);
    }
    if (message.NoChatAllowed !== false) {
      writer.uint32(80).bool(message.NoChatAllowed);
    }
    if (message.NoPlayingAllowed !== false) {
      writer.uint32(88).bool(message.NoPlayingAllowed);
    }
    if (message.FilterProfanity !== false) {
      writer.uint32(96).bool(message.FilterProfanity);
    }
    if (message.HostDetails !== undefined) {
      RoomHostDetails.encode(message.HostDetails, writer.uint32(106).fork()).ldelim();
    }
    if (message.StageDetails !== undefined) {
      RoomStageDetails.encode(message.StageDetails, writer.uint32(114).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomSettingsBasicDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomSettingsBasicDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.MaxPlayers = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.MaxChatHistory = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.RoomStatus = reader.int32() as any;
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.OnlyOwnerCanPlay = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.OnlyOwnerCanChat = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.AllowBlackMidi = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.AllowGuests = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.AllowBots = reader.bool();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.OnlyMods = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.NoChatAllowed = reader.bool();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.NoPlayingAllowed = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.FilterProfanity = reader.bool();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.HostDetails = RoomHostDetails.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.StageDetails = RoomStageDetails.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomSettingsBasicDto {
    return {
      MaxPlayers: isSet(object.MaxPlayers) ? globalThis.Number(object.MaxPlayers) : 0,
      MaxChatHistory: isSet(object.MaxChatHistory) ? globalThis.Number(object.MaxChatHistory) : 0,
      RoomStatus: isSet(object.RoomStatus) ? roomStatusFromJSON(object.RoomStatus) : 0,
      OnlyOwnerCanPlay: isSet(object.OnlyOwnerCanPlay) ? globalThis.Boolean(object.OnlyOwnerCanPlay) : false,
      OnlyOwnerCanChat: isSet(object.OnlyOwnerCanChat) ? globalThis.Boolean(object.OnlyOwnerCanChat) : false,
      AllowBlackMidi: isSet(object.AllowBlackMidi) ? globalThis.Boolean(object.AllowBlackMidi) : false,
      AllowGuests: isSet(object.AllowGuests) ? globalThis.Boolean(object.AllowGuests) : false,
      AllowBots: isSet(object.AllowBots) ? globalThis.Boolean(object.AllowBots) : false,
      OnlyMods: isSet(object.OnlyMods) ? globalThis.Boolean(object.OnlyMods) : false,
      NoChatAllowed: isSet(object.NoChatAllowed) ? globalThis.Boolean(object.NoChatAllowed) : false,
      NoPlayingAllowed: isSet(object.NoPlayingAllowed) ? globalThis.Boolean(object.NoPlayingAllowed) : false,
      FilterProfanity: isSet(object.FilterProfanity) ? globalThis.Boolean(object.FilterProfanity) : false,
      HostDetails: isSet(object.HostDetails) ? RoomHostDetails.fromJSON(object.HostDetails) : undefined,
      StageDetails: isSet(object.StageDetails) ? RoomStageDetails.fromJSON(object.StageDetails) : undefined,
    };
  },

  toJSON(message: RoomSettingsBasicDto): unknown {
    const obj: any = {};
    if (message.MaxPlayers !== 0) {
      obj.MaxPlayers = Math.round(message.MaxPlayers);
    }
    if (message.MaxChatHistory !== 0) {
      obj.MaxChatHistory = Math.round(message.MaxChatHistory);
    }
    if (message.RoomStatus !== 0) {
      obj.RoomStatus = roomStatusToJSON(message.RoomStatus);
    }
    if (message.OnlyOwnerCanPlay !== false) {
      obj.OnlyOwnerCanPlay = message.OnlyOwnerCanPlay;
    }
    if (message.OnlyOwnerCanChat !== false) {
      obj.OnlyOwnerCanChat = message.OnlyOwnerCanChat;
    }
    if (message.AllowBlackMidi !== false) {
      obj.AllowBlackMidi = message.AllowBlackMidi;
    }
    if (message.AllowGuests !== false) {
      obj.AllowGuests = message.AllowGuests;
    }
    if (message.AllowBots !== false) {
      obj.AllowBots = message.AllowBots;
    }
    if (message.OnlyMods !== false) {
      obj.OnlyMods = message.OnlyMods;
    }
    if (message.NoChatAllowed !== false) {
      obj.NoChatAllowed = message.NoChatAllowed;
    }
    if (message.NoPlayingAllowed !== false) {
      obj.NoPlayingAllowed = message.NoPlayingAllowed;
    }
    if (message.FilterProfanity !== false) {
      obj.FilterProfanity = message.FilterProfanity;
    }
    if (message.HostDetails !== undefined) {
      obj.HostDetails = RoomHostDetails.toJSON(message.HostDetails);
    }
    if (message.StageDetails !== undefined) {
      obj.StageDetails = RoomStageDetails.toJSON(message.StageDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomSettingsBasicDto>, I>>(base?: I): RoomSettingsBasicDto {
    return RoomSettingsBasicDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomSettingsBasicDto>, I>>(object: I): RoomSettingsBasicDto {
    const message = createBaseRoomSettingsBasicDto();
    message.MaxPlayers = object.MaxPlayers ?? 0;
    message.MaxChatHistory = object.MaxChatHistory ?? 0;
    message.RoomStatus = object.RoomStatus ?? 0;
    message.OnlyOwnerCanPlay = object.OnlyOwnerCanPlay ?? false;
    message.OnlyOwnerCanChat = object.OnlyOwnerCanChat ?? false;
    message.AllowBlackMidi = object.AllowBlackMidi ?? false;
    message.AllowGuests = object.AllowGuests ?? false;
    message.AllowBots = object.AllowBots ?? false;
    message.OnlyMods = object.OnlyMods ?? false;
    message.NoChatAllowed = object.NoChatAllowed ?? false;
    message.NoPlayingAllowed = object.NoPlayingAllowed ?? false;
    message.FilterProfanity = object.FilterProfanity ?? false;
    message.HostDetails = (object.HostDetails !== undefined && object.HostDetails !== null)
      ? RoomHostDetails.fromPartial(object.HostDetails)
      : undefined;
    message.StageDetails = (object.StageDetails !== undefined && object.StageDetails !== null)
      ? RoomStageDetails.fromPartial(object.StageDetails)
      : undefined;
    return message;
  },
};

function createBaseRoomProfileBasicInfoDto(): RoomProfileBasicInfoDto {
  return {
    roomName: "",
    roomID: "",
    roomOwner: "",
    settings: undefined,
    stageDetailsJSON: undefined,
    roomStage: 0,
    StageDetails: undefined,
  };
}

export const RoomProfileBasicInfoDto = {
  encode(message: RoomProfileBasicInfoDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomName !== "") {
      writer.uint32(10).string(message.roomName);
    }
    if (message.roomID !== "") {
      writer.uint32(18).string(message.roomID);
    }
    if (message.roomOwner !== "") {
      writer.uint32(26).string(message.roomOwner);
    }
    if (message.settings !== undefined) {
      RoomSettingsBasicDto.encode(message.settings, writer.uint32(34).fork()).ldelim();
    }
    if (message.stageDetailsJSON !== undefined) {
      writer.uint32(42).string(message.stageDetailsJSON);
    }
    if (message.roomStage !== 0) {
      writer.uint32(48).int32(message.roomStage);
    }
    if (message.StageDetails !== undefined) {
      RoomStageDetails.encode(message.StageDetails, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomProfileBasicInfoDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomProfileBasicInfoDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomOwner = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.settings = RoomSettingsBasicDto.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.stageDetailsJSON = reader.string();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.roomStage = reader.int32() as any;
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.StageDetails = RoomStageDetails.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomProfileBasicInfoDto {
    return {
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      roomOwner: isSet(object.roomOwner) ? globalThis.String(object.roomOwner) : "",
      settings: isSet(object.settings) ? RoomSettingsBasicDto.fromJSON(object.settings) : undefined,
      stageDetailsJSON: isSet(object.stageDetailsJSON) ? globalThis.String(object.stageDetailsJSON) : undefined,
      roomStage: isSet(object.roomStage) ? roomStagesFromJSON(object.roomStage) : 0,
      StageDetails: isSet(object.StageDetails) ? RoomStageDetails.fromJSON(object.StageDetails) : undefined,
    };
  },

  toJSON(message: RoomProfileBasicInfoDto): unknown {
    const obj: any = {};
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.roomOwner !== "") {
      obj.roomOwner = message.roomOwner;
    }
    if (message.settings !== undefined) {
      obj.settings = RoomSettingsBasicDto.toJSON(message.settings);
    }
    if (message.stageDetailsJSON !== undefined) {
      obj.stageDetailsJSON = message.stageDetailsJSON;
    }
    if (message.roomStage !== 0) {
      obj.roomStage = roomStagesToJSON(message.roomStage);
    }
    if (message.StageDetails !== undefined) {
      obj.StageDetails = RoomStageDetails.toJSON(message.StageDetails);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomProfileBasicInfoDto>, I>>(base?: I): RoomProfileBasicInfoDto {
    return RoomProfileBasicInfoDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomProfileBasicInfoDto>, I>>(object: I): RoomProfileBasicInfoDto {
    const message = createBaseRoomProfileBasicInfoDto();
    message.roomName = object.roomName ?? "";
    message.roomID = object.roomID ?? "";
    message.roomOwner = object.roomOwner ?? "";
    message.settings = (object.settings !== undefined && object.settings !== null)
      ? RoomSettingsBasicDto.fromPartial(object.settings)
      : undefined;
    message.stageDetailsJSON = object.stageDetailsJSON ?? undefined;
    message.roomStage = object.roomStage ?? 0;
    message.StageDetails = (object.StageDetails !== undefined && object.StageDetails !== null)
      ? RoomStageDetails.fromPartial(object.StageDetails)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
