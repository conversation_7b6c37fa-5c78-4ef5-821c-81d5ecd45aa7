// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: pianorhythm-app-renditions.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";
import { ActiveChannelsMode, activeChannelsModeFromJSON, activeChannelsModeToJSON } from "./midi-renditions";
import {
  RoomStages,
  roomStagesFromJSON,
  roomStagesToJSON,
  RoomType,
  roomTypeFromJSON,
  roomTypeToJSON,
} from "./room-renditions";

export const protobufPackage = "PianoRhythm.AppRenditions";

export enum AudioSynthesizerEngine {
  OXISYNTH = 0,
  RUSTYSYNTH = 1,
  UNRECOGNIZED = -1,
}

export function audioSynthesizerEngineFromJSON(object: any): AudioSynthesizerEngine {
  switch (object) {
    case 0:
    case "OXISYNTH":
      return AudioSynthesizerEngine.OXISYNTH;
    case 1:
    case "RUSTYSYNTH":
      return AudioSynthesizerEngine.RUSTYSYNTH;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AudioSynthesizerEngine.UNRECOGNIZED;
  }
}

export function audioSynthesizerEngineToJSON(object: AudioSynthesizerEngine): string {
  switch (object) {
    case AudioSynthesizerEngine.OXISYNTH:
      return "OXISYNTH";
    case AudioSynthesizerEngine.RUSTYSYNTH:
      return "RUSTYSYNTH";
    case AudioSynthesizerEngine.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum EqualizerPreset {
  Flat = 0,
  Acoustic = 1,
  Electronic = 2,
  Latin = 3,
  Piano = 4,
  Pop = 5,
  Rock = 6,
  BassBooster = 7,
  Custom = 8,
  UNRECOGNIZED = -1,
}

export function equalizerPresetFromJSON(object: any): EqualizerPreset {
  switch (object) {
    case 0:
    case "Flat":
      return EqualizerPreset.Flat;
    case 1:
    case "Acoustic":
      return EqualizerPreset.Acoustic;
    case 2:
    case "Electronic":
      return EqualizerPreset.Electronic;
    case 3:
    case "Latin":
      return EqualizerPreset.Latin;
    case 4:
    case "Piano":
      return EqualizerPreset.Piano;
    case 5:
    case "Pop":
      return EqualizerPreset.Pop;
    case 6:
    case "Rock":
      return EqualizerPreset.Rock;
    case 7:
    case "BassBooster":
      return EqualizerPreset.BassBooster;
    case 8:
    case "Custom":
      return EqualizerPreset.Custom;
    case -1:
    case "UNRECOGNIZED":
    default:
      return EqualizerPreset.UNRECOGNIZED;
  }
}

export function equalizerPresetToJSON(object: EqualizerPreset): string {
  switch (object) {
    case EqualizerPreset.Flat:
      return "Flat";
    case EqualizerPreset.Acoustic:
      return "Acoustic";
    case EqualizerPreset.Electronic:
      return "Electronic";
    case EqualizerPreset.Latin:
      return "Latin";
    case EqualizerPreset.Piano:
      return "Piano";
    case EqualizerPreset.Pop:
      return "Pop";
    case EqualizerPreset.Rock:
      return "Rock";
    case EqualizerPreset.BassBooster:
      return "BassBooster";
    case EqualizerPreset.Custom:
      return "Custom";
    case EqualizerPreset.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GraphicShadowFilteringMethod {
  ShadowFiltering_Hardware2x2 = 0,
  ShadowFiltering_Gaussian = 1,
  ShadowFiltering_Temporal = 2,
  UNRECOGNIZED = -1,
}

export function graphicShadowFilteringMethodFromJSON(object: any): GraphicShadowFilteringMethod {
  switch (object) {
    case 0:
    case "ShadowFiltering_Hardware2x2":
      return GraphicShadowFilteringMethod.ShadowFiltering_Hardware2x2;
    case 1:
    case "ShadowFiltering_Gaussian":
      return GraphicShadowFilteringMethod.ShadowFiltering_Gaussian;
    case 2:
    case "ShadowFiltering_Temporal":
      return GraphicShadowFilteringMethod.ShadowFiltering_Temporal;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GraphicShadowFilteringMethod.UNRECOGNIZED;
  }
}

export function graphicShadowFilteringMethodToJSON(object: GraphicShadowFilteringMethod): string {
  switch (object) {
    case GraphicShadowFilteringMethod.ShadowFiltering_Hardware2x2:
      return "ShadowFiltering_Hardware2x2";
    case GraphicShadowFilteringMethod.ShadowFiltering_Gaussian:
      return "ShadowFiltering_Gaussian";
    case GraphicShadowFilteringMethod.ShadowFiltering_Temporal:
      return "ShadowFiltering_Temporal";
    case GraphicShadowFilteringMethod.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GraphicsPresets {
  Preset_None = 0,
  Preset_Custom = 1,
  Preset_Low = 2,
  Preset_Medium = 3,
  Preset_High = 4,
  Preset_Ultra = 5,
  UNRECOGNIZED = -1,
}

export function graphicsPresetsFromJSON(object: any): GraphicsPresets {
  switch (object) {
    case 0:
    case "Preset_None":
      return GraphicsPresets.Preset_None;
    case 1:
    case "Preset_Custom":
      return GraphicsPresets.Preset_Custom;
    case 2:
    case "Preset_Low":
      return GraphicsPresets.Preset_Low;
    case 3:
    case "Preset_Medium":
      return GraphicsPresets.Preset_Medium;
    case 4:
    case "Preset_High":
      return GraphicsPresets.Preset_High;
    case 5:
    case "Preset_Ultra":
      return GraphicsPresets.Preset_Ultra;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GraphicsPresets.UNRECOGNIZED;
  }
}

export function graphicsPresetsToJSON(object: GraphicsPresets): string {
  switch (object) {
    case GraphicsPresets.Preset_None:
      return "Preset_None";
    case GraphicsPresets.Preset_Custom:
      return "Preset_Custom";
    case GraphicsPresets.Preset_Low:
      return "Preset_Low";
    case GraphicsPresets.Preset_Medium:
      return "Preset_Medium";
    case GraphicsPresets.Preset_High:
      return "Preset_High";
    case GraphicsPresets.Preset_Ultra:
      return "Preset_Ultra";
    case GraphicsPresets.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum GraphicsMsaaSamples {
  Msaa_Off = 0,
  Msaa_Sample2 = 1,
  /** Msaa_Sample4 - Only one supported in web */
  Msaa_Sample4 = 2,
  Msaa_Sample8 = 3,
  UNRECOGNIZED = -1,
}

export function graphicsMsaaSamplesFromJSON(object: any): GraphicsMsaaSamples {
  switch (object) {
    case 0:
    case "Msaa_Off":
      return GraphicsMsaaSamples.Msaa_Off;
    case 1:
    case "Msaa_Sample2":
      return GraphicsMsaaSamples.Msaa_Sample2;
    case 2:
    case "Msaa_Sample4":
      return GraphicsMsaaSamples.Msaa_Sample4;
    case 3:
    case "Msaa_Sample8":
      return GraphicsMsaaSamples.Msaa_Sample8;
    case -1:
    case "UNRECOGNIZED":
    default:
      return GraphicsMsaaSamples.UNRECOGNIZED;
  }
}

export function graphicsMsaaSamplesToJSON(object: GraphicsMsaaSamples): string {
  switch (object) {
    case GraphicsMsaaSamples.Msaa_Off:
      return "Msaa_Off";
    case GraphicsMsaaSamples.Msaa_Sample2:
      return "Msaa_Sample2";
    case GraphicsMsaaSamples.Msaa_Sample4:
      return "Msaa_Sample4";
    case GraphicsMsaaSamples.Msaa_Sample8:
      return "Msaa_Sample8";
    case GraphicsMsaaSamples.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppPianoKeyType {
  Black = 0,
  White = 1,
  UNRECOGNIZED = -1,
}

export function appPianoKeyTypeFromJSON(object: any): AppPianoKeyType {
  switch (object) {
    case 0:
    case "Black":
      return AppPianoKeyType.Black;
    case 1:
    case "White":
      return AppPianoKeyType.White;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppPianoKeyType.UNRECOGNIZED;
  }
}

export function appPianoKeyTypeToJSON(object: AppPianoKeyType): string {
  switch (object) {
    case AppPianoKeyType.Black:
      return "Black";
    case AppPianoKeyType.White:
      return "White";
    case AppPianoKeyType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppPianoPedalType {
  Sustain = 0,
  Dampen = 1,
  UNRECOGNIZED = -1,
}

export function appPianoPedalTypeFromJSON(object: any): AppPianoPedalType {
  switch (object) {
    case 0:
    case "Sustain":
      return AppPianoPedalType.Sustain;
    case 1:
    case "Dampen":
      return AppPianoPedalType.Dampen;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppPianoPedalType.UNRECOGNIZED;
  }
}

export function appPianoPedalTypeToJSON(object: AppPianoPedalType): string {
  switch (object) {
    case AppPianoPedalType.Sustain:
      return "Sustain";
    case AppPianoPedalType.Dampen:
      return "Dampen";
    case AppPianoPedalType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppNotificationConfigStatus {
  NotificationSuccess = 0,
  NotificationInfo = 1,
  NotificationWarning = 2,
  NotificationDanger = 3,
  UNRECOGNIZED = -1,
}

export function appNotificationConfigStatusFromJSON(object: any): AppNotificationConfigStatus {
  switch (object) {
    case 0:
    case "NotificationSuccess":
      return AppNotificationConfigStatus.NotificationSuccess;
    case 1:
    case "NotificationInfo":
      return AppNotificationConfigStatus.NotificationInfo;
    case 2:
    case "NotificationWarning":
      return AppNotificationConfigStatus.NotificationWarning;
    case 3:
    case "NotificationDanger":
      return AppNotificationConfigStatus.NotificationDanger;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppNotificationConfigStatus.UNRECOGNIZED;
  }
}

export function appNotificationConfigStatusToJSON(object: AppNotificationConfigStatus): string {
  switch (object) {
    case AppNotificationConfigStatus.NotificationSuccess:
      return "NotificationSuccess";
    case AppNotificationConfigStatus.NotificationInfo:
      return "NotificationInfo";
    case AppNotificationConfigStatus.NotificationWarning:
      return "NotificationWarning";
    case AppNotificationConfigStatus.NotificationDanger:
      return "NotificationDanger";
    case AppNotificationConfigStatus.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppMidiSequencerEventType {
  UNKNOWN = 0,
  DATA = 1,
  LYRIC = 2,
  TEXT = 3,
  MARKER_TEXT = 4,
  COPYRIGHT_NOTICE = 5,
  INSTRUMENT_NAME = 6,
  TRACK_NAME = 7,
  TEMPO_CHANGE = 8,
  LOOP_START = 9,
  LOOP_END = 10,
  END_OF_TRACK = 11,
  CURRENT_TIME = 12,
  FILE_NAME = 13,
  FILE_OUTPUT = 14,
  TOTAL_TIME_CHANGE = 15,
  FINISHED = 16,
  STOPPED = 17,
  PROGRAM_CHANGE = 18,
  SEEK_POSITION_CHANGED = 19,
  FILE_TEMPO_CHANGE = 20,
  UNRECOGNIZED = -1,
}

export function appMidiSequencerEventTypeFromJSON(object: any): AppMidiSequencerEventType {
  switch (object) {
    case 0:
    case "UNKNOWN":
      return AppMidiSequencerEventType.UNKNOWN;
    case 1:
    case "DATA":
      return AppMidiSequencerEventType.DATA;
    case 2:
    case "LYRIC":
      return AppMidiSequencerEventType.LYRIC;
    case 3:
    case "TEXT":
      return AppMidiSequencerEventType.TEXT;
    case 4:
    case "MARKER_TEXT":
      return AppMidiSequencerEventType.MARKER_TEXT;
    case 5:
    case "COPYRIGHT_NOTICE":
      return AppMidiSequencerEventType.COPYRIGHT_NOTICE;
    case 6:
    case "INSTRUMENT_NAME":
      return AppMidiSequencerEventType.INSTRUMENT_NAME;
    case 7:
    case "TRACK_NAME":
      return AppMidiSequencerEventType.TRACK_NAME;
    case 8:
    case "TEMPO_CHANGE":
      return AppMidiSequencerEventType.TEMPO_CHANGE;
    case 9:
    case "LOOP_START":
      return AppMidiSequencerEventType.LOOP_START;
    case 10:
    case "LOOP_END":
      return AppMidiSequencerEventType.LOOP_END;
    case 11:
    case "END_OF_TRACK":
      return AppMidiSequencerEventType.END_OF_TRACK;
    case 12:
    case "CURRENT_TIME":
      return AppMidiSequencerEventType.CURRENT_TIME;
    case 13:
    case "FILE_NAME":
      return AppMidiSequencerEventType.FILE_NAME;
    case 14:
    case "FILE_OUTPUT":
      return AppMidiSequencerEventType.FILE_OUTPUT;
    case 15:
    case "TOTAL_TIME_CHANGE":
      return AppMidiSequencerEventType.TOTAL_TIME_CHANGE;
    case 16:
    case "FINISHED":
      return AppMidiSequencerEventType.FINISHED;
    case 17:
    case "STOPPED":
      return AppMidiSequencerEventType.STOPPED;
    case 18:
    case "PROGRAM_CHANGE":
      return AppMidiSequencerEventType.PROGRAM_CHANGE;
    case 19:
    case "SEEK_POSITION_CHANGED":
      return AppMidiSequencerEventType.SEEK_POSITION_CHANGED;
    case 20:
    case "FILE_TEMPO_CHANGE":
      return AppMidiSequencerEventType.FILE_TEMPO_CHANGE;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppMidiSequencerEventType.UNRECOGNIZED;
  }
}

export function appMidiSequencerEventTypeToJSON(object: AppMidiSequencerEventType): string {
  switch (object) {
    case AppMidiSequencerEventType.UNKNOWN:
      return "UNKNOWN";
    case AppMidiSequencerEventType.DATA:
      return "DATA";
    case AppMidiSequencerEventType.LYRIC:
      return "LYRIC";
    case AppMidiSequencerEventType.TEXT:
      return "TEXT";
    case AppMidiSequencerEventType.MARKER_TEXT:
      return "MARKER_TEXT";
    case AppMidiSequencerEventType.COPYRIGHT_NOTICE:
      return "COPYRIGHT_NOTICE";
    case AppMidiSequencerEventType.INSTRUMENT_NAME:
      return "INSTRUMENT_NAME";
    case AppMidiSequencerEventType.TRACK_NAME:
      return "TRACK_NAME";
    case AppMidiSequencerEventType.TEMPO_CHANGE:
      return "TEMPO_CHANGE";
    case AppMidiSequencerEventType.LOOP_START:
      return "LOOP_START";
    case AppMidiSequencerEventType.LOOP_END:
      return "LOOP_END";
    case AppMidiSequencerEventType.END_OF_TRACK:
      return "END_OF_TRACK";
    case AppMidiSequencerEventType.CURRENT_TIME:
      return "CURRENT_TIME";
    case AppMidiSequencerEventType.FILE_NAME:
      return "FILE_NAME";
    case AppMidiSequencerEventType.FILE_OUTPUT:
      return "FILE_OUTPUT";
    case AppMidiSequencerEventType.TOTAL_TIME_CHANGE:
      return "TOTAL_TIME_CHANGE";
    case AppMidiSequencerEventType.FINISHED:
      return "FINISHED";
    case AppMidiSequencerEventType.STOPPED:
      return "STOPPED";
    case AppMidiSequencerEventType.PROGRAM_CHANGE:
      return "PROGRAM_CHANGE";
    case AppMidiSequencerEventType.SEEK_POSITION_CHANGED:
      return "SEEK_POSITION_CHANGED";
    case AppMidiSequencerEventType.FILE_TEMPO_CHANGE:
      return "FILE_TEMPO_CHANGE";
    case AppMidiSequencerEventType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum AppSceneMode {
  ThreeD = 0,
  TwoD = 1,
  GameMode = 2,
  WorldMode = 3,
  Unknown = 4,
  UNRECOGNIZED = -1,
}

export function appSceneModeFromJSON(object: any): AppSceneMode {
  switch (object) {
    case 0:
    case "ThreeD":
      return AppSceneMode.ThreeD;
    case 1:
    case "TwoD":
      return AppSceneMode.TwoD;
    case 2:
    case "GameMode":
      return AppSceneMode.GameMode;
    case 3:
    case "WorldMode":
      return AppSceneMode.WorldMode;
    case 4:
    case "Unknown":
      return AppSceneMode.Unknown;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppSceneMode.UNRECOGNIZED;
  }
}

export function appSceneModeToJSON(object: AppSceneMode): string {
  switch (object) {
    case AppSceneMode.ThreeD:
      return "ThreeD";
    case AppSceneMode.TwoD:
      return "TwoD";
    case AppSceneMode.GameMode:
      return "GameMode";
    case AppSceneMode.WorldMode:
      return "WorldMode";
    case AppSceneMode.Unknown:
      return "Unknown";
    case AppSceneMode.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AppSettings {
  defaultLanguage: string;
  displayPiano: boolean;
  displayFps: boolean;
  discordSync: boolean;
  displayPing: boolean;
  display3dStats: boolean;
  displayCursors: boolean;
  displayInstDock: boolean;
  displayChat: boolean;
  displaySceneWidgetButtons: boolean;
  displayWhoistyping: boolean;
  keepChatInFocus: boolean;
  instDockTranspose: number;
  instDockOctave: number;
  instDockOpacity: number;
  volumeSaved: number;
  audioMaxPolyphony: number;
  audioVelMuting: number;
  audioSustainCutoff: number;
  audioSustainedNoteFadeoutDuration: number;
  audioVelBoost: number;
  audioEnableVelBoost: boolean;
  audioEnableVel: boolean;
  audioEnableReverb: boolean;
  audioMidiOutputOnly: boolean;
  audioMousePosSetsVelocity: boolean;
  audioEnableDrumChannel: boolean;
  audioBufferSize: number;
  audioOutputOwnNotesToMidi: boolean;
  chatAutoScroll: boolean;
  showEmbeddedLinks: boolean;
  midiListenToProgramChanges: boolean;
  midiAutoFillEmptyChannels: boolean;
  midiUseDefaultBankWhenMissing: boolean;
  audioUseDefaultInstrumentWhenMissingForOtherUsers: boolean;
  midiEnableStereoPanning: boolean;
  /** repeated Keybind KEYBINDS = 37; */
  graphicsOnlyShowPianoKeys: boolean;
  graphicsDisplayRenderStats: boolean;
  graphicsEnableAvatars: boolean;
  graphicsEnablePhysics: boolean;
  graphicsEnableSpecialEffects: boolean;
  graphicsEnableAutoAnimateToInstruments: boolean;
  graphicsEnableAntialias: boolean;
  graphicsEnableOrchestraModels: boolean;
  graphicsRenderEvenInBackground: boolean;
  graphicsEnableAmbientOcclusion: boolean;
  graphicsEnableDepthOfField: boolean;
  graphicsUseOffscreenCanvas: boolean;
  chatDisableMarkdown: boolean;
  chatEnableImageUrlPreview: boolean;
  enableDesktopNotifications: boolean;
  allowUsersToNotifyMe: boolean;
  audioSampleRate: number;
  audioIgnoreSoundfontVolumeEnvDelay: boolean;
  audioIgnoreSoundfontVolumeEnvDelay2: boolean;
  audioUseSeparateDrumKit: boolean;
  audioReverbLevel: number;
  audioReverbDamp: number;
  audioReverbWidth: number;
  audioReverbRoomsize: number;
  audioCacheSoundfontsWeb: boolean;
  desktopSaveSoundfonts: boolean;
  /** AppThemes UI_THEME = 70; */
  uiEnableUserNoteActivities: boolean;
  webglPowerPreference: string;
  enableDebugMode: boolean;
  autoCheckForUpdates: boolean;
  audioEqualizerPreset: EqualizerPreset;
  gameManiaDisableBackground: boolean;
  audioSfxEnable: boolean;
  audioSfxGlobalVolume: number;
  audioSfxStageEffectsGlobalVolume: number;
  audioSfxHoverVolume: number;
  audioSfxChatEnable: boolean;
  audioEnableStageSfx: boolean;
  audioSynthEngine: AudioSynthesizerEngine;
  audioGlobalUsersVelocityPercentage: number;
  audioMultimodeMaxChannels: number;
  appAutoLoginEnable: boolean;
  audioBgMusicEnable: boolean;
  audioBgMusicGlobalVolume: number;
  onlineWarnAboutExternalLinks: boolean;
  enablePlugins: boolean;
  keyboardShiftKeyAutoNoteOff: boolean;
  audioReverbDefaultMigratedVersion: string;
  graphicsEnableFog: boolean;
  graphicsEnableShadows: boolean;
  graphicsEnableStage: boolean;
  graphicsEnablePiano: boolean;
  graphicsEnableMotionBlur: boolean;
  graphicsEnableGlow: boolean;
  graphicsEnableDrums: boolean;
  graphicsEnableAllParticles: boolean;
  audioUseWorklet: boolean;
  uiEnableSynthNoteActivities: boolean;
  graphicsEnableSoftShadows: boolean;
  graphicsEnablePostProcessing: boolean;
  graphicsTargetFps: string;
  graphicsEnableLights: boolean;
  graphicsEnableBloom: boolean;
  graphicsEnableToneMapping: boolean;
  graphicsEnableHdr: boolean;
  graphicsEnableWebgpu: boolean;
  graphicsEnableAnimations: boolean;
  graphicsMsaaSamples: GraphicsMsaaSamples;
  graphicsPreset: GraphicsPresets;
  graphicsShadowFilter: GraphicShadowFilteringMethod;
  graphicsEnableEngine: boolean;
  graphicsUseLowPolyModels: boolean;
  graphicsEnableGuitars: boolean;
  chatMessagesMinimized: boolean;
  mutedNotesSelf: boolean;
  sendWhoistyping: boolean;
  chatMessagesMaximized: boolean;
  audioEnableEqualizer: boolean;
  audioMaxNoteOnTime: number;
  audioMaxVelocity: number;
  audioMinVelocity: number;
  audioMinVolumeRelease: number;
  inputCtrlKeyLowersOctave: boolean;
  inputMidiToQwertyMod: boolean;
  inputMidiToQwertyModUseCapslock: boolean;
  slotMode: ActiveChannelsMode;
  audioUseVelocityCurve: boolean;
}

export interface AppCommonEnvironment {
  offlineDevMode: boolean;
  colyseusHost: string;
  colyseusPort: string;
  HOST: string;
  wsHost: string;
  docsHost: string;
  fullHost: string;
  seqUrl: string;
  statusPageUrl: string;
  expressApiHost: string;
  metricsUrl: string;
  websocketProtocol: string;
  PORT: number;
  assetsUrl: string;
  assetsProxyUrl: string;
  MODE: string;
  sentryDsn: string;
  isLocalDev: boolean;
  isTestMode: boolean;
  isDevMode: boolean;
  isDesktopApp: boolean;
  isWebApp: boolean;
  isProduction: boolean;
  isStaging: boolean;
  isAutomatedTestMode: boolean;
  clientVersion: string;
  clientBuildDate: string;
  rendererWasmFilePath: string;
  rendererJsFilePath: string;
  synthJsFilePath: string;
  coreJsFilePath: string;
  maniaJsFilePath: string;
  dawJsFilePath: string;
  soundfontCachePrefix: string;
}

export interface AppPianoKey {
  keyType: AppPianoKeyType;
  midiID: number;
  positionX?: number | undefined;
  positionY?: number | undefined;
  positionZ?: number | undefined;
  rotationX?: number | undefined;
  rotationY?: number | undefined;
  rotationZ?: number | undefined;
  colorR?: number | undefined;
  colorG?: number | undefined;
  colorB?: number | undefined;
  colorA?: number | undefined;
}

export interface AppPianoPedal {
  id: string;
  name?: string | undefined;
  positionX?: number | undefined;
  positionY?: number | undefined;
  positionZ?: number | undefined;
  rotationX?: number | undefined;
  rotationY?: number | undefined;
  rotationZ?: number | undefined;
  colorR?: number | undefined;
  colorG?: number | undefined;
  colorB?: number | undefined;
  colorA?: number | undefined;
  type: AppPianoPedalType;
}

export interface AppRenderableEntity {
  id: string;
  name?: string | undefined;
  positionX?: number | undefined;
  positionY?: number | undefined;
  positionZ?: number | undefined;
  rotationX?: number | undefined;
  rotationY?: number | undefined;
  rotationZ?: number | undefined;
  colorR?: number | undefined;
  colorG?: number | undefined;
  colorB?: number | undefined;
  colorA?: number | undefined;
  alpha?: number | undefined;
  beta?: number | undefined;
  radius?: number | undefined;
  targetX?: number | undefined;
  targetY?: number | undefined;
  targetZ?: number | undefined;
  scaleX?: number | undefined;
  scaleY?: number | undefined;
  scaleZ?: number | undefined;
  visible?: boolean | undefined;
}

export interface AppPageloaderDetail {
  active: boolean;
  details?: string | undefined;
}

export interface AppNotificationConfig {
  id: string;
  status?: AppNotificationConfigStatus | undefined;
  title?: string | undefined;
  description?: string | undefined;
  duration?: number | undefined;
  persistent?: boolean | undefined;
  closable?: boolean | undefined;
  loading?: boolean | undefined;
}

export interface AppMidiSequencerProgramChange {
  channel: number;
  program: number;
  time: number;
  tick: number;
  bank: number;
}

export interface AppMidiSequencerTempoChange {
  time: number;
  tempo: number;
}

export interface AppMidiSequencerEvent {
  eventType: AppMidiSequencerEventType;
  dataStr?: string | undefined;
  dataFloat?: number | undefined;
  channel?: number | undefined;
  command?: number | undefined;
  data1?: number | undefined;
  data2?: number | undefined;
  lyrics: string[];
  fileName?: string | undefined;
  totalTime?: number | undefined;
  trackNames: string[];
  currentBPM?: number | undefined;
  copyrightNotice: string[];
  texts: string[];
  markerTexts: string[];
  tick?: number | undefined;
  programChanges: AppMidiSequencerProgramChange[];
  tempoChanges: AppMidiSequencerTempoChange[];
  ppq?: number | undefined;
  isVPSheet?: boolean | undefined;
  index?: number | undefined;
  time?: number | undefined;
}

export interface AppVPSequencerTrack {
  index: number;
  tempo?: number | undefined;
  name?: string | undefined;
}

export interface AppVPSequencerFileLoad {
  data: string;
  fileName?: string | undefined;
  tracks: AppVPSequencerTrack[];
}

export interface AppMidiSequencerFileLoad {
  data: number[];
  fileName?: string | undefined;
}

export interface AppMidiTrack {
  index: number;
  name?: string | undefined;
  volume?: number | undefined;
  pan?: number | undefined;
  active: boolean;
  hasData: boolean;
  playing: boolean;
  recording: boolean;
}

export interface AppRoomStageLoaded {
  roomStage: RoomStages;
  roomType: RoomType;
}

export interface AppKeyboardMappingVisualize {
  note: number;
  key: string;
}

export interface AppKeyboardMappingVisualizeVec {
  mappings: AppKeyboardMappingVisualize[];
}

export interface AppThemeColors {
  primary: string;
  accent: string;
  tertiary: string;
}

function createBaseAppSettings(): AppSettings {
  return {
    defaultLanguage: "",
    displayPiano: false,
    displayFps: false,
    discordSync: false,
    displayPing: false,
    display3dStats: false,
    displayCursors: false,
    displayInstDock: false,
    displayChat: false,
    displaySceneWidgetButtons: false,
    displayWhoistyping: false,
    keepChatInFocus: false,
    instDockTranspose: 0,
    instDockOctave: 0,
    instDockOpacity: 0,
    volumeSaved: 0,
    audioMaxPolyphony: 0,
    audioVelMuting: 0,
    audioSustainCutoff: 0,
    audioSustainedNoteFadeoutDuration: 0,
    audioVelBoost: 0,
    audioEnableVelBoost: false,
    audioEnableVel: false,
    audioEnableReverb: false,
    audioMidiOutputOnly: false,
    audioMousePosSetsVelocity: false,
    audioEnableDrumChannel: false,
    audioBufferSize: 0,
    audioOutputOwnNotesToMidi: false,
    chatAutoScroll: false,
    showEmbeddedLinks: false,
    midiListenToProgramChanges: false,
    midiAutoFillEmptyChannels: false,
    midiUseDefaultBankWhenMissing: false,
    audioUseDefaultInstrumentWhenMissingForOtherUsers: false,
    midiEnableStereoPanning: false,
    graphicsOnlyShowPianoKeys: false,
    graphicsDisplayRenderStats: false,
    graphicsEnableAvatars: false,
    graphicsEnablePhysics: false,
    graphicsEnableSpecialEffects: false,
    graphicsEnableAutoAnimateToInstruments: false,
    graphicsEnableAntialias: false,
    graphicsEnableOrchestraModels: false,
    graphicsRenderEvenInBackground: false,
    graphicsEnableAmbientOcclusion: false,
    graphicsEnableDepthOfField: false,
    graphicsUseOffscreenCanvas: false,
    chatDisableMarkdown: false,
    chatEnableImageUrlPreview: false,
    enableDesktopNotifications: false,
    allowUsersToNotifyMe: false,
    audioSampleRate: 0,
    audioIgnoreSoundfontVolumeEnvDelay: false,
    audioIgnoreSoundfontVolumeEnvDelay2: false,
    audioUseSeparateDrumKit: false,
    audioReverbLevel: 0,
    audioReverbDamp: 0,
    audioReverbWidth: 0,
    audioReverbRoomsize: 0,
    audioCacheSoundfontsWeb: false,
    desktopSaveSoundfonts: false,
    uiEnableUserNoteActivities: false,
    webglPowerPreference: "",
    enableDebugMode: false,
    autoCheckForUpdates: false,
    audioEqualizerPreset: 0,
    gameManiaDisableBackground: false,
    audioSfxEnable: false,
    audioSfxGlobalVolume: 0,
    audioSfxStageEffectsGlobalVolume: 0,
    audioSfxHoverVolume: 0,
    audioSfxChatEnable: false,
    audioEnableStageSfx: false,
    audioSynthEngine: 0,
    audioGlobalUsersVelocityPercentage: 0,
    audioMultimodeMaxChannels: 0,
    appAutoLoginEnable: false,
    audioBgMusicEnable: false,
    audioBgMusicGlobalVolume: 0,
    onlineWarnAboutExternalLinks: false,
    enablePlugins: false,
    keyboardShiftKeyAutoNoteOff: false,
    audioReverbDefaultMigratedVersion: "",
    graphicsEnableFog: false,
    graphicsEnableShadows: false,
    graphicsEnableStage: false,
    graphicsEnablePiano: false,
    graphicsEnableMotionBlur: false,
    graphicsEnableGlow: false,
    graphicsEnableDrums: false,
    graphicsEnableAllParticles: false,
    audioUseWorklet: false,
    uiEnableSynthNoteActivities: false,
    graphicsEnableSoftShadows: false,
    graphicsEnablePostProcessing: false,
    graphicsTargetFps: "",
    graphicsEnableLights: false,
    graphicsEnableBloom: false,
    graphicsEnableToneMapping: false,
    graphicsEnableHdr: false,
    graphicsEnableWebgpu: false,
    graphicsEnableAnimations: false,
    graphicsMsaaSamples: 0,
    graphicsPreset: 0,
    graphicsShadowFilter: 0,
    graphicsEnableEngine: false,
    graphicsUseLowPolyModels: false,
    graphicsEnableGuitars: false,
    chatMessagesMinimized: false,
    mutedNotesSelf: false,
    sendWhoistyping: false,
    chatMessagesMaximized: false,
    audioEnableEqualizer: false,
    audioMaxNoteOnTime: 0,
    audioMaxVelocity: 0,
    audioMinVelocity: 0,
    audioMinVolumeRelease: 0,
    inputCtrlKeyLowersOctave: false,
    inputMidiToQwertyMod: false,
    inputMidiToQwertyModUseCapslock: false,
    slotMode: 0,
    audioUseVelocityCurve: false,
  };
}

export const AppSettings = {
  encode(message: AppSettings, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.defaultLanguage !== "") {
      writer.uint32(10).string(message.defaultLanguage);
    }
    if (message.displayPiano !== false) {
      writer.uint32(16).bool(message.displayPiano);
    }
    if (message.displayFps !== false) {
      writer.uint32(24).bool(message.displayFps);
    }
    if (message.discordSync !== false) {
      writer.uint32(32).bool(message.discordSync);
    }
    if (message.displayPing !== false) {
      writer.uint32(40).bool(message.displayPing);
    }
    if (message.display3dStats !== false) {
      writer.uint32(48).bool(message.display3dStats);
    }
    if (message.displayCursors !== false) {
      writer.uint32(56).bool(message.displayCursors);
    }
    if (message.displayInstDock !== false) {
      writer.uint32(64).bool(message.displayInstDock);
    }
    if (message.displayChat !== false) {
      writer.uint32(72).bool(message.displayChat);
    }
    if (message.displaySceneWidgetButtons !== false) {
      writer.uint32(80).bool(message.displaySceneWidgetButtons);
    }
    if (message.displayWhoistyping !== false) {
      writer.uint32(88).bool(message.displayWhoistyping);
    }
    if (message.keepChatInFocus !== false) {
      writer.uint32(96).bool(message.keepChatInFocus);
    }
    if (message.instDockTranspose !== 0) {
      writer.uint32(104).int32(message.instDockTranspose);
    }
    if (message.instDockOctave !== 0) {
      writer.uint32(112).int32(message.instDockOctave);
    }
    if (message.instDockOpacity !== 0) {
      writer.uint32(125).float(message.instDockOpacity);
    }
    if (message.volumeSaved !== 0) {
      writer.uint32(133).float(message.volumeSaved);
    }
    if (message.audioMaxPolyphony !== 0) {
      writer.uint32(136).int32(message.audioMaxPolyphony);
    }
    if (message.audioVelMuting !== 0) {
      writer.uint32(149).float(message.audioVelMuting);
    }
    if (message.audioSustainCutoff !== 0) {
      writer.uint32(157).float(message.audioSustainCutoff);
    }
    if (message.audioSustainedNoteFadeoutDuration !== 0) {
      writer.uint32(165).float(message.audioSustainedNoteFadeoutDuration);
    }
    if (message.audioVelBoost !== 0) {
      writer.uint32(173).float(message.audioVelBoost);
    }
    if (message.audioEnableVelBoost !== false) {
      writer.uint32(176).bool(message.audioEnableVelBoost);
    }
    if (message.audioEnableVel !== false) {
      writer.uint32(184).bool(message.audioEnableVel);
    }
    if (message.audioEnableReverb !== false) {
      writer.uint32(192).bool(message.audioEnableReverb);
    }
    if (message.audioMidiOutputOnly !== false) {
      writer.uint32(200).bool(message.audioMidiOutputOnly);
    }
    if (message.audioMousePosSetsVelocity !== false) {
      writer.uint32(208).bool(message.audioMousePosSetsVelocity);
    }
    if (message.audioEnableDrumChannel !== false) {
      writer.uint32(216).bool(message.audioEnableDrumChannel);
    }
    if (message.audioBufferSize !== 0) {
      writer.uint32(224).int32(message.audioBufferSize);
    }
    if (message.audioOutputOwnNotesToMidi !== false) {
      writer.uint32(232).bool(message.audioOutputOwnNotesToMidi);
    }
    if (message.chatAutoScroll !== false) {
      writer.uint32(240).bool(message.chatAutoScroll);
    }
    if (message.showEmbeddedLinks !== false) {
      writer.uint32(248).bool(message.showEmbeddedLinks);
    }
    if (message.midiListenToProgramChanges !== false) {
      writer.uint32(256).bool(message.midiListenToProgramChanges);
    }
    if (message.midiAutoFillEmptyChannels !== false) {
      writer.uint32(264).bool(message.midiAutoFillEmptyChannels);
    }
    if (message.midiUseDefaultBankWhenMissing !== false) {
      writer.uint32(272).bool(message.midiUseDefaultBankWhenMissing);
    }
    if (message.audioUseDefaultInstrumentWhenMissingForOtherUsers !== false) {
      writer.uint32(280).bool(message.audioUseDefaultInstrumentWhenMissingForOtherUsers);
    }
    if (message.midiEnableStereoPanning !== false) {
      writer.uint32(288).bool(message.midiEnableStereoPanning);
    }
    if (message.graphicsOnlyShowPianoKeys !== false) {
      writer.uint32(312).bool(message.graphicsOnlyShowPianoKeys);
    }
    if (message.graphicsDisplayRenderStats !== false) {
      writer.uint32(320).bool(message.graphicsDisplayRenderStats);
    }
    if (message.graphicsEnableAvatars !== false) {
      writer.uint32(352).bool(message.graphicsEnableAvatars);
    }
    if (message.graphicsEnablePhysics !== false) {
      writer.uint32(360).bool(message.graphicsEnablePhysics);
    }
    if (message.graphicsEnableSpecialEffects !== false) {
      writer.uint32(368).bool(message.graphicsEnableSpecialEffects);
    }
    if (message.graphicsEnableAutoAnimateToInstruments !== false) {
      writer.uint32(392).bool(message.graphicsEnableAutoAnimateToInstruments);
    }
    if (message.graphicsEnableAntialias !== false) {
      writer.uint32(400).bool(message.graphicsEnableAntialias);
    }
    if (message.graphicsEnableOrchestraModels !== false) {
      writer.uint32(408).bool(message.graphicsEnableOrchestraModels);
    }
    if (message.graphicsRenderEvenInBackground !== false) {
      writer.uint32(416).bool(message.graphicsRenderEvenInBackground);
    }
    if (message.graphicsEnableAmbientOcclusion !== false) {
      writer.uint32(424).bool(message.graphicsEnableAmbientOcclusion);
    }
    if (message.graphicsEnableDepthOfField !== false) {
      writer.uint32(432).bool(message.graphicsEnableDepthOfField);
    }
    if (message.graphicsUseOffscreenCanvas !== false) {
      writer.uint32(440).bool(message.graphicsUseOffscreenCanvas);
    }
    if (message.chatDisableMarkdown !== false) {
      writer.uint32(448).bool(message.chatDisableMarkdown);
    }
    if (message.chatEnableImageUrlPreview !== false) {
      writer.uint32(456).bool(message.chatEnableImageUrlPreview);
    }
    if (message.enableDesktopNotifications !== false) {
      writer.uint32(464).bool(message.enableDesktopNotifications);
    }
    if (message.allowUsersToNotifyMe !== false) {
      writer.uint32(472).bool(message.allowUsersToNotifyMe);
    }
    if (message.audioSampleRate !== 0) {
      writer.uint32(480).int32(message.audioSampleRate);
    }
    if (message.audioIgnoreSoundfontVolumeEnvDelay !== false) {
      writer.uint32(488).bool(message.audioIgnoreSoundfontVolumeEnvDelay);
    }
    if (message.audioIgnoreSoundfontVolumeEnvDelay2 !== false) {
      writer.uint32(496).bool(message.audioIgnoreSoundfontVolumeEnvDelay2);
    }
    if (message.audioUseSeparateDrumKit !== false) {
      writer.uint32(504).bool(message.audioUseSeparateDrumKit);
    }
    if (message.audioReverbLevel !== 0) {
      writer.uint32(517).float(message.audioReverbLevel);
    }
    if (message.audioReverbDamp !== 0) {
      writer.uint32(525).float(message.audioReverbDamp);
    }
    if (message.audioReverbWidth !== 0) {
      writer.uint32(533).float(message.audioReverbWidth);
    }
    if (message.audioReverbRoomsize !== 0) {
      writer.uint32(541).float(message.audioReverbRoomsize);
    }
    if (message.audioCacheSoundfontsWeb !== false) {
      writer.uint32(544).bool(message.audioCacheSoundfontsWeb);
    }
    if (message.desktopSaveSoundfonts !== false) {
      writer.uint32(552).bool(message.desktopSaveSoundfonts);
    }
    if (message.uiEnableUserNoteActivities !== false) {
      writer.uint32(568).bool(message.uiEnableUserNoteActivities);
    }
    if (message.webglPowerPreference !== "") {
      writer.uint32(586).string(message.webglPowerPreference);
    }
    if (message.enableDebugMode !== false) {
      writer.uint32(592).bool(message.enableDebugMode);
    }
    if (message.autoCheckForUpdates !== false) {
      writer.uint32(600).bool(message.autoCheckForUpdates);
    }
    if (message.audioEqualizerPreset !== 0) {
      writer.uint32(616).int32(message.audioEqualizerPreset);
    }
    if (message.gameManiaDisableBackground !== false) {
      writer.uint32(632).bool(message.gameManiaDisableBackground);
    }
    if (message.audioSfxEnable !== false) {
      writer.uint32(640).bool(message.audioSfxEnable);
    }
    if (message.audioSfxGlobalVolume !== 0) {
      writer.uint32(653).float(message.audioSfxGlobalVolume);
    }
    if (message.audioSfxStageEffectsGlobalVolume !== 0) {
      writer.uint32(661).float(message.audioSfxStageEffectsGlobalVolume);
    }
    if (message.audioSfxHoverVolume !== 0) {
      writer.uint32(669).float(message.audioSfxHoverVolume);
    }
    if (message.audioSfxChatEnable !== false) {
      writer.uint32(672).bool(message.audioSfxChatEnable);
    }
    if (message.audioEnableStageSfx !== false) {
      writer.uint32(680).bool(message.audioEnableStageSfx);
    }
    if (message.audioSynthEngine !== 0) {
      writer.uint32(688).int32(message.audioSynthEngine);
    }
    if (message.audioGlobalUsersVelocityPercentage !== 0) {
      writer.uint32(701).float(message.audioGlobalUsersVelocityPercentage);
    }
    if (message.audioMultimodeMaxChannels !== 0) {
      writer.uint32(704).int32(message.audioMultimodeMaxChannels);
    }
    if (message.appAutoLoginEnable !== false) {
      writer.uint32(712).bool(message.appAutoLoginEnable);
    }
    if (message.audioBgMusicEnable !== false) {
      writer.uint32(720).bool(message.audioBgMusicEnable);
    }
    if (message.audioBgMusicGlobalVolume !== 0) {
      writer.uint32(733).float(message.audioBgMusicGlobalVolume);
    }
    if (message.onlineWarnAboutExternalLinks !== false) {
      writer.uint32(736).bool(message.onlineWarnAboutExternalLinks);
    }
    if (message.enablePlugins !== false) {
      writer.uint32(744).bool(message.enablePlugins);
    }
    if (message.keyboardShiftKeyAutoNoteOff !== false) {
      writer.uint32(752).bool(message.keyboardShiftKeyAutoNoteOff);
    }
    if (message.audioReverbDefaultMigratedVersion !== "") {
      writer.uint32(762).string(message.audioReverbDefaultMigratedVersion);
    }
    if (message.graphicsEnableFog !== false) {
      writer.uint32(768).bool(message.graphicsEnableFog);
    }
    if (message.graphicsEnableShadows !== false) {
      writer.uint32(776).bool(message.graphicsEnableShadows);
    }
    if (message.graphicsEnableStage !== false) {
      writer.uint32(784).bool(message.graphicsEnableStage);
    }
    if (message.graphicsEnablePiano !== false) {
      writer.uint32(792).bool(message.graphicsEnablePiano);
    }
    if (message.graphicsEnableMotionBlur !== false) {
      writer.uint32(808).bool(message.graphicsEnableMotionBlur);
    }
    if (message.graphicsEnableGlow !== false) {
      writer.uint32(816).bool(message.graphicsEnableGlow);
    }
    if (message.graphicsEnableDrums !== false) {
      writer.uint32(824).bool(message.graphicsEnableDrums);
    }
    if (message.graphicsEnableAllParticles !== false) {
      writer.uint32(832).bool(message.graphicsEnableAllParticles);
    }
    if (message.audioUseWorklet !== false) {
      writer.uint32(840).bool(message.audioUseWorklet);
    }
    if (message.uiEnableSynthNoteActivities !== false) {
      writer.uint32(848).bool(message.uiEnableSynthNoteActivities);
    }
    if (message.graphicsEnableSoftShadows !== false) {
      writer.uint32(856).bool(message.graphicsEnableSoftShadows);
    }
    if (message.graphicsEnablePostProcessing !== false) {
      writer.uint32(864).bool(message.graphicsEnablePostProcessing);
    }
    if (message.graphicsTargetFps !== "") {
      writer.uint32(874).string(message.graphicsTargetFps);
    }
    if (message.graphicsEnableLights !== false) {
      writer.uint32(880).bool(message.graphicsEnableLights);
    }
    if (message.graphicsEnableBloom !== false) {
      writer.uint32(888).bool(message.graphicsEnableBloom);
    }
    if (message.graphicsEnableToneMapping !== false) {
      writer.uint32(896).bool(message.graphicsEnableToneMapping);
    }
    if (message.graphicsEnableHdr !== false) {
      writer.uint32(904).bool(message.graphicsEnableHdr);
    }
    if (message.graphicsEnableWebgpu !== false) {
      writer.uint32(912).bool(message.graphicsEnableWebgpu);
    }
    if (message.graphicsEnableAnimations !== false) {
      writer.uint32(920).bool(message.graphicsEnableAnimations);
    }
    if (message.graphicsMsaaSamples !== 0) {
      writer.uint32(928).int32(message.graphicsMsaaSamples);
    }
    if (message.graphicsPreset !== 0) {
      writer.uint32(936).int32(message.graphicsPreset);
    }
    if (message.graphicsShadowFilter !== 0) {
      writer.uint32(944).int32(message.graphicsShadowFilter);
    }
    if (message.graphicsEnableEngine !== false) {
      writer.uint32(952).bool(message.graphicsEnableEngine);
    }
    if (message.graphicsUseLowPolyModels !== false) {
      writer.uint32(960).bool(message.graphicsUseLowPolyModels);
    }
    if (message.graphicsEnableGuitars !== false) {
      writer.uint32(968).bool(message.graphicsEnableGuitars);
    }
    if (message.chatMessagesMinimized !== false) {
      writer.uint32(976).bool(message.chatMessagesMinimized);
    }
    if (message.mutedNotesSelf !== false) {
      writer.uint32(984).bool(message.mutedNotesSelf);
    }
    if (message.sendWhoistyping !== false) {
      writer.uint32(992).bool(message.sendWhoistyping);
    }
    if (message.chatMessagesMaximized !== false) {
      writer.uint32(1000).bool(message.chatMessagesMaximized);
    }
    if (message.audioEnableEqualizer !== false) {
      writer.uint32(1008).bool(message.audioEnableEqualizer);
    }
    if (message.audioMaxNoteOnTime !== 0) {
      writer.uint32(1021).float(message.audioMaxNoteOnTime);
    }
    if (message.audioMaxVelocity !== 0) {
      writer.uint32(1024).uint32(message.audioMaxVelocity);
    }
    if (message.audioMinVelocity !== 0) {
      writer.uint32(1032).uint32(message.audioMinVelocity);
    }
    if (message.audioMinVolumeRelease !== 0) {
      writer.uint32(1045).float(message.audioMinVolumeRelease);
    }
    if (message.inputCtrlKeyLowersOctave !== false) {
      writer.uint32(1048).bool(message.inputCtrlKeyLowersOctave);
    }
    if (message.inputMidiToQwertyMod !== false) {
      writer.uint32(1056).bool(message.inputMidiToQwertyMod);
    }
    if (message.inputMidiToQwertyModUseCapslock !== false) {
      writer.uint32(1064).bool(message.inputMidiToQwertyModUseCapslock);
    }
    if (message.slotMode !== 0) {
      writer.uint32(1072).int32(message.slotMode);
    }
    if (message.audioUseVelocityCurve !== false) {
      writer.uint32(1080).bool(message.audioUseVelocityCurve);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppSettings {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppSettings();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.defaultLanguage = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.displayPiano = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.displayFps = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.discordSync = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.displayPing = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.display3dStats = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.displayCursors = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.displayInstDock = reader.bool();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.displayChat = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.displaySceneWidgetButtons = reader.bool();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.displayWhoistyping = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.keepChatInFocus = reader.bool();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.instDockTranspose = reader.int32();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.instDockOctave = reader.int32();
          continue;
        case 15:
          if (tag !== 125) {
            break;
          }

          message.instDockOpacity = reader.float();
          continue;
        case 16:
          if (tag !== 133) {
            break;
          }

          message.volumeSaved = reader.float();
          continue;
        case 17:
          if (tag !== 136) {
            break;
          }

          message.audioMaxPolyphony = reader.int32();
          continue;
        case 18:
          if (tag !== 149) {
            break;
          }

          message.audioVelMuting = reader.float();
          continue;
        case 19:
          if (tag !== 157) {
            break;
          }

          message.audioSustainCutoff = reader.float();
          continue;
        case 20:
          if (tag !== 165) {
            break;
          }

          message.audioSustainedNoteFadeoutDuration = reader.float();
          continue;
        case 21:
          if (tag !== 173) {
            break;
          }

          message.audioVelBoost = reader.float();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.audioEnableVelBoost = reader.bool();
          continue;
        case 23:
          if (tag !== 184) {
            break;
          }

          message.audioEnableVel = reader.bool();
          continue;
        case 24:
          if (tag !== 192) {
            break;
          }

          message.audioEnableReverb = reader.bool();
          continue;
        case 25:
          if (tag !== 200) {
            break;
          }

          message.audioMidiOutputOnly = reader.bool();
          continue;
        case 26:
          if (tag !== 208) {
            break;
          }

          message.audioMousePosSetsVelocity = reader.bool();
          continue;
        case 27:
          if (tag !== 216) {
            break;
          }

          message.audioEnableDrumChannel = reader.bool();
          continue;
        case 28:
          if (tag !== 224) {
            break;
          }

          message.audioBufferSize = reader.int32();
          continue;
        case 29:
          if (tag !== 232) {
            break;
          }

          message.audioOutputOwnNotesToMidi = reader.bool();
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }

          message.chatAutoScroll = reader.bool();
          continue;
        case 31:
          if (tag !== 248) {
            break;
          }

          message.showEmbeddedLinks = reader.bool();
          continue;
        case 32:
          if (tag !== 256) {
            break;
          }

          message.midiListenToProgramChanges = reader.bool();
          continue;
        case 33:
          if (tag !== 264) {
            break;
          }

          message.midiAutoFillEmptyChannels = reader.bool();
          continue;
        case 34:
          if (tag !== 272) {
            break;
          }

          message.midiUseDefaultBankWhenMissing = reader.bool();
          continue;
        case 35:
          if (tag !== 280) {
            break;
          }

          message.audioUseDefaultInstrumentWhenMissingForOtherUsers = reader.bool();
          continue;
        case 36:
          if (tag !== 288) {
            break;
          }

          message.midiEnableStereoPanning = reader.bool();
          continue;
        case 39:
          if (tag !== 312) {
            break;
          }

          message.graphicsOnlyShowPianoKeys = reader.bool();
          continue;
        case 40:
          if (tag !== 320) {
            break;
          }

          message.graphicsDisplayRenderStats = reader.bool();
          continue;
        case 44:
          if (tag !== 352) {
            break;
          }

          message.graphicsEnableAvatars = reader.bool();
          continue;
        case 45:
          if (tag !== 360) {
            break;
          }

          message.graphicsEnablePhysics = reader.bool();
          continue;
        case 46:
          if (tag !== 368) {
            break;
          }

          message.graphicsEnableSpecialEffects = reader.bool();
          continue;
        case 49:
          if (tag !== 392) {
            break;
          }

          message.graphicsEnableAutoAnimateToInstruments = reader.bool();
          continue;
        case 50:
          if (tag !== 400) {
            break;
          }

          message.graphicsEnableAntialias = reader.bool();
          continue;
        case 51:
          if (tag !== 408) {
            break;
          }

          message.graphicsEnableOrchestraModels = reader.bool();
          continue;
        case 52:
          if (tag !== 416) {
            break;
          }

          message.graphicsRenderEvenInBackground = reader.bool();
          continue;
        case 53:
          if (tag !== 424) {
            break;
          }

          message.graphicsEnableAmbientOcclusion = reader.bool();
          continue;
        case 54:
          if (tag !== 432) {
            break;
          }

          message.graphicsEnableDepthOfField = reader.bool();
          continue;
        case 55:
          if (tag !== 440) {
            break;
          }

          message.graphicsUseOffscreenCanvas = reader.bool();
          continue;
        case 56:
          if (tag !== 448) {
            break;
          }

          message.chatDisableMarkdown = reader.bool();
          continue;
        case 57:
          if (tag !== 456) {
            break;
          }

          message.chatEnableImageUrlPreview = reader.bool();
          continue;
        case 58:
          if (tag !== 464) {
            break;
          }

          message.enableDesktopNotifications = reader.bool();
          continue;
        case 59:
          if (tag !== 472) {
            break;
          }

          message.allowUsersToNotifyMe = reader.bool();
          continue;
        case 60:
          if (tag !== 480) {
            break;
          }

          message.audioSampleRate = reader.int32();
          continue;
        case 61:
          if (tag !== 488) {
            break;
          }

          message.audioIgnoreSoundfontVolumeEnvDelay = reader.bool();
          continue;
        case 62:
          if (tag !== 496) {
            break;
          }

          message.audioIgnoreSoundfontVolumeEnvDelay2 = reader.bool();
          continue;
        case 63:
          if (tag !== 504) {
            break;
          }

          message.audioUseSeparateDrumKit = reader.bool();
          continue;
        case 64:
          if (tag !== 517) {
            break;
          }

          message.audioReverbLevel = reader.float();
          continue;
        case 65:
          if (tag !== 525) {
            break;
          }

          message.audioReverbDamp = reader.float();
          continue;
        case 66:
          if (tag !== 533) {
            break;
          }

          message.audioReverbWidth = reader.float();
          continue;
        case 67:
          if (tag !== 541) {
            break;
          }

          message.audioReverbRoomsize = reader.float();
          continue;
        case 68:
          if (tag !== 544) {
            break;
          }

          message.audioCacheSoundfontsWeb = reader.bool();
          continue;
        case 69:
          if (tag !== 552) {
            break;
          }

          message.desktopSaveSoundfonts = reader.bool();
          continue;
        case 71:
          if (tag !== 568) {
            break;
          }

          message.uiEnableUserNoteActivities = reader.bool();
          continue;
        case 73:
          if (tag !== 586) {
            break;
          }

          message.webglPowerPreference = reader.string();
          continue;
        case 74:
          if (tag !== 592) {
            break;
          }

          message.enableDebugMode = reader.bool();
          continue;
        case 75:
          if (tag !== 600) {
            break;
          }

          message.autoCheckForUpdates = reader.bool();
          continue;
        case 77:
          if (tag !== 616) {
            break;
          }

          message.audioEqualizerPreset = reader.int32() as any;
          continue;
        case 79:
          if (tag !== 632) {
            break;
          }

          message.gameManiaDisableBackground = reader.bool();
          continue;
        case 80:
          if (tag !== 640) {
            break;
          }

          message.audioSfxEnable = reader.bool();
          continue;
        case 81:
          if (tag !== 653) {
            break;
          }

          message.audioSfxGlobalVolume = reader.float();
          continue;
        case 82:
          if (tag !== 661) {
            break;
          }

          message.audioSfxStageEffectsGlobalVolume = reader.float();
          continue;
        case 83:
          if (tag !== 669) {
            break;
          }

          message.audioSfxHoverVolume = reader.float();
          continue;
        case 84:
          if (tag !== 672) {
            break;
          }

          message.audioSfxChatEnable = reader.bool();
          continue;
        case 85:
          if (tag !== 680) {
            break;
          }

          message.audioEnableStageSfx = reader.bool();
          continue;
        case 86:
          if (tag !== 688) {
            break;
          }

          message.audioSynthEngine = reader.int32() as any;
          continue;
        case 87:
          if (tag !== 701) {
            break;
          }

          message.audioGlobalUsersVelocityPercentage = reader.float();
          continue;
        case 88:
          if (tag !== 704) {
            break;
          }

          message.audioMultimodeMaxChannels = reader.int32();
          continue;
        case 89:
          if (tag !== 712) {
            break;
          }

          message.appAutoLoginEnable = reader.bool();
          continue;
        case 90:
          if (tag !== 720) {
            break;
          }

          message.audioBgMusicEnable = reader.bool();
          continue;
        case 91:
          if (tag !== 733) {
            break;
          }

          message.audioBgMusicGlobalVolume = reader.float();
          continue;
        case 92:
          if (tag !== 736) {
            break;
          }

          message.onlineWarnAboutExternalLinks = reader.bool();
          continue;
        case 93:
          if (tag !== 744) {
            break;
          }

          message.enablePlugins = reader.bool();
          continue;
        case 94:
          if (tag !== 752) {
            break;
          }

          message.keyboardShiftKeyAutoNoteOff = reader.bool();
          continue;
        case 95:
          if (tag !== 762) {
            break;
          }

          message.audioReverbDefaultMigratedVersion = reader.string();
          continue;
        case 96:
          if (tag !== 768) {
            break;
          }

          message.graphicsEnableFog = reader.bool();
          continue;
        case 97:
          if (tag !== 776) {
            break;
          }

          message.graphicsEnableShadows = reader.bool();
          continue;
        case 98:
          if (tag !== 784) {
            break;
          }

          message.graphicsEnableStage = reader.bool();
          continue;
        case 99:
          if (tag !== 792) {
            break;
          }

          message.graphicsEnablePiano = reader.bool();
          continue;
        case 101:
          if (tag !== 808) {
            break;
          }

          message.graphicsEnableMotionBlur = reader.bool();
          continue;
        case 102:
          if (tag !== 816) {
            break;
          }

          message.graphicsEnableGlow = reader.bool();
          continue;
        case 103:
          if (tag !== 824) {
            break;
          }

          message.graphicsEnableDrums = reader.bool();
          continue;
        case 104:
          if (tag !== 832) {
            break;
          }

          message.graphicsEnableAllParticles = reader.bool();
          continue;
        case 105:
          if (tag !== 840) {
            break;
          }

          message.audioUseWorklet = reader.bool();
          continue;
        case 106:
          if (tag !== 848) {
            break;
          }

          message.uiEnableSynthNoteActivities = reader.bool();
          continue;
        case 107:
          if (tag !== 856) {
            break;
          }

          message.graphicsEnableSoftShadows = reader.bool();
          continue;
        case 108:
          if (tag !== 864) {
            break;
          }

          message.graphicsEnablePostProcessing = reader.bool();
          continue;
        case 109:
          if (tag !== 874) {
            break;
          }

          message.graphicsTargetFps = reader.string();
          continue;
        case 110:
          if (tag !== 880) {
            break;
          }

          message.graphicsEnableLights = reader.bool();
          continue;
        case 111:
          if (tag !== 888) {
            break;
          }

          message.graphicsEnableBloom = reader.bool();
          continue;
        case 112:
          if (tag !== 896) {
            break;
          }

          message.graphicsEnableToneMapping = reader.bool();
          continue;
        case 113:
          if (tag !== 904) {
            break;
          }

          message.graphicsEnableHdr = reader.bool();
          continue;
        case 114:
          if (tag !== 912) {
            break;
          }

          message.graphicsEnableWebgpu = reader.bool();
          continue;
        case 115:
          if (tag !== 920) {
            break;
          }

          message.graphicsEnableAnimations = reader.bool();
          continue;
        case 116:
          if (tag !== 928) {
            break;
          }

          message.graphicsMsaaSamples = reader.int32() as any;
          continue;
        case 117:
          if (tag !== 936) {
            break;
          }

          message.graphicsPreset = reader.int32() as any;
          continue;
        case 118:
          if (tag !== 944) {
            break;
          }

          message.graphicsShadowFilter = reader.int32() as any;
          continue;
        case 119:
          if (tag !== 952) {
            break;
          }

          message.graphicsEnableEngine = reader.bool();
          continue;
        case 120:
          if (tag !== 960) {
            break;
          }

          message.graphicsUseLowPolyModels = reader.bool();
          continue;
        case 121:
          if (tag !== 968) {
            break;
          }

          message.graphicsEnableGuitars = reader.bool();
          continue;
        case 122:
          if (tag !== 976) {
            break;
          }

          message.chatMessagesMinimized = reader.bool();
          continue;
        case 123:
          if (tag !== 984) {
            break;
          }

          message.mutedNotesSelf = reader.bool();
          continue;
        case 124:
          if (tag !== 992) {
            break;
          }

          message.sendWhoistyping = reader.bool();
          continue;
        case 125:
          if (tag !== 1000) {
            break;
          }

          message.chatMessagesMaximized = reader.bool();
          continue;
        case 126:
          if (tag !== 1008) {
            break;
          }

          message.audioEnableEqualizer = reader.bool();
          continue;
        case 127:
          if (tag !== 1021) {
            break;
          }

          message.audioMaxNoteOnTime = reader.float();
          continue;
        case 128:
          if (tag !== 1024) {
            break;
          }

          message.audioMaxVelocity = reader.uint32();
          continue;
        case 129:
          if (tag !== 1032) {
            break;
          }

          message.audioMinVelocity = reader.uint32();
          continue;
        case 130:
          if (tag !== 1045) {
            break;
          }

          message.audioMinVolumeRelease = reader.float();
          continue;
        case 131:
          if (tag !== 1048) {
            break;
          }

          message.inputCtrlKeyLowersOctave = reader.bool();
          continue;
        case 132:
          if (tag !== 1056) {
            break;
          }

          message.inputMidiToQwertyMod = reader.bool();
          continue;
        case 133:
          if (tag !== 1064) {
            break;
          }

          message.inputMidiToQwertyModUseCapslock = reader.bool();
          continue;
        case 134:
          if (tag !== 1072) {
            break;
          }

          message.slotMode = reader.int32() as any;
          continue;
        case 135:
          if (tag !== 1080) {
            break;
          }

          message.audioUseVelocityCurve = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppSettings {
    return {
      defaultLanguage: isSet(object.DEFAULTLANGUAGE) ? globalThis.String(object.DEFAULTLANGUAGE) : "",
      displayPiano: isSet(object.DISPLAYPIANO) ? globalThis.Boolean(object.DISPLAYPIANO) : false,
      displayFps: isSet(object.DISPLAYFPS) ? globalThis.Boolean(object.DISPLAYFPS) : false,
      discordSync: isSet(object.DISCORDSYNC) ? globalThis.Boolean(object.DISCORDSYNC) : false,
      displayPing: isSet(object.DISPLAYPING) ? globalThis.Boolean(object.DISPLAYPING) : false,
      display3dStats: isSet(object.DISPLAY3DSTATS) ? globalThis.Boolean(object.DISPLAY3DSTATS) : false,
      displayCursors: isSet(object.DISPLAYCURSORS) ? globalThis.Boolean(object.DISPLAYCURSORS) : false,
      displayInstDock: isSet(object.DISPLAYINSTDOCK) ? globalThis.Boolean(object.DISPLAYINSTDOCK) : false,
      displayChat: isSet(object.DISPLAYCHAT) ? globalThis.Boolean(object.DISPLAYCHAT) : false,
      displaySceneWidgetButtons: isSet(object.DISPLAYSCENEWIDGETBUTTONS)
        ? globalThis.Boolean(object.DISPLAYSCENEWIDGETBUTTONS)
        : false,
      displayWhoistyping: isSet(object.DISPLAYWHOISTYPING) ? globalThis.Boolean(object.DISPLAYWHOISTYPING) : false,
      keepChatInFocus: isSet(object.KEEPCHATINFOCUS) ? globalThis.Boolean(object.KEEPCHATINFOCUS) : false,
      instDockTranspose: isSet(object.INSTDOCKTRANSPOSE) ? globalThis.Number(object.INSTDOCKTRANSPOSE) : 0,
      instDockOctave: isSet(object.INSTDOCKOCTAVE) ? globalThis.Number(object.INSTDOCKOCTAVE) : 0,
      instDockOpacity: isSet(object.INSTDOCKOPACITY) ? globalThis.Number(object.INSTDOCKOPACITY) : 0,
      volumeSaved: isSet(object.VOLUMESAVED) ? globalThis.Number(object.VOLUMESAVED) : 0,
      audioMaxPolyphony: isSet(object.AUDIOMAXPOLYPHONY) ? globalThis.Number(object.AUDIOMAXPOLYPHONY) : 0,
      audioVelMuting: isSet(object.AUDIOVELMUTING) ? globalThis.Number(object.AUDIOVELMUTING) : 0,
      audioSustainCutoff: isSet(object.AUDIOSUSTAINCUTOFF) ? globalThis.Number(object.AUDIOSUSTAINCUTOFF) : 0,
      audioSustainedNoteFadeoutDuration: isSet(object.AUDIOSUSTAINEDNOTEFADEOUTDURATION)
        ? globalThis.Number(object.AUDIOSUSTAINEDNOTEFADEOUTDURATION)
        : 0,
      audioVelBoost: isSet(object.AUDIOVELBOOST) ? globalThis.Number(object.AUDIOVELBOOST) : 0,
      audioEnableVelBoost: isSet(object.AUDIOENABLEVELBOOST) ? globalThis.Boolean(object.AUDIOENABLEVELBOOST) : false,
      audioEnableVel: isSet(object.AUDIOENABLEVEL) ? globalThis.Boolean(object.AUDIOENABLEVEL) : false,
      audioEnableReverb: isSet(object.AUDIOENABLEREVERB) ? globalThis.Boolean(object.AUDIOENABLEREVERB) : false,
      audioMidiOutputOnly: isSet(object.AUDIOMIDIOUTPUTONLY) ? globalThis.Boolean(object.AUDIOMIDIOUTPUTONLY) : false,
      audioMousePosSetsVelocity: isSet(object.AUDIOMOUSEPOSSETSVELOCITY)
        ? globalThis.Boolean(object.AUDIOMOUSEPOSSETSVELOCITY)
        : false,
      audioEnableDrumChannel: isSet(object.AUDIOENABLEDRUMCHANNEL)
        ? globalThis.Boolean(object.AUDIOENABLEDRUMCHANNEL)
        : false,
      audioBufferSize: isSet(object.AUDIOBUFFERSIZE) ? globalThis.Number(object.AUDIOBUFFERSIZE) : 0,
      audioOutputOwnNotesToMidi: isSet(object.AUDIOOUTPUTOWNNOTESTOMIDI)
        ? globalThis.Boolean(object.AUDIOOUTPUTOWNNOTESTOMIDI)
        : false,
      chatAutoScroll: isSet(object.CHATAUTOSCROLL) ? globalThis.Boolean(object.CHATAUTOSCROLL) : false,
      showEmbeddedLinks: isSet(object.SHOWEMBEDDEDLINKS) ? globalThis.Boolean(object.SHOWEMBEDDEDLINKS) : false,
      midiListenToProgramChanges: isSet(object.MIDILISTENTOPROGRAMCHANGES)
        ? globalThis.Boolean(object.MIDILISTENTOPROGRAMCHANGES)
        : false,
      midiAutoFillEmptyChannels: isSet(object.MIDIAUTOFILLEMPTYCHANNELS)
        ? globalThis.Boolean(object.MIDIAUTOFILLEMPTYCHANNELS)
        : false,
      midiUseDefaultBankWhenMissing: isSet(object.MIDIUSEDEFAULTBANKWHENMISSING)
        ? globalThis.Boolean(object.MIDIUSEDEFAULTBANKWHENMISSING)
        : false,
      audioUseDefaultInstrumentWhenMissingForOtherUsers: isSet(object.AUDIOUSEDEFAULTINSTRUMENTWHENMISSINGFOROTHERUSERS)
        ? globalThis.Boolean(object.AUDIOUSEDEFAULTINSTRUMENTWHENMISSINGFOROTHERUSERS)
        : false,
      midiEnableStereoPanning: isSet(object.MIDIENABLESTEREOPANNING)
        ? globalThis.Boolean(object.MIDIENABLESTEREOPANNING)
        : false,
      graphicsOnlyShowPianoKeys: isSet(object.GRAPHICSONLYSHOWPIANOKEYS)
        ? globalThis.Boolean(object.GRAPHICSONLYSHOWPIANOKEYS)
        : false,
      graphicsDisplayRenderStats: isSet(object.GRAPHICSDISPLAYRENDERSTATS)
        ? globalThis.Boolean(object.GRAPHICSDISPLAYRENDERSTATS)
        : false,
      graphicsEnableAvatars: isSet(object.GRAPHICSENABLEAVATARS)
        ? globalThis.Boolean(object.GRAPHICSENABLEAVATARS)
        : false,
      graphicsEnablePhysics: isSet(object.GRAPHICSENABLEPHYSICS)
        ? globalThis.Boolean(object.GRAPHICSENABLEPHYSICS)
        : false,
      graphicsEnableSpecialEffects: isSet(object.GRAPHICSENABLESPECIALEFFECTS)
        ? globalThis.Boolean(object.GRAPHICSENABLESPECIALEFFECTS)
        : false,
      graphicsEnableAutoAnimateToInstruments: isSet(object.GRAPHICSENABLEAUTOANIMATETOINSTRUMENTS)
        ? globalThis.Boolean(object.GRAPHICSENABLEAUTOANIMATETOINSTRUMENTS)
        : false,
      graphicsEnableAntialias: isSet(object.GRAPHICSENABLEANTIALIAS)
        ? globalThis.Boolean(object.GRAPHICSENABLEANTIALIAS)
        : false,
      graphicsEnableOrchestraModels: isSet(object.GRAPHICSENABLEORCHESTRAMODELS)
        ? globalThis.Boolean(object.GRAPHICSENABLEORCHESTRAMODELS)
        : false,
      graphicsRenderEvenInBackground: isSet(object.GRAPHICSRENDEREVENINBACKGROUND)
        ? globalThis.Boolean(object.GRAPHICSRENDEREVENINBACKGROUND)
        : false,
      graphicsEnableAmbientOcclusion: isSet(object.GRAPHICSENABLEAMBIENTOCCLUSION)
        ? globalThis.Boolean(object.GRAPHICSENABLEAMBIENTOCCLUSION)
        : false,
      graphicsEnableDepthOfField: isSet(object.GRAPHICSENABLEDEPTHOFFIELD)
        ? globalThis.Boolean(object.GRAPHICSENABLEDEPTHOFFIELD)
        : false,
      graphicsUseOffscreenCanvas: isSet(object.GRAPHICSUSEOFFSCREENCANVAS)
        ? globalThis.Boolean(object.GRAPHICSUSEOFFSCREENCANVAS)
        : false,
      chatDisableMarkdown: isSet(object.CHATDISABLEMARKDOWN) ? globalThis.Boolean(object.CHATDISABLEMARKDOWN) : false,
      chatEnableImageUrlPreview: isSet(object.CHATENABLEIMAGEURLPREVIEW)
        ? globalThis.Boolean(object.CHATENABLEIMAGEURLPREVIEW)
        : false,
      enableDesktopNotifications: isSet(object.ENABLEDESKTOPNOTIFICATIONS)
        ? globalThis.Boolean(object.ENABLEDESKTOPNOTIFICATIONS)
        : false,
      allowUsersToNotifyMe: isSet(object.ALLOWUSERSTONOTIFYME)
        ? globalThis.Boolean(object.ALLOWUSERSTONOTIFYME)
        : false,
      audioSampleRate: isSet(object.AUDIOSAMPLERATE) ? globalThis.Number(object.AUDIOSAMPLERATE) : 0,
      audioIgnoreSoundfontVolumeEnvDelay: isSet(object.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY)
        ? globalThis.Boolean(object.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY)
        : false,
      audioIgnoreSoundfontVolumeEnvDelay2: isSet(object.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY2)
        ? globalThis.Boolean(object.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY2)
        : false,
      audioUseSeparateDrumKit: isSet(object.AUDIOUSESEPARATEDRUMKIT)
        ? globalThis.Boolean(object.AUDIOUSESEPARATEDRUMKIT)
        : false,
      audioReverbLevel: isSet(object.AUDIOREVERBLEVEL) ? globalThis.Number(object.AUDIOREVERBLEVEL) : 0,
      audioReverbDamp: isSet(object.AUDIOREVERBDAMP) ? globalThis.Number(object.AUDIOREVERBDAMP) : 0,
      audioReverbWidth: isSet(object.AUDIOREVERBWIDTH) ? globalThis.Number(object.AUDIOREVERBWIDTH) : 0,
      audioReverbRoomsize: isSet(object.AUDIOREVERBROOMSIZE) ? globalThis.Number(object.AUDIOREVERBROOMSIZE) : 0,
      audioCacheSoundfontsWeb: isSet(object.AUDIOCACHESOUNDFONTSWEB)
        ? globalThis.Boolean(object.AUDIOCACHESOUNDFONTSWEB)
        : false,
      desktopSaveSoundfonts: isSet(object.DESKTOPSAVESOUNDFONTS)
        ? globalThis.Boolean(object.DESKTOPSAVESOUNDFONTS)
        : false,
      uiEnableUserNoteActivities: isSet(object.UIENABLEUSERNOTEACTIVITIES)
        ? globalThis.Boolean(object.UIENABLEUSERNOTEACTIVITIES)
        : false,
      webglPowerPreference: isSet(object.WEBGLPOWERPREFERENCE) ? globalThis.String(object.WEBGLPOWERPREFERENCE) : "",
      enableDebugMode: isSet(object.ENABLEDEBUGMODE) ? globalThis.Boolean(object.ENABLEDEBUGMODE) : false,
      autoCheckForUpdates: isSet(object.AUTOCHECKFORUPDATES) ? globalThis.Boolean(object.AUTOCHECKFORUPDATES) : false,
      audioEqualizerPreset: isSet(object.AUDIOEQUALIZERPRESET)
        ? equalizerPresetFromJSON(object.AUDIOEQUALIZERPRESET)
        : 0,
      gameManiaDisableBackground: isSet(object.GAMEMANIADISABLEBACKGROUND)
        ? globalThis.Boolean(object.GAMEMANIADISABLEBACKGROUND)
        : false,
      audioSfxEnable: isSet(object.AUDIOSFXENABLE) ? globalThis.Boolean(object.AUDIOSFXENABLE) : false,
      audioSfxGlobalVolume: isSet(object.AUDIOSFXGLOBALVOLUME) ? globalThis.Number(object.AUDIOSFXGLOBALVOLUME) : 0,
      audioSfxStageEffectsGlobalVolume: isSet(object.AUDIOSFXSTAGEEFFECTSGLOBALVOLUME)
        ? globalThis.Number(object.AUDIOSFXSTAGEEFFECTSGLOBALVOLUME)
        : 0,
      audioSfxHoverVolume: isSet(object.AUDIOSFXHOVERVOLUME) ? globalThis.Number(object.AUDIOSFXHOVERVOLUME) : 0,
      audioSfxChatEnable: isSet(object.AUDIOSFXCHATENABLE) ? globalThis.Boolean(object.AUDIOSFXCHATENABLE) : false,
      audioEnableStageSfx: isSet(object.AUDIOENABLESTAGESFX) ? globalThis.Boolean(object.AUDIOENABLESTAGESFX) : false,
      audioSynthEngine: isSet(object.AUDIOSYNTHENGINE) ? audioSynthesizerEngineFromJSON(object.AUDIOSYNTHENGINE) : 0,
      audioGlobalUsersVelocityPercentage: isSet(object.AUDIOGLOBALUSERSVELOCITYPERCENTAGE)
        ? globalThis.Number(object.AUDIOGLOBALUSERSVELOCITYPERCENTAGE)
        : 0,
      audioMultimodeMaxChannels: isSet(object.AUDIOMULTIMODEMAXCHANNELS)
        ? globalThis.Number(object.AUDIOMULTIMODEMAXCHANNELS)
        : 0,
      appAutoLoginEnable: isSet(object.APPAUTOLOGINENABLE) ? globalThis.Boolean(object.APPAUTOLOGINENABLE) : false,
      audioBgMusicEnable: isSet(object.AUDIOBGMUSICENABLE) ? globalThis.Boolean(object.AUDIOBGMUSICENABLE) : false,
      audioBgMusicGlobalVolume: isSet(object.AUDIOBGMUSICGLOBALVOLUME)
        ? globalThis.Number(object.AUDIOBGMUSICGLOBALVOLUME)
        : 0,
      onlineWarnAboutExternalLinks: isSet(object.ONLINEWARNABOUTEXTERNALLINKS)
        ? globalThis.Boolean(object.ONLINEWARNABOUTEXTERNALLINKS)
        : false,
      enablePlugins: isSet(object.ENABLEPLUGINS) ? globalThis.Boolean(object.ENABLEPLUGINS) : false,
      keyboardShiftKeyAutoNoteOff: isSet(object.KEYBOARDSHIFTKEYAUTONOTEOFF)
        ? globalThis.Boolean(object.KEYBOARDSHIFTKEYAUTONOTEOFF)
        : false,
      audioReverbDefaultMigratedVersion: isSet(object.AUDIOREVERBDEFAULTMIGRATEDVERSION)
        ? globalThis.String(object.AUDIOREVERBDEFAULTMIGRATEDVERSION)
        : "",
      graphicsEnableFog: isSet(object.GRAPHICSENABLEFOG) ? globalThis.Boolean(object.GRAPHICSENABLEFOG) : false,
      graphicsEnableShadows: isSet(object.GRAPHICSENABLESHADOWS)
        ? globalThis.Boolean(object.GRAPHICSENABLESHADOWS)
        : false,
      graphicsEnableStage: isSet(object.GRAPHICSENABLESTAGE) ? globalThis.Boolean(object.GRAPHICSENABLESTAGE) : false,
      graphicsEnablePiano: isSet(object.GRAPHICSENABLEPIANO) ? globalThis.Boolean(object.GRAPHICSENABLEPIANO) : false,
      graphicsEnableMotionBlur: isSet(object.GRAPHICSENABLEMOTIONBLUR)
        ? globalThis.Boolean(object.GRAPHICSENABLEMOTIONBLUR)
        : false,
      graphicsEnableGlow: isSet(object.GRAPHICSENABLEGLOW) ? globalThis.Boolean(object.GRAPHICSENABLEGLOW) : false,
      graphicsEnableDrums: isSet(object.GRAPHICSENABLEDRUMS) ? globalThis.Boolean(object.GRAPHICSENABLEDRUMS) : false,
      graphicsEnableAllParticles: isSet(object.GRAPHICSENABLEALLPARTICLES)
        ? globalThis.Boolean(object.GRAPHICSENABLEALLPARTICLES)
        : false,
      audioUseWorklet: isSet(object.AUDIOUSEWORKLET) ? globalThis.Boolean(object.AUDIOUSEWORKLET) : false,
      uiEnableSynthNoteActivities: isSet(object.UIENABLESYNTHNOTEACTIVITIES)
        ? globalThis.Boolean(object.UIENABLESYNTHNOTEACTIVITIES)
        : false,
      graphicsEnableSoftShadows: isSet(object.GRAPHICSENABLESOFTSHADOWS)
        ? globalThis.Boolean(object.GRAPHICSENABLESOFTSHADOWS)
        : false,
      graphicsEnablePostProcessing: isSet(object.GRAPHICSENABLEPOSTPROCESSING)
        ? globalThis.Boolean(object.GRAPHICSENABLEPOSTPROCESSING)
        : false,
      graphicsTargetFps: isSet(object.GRAPHICSTARGETFPS) ? globalThis.String(object.GRAPHICSTARGETFPS) : "",
      graphicsEnableLights: isSet(object.GRAPHICSENABLELIGHTS)
        ? globalThis.Boolean(object.GRAPHICSENABLELIGHTS)
        : false,
      graphicsEnableBloom: isSet(object.GRAPHICSENABLEBLOOM) ? globalThis.Boolean(object.GRAPHICSENABLEBLOOM) : false,
      graphicsEnableToneMapping: isSet(object.GRAPHICSENABLETONEMAPPING)
        ? globalThis.Boolean(object.GRAPHICSENABLETONEMAPPING)
        : false,
      graphicsEnableHdr: isSet(object.GRAPHICSENABLEHDR) ? globalThis.Boolean(object.GRAPHICSENABLEHDR) : false,
      graphicsEnableWebgpu: isSet(object.GRAPHICSENABLEWEBGPU)
        ? globalThis.Boolean(object.GRAPHICSENABLEWEBGPU)
        : false,
      graphicsEnableAnimations: isSet(object.GRAPHICSENABLEANIMATIONS)
        ? globalThis.Boolean(object.GRAPHICSENABLEANIMATIONS)
        : false,
      graphicsMsaaSamples: isSet(object.GRAPHICSMSAASAMPLES)
        ? graphicsMsaaSamplesFromJSON(object.GRAPHICSMSAASAMPLES)
        : 0,
      graphicsPreset: isSet(object.GRAPHICSPRESET) ? graphicsPresetsFromJSON(object.GRAPHICSPRESET) : 0,
      graphicsShadowFilter: isSet(object.GRAPHICSSHADOWFILTER)
        ? graphicShadowFilteringMethodFromJSON(object.GRAPHICSSHADOWFILTER)
        : 0,
      graphicsEnableEngine: isSet(object.GRAPHICSENABLEENGINE)
        ? globalThis.Boolean(object.GRAPHICSENABLEENGINE)
        : false,
      graphicsUseLowPolyModels: isSet(object.GRAPHICSUSELOWPOLYMODELS)
        ? globalThis.Boolean(object.GRAPHICSUSELOWPOLYMODELS)
        : false,
      graphicsEnableGuitars: isSet(object.GRAPHICSENABLEGUITARS)
        ? globalThis.Boolean(object.GRAPHICSENABLEGUITARS)
        : false,
      chatMessagesMinimized: isSet(object.CHATMESSAGESMINIMIZED)
        ? globalThis.Boolean(object.CHATMESSAGESMINIMIZED)
        : false,
      mutedNotesSelf: isSet(object.MUTEDNOTESSELF) ? globalThis.Boolean(object.MUTEDNOTESSELF) : false,
      sendWhoistyping: isSet(object.SENDWHOISTYPING) ? globalThis.Boolean(object.SENDWHOISTYPING) : false,
      chatMessagesMaximized: isSet(object.CHATMESSAGESMAXIMIZED)
        ? globalThis.Boolean(object.CHATMESSAGESMAXIMIZED)
        : false,
      audioEnableEqualizer: isSet(object.AUDIOENABLEEQUALIZER)
        ? globalThis.Boolean(object.AUDIOENABLEEQUALIZER)
        : false,
      audioMaxNoteOnTime: isSet(object.AUDIOMAXNOTEONTIME) ? globalThis.Number(object.AUDIOMAXNOTEONTIME) : 0,
      audioMaxVelocity: isSet(object.AUDIOMAXVELOCITY) ? globalThis.Number(object.AUDIOMAXVELOCITY) : 0,
      audioMinVelocity: isSet(object.AUDIOMINVELOCITY) ? globalThis.Number(object.AUDIOMINVELOCITY) : 0,
      audioMinVolumeRelease: isSet(object.AUDIOMINVOLUMERELEASE) ? globalThis.Number(object.AUDIOMINVOLUMERELEASE) : 0,
      inputCtrlKeyLowersOctave: isSet(object.INPUTCTRLKEYLOWERSOCTAVE)
        ? globalThis.Boolean(object.INPUTCTRLKEYLOWERSOCTAVE)
        : false,
      inputMidiToQwertyMod: isSet(object.INPUTMIDITOQWERTYMOD)
        ? globalThis.Boolean(object.INPUTMIDITOQWERTYMOD)
        : false,
      inputMidiToQwertyModUseCapslock: isSet(object.INPUTMIDITOQWERTYMODUSECAPSLOCK)
        ? globalThis.Boolean(object.INPUTMIDITOQWERTYMODUSECAPSLOCK)
        : false,
      slotMode: isSet(object.SLOTMODE) ? activeChannelsModeFromJSON(object.SLOTMODE) : 0,
      audioUseVelocityCurve: isSet(object.AUDIOUSEVELOCITYCURVE)
        ? globalThis.Boolean(object.AUDIOUSEVELOCITYCURVE)
        : false,
    };
  },

  toJSON(message: AppSettings): unknown {
    const obj: any = {};
    if (message.defaultLanguage !== "") {
      obj.DEFAULTLANGUAGE = message.defaultLanguage;
    }
    if (message.displayPiano !== false) {
      obj.DISPLAYPIANO = message.displayPiano;
    }
    if (message.displayFps !== false) {
      obj.DISPLAYFPS = message.displayFps;
    }
    if (message.discordSync !== false) {
      obj.DISCORDSYNC = message.discordSync;
    }
    if (message.displayPing !== false) {
      obj.DISPLAYPING = message.displayPing;
    }
    if (message.display3dStats !== false) {
      obj.DISPLAY3DSTATS = message.display3dStats;
    }
    if (message.displayCursors !== false) {
      obj.DISPLAYCURSORS = message.displayCursors;
    }
    if (message.displayInstDock !== false) {
      obj.DISPLAYINSTDOCK = message.displayInstDock;
    }
    if (message.displayChat !== false) {
      obj.DISPLAYCHAT = message.displayChat;
    }
    if (message.displaySceneWidgetButtons !== false) {
      obj.DISPLAYSCENEWIDGETBUTTONS = message.displaySceneWidgetButtons;
    }
    if (message.displayWhoistyping !== false) {
      obj.DISPLAYWHOISTYPING = message.displayWhoistyping;
    }
    if (message.keepChatInFocus !== false) {
      obj.KEEPCHATINFOCUS = message.keepChatInFocus;
    }
    if (message.instDockTranspose !== 0) {
      obj.INSTDOCKTRANSPOSE = Math.round(message.instDockTranspose);
    }
    if (message.instDockOctave !== 0) {
      obj.INSTDOCKOCTAVE = Math.round(message.instDockOctave);
    }
    if (message.instDockOpacity !== 0) {
      obj.INSTDOCKOPACITY = message.instDockOpacity;
    }
    if (message.volumeSaved !== 0) {
      obj.VOLUMESAVED = message.volumeSaved;
    }
    if (message.audioMaxPolyphony !== 0) {
      obj.AUDIOMAXPOLYPHONY = Math.round(message.audioMaxPolyphony);
    }
    if (message.audioVelMuting !== 0) {
      obj.AUDIOVELMUTING = message.audioVelMuting;
    }
    if (message.audioSustainCutoff !== 0) {
      obj.AUDIOSUSTAINCUTOFF = message.audioSustainCutoff;
    }
    if (message.audioSustainedNoteFadeoutDuration !== 0) {
      obj.AUDIOSUSTAINEDNOTEFADEOUTDURATION = message.audioSustainedNoteFadeoutDuration;
    }
    if (message.audioVelBoost !== 0) {
      obj.AUDIOVELBOOST = message.audioVelBoost;
    }
    if (message.audioEnableVelBoost !== false) {
      obj.AUDIOENABLEVELBOOST = message.audioEnableVelBoost;
    }
    if (message.audioEnableVel !== false) {
      obj.AUDIOENABLEVEL = message.audioEnableVel;
    }
    if (message.audioEnableReverb !== false) {
      obj.AUDIOENABLEREVERB = message.audioEnableReverb;
    }
    if (message.audioMidiOutputOnly !== false) {
      obj.AUDIOMIDIOUTPUTONLY = message.audioMidiOutputOnly;
    }
    if (message.audioMousePosSetsVelocity !== false) {
      obj.AUDIOMOUSEPOSSETSVELOCITY = message.audioMousePosSetsVelocity;
    }
    if (message.audioEnableDrumChannel !== false) {
      obj.AUDIOENABLEDRUMCHANNEL = message.audioEnableDrumChannel;
    }
    if (message.audioBufferSize !== 0) {
      obj.AUDIOBUFFERSIZE = Math.round(message.audioBufferSize);
    }
    if (message.audioOutputOwnNotesToMidi !== false) {
      obj.AUDIOOUTPUTOWNNOTESTOMIDI = message.audioOutputOwnNotesToMidi;
    }
    if (message.chatAutoScroll !== false) {
      obj.CHATAUTOSCROLL = message.chatAutoScroll;
    }
    if (message.showEmbeddedLinks !== false) {
      obj.SHOWEMBEDDEDLINKS = message.showEmbeddedLinks;
    }
    if (message.midiListenToProgramChanges !== false) {
      obj.MIDILISTENTOPROGRAMCHANGES = message.midiListenToProgramChanges;
    }
    if (message.midiAutoFillEmptyChannels !== false) {
      obj.MIDIAUTOFILLEMPTYCHANNELS = message.midiAutoFillEmptyChannels;
    }
    if (message.midiUseDefaultBankWhenMissing !== false) {
      obj.MIDIUSEDEFAULTBANKWHENMISSING = message.midiUseDefaultBankWhenMissing;
    }
    if (message.audioUseDefaultInstrumentWhenMissingForOtherUsers !== false) {
      obj.AUDIOUSEDEFAULTINSTRUMENTWHENMISSINGFOROTHERUSERS = message.audioUseDefaultInstrumentWhenMissingForOtherUsers;
    }
    if (message.midiEnableStereoPanning !== false) {
      obj.MIDIENABLESTEREOPANNING = message.midiEnableStereoPanning;
    }
    if (message.graphicsOnlyShowPianoKeys !== false) {
      obj.GRAPHICSONLYSHOWPIANOKEYS = message.graphicsOnlyShowPianoKeys;
    }
    if (message.graphicsDisplayRenderStats !== false) {
      obj.GRAPHICSDISPLAYRENDERSTATS = message.graphicsDisplayRenderStats;
    }
    if (message.graphicsEnableAvatars !== false) {
      obj.GRAPHICSENABLEAVATARS = message.graphicsEnableAvatars;
    }
    if (message.graphicsEnablePhysics !== false) {
      obj.GRAPHICSENABLEPHYSICS = message.graphicsEnablePhysics;
    }
    if (message.graphicsEnableSpecialEffects !== false) {
      obj.GRAPHICSENABLESPECIALEFFECTS = message.graphicsEnableSpecialEffects;
    }
    if (message.graphicsEnableAutoAnimateToInstruments !== false) {
      obj.GRAPHICSENABLEAUTOANIMATETOINSTRUMENTS = message.graphicsEnableAutoAnimateToInstruments;
    }
    if (message.graphicsEnableAntialias !== false) {
      obj.GRAPHICSENABLEANTIALIAS = message.graphicsEnableAntialias;
    }
    if (message.graphicsEnableOrchestraModels !== false) {
      obj.GRAPHICSENABLEORCHESTRAMODELS = message.graphicsEnableOrchestraModels;
    }
    if (message.graphicsRenderEvenInBackground !== false) {
      obj.GRAPHICSRENDEREVENINBACKGROUND = message.graphicsRenderEvenInBackground;
    }
    if (message.graphicsEnableAmbientOcclusion !== false) {
      obj.GRAPHICSENABLEAMBIENTOCCLUSION = message.graphicsEnableAmbientOcclusion;
    }
    if (message.graphicsEnableDepthOfField !== false) {
      obj.GRAPHICSENABLEDEPTHOFFIELD = message.graphicsEnableDepthOfField;
    }
    if (message.graphicsUseOffscreenCanvas !== false) {
      obj.GRAPHICSUSEOFFSCREENCANVAS = message.graphicsUseOffscreenCanvas;
    }
    if (message.chatDisableMarkdown !== false) {
      obj.CHATDISABLEMARKDOWN = message.chatDisableMarkdown;
    }
    if (message.chatEnableImageUrlPreview !== false) {
      obj.CHATENABLEIMAGEURLPREVIEW = message.chatEnableImageUrlPreview;
    }
    if (message.enableDesktopNotifications !== false) {
      obj.ENABLEDESKTOPNOTIFICATIONS = message.enableDesktopNotifications;
    }
    if (message.allowUsersToNotifyMe !== false) {
      obj.ALLOWUSERSTONOTIFYME = message.allowUsersToNotifyMe;
    }
    if (message.audioSampleRate !== 0) {
      obj.AUDIOSAMPLERATE = Math.round(message.audioSampleRate);
    }
    if (message.audioIgnoreSoundfontVolumeEnvDelay !== false) {
      obj.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY = message.audioIgnoreSoundfontVolumeEnvDelay;
    }
    if (message.audioIgnoreSoundfontVolumeEnvDelay2 !== false) {
      obj.AUDIOIGNORESOUNDFONTVOLUMEENVDELAY2 = message.audioIgnoreSoundfontVolumeEnvDelay2;
    }
    if (message.audioUseSeparateDrumKit !== false) {
      obj.AUDIOUSESEPARATEDRUMKIT = message.audioUseSeparateDrumKit;
    }
    if (message.audioReverbLevel !== 0) {
      obj.AUDIOREVERBLEVEL = message.audioReverbLevel;
    }
    if (message.audioReverbDamp !== 0) {
      obj.AUDIOREVERBDAMP = message.audioReverbDamp;
    }
    if (message.audioReverbWidth !== 0) {
      obj.AUDIOREVERBWIDTH = message.audioReverbWidth;
    }
    if (message.audioReverbRoomsize !== 0) {
      obj.AUDIOREVERBROOMSIZE = message.audioReverbRoomsize;
    }
    if (message.audioCacheSoundfontsWeb !== false) {
      obj.AUDIOCACHESOUNDFONTSWEB = message.audioCacheSoundfontsWeb;
    }
    if (message.desktopSaveSoundfonts !== false) {
      obj.DESKTOPSAVESOUNDFONTS = message.desktopSaveSoundfonts;
    }
    if (message.uiEnableUserNoteActivities !== false) {
      obj.UIENABLEUSERNOTEACTIVITIES = message.uiEnableUserNoteActivities;
    }
    if (message.webglPowerPreference !== "") {
      obj.WEBGLPOWERPREFERENCE = message.webglPowerPreference;
    }
    if (message.enableDebugMode !== false) {
      obj.ENABLEDEBUGMODE = message.enableDebugMode;
    }
    if (message.autoCheckForUpdates !== false) {
      obj.AUTOCHECKFORUPDATES = message.autoCheckForUpdates;
    }
    if (message.audioEqualizerPreset !== 0) {
      obj.AUDIOEQUALIZERPRESET = equalizerPresetToJSON(message.audioEqualizerPreset);
    }
    if (message.gameManiaDisableBackground !== false) {
      obj.GAMEMANIADISABLEBACKGROUND = message.gameManiaDisableBackground;
    }
    if (message.audioSfxEnable !== false) {
      obj.AUDIOSFXENABLE = message.audioSfxEnable;
    }
    if (message.audioSfxGlobalVolume !== 0) {
      obj.AUDIOSFXGLOBALVOLUME = message.audioSfxGlobalVolume;
    }
    if (message.audioSfxStageEffectsGlobalVolume !== 0) {
      obj.AUDIOSFXSTAGEEFFECTSGLOBALVOLUME = message.audioSfxStageEffectsGlobalVolume;
    }
    if (message.audioSfxHoverVolume !== 0) {
      obj.AUDIOSFXHOVERVOLUME = message.audioSfxHoverVolume;
    }
    if (message.audioSfxChatEnable !== false) {
      obj.AUDIOSFXCHATENABLE = message.audioSfxChatEnable;
    }
    if (message.audioEnableStageSfx !== false) {
      obj.AUDIOENABLESTAGESFX = message.audioEnableStageSfx;
    }
    if (message.audioSynthEngine !== 0) {
      obj.AUDIOSYNTHENGINE = audioSynthesizerEngineToJSON(message.audioSynthEngine);
    }
    if (message.audioGlobalUsersVelocityPercentage !== 0) {
      obj.AUDIOGLOBALUSERSVELOCITYPERCENTAGE = message.audioGlobalUsersVelocityPercentage;
    }
    if (message.audioMultimodeMaxChannels !== 0) {
      obj.AUDIOMULTIMODEMAXCHANNELS = Math.round(message.audioMultimodeMaxChannels);
    }
    if (message.appAutoLoginEnable !== false) {
      obj.APPAUTOLOGINENABLE = message.appAutoLoginEnable;
    }
    if (message.audioBgMusicEnable !== false) {
      obj.AUDIOBGMUSICENABLE = message.audioBgMusicEnable;
    }
    if (message.audioBgMusicGlobalVolume !== 0) {
      obj.AUDIOBGMUSICGLOBALVOLUME = message.audioBgMusicGlobalVolume;
    }
    if (message.onlineWarnAboutExternalLinks !== false) {
      obj.ONLINEWARNABOUTEXTERNALLINKS = message.onlineWarnAboutExternalLinks;
    }
    if (message.enablePlugins !== false) {
      obj.ENABLEPLUGINS = message.enablePlugins;
    }
    if (message.keyboardShiftKeyAutoNoteOff !== false) {
      obj.KEYBOARDSHIFTKEYAUTONOTEOFF = message.keyboardShiftKeyAutoNoteOff;
    }
    if (message.audioReverbDefaultMigratedVersion !== "") {
      obj.AUDIOREVERBDEFAULTMIGRATEDVERSION = message.audioReverbDefaultMigratedVersion;
    }
    if (message.graphicsEnableFog !== false) {
      obj.GRAPHICSENABLEFOG = message.graphicsEnableFog;
    }
    if (message.graphicsEnableShadows !== false) {
      obj.GRAPHICSENABLESHADOWS = message.graphicsEnableShadows;
    }
    if (message.graphicsEnableStage !== false) {
      obj.GRAPHICSENABLESTAGE = message.graphicsEnableStage;
    }
    if (message.graphicsEnablePiano !== false) {
      obj.GRAPHICSENABLEPIANO = message.graphicsEnablePiano;
    }
    if (message.graphicsEnableMotionBlur !== false) {
      obj.GRAPHICSENABLEMOTIONBLUR = message.graphicsEnableMotionBlur;
    }
    if (message.graphicsEnableGlow !== false) {
      obj.GRAPHICSENABLEGLOW = message.graphicsEnableGlow;
    }
    if (message.graphicsEnableDrums !== false) {
      obj.GRAPHICSENABLEDRUMS = message.graphicsEnableDrums;
    }
    if (message.graphicsEnableAllParticles !== false) {
      obj.GRAPHICSENABLEALLPARTICLES = message.graphicsEnableAllParticles;
    }
    if (message.audioUseWorklet !== false) {
      obj.AUDIOUSEWORKLET = message.audioUseWorklet;
    }
    if (message.uiEnableSynthNoteActivities !== false) {
      obj.UIENABLESYNTHNOTEACTIVITIES = message.uiEnableSynthNoteActivities;
    }
    if (message.graphicsEnableSoftShadows !== false) {
      obj.GRAPHICSENABLESOFTSHADOWS = message.graphicsEnableSoftShadows;
    }
    if (message.graphicsEnablePostProcessing !== false) {
      obj.GRAPHICSENABLEPOSTPROCESSING = message.graphicsEnablePostProcessing;
    }
    if (message.graphicsTargetFps !== "") {
      obj.GRAPHICSTARGETFPS = message.graphicsTargetFps;
    }
    if (message.graphicsEnableLights !== false) {
      obj.GRAPHICSENABLELIGHTS = message.graphicsEnableLights;
    }
    if (message.graphicsEnableBloom !== false) {
      obj.GRAPHICSENABLEBLOOM = message.graphicsEnableBloom;
    }
    if (message.graphicsEnableToneMapping !== false) {
      obj.GRAPHICSENABLETONEMAPPING = message.graphicsEnableToneMapping;
    }
    if (message.graphicsEnableHdr !== false) {
      obj.GRAPHICSENABLEHDR = message.graphicsEnableHdr;
    }
    if (message.graphicsEnableWebgpu !== false) {
      obj.GRAPHICSENABLEWEBGPU = message.graphicsEnableWebgpu;
    }
    if (message.graphicsEnableAnimations !== false) {
      obj.GRAPHICSENABLEANIMATIONS = message.graphicsEnableAnimations;
    }
    if (message.graphicsMsaaSamples !== 0) {
      obj.GRAPHICSMSAASAMPLES = graphicsMsaaSamplesToJSON(message.graphicsMsaaSamples);
    }
    if (message.graphicsPreset !== 0) {
      obj.GRAPHICSPRESET = graphicsPresetsToJSON(message.graphicsPreset);
    }
    if (message.graphicsShadowFilter !== 0) {
      obj.GRAPHICSSHADOWFILTER = graphicShadowFilteringMethodToJSON(message.graphicsShadowFilter);
    }
    if (message.graphicsEnableEngine !== false) {
      obj.GRAPHICSENABLEENGINE = message.graphicsEnableEngine;
    }
    if (message.graphicsUseLowPolyModels !== false) {
      obj.GRAPHICSUSELOWPOLYMODELS = message.graphicsUseLowPolyModels;
    }
    if (message.graphicsEnableGuitars !== false) {
      obj.GRAPHICSENABLEGUITARS = message.graphicsEnableGuitars;
    }
    if (message.chatMessagesMinimized !== false) {
      obj.CHATMESSAGESMINIMIZED = message.chatMessagesMinimized;
    }
    if (message.mutedNotesSelf !== false) {
      obj.MUTEDNOTESSELF = message.mutedNotesSelf;
    }
    if (message.sendWhoistyping !== false) {
      obj.SENDWHOISTYPING = message.sendWhoistyping;
    }
    if (message.chatMessagesMaximized !== false) {
      obj.CHATMESSAGESMAXIMIZED = message.chatMessagesMaximized;
    }
    if (message.audioEnableEqualizer !== false) {
      obj.AUDIOENABLEEQUALIZER = message.audioEnableEqualizer;
    }
    if (message.audioMaxNoteOnTime !== 0) {
      obj.AUDIOMAXNOTEONTIME = message.audioMaxNoteOnTime;
    }
    if (message.audioMaxVelocity !== 0) {
      obj.AUDIOMAXVELOCITY = Math.round(message.audioMaxVelocity);
    }
    if (message.audioMinVelocity !== 0) {
      obj.AUDIOMINVELOCITY = Math.round(message.audioMinVelocity);
    }
    if (message.audioMinVolumeRelease !== 0) {
      obj.AUDIOMINVOLUMERELEASE = message.audioMinVolumeRelease;
    }
    if (message.inputCtrlKeyLowersOctave !== false) {
      obj.INPUTCTRLKEYLOWERSOCTAVE = message.inputCtrlKeyLowersOctave;
    }
    if (message.inputMidiToQwertyMod !== false) {
      obj.INPUTMIDITOQWERTYMOD = message.inputMidiToQwertyMod;
    }
    if (message.inputMidiToQwertyModUseCapslock !== false) {
      obj.INPUTMIDITOQWERTYMODUSECAPSLOCK = message.inputMidiToQwertyModUseCapslock;
    }
    if (message.slotMode !== 0) {
      obj.SLOTMODE = activeChannelsModeToJSON(message.slotMode);
    }
    if (message.audioUseVelocityCurve !== false) {
      obj.AUDIOUSEVELOCITYCURVE = message.audioUseVelocityCurve;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppSettings>, I>>(base?: I): AppSettings {
    return AppSettings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppSettings>, I>>(object: I): AppSettings {
    const message = createBaseAppSettings();
    message.defaultLanguage = object.defaultLanguage ?? "";
    message.displayPiano = object.displayPiano ?? false;
    message.displayFps = object.displayFps ?? false;
    message.discordSync = object.discordSync ?? false;
    message.displayPing = object.displayPing ?? false;
    message.display3dStats = object.display3dStats ?? false;
    message.displayCursors = object.displayCursors ?? false;
    message.displayInstDock = object.displayInstDock ?? false;
    message.displayChat = object.displayChat ?? false;
    message.displaySceneWidgetButtons = object.displaySceneWidgetButtons ?? false;
    message.displayWhoistyping = object.displayWhoistyping ?? false;
    message.keepChatInFocus = object.keepChatInFocus ?? false;
    message.instDockTranspose = object.instDockTranspose ?? 0;
    message.instDockOctave = object.instDockOctave ?? 0;
    message.instDockOpacity = object.instDockOpacity ?? 0;
    message.volumeSaved = object.volumeSaved ?? 0;
    message.audioMaxPolyphony = object.audioMaxPolyphony ?? 0;
    message.audioVelMuting = object.audioVelMuting ?? 0;
    message.audioSustainCutoff = object.audioSustainCutoff ?? 0;
    message.audioSustainedNoteFadeoutDuration = object.audioSustainedNoteFadeoutDuration ?? 0;
    message.audioVelBoost = object.audioVelBoost ?? 0;
    message.audioEnableVelBoost = object.audioEnableVelBoost ?? false;
    message.audioEnableVel = object.audioEnableVel ?? false;
    message.audioEnableReverb = object.audioEnableReverb ?? false;
    message.audioMidiOutputOnly = object.audioMidiOutputOnly ?? false;
    message.audioMousePosSetsVelocity = object.audioMousePosSetsVelocity ?? false;
    message.audioEnableDrumChannel = object.audioEnableDrumChannel ?? false;
    message.audioBufferSize = object.audioBufferSize ?? 0;
    message.audioOutputOwnNotesToMidi = object.audioOutputOwnNotesToMidi ?? false;
    message.chatAutoScroll = object.chatAutoScroll ?? false;
    message.showEmbeddedLinks = object.showEmbeddedLinks ?? false;
    message.midiListenToProgramChanges = object.midiListenToProgramChanges ?? false;
    message.midiAutoFillEmptyChannels = object.midiAutoFillEmptyChannels ?? false;
    message.midiUseDefaultBankWhenMissing = object.midiUseDefaultBankWhenMissing ?? false;
    message.audioUseDefaultInstrumentWhenMissingForOtherUsers =
      object.audioUseDefaultInstrumentWhenMissingForOtherUsers ?? false;
    message.midiEnableStereoPanning = object.midiEnableStereoPanning ?? false;
    message.graphicsOnlyShowPianoKeys = object.graphicsOnlyShowPianoKeys ?? false;
    message.graphicsDisplayRenderStats = object.graphicsDisplayRenderStats ?? false;
    message.graphicsEnableAvatars = object.graphicsEnableAvatars ?? false;
    message.graphicsEnablePhysics = object.graphicsEnablePhysics ?? false;
    message.graphicsEnableSpecialEffects = object.graphicsEnableSpecialEffects ?? false;
    message.graphicsEnableAutoAnimateToInstruments = object.graphicsEnableAutoAnimateToInstruments ?? false;
    message.graphicsEnableAntialias = object.graphicsEnableAntialias ?? false;
    message.graphicsEnableOrchestraModels = object.graphicsEnableOrchestraModels ?? false;
    message.graphicsRenderEvenInBackground = object.graphicsRenderEvenInBackground ?? false;
    message.graphicsEnableAmbientOcclusion = object.graphicsEnableAmbientOcclusion ?? false;
    message.graphicsEnableDepthOfField = object.graphicsEnableDepthOfField ?? false;
    message.graphicsUseOffscreenCanvas = object.graphicsUseOffscreenCanvas ?? false;
    message.chatDisableMarkdown = object.chatDisableMarkdown ?? false;
    message.chatEnableImageUrlPreview = object.chatEnableImageUrlPreview ?? false;
    message.enableDesktopNotifications = object.enableDesktopNotifications ?? false;
    message.allowUsersToNotifyMe = object.allowUsersToNotifyMe ?? false;
    message.audioSampleRate = object.audioSampleRate ?? 0;
    message.audioIgnoreSoundfontVolumeEnvDelay = object.audioIgnoreSoundfontVolumeEnvDelay ?? false;
    message.audioIgnoreSoundfontVolumeEnvDelay2 = object.audioIgnoreSoundfontVolumeEnvDelay2 ?? false;
    message.audioUseSeparateDrumKit = object.audioUseSeparateDrumKit ?? false;
    message.audioReverbLevel = object.audioReverbLevel ?? 0;
    message.audioReverbDamp = object.audioReverbDamp ?? 0;
    message.audioReverbWidth = object.audioReverbWidth ?? 0;
    message.audioReverbRoomsize = object.audioReverbRoomsize ?? 0;
    message.audioCacheSoundfontsWeb = object.audioCacheSoundfontsWeb ?? false;
    message.desktopSaveSoundfonts = object.desktopSaveSoundfonts ?? false;
    message.uiEnableUserNoteActivities = object.uiEnableUserNoteActivities ?? false;
    message.webglPowerPreference = object.webglPowerPreference ?? "";
    message.enableDebugMode = object.enableDebugMode ?? false;
    message.autoCheckForUpdates = object.autoCheckForUpdates ?? false;
    message.audioEqualizerPreset = object.audioEqualizerPreset ?? 0;
    message.gameManiaDisableBackground = object.gameManiaDisableBackground ?? false;
    message.audioSfxEnable = object.audioSfxEnable ?? false;
    message.audioSfxGlobalVolume = object.audioSfxGlobalVolume ?? 0;
    message.audioSfxStageEffectsGlobalVolume = object.audioSfxStageEffectsGlobalVolume ?? 0;
    message.audioSfxHoverVolume = object.audioSfxHoverVolume ?? 0;
    message.audioSfxChatEnable = object.audioSfxChatEnable ?? false;
    message.audioEnableStageSfx = object.audioEnableStageSfx ?? false;
    message.audioSynthEngine = object.audioSynthEngine ?? 0;
    message.audioGlobalUsersVelocityPercentage = object.audioGlobalUsersVelocityPercentage ?? 0;
    message.audioMultimodeMaxChannels = object.audioMultimodeMaxChannels ?? 0;
    message.appAutoLoginEnable = object.appAutoLoginEnable ?? false;
    message.audioBgMusicEnable = object.audioBgMusicEnable ?? false;
    message.audioBgMusicGlobalVolume = object.audioBgMusicGlobalVolume ?? 0;
    message.onlineWarnAboutExternalLinks = object.onlineWarnAboutExternalLinks ?? false;
    message.enablePlugins = object.enablePlugins ?? false;
    message.keyboardShiftKeyAutoNoteOff = object.keyboardShiftKeyAutoNoteOff ?? false;
    message.audioReverbDefaultMigratedVersion = object.audioReverbDefaultMigratedVersion ?? "";
    message.graphicsEnableFog = object.graphicsEnableFog ?? false;
    message.graphicsEnableShadows = object.graphicsEnableShadows ?? false;
    message.graphicsEnableStage = object.graphicsEnableStage ?? false;
    message.graphicsEnablePiano = object.graphicsEnablePiano ?? false;
    message.graphicsEnableMotionBlur = object.graphicsEnableMotionBlur ?? false;
    message.graphicsEnableGlow = object.graphicsEnableGlow ?? false;
    message.graphicsEnableDrums = object.graphicsEnableDrums ?? false;
    message.graphicsEnableAllParticles = object.graphicsEnableAllParticles ?? false;
    message.audioUseWorklet = object.audioUseWorklet ?? false;
    message.uiEnableSynthNoteActivities = object.uiEnableSynthNoteActivities ?? false;
    message.graphicsEnableSoftShadows = object.graphicsEnableSoftShadows ?? false;
    message.graphicsEnablePostProcessing = object.graphicsEnablePostProcessing ?? false;
    message.graphicsTargetFps = object.graphicsTargetFps ?? "";
    message.graphicsEnableLights = object.graphicsEnableLights ?? false;
    message.graphicsEnableBloom = object.graphicsEnableBloom ?? false;
    message.graphicsEnableToneMapping = object.graphicsEnableToneMapping ?? false;
    message.graphicsEnableHdr = object.graphicsEnableHdr ?? false;
    message.graphicsEnableWebgpu = object.graphicsEnableWebgpu ?? false;
    message.graphicsEnableAnimations = object.graphicsEnableAnimations ?? false;
    message.graphicsMsaaSamples = object.graphicsMsaaSamples ?? 0;
    message.graphicsPreset = object.graphicsPreset ?? 0;
    message.graphicsShadowFilter = object.graphicsShadowFilter ?? 0;
    message.graphicsEnableEngine = object.graphicsEnableEngine ?? false;
    message.graphicsUseLowPolyModels = object.graphicsUseLowPolyModels ?? false;
    message.graphicsEnableGuitars = object.graphicsEnableGuitars ?? false;
    message.chatMessagesMinimized = object.chatMessagesMinimized ?? false;
    message.mutedNotesSelf = object.mutedNotesSelf ?? false;
    message.sendWhoistyping = object.sendWhoistyping ?? false;
    message.chatMessagesMaximized = object.chatMessagesMaximized ?? false;
    message.audioEnableEqualizer = object.audioEnableEqualizer ?? false;
    message.audioMaxNoteOnTime = object.audioMaxNoteOnTime ?? 0;
    message.audioMaxVelocity = object.audioMaxVelocity ?? 0;
    message.audioMinVelocity = object.audioMinVelocity ?? 0;
    message.audioMinVolumeRelease = object.audioMinVolumeRelease ?? 0;
    message.inputCtrlKeyLowersOctave = object.inputCtrlKeyLowersOctave ?? false;
    message.inputMidiToQwertyMod = object.inputMidiToQwertyMod ?? false;
    message.inputMidiToQwertyModUseCapslock = object.inputMidiToQwertyModUseCapslock ?? false;
    message.slotMode = object.slotMode ?? 0;
    message.audioUseVelocityCurve = object.audioUseVelocityCurve ?? false;
    return message;
  },
};

function createBaseAppCommonEnvironment(): AppCommonEnvironment {
  return {
    offlineDevMode: false,
    colyseusHost: "",
    colyseusPort: "",
    HOST: "",
    wsHost: "",
    docsHost: "",
    fullHost: "",
    seqUrl: "",
    statusPageUrl: "",
    expressApiHost: "",
    metricsUrl: "",
    websocketProtocol: "",
    PORT: 0,
    assetsUrl: "",
    assetsProxyUrl: "",
    MODE: "",
    sentryDsn: "",
    isLocalDev: false,
    isTestMode: false,
    isDevMode: false,
    isDesktopApp: false,
    isWebApp: false,
    isProduction: false,
    isStaging: false,
    isAutomatedTestMode: false,
    clientVersion: "",
    clientBuildDate: "",
    rendererWasmFilePath: "",
    rendererJsFilePath: "",
    synthJsFilePath: "",
    coreJsFilePath: "",
    maniaJsFilePath: "",
    dawJsFilePath: "",
    soundfontCachePrefix: "",
  };
}

export const AppCommonEnvironment = {
  encode(message: AppCommonEnvironment, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.offlineDevMode !== false) {
      writer.uint32(8).bool(message.offlineDevMode);
    }
    if (message.colyseusHost !== "") {
      writer.uint32(18).string(message.colyseusHost);
    }
    if (message.colyseusPort !== "") {
      writer.uint32(26).string(message.colyseusPort);
    }
    if (message.HOST !== "") {
      writer.uint32(34).string(message.HOST);
    }
    if (message.wsHost !== "") {
      writer.uint32(42).string(message.wsHost);
    }
    if (message.docsHost !== "") {
      writer.uint32(50).string(message.docsHost);
    }
    if (message.fullHost !== "") {
      writer.uint32(58).string(message.fullHost);
    }
    if (message.seqUrl !== "") {
      writer.uint32(66).string(message.seqUrl);
    }
    if (message.statusPageUrl !== "") {
      writer.uint32(74).string(message.statusPageUrl);
    }
    if (message.expressApiHost !== "") {
      writer.uint32(82).string(message.expressApiHost);
    }
    if (message.metricsUrl !== "") {
      writer.uint32(90).string(message.metricsUrl);
    }
    if (message.websocketProtocol !== "") {
      writer.uint32(98).string(message.websocketProtocol);
    }
    if (message.PORT !== 0) {
      writer.uint32(104).int32(message.PORT);
    }
    if (message.assetsUrl !== "") {
      writer.uint32(114).string(message.assetsUrl);
    }
    if (message.assetsProxyUrl !== "") {
      writer.uint32(122).string(message.assetsProxyUrl);
    }
    if (message.MODE !== "") {
      writer.uint32(130).string(message.MODE);
    }
    if (message.sentryDsn !== "") {
      writer.uint32(138).string(message.sentryDsn);
    }
    if (message.isLocalDev !== false) {
      writer.uint32(144).bool(message.isLocalDev);
    }
    if (message.isTestMode !== false) {
      writer.uint32(152).bool(message.isTestMode);
    }
    if (message.isDevMode !== false) {
      writer.uint32(160).bool(message.isDevMode);
    }
    if (message.isDesktopApp !== false) {
      writer.uint32(168).bool(message.isDesktopApp);
    }
    if (message.isWebApp !== false) {
      writer.uint32(176).bool(message.isWebApp);
    }
    if (message.isProduction !== false) {
      writer.uint32(184).bool(message.isProduction);
    }
    if (message.isStaging !== false) {
      writer.uint32(192).bool(message.isStaging);
    }
    if (message.isAutomatedTestMode !== false) {
      writer.uint32(200).bool(message.isAutomatedTestMode);
    }
    if (message.clientVersion !== "") {
      writer.uint32(210).string(message.clientVersion);
    }
    if (message.clientBuildDate !== "") {
      writer.uint32(218).string(message.clientBuildDate);
    }
    if (message.rendererWasmFilePath !== "") {
      writer.uint32(226).string(message.rendererWasmFilePath);
    }
    if (message.rendererJsFilePath !== "") {
      writer.uint32(234).string(message.rendererJsFilePath);
    }
    if (message.synthJsFilePath !== "") {
      writer.uint32(242).string(message.synthJsFilePath);
    }
    if (message.coreJsFilePath !== "") {
      writer.uint32(250).string(message.coreJsFilePath);
    }
    if (message.maniaJsFilePath !== "") {
      writer.uint32(258).string(message.maniaJsFilePath);
    }
    if (message.dawJsFilePath !== "") {
      writer.uint32(266).string(message.dawJsFilePath);
    }
    if (message.soundfontCachePrefix !== "") {
      writer.uint32(274).string(message.soundfontCachePrefix);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppCommonEnvironment {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppCommonEnvironment();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.offlineDevMode = reader.bool();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.colyseusHost = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.colyseusPort = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.HOST = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.wsHost = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.docsHost = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.fullHost = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.seqUrl = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.statusPageUrl = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.expressApiHost = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.metricsUrl = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.websocketProtocol = reader.string();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.PORT = reader.int32();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.assetsUrl = reader.string();
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.assetsProxyUrl = reader.string();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.MODE = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.sentryDsn = reader.string();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.isLocalDev = reader.bool();
          continue;
        case 19:
          if (tag !== 152) {
            break;
          }

          message.isTestMode = reader.bool();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.isDevMode = reader.bool();
          continue;
        case 21:
          if (tag !== 168) {
            break;
          }

          message.isDesktopApp = reader.bool();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.isWebApp = reader.bool();
          continue;
        case 23:
          if (tag !== 184) {
            break;
          }

          message.isProduction = reader.bool();
          continue;
        case 24:
          if (tag !== 192) {
            break;
          }

          message.isStaging = reader.bool();
          continue;
        case 25:
          if (tag !== 200) {
            break;
          }

          message.isAutomatedTestMode = reader.bool();
          continue;
        case 26:
          if (tag !== 210) {
            break;
          }

          message.clientVersion = reader.string();
          continue;
        case 27:
          if (tag !== 218) {
            break;
          }

          message.clientBuildDate = reader.string();
          continue;
        case 28:
          if (tag !== 226) {
            break;
          }

          message.rendererWasmFilePath = reader.string();
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.rendererJsFilePath = reader.string();
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.synthJsFilePath = reader.string();
          continue;
        case 31:
          if (tag !== 250) {
            break;
          }

          message.coreJsFilePath = reader.string();
          continue;
        case 32:
          if (tag !== 258) {
            break;
          }

          message.maniaJsFilePath = reader.string();
          continue;
        case 33:
          if (tag !== 266) {
            break;
          }

          message.dawJsFilePath = reader.string();
          continue;
        case 34:
          if (tag !== 274) {
            break;
          }

          message.soundfontCachePrefix = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppCommonEnvironment {
    return {
      offlineDevMode: isSet(object.OFFLINEDEVMODE) ? globalThis.Boolean(object.OFFLINEDEVMODE) : false,
      colyseusHost: isSet(object.COLYSEUSHOST) ? globalThis.String(object.COLYSEUSHOST) : "",
      colyseusPort: isSet(object.COLYSEUSPORT) ? globalThis.String(object.COLYSEUSPORT) : "",
      HOST: isSet(object.HOST) ? globalThis.String(object.HOST) : "",
      wsHost: isSet(object.WSHOST) ? globalThis.String(object.WSHOST) : "",
      docsHost: isSet(object.DOCSHOST) ? globalThis.String(object.DOCSHOST) : "",
      fullHost: isSet(object.FULLHOST) ? globalThis.String(object.FULLHOST) : "",
      seqUrl: isSet(object.SEQURL) ? globalThis.String(object.SEQURL) : "",
      statusPageUrl: isSet(object.STATUSPAGEURL) ? globalThis.String(object.STATUSPAGEURL) : "",
      expressApiHost: isSet(object.EXPRESSAPIHOST) ? globalThis.String(object.EXPRESSAPIHOST) : "",
      metricsUrl: isSet(object.METRICSURL) ? globalThis.String(object.METRICSURL) : "",
      websocketProtocol: isSet(object.WEBSOCKETPROTOCOL) ? globalThis.String(object.WEBSOCKETPROTOCOL) : "",
      PORT: isSet(object.PORT) ? globalThis.Number(object.PORT) : 0,
      assetsUrl: isSet(object.ASSETSURL) ? globalThis.String(object.ASSETSURL) : "",
      assetsProxyUrl: isSet(object.ASSETSPROXYURL) ? globalThis.String(object.ASSETSPROXYURL) : "",
      MODE: isSet(object.MODE) ? globalThis.String(object.MODE) : "",
      sentryDsn: isSet(object.SENTRYDSN) ? globalThis.String(object.SENTRYDSN) : "",
      isLocalDev: isSet(object.ISLOCALDEV) ? globalThis.Boolean(object.ISLOCALDEV) : false,
      isTestMode: isSet(object.ISTESTMODE) ? globalThis.Boolean(object.ISTESTMODE) : false,
      isDevMode: isSet(object.ISDEVMODE) ? globalThis.Boolean(object.ISDEVMODE) : false,
      isDesktopApp: isSet(object.ISDESKTOPAPP) ? globalThis.Boolean(object.ISDESKTOPAPP) : false,
      isWebApp: isSet(object.ISWEBAPP) ? globalThis.Boolean(object.ISWEBAPP) : false,
      isProduction: isSet(object.ISPRODUCTION) ? globalThis.Boolean(object.ISPRODUCTION) : false,
      isStaging: isSet(object.ISSTAGING) ? globalThis.Boolean(object.ISSTAGING) : false,
      isAutomatedTestMode: isSet(object.ISAUTOMATEDTESTMODE) ? globalThis.Boolean(object.ISAUTOMATEDTESTMODE) : false,
      clientVersion: isSet(object.CLIENTVERSION) ? globalThis.String(object.CLIENTVERSION) : "",
      clientBuildDate: isSet(object.CLIENTBUILDDATE) ? globalThis.String(object.CLIENTBUILDDATE) : "",
      rendererWasmFilePath: isSet(object.RENDERERWASMFILEPATH) ? globalThis.String(object.RENDERERWASMFILEPATH) : "",
      rendererJsFilePath: isSet(object.RENDERERJSFILEPATH) ? globalThis.String(object.RENDERERJSFILEPATH) : "",
      synthJsFilePath: isSet(object.SYNTHJSFILEPATH) ? globalThis.String(object.SYNTHJSFILEPATH) : "",
      coreJsFilePath: isSet(object.COREJSFILEPATH) ? globalThis.String(object.COREJSFILEPATH) : "",
      maniaJsFilePath: isSet(object.MANIAJSFILEPATH) ? globalThis.String(object.MANIAJSFILEPATH) : "",
      dawJsFilePath: isSet(object.DAWJSFILEPATH) ? globalThis.String(object.DAWJSFILEPATH) : "",
      soundfontCachePrefix: isSet(object.SOUNDFONTCACHEPREFIX) ? globalThis.String(object.SOUNDFONTCACHEPREFIX) : "",
    };
  },

  toJSON(message: AppCommonEnvironment): unknown {
    const obj: any = {};
    if (message.offlineDevMode !== false) {
      obj.OFFLINEDEVMODE = message.offlineDevMode;
    }
    if (message.colyseusHost !== "") {
      obj.COLYSEUSHOST = message.colyseusHost;
    }
    if (message.colyseusPort !== "") {
      obj.COLYSEUSPORT = message.colyseusPort;
    }
    if (message.HOST !== "") {
      obj.HOST = message.HOST;
    }
    if (message.wsHost !== "") {
      obj.WSHOST = message.wsHost;
    }
    if (message.docsHost !== "") {
      obj.DOCSHOST = message.docsHost;
    }
    if (message.fullHost !== "") {
      obj.FULLHOST = message.fullHost;
    }
    if (message.seqUrl !== "") {
      obj.SEQURL = message.seqUrl;
    }
    if (message.statusPageUrl !== "") {
      obj.STATUSPAGEURL = message.statusPageUrl;
    }
    if (message.expressApiHost !== "") {
      obj.EXPRESSAPIHOST = message.expressApiHost;
    }
    if (message.metricsUrl !== "") {
      obj.METRICSURL = message.metricsUrl;
    }
    if (message.websocketProtocol !== "") {
      obj.WEBSOCKETPROTOCOL = message.websocketProtocol;
    }
    if (message.PORT !== 0) {
      obj.PORT = Math.round(message.PORT);
    }
    if (message.assetsUrl !== "") {
      obj.ASSETSURL = message.assetsUrl;
    }
    if (message.assetsProxyUrl !== "") {
      obj.ASSETSPROXYURL = message.assetsProxyUrl;
    }
    if (message.MODE !== "") {
      obj.MODE = message.MODE;
    }
    if (message.sentryDsn !== "") {
      obj.SENTRYDSN = message.sentryDsn;
    }
    if (message.isLocalDev !== false) {
      obj.ISLOCALDEV = message.isLocalDev;
    }
    if (message.isTestMode !== false) {
      obj.ISTESTMODE = message.isTestMode;
    }
    if (message.isDevMode !== false) {
      obj.ISDEVMODE = message.isDevMode;
    }
    if (message.isDesktopApp !== false) {
      obj.ISDESKTOPAPP = message.isDesktopApp;
    }
    if (message.isWebApp !== false) {
      obj.ISWEBAPP = message.isWebApp;
    }
    if (message.isProduction !== false) {
      obj.ISPRODUCTION = message.isProduction;
    }
    if (message.isStaging !== false) {
      obj.ISSTAGING = message.isStaging;
    }
    if (message.isAutomatedTestMode !== false) {
      obj.ISAUTOMATEDTESTMODE = message.isAutomatedTestMode;
    }
    if (message.clientVersion !== "") {
      obj.CLIENTVERSION = message.clientVersion;
    }
    if (message.clientBuildDate !== "") {
      obj.CLIENTBUILDDATE = message.clientBuildDate;
    }
    if (message.rendererWasmFilePath !== "") {
      obj.RENDERERWASMFILEPATH = message.rendererWasmFilePath;
    }
    if (message.rendererJsFilePath !== "") {
      obj.RENDERERJSFILEPATH = message.rendererJsFilePath;
    }
    if (message.synthJsFilePath !== "") {
      obj.SYNTHJSFILEPATH = message.synthJsFilePath;
    }
    if (message.coreJsFilePath !== "") {
      obj.COREJSFILEPATH = message.coreJsFilePath;
    }
    if (message.maniaJsFilePath !== "") {
      obj.MANIAJSFILEPATH = message.maniaJsFilePath;
    }
    if (message.dawJsFilePath !== "") {
      obj.DAWJSFILEPATH = message.dawJsFilePath;
    }
    if (message.soundfontCachePrefix !== "") {
      obj.SOUNDFONTCACHEPREFIX = message.soundfontCachePrefix;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppCommonEnvironment>, I>>(base?: I): AppCommonEnvironment {
    return AppCommonEnvironment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppCommonEnvironment>, I>>(object: I): AppCommonEnvironment {
    const message = createBaseAppCommonEnvironment();
    message.offlineDevMode = object.offlineDevMode ?? false;
    message.colyseusHost = object.colyseusHost ?? "";
    message.colyseusPort = object.colyseusPort ?? "";
    message.HOST = object.HOST ?? "";
    message.wsHost = object.wsHost ?? "";
    message.docsHost = object.docsHost ?? "";
    message.fullHost = object.fullHost ?? "";
    message.seqUrl = object.seqUrl ?? "";
    message.statusPageUrl = object.statusPageUrl ?? "";
    message.expressApiHost = object.expressApiHost ?? "";
    message.metricsUrl = object.metricsUrl ?? "";
    message.websocketProtocol = object.websocketProtocol ?? "";
    message.PORT = object.PORT ?? 0;
    message.assetsUrl = object.assetsUrl ?? "";
    message.assetsProxyUrl = object.assetsProxyUrl ?? "";
    message.MODE = object.MODE ?? "";
    message.sentryDsn = object.sentryDsn ?? "";
    message.isLocalDev = object.isLocalDev ?? false;
    message.isTestMode = object.isTestMode ?? false;
    message.isDevMode = object.isDevMode ?? false;
    message.isDesktopApp = object.isDesktopApp ?? false;
    message.isWebApp = object.isWebApp ?? false;
    message.isProduction = object.isProduction ?? false;
    message.isStaging = object.isStaging ?? false;
    message.isAutomatedTestMode = object.isAutomatedTestMode ?? false;
    message.clientVersion = object.clientVersion ?? "";
    message.clientBuildDate = object.clientBuildDate ?? "";
    message.rendererWasmFilePath = object.rendererWasmFilePath ?? "";
    message.rendererJsFilePath = object.rendererJsFilePath ?? "";
    message.synthJsFilePath = object.synthJsFilePath ?? "";
    message.coreJsFilePath = object.coreJsFilePath ?? "";
    message.maniaJsFilePath = object.maniaJsFilePath ?? "";
    message.dawJsFilePath = object.dawJsFilePath ?? "";
    message.soundfontCachePrefix = object.soundfontCachePrefix ?? "";
    return message;
  },
};

function createBaseAppPianoKey(): AppPianoKey {
  return {
    keyType: 0,
    midiID: 0,
    positionX: undefined,
    positionY: undefined,
    positionZ: undefined,
    rotationX: undefined,
    rotationY: undefined,
    rotationZ: undefined,
    colorR: undefined,
    colorG: undefined,
    colorB: undefined,
    colorA: undefined,
  };
}

export const AppPianoKey = {
  encode(message: AppPianoKey, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.keyType !== 0) {
      writer.uint32(8).int32(message.keyType);
    }
    if (message.midiID !== 0) {
      writer.uint32(16).uint32(message.midiID);
    }
    if (message.positionX !== undefined) {
      writer.uint32(29).float(message.positionX);
    }
    if (message.positionY !== undefined) {
      writer.uint32(37).float(message.positionY);
    }
    if (message.positionZ !== undefined) {
      writer.uint32(45).float(message.positionZ);
    }
    if (message.rotationX !== undefined) {
      writer.uint32(53).float(message.rotationX);
    }
    if (message.rotationY !== undefined) {
      writer.uint32(61).float(message.rotationY);
    }
    if (message.rotationZ !== undefined) {
      writer.uint32(69).float(message.rotationZ);
    }
    if (message.colorR !== undefined) {
      writer.uint32(77).float(message.colorR);
    }
    if (message.colorG !== undefined) {
      writer.uint32(85).float(message.colorG);
    }
    if (message.colorB !== undefined) {
      writer.uint32(93).float(message.colorB);
    }
    if (message.colorA !== undefined) {
      writer.uint32(101).float(message.colorA);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppPianoKey {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppPianoKey();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.keyType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.midiID = reader.uint32();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.positionX = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.positionY = reader.float();
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.positionZ = reader.float();
          continue;
        case 6:
          if (tag !== 53) {
            break;
          }

          message.rotationX = reader.float();
          continue;
        case 7:
          if (tag !== 61) {
            break;
          }

          message.rotationY = reader.float();
          continue;
        case 8:
          if (tag !== 69) {
            break;
          }

          message.rotationZ = reader.float();
          continue;
        case 9:
          if (tag !== 77) {
            break;
          }

          message.colorR = reader.float();
          continue;
        case 10:
          if (tag !== 85) {
            break;
          }

          message.colorG = reader.float();
          continue;
        case 11:
          if (tag !== 93) {
            break;
          }

          message.colorB = reader.float();
          continue;
        case 12:
          if (tag !== 101) {
            break;
          }

          message.colorA = reader.float();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppPianoKey {
    return {
      keyType: isSet(object.keyType) ? appPianoKeyTypeFromJSON(object.keyType) : 0,
      midiID: isSet(object.midiID) ? globalThis.Number(object.midiID) : 0,
      positionX: isSet(object.positionX) ? globalThis.Number(object.positionX) : undefined,
      positionY: isSet(object.positionY) ? globalThis.Number(object.positionY) : undefined,
      positionZ: isSet(object.positionZ) ? globalThis.Number(object.positionZ) : undefined,
      rotationX: isSet(object.rotationX) ? globalThis.Number(object.rotationX) : undefined,
      rotationY: isSet(object.rotationY) ? globalThis.Number(object.rotationY) : undefined,
      rotationZ: isSet(object.rotationZ) ? globalThis.Number(object.rotationZ) : undefined,
      colorR: isSet(object.colorR) ? globalThis.Number(object.colorR) : undefined,
      colorG: isSet(object.colorG) ? globalThis.Number(object.colorG) : undefined,
      colorB: isSet(object.colorB) ? globalThis.Number(object.colorB) : undefined,
      colorA: isSet(object.colorA) ? globalThis.Number(object.colorA) : undefined,
    };
  },

  toJSON(message: AppPianoKey): unknown {
    const obj: any = {};
    if (message.keyType !== 0) {
      obj.keyType = appPianoKeyTypeToJSON(message.keyType);
    }
    if (message.midiID !== 0) {
      obj.midiID = Math.round(message.midiID);
    }
    if (message.positionX !== undefined) {
      obj.positionX = message.positionX;
    }
    if (message.positionY !== undefined) {
      obj.positionY = message.positionY;
    }
    if (message.positionZ !== undefined) {
      obj.positionZ = message.positionZ;
    }
    if (message.rotationX !== undefined) {
      obj.rotationX = message.rotationX;
    }
    if (message.rotationY !== undefined) {
      obj.rotationY = message.rotationY;
    }
    if (message.rotationZ !== undefined) {
      obj.rotationZ = message.rotationZ;
    }
    if (message.colorR !== undefined) {
      obj.colorR = message.colorR;
    }
    if (message.colorG !== undefined) {
      obj.colorG = message.colorG;
    }
    if (message.colorB !== undefined) {
      obj.colorB = message.colorB;
    }
    if (message.colorA !== undefined) {
      obj.colorA = message.colorA;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppPianoKey>, I>>(base?: I): AppPianoKey {
    return AppPianoKey.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppPianoKey>, I>>(object: I): AppPianoKey {
    const message = createBaseAppPianoKey();
    message.keyType = object.keyType ?? 0;
    message.midiID = object.midiID ?? 0;
    message.positionX = object.positionX ?? undefined;
    message.positionY = object.positionY ?? undefined;
    message.positionZ = object.positionZ ?? undefined;
    message.rotationX = object.rotationX ?? undefined;
    message.rotationY = object.rotationY ?? undefined;
    message.rotationZ = object.rotationZ ?? undefined;
    message.colorR = object.colorR ?? undefined;
    message.colorG = object.colorG ?? undefined;
    message.colorB = object.colorB ?? undefined;
    message.colorA = object.colorA ?? undefined;
    return message;
  },
};

function createBaseAppPianoPedal(): AppPianoPedal {
  return {
    id: "",
    name: undefined,
    positionX: undefined,
    positionY: undefined,
    positionZ: undefined,
    rotationX: undefined,
    rotationY: undefined,
    rotationZ: undefined,
    colorR: undefined,
    colorG: undefined,
    colorB: undefined,
    colorA: undefined,
    type: 0,
  };
}

export const AppPianoPedal = {
  encode(message: AppPianoPedal, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== undefined) {
      writer.uint32(18).string(message.name);
    }
    if (message.positionX !== undefined) {
      writer.uint32(29).float(message.positionX);
    }
    if (message.positionY !== undefined) {
      writer.uint32(37).float(message.positionY);
    }
    if (message.positionZ !== undefined) {
      writer.uint32(45).float(message.positionZ);
    }
    if (message.rotationX !== undefined) {
      writer.uint32(53).float(message.rotationX);
    }
    if (message.rotationY !== undefined) {
      writer.uint32(61).float(message.rotationY);
    }
    if (message.rotationZ !== undefined) {
      writer.uint32(69).float(message.rotationZ);
    }
    if (message.colorR !== undefined) {
      writer.uint32(77).float(message.colorR);
    }
    if (message.colorG !== undefined) {
      writer.uint32(85).float(message.colorG);
    }
    if (message.colorB !== undefined) {
      writer.uint32(93).float(message.colorB);
    }
    if (message.colorA !== undefined) {
      writer.uint32(101).float(message.colorA);
    }
    if (message.type !== 0) {
      writer.uint32(104).int32(message.type);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppPianoPedal {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppPianoPedal();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.positionX = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.positionY = reader.float();
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.positionZ = reader.float();
          continue;
        case 6:
          if (tag !== 53) {
            break;
          }

          message.rotationX = reader.float();
          continue;
        case 7:
          if (tag !== 61) {
            break;
          }

          message.rotationY = reader.float();
          continue;
        case 8:
          if (tag !== 69) {
            break;
          }

          message.rotationZ = reader.float();
          continue;
        case 9:
          if (tag !== 77) {
            break;
          }

          message.colorR = reader.float();
          continue;
        case 10:
          if (tag !== 85) {
            break;
          }

          message.colorG = reader.float();
          continue;
        case 11:
          if (tag !== 93) {
            break;
          }

          message.colorB = reader.float();
          continue;
        case 12:
          if (tag !== 101) {
            break;
          }

          message.colorA = reader.float();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.type = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppPianoPedal {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      positionX: isSet(object.positionX) ? globalThis.Number(object.positionX) : undefined,
      positionY: isSet(object.positionY) ? globalThis.Number(object.positionY) : undefined,
      positionZ: isSet(object.positionZ) ? globalThis.Number(object.positionZ) : undefined,
      rotationX: isSet(object.rotationX) ? globalThis.Number(object.rotationX) : undefined,
      rotationY: isSet(object.rotationY) ? globalThis.Number(object.rotationY) : undefined,
      rotationZ: isSet(object.rotationZ) ? globalThis.Number(object.rotationZ) : undefined,
      colorR: isSet(object.colorR) ? globalThis.Number(object.colorR) : undefined,
      colorG: isSet(object.colorG) ? globalThis.Number(object.colorG) : undefined,
      colorB: isSet(object.colorB) ? globalThis.Number(object.colorB) : undefined,
      colorA: isSet(object.colorA) ? globalThis.Number(object.colorA) : undefined,
      type: isSet(object.type) ? appPianoPedalTypeFromJSON(object.type) : 0,
    };
  },

  toJSON(message: AppPianoPedal): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.positionX !== undefined) {
      obj.positionX = message.positionX;
    }
    if (message.positionY !== undefined) {
      obj.positionY = message.positionY;
    }
    if (message.positionZ !== undefined) {
      obj.positionZ = message.positionZ;
    }
    if (message.rotationX !== undefined) {
      obj.rotationX = message.rotationX;
    }
    if (message.rotationY !== undefined) {
      obj.rotationY = message.rotationY;
    }
    if (message.rotationZ !== undefined) {
      obj.rotationZ = message.rotationZ;
    }
    if (message.colorR !== undefined) {
      obj.colorR = message.colorR;
    }
    if (message.colorG !== undefined) {
      obj.colorG = message.colorG;
    }
    if (message.colorB !== undefined) {
      obj.colorB = message.colorB;
    }
    if (message.colorA !== undefined) {
      obj.colorA = message.colorA;
    }
    if (message.type !== 0) {
      obj.type = appPianoPedalTypeToJSON(message.type);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppPianoPedal>, I>>(base?: I): AppPianoPedal {
    return AppPianoPedal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppPianoPedal>, I>>(object: I): AppPianoPedal {
    const message = createBaseAppPianoPedal();
    message.id = object.id ?? "";
    message.name = object.name ?? undefined;
    message.positionX = object.positionX ?? undefined;
    message.positionY = object.positionY ?? undefined;
    message.positionZ = object.positionZ ?? undefined;
    message.rotationX = object.rotationX ?? undefined;
    message.rotationY = object.rotationY ?? undefined;
    message.rotationZ = object.rotationZ ?? undefined;
    message.colorR = object.colorR ?? undefined;
    message.colorG = object.colorG ?? undefined;
    message.colorB = object.colorB ?? undefined;
    message.colorA = object.colorA ?? undefined;
    message.type = object.type ?? 0;
    return message;
  },
};

function createBaseAppRenderableEntity(): AppRenderableEntity {
  return {
    id: "",
    name: undefined,
    positionX: undefined,
    positionY: undefined,
    positionZ: undefined,
    rotationX: undefined,
    rotationY: undefined,
    rotationZ: undefined,
    colorR: undefined,
    colorG: undefined,
    colorB: undefined,
    colorA: undefined,
    alpha: undefined,
    beta: undefined,
    radius: undefined,
    targetX: undefined,
    targetY: undefined,
    targetZ: undefined,
    scaleX: undefined,
    scaleY: undefined,
    scaleZ: undefined,
    visible: undefined,
  };
}

export const AppRenderableEntity = {
  encode(message: AppRenderableEntity, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.name !== undefined) {
      writer.uint32(18).string(message.name);
    }
    if (message.positionX !== undefined) {
      writer.uint32(29).float(message.positionX);
    }
    if (message.positionY !== undefined) {
      writer.uint32(37).float(message.positionY);
    }
    if (message.positionZ !== undefined) {
      writer.uint32(45).float(message.positionZ);
    }
    if (message.rotationX !== undefined) {
      writer.uint32(53).float(message.rotationX);
    }
    if (message.rotationY !== undefined) {
      writer.uint32(61).float(message.rotationY);
    }
    if (message.rotationZ !== undefined) {
      writer.uint32(69).float(message.rotationZ);
    }
    if (message.colorR !== undefined) {
      writer.uint32(77).float(message.colorR);
    }
    if (message.colorG !== undefined) {
      writer.uint32(85).float(message.colorG);
    }
    if (message.colorB !== undefined) {
      writer.uint32(93).float(message.colorB);
    }
    if (message.colorA !== undefined) {
      writer.uint32(101).float(message.colorA);
    }
    if (message.alpha !== undefined) {
      writer.uint32(109).float(message.alpha);
    }
    if (message.beta !== undefined) {
      writer.uint32(117).float(message.beta);
    }
    if (message.radius !== undefined) {
      writer.uint32(125).float(message.radius);
    }
    if (message.targetX !== undefined) {
      writer.uint32(133).float(message.targetX);
    }
    if (message.targetY !== undefined) {
      writer.uint32(141).float(message.targetY);
    }
    if (message.targetZ !== undefined) {
      writer.uint32(149).float(message.targetZ);
    }
    if (message.scaleX !== undefined) {
      writer.uint32(157).float(message.scaleX);
    }
    if (message.scaleY !== undefined) {
      writer.uint32(165).float(message.scaleY);
    }
    if (message.scaleZ !== undefined) {
      writer.uint32(173).float(message.scaleZ);
    }
    if (message.visible !== undefined) {
      writer.uint32(176).bool(message.visible);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppRenderableEntity {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppRenderableEntity();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }

          message.positionX = reader.float();
          continue;
        case 4:
          if (tag !== 37) {
            break;
          }

          message.positionY = reader.float();
          continue;
        case 5:
          if (tag !== 45) {
            break;
          }

          message.positionZ = reader.float();
          continue;
        case 6:
          if (tag !== 53) {
            break;
          }

          message.rotationX = reader.float();
          continue;
        case 7:
          if (tag !== 61) {
            break;
          }

          message.rotationY = reader.float();
          continue;
        case 8:
          if (tag !== 69) {
            break;
          }

          message.rotationZ = reader.float();
          continue;
        case 9:
          if (tag !== 77) {
            break;
          }

          message.colorR = reader.float();
          continue;
        case 10:
          if (tag !== 85) {
            break;
          }

          message.colorG = reader.float();
          continue;
        case 11:
          if (tag !== 93) {
            break;
          }

          message.colorB = reader.float();
          continue;
        case 12:
          if (tag !== 101) {
            break;
          }

          message.colorA = reader.float();
          continue;
        case 13:
          if (tag !== 109) {
            break;
          }

          message.alpha = reader.float();
          continue;
        case 14:
          if (tag !== 117) {
            break;
          }

          message.beta = reader.float();
          continue;
        case 15:
          if (tag !== 125) {
            break;
          }

          message.radius = reader.float();
          continue;
        case 16:
          if (tag !== 133) {
            break;
          }

          message.targetX = reader.float();
          continue;
        case 17:
          if (tag !== 141) {
            break;
          }

          message.targetY = reader.float();
          continue;
        case 18:
          if (tag !== 149) {
            break;
          }

          message.targetZ = reader.float();
          continue;
        case 19:
          if (tag !== 157) {
            break;
          }

          message.scaleX = reader.float();
          continue;
        case 20:
          if (tag !== 165) {
            break;
          }

          message.scaleY = reader.float();
          continue;
        case 21:
          if (tag !== 173) {
            break;
          }

          message.scaleZ = reader.float();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.visible = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppRenderableEntity {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      positionX: isSet(object.positionX) ? globalThis.Number(object.positionX) : undefined,
      positionY: isSet(object.positionY) ? globalThis.Number(object.positionY) : undefined,
      positionZ: isSet(object.positionZ) ? globalThis.Number(object.positionZ) : undefined,
      rotationX: isSet(object.rotationX) ? globalThis.Number(object.rotationX) : undefined,
      rotationY: isSet(object.rotationY) ? globalThis.Number(object.rotationY) : undefined,
      rotationZ: isSet(object.rotationZ) ? globalThis.Number(object.rotationZ) : undefined,
      colorR: isSet(object.colorR) ? globalThis.Number(object.colorR) : undefined,
      colorG: isSet(object.colorG) ? globalThis.Number(object.colorG) : undefined,
      colorB: isSet(object.colorB) ? globalThis.Number(object.colorB) : undefined,
      colorA: isSet(object.colorA) ? globalThis.Number(object.colorA) : undefined,
      alpha: isSet(object.alpha) ? globalThis.Number(object.alpha) : undefined,
      beta: isSet(object.beta) ? globalThis.Number(object.beta) : undefined,
      radius: isSet(object.radius) ? globalThis.Number(object.radius) : undefined,
      targetX: isSet(object.targetX) ? globalThis.Number(object.targetX) : undefined,
      targetY: isSet(object.targetY) ? globalThis.Number(object.targetY) : undefined,
      targetZ: isSet(object.targetZ) ? globalThis.Number(object.targetZ) : undefined,
      scaleX: isSet(object.scaleX) ? globalThis.Number(object.scaleX) : undefined,
      scaleY: isSet(object.scaleY) ? globalThis.Number(object.scaleY) : undefined,
      scaleZ: isSet(object.scaleZ) ? globalThis.Number(object.scaleZ) : undefined,
      visible: isSet(object.visible) ? globalThis.Boolean(object.visible) : undefined,
    };
  },

  toJSON(message: AppRenderableEntity): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.positionX !== undefined) {
      obj.positionX = message.positionX;
    }
    if (message.positionY !== undefined) {
      obj.positionY = message.positionY;
    }
    if (message.positionZ !== undefined) {
      obj.positionZ = message.positionZ;
    }
    if (message.rotationX !== undefined) {
      obj.rotationX = message.rotationX;
    }
    if (message.rotationY !== undefined) {
      obj.rotationY = message.rotationY;
    }
    if (message.rotationZ !== undefined) {
      obj.rotationZ = message.rotationZ;
    }
    if (message.colorR !== undefined) {
      obj.colorR = message.colorR;
    }
    if (message.colorG !== undefined) {
      obj.colorG = message.colorG;
    }
    if (message.colorB !== undefined) {
      obj.colorB = message.colorB;
    }
    if (message.colorA !== undefined) {
      obj.colorA = message.colorA;
    }
    if (message.alpha !== undefined) {
      obj.alpha = message.alpha;
    }
    if (message.beta !== undefined) {
      obj.beta = message.beta;
    }
    if (message.radius !== undefined) {
      obj.radius = message.radius;
    }
    if (message.targetX !== undefined) {
      obj.targetX = message.targetX;
    }
    if (message.targetY !== undefined) {
      obj.targetY = message.targetY;
    }
    if (message.targetZ !== undefined) {
      obj.targetZ = message.targetZ;
    }
    if (message.scaleX !== undefined) {
      obj.scaleX = message.scaleX;
    }
    if (message.scaleY !== undefined) {
      obj.scaleY = message.scaleY;
    }
    if (message.scaleZ !== undefined) {
      obj.scaleZ = message.scaleZ;
    }
    if (message.visible !== undefined) {
      obj.visible = message.visible;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppRenderableEntity>, I>>(base?: I): AppRenderableEntity {
    return AppRenderableEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppRenderableEntity>, I>>(object: I): AppRenderableEntity {
    const message = createBaseAppRenderableEntity();
    message.id = object.id ?? "";
    message.name = object.name ?? undefined;
    message.positionX = object.positionX ?? undefined;
    message.positionY = object.positionY ?? undefined;
    message.positionZ = object.positionZ ?? undefined;
    message.rotationX = object.rotationX ?? undefined;
    message.rotationY = object.rotationY ?? undefined;
    message.rotationZ = object.rotationZ ?? undefined;
    message.colorR = object.colorR ?? undefined;
    message.colorG = object.colorG ?? undefined;
    message.colorB = object.colorB ?? undefined;
    message.colorA = object.colorA ?? undefined;
    message.alpha = object.alpha ?? undefined;
    message.beta = object.beta ?? undefined;
    message.radius = object.radius ?? undefined;
    message.targetX = object.targetX ?? undefined;
    message.targetY = object.targetY ?? undefined;
    message.targetZ = object.targetZ ?? undefined;
    message.scaleX = object.scaleX ?? undefined;
    message.scaleY = object.scaleY ?? undefined;
    message.scaleZ = object.scaleZ ?? undefined;
    message.visible = object.visible ?? undefined;
    return message;
  },
};

function createBaseAppPageloaderDetail(): AppPageloaderDetail {
  return { active: false, details: undefined };
}

export const AppPageloaderDetail = {
  encode(message: AppPageloaderDetail, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.active !== false) {
      writer.uint32(8).bool(message.active);
    }
    if (message.details !== undefined) {
      writer.uint32(18).string(message.details);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppPageloaderDetail {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppPageloaderDetail();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.active = reader.bool();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.details = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppPageloaderDetail {
    return {
      active: isSet(object.active) ? globalThis.Boolean(object.active) : false,
      details: isSet(object.details) ? globalThis.String(object.details) : undefined,
    };
  },

  toJSON(message: AppPageloaderDetail): unknown {
    const obj: any = {};
    if (message.active !== false) {
      obj.active = message.active;
    }
    if (message.details !== undefined) {
      obj.details = message.details;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppPageloaderDetail>, I>>(base?: I): AppPageloaderDetail {
    return AppPageloaderDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppPageloaderDetail>, I>>(object: I): AppPageloaderDetail {
    const message = createBaseAppPageloaderDetail();
    message.active = object.active ?? false;
    message.details = object.details ?? undefined;
    return message;
  },
};

function createBaseAppNotificationConfig(): AppNotificationConfig {
  return {
    id: "",
    status: undefined,
    title: undefined,
    description: undefined,
    duration: undefined,
    persistent: undefined,
    closable: undefined,
    loading: undefined,
  };
}

export const AppNotificationConfig = {
  encode(message: AppNotificationConfig, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== "") {
      writer.uint32(10).string(message.id);
    }
    if (message.status !== undefined) {
      writer.uint32(16).int32(message.status);
    }
    if (message.title !== undefined) {
      writer.uint32(26).string(message.title);
    }
    if (message.description !== undefined) {
      writer.uint32(34).string(message.description);
    }
    if (message.duration !== undefined) {
      writer.uint32(40).int32(message.duration);
    }
    if (message.persistent !== undefined) {
      writer.uint32(48).bool(message.persistent);
    }
    if (message.closable !== undefined) {
      writer.uint32(56).bool(message.closable);
    }
    if (message.loading !== undefined) {
      writer.uint32(64).bool(message.loading);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppNotificationConfig {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppNotificationConfig();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.id = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.status = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.title = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.description = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.duration = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.persistent = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.closable = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.loading = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppNotificationConfig {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : "",
      status: isSet(object.status) ? appNotificationConfigStatusFromJSON(object.status) : undefined,
      title: isSet(object.title) ? globalThis.String(object.title) : undefined,
      description: isSet(object.description) ? globalThis.String(object.description) : undefined,
      duration: isSet(object.duration) ? globalThis.Number(object.duration) : undefined,
      persistent: isSet(object.persistent) ? globalThis.Boolean(object.persistent) : undefined,
      closable: isSet(object.closable) ? globalThis.Boolean(object.closable) : undefined,
      loading: isSet(object.loading) ? globalThis.Boolean(object.loading) : undefined,
    };
  },

  toJSON(message: AppNotificationConfig): unknown {
    const obj: any = {};
    if (message.id !== "") {
      obj.id = message.id;
    }
    if (message.status !== undefined) {
      obj.status = appNotificationConfigStatusToJSON(message.status);
    }
    if (message.title !== undefined) {
      obj.title = message.title;
    }
    if (message.description !== undefined) {
      obj.description = message.description;
    }
    if (message.duration !== undefined) {
      obj.duration = Math.round(message.duration);
    }
    if (message.persistent !== undefined) {
      obj.persistent = message.persistent;
    }
    if (message.closable !== undefined) {
      obj.closable = message.closable;
    }
    if (message.loading !== undefined) {
      obj.loading = message.loading;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppNotificationConfig>, I>>(base?: I): AppNotificationConfig {
    return AppNotificationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppNotificationConfig>, I>>(object: I): AppNotificationConfig {
    const message = createBaseAppNotificationConfig();
    message.id = object.id ?? "";
    message.status = object.status ?? undefined;
    message.title = object.title ?? undefined;
    message.description = object.description ?? undefined;
    message.duration = object.duration ?? undefined;
    message.persistent = object.persistent ?? undefined;
    message.closable = object.closable ?? undefined;
    message.loading = object.loading ?? undefined;
    return message;
  },
};

function createBaseAppMidiSequencerProgramChange(): AppMidiSequencerProgramChange {
  return { channel: 0, program: 0, time: 0, tick: 0, bank: 0 };
}

export const AppMidiSequencerProgramChange = {
  encode(message: AppMidiSequencerProgramChange, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).int32(message.channel);
    }
    if (message.program !== 0) {
      writer.uint32(16).int32(message.program);
    }
    if (message.time !== 0) {
      writer.uint32(25).double(message.time);
    }
    if (message.tick !== 0) {
      writer.uint32(32).int32(message.tick);
    }
    if (message.bank !== 0) {
      writer.uint32(40).int32(message.bank);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppMidiSequencerProgramChange {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppMidiSequencerProgramChange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.program = reader.int32();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.time = reader.double();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.tick = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.bank = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppMidiSequencerProgramChange {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      program: isSet(object.program) ? globalThis.Number(object.program) : 0,
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      tick: isSet(object.tick) ? globalThis.Number(object.tick) : 0,
      bank: isSet(object.bank) ? globalThis.Number(object.bank) : 0,
    };
  },

  toJSON(message: AppMidiSequencerProgramChange): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.program !== 0) {
      obj.program = Math.round(message.program);
    }
    if (message.time !== 0) {
      obj.time = message.time;
    }
    if (message.tick !== 0) {
      obj.tick = Math.round(message.tick);
    }
    if (message.bank !== 0) {
      obj.bank = Math.round(message.bank);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppMidiSequencerProgramChange>, I>>(base?: I): AppMidiSequencerProgramChange {
    return AppMidiSequencerProgramChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppMidiSequencerProgramChange>, I>>(
    object: I,
  ): AppMidiSequencerProgramChange {
    const message = createBaseAppMidiSequencerProgramChange();
    message.channel = object.channel ?? 0;
    message.program = object.program ?? 0;
    message.time = object.time ?? 0;
    message.tick = object.tick ?? 0;
    message.bank = object.bank ?? 0;
    return message;
  },
};

function createBaseAppMidiSequencerTempoChange(): AppMidiSequencerTempoChange {
  return { time: 0, tempo: 0 };
}

export const AppMidiSequencerTempoChange = {
  encode(message: AppMidiSequencerTempoChange, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.time !== 0) {
      writer.uint32(9).double(message.time);
    }
    if (message.tempo !== 0) {
      writer.uint32(17).double(message.tempo);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppMidiSequencerTempoChange {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppMidiSequencerTempoChange();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }

          message.time = reader.double();
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.tempo = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppMidiSequencerTempoChange {
    return {
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      tempo: isSet(object.tempo) ? globalThis.Number(object.tempo) : 0,
    };
  },

  toJSON(message: AppMidiSequencerTempoChange): unknown {
    const obj: any = {};
    if (message.time !== 0) {
      obj.time = message.time;
    }
    if (message.tempo !== 0) {
      obj.tempo = message.tempo;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppMidiSequencerTempoChange>, I>>(base?: I): AppMidiSequencerTempoChange {
    return AppMidiSequencerTempoChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppMidiSequencerTempoChange>, I>>(object: I): AppMidiSequencerTempoChange {
    const message = createBaseAppMidiSequencerTempoChange();
    message.time = object.time ?? 0;
    message.tempo = object.tempo ?? 0;
    return message;
  },
};

function createBaseAppMidiSequencerEvent(): AppMidiSequencerEvent {
  return {
    eventType: 0,
    dataStr: undefined,
    dataFloat: undefined,
    channel: undefined,
    command: undefined,
    data1: undefined,
    data2: undefined,
    lyrics: [],
    fileName: undefined,
    totalTime: undefined,
    trackNames: [],
    currentBPM: undefined,
    copyrightNotice: [],
    texts: [],
    markerTexts: [],
    tick: undefined,
    programChanges: [],
    tempoChanges: [],
    ppq: undefined,
    isVPSheet: undefined,
    index: undefined,
    time: undefined,
  };
}

export const AppMidiSequencerEvent = {
  encode(message: AppMidiSequencerEvent, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.eventType !== 0) {
      writer.uint32(8).int32(message.eventType);
    }
    if (message.dataStr !== undefined) {
      writer.uint32(18).string(message.dataStr);
    }
    if (message.dataFloat !== undefined) {
      writer.uint32(25).double(message.dataFloat);
    }
    if (message.channel !== undefined) {
      writer.uint32(32).int32(message.channel);
    }
    if (message.command !== undefined) {
      writer.uint32(40).int32(message.command);
    }
    if (message.data1 !== undefined) {
      writer.uint32(48).int32(message.data1);
    }
    if (message.data2 !== undefined) {
      writer.uint32(56).int32(message.data2);
    }
    for (const v of message.lyrics) {
      writer.uint32(66).string(v!);
    }
    if (message.fileName !== undefined) {
      writer.uint32(74).string(message.fileName);
    }
    if (message.totalTime !== undefined) {
      writer.uint32(81).double(message.totalTime);
    }
    for (const v of message.trackNames) {
      writer.uint32(90).string(v!);
    }
    if (message.currentBPM !== undefined) {
      writer.uint32(96).int32(message.currentBPM);
    }
    for (const v of message.copyrightNotice) {
      writer.uint32(106).string(v!);
    }
    for (const v of message.texts) {
      writer.uint32(114).string(v!);
    }
    for (const v of message.markerTexts) {
      writer.uint32(122).string(v!);
    }
    if (message.tick !== undefined) {
      writer.uint32(128).int32(message.tick);
    }
    for (const v of message.programChanges) {
      AppMidiSequencerProgramChange.encode(v!, writer.uint32(138).fork()).ldelim();
    }
    for (const v of message.tempoChanges) {
      AppMidiSequencerTempoChange.encode(v!, writer.uint32(146).fork()).ldelim();
    }
    if (message.ppq !== undefined) {
      writer.uint32(152).int32(message.ppq);
    }
    if (message.isVPSheet !== undefined) {
      writer.uint32(160).bool(message.isVPSheet);
    }
    if (message.index !== undefined) {
      writer.uint32(168).int32(message.index);
    }
    if (message.time !== undefined) {
      writer.uint32(177).double(message.time);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppMidiSequencerEvent {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppMidiSequencerEvent();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.eventType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.dataStr = reader.string();
          continue;
        case 3:
          if (tag !== 25) {
            break;
          }

          message.dataFloat = reader.double();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.channel = reader.int32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.command = reader.int32();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.data1 = reader.int32();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.data2 = reader.int32();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.lyrics.push(reader.string());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.fileName = reader.string();
          continue;
        case 10:
          if (tag !== 81) {
            break;
          }

          message.totalTime = reader.double();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.trackNames.push(reader.string());
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.currentBPM = reader.int32();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.copyrightNotice.push(reader.string());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.texts.push(reader.string());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.markerTexts.push(reader.string());
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.tick = reader.int32();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.programChanges.push(AppMidiSequencerProgramChange.decode(reader, reader.uint32()));
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.tempoChanges.push(AppMidiSequencerTempoChange.decode(reader, reader.uint32()));
          continue;
        case 19:
          if (tag !== 152) {
            break;
          }

          message.ppq = reader.int32();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.isVPSheet = reader.bool();
          continue;
        case 21:
          if (tag !== 168) {
            break;
          }

          message.index = reader.int32();
          continue;
        case 22:
          if (tag !== 177) {
            break;
          }

          message.time = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppMidiSequencerEvent {
    return {
      eventType: isSet(object.eventType) ? appMidiSequencerEventTypeFromJSON(object.eventType) : 0,
      dataStr: isSet(object.dataStr) ? globalThis.String(object.dataStr) : undefined,
      dataFloat: isSet(object.dataFloat) ? globalThis.Number(object.dataFloat) : undefined,
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : undefined,
      command: isSet(object.command) ? globalThis.Number(object.command) : undefined,
      data1: isSet(object.data1) ? globalThis.Number(object.data1) : undefined,
      data2: isSet(object.data2) ? globalThis.Number(object.data2) : undefined,
      lyrics: globalThis.Array.isArray(object?.lyrics) ? object.lyrics.map((e: any) => globalThis.String(e)) : [],
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : undefined,
      totalTime: isSet(object.totalTime) ? globalThis.Number(object.totalTime) : undefined,
      trackNames: globalThis.Array.isArray(object?.trackNames)
        ? object.trackNames.map((e: any) => globalThis.String(e))
        : [],
      currentBPM: isSet(object.currentBPM) ? globalThis.Number(object.currentBPM) : undefined,
      copyrightNotice: globalThis.Array.isArray(object?.copyrightNotice)
        ? object.copyrightNotice.map((e: any) => globalThis.String(e))
        : [],
      texts: globalThis.Array.isArray(object?.texts) ? object.texts.map((e: any) => globalThis.String(e)) : [],
      markerTexts: globalThis.Array.isArray(object?.markerTexts)
        ? object.markerTexts.map((e: any) => globalThis.String(e))
        : [],
      tick: isSet(object.tick) ? globalThis.Number(object.tick) : undefined,
      programChanges: globalThis.Array.isArray(object?.programChanges)
        ? object.programChanges.map((e: any) => AppMidiSequencerProgramChange.fromJSON(e))
        : [],
      tempoChanges: globalThis.Array.isArray(object?.tempoChanges)
        ? object.tempoChanges.map((e: any) => AppMidiSequencerTempoChange.fromJSON(e))
        : [],
      ppq: isSet(object.ppq) ? globalThis.Number(object.ppq) : undefined,
      isVPSheet: isSet(object.isVPSheet) ? globalThis.Boolean(object.isVPSheet) : undefined,
      index: isSet(object.index) ? globalThis.Number(object.index) : undefined,
      time: isSet(object.time) ? globalThis.Number(object.time) : undefined,
    };
  },

  toJSON(message: AppMidiSequencerEvent): unknown {
    const obj: any = {};
    if (message.eventType !== 0) {
      obj.eventType = appMidiSequencerEventTypeToJSON(message.eventType);
    }
    if (message.dataStr !== undefined) {
      obj.dataStr = message.dataStr;
    }
    if (message.dataFloat !== undefined) {
      obj.dataFloat = message.dataFloat;
    }
    if (message.channel !== undefined) {
      obj.channel = Math.round(message.channel);
    }
    if (message.command !== undefined) {
      obj.command = Math.round(message.command);
    }
    if (message.data1 !== undefined) {
      obj.data1 = Math.round(message.data1);
    }
    if (message.data2 !== undefined) {
      obj.data2 = Math.round(message.data2);
    }
    if (message.lyrics?.length) {
      obj.lyrics = message.lyrics;
    }
    if (message.fileName !== undefined) {
      obj.fileName = message.fileName;
    }
    if (message.totalTime !== undefined) {
      obj.totalTime = message.totalTime;
    }
    if (message.trackNames?.length) {
      obj.trackNames = message.trackNames;
    }
    if (message.currentBPM !== undefined) {
      obj.currentBPM = Math.round(message.currentBPM);
    }
    if (message.copyrightNotice?.length) {
      obj.copyrightNotice = message.copyrightNotice;
    }
    if (message.texts?.length) {
      obj.texts = message.texts;
    }
    if (message.markerTexts?.length) {
      obj.markerTexts = message.markerTexts;
    }
    if (message.tick !== undefined) {
      obj.tick = Math.round(message.tick);
    }
    if (message.programChanges?.length) {
      obj.programChanges = message.programChanges.map((e) => AppMidiSequencerProgramChange.toJSON(e));
    }
    if (message.tempoChanges?.length) {
      obj.tempoChanges = message.tempoChanges.map((e) => AppMidiSequencerTempoChange.toJSON(e));
    }
    if (message.ppq !== undefined) {
      obj.ppq = Math.round(message.ppq);
    }
    if (message.isVPSheet !== undefined) {
      obj.isVPSheet = message.isVPSheet;
    }
    if (message.index !== undefined) {
      obj.index = Math.round(message.index);
    }
    if (message.time !== undefined) {
      obj.time = message.time;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppMidiSequencerEvent>, I>>(base?: I): AppMidiSequencerEvent {
    return AppMidiSequencerEvent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppMidiSequencerEvent>, I>>(object: I): AppMidiSequencerEvent {
    const message = createBaseAppMidiSequencerEvent();
    message.eventType = object.eventType ?? 0;
    message.dataStr = object.dataStr ?? undefined;
    message.dataFloat = object.dataFloat ?? undefined;
    message.channel = object.channel ?? undefined;
    message.command = object.command ?? undefined;
    message.data1 = object.data1 ?? undefined;
    message.data2 = object.data2 ?? undefined;
    message.lyrics = object.lyrics?.map((e) => e) || [];
    message.fileName = object.fileName ?? undefined;
    message.totalTime = object.totalTime ?? undefined;
    message.trackNames = object.trackNames?.map((e) => e) || [];
    message.currentBPM = object.currentBPM ?? undefined;
    message.copyrightNotice = object.copyrightNotice?.map((e) => e) || [];
    message.texts = object.texts?.map((e) => e) || [];
    message.markerTexts = object.markerTexts?.map((e) => e) || [];
    message.tick = object.tick ?? undefined;
    message.programChanges = object.programChanges?.map((e) => AppMidiSequencerProgramChange.fromPartial(e)) || [];
    message.tempoChanges = object.tempoChanges?.map((e) => AppMidiSequencerTempoChange.fromPartial(e)) || [];
    message.ppq = object.ppq ?? undefined;
    message.isVPSheet = object.isVPSheet ?? undefined;
    message.index = object.index ?? undefined;
    message.time = object.time ?? undefined;
    return message;
  },
};

function createBaseAppVPSequencerTrack(): AppVPSequencerTrack {
  return { index: 0, tempo: undefined, name: undefined };
}

export const AppVPSequencerTrack = {
  encode(message: AppVPSequencerTrack, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.index !== 0) {
      writer.uint32(8).int32(message.index);
    }
    if (message.tempo !== undefined) {
      writer.uint32(16).int32(message.tempo);
    }
    if (message.name !== undefined) {
      writer.uint32(26).string(message.name);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppVPSequencerTrack {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppVPSequencerTrack();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.index = reader.int32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.tempo = reader.int32();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.name = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppVPSequencerTrack {
    return {
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      tempo: isSet(object.tempo) ? globalThis.Number(object.tempo) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
    };
  },

  toJSON(message: AppVPSequencerTrack): unknown {
    const obj: any = {};
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.tempo !== undefined) {
      obj.tempo = Math.round(message.tempo);
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppVPSequencerTrack>, I>>(base?: I): AppVPSequencerTrack {
    return AppVPSequencerTrack.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppVPSequencerTrack>, I>>(object: I): AppVPSequencerTrack {
    const message = createBaseAppVPSequencerTrack();
    message.index = object.index ?? 0;
    message.tempo = object.tempo ?? undefined;
    message.name = object.name ?? undefined;
    return message;
  },
};

function createBaseAppVPSequencerFileLoad(): AppVPSequencerFileLoad {
  return { data: "", fileName: undefined, tracks: [] };
}

export const AppVPSequencerFileLoad = {
  encode(message: AppVPSequencerFileLoad, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.data !== "") {
      writer.uint32(10).string(message.data);
    }
    if (message.fileName !== undefined) {
      writer.uint32(18).string(message.fileName);
    }
    for (const v of message.tracks) {
      AppVPSequencerTrack.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppVPSequencerFileLoad {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppVPSequencerFileLoad();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.fileName = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.tracks.push(AppVPSequencerTrack.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppVPSequencerFileLoad {
    return {
      data: isSet(object.data) ? globalThis.String(object.data) : "",
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : undefined,
      tracks: globalThis.Array.isArray(object?.tracks)
        ? object.tracks.map((e: any) => AppVPSequencerTrack.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AppVPSequencerFileLoad): unknown {
    const obj: any = {};
    if (message.data !== "") {
      obj.data = message.data;
    }
    if (message.fileName !== undefined) {
      obj.fileName = message.fileName;
    }
    if (message.tracks?.length) {
      obj.tracks = message.tracks.map((e) => AppVPSequencerTrack.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppVPSequencerFileLoad>, I>>(base?: I): AppVPSequencerFileLoad {
    return AppVPSequencerFileLoad.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppVPSequencerFileLoad>, I>>(object: I): AppVPSequencerFileLoad {
    const message = createBaseAppVPSequencerFileLoad();
    message.data = object.data ?? "";
    message.fileName = object.fileName ?? undefined;
    message.tracks = object.tracks?.map((e) => AppVPSequencerTrack.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAppMidiSequencerFileLoad(): AppMidiSequencerFileLoad {
  return { data: [], fileName: undefined };
}

export const AppMidiSequencerFileLoad = {
  encode(message: AppMidiSequencerFileLoad, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    writer.uint32(10).fork();
    for (const v of message.data) {
      writer.uint32(v);
    }
    writer.ldelim();
    if (message.fileName !== undefined) {
      writer.uint32(18).string(message.fileName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppMidiSequencerFileLoad {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppMidiSequencerFileLoad();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag === 8) {
            message.data.push(reader.uint32());

            continue;
          }

          if (tag === 10) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.data.push(reader.uint32());
            }

            continue;
          }

          break;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.fileName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppMidiSequencerFileLoad {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => globalThis.Number(e)) : [],
      fileName: isSet(object.fileName) ? globalThis.String(object.fileName) : undefined,
    };
  },

  toJSON(message: AppMidiSequencerFileLoad): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => Math.round(e));
    }
    if (message.fileName !== undefined) {
      obj.fileName = message.fileName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppMidiSequencerFileLoad>, I>>(base?: I): AppMidiSequencerFileLoad {
    return AppMidiSequencerFileLoad.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppMidiSequencerFileLoad>, I>>(object: I): AppMidiSequencerFileLoad {
    const message = createBaseAppMidiSequencerFileLoad();
    message.data = object.data?.map((e) => e) || [];
    message.fileName = object.fileName ?? undefined;
    return message;
  },
};

function createBaseAppMidiTrack(): AppMidiTrack {
  return {
    index: 0,
    name: undefined,
    volume: undefined,
    pan: undefined,
    active: false,
    hasData: false,
    playing: false,
    recording: false,
  };
}

export const AppMidiTrack = {
  encode(message: AppMidiTrack, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.index !== 0) {
      writer.uint32(8).uint32(message.index);
    }
    if (message.name !== undefined) {
      writer.uint32(18).string(message.name);
    }
    if (message.volume !== undefined) {
      writer.uint32(24).uint32(message.volume);
    }
    if (message.pan !== undefined) {
      writer.uint32(32).uint32(message.pan);
    }
    if (message.active !== false) {
      writer.uint32(40).bool(message.active);
    }
    if (message.hasData !== false) {
      writer.uint32(48).bool(message.hasData);
    }
    if (message.playing !== false) {
      writer.uint32(56).bool(message.playing);
    }
    if (message.recording !== false) {
      writer.uint32(64).bool(message.recording);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppMidiTrack {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppMidiTrack();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.index = reader.uint32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.name = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.volume = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.pan = reader.uint32();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.active = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.hasData = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.playing = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.recording = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppMidiTrack {
    return {
      index: isSet(object.index) ? globalThis.Number(object.index) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : undefined,
      volume: isSet(object.volume) ? globalThis.Number(object.volume) : undefined,
      pan: isSet(object.pan) ? globalThis.Number(object.pan) : undefined,
      active: isSet(object.active) ? globalThis.Boolean(object.active) : false,
      hasData: isSet(object.hasData) ? globalThis.Boolean(object.hasData) : false,
      playing: isSet(object.playing) ? globalThis.Boolean(object.playing) : false,
      recording: isSet(object.recording) ? globalThis.Boolean(object.recording) : false,
    };
  },

  toJSON(message: AppMidiTrack): unknown {
    const obj: any = {};
    if (message.index !== 0) {
      obj.index = Math.round(message.index);
    }
    if (message.name !== undefined) {
      obj.name = message.name;
    }
    if (message.volume !== undefined) {
      obj.volume = Math.round(message.volume);
    }
    if (message.pan !== undefined) {
      obj.pan = Math.round(message.pan);
    }
    if (message.active !== false) {
      obj.active = message.active;
    }
    if (message.hasData !== false) {
      obj.hasData = message.hasData;
    }
    if (message.playing !== false) {
      obj.playing = message.playing;
    }
    if (message.recording !== false) {
      obj.recording = message.recording;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppMidiTrack>, I>>(base?: I): AppMidiTrack {
    return AppMidiTrack.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppMidiTrack>, I>>(object: I): AppMidiTrack {
    const message = createBaseAppMidiTrack();
    message.index = object.index ?? 0;
    message.name = object.name ?? undefined;
    message.volume = object.volume ?? undefined;
    message.pan = object.pan ?? undefined;
    message.active = object.active ?? false;
    message.hasData = object.hasData ?? false;
    message.playing = object.playing ?? false;
    message.recording = object.recording ?? false;
    return message;
  },
};

function createBaseAppRoomStageLoaded(): AppRoomStageLoaded {
  return { roomStage: 0, roomType: 0 };
}

export const AppRoomStageLoaded = {
  encode(message: AppRoomStageLoaded, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomStage !== 0) {
      writer.uint32(8).int32(message.roomStage);
    }
    if (message.roomType !== 0) {
      writer.uint32(16).int32(message.roomType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppRoomStageLoaded {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppRoomStageLoaded();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.roomStage = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.roomType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppRoomStageLoaded {
    return {
      roomStage: isSet(object.roomStage) ? roomStagesFromJSON(object.roomStage) : 0,
      roomType: isSet(object.roomType) ? roomTypeFromJSON(object.roomType) : 0,
    };
  },

  toJSON(message: AppRoomStageLoaded): unknown {
    const obj: any = {};
    if (message.roomStage !== 0) {
      obj.roomStage = roomStagesToJSON(message.roomStage);
    }
    if (message.roomType !== 0) {
      obj.roomType = roomTypeToJSON(message.roomType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppRoomStageLoaded>, I>>(base?: I): AppRoomStageLoaded {
    return AppRoomStageLoaded.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppRoomStageLoaded>, I>>(object: I): AppRoomStageLoaded {
    const message = createBaseAppRoomStageLoaded();
    message.roomStage = object.roomStage ?? 0;
    message.roomType = object.roomType ?? 0;
    return message;
  },
};

function createBaseAppKeyboardMappingVisualize(): AppKeyboardMappingVisualize {
  return { note: 0, key: "" };
}

export const AppKeyboardMappingVisualize = {
  encode(message: AppKeyboardMappingVisualize, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.note !== 0) {
      writer.uint32(8).uint32(message.note);
    }
    if (message.key !== "") {
      writer.uint32(18).string(message.key);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppKeyboardMappingVisualize {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppKeyboardMappingVisualize();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.note = reader.uint32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.key = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppKeyboardMappingVisualize {
    return {
      note: isSet(object.note) ? globalThis.Number(object.note) : 0,
      key: isSet(object.key) ? globalThis.String(object.key) : "",
    };
  },

  toJSON(message: AppKeyboardMappingVisualize): unknown {
    const obj: any = {};
    if (message.note !== 0) {
      obj.note = Math.round(message.note);
    }
    if (message.key !== "") {
      obj.key = message.key;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppKeyboardMappingVisualize>, I>>(base?: I): AppKeyboardMappingVisualize {
    return AppKeyboardMappingVisualize.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppKeyboardMappingVisualize>, I>>(object: I): AppKeyboardMappingVisualize {
    const message = createBaseAppKeyboardMappingVisualize();
    message.note = object.note ?? 0;
    message.key = object.key ?? "";
    return message;
  },
};

function createBaseAppKeyboardMappingVisualizeVec(): AppKeyboardMappingVisualizeVec {
  return { mappings: [] };
}

export const AppKeyboardMappingVisualizeVec = {
  encode(message: AppKeyboardMappingVisualizeVec, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.mappings) {
      AppKeyboardMappingVisualize.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppKeyboardMappingVisualizeVec {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppKeyboardMappingVisualizeVec();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.mappings.push(AppKeyboardMappingVisualize.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppKeyboardMappingVisualizeVec {
    return {
      mappings: globalThis.Array.isArray(object?.mappings)
        ? object.mappings.map((e: any) => AppKeyboardMappingVisualize.fromJSON(e))
        : [],
    };
  },

  toJSON(message: AppKeyboardMappingVisualizeVec): unknown {
    const obj: any = {};
    if (message.mappings?.length) {
      obj.mappings = message.mappings.map((e) => AppKeyboardMappingVisualize.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppKeyboardMappingVisualizeVec>, I>>(base?: I): AppKeyboardMappingVisualizeVec {
    return AppKeyboardMappingVisualizeVec.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppKeyboardMappingVisualizeVec>, I>>(
    object: I,
  ): AppKeyboardMappingVisualizeVec {
    const message = createBaseAppKeyboardMappingVisualizeVec();
    message.mappings = object.mappings?.map((e) => AppKeyboardMappingVisualize.fromPartial(e)) || [];
    return message;
  },
};

function createBaseAppThemeColors(): AppThemeColors {
  return { primary: "", accent: "", tertiary: "" };
}

export const AppThemeColors = {
  encode(message: AppThemeColors, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.primary !== "") {
      writer.uint32(10).string(message.primary);
    }
    if (message.accent !== "") {
      writer.uint32(18).string(message.accent);
    }
    if (message.tertiary !== "") {
      writer.uint32(26).string(message.tertiary);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppThemeColors {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppThemeColors();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.primary = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.accent = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.tertiary = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppThemeColors {
    return {
      primary: isSet(object.primary) ? globalThis.String(object.primary) : "",
      accent: isSet(object.accent) ? globalThis.String(object.accent) : "",
      tertiary: isSet(object.tertiary) ? globalThis.String(object.tertiary) : "",
    };
  },

  toJSON(message: AppThemeColors): unknown {
    const obj: any = {};
    if (message.primary !== "") {
      obj.primary = message.primary;
    }
    if (message.accent !== "") {
      obj.accent = message.accent;
    }
    if (message.tertiary !== "") {
      obj.tertiary = message.tertiary;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppThemeColors>, I>>(base?: I): AppThemeColors {
    return AppThemeColors.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppThemeColors>, I>>(object: I): AppThemeColors {
    const message = createBaseAppThemeColors();
    message.primary = object.primary ?? "";
    message.accent = object.accent ?? "";
    message.tertiary = object.tertiary ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
