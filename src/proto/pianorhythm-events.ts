// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: pianorhythm-events.proto

/* eslint-disable */

export const protobufPackage = "PianoRhythm.AppStateEvents";

export enum AppStateEvents {
  Unknown = 0,
  UIDisabled = 1,
  UIEnabled = 2,
  ClientLoggedIn = 3,
  ClientLoggedOut = 4,
  CanvasLoaded = 5,
  ClientLoaded = 6,
  ClientUpdated = 7,
  UsersSet = 8,
  JoinRoomFailed = 9,
  RoomChatHistorySet = 10,
  AddedSynthUser = 11,
  AddedClientSynthUser = 12,
  AudioStateInitialized = 13,
  RemovedSynthUser = 14,
  ClearedSynthUsers = 15,
  InstrumentsLoaded = 16,
  SynthSoundfontLoaded = 17,
  AppStateReset = 18,
  AudioChannelsCleared = 19,
  AudioEngineStateUpdated = 20,
  AppStateUpdated = 21,
  AudioChannelsUpdated = 22,
  AudioInstrumentsUpdated = 23,
  ClientStateReadyToRetrieve = 25,
  OfflineModeEnabled = 26,
  OfflineModeDisabled = 27,
  /** RoomStateUpdated - Room related */
  RoomStateUpdated = 2000,
  RoomStageLoading = 2001,
  RoomStageLoaded = 2002,
  JoinedSelfHostedRoom = 2003,
  LeftSelfHostedRoom = 2004,
  RoomMuted = 2005,
  RoomUnmuted = 2006,
  UNRECOGNIZED = -1,
}

export function appStateEventsFromJSON(object: any): AppStateEvents {
  switch (object) {
    case 0:
    case "Unknown":
      return AppStateEvents.Unknown;
    case 1:
    case "UIDisabled":
      return AppStateEvents.UIDisabled;
    case 2:
    case "UIEnabled":
      return AppStateEvents.UIEnabled;
    case 3:
    case "ClientLoggedIn":
      return AppStateEvents.ClientLoggedIn;
    case 4:
    case "ClientLoggedOut":
      return AppStateEvents.ClientLoggedOut;
    case 5:
    case "CanvasLoaded":
      return AppStateEvents.CanvasLoaded;
    case 6:
    case "ClientLoaded":
      return AppStateEvents.ClientLoaded;
    case 7:
    case "ClientUpdated":
      return AppStateEvents.ClientUpdated;
    case 8:
    case "UsersSet":
      return AppStateEvents.UsersSet;
    case 9:
    case "JoinRoomFailed":
      return AppStateEvents.JoinRoomFailed;
    case 10:
    case "RoomChatHistorySet":
      return AppStateEvents.RoomChatHistorySet;
    case 11:
    case "AddedSynthUser":
      return AppStateEvents.AddedSynthUser;
    case 12:
    case "AddedClientSynthUser":
      return AppStateEvents.AddedClientSynthUser;
    case 13:
    case "AudioStateInitialized":
      return AppStateEvents.AudioStateInitialized;
    case 14:
    case "RemovedSynthUser":
      return AppStateEvents.RemovedSynthUser;
    case 15:
    case "ClearedSynthUsers":
      return AppStateEvents.ClearedSynthUsers;
    case 16:
    case "InstrumentsLoaded":
      return AppStateEvents.InstrumentsLoaded;
    case 17:
    case "SynthSoundfontLoaded":
      return AppStateEvents.SynthSoundfontLoaded;
    case 18:
    case "AppStateReset":
      return AppStateEvents.AppStateReset;
    case 19:
    case "AudioChannelsCleared":
      return AppStateEvents.AudioChannelsCleared;
    case 20:
    case "AudioEngineStateUpdated":
      return AppStateEvents.AudioEngineStateUpdated;
    case 21:
    case "AppStateUpdated":
      return AppStateEvents.AppStateUpdated;
    case 22:
    case "AudioChannelsUpdated":
      return AppStateEvents.AudioChannelsUpdated;
    case 23:
    case "AudioInstrumentsUpdated":
      return AppStateEvents.AudioInstrumentsUpdated;
    case 25:
    case "ClientStateReadyToRetrieve":
      return AppStateEvents.ClientStateReadyToRetrieve;
    case 26:
    case "OfflineModeEnabled":
      return AppStateEvents.OfflineModeEnabled;
    case 27:
    case "OfflineModeDisabled":
      return AppStateEvents.OfflineModeDisabled;
    case 2000:
    case "RoomStateUpdated":
      return AppStateEvents.RoomStateUpdated;
    case 2001:
    case "RoomStageLoading":
      return AppStateEvents.RoomStageLoading;
    case 2002:
    case "RoomStageLoaded":
      return AppStateEvents.RoomStageLoaded;
    case 2003:
    case "JoinedSelfHostedRoom":
      return AppStateEvents.JoinedSelfHostedRoom;
    case 2004:
    case "LeftSelfHostedRoom":
      return AppStateEvents.LeftSelfHostedRoom;
    case 2005:
    case "RoomMuted":
      return AppStateEvents.RoomMuted;
    case 2006:
    case "RoomUnmuted":
      return AppStateEvents.RoomUnmuted;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppStateEvents.UNRECOGNIZED;
  }
}

export function appStateEventsToJSON(object: AppStateEvents): string {
  switch (object) {
    case AppStateEvents.Unknown:
      return "Unknown";
    case AppStateEvents.UIDisabled:
      return "UIDisabled";
    case AppStateEvents.UIEnabled:
      return "UIEnabled";
    case AppStateEvents.ClientLoggedIn:
      return "ClientLoggedIn";
    case AppStateEvents.ClientLoggedOut:
      return "ClientLoggedOut";
    case AppStateEvents.CanvasLoaded:
      return "CanvasLoaded";
    case AppStateEvents.ClientLoaded:
      return "ClientLoaded";
    case AppStateEvents.ClientUpdated:
      return "ClientUpdated";
    case AppStateEvents.UsersSet:
      return "UsersSet";
    case AppStateEvents.JoinRoomFailed:
      return "JoinRoomFailed";
    case AppStateEvents.RoomChatHistorySet:
      return "RoomChatHistorySet";
    case AppStateEvents.AddedSynthUser:
      return "AddedSynthUser";
    case AppStateEvents.AddedClientSynthUser:
      return "AddedClientSynthUser";
    case AppStateEvents.AudioStateInitialized:
      return "AudioStateInitialized";
    case AppStateEvents.RemovedSynthUser:
      return "RemovedSynthUser";
    case AppStateEvents.ClearedSynthUsers:
      return "ClearedSynthUsers";
    case AppStateEvents.InstrumentsLoaded:
      return "InstrumentsLoaded";
    case AppStateEvents.SynthSoundfontLoaded:
      return "SynthSoundfontLoaded";
    case AppStateEvents.AppStateReset:
      return "AppStateReset";
    case AppStateEvents.AudioChannelsCleared:
      return "AudioChannelsCleared";
    case AppStateEvents.AudioEngineStateUpdated:
      return "AudioEngineStateUpdated";
    case AppStateEvents.AppStateUpdated:
      return "AppStateUpdated";
    case AppStateEvents.AudioChannelsUpdated:
      return "AudioChannelsUpdated";
    case AppStateEvents.AudioInstrumentsUpdated:
      return "AudioInstrumentsUpdated";
    case AppStateEvents.ClientStateReadyToRetrieve:
      return "ClientStateReadyToRetrieve";
    case AppStateEvents.OfflineModeEnabled:
      return "OfflineModeEnabled";
    case AppStateEvents.OfflineModeDisabled:
      return "OfflineModeDisabled";
    case AppStateEvents.RoomStateUpdated:
      return "RoomStateUpdated";
    case AppStateEvents.RoomStageLoading:
      return "RoomStageLoading";
    case AppStateEvents.RoomStageLoaded:
      return "RoomStageLoaded";
    case AppStateEvents.JoinedSelfHostedRoom:
      return "JoinedSelfHostedRoom";
    case AppStateEvents.LeftSelfHostedRoom:
      return "LeftSelfHostedRoom";
    case AppStateEvents.RoomMuted:
      return "RoomMuted";
    case AppStateEvents.RoomUnmuted:
      return "RoomUnmuted";
    case AppStateEvents.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}
