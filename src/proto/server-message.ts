// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: server-message.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";
import { MidiDto } from "./midi-renditions";
import {
  RoomHostDetails,
  RoomStatus,
  roomStatusFromJSON,
  roomStatusToJSON,
  RoomType,
  roomTypeFromJSON,
  roomTypeToJSON,
} from "./room-renditions";
import { AvatarWorldDataDto_AvatarMessageWorldPosition, UserBadges } from "./user-renditions";

export const protobufPackage = "PianoRhythm.Serialization.ClientToServer.Msgs";

export enum ServerMessageType {
  Invalid = 0,
  Hello = 1,
  Disconnect = 2,
  RoomChatMessage = 3,
  RoomChatServerCommand = 4,
  MousePosMessage = 5,
  MidiMessage = 6,
  ServerCommand = 7,
  ServerModCommand = 8,
  CreateRoomCommand = 9,
  UpdateRoomCommand = 10,
  JoinRoomByName = 11,
  RoomOwnerCommand = 12,
  JoinNextAvailableLobby = 13,
  AvatarCommand = 14,
  UNRECOGNIZED = -1,
}

export function serverMessageTypeFromJSON(object: any): ServerMessageType {
  switch (object) {
    case 0:
    case "Invalid":
      return ServerMessageType.Invalid;
    case 1:
    case "Hello":
      return ServerMessageType.Hello;
    case 2:
    case "Disconnect":
      return ServerMessageType.Disconnect;
    case 3:
    case "RoomChatMessage":
      return ServerMessageType.RoomChatMessage;
    case 4:
    case "RoomChatServerCommand":
      return ServerMessageType.RoomChatServerCommand;
    case 5:
    case "MousePosMessage":
      return ServerMessageType.MousePosMessage;
    case 6:
    case "MidiMessage":
      return ServerMessageType.MidiMessage;
    case 7:
    case "ServerCommand":
      return ServerMessageType.ServerCommand;
    case 8:
    case "ServerModCommand":
      return ServerMessageType.ServerModCommand;
    case 9:
    case "CreateRoomCommand":
      return ServerMessageType.CreateRoomCommand;
    case 10:
    case "UpdateRoomCommand":
      return ServerMessageType.UpdateRoomCommand;
    case 11:
    case "JoinRoomByName":
      return ServerMessageType.JoinRoomByName;
    case 12:
    case "RoomOwnerCommand":
      return ServerMessageType.RoomOwnerCommand;
    case 13:
    case "JoinNextAvailableLobby":
      return ServerMessageType.JoinNextAvailableLobby;
    case 14:
    case "AvatarCommand":
      return ServerMessageType.AvatarCommand;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ServerMessageType.UNRECOGNIZED;
  }
}

export function serverMessageTypeToJSON(object: ServerMessageType): string {
  switch (object) {
    case ServerMessageType.Invalid:
      return "Invalid";
    case ServerMessageType.Hello:
      return "Hello";
    case ServerMessageType.Disconnect:
      return "Disconnect";
    case ServerMessageType.RoomChatMessage:
      return "RoomChatMessage";
    case ServerMessageType.RoomChatServerCommand:
      return "RoomChatServerCommand";
    case ServerMessageType.MousePosMessage:
      return "MousePosMessage";
    case ServerMessageType.MidiMessage:
      return "MidiMessage";
    case ServerMessageType.ServerCommand:
      return "ServerCommand";
    case ServerMessageType.ServerModCommand:
      return "ServerModCommand";
    case ServerMessageType.CreateRoomCommand:
      return "CreateRoomCommand";
    case ServerMessageType.UpdateRoomCommand:
      return "UpdateRoomCommand";
    case ServerMessageType.JoinRoomByName:
      return "JoinRoomByName";
    case ServerMessageType.RoomOwnerCommand:
      return "RoomOwnerCommand";
    case ServerMessageType.JoinNextAvailableLobby:
      return "JoinNextAvailableLobby";
    case ServerMessageType.AvatarCommand:
      return "AvatarCommand";
    case ServerMessageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ChatMessageInputData {
  text: string;
  options: ChatMessageInputData_ChatMessageDataOptions | undefined;
}

export interface ChatMessageInputData_ChatMessageDataOptions {
  syncToDiscord: boolean;
  isFromPlugin: boolean;
  messageReplyID: string;
  messageEditID: string;
}

export interface MidiMessageInputDto {
  time: string;
  data: MidiMessageInputDto_MidiMessageInputBuffer[];
}

export interface MidiMessageInputDto_MidiMessageInputBuffer {
  data: MidiDto | undefined;
  delay: number;
}

export interface RoomIDWithPassword {
  roomID: string;
  password: string;
}

export interface LoginDataDto {
  username: string;
  password: string;
}

export interface SimpleEmailDto {
  email: string;
}

export interface ResetPasswordDataDto {
  password: string;
  confirmpassword: string;
  token: string;
}

export interface RegistrationData {
  username: string;
  email: string;
  password: string;
  confirmpassword: string;
  acceptedtos: boolean;
}

export interface ServerCommandDU {
  commandType: ServerCommandDU_CommandType;
  roomID?: string | undefined;
  roomName?: string | undefined;
  roomIDorName?: string | undefined;
  usertag?: string | undefined;
  jsonValue?: string | undefined;
  boolean?: boolean | undefined;
  roomIDAndPassword?: RoomIDWithPassword | undefined;
  loginData?: LoginDataDto | undefined;
  emailData?: SimpleEmailDto | undefined;
  resetPasswordData?: ResetPasswordDataDto | undefined;
  registrationData?: RegistrationData | undefined;
  stringValue?: string | undefined;
}

export enum ServerCommandDU_CommandType {
  Invalid = 0,
  UserUpdateCommand = 2,
  PermissionUpdatedCommand = 3,
  Join = 7,
  JoinRoomWithPassword = 8,
  CreateOrJoinRoom = 10,
  GetRoomSettings = 11,
  EnterLobby = 12,
  CreateRoom = 13,
  UpdateRoom = 14,
  LeaveRoom = 15,
  Ping = 16,
  GetAllRooms = 17,
  GetAllUsersInRoom = 18,
  Login = 19,
  Register = 20,
  ResendEmailVerification = 21,
  ResetPassword = 22,
  ForgotPassword = 23,
  IsTyping = 24,
  RoomLoaded = 25,
  UploadClientSettings = 26,
  SendFriendRequest = 27,
  SendUnfriendRequest = 28,
  AcceptFriendRequest = 29,
  DenyFriendRequest = 30,
  DeleteSentFriendRequest = 31,
  UploadOrchestraModelCustomizationData = 33,
  UploadCharacterData = 34,
  DeleteChatMessageById = 35,
  DeleteChatMessageByUsertag = 36,
  GetRoomFullDetails = 37,
  UNRECOGNIZED = -1,
}

export function serverCommandDU_CommandTypeFromJSON(object: any): ServerCommandDU_CommandType {
  switch (object) {
    case 0:
    case "Invalid":
      return ServerCommandDU_CommandType.Invalid;
    case 2:
    case "UserUpdateCommand":
      return ServerCommandDU_CommandType.UserUpdateCommand;
    case 3:
    case "PermissionUpdatedCommand":
      return ServerCommandDU_CommandType.PermissionUpdatedCommand;
    case 7:
    case "Join":
      return ServerCommandDU_CommandType.Join;
    case 8:
    case "JoinRoomWithPassword":
      return ServerCommandDU_CommandType.JoinRoomWithPassword;
    case 10:
    case "CreateOrJoinRoom":
      return ServerCommandDU_CommandType.CreateOrJoinRoom;
    case 11:
    case "GetRoomSettings":
      return ServerCommandDU_CommandType.GetRoomSettings;
    case 12:
    case "EnterLobby":
      return ServerCommandDU_CommandType.EnterLobby;
    case 13:
    case "CreateRoom":
      return ServerCommandDU_CommandType.CreateRoom;
    case 14:
    case "UpdateRoom":
      return ServerCommandDU_CommandType.UpdateRoom;
    case 15:
    case "LeaveRoom":
      return ServerCommandDU_CommandType.LeaveRoom;
    case 16:
    case "Ping":
      return ServerCommandDU_CommandType.Ping;
    case 17:
    case "GetAllRooms":
      return ServerCommandDU_CommandType.GetAllRooms;
    case 18:
    case "GetAllUsersInRoom":
      return ServerCommandDU_CommandType.GetAllUsersInRoom;
    case 19:
    case "Login":
      return ServerCommandDU_CommandType.Login;
    case 20:
    case "Register":
      return ServerCommandDU_CommandType.Register;
    case 21:
    case "ResendEmailVerification":
      return ServerCommandDU_CommandType.ResendEmailVerification;
    case 22:
    case "ResetPassword":
      return ServerCommandDU_CommandType.ResetPassword;
    case 23:
    case "ForgotPassword":
      return ServerCommandDU_CommandType.ForgotPassword;
    case 24:
    case "IsTyping":
      return ServerCommandDU_CommandType.IsTyping;
    case 25:
    case "RoomLoaded":
      return ServerCommandDU_CommandType.RoomLoaded;
    case 26:
    case "UploadClientSettings":
      return ServerCommandDU_CommandType.UploadClientSettings;
    case 27:
    case "SendFriendRequest":
      return ServerCommandDU_CommandType.SendFriendRequest;
    case 28:
    case "SendUnfriendRequest":
      return ServerCommandDU_CommandType.SendUnfriendRequest;
    case 29:
    case "AcceptFriendRequest":
      return ServerCommandDU_CommandType.AcceptFriendRequest;
    case 30:
    case "DenyFriendRequest":
      return ServerCommandDU_CommandType.DenyFriendRequest;
    case 31:
    case "DeleteSentFriendRequest":
      return ServerCommandDU_CommandType.DeleteSentFriendRequest;
    case 33:
    case "UploadOrchestraModelCustomizationData":
      return ServerCommandDU_CommandType.UploadOrchestraModelCustomizationData;
    case 34:
    case "UploadCharacterData":
      return ServerCommandDU_CommandType.UploadCharacterData;
    case 35:
    case "DeleteChatMessageById":
      return ServerCommandDU_CommandType.DeleteChatMessageById;
    case 36:
    case "DeleteChatMessageByUsertag":
      return ServerCommandDU_CommandType.DeleteChatMessageByUsertag;
    case 37:
    case "GetRoomFullDetails":
      return ServerCommandDU_CommandType.GetRoomFullDetails;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ServerCommandDU_CommandType.UNRECOGNIZED;
  }
}

export function serverCommandDU_CommandTypeToJSON(object: ServerCommandDU_CommandType): string {
  switch (object) {
    case ServerCommandDU_CommandType.Invalid:
      return "Invalid";
    case ServerCommandDU_CommandType.UserUpdateCommand:
      return "UserUpdateCommand";
    case ServerCommandDU_CommandType.PermissionUpdatedCommand:
      return "PermissionUpdatedCommand";
    case ServerCommandDU_CommandType.Join:
      return "Join";
    case ServerCommandDU_CommandType.JoinRoomWithPassword:
      return "JoinRoomWithPassword";
    case ServerCommandDU_CommandType.CreateOrJoinRoom:
      return "CreateOrJoinRoom";
    case ServerCommandDU_CommandType.GetRoomSettings:
      return "GetRoomSettings";
    case ServerCommandDU_CommandType.EnterLobby:
      return "EnterLobby";
    case ServerCommandDU_CommandType.CreateRoom:
      return "CreateRoom";
    case ServerCommandDU_CommandType.UpdateRoom:
      return "UpdateRoom";
    case ServerCommandDU_CommandType.LeaveRoom:
      return "LeaveRoom";
    case ServerCommandDU_CommandType.Ping:
      return "Ping";
    case ServerCommandDU_CommandType.GetAllRooms:
      return "GetAllRooms";
    case ServerCommandDU_CommandType.GetAllUsersInRoom:
      return "GetAllUsersInRoom";
    case ServerCommandDU_CommandType.Login:
      return "Login";
    case ServerCommandDU_CommandType.Register:
      return "Register";
    case ServerCommandDU_CommandType.ResendEmailVerification:
      return "ResendEmailVerification";
    case ServerCommandDU_CommandType.ResetPassword:
      return "ResetPassword";
    case ServerCommandDU_CommandType.ForgotPassword:
      return "ForgotPassword";
    case ServerCommandDU_CommandType.IsTyping:
      return "IsTyping";
    case ServerCommandDU_CommandType.RoomLoaded:
      return "RoomLoaded";
    case ServerCommandDU_CommandType.UploadClientSettings:
      return "UploadClientSettings";
    case ServerCommandDU_CommandType.SendFriendRequest:
      return "SendFriendRequest";
    case ServerCommandDU_CommandType.SendUnfriendRequest:
      return "SendUnfriendRequest";
    case ServerCommandDU_CommandType.AcceptFriendRequest:
      return "AcceptFriendRequest";
    case ServerCommandDU_CommandType.DenyFriendRequest:
      return "DenyFriendRequest";
    case ServerCommandDU_CommandType.DeleteSentFriendRequest:
      return "DeleteSentFriendRequest";
    case ServerCommandDU_CommandType.UploadOrchestraModelCustomizationData:
      return "UploadOrchestraModelCustomizationData";
    case ServerCommandDU_CommandType.UploadCharacterData:
      return "UploadCharacterData";
    case ServerCommandDU_CommandType.DeleteChatMessageById:
      return "DeleteChatMessageById";
    case ServerCommandDU_CommandType.DeleteChatMessageByUsertag:
      return "DeleteChatMessageByUsertag";
    case ServerCommandDU_CommandType.GetRoomFullDetails:
      return "GetRoomFullDetails";
    case ServerCommandDU_CommandType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RoomOwnerCommandDU {
  commandType: RoomOwnerCommandDU_CommandType;
  usertag?: string | undefined;
  socketID?: string | undefined;
  kickedUserData?: RoomOwnerCommandDU_KickedUserData | undefined;
}

export enum RoomOwnerCommandDU_CommandType {
  Invalid = 0,
  KickUser = 1,
  RemovedKickedUser = 2,
  GetKickedUsersList = 3,
  GiveCrown = 4,
  ClearChat = 5,
  GetRoomSettings = 6,
  UNRECOGNIZED = -1,
}

export function roomOwnerCommandDU_CommandTypeFromJSON(object: any): RoomOwnerCommandDU_CommandType {
  switch (object) {
    case 0:
    case "Invalid":
      return RoomOwnerCommandDU_CommandType.Invalid;
    case 1:
    case "KickUser":
      return RoomOwnerCommandDU_CommandType.KickUser;
    case 2:
    case "RemovedKickedUser":
      return RoomOwnerCommandDU_CommandType.RemovedKickedUser;
    case 3:
    case "GetKickedUsersList":
      return RoomOwnerCommandDU_CommandType.GetKickedUsersList;
    case 4:
    case "GiveCrown":
      return RoomOwnerCommandDU_CommandType.GiveCrown;
    case 5:
    case "ClearChat":
      return RoomOwnerCommandDU_CommandType.ClearChat;
    case 6:
    case "GetRoomSettings":
      return RoomOwnerCommandDU_CommandType.GetRoomSettings;
    case -1:
    case "UNRECOGNIZED":
    default:
      return RoomOwnerCommandDU_CommandType.UNRECOGNIZED;
  }
}

export function roomOwnerCommandDU_CommandTypeToJSON(object: RoomOwnerCommandDU_CommandType): string {
  switch (object) {
    case RoomOwnerCommandDU_CommandType.Invalid:
      return "Invalid";
    case RoomOwnerCommandDU_CommandType.KickUser:
      return "KickUser";
    case RoomOwnerCommandDU_CommandType.RemovedKickedUser:
      return "RemovedKickedUser";
    case RoomOwnerCommandDU_CommandType.GetKickedUsersList:
      return "GetKickedUsersList";
    case RoomOwnerCommandDU_CommandType.GiveCrown:
      return "GiveCrown";
    case RoomOwnerCommandDU_CommandType.ClearChat:
      return "ClearChat";
    case RoomOwnerCommandDU_CommandType.GetRoomSettings:
      return "GetRoomSettings";
    case RoomOwnerCommandDU_CommandType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface RoomOwnerCommandDU_KickedUserData {
  usertag: string;
  socketID: string;
  time?: string | undefined;
}

export interface ServerModCommandDto {
  commandType: ServerModCommandDto_ModCommandType;
  stringValue?: string | undefined;
  jsonValue?: string | undefined;
  usertag?: string | undefined;
  socketID?: string | undefined;
  userBadge?: UserBadges | undefined;
}

export enum ServerModCommandDto_ModCommandType {
  Invalid = 0,
  BanUser = 1,
  UnbanAccount = 2,
  UnbanIp = 3,
  AddBadge = 4,
  RemoveBadge = 5,
  PermissionUpdatedCommand = 6,
  KickUser = 7,
  ClearChat = 8,
  UNRECOGNIZED = -1,
}

export function serverModCommandDto_ModCommandTypeFromJSON(object: any): ServerModCommandDto_ModCommandType {
  switch (object) {
    case 0:
    case "Invalid":
      return ServerModCommandDto_ModCommandType.Invalid;
    case 1:
    case "BanUser":
      return ServerModCommandDto_ModCommandType.BanUser;
    case 2:
    case "UnbanAccount":
      return ServerModCommandDto_ModCommandType.UnbanAccount;
    case 3:
    case "UnbanIp":
      return ServerModCommandDto_ModCommandType.UnbanIp;
    case 4:
    case "AddBadge":
      return ServerModCommandDto_ModCommandType.AddBadge;
    case 5:
    case "RemoveBadge":
      return ServerModCommandDto_ModCommandType.RemoveBadge;
    case 6:
    case "PermissionUpdatedCommand":
      return ServerModCommandDto_ModCommandType.PermissionUpdatedCommand;
    case 7:
    case "KickUser":
      return ServerModCommandDto_ModCommandType.KickUser;
    case 8:
    case "ClearChat":
      return ServerModCommandDto_ModCommandType.ClearChat;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ServerModCommandDto_ModCommandType.UNRECOGNIZED;
  }
}

export function serverModCommandDto_ModCommandTypeToJSON(object: ServerModCommandDto_ModCommandType): string {
  switch (object) {
    case ServerModCommandDto_ModCommandType.Invalid:
      return "Invalid";
    case ServerModCommandDto_ModCommandType.BanUser:
      return "BanUser";
    case ServerModCommandDto_ModCommandType.UnbanAccount:
      return "UnbanAccount";
    case ServerModCommandDto_ModCommandType.UnbanIp:
      return "UnbanIp";
    case ServerModCommandDto_ModCommandType.AddBadge:
      return "AddBadge";
    case ServerModCommandDto_ModCommandType.RemoveBadge:
      return "RemoveBadge";
    case ServerModCommandDto_ModCommandType.PermissionUpdatedCommand:
      return "PermissionUpdatedCommand";
    case ServerModCommandDto_ModCommandType.KickUser:
      return "KickUser";
    case ServerModCommandDto_ModCommandType.ClearChat:
      return "ClearChat";
    case ServerModCommandDto_ModCommandType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ServerModCommandDU {
  usertag: string;
  serverCommand: ServerModCommandDto | undefined;
}

export interface RoomChatServerCommandMessage {
  command: string;
}

export interface CreateRoomParam {
  RoomID?: string | undefined;
  RoomName: string;
  RoomType: RoomType;
  RoomOwner: string;
  RoomStatus: RoomStatus;
  Password?: string | undefined;
  MaxPlayers: number;
  WelcomeMessage?: string | undefined;
  AutoRemove: boolean;
  OnlyOwnerCanChat: boolean;
  OnlyOwnerCanPlay: boolean;
  AllowBlackMidi: boolean;
  AllowGuests: boolean;
  AllowBots: boolean;
  OnlyMods: boolean;
  FilterProfanity: boolean;
  StageDetailsJSON?: string | undefined;
  HostDetails?: RoomHostDetails | undefined;
  NoChatAllowed: boolean;
  NoPlayingAllowed: boolean;
  StageDetailsPROTO: Uint8Array;
}

export interface AvatarCommandDto {
  commandType: AvatarCommandDto_AvatarCommandType;
  socketID?: string | undefined;
  worldPosition?: AvatarWorldDataDto_AvatarMessageWorldPosition | undefined;
  intValue?: number | undefined;
}

export enum AvatarCommandDto_AvatarCommandType {
  Invalid = 0,
  SetPosition = 1,
  SetPianoBenchTarget = 2,
  UNRECOGNIZED = -1,
}

export function avatarCommandDto_AvatarCommandTypeFromJSON(object: any): AvatarCommandDto_AvatarCommandType {
  switch (object) {
    case 0:
    case "Invalid":
      return AvatarCommandDto_AvatarCommandType.Invalid;
    case 1:
    case "SetPosition":
      return AvatarCommandDto_AvatarCommandType.SetPosition;
    case 2:
    case "SetPianoBenchTarget":
      return AvatarCommandDto_AvatarCommandType.SetPianoBenchTarget;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AvatarCommandDto_AvatarCommandType.UNRECOGNIZED;
  }
}

export function avatarCommandDto_AvatarCommandTypeToJSON(object: AvatarCommandDto_AvatarCommandType): string {
  switch (object) {
    case AvatarCommandDto_AvatarCommandType.Invalid:
      return "Invalid";
    case AvatarCommandDto_AvatarCommandType.SetPosition:
      return "SetPosition";
    case AvatarCommandDto_AvatarCommandType.SetPianoBenchTarget:
      return "SetPianoBenchTarget";
    case AvatarCommandDto_AvatarCommandType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ServerMessage {
  messageType: ServerMessageType;
  chatMessageInputData?: ChatMessageInputData | undefined;
  midiMessageInputDto?: MidiMessageInputDto | undefined;
  serverCommand?: ServerCommandDU | undefined;
  serverModCommand?: ServerModCommandDU | undefined;
  roomChatServerCommand?: RoomChatServerCommandMessage | undefined;
  createRoomParam?: CreateRoomParam | undefined;
  joinRoomRequest?: ServerMessage_JoinRoomByNameRequest | undefined;
  roomOwnerCommand?: RoomOwnerCommandDU | undefined;
  avatarCommand?: AvatarCommandDto | undefined;
}

export interface ServerMessage_JoinRoomByNameRequest {
  roomName: string;
  createRoomIfNotExist: boolean;
  joinNextAvailableLobby: boolean;
}

function createBaseChatMessageInputData(): ChatMessageInputData {
  return { text: "", options: undefined };
}

export const ChatMessageInputData = {
  encode(message: ChatMessageInputData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.text !== "") {
      writer.uint32(10).string(message.text);
    }
    if (message.options !== undefined) {
      ChatMessageInputData_ChatMessageDataOptions.encode(message.options, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChatMessageInputData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatMessageInputData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.text = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.options = ChatMessageInputData_ChatMessageDataOptions.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatMessageInputData {
    return {
      text: isSet(object.text) ? globalThis.String(object.text) : "",
      options: isSet(object.options) ? ChatMessageInputData_ChatMessageDataOptions.fromJSON(object.options) : undefined,
    };
  },

  toJSON(message: ChatMessageInputData): unknown {
    const obj: any = {};
    if (message.text !== "") {
      obj.text = message.text;
    }
    if (message.options !== undefined) {
      obj.options = ChatMessageInputData_ChatMessageDataOptions.toJSON(message.options);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChatMessageInputData>, I>>(base?: I): ChatMessageInputData {
    return ChatMessageInputData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatMessageInputData>, I>>(object: I): ChatMessageInputData {
    const message = createBaseChatMessageInputData();
    message.text = object.text ?? "";
    message.options = (object.options !== undefined && object.options !== null)
      ? ChatMessageInputData_ChatMessageDataOptions.fromPartial(object.options)
      : undefined;
    return message;
  },
};

function createBaseChatMessageInputData_ChatMessageDataOptions(): ChatMessageInputData_ChatMessageDataOptions {
  return { syncToDiscord: false, isFromPlugin: false, messageReplyID: "", messageEditID: "" };
}

export const ChatMessageInputData_ChatMessageDataOptions = {
  encode(message: ChatMessageInputData_ChatMessageDataOptions, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.syncToDiscord !== false) {
      writer.uint32(8).bool(message.syncToDiscord);
    }
    if (message.isFromPlugin !== false) {
      writer.uint32(16).bool(message.isFromPlugin);
    }
    if (message.messageReplyID !== "") {
      writer.uint32(26).string(message.messageReplyID);
    }
    if (message.messageEditID !== "") {
      writer.uint32(34).string(message.messageEditID);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChatMessageInputData_ChatMessageDataOptions {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatMessageInputData_ChatMessageDataOptions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.syncToDiscord = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.isFromPlugin = reader.bool();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.messageReplyID = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.messageEditID = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatMessageInputData_ChatMessageDataOptions {
    return {
      syncToDiscord: isSet(object.syncToDiscord) ? globalThis.Boolean(object.syncToDiscord) : false,
      isFromPlugin: isSet(object.isFromPlugin) ? globalThis.Boolean(object.isFromPlugin) : false,
      messageReplyID: isSet(object.messageReplyID) ? globalThis.String(object.messageReplyID) : "",
      messageEditID: isSet(object.messageEditID) ? globalThis.String(object.messageEditID) : "",
    };
  },

  toJSON(message: ChatMessageInputData_ChatMessageDataOptions): unknown {
    const obj: any = {};
    if (message.syncToDiscord !== false) {
      obj.syncToDiscord = message.syncToDiscord;
    }
    if (message.isFromPlugin !== false) {
      obj.isFromPlugin = message.isFromPlugin;
    }
    if (message.messageReplyID !== "") {
      obj.messageReplyID = message.messageReplyID;
    }
    if (message.messageEditID !== "") {
      obj.messageEditID = message.messageEditID;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChatMessageInputData_ChatMessageDataOptions>, I>>(
    base?: I,
  ): ChatMessageInputData_ChatMessageDataOptions {
    return ChatMessageInputData_ChatMessageDataOptions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatMessageInputData_ChatMessageDataOptions>, I>>(
    object: I,
  ): ChatMessageInputData_ChatMessageDataOptions {
    const message = createBaseChatMessageInputData_ChatMessageDataOptions();
    message.syncToDiscord = object.syncToDiscord ?? false;
    message.isFromPlugin = object.isFromPlugin ?? false;
    message.messageReplyID = object.messageReplyID ?? "";
    message.messageEditID = object.messageEditID ?? "";
    return message;
  },
};

function createBaseMidiMessageInputDto(): MidiMessageInputDto {
  return { time: "", data: [] };
}

export const MidiMessageInputDto = {
  encode(message: MidiMessageInputDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.time !== "") {
      writer.uint32(10).string(message.time);
    }
    for (const v of message.data) {
      MidiMessageInputDto_MidiMessageInputBuffer.encode(v!, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageInputDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageInputDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.time = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.data.push(MidiMessageInputDto_MidiMessageInputBuffer.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageInputDto {
    return {
      time: isSet(object.time) ? globalThis.String(object.time) : "",
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MidiMessageInputDto_MidiMessageInputBuffer.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidiMessageInputDto): unknown {
    const obj: any = {};
    if (message.time !== "") {
      obj.time = message.time;
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MidiMessageInputDto_MidiMessageInputBuffer.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageInputDto>, I>>(base?: I): MidiMessageInputDto {
    return MidiMessageInputDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageInputDto>, I>>(object: I): MidiMessageInputDto {
    const message = createBaseMidiMessageInputDto();
    message.time = object.time ?? "";
    message.data = object.data?.map((e) => MidiMessageInputDto_MidiMessageInputBuffer.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidiMessageInputDto_MidiMessageInputBuffer(): MidiMessageInputDto_MidiMessageInputBuffer {
  return { data: undefined, delay: 0 };
}

export const MidiMessageInputDto_MidiMessageInputBuffer = {
  encode(message: MidiMessageInputDto_MidiMessageInputBuffer, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.data !== undefined) {
      MidiDto.encode(message.data, writer.uint32(10).fork()).ldelim();
    }
    if (message.delay !== 0) {
      writer.uint32(17).double(message.delay);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageInputDto_MidiMessageInputBuffer {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageInputDto_MidiMessageInputBuffer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data = MidiDto.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.delay = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageInputDto_MidiMessageInputBuffer {
    return {
      data: isSet(object.data) ? MidiDto.fromJSON(object.data) : undefined,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : 0,
    };
  },

  toJSON(message: MidiMessageInputDto_MidiMessageInputBuffer): unknown {
    const obj: any = {};
    if (message.data !== undefined) {
      obj.data = MidiDto.toJSON(message.data);
    }
    if (message.delay !== 0) {
      obj.delay = message.delay;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageInputDto_MidiMessageInputBuffer>, I>>(
    base?: I,
  ): MidiMessageInputDto_MidiMessageInputBuffer {
    return MidiMessageInputDto_MidiMessageInputBuffer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageInputDto_MidiMessageInputBuffer>, I>>(
    object: I,
  ): MidiMessageInputDto_MidiMessageInputBuffer {
    const message = createBaseMidiMessageInputDto_MidiMessageInputBuffer();
    message.data = (object.data !== undefined && object.data !== null) ? MidiDto.fromPartial(object.data) : undefined;
    message.delay = object.delay ?? 0;
    return message;
  },
};

function createBaseRoomIDWithPassword(): RoomIDWithPassword {
  return { roomID: "", password: "" };
}

export const RoomIDWithPassword = {
  encode(message: RoomIDWithPassword, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomID !== "") {
      writer.uint32(10).string(message.roomID);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomIDWithPassword {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomIDWithPassword();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomIDWithPassword {
    return {
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      password: isSet(object.password) ? globalThis.String(object.password) : "",
    };
  },

  toJSON(message: RoomIDWithPassword): unknown {
    const obj: any = {};
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.password !== "") {
      obj.password = message.password;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomIDWithPassword>, I>>(base?: I): RoomIDWithPassword {
    return RoomIDWithPassword.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomIDWithPassword>, I>>(object: I): RoomIDWithPassword {
    const message = createBaseRoomIDWithPassword();
    message.roomID = object.roomID ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

function createBaseLoginDataDto(): LoginDataDto {
  return { username: "", password: "" };
}

export const LoginDataDto = {
  encode(message: LoginDataDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.password !== "") {
      writer.uint32(18).string(message.password);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): LoginDataDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseLoginDataDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.password = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): LoginDataDto {
    return {
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      password: isSet(object.password) ? globalThis.String(object.password) : "",
    };
  },

  toJSON(message: LoginDataDto): unknown {
    const obj: any = {};
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.password !== "") {
      obj.password = message.password;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<LoginDataDto>, I>>(base?: I): LoginDataDto {
    return LoginDataDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginDataDto>, I>>(object: I): LoginDataDto {
    const message = createBaseLoginDataDto();
    message.username = object.username ?? "";
    message.password = object.password ?? "";
    return message;
  },
};

function createBaseSimpleEmailDto(): SimpleEmailDto {
  return { email: "" };
}

export const SimpleEmailDto = {
  encode(message: SimpleEmailDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.email !== "") {
      writer.uint32(10).string(message.email);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SimpleEmailDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSimpleEmailDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.email = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SimpleEmailDto {
    return { email: isSet(object.email) ? globalThis.String(object.email) : "" };
  },

  toJSON(message: SimpleEmailDto): unknown {
    const obj: any = {};
    if (message.email !== "") {
      obj.email = message.email;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SimpleEmailDto>, I>>(base?: I): SimpleEmailDto {
    return SimpleEmailDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimpleEmailDto>, I>>(object: I): SimpleEmailDto {
    const message = createBaseSimpleEmailDto();
    message.email = object.email ?? "";
    return message;
  },
};

function createBaseResetPasswordDataDto(): ResetPasswordDataDto {
  return { password: "", confirmpassword: "", token: "" };
}

export const ResetPasswordDataDto = {
  encode(message: ResetPasswordDataDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.password !== "") {
      writer.uint32(10).string(message.password);
    }
    if (message.confirmpassword !== "") {
      writer.uint32(18).string(message.confirmpassword);
    }
    if (message.token !== "") {
      writer.uint32(26).string(message.token);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ResetPasswordDataDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseResetPasswordDataDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.password = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.confirmpassword = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.token = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ResetPasswordDataDto {
    return {
      password: isSet(object.password) ? globalThis.String(object.password) : "",
      confirmpassword: isSet(object.confirmpassword) ? globalThis.String(object.confirmpassword) : "",
      token: isSet(object.token) ? globalThis.String(object.token) : "",
    };
  },

  toJSON(message: ResetPasswordDataDto): unknown {
    const obj: any = {};
    if (message.password !== "") {
      obj.password = message.password;
    }
    if (message.confirmpassword !== "") {
      obj.confirmpassword = message.confirmpassword;
    }
    if (message.token !== "") {
      obj.token = message.token;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ResetPasswordDataDto>, I>>(base?: I): ResetPasswordDataDto {
    return ResetPasswordDataDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResetPasswordDataDto>, I>>(object: I): ResetPasswordDataDto {
    const message = createBaseResetPasswordDataDto();
    message.password = object.password ?? "";
    message.confirmpassword = object.confirmpassword ?? "";
    message.token = object.token ?? "";
    return message;
  },
};

function createBaseRegistrationData(): RegistrationData {
  return { username: "", email: "", password: "", confirmpassword: "", acceptedtos: false };
}

export const RegistrationData = {
  encode(message: RegistrationData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.username !== "") {
      writer.uint32(10).string(message.username);
    }
    if (message.email !== "") {
      writer.uint32(18).string(message.email);
    }
    if (message.password !== "") {
      writer.uint32(26).string(message.password);
    }
    if (message.confirmpassword !== "") {
      writer.uint32(34).string(message.confirmpassword);
    }
    if (message.acceptedtos !== false) {
      writer.uint32(40).bool(message.acceptedtos);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RegistrationData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRegistrationData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.username = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.email = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.password = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.confirmpassword = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.acceptedtos = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RegistrationData {
    return {
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      email: isSet(object.email) ? globalThis.String(object.email) : "",
      password: isSet(object.password) ? globalThis.String(object.password) : "",
      confirmpassword: isSet(object.confirmpassword) ? globalThis.String(object.confirmpassword) : "",
      acceptedtos: isSet(object.acceptedtos) ? globalThis.Boolean(object.acceptedtos) : false,
    };
  },

  toJSON(message: RegistrationData): unknown {
    const obj: any = {};
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.email !== "") {
      obj.email = message.email;
    }
    if (message.password !== "") {
      obj.password = message.password;
    }
    if (message.confirmpassword !== "") {
      obj.confirmpassword = message.confirmpassword;
    }
    if (message.acceptedtos !== false) {
      obj.acceptedtos = message.acceptedtos;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RegistrationData>, I>>(base?: I): RegistrationData {
    return RegistrationData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegistrationData>, I>>(object: I): RegistrationData {
    const message = createBaseRegistrationData();
    message.username = object.username ?? "";
    message.email = object.email ?? "";
    message.password = object.password ?? "";
    message.confirmpassword = object.confirmpassword ?? "";
    message.acceptedtos = object.acceptedtos ?? false;
    return message;
  },
};

function createBaseServerCommandDU(): ServerCommandDU {
  return {
    commandType: 0,
    roomID: undefined,
    roomName: undefined,
    roomIDorName: undefined,
    usertag: undefined,
    jsonValue: undefined,
    boolean: undefined,
    roomIDAndPassword: undefined,
    loginData: undefined,
    emailData: undefined,
    resetPasswordData: undefined,
    registrationData: undefined,
    stringValue: undefined,
  };
}

export const ServerCommandDU = {
  encode(message: ServerCommandDU, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.commandType !== 0) {
      writer.uint32(8).int32(message.commandType);
    }
    if (message.roomID !== undefined) {
      writer.uint32(18).string(message.roomID);
    }
    if (message.roomName !== undefined) {
      writer.uint32(26).string(message.roomName);
    }
    if (message.roomIDorName !== undefined) {
      writer.uint32(34).string(message.roomIDorName);
    }
    if (message.usertag !== undefined) {
      writer.uint32(42).string(message.usertag);
    }
    if (message.jsonValue !== undefined) {
      writer.uint32(58).string(message.jsonValue);
    }
    if (message.boolean !== undefined) {
      writer.uint32(64).bool(message.boolean);
    }
    if (message.roomIDAndPassword !== undefined) {
      RoomIDWithPassword.encode(message.roomIDAndPassword, writer.uint32(74).fork()).ldelim();
    }
    if (message.loginData !== undefined) {
      LoginDataDto.encode(message.loginData, writer.uint32(82).fork()).ldelim();
    }
    if (message.emailData !== undefined) {
      SimpleEmailDto.encode(message.emailData, writer.uint32(90).fork()).ldelim();
    }
    if (message.resetPasswordData !== undefined) {
      ResetPasswordDataDto.encode(message.resetPasswordData, writer.uint32(98).fork()).ldelim();
    }
    if (message.registrationData !== undefined) {
      RegistrationData.encode(message.registrationData, writer.uint32(106).fork()).ldelim();
    }
    if (message.stringValue !== undefined) {
      writer.uint32(114).string(message.stringValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ServerCommandDU {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerCommandDU();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.commandType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.roomIDorName = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.jsonValue = reader.string();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.boolean = reader.bool();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.roomIDAndPassword = RoomIDWithPassword.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.loginData = LoginDataDto.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.emailData = SimpleEmailDto.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.resetPasswordData = ResetPasswordDataDto.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.registrationData = RegistrationData.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.stringValue = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerCommandDU {
    return {
      commandType: isSet(object.commandType) ? serverCommandDU_CommandTypeFromJSON(object.commandType) : 0,
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : undefined,
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : undefined,
      roomIDorName: isSet(object.roomIDorName) ? globalThis.String(object.roomIDorName) : undefined,
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : undefined,
      jsonValue: isSet(object.jsonValue) ? globalThis.String(object.jsonValue) : undefined,
      boolean: isSet(object.boolean) ? globalThis.Boolean(object.boolean) : undefined,
      roomIDAndPassword: isSet(object.roomIDAndPassword)
        ? RoomIDWithPassword.fromJSON(object.roomIDAndPassword)
        : undefined,
      loginData: isSet(object.loginData) ? LoginDataDto.fromJSON(object.loginData) : undefined,
      emailData: isSet(object.emailData) ? SimpleEmailDto.fromJSON(object.emailData) : undefined,
      resetPasswordData: isSet(object.resetPasswordData)
        ? ResetPasswordDataDto.fromJSON(object.resetPasswordData)
        : undefined,
      registrationData: isSet(object.registrationData) ? RegistrationData.fromJSON(object.registrationData) : undefined,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
    };
  },

  toJSON(message: ServerCommandDU): unknown {
    const obj: any = {};
    if (message.commandType !== 0) {
      obj.commandType = serverCommandDU_CommandTypeToJSON(message.commandType);
    }
    if (message.roomID !== undefined) {
      obj.roomID = message.roomID;
    }
    if (message.roomName !== undefined) {
      obj.roomName = message.roomName;
    }
    if (message.roomIDorName !== undefined) {
      obj.roomIDorName = message.roomIDorName;
    }
    if (message.usertag !== undefined) {
      obj.usertag = message.usertag;
    }
    if (message.jsonValue !== undefined) {
      obj.jsonValue = message.jsonValue;
    }
    if (message.boolean !== undefined) {
      obj.boolean = message.boolean;
    }
    if (message.roomIDAndPassword !== undefined) {
      obj.roomIDAndPassword = RoomIDWithPassword.toJSON(message.roomIDAndPassword);
    }
    if (message.loginData !== undefined) {
      obj.loginData = LoginDataDto.toJSON(message.loginData);
    }
    if (message.emailData !== undefined) {
      obj.emailData = SimpleEmailDto.toJSON(message.emailData);
    }
    if (message.resetPasswordData !== undefined) {
      obj.resetPasswordData = ResetPasswordDataDto.toJSON(message.resetPasswordData);
    }
    if (message.registrationData !== undefined) {
      obj.registrationData = RegistrationData.toJSON(message.registrationData);
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerCommandDU>, I>>(base?: I): ServerCommandDU {
    return ServerCommandDU.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerCommandDU>, I>>(object: I): ServerCommandDU {
    const message = createBaseServerCommandDU();
    message.commandType = object.commandType ?? 0;
    message.roomID = object.roomID ?? undefined;
    message.roomName = object.roomName ?? undefined;
    message.roomIDorName = object.roomIDorName ?? undefined;
    message.usertag = object.usertag ?? undefined;
    message.jsonValue = object.jsonValue ?? undefined;
    message.boolean = object.boolean ?? undefined;
    message.roomIDAndPassword = (object.roomIDAndPassword !== undefined && object.roomIDAndPassword !== null)
      ? RoomIDWithPassword.fromPartial(object.roomIDAndPassword)
      : undefined;
    message.loginData = (object.loginData !== undefined && object.loginData !== null)
      ? LoginDataDto.fromPartial(object.loginData)
      : undefined;
    message.emailData = (object.emailData !== undefined && object.emailData !== null)
      ? SimpleEmailDto.fromPartial(object.emailData)
      : undefined;
    message.resetPasswordData = (object.resetPasswordData !== undefined && object.resetPasswordData !== null)
      ? ResetPasswordDataDto.fromPartial(object.resetPasswordData)
      : undefined;
    message.registrationData = (object.registrationData !== undefined && object.registrationData !== null)
      ? RegistrationData.fromPartial(object.registrationData)
      : undefined;
    message.stringValue = object.stringValue ?? undefined;
    return message;
  },
};

function createBaseRoomOwnerCommandDU(): RoomOwnerCommandDU {
  return { commandType: 0, usertag: undefined, socketID: undefined, kickedUserData: undefined };
}

export const RoomOwnerCommandDU = {
  encode(message: RoomOwnerCommandDU, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.commandType !== 0) {
      writer.uint32(8).int32(message.commandType);
    }
    if (message.usertag !== undefined) {
      writer.uint32(18).string(message.usertag);
    }
    if (message.socketID !== undefined) {
      writer.uint32(26).string(message.socketID);
    }
    if (message.kickedUserData !== undefined) {
      RoomOwnerCommandDU_KickedUserData.encode(message.kickedUserData, writer.uint32(34).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomOwnerCommandDU {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomOwnerCommandDU();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.commandType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.kickedUserData = RoomOwnerCommandDU_KickedUserData.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomOwnerCommandDU {
    return {
      commandType: isSet(object.commandType) ? roomOwnerCommandDU_CommandTypeFromJSON(object.commandType) : 0,
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : undefined,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : undefined,
      kickedUserData: isSet(object.kickedUserData)
        ? RoomOwnerCommandDU_KickedUserData.fromJSON(object.kickedUserData)
        : undefined,
    };
  },

  toJSON(message: RoomOwnerCommandDU): unknown {
    const obj: any = {};
    if (message.commandType !== 0) {
      obj.commandType = roomOwnerCommandDU_CommandTypeToJSON(message.commandType);
    }
    if (message.usertag !== undefined) {
      obj.usertag = message.usertag;
    }
    if (message.socketID !== undefined) {
      obj.socketID = message.socketID;
    }
    if (message.kickedUserData !== undefined) {
      obj.kickedUserData = RoomOwnerCommandDU_KickedUserData.toJSON(message.kickedUserData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomOwnerCommandDU>, I>>(base?: I): RoomOwnerCommandDU {
    return RoomOwnerCommandDU.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomOwnerCommandDU>, I>>(object: I): RoomOwnerCommandDU {
    const message = createBaseRoomOwnerCommandDU();
    message.commandType = object.commandType ?? 0;
    message.usertag = object.usertag ?? undefined;
    message.socketID = object.socketID ?? undefined;
    message.kickedUserData = (object.kickedUserData !== undefined && object.kickedUserData !== null)
      ? RoomOwnerCommandDU_KickedUserData.fromPartial(object.kickedUserData)
      : undefined;
    return message;
  },
};

function createBaseRoomOwnerCommandDU_KickedUserData(): RoomOwnerCommandDU_KickedUserData {
  return { usertag: "", socketID: "", time: undefined };
}

export const RoomOwnerCommandDU_KickedUserData = {
  encode(message: RoomOwnerCommandDU_KickedUserData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.usertag !== "") {
      writer.uint32(10).string(message.usertag);
    }
    if (message.socketID !== "") {
      writer.uint32(18).string(message.socketID);
    }
    if (message.time !== undefined) {
      writer.uint32(26).string(message.time);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomOwnerCommandDU_KickedUserData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomOwnerCommandDU_KickedUserData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.time = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomOwnerCommandDU_KickedUserData {
    return {
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      time: isSet(object.time) ? globalThis.String(object.time) : undefined,
    };
  },

  toJSON(message: RoomOwnerCommandDU_KickedUserData): unknown {
    const obj: any = {};
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.time !== undefined) {
      obj.time = message.time;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomOwnerCommandDU_KickedUserData>, I>>(
    base?: I,
  ): RoomOwnerCommandDU_KickedUserData {
    return RoomOwnerCommandDU_KickedUserData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomOwnerCommandDU_KickedUserData>, I>>(
    object: I,
  ): RoomOwnerCommandDU_KickedUserData {
    const message = createBaseRoomOwnerCommandDU_KickedUserData();
    message.usertag = object.usertag ?? "";
    message.socketID = object.socketID ?? "";
    message.time = object.time ?? undefined;
    return message;
  },
};

function createBaseServerModCommandDto(): ServerModCommandDto {
  return {
    commandType: 0,
    stringValue: undefined,
    jsonValue: undefined,
    usertag: undefined,
    socketID: undefined,
    userBadge: undefined,
  };
}

export const ServerModCommandDto = {
  encode(message: ServerModCommandDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.commandType !== 0) {
      writer.uint32(8).int32(message.commandType);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.jsonValue !== undefined) {
      writer.uint32(26).string(message.jsonValue);
    }
    if (message.usertag !== undefined) {
      writer.uint32(34).string(message.usertag);
    }
    if (message.socketID !== undefined) {
      writer.uint32(42).string(message.socketID);
    }
    if (message.userBadge !== undefined) {
      UserBadges.encode(message.userBadge, writer.uint32(50).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ServerModCommandDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerModCommandDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.commandType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.jsonValue = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.userBadge = UserBadges.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerModCommandDto {
    return {
      commandType: isSet(object.commandType) ? serverModCommandDto_ModCommandTypeFromJSON(object.commandType) : 0,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      jsonValue: isSet(object.jsonValue) ? globalThis.String(object.jsonValue) : undefined,
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : undefined,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : undefined,
      userBadge: isSet(object.userBadge) ? UserBadges.fromJSON(object.userBadge) : undefined,
    };
  },

  toJSON(message: ServerModCommandDto): unknown {
    const obj: any = {};
    if (message.commandType !== 0) {
      obj.commandType = serverModCommandDto_ModCommandTypeToJSON(message.commandType);
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.jsonValue !== undefined) {
      obj.jsonValue = message.jsonValue;
    }
    if (message.usertag !== undefined) {
      obj.usertag = message.usertag;
    }
    if (message.socketID !== undefined) {
      obj.socketID = message.socketID;
    }
    if (message.userBadge !== undefined) {
      obj.userBadge = UserBadges.toJSON(message.userBadge);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerModCommandDto>, I>>(base?: I): ServerModCommandDto {
    return ServerModCommandDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerModCommandDto>, I>>(object: I): ServerModCommandDto {
    const message = createBaseServerModCommandDto();
    message.commandType = object.commandType ?? 0;
    message.stringValue = object.stringValue ?? undefined;
    message.jsonValue = object.jsonValue ?? undefined;
    message.usertag = object.usertag ?? undefined;
    message.socketID = object.socketID ?? undefined;
    message.userBadge = (object.userBadge !== undefined && object.userBadge !== null)
      ? UserBadges.fromPartial(object.userBadge)
      : undefined;
    return message;
  },
};

function createBaseServerModCommandDU(): ServerModCommandDU {
  return { usertag: "", serverCommand: undefined };
}

export const ServerModCommandDU = {
  encode(message: ServerModCommandDU, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.usertag !== "") {
      writer.uint32(10).string(message.usertag);
    }
    if (message.serverCommand !== undefined) {
      ServerModCommandDto.encode(message.serverCommand, writer.uint32(18).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ServerModCommandDU {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerModCommandDU();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.serverCommand = ServerModCommandDto.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerModCommandDU {
    return {
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      serverCommand: isSet(object.serverCommand) ? ServerModCommandDto.fromJSON(object.serverCommand) : undefined,
    };
  },

  toJSON(message: ServerModCommandDU): unknown {
    const obj: any = {};
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.serverCommand !== undefined) {
      obj.serverCommand = ServerModCommandDto.toJSON(message.serverCommand);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerModCommandDU>, I>>(base?: I): ServerModCommandDU {
    return ServerModCommandDU.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerModCommandDU>, I>>(object: I): ServerModCommandDU {
    const message = createBaseServerModCommandDU();
    message.usertag = object.usertag ?? "";
    message.serverCommand = (object.serverCommand !== undefined && object.serverCommand !== null)
      ? ServerModCommandDto.fromPartial(object.serverCommand)
      : undefined;
    return message;
  },
};

function createBaseRoomChatServerCommandMessage(): RoomChatServerCommandMessage {
  return { command: "" };
}

export const RoomChatServerCommandMessage = {
  encode(message: RoomChatServerCommandMessage, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.command !== "") {
      writer.uint32(10).string(message.command);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomChatServerCommandMessage {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomChatServerCommandMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.command = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomChatServerCommandMessage {
    return { command: isSet(object.command) ? globalThis.String(object.command) : "" };
  },

  toJSON(message: RoomChatServerCommandMessage): unknown {
    const obj: any = {};
    if (message.command !== "") {
      obj.command = message.command;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomChatServerCommandMessage>, I>>(base?: I): RoomChatServerCommandMessage {
    return RoomChatServerCommandMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomChatServerCommandMessage>, I>>(object: I): RoomChatServerCommandMessage {
    const message = createBaseRoomChatServerCommandMessage();
    message.command = object.command ?? "";
    return message;
  },
};

function createBaseCreateRoomParam(): CreateRoomParam {
  return {
    RoomID: undefined,
    RoomName: "",
    RoomType: 0,
    RoomOwner: "",
    RoomStatus: 0,
    Password: undefined,
    MaxPlayers: 0,
    WelcomeMessage: undefined,
    AutoRemove: false,
    OnlyOwnerCanChat: false,
    OnlyOwnerCanPlay: false,
    AllowBlackMidi: false,
    AllowGuests: false,
    AllowBots: false,
    OnlyMods: false,
    FilterProfanity: false,
    StageDetailsJSON: undefined,
    HostDetails: undefined,
    NoChatAllowed: false,
    NoPlayingAllowed: false,
    StageDetailsPROTO: new Uint8Array(0),
  };
}

export const CreateRoomParam = {
  encode(message: CreateRoomParam, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.RoomID !== undefined) {
      writer.uint32(10).string(message.RoomID);
    }
    if (message.RoomName !== "") {
      writer.uint32(18).string(message.RoomName);
    }
    if (message.RoomType !== 0) {
      writer.uint32(24).int32(message.RoomType);
    }
    if (message.RoomOwner !== "") {
      writer.uint32(34).string(message.RoomOwner);
    }
    if (message.RoomStatus !== 0) {
      writer.uint32(40).int32(message.RoomStatus);
    }
    if (message.Password !== undefined) {
      writer.uint32(50).string(message.Password);
    }
    if (message.MaxPlayers !== 0) {
      writer.uint32(56).int32(message.MaxPlayers);
    }
    if (message.WelcomeMessage !== undefined) {
      writer.uint32(66).string(message.WelcomeMessage);
    }
    if (message.AutoRemove !== false) {
      writer.uint32(72).bool(message.AutoRemove);
    }
    if (message.OnlyOwnerCanChat !== false) {
      writer.uint32(80).bool(message.OnlyOwnerCanChat);
    }
    if (message.OnlyOwnerCanPlay !== false) {
      writer.uint32(88).bool(message.OnlyOwnerCanPlay);
    }
    if (message.AllowBlackMidi !== false) {
      writer.uint32(96).bool(message.AllowBlackMidi);
    }
    if (message.AllowGuests !== false) {
      writer.uint32(104).bool(message.AllowGuests);
    }
    if (message.AllowBots !== false) {
      writer.uint32(112).bool(message.AllowBots);
    }
    if (message.OnlyMods !== false) {
      writer.uint32(120).bool(message.OnlyMods);
    }
    if (message.FilterProfanity !== false) {
      writer.uint32(128).bool(message.FilterProfanity);
    }
    if (message.StageDetailsJSON !== undefined) {
      writer.uint32(138).string(message.StageDetailsJSON);
    }
    if (message.HostDetails !== undefined) {
      RoomHostDetails.encode(message.HostDetails, writer.uint32(146).fork()).ldelim();
    }
    if (message.NoChatAllowed !== false) {
      writer.uint32(152).bool(message.NoChatAllowed);
    }
    if (message.NoPlayingAllowed !== false) {
      writer.uint32(160).bool(message.NoPlayingAllowed);
    }
    if (message.StageDetailsPROTO.length !== 0) {
      writer.uint32(170).bytes(message.StageDetailsPROTO);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CreateRoomParam {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCreateRoomParam();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.RoomID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.RoomName = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.RoomType = reader.int32() as any;
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.RoomOwner = reader.string();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }

          message.RoomStatus = reader.int32() as any;
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.Password = reader.string();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }

          message.MaxPlayers = reader.int32();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.WelcomeMessage = reader.string();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }

          message.AutoRemove = reader.bool();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }

          message.OnlyOwnerCanChat = reader.bool();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }

          message.OnlyOwnerCanPlay = reader.bool();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.AllowBlackMidi = reader.bool();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.AllowGuests = reader.bool();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.AllowBots = reader.bool();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.OnlyMods = reader.bool();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.FilterProfanity = reader.bool();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.StageDetailsJSON = reader.string();
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.HostDetails = RoomHostDetails.decode(reader, reader.uint32());
          continue;
        case 19:
          if (tag !== 152) {
            break;
          }

          message.NoChatAllowed = reader.bool();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.NoPlayingAllowed = reader.bool();
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.StageDetailsPROTO = reader.bytes();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CreateRoomParam {
    return {
      RoomID: isSet(object.RoomID) ? globalThis.String(object.RoomID) : undefined,
      RoomName: isSet(object.RoomName) ? globalThis.String(object.RoomName) : "",
      RoomType: isSet(object.RoomType) ? roomTypeFromJSON(object.RoomType) : 0,
      RoomOwner: isSet(object.RoomOwner) ? globalThis.String(object.RoomOwner) : "",
      RoomStatus: isSet(object.RoomStatus) ? roomStatusFromJSON(object.RoomStatus) : 0,
      Password: isSet(object.Password) ? globalThis.String(object.Password) : undefined,
      MaxPlayers: isSet(object.MaxPlayers) ? globalThis.Number(object.MaxPlayers) : 0,
      WelcomeMessage: isSet(object.WelcomeMessage) ? globalThis.String(object.WelcomeMessage) : undefined,
      AutoRemove: isSet(object.AutoRemove) ? globalThis.Boolean(object.AutoRemove) : false,
      OnlyOwnerCanChat: isSet(object.OnlyOwnerCanChat) ? globalThis.Boolean(object.OnlyOwnerCanChat) : false,
      OnlyOwnerCanPlay: isSet(object.OnlyOwnerCanPlay) ? globalThis.Boolean(object.OnlyOwnerCanPlay) : false,
      AllowBlackMidi: isSet(object.AllowBlackMidi) ? globalThis.Boolean(object.AllowBlackMidi) : false,
      AllowGuests: isSet(object.AllowGuests) ? globalThis.Boolean(object.AllowGuests) : false,
      AllowBots: isSet(object.AllowBots) ? globalThis.Boolean(object.AllowBots) : false,
      OnlyMods: isSet(object.OnlyMods) ? globalThis.Boolean(object.OnlyMods) : false,
      FilterProfanity: isSet(object.FilterProfanity) ? globalThis.Boolean(object.FilterProfanity) : false,
      StageDetailsJSON: isSet(object.StageDetailsJSON) ? globalThis.String(object.StageDetailsJSON) : undefined,
      HostDetails: isSet(object.HostDetails) ? RoomHostDetails.fromJSON(object.HostDetails) : undefined,
      NoChatAllowed: isSet(object.NoChatAllowed) ? globalThis.Boolean(object.NoChatAllowed) : false,
      NoPlayingAllowed: isSet(object.NoPlayingAllowed) ? globalThis.Boolean(object.NoPlayingAllowed) : false,
      StageDetailsPROTO: isSet(object.StageDetailsPROTO)
        ? bytesFromBase64(object.StageDetailsPROTO)
        : new Uint8Array(0),
    };
  },

  toJSON(message: CreateRoomParam): unknown {
    const obj: any = {};
    if (message.RoomID !== undefined) {
      obj.RoomID = message.RoomID;
    }
    if (message.RoomName !== "") {
      obj.RoomName = message.RoomName;
    }
    if (message.RoomType !== 0) {
      obj.RoomType = roomTypeToJSON(message.RoomType);
    }
    if (message.RoomOwner !== "") {
      obj.RoomOwner = message.RoomOwner;
    }
    if (message.RoomStatus !== 0) {
      obj.RoomStatus = roomStatusToJSON(message.RoomStatus);
    }
    if (message.Password !== undefined) {
      obj.Password = message.Password;
    }
    if (message.MaxPlayers !== 0) {
      obj.MaxPlayers = Math.round(message.MaxPlayers);
    }
    if (message.WelcomeMessage !== undefined) {
      obj.WelcomeMessage = message.WelcomeMessage;
    }
    if (message.AutoRemove !== false) {
      obj.AutoRemove = message.AutoRemove;
    }
    if (message.OnlyOwnerCanChat !== false) {
      obj.OnlyOwnerCanChat = message.OnlyOwnerCanChat;
    }
    if (message.OnlyOwnerCanPlay !== false) {
      obj.OnlyOwnerCanPlay = message.OnlyOwnerCanPlay;
    }
    if (message.AllowBlackMidi !== false) {
      obj.AllowBlackMidi = message.AllowBlackMidi;
    }
    if (message.AllowGuests !== false) {
      obj.AllowGuests = message.AllowGuests;
    }
    if (message.AllowBots !== false) {
      obj.AllowBots = message.AllowBots;
    }
    if (message.OnlyMods !== false) {
      obj.OnlyMods = message.OnlyMods;
    }
    if (message.FilterProfanity !== false) {
      obj.FilterProfanity = message.FilterProfanity;
    }
    if (message.StageDetailsJSON !== undefined) {
      obj.StageDetailsJSON = message.StageDetailsJSON;
    }
    if (message.HostDetails !== undefined) {
      obj.HostDetails = RoomHostDetails.toJSON(message.HostDetails);
    }
    if (message.NoChatAllowed !== false) {
      obj.NoChatAllowed = message.NoChatAllowed;
    }
    if (message.NoPlayingAllowed !== false) {
      obj.NoPlayingAllowed = message.NoPlayingAllowed;
    }
    if (message.StageDetailsPROTO.length !== 0) {
      obj.StageDetailsPROTO = base64FromBytes(message.StageDetailsPROTO);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CreateRoomParam>, I>>(base?: I): CreateRoomParam {
    return CreateRoomParam.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomParam>, I>>(object: I): CreateRoomParam {
    const message = createBaseCreateRoomParam();
    message.RoomID = object.RoomID ?? undefined;
    message.RoomName = object.RoomName ?? "";
    message.RoomType = object.RoomType ?? 0;
    message.RoomOwner = object.RoomOwner ?? "";
    message.RoomStatus = object.RoomStatus ?? 0;
    message.Password = object.Password ?? undefined;
    message.MaxPlayers = object.MaxPlayers ?? 0;
    message.WelcomeMessage = object.WelcomeMessage ?? undefined;
    message.AutoRemove = object.AutoRemove ?? false;
    message.OnlyOwnerCanChat = object.OnlyOwnerCanChat ?? false;
    message.OnlyOwnerCanPlay = object.OnlyOwnerCanPlay ?? false;
    message.AllowBlackMidi = object.AllowBlackMidi ?? false;
    message.AllowGuests = object.AllowGuests ?? false;
    message.AllowBots = object.AllowBots ?? false;
    message.OnlyMods = object.OnlyMods ?? false;
    message.FilterProfanity = object.FilterProfanity ?? false;
    message.StageDetailsJSON = object.StageDetailsJSON ?? undefined;
    message.HostDetails = (object.HostDetails !== undefined && object.HostDetails !== null)
      ? RoomHostDetails.fromPartial(object.HostDetails)
      : undefined;
    message.NoChatAllowed = object.NoChatAllowed ?? false;
    message.NoPlayingAllowed = object.NoPlayingAllowed ?? false;
    message.StageDetailsPROTO = object.StageDetailsPROTO ?? new Uint8Array(0);
    return message;
  },
};

function createBaseAvatarCommandDto(): AvatarCommandDto {
  return { commandType: 0, socketID: undefined, worldPosition: undefined, intValue: undefined };
}

export const AvatarCommandDto = {
  encode(message: AvatarCommandDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.commandType !== 0) {
      writer.uint32(8).int32(message.commandType);
    }
    if (message.socketID !== undefined) {
      writer.uint32(18).string(message.socketID);
    }
    if (message.worldPosition !== undefined) {
      AvatarWorldDataDto_AvatarMessageWorldPosition.encode(message.worldPosition, writer.uint32(26).fork()).ldelim();
    }
    if (message.intValue !== undefined) {
      writer.uint32(32).int32(message.intValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AvatarCommandDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAvatarCommandDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.commandType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.intValue = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AvatarCommandDto {
    return {
      commandType: isSet(object.commandType) ? avatarCommandDto_AvatarCommandTypeFromJSON(object.commandType) : 0,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : undefined,
      worldPosition: isSet(object.worldPosition)
        ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromJSON(object.worldPosition)
        : undefined,
      intValue: isSet(object.intValue) ? globalThis.Number(object.intValue) : undefined,
    };
  },

  toJSON(message: AvatarCommandDto): unknown {
    const obj: any = {};
    if (message.commandType !== 0) {
      obj.commandType = avatarCommandDto_AvatarCommandTypeToJSON(message.commandType);
    }
    if (message.socketID !== undefined) {
      obj.socketID = message.socketID;
    }
    if (message.worldPosition !== undefined) {
      obj.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.toJSON(message.worldPosition);
    }
    if (message.intValue !== undefined) {
      obj.intValue = Math.round(message.intValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AvatarCommandDto>, I>>(base?: I): AvatarCommandDto {
    return AvatarCommandDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvatarCommandDto>, I>>(object: I): AvatarCommandDto {
    const message = createBaseAvatarCommandDto();
    message.commandType = object.commandType ?? 0;
    message.socketID = object.socketID ?? undefined;
    message.worldPosition = (object.worldPosition !== undefined && object.worldPosition !== null)
      ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(object.worldPosition)
      : undefined;
    message.intValue = object.intValue ?? undefined;
    return message;
  },
};

function createBaseServerMessage(): ServerMessage {
  return {
    messageType: 0,
    chatMessageInputData: undefined,
    midiMessageInputDto: undefined,
    serverCommand: undefined,
    serverModCommand: undefined,
    roomChatServerCommand: undefined,
    createRoomParam: undefined,
    joinRoomRequest: undefined,
    roomOwnerCommand: undefined,
    avatarCommand: undefined,
  };
}

export const ServerMessage = {
  encode(message: ServerMessage, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.messageType !== 0) {
      writer.uint32(8).int32(message.messageType);
    }
    if (message.chatMessageInputData !== undefined) {
      ChatMessageInputData.encode(message.chatMessageInputData, writer.uint32(18).fork()).ldelim();
    }
    if (message.midiMessageInputDto !== undefined) {
      MidiMessageInputDto.encode(message.midiMessageInputDto, writer.uint32(26).fork()).ldelim();
    }
    if (message.serverCommand !== undefined) {
      ServerCommandDU.encode(message.serverCommand, writer.uint32(34).fork()).ldelim();
    }
    if (message.serverModCommand !== undefined) {
      ServerModCommandDU.encode(message.serverModCommand, writer.uint32(42).fork()).ldelim();
    }
    if (message.roomChatServerCommand !== undefined) {
      RoomChatServerCommandMessage.encode(message.roomChatServerCommand, writer.uint32(50).fork()).ldelim();
    }
    if (message.createRoomParam !== undefined) {
      CreateRoomParam.encode(message.createRoomParam, writer.uint32(58).fork()).ldelim();
    }
    if (message.joinRoomRequest !== undefined) {
      ServerMessage_JoinRoomByNameRequest.encode(message.joinRoomRequest, writer.uint32(66).fork()).ldelim();
    }
    if (message.roomOwnerCommand !== undefined) {
      RoomOwnerCommandDU.encode(message.roomOwnerCommand, writer.uint32(74).fork()).ldelim();
    }
    if (message.avatarCommand !== undefined) {
      AvatarCommandDto.encode(message.avatarCommand, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ServerMessage {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.messageType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.chatMessageInputData = ChatMessageInputData.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.midiMessageInputDto = MidiMessageInputDto.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.serverCommand = ServerCommandDU.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.serverModCommand = ServerModCommandDU.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.roomChatServerCommand = RoomChatServerCommandMessage.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.createRoomParam = CreateRoomParam.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.joinRoomRequest = ServerMessage_JoinRoomByNameRequest.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.roomOwnerCommand = RoomOwnerCommandDU.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.avatarCommand = AvatarCommandDto.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerMessage {
    return {
      messageType: isSet(object.messageType) ? serverMessageTypeFromJSON(object.messageType) : 0,
      chatMessageInputData: isSet(object.chatMessageInputData)
        ? ChatMessageInputData.fromJSON(object.chatMessageInputData)
        : undefined,
      midiMessageInputDto: isSet(object.midiMessageInputDto)
        ? MidiMessageInputDto.fromJSON(object.midiMessageInputDto)
        : undefined,
      serverCommand: isSet(object.serverCommand) ? ServerCommandDU.fromJSON(object.serverCommand) : undefined,
      serverModCommand: isSet(object.serverModCommand)
        ? ServerModCommandDU.fromJSON(object.serverModCommand)
        : undefined,
      roomChatServerCommand: isSet(object.roomChatServerCommand)
        ? RoomChatServerCommandMessage.fromJSON(object.roomChatServerCommand)
        : undefined,
      createRoomParam: isSet(object.createRoomParam) ? CreateRoomParam.fromJSON(object.createRoomParam) : undefined,
      joinRoomRequest: isSet(object.joinRoomRequest)
        ? ServerMessage_JoinRoomByNameRequest.fromJSON(object.joinRoomRequest)
        : undefined,
      roomOwnerCommand: isSet(object.roomOwnerCommand)
        ? RoomOwnerCommandDU.fromJSON(object.roomOwnerCommand)
        : undefined,
      avatarCommand: isSet(object.avatarCommand) ? AvatarCommandDto.fromJSON(object.avatarCommand) : undefined,
    };
  },

  toJSON(message: ServerMessage): unknown {
    const obj: any = {};
    if (message.messageType !== 0) {
      obj.messageType = serverMessageTypeToJSON(message.messageType);
    }
    if (message.chatMessageInputData !== undefined) {
      obj.chatMessageInputData = ChatMessageInputData.toJSON(message.chatMessageInputData);
    }
    if (message.midiMessageInputDto !== undefined) {
      obj.midiMessageInputDto = MidiMessageInputDto.toJSON(message.midiMessageInputDto);
    }
    if (message.serverCommand !== undefined) {
      obj.serverCommand = ServerCommandDU.toJSON(message.serverCommand);
    }
    if (message.serverModCommand !== undefined) {
      obj.serverModCommand = ServerModCommandDU.toJSON(message.serverModCommand);
    }
    if (message.roomChatServerCommand !== undefined) {
      obj.roomChatServerCommand = RoomChatServerCommandMessage.toJSON(message.roomChatServerCommand);
    }
    if (message.createRoomParam !== undefined) {
      obj.createRoomParam = CreateRoomParam.toJSON(message.createRoomParam);
    }
    if (message.joinRoomRequest !== undefined) {
      obj.joinRoomRequest = ServerMessage_JoinRoomByNameRequest.toJSON(message.joinRoomRequest);
    }
    if (message.roomOwnerCommand !== undefined) {
      obj.roomOwnerCommand = RoomOwnerCommandDU.toJSON(message.roomOwnerCommand);
    }
    if (message.avatarCommand !== undefined) {
      obj.avatarCommand = AvatarCommandDto.toJSON(message.avatarCommand);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerMessage>, I>>(base?: I): ServerMessage {
    return ServerMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerMessage>, I>>(object: I): ServerMessage {
    const message = createBaseServerMessage();
    message.messageType = object.messageType ?? 0;
    message.chatMessageInputData = (object.chatMessageInputData !== undefined && object.chatMessageInputData !== null)
      ? ChatMessageInputData.fromPartial(object.chatMessageInputData)
      : undefined;
    message.midiMessageInputDto = (object.midiMessageInputDto !== undefined && object.midiMessageInputDto !== null)
      ? MidiMessageInputDto.fromPartial(object.midiMessageInputDto)
      : undefined;
    message.serverCommand = (object.serverCommand !== undefined && object.serverCommand !== null)
      ? ServerCommandDU.fromPartial(object.serverCommand)
      : undefined;
    message.serverModCommand = (object.serverModCommand !== undefined && object.serverModCommand !== null)
      ? ServerModCommandDU.fromPartial(object.serverModCommand)
      : undefined;
    message.roomChatServerCommand =
      (object.roomChatServerCommand !== undefined && object.roomChatServerCommand !== null)
        ? RoomChatServerCommandMessage.fromPartial(object.roomChatServerCommand)
        : undefined;
    message.createRoomParam = (object.createRoomParam !== undefined && object.createRoomParam !== null)
      ? CreateRoomParam.fromPartial(object.createRoomParam)
      : undefined;
    message.joinRoomRequest = (object.joinRoomRequest !== undefined && object.joinRoomRequest !== null)
      ? ServerMessage_JoinRoomByNameRequest.fromPartial(object.joinRoomRequest)
      : undefined;
    message.roomOwnerCommand = (object.roomOwnerCommand !== undefined && object.roomOwnerCommand !== null)
      ? RoomOwnerCommandDU.fromPartial(object.roomOwnerCommand)
      : undefined;
    message.avatarCommand = (object.avatarCommand !== undefined && object.avatarCommand !== null)
      ? AvatarCommandDto.fromPartial(object.avatarCommand)
      : undefined;
    return message;
  },
};

function createBaseServerMessage_JoinRoomByNameRequest(): ServerMessage_JoinRoomByNameRequest {
  return { roomName: "", createRoomIfNotExist: false, joinNextAvailableLobby: false };
}

export const ServerMessage_JoinRoomByNameRequest = {
  encode(message: ServerMessage_JoinRoomByNameRequest, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomName !== "") {
      writer.uint32(10).string(message.roomName);
    }
    if (message.createRoomIfNotExist !== false) {
      writer.uint32(16).bool(message.createRoomIfNotExist);
    }
    if (message.joinNextAvailableLobby !== false) {
      writer.uint32(24).bool(message.joinNextAvailableLobby);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ServerMessage_JoinRoomByNameRequest {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseServerMessage_JoinRoomByNameRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.createRoomIfNotExist = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.joinNextAvailableLobby = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ServerMessage_JoinRoomByNameRequest {
    return {
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      createRoomIfNotExist: isSet(object.createRoomIfNotExist)
        ? globalThis.Boolean(object.createRoomIfNotExist)
        : false,
      joinNextAvailableLobby: isSet(object.joinNextAvailableLobby)
        ? globalThis.Boolean(object.joinNextAvailableLobby)
        : false,
    };
  },

  toJSON(message: ServerMessage_JoinRoomByNameRequest): unknown {
    const obj: any = {};
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.createRoomIfNotExist !== false) {
      obj.createRoomIfNotExist = message.createRoomIfNotExist;
    }
    if (message.joinNextAvailableLobby !== false) {
      obj.joinNextAvailableLobby = message.joinNextAvailableLobby;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ServerMessage_JoinRoomByNameRequest>, I>>(
    base?: I,
  ): ServerMessage_JoinRoomByNameRequest {
    return ServerMessage_JoinRoomByNameRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServerMessage_JoinRoomByNameRequest>, I>>(
    object: I,
  ): ServerMessage_JoinRoomByNameRequest {
    const message = createBaseServerMessage_JoinRoomByNameRequest();
    message.roomName = object.roomName ?? "";
    message.createRoomIfNotExist = object.createRoomIfNotExist ?? false;
    message.joinNextAvailableLobby = object.joinNextAvailableLobby ?? false;
    return message;
  },
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, "base64"));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

function base64FromBytes(arr: Uint8Array): string {
  if ((globalThis as any).Buffer) {
    return globalThis.Buffer.from(arr).toString("base64");
  } else {
    const bin: string[] = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(""));
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
