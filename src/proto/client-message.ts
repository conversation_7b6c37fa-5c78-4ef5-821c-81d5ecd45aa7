// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: client-message.proto

/* eslint-disable */
import _m0 from "protobufjs/minimal";
import {
  MidiDto_MidiAllSoundOff,
  MidiDto_MidiNoteOff,
  MidiDto_MidiNoteOn,
  MidiDto_MidiNoteSustain,
  MidiDto_MidiPitchBend,
  MidiDtoType,
  midiDtoTypeFromJSON,
  midiDtoTypeToJSON,
  MidiNoteSource,
  midiNoteSourceFromJSON,
  midiNoteSourceToJSON,
} from "./midi-renditions";
import {
  BasicRoomDto,
  RoomDto,
  RoomFullDetails,
  RoomSettings,
  RoomStatus,
  roomStatusFromJSON,
  roomStatusToJSON,
  RoomType,
  roomTypeFromJSON,
  roomTypeToJSON,
} from "./room-renditions";
import {
  AvatarWorldDataDto_AvatarMessageWorldPosition,
  ClientSideUserDto,
  FriendDto,
  KickedUserData,
  PendingFriendRequest,
  UserClientDto,
  UserDto,
  UserUpdateCommand,
} from "./user-renditions";

export const protobufPackage = "PianoRhythm.Serialization.ServerToClient.Msgs";

export enum ClientValidationErrorMsg {
  CreateRoomValidationError = 0,
  UpdateRoomValidationError = 1,
  RegistrationValidationError = 2,
  LoginValidationError = 3,
  ForgotPasswordValidationError = 4,
  ResetPasswordValidationError = 5,
  ResendEmailVerificationValidationError = 6,
  UNRECOGNIZED = -1,
}

export function clientValidationErrorMsgFromJSON(object: any): ClientValidationErrorMsg {
  switch (object) {
    case 0:
    case "CreateRoomValidationError":
      return ClientValidationErrorMsg.CreateRoomValidationError;
    case 1:
    case "UpdateRoomValidationError":
      return ClientValidationErrorMsg.UpdateRoomValidationError;
    case 2:
    case "RegistrationValidationError":
      return ClientValidationErrorMsg.RegistrationValidationError;
    case 3:
    case "LoginValidationError":
      return ClientValidationErrorMsg.LoginValidationError;
    case 4:
    case "ForgotPasswordValidationError":
      return ClientValidationErrorMsg.ForgotPasswordValidationError;
    case 5:
    case "ResetPasswordValidationError":
      return ClientValidationErrorMsg.ResetPasswordValidationError;
    case 6:
    case "ResendEmailVerificationValidationError":
      return ClientValidationErrorMsg.ResendEmailVerificationValidationError;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClientValidationErrorMsg.UNRECOGNIZED;
  }
}

export function clientValidationErrorMsgToJSON(object: ClientValidationErrorMsg): string {
  switch (object) {
    case ClientValidationErrorMsg.CreateRoomValidationError:
      return "CreateRoomValidationError";
    case ClientValidationErrorMsg.UpdateRoomValidationError:
      return "UpdateRoomValidationError";
    case ClientValidationErrorMsg.RegistrationValidationError:
      return "RegistrationValidationError";
    case ClientValidationErrorMsg.LoginValidationError:
      return "LoginValidationError";
    case ClientValidationErrorMsg.ForgotPasswordValidationError:
      return "ForgotPasswordValidationError";
    case ClientValidationErrorMsg.ResetPasswordValidationError:
      return "ResetPasswordValidationError";
    case ClientValidationErrorMsg.ResendEmailVerificationValidationError:
      return "ResendEmailVerificationValidationError";
    case ClientValidationErrorMsg.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ClientMessageType {
  None = 0,
  Welcome = 1,
  CmdResponse = 2,
  ApiResponse = 3,
  ClearChat = 4,
  ClearChatBySocketID = 5,
  ClearChatByUsername = 6,
  ClearChatByMessageID = 7,
  ClearChatByAmount = 8,
  RoomChatMessage = 9,
  MidiMessage = 10,
  ServerEventInfo = 11,
  LoadServerCommands = 12,
  LoadRoomOwnerCommands = 13,
  UnloadRoomOwnerCommands = 14,
  RoomSettingsUpdated = 15,
  SessionExpired = 16,
  RejoinRecoveredRoom = 17,
  GetFriendsList = 18,
  GetPendingFriendRequestsList = 19,
  GetUserNotificationSettings = 20,
  RoomStoreServiceUp = 21,
  EditMessageByID = 22,
  Announcements = 23,
  LoadRoomChatHistory = 24,
  LoadRoomWelcomeMessage = 25,
  JoinedRoomSuccess = 26,
  JoinedRoomFail = 27,
  UsersInRoomList = 28,
  UserJoined = 29,
  UserUpdated = 30,
  UserLeft = 31,
  UsersTyping = 32,
  BasicRoomApiCreated = 33,
  BasicRoomApiUpdated = 34,
  BasicRoomApiDeleted = 35,
  ClientUpdated = 36,
  Unauthenticated = 37,
  RoomOwnerUpdated = 38,
  FriendStatusUpdated = 39,
  AddPendingFriendRequest = 40,
  RemovePendingFriendRequest = 41,
  GetKickedUsersList = 42,
  GetRoomFullDetails = 43,
  MaintenanceModeActive = 44,
  UNRECOGNIZED = -1,
}

export function clientMessageTypeFromJSON(object: any): ClientMessageType {
  switch (object) {
    case 0:
    case "None":
      return ClientMessageType.None;
    case 1:
    case "Welcome":
      return ClientMessageType.Welcome;
    case 2:
    case "CmdResponse":
      return ClientMessageType.CmdResponse;
    case 3:
    case "ApiResponse":
      return ClientMessageType.ApiResponse;
    case 4:
    case "ClearChat":
      return ClientMessageType.ClearChat;
    case 5:
    case "ClearChatBySocketID":
      return ClientMessageType.ClearChatBySocketID;
    case 6:
    case "ClearChatByUsername":
      return ClientMessageType.ClearChatByUsername;
    case 7:
    case "ClearChatByMessageID":
      return ClientMessageType.ClearChatByMessageID;
    case 8:
    case "ClearChatByAmount":
      return ClientMessageType.ClearChatByAmount;
    case 9:
    case "RoomChatMessage":
      return ClientMessageType.RoomChatMessage;
    case 10:
    case "MidiMessage":
      return ClientMessageType.MidiMessage;
    case 11:
    case "ServerEventInfo":
      return ClientMessageType.ServerEventInfo;
    case 12:
    case "LoadServerCommands":
      return ClientMessageType.LoadServerCommands;
    case 13:
    case "LoadRoomOwnerCommands":
      return ClientMessageType.LoadRoomOwnerCommands;
    case 14:
    case "UnloadRoomOwnerCommands":
      return ClientMessageType.UnloadRoomOwnerCommands;
    case 15:
    case "RoomSettingsUpdated":
      return ClientMessageType.RoomSettingsUpdated;
    case 16:
    case "SessionExpired":
      return ClientMessageType.SessionExpired;
    case 17:
    case "RejoinRecoveredRoom":
      return ClientMessageType.RejoinRecoveredRoom;
    case 18:
    case "GetFriendsList":
      return ClientMessageType.GetFriendsList;
    case 19:
    case "GetPendingFriendRequestsList":
      return ClientMessageType.GetPendingFriendRequestsList;
    case 20:
    case "GetUserNotificationSettings":
      return ClientMessageType.GetUserNotificationSettings;
    case 21:
    case "RoomStoreServiceUp":
      return ClientMessageType.RoomStoreServiceUp;
    case 22:
    case "EditMessageByID":
      return ClientMessageType.EditMessageByID;
    case 23:
    case "Announcements":
      return ClientMessageType.Announcements;
    case 24:
    case "LoadRoomChatHistory":
      return ClientMessageType.LoadRoomChatHistory;
    case 25:
    case "LoadRoomWelcomeMessage":
      return ClientMessageType.LoadRoomWelcomeMessage;
    case 26:
    case "JoinedRoomSuccess":
      return ClientMessageType.JoinedRoomSuccess;
    case 27:
    case "JoinedRoomFail":
      return ClientMessageType.JoinedRoomFail;
    case 28:
    case "UsersInRoomList":
      return ClientMessageType.UsersInRoomList;
    case 29:
    case "UserJoined":
      return ClientMessageType.UserJoined;
    case 30:
    case "UserUpdated":
      return ClientMessageType.UserUpdated;
    case 31:
    case "UserLeft":
      return ClientMessageType.UserLeft;
    case 32:
    case "UsersTyping":
      return ClientMessageType.UsersTyping;
    case 33:
    case "BasicRoomApiCreated":
      return ClientMessageType.BasicRoomApiCreated;
    case 34:
    case "BasicRoomApiUpdated":
      return ClientMessageType.BasicRoomApiUpdated;
    case 35:
    case "BasicRoomApiDeleted":
      return ClientMessageType.BasicRoomApiDeleted;
    case 36:
    case "ClientUpdated":
      return ClientMessageType.ClientUpdated;
    case 37:
    case "Unauthenticated":
      return ClientMessageType.Unauthenticated;
    case 38:
    case "RoomOwnerUpdated":
      return ClientMessageType.RoomOwnerUpdated;
    case 39:
    case "FriendStatusUpdated":
      return ClientMessageType.FriendStatusUpdated;
    case 40:
    case "AddPendingFriendRequest":
      return ClientMessageType.AddPendingFriendRequest;
    case 41:
    case "RemovePendingFriendRequest":
      return ClientMessageType.RemovePendingFriendRequest;
    case 42:
    case "GetKickedUsersList":
      return ClientMessageType.GetKickedUsersList;
    case 43:
    case "GetRoomFullDetails":
      return ClientMessageType.GetRoomFullDetails;
    case 44:
    case "MaintenanceModeActive":
      return ClientMessageType.MaintenanceModeActive;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClientMessageType.UNRECOGNIZED;
  }
}

export function clientMessageTypeToJSON(object: ClientMessageType): string {
  switch (object) {
    case ClientMessageType.None:
      return "None";
    case ClientMessageType.Welcome:
      return "Welcome";
    case ClientMessageType.CmdResponse:
      return "CmdResponse";
    case ClientMessageType.ApiResponse:
      return "ApiResponse";
    case ClientMessageType.ClearChat:
      return "ClearChat";
    case ClientMessageType.ClearChatBySocketID:
      return "ClearChatBySocketID";
    case ClientMessageType.ClearChatByUsername:
      return "ClearChatByUsername";
    case ClientMessageType.ClearChatByMessageID:
      return "ClearChatByMessageID";
    case ClientMessageType.ClearChatByAmount:
      return "ClearChatByAmount";
    case ClientMessageType.RoomChatMessage:
      return "RoomChatMessage";
    case ClientMessageType.MidiMessage:
      return "MidiMessage";
    case ClientMessageType.ServerEventInfo:
      return "ServerEventInfo";
    case ClientMessageType.LoadServerCommands:
      return "LoadServerCommands";
    case ClientMessageType.LoadRoomOwnerCommands:
      return "LoadRoomOwnerCommands";
    case ClientMessageType.UnloadRoomOwnerCommands:
      return "UnloadRoomOwnerCommands";
    case ClientMessageType.RoomSettingsUpdated:
      return "RoomSettingsUpdated";
    case ClientMessageType.SessionExpired:
      return "SessionExpired";
    case ClientMessageType.RejoinRecoveredRoom:
      return "RejoinRecoveredRoom";
    case ClientMessageType.GetFriendsList:
      return "GetFriendsList";
    case ClientMessageType.GetPendingFriendRequestsList:
      return "GetPendingFriendRequestsList";
    case ClientMessageType.GetUserNotificationSettings:
      return "GetUserNotificationSettings";
    case ClientMessageType.RoomStoreServiceUp:
      return "RoomStoreServiceUp";
    case ClientMessageType.EditMessageByID:
      return "EditMessageByID";
    case ClientMessageType.Announcements:
      return "Announcements";
    case ClientMessageType.LoadRoomChatHistory:
      return "LoadRoomChatHistory";
    case ClientMessageType.LoadRoomWelcomeMessage:
      return "LoadRoomWelcomeMessage";
    case ClientMessageType.JoinedRoomSuccess:
      return "JoinedRoomSuccess";
    case ClientMessageType.JoinedRoomFail:
      return "JoinedRoomFail";
    case ClientMessageType.UsersInRoomList:
      return "UsersInRoomList";
    case ClientMessageType.UserJoined:
      return "UserJoined";
    case ClientMessageType.UserUpdated:
      return "UserUpdated";
    case ClientMessageType.UserLeft:
      return "UserLeft";
    case ClientMessageType.UsersTyping:
      return "UsersTyping";
    case ClientMessageType.BasicRoomApiCreated:
      return "BasicRoomApiCreated";
    case ClientMessageType.BasicRoomApiUpdated:
      return "BasicRoomApiUpdated";
    case ClientMessageType.BasicRoomApiDeleted:
      return "BasicRoomApiDeleted";
    case ClientMessageType.ClientUpdated:
      return "ClientUpdated";
    case ClientMessageType.Unauthenticated:
      return "Unauthenticated";
    case ClientMessageType.RoomOwnerUpdated:
      return "RoomOwnerUpdated";
    case ClientMessageType.FriendStatusUpdated:
      return "FriendStatusUpdated";
    case ClientMessageType.AddPendingFriendRequest:
      return "AddPendingFriendRequest";
    case ClientMessageType.RemovePendingFriendRequest:
      return "RemovePendingFriendRequest";
    case ClientMessageType.GetKickedUsersList:
      return "GetKickedUsersList";
    case ClientMessageType.GetRoomFullDetails:
      return "GetRoomFullDetails";
    case ClientMessageType.MaintenanceModeActive:
      return "MaintenanceModeActive";
    case ClientMessageType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum CommandResponseType {
  Error = 0,
  ValidationError = 1,
  JoinRoomFail = 3,
  LeftRoom = 4,
  RoomsList = 5,
  KickedUsersInRoomList = 6,
  RoomSettings = 7,
  RegistrationSuccess = 8,
  Toast = 9,
  EnterRoomPassword = 10,
  UNRECOGNIZED = -1,
}

export function commandResponseTypeFromJSON(object: any): CommandResponseType {
  switch (object) {
    case 0:
    case "Error":
      return CommandResponseType.Error;
    case 1:
    case "ValidationError":
      return CommandResponseType.ValidationError;
    case 3:
    case "JoinRoomFail":
      return CommandResponseType.JoinRoomFail;
    case 4:
    case "LeftRoom":
      return CommandResponseType.LeftRoom;
    case 5:
    case "RoomsList":
      return CommandResponseType.RoomsList;
    case 6:
    case "KickedUsersInRoomList":
      return CommandResponseType.KickedUsersInRoomList;
    case 7:
    case "RoomSettings":
      return CommandResponseType.RoomSettings;
    case 8:
    case "RegistrationSuccess":
      return CommandResponseType.RegistrationSuccess;
    case 9:
    case "Toast":
      return CommandResponseType.Toast;
    case 10:
    case "EnterRoomPassword":
      return CommandResponseType.EnterRoomPassword;
    case -1:
    case "UNRECOGNIZED":
    default:
      return CommandResponseType.UNRECOGNIZED;
  }
}

export function commandResponseTypeToJSON(object: CommandResponseType): string {
  switch (object) {
    case CommandResponseType.Error:
      return "Error";
    case CommandResponseType.ValidationError:
      return "ValidationError";
    case CommandResponseType.JoinRoomFail:
      return "JoinRoomFail";
    case CommandResponseType.LeftRoom:
      return "LeftRoom";
    case CommandResponseType.RoomsList:
      return "RoomsList";
    case CommandResponseType.KickedUsersInRoomList:
      return "KickedUsersInRoomList";
    case CommandResponseType.RoomSettings:
      return "RoomSettings";
    case CommandResponseType.RegistrationSuccess:
      return "RegistrationSuccess";
    case CommandResponseType.Toast:
      return "Toast";
    case CommandResponseType.EnterRoomPassword:
      return "EnterRoomPassword";
    case CommandResponseType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum ClientValidationErrorMsgType {
  CreateRoom = 0,
  UpdateRoom = 1,
  Registration = 2,
  Login = 3,
  ForgotPassword = 4,
  ResetPassword = 5,
  ResendEmail = 6,
  UNRECOGNIZED = -1,
}

export function clientValidationErrorMsgTypeFromJSON(object: any): ClientValidationErrorMsgType {
  switch (object) {
    case 0:
    case "CreateRoom":
      return ClientValidationErrorMsgType.CreateRoom;
    case 1:
    case "UpdateRoom":
      return ClientValidationErrorMsgType.UpdateRoom;
    case 2:
    case "Registration":
      return ClientValidationErrorMsgType.Registration;
    case 3:
    case "Login":
      return ClientValidationErrorMsgType.Login;
    case 4:
    case "ForgotPassword":
      return ClientValidationErrorMsgType.ForgotPassword;
    case 5:
    case "ResetPassword":
      return ClientValidationErrorMsgType.ResetPassword;
    case 6:
    case "ResendEmail":
      return ClientValidationErrorMsgType.ResendEmail;
    case -1:
    case "UNRECOGNIZED":
    default:
      return ClientValidationErrorMsgType.UNRECOGNIZED;
  }
}

export function clientValidationErrorMsgTypeToJSON(object: ClientValidationErrorMsgType): string {
  switch (object) {
    case ClientValidationErrorMsgType.CreateRoom:
      return "CreateRoom";
    case ClientValidationErrorMsgType.UpdateRoom:
      return "UpdateRoom";
    case ClientValidationErrorMsgType.Registration:
      return "Registration";
    case ClientValidationErrorMsgType.Login:
      return "Login";
    case ClientValidationErrorMsgType.ForgotPassword:
      return "ForgotPassword";
    case ClientValidationErrorMsgType.ResetPassword:
      return "ResetPassword";
    case ClientValidationErrorMsgType.ResendEmail:
      return "ResendEmail";
    case ClientValidationErrorMsgType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export enum JoinRoomFailType {
  QueuedToJoinRoom = 0,
  AlreadyInRoom = 1,
  RoomFull = 2,
  NoGuests = 3,
  ProMembersOnly = 4,
  NotAllowed = 5,
  MaintenanceMode = 6,
  IncorrectPassword = 7,
  ServerError = 8,
  RoomNotFound = 9,
  EnterPassword = 10,
  UNRECOGNIZED = -1,
}

export function joinRoomFailTypeFromJSON(object: any): JoinRoomFailType {
  switch (object) {
    case 0:
    case "QueuedToJoinRoom":
      return JoinRoomFailType.QueuedToJoinRoom;
    case 1:
    case "AlreadyInRoom":
      return JoinRoomFailType.AlreadyInRoom;
    case 2:
    case "RoomFull":
      return JoinRoomFailType.RoomFull;
    case 3:
    case "NoGuests":
      return JoinRoomFailType.NoGuests;
    case 4:
    case "ProMembersOnly":
      return JoinRoomFailType.ProMembersOnly;
    case 5:
    case "NotAllowed":
      return JoinRoomFailType.NotAllowed;
    case 6:
    case "MaintenanceMode":
      return JoinRoomFailType.MaintenanceMode;
    case 7:
    case "IncorrectPassword":
      return JoinRoomFailType.IncorrectPassword;
    case 8:
    case "ServerError":
      return JoinRoomFailType.ServerError;
    case 9:
    case "RoomNotFound":
      return JoinRoomFailType.RoomNotFound;
    case 10:
    case "EnterPassword":
      return JoinRoomFailType.EnterPassword;
    case -1:
    case "UNRECOGNIZED":
    default:
      return JoinRoomFailType.UNRECOGNIZED;
  }
}

export function joinRoomFailTypeToJSON(object: JoinRoomFailType): string {
  switch (object) {
    case JoinRoomFailType.QueuedToJoinRoom:
      return "QueuedToJoinRoom";
    case JoinRoomFailType.AlreadyInRoom:
      return "AlreadyInRoom";
    case JoinRoomFailType.RoomFull:
      return "RoomFull";
    case JoinRoomFailType.NoGuests:
      return "NoGuests";
    case JoinRoomFailType.ProMembersOnly:
      return "ProMembersOnly";
    case JoinRoomFailType.NotAllowed:
      return "NotAllowed";
    case JoinRoomFailType.MaintenanceMode:
      return "MaintenanceMode";
    case JoinRoomFailType.IncorrectPassword:
      return "IncorrectPassword";
    case JoinRoomFailType.ServerError:
      return "ServerError";
    case JoinRoomFailType.RoomNotFound:
      return "RoomNotFound";
    case JoinRoomFailType.EnterPassword:
      return "EnterPassword";
    case JoinRoomFailType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ClientValidationError {
  FieldName: string;
  Reason: string;
}

export interface MidiDto {
  messageType: MidiDtoType;
  noteSource: MidiNoteSource;
  noteOn?: MidiDto_MidiNoteOn | undefined;
  noteOff?: MidiDto_MidiNoteOff | undefined;
  sustain?: MidiDto_MidiNoteSustain | undefined;
  allSoundOff?: MidiDto_MidiAllSoundOff | undefined;
  pitchBend?: MidiDto_MidiPitchBend | undefined;
}

export interface MidiMessageOutputDto {
  socketID: string;
  time: string;
  data: MidiMessageOutputDto_MidiMessageBuffer[];
}

export interface MidiMessageOutputDto_MidiMessageBuffer {
  data: MidiDto | undefined;
  delay: number;
}

export interface ChatMessageDto {
  id: number;
  messageID: string;
  messageReplyID: string;
  socketID: string;
  message: string;
  ts: string;
  roomID: string;
  usertag: string;
  username: string;
  nickname: string;
  userColor: string;
  isBot: boolean;
  isPlugin: boolean;
  isMod: boolean;
  isSystem: boolean;
  isAdmin: boolean;
  isDev: boolean;
  isRoomOwner: boolean;
  isGuest: boolean;
  isProMember: boolean;
  whisperer: string;
  autoDelete: boolean;
  modifiedDate: string;
  userUuid: string;
}

export interface RoomChatHistory {
  lastMessages: ChatMessageDto[];
}

export interface ChatMessageDtoList {
  messages: ChatMessageDto[];
}

export interface JoinedRoomData {
  roomID: string;
  roomName: string;
  roomType: RoomType;
  roomStatus: RoomStatus;
  roomSettings: RoomSettings | undefined;
  roomOwner: string;
  selfHostedCountryCode?: string | undefined;
}

export interface CommandResponse {
  messageType: CommandResponseType;
  stringValue?: string | undefined;
  roomsList?: CommandResponse_RoomsList | undefined;
  roomDto?: RoomDto | undefined;
  validationErrorList?: CommandResponse_ClientValidationErrorList | undefined;
  joinRoomFailResponse?: CommandResponse_JoinRoomFailResponse | undefined;
  enterRoomPasswordResponse?: CommandResponse_EnterRoomPasswordResponse | undefined;
}

export interface CommandResponse_RoomsList {
  list: BasicRoomDto[];
}

export interface CommandResponse_ClientValidationErrorList {
  data: ClientValidationError[];
  errorType: ClientValidationErrorMsgType;
}

export interface CommandResponse_JoinRoomFailResponse {
  roomID: string;
  reason: JoinRoomFailType;
  roomName: string;
}

export interface CommandResponse_EnterRoomPasswordResponse {
  roomID: string;
  roomName: string;
}

export interface UserDtoList {
  userDto: UserDto[];
}

export interface ClientSideUserDtoList {
  list: ClientSideUserDto[];
}

export interface SocketIdList {
  socketIDs: string[];
}

export interface FriendDtoList {
  friendDto: FriendDto[];
}

export interface PendingFriendRequestList {
  pendingFriendRequest: PendingFriendRequest[];
}

export interface KickedUsersList {
  kickedUsers: KickedUserData[];
}

export interface WelcomeDto {
  userClientDto: UserClientDto | undefined;
  settings?: string | undefined;
}

export interface AvatarMessageDto {
  commandType: AvatarMessageDto_AvatarMessageCommandType;
  socketID: string;
  worldPosition?: AvatarWorldDataDto_AvatarMessageWorldPosition | undefined;
  intValue?: number | undefined;
}

export enum AvatarMessageDto_AvatarMessageCommandType {
  Invalid = 0,
  SetPosition = 1,
  SetPianoBenchTarget = 2,
  UNRECOGNIZED = -1,
}

export function avatarMessageDto_AvatarMessageCommandTypeFromJSON(
  object: any,
): AvatarMessageDto_AvatarMessageCommandType {
  switch (object) {
    case 0:
    case "Invalid":
      return AvatarMessageDto_AvatarMessageCommandType.Invalid;
    case 1:
    case "SetPosition":
      return AvatarMessageDto_AvatarMessageCommandType.SetPosition;
    case 2:
    case "SetPianoBenchTarget":
      return AvatarMessageDto_AvatarMessageCommandType.SetPianoBenchTarget;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AvatarMessageDto_AvatarMessageCommandType.UNRECOGNIZED;
  }
}

export function avatarMessageDto_AvatarMessageCommandTypeToJSON(
  object: AvatarMessageDto_AvatarMessageCommandType,
): string {
  switch (object) {
    case AvatarMessageDto_AvatarMessageCommandType.Invalid:
      return "Invalid";
    case AvatarMessageDto_AvatarMessageCommandType.SetPosition:
      return "SetPosition";
    case AvatarMessageDto_AvatarMessageCommandType.SetPianoBenchTarget:
      return "SetPianoBenchTarget";
    case AvatarMessageDto_AvatarMessageCommandType.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface ClientMessage {
  messageType: ClientMessageType;
  stringValue?: string | undefined;
  intValue?: number | undefined;
  midiMessageOutputDto?: MidiMessageOutputDto | undefined;
  chatMessageDto?: ChatMessageDto | undefined;
  roomChatHistory?: RoomChatHistory | undefined;
  joinedRoomData?: JoinedRoomData | undefined;
  userDto?: UserDto | undefined;
  userClientDto?: UserClientDto | undefined;
  commandResponse?: CommandResponse | undefined;
  userDtoList?: UserDtoList | undefined;
  welcomeDto?: WelcomeDto | undefined;
  basicRoomDto?: BasicRoomDto | undefined;
  userUpdateCommand?: UserUpdateCommand | undefined;
  socketId?: string | undefined;
  usertag?: string | undefined;
  roomSettings?: RoomSettings | undefined;
  friendDtoList?: FriendDtoList | undefined;
  friendDto?: FriendDto | undefined;
  pendingFriendRequestList?: PendingFriendRequestList | undefined;
  pendingFriendRequest?: PendingFriendRequest | undefined;
  userId?: string | undefined;
  socketIDList?: SocketIdList | undefined;
  kickedUsersList?: KickedUsersList | undefined;
  roomFullDetails?: RoomFullDetails | undefined;
  boolValue?: boolean | undefined;
  clientSideUserDtoList?: ClientSideUserDtoList | undefined;
  chatMessageDtoList?: ChatMessageDtoList | undefined;
  avatarMessageDto?: AvatarMessageDto | undefined;
}

function createBaseClientValidationError(): ClientValidationError {
  return { FieldName: "", Reason: "" };
}

export const ClientValidationError = {
  encode(message: ClientValidationError, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.FieldName !== "") {
      writer.uint32(10).string(message.FieldName);
    }
    if (message.Reason !== "") {
      writer.uint32(18).string(message.Reason);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ClientValidationError {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientValidationError();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.FieldName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.Reason = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClientValidationError {
    return {
      FieldName: isSet(object.FieldName) ? globalThis.String(object.FieldName) : "",
      Reason: isSet(object.Reason) ? globalThis.String(object.Reason) : "",
    };
  },

  toJSON(message: ClientValidationError): unknown {
    const obj: any = {};
    if (message.FieldName !== "") {
      obj.FieldName = message.FieldName;
    }
    if (message.Reason !== "") {
      obj.Reason = message.Reason;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClientValidationError>, I>>(base?: I): ClientValidationError {
    return ClientValidationError.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientValidationError>, I>>(object: I): ClientValidationError {
    const message = createBaseClientValidationError();
    message.FieldName = object.FieldName ?? "";
    message.Reason = object.Reason ?? "";
    return message;
  },
};

function createBaseMidiDto(): MidiDto {
  return {
    messageType: 0,
    noteSource: 0,
    noteOn: undefined,
    noteOff: undefined,
    sustain: undefined,
    allSoundOff: undefined,
    pitchBend: undefined,
  };
}

export const MidiDto = {
  encode(message: MidiDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.messageType !== 0) {
      writer.uint32(8).int32(message.messageType);
    }
    if (message.noteSource !== 0) {
      writer.uint32(7992).int32(message.noteSource);
    }
    if (message.noteOn !== undefined) {
      MidiDto_MidiNoteOn.encode(message.noteOn, writer.uint32(18).fork()).ldelim();
    }
    if (message.noteOff !== undefined) {
      MidiDto_MidiNoteOff.encode(message.noteOff, writer.uint32(26).fork()).ldelim();
    }
    if (message.sustain !== undefined) {
      MidiDto_MidiNoteSustain.encode(message.sustain, writer.uint32(34).fork()).ldelim();
    }
    if (message.allSoundOff !== undefined) {
      MidiDto_MidiAllSoundOff.encode(message.allSoundOff, writer.uint32(42).fork()).ldelim();
    }
    if (message.pitchBend !== undefined) {
      MidiDto_MidiPitchBend.encode(message.pitchBend, writer.uint32(50).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.messageType = reader.int32() as any;
          continue;
        case 999:
          if (tag !== 7992) {
            break;
          }

          message.noteSource = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.noteOn = MidiDto_MidiNoteOn.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.noteOff = MidiDto_MidiNoteOff.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.sustain = MidiDto_MidiNoteSustain.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.allSoundOff = MidiDto_MidiAllSoundOff.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.pitchBend = MidiDto_MidiPitchBend.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiDto {
    return {
      messageType: isSet(object.messageType) ? midiDtoTypeFromJSON(object.messageType) : 0,
      noteSource: isSet(object.noteSource) ? midiNoteSourceFromJSON(object.noteSource) : 0,
      noteOn: isSet(object.noteOn) ? MidiDto_MidiNoteOn.fromJSON(object.noteOn) : undefined,
      noteOff: isSet(object.noteOff) ? MidiDto_MidiNoteOff.fromJSON(object.noteOff) : undefined,
      sustain: isSet(object.sustain) ? MidiDto_MidiNoteSustain.fromJSON(object.sustain) : undefined,
      allSoundOff: isSet(object.allSoundOff) ? MidiDto_MidiAllSoundOff.fromJSON(object.allSoundOff) : undefined,
      pitchBend: isSet(object.pitchBend) ? MidiDto_MidiPitchBend.fromJSON(object.pitchBend) : undefined,
    };
  },

  toJSON(message: MidiDto): unknown {
    const obj: any = {};
    if (message.messageType !== 0) {
      obj.messageType = midiDtoTypeToJSON(message.messageType);
    }
    if (message.noteSource !== 0) {
      obj.noteSource = midiNoteSourceToJSON(message.noteSource);
    }
    if (message.noteOn !== undefined) {
      obj.noteOn = MidiDto_MidiNoteOn.toJSON(message.noteOn);
    }
    if (message.noteOff !== undefined) {
      obj.noteOff = MidiDto_MidiNoteOff.toJSON(message.noteOff);
    }
    if (message.sustain !== undefined) {
      obj.sustain = MidiDto_MidiNoteSustain.toJSON(message.sustain);
    }
    if (message.allSoundOff !== undefined) {
      obj.allSoundOff = MidiDto_MidiAllSoundOff.toJSON(message.allSoundOff);
    }
    if (message.pitchBend !== undefined) {
      obj.pitchBend = MidiDto_MidiPitchBend.toJSON(message.pitchBend);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiDto>, I>>(base?: I): MidiDto {
    return MidiDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiDto>, I>>(object: I): MidiDto {
    const message = createBaseMidiDto();
    message.messageType = object.messageType ?? 0;
    message.noteSource = object.noteSource ?? 0;
    message.noteOn = (object.noteOn !== undefined && object.noteOn !== null)
      ? MidiDto_MidiNoteOn.fromPartial(object.noteOn)
      : undefined;
    message.noteOff = (object.noteOff !== undefined && object.noteOff !== null)
      ? MidiDto_MidiNoteOff.fromPartial(object.noteOff)
      : undefined;
    message.sustain = (object.sustain !== undefined && object.sustain !== null)
      ? MidiDto_MidiNoteSustain.fromPartial(object.sustain)
      : undefined;
    message.allSoundOff = (object.allSoundOff !== undefined && object.allSoundOff !== null)
      ? MidiDto_MidiAllSoundOff.fromPartial(object.allSoundOff)
      : undefined;
    message.pitchBend = (object.pitchBend !== undefined && object.pitchBend !== null)
      ? MidiDto_MidiPitchBend.fromPartial(object.pitchBend)
      : undefined;
    return message;
  },
};

function createBaseMidiMessageOutputDto(): MidiMessageOutputDto {
  return { socketID: "", time: "", data: [] };
}

export const MidiMessageOutputDto = {
  encode(message: MidiMessageOutputDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.socketID !== "") {
      writer.uint32(10).string(message.socketID);
    }
    if (message.time !== "") {
      writer.uint32(18).string(message.time);
    }
    for (const v of message.data) {
      MidiMessageOutputDto_MidiMessageBuffer.encode(v!, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageOutputDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageOutputDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.time = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.data.push(MidiMessageOutputDto_MidiMessageBuffer.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageOutputDto {
    return {
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      time: isSet(object.time) ? globalThis.String(object.time) : "",
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => MidiMessageOutputDto_MidiMessageBuffer.fromJSON(e))
        : [],
    };
  },

  toJSON(message: MidiMessageOutputDto): unknown {
    const obj: any = {};
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.time !== "") {
      obj.time = message.time;
    }
    if (message.data?.length) {
      obj.data = message.data.map((e) => MidiMessageOutputDto_MidiMessageBuffer.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageOutputDto>, I>>(base?: I): MidiMessageOutputDto {
    return MidiMessageOutputDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageOutputDto>, I>>(object: I): MidiMessageOutputDto {
    const message = createBaseMidiMessageOutputDto();
    message.socketID = object.socketID ?? "";
    message.time = object.time ?? "";
    message.data = object.data?.map((e) => MidiMessageOutputDto_MidiMessageBuffer.fromPartial(e)) || [];
    return message;
  },
};

function createBaseMidiMessageOutputDto_MidiMessageBuffer(): MidiMessageOutputDto_MidiMessageBuffer {
  return { data: undefined, delay: 0 };
}

export const MidiMessageOutputDto_MidiMessageBuffer = {
  encode(message: MidiMessageOutputDto_MidiMessageBuffer, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.data !== undefined) {
      MidiDto.encode(message.data, writer.uint32(10).fork()).ldelim();
    }
    if (message.delay !== 0) {
      writer.uint32(17).double(message.delay);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): MidiMessageOutputDto_MidiMessageBuffer {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMidiMessageOutputDto_MidiMessageBuffer();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data = MidiDto.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 17) {
            break;
          }

          message.delay = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): MidiMessageOutputDto_MidiMessageBuffer {
    return {
      data: isSet(object.data) ? MidiDto.fromJSON(object.data) : undefined,
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : 0,
    };
  },

  toJSON(message: MidiMessageOutputDto_MidiMessageBuffer): unknown {
    const obj: any = {};
    if (message.data !== undefined) {
      obj.data = MidiDto.toJSON(message.data);
    }
    if (message.delay !== 0) {
      obj.delay = message.delay;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<MidiMessageOutputDto_MidiMessageBuffer>, I>>(
    base?: I,
  ): MidiMessageOutputDto_MidiMessageBuffer {
    return MidiMessageOutputDto_MidiMessageBuffer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MidiMessageOutputDto_MidiMessageBuffer>, I>>(
    object: I,
  ): MidiMessageOutputDto_MidiMessageBuffer {
    const message = createBaseMidiMessageOutputDto_MidiMessageBuffer();
    message.data = (object.data !== undefined && object.data !== null) ? MidiDto.fromPartial(object.data) : undefined;
    message.delay = object.delay ?? 0;
    return message;
  },
};

function createBaseChatMessageDto(): ChatMessageDto {
  return {
    id: 0,
    messageID: "",
    messageReplyID: "",
    socketID: "",
    message: "",
    ts: "",
    roomID: "",
    usertag: "",
    username: "",
    nickname: "",
    userColor: "",
    isBot: false,
    isPlugin: false,
    isMod: false,
    isSystem: false,
    isAdmin: false,
    isDev: false,
    isRoomOwner: false,
    isGuest: false,
    isProMember: false,
    whisperer: "",
    autoDelete: false,
    modifiedDate: "",
    userUuid: "",
  };
}

export const ChatMessageDto = {
  encode(message: ChatMessageDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.id !== 0) {
      writer.uint32(8).int32(message.id);
    }
    if (message.messageID !== "") {
      writer.uint32(18).string(message.messageID);
    }
    if (message.messageReplyID !== "") {
      writer.uint32(26).string(message.messageReplyID);
    }
    if (message.socketID !== "") {
      writer.uint32(34).string(message.socketID);
    }
    if (message.message !== "") {
      writer.uint32(42).string(message.message);
    }
    if (message.ts !== "") {
      writer.uint32(50).string(message.ts);
    }
    if (message.roomID !== "") {
      writer.uint32(58).string(message.roomID);
    }
    if (message.usertag !== "") {
      writer.uint32(66).string(message.usertag);
    }
    if (message.username !== "") {
      writer.uint32(74).string(message.username);
    }
    if (message.nickname !== "") {
      writer.uint32(82).string(message.nickname);
    }
    if (message.userColor !== "") {
      writer.uint32(90).string(message.userColor);
    }
    if (message.isBot !== false) {
      writer.uint32(96).bool(message.isBot);
    }
    if (message.isPlugin !== false) {
      writer.uint32(104).bool(message.isPlugin);
    }
    if (message.isMod !== false) {
      writer.uint32(112).bool(message.isMod);
    }
    if (message.isSystem !== false) {
      writer.uint32(120).bool(message.isSystem);
    }
    if (message.isAdmin !== false) {
      writer.uint32(128).bool(message.isAdmin);
    }
    if (message.isDev !== false) {
      writer.uint32(136).bool(message.isDev);
    }
    if (message.isRoomOwner !== false) {
      writer.uint32(144).bool(message.isRoomOwner);
    }
    if (message.isGuest !== false) {
      writer.uint32(152).bool(message.isGuest);
    }
    if (message.isProMember !== false) {
      writer.uint32(160).bool(message.isProMember);
    }
    if (message.whisperer !== "") {
      writer.uint32(170).string(message.whisperer);
    }
    if (message.autoDelete !== false) {
      writer.uint32(176).bool(message.autoDelete);
    }
    if (message.modifiedDate !== "") {
      writer.uint32(186).string(message.modifiedDate);
    }
    if (message.userUuid !== "") {
      writer.uint32(194).string(message.userUuid);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChatMessageDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatMessageDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.id = reader.int32();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.messageID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.messageReplyID = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.message = reader.string();
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.ts = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.username = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.nickname = reader.string();
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.userColor = reader.string();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }

          message.isBot = reader.bool();
          continue;
        case 13:
          if (tag !== 104) {
            break;
          }

          message.isPlugin = reader.bool();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }

          message.isMod = reader.bool();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }

          message.isSystem = reader.bool();
          continue;
        case 16:
          if (tag !== 128) {
            break;
          }

          message.isAdmin = reader.bool();
          continue;
        case 17:
          if (tag !== 136) {
            break;
          }

          message.isDev = reader.bool();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }

          message.isRoomOwner = reader.bool();
          continue;
        case 19:
          if (tag !== 152) {
            break;
          }

          message.isGuest = reader.bool();
          continue;
        case 20:
          if (tag !== 160) {
            break;
          }

          message.isProMember = reader.bool();
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.whisperer = reader.string();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }

          message.autoDelete = reader.bool();
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }

          message.modifiedDate = reader.string();
          continue;
        case 24:
          if (tag !== 194) {
            break;
          }

          message.userUuid = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatMessageDto {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      messageID: isSet(object.messageID) ? globalThis.String(object.messageID) : "",
      messageReplyID: isSet(object.messageReplyID) ? globalThis.String(object.messageReplyID) : "",
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      message: isSet(object.message) ? globalThis.String(object.message) : "",
      ts: isSet(object.ts) ? globalThis.String(object.ts) : "",
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : "",
      username: isSet(object.username) ? globalThis.String(object.username) : "",
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : "",
      userColor: isSet(object.userColor) ? globalThis.String(object.userColor) : "",
      isBot: isSet(object.isBot) ? globalThis.Boolean(object.isBot) : false,
      isPlugin: isSet(object.isPlugin) ? globalThis.Boolean(object.isPlugin) : false,
      isMod: isSet(object.isMod) ? globalThis.Boolean(object.isMod) : false,
      isSystem: isSet(object.isSystem) ? globalThis.Boolean(object.isSystem) : false,
      isAdmin: isSet(object.isAdmin) ? globalThis.Boolean(object.isAdmin) : false,
      isDev: isSet(object.isDev) ? globalThis.Boolean(object.isDev) : false,
      isRoomOwner: isSet(object.isRoomOwner) ? globalThis.Boolean(object.isRoomOwner) : false,
      isGuest: isSet(object.isGuest) ? globalThis.Boolean(object.isGuest) : false,
      isProMember: isSet(object.isProMember) ? globalThis.Boolean(object.isProMember) : false,
      whisperer: isSet(object.whisperer) ? globalThis.String(object.whisperer) : "",
      autoDelete: isSet(object.autoDelete) ? globalThis.Boolean(object.autoDelete) : false,
      modifiedDate: isSet(object.modifiedDate) ? globalThis.String(object.modifiedDate) : "",
      userUuid: isSet(object.userUuid) ? globalThis.String(object.userUuid) : "",
    };
  },

  toJSON(message: ChatMessageDto): unknown {
    const obj: any = {};
    if (message.id !== 0) {
      obj.id = Math.round(message.id);
    }
    if (message.messageID !== "") {
      obj.messageID = message.messageID;
    }
    if (message.messageReplyID !== "") {
      obj.messageReplyID = message.messageReplyID;
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.message !== "") {
      obj.message = message.message;
    }
    if (message.ts !== "") {
      obj.ts = message.ts;
    }
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.usertag !== "") {
      obj.usertag = message.usertag;
    }
    if (message.username !== "") {
      obj.username = message.username;
    }
    if (message.nickname !== "") {
      obj.nickname = message.nickname;
    }
    if (message.userColor !== "") {
      obj.userColor = message.userColor;
    }
    if (message.isBot !== false) {
      obj.isBot = message.isBot;
    }
    if (message.isPlugin !== false) {
      obj.isPlugin = message.isPlugin;
    }
    if (message.isMod !== false) {
      obj.isMod = message.isMod;
    }
    if (message.isSystem !== false) {
      obj.isSystem = message.isSystem;
    }
    if (message.isAdmin !== false) {
      obj.isAdmin = message.isAdmin;
    }
    if (message.isDev !== false) {
      obj.isDev = message.isDev;
    }
    if (message.isRoomOwner !== false) {
      obj.isRoomOwner = message.isRoomOwner;
    }
    if (message.isGuest !== false) {
      obj.isGuest = message.isGuest;
    }
    if (message.isProMember !== false) {
      obj.isProMember = message.isProMember;
    }
    if (message.whisperer !== "") {
      obj.whisperer = message.whisperer;
    }
    if (message.autoDelete !== false) {
      obj.autoDelete = message.autoDelete;
    }
    if (message.modifiedDate !== "") {
      obj.modifiedDate = message.modifiedDate;
    }
    if (message.userUuid !== "") {
      obj.userUuid = message.userUuid;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChatMessageDto>, I>>(base?: I): ChatMessageDto {
    return ChatMessageDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatMessageDto>, I>>(object: I): ChatMessageDto {
    const message = createBaseChatMessageDto();
    message.id = object.id ?? 0;
    message.messageID = object.messageID ?? "";
    message.messageReplyID = object.messageReplyID ?? "";
    message.socketID = object.socketID ?? "";
    message.message = object.message ?? "";
    message.ts = object.ts ?? "";
    message.roomID = object.roomID ?? "";
    message.usertag = object.usertag ?? "";
    message.username = object.username ?? "";
    message.nickname = object.nickname ?? "";
    message.userColor = object.userColor ?? "";
    message.isBot = object.isBot ?? false;
    message.isPlugin = object.isPlugin ?? false;
    message.isMod = object.isMod ?? false;
    message.isSystem = object.isSystem ?? false;
    message.isAdmin = object.isAdmin ?? false;
    message.isDev = object.isDev ?? false;
    message.isRoomOwner = object.isRoomOwner ?? false;
    message.isGuest = object.isGuest ?? false;
    message.isProMember = object.isProMember ?? false;
    message.whisperer = object.whisperer ?? "";
    message.autoDelete = object.autoDelete ?? false;
    message.modifiedDate = object.modifiedDate ?? "";
    message.userUuid = object.userUuid ?? "";
    return message;
  },
};

function createBaseRoomChatHistory(): RoomChatHistory {
  return { lastMessages: [] };
}

export const RoomChatHistory = {
  encode(message: RoomChatHistory, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.lastMessages) {
      ChatMessageDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): RoomChatHistory {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRoomChatHistory();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.lastMessages.push(ChatMessageDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): RoomChatHistory {
    return {
      lastMessages: globalThis.Array.isArray(object?.lastMessages)
        ? object.lastMessages.map((e: any) => ChatMessageDto.fromJSON(e))
        : [],
    };
  },

  toJSON(message: RoomChatHistory): unknown {
    const obj: any = {};
    if (message.lastMessages?.length) {
      obj.lastMessages = message.lastMessages.map((e) => ChatMessageDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<RoomChatHistory>, I>>(base?: I): RoomChatHistory {
    return RoomChatHistory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomChatHistory>, I>>(object: I): RoomChatHistory {
    const message = createBaseRoomChatHistory();
    message.lastMessages = object.lastMessages?.map((e) => ChatMessageDto.fromPartial(e)) || [];
    return message;
  },
};

function createBaseChatMessageDtoList(): ChatMessageDtoList {
  return { messages: [] };
}

export const ChatMessageDtoList = {
  encode(message: ChatMessageDtoList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.messages) {
      ChatMessageDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChatMessageDtoList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChatMessageDtoList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.messages.push(ChatMessageDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChatMessageDtoList {
    return {
      messages: globalThis.Array.isArray(object?.messages)
        ? object.messages.map((e: any) => ChatMessageDto.fromJSON(e))
        : [],
    };
  },

  toJSON(message: ChatMessageDtoList): unknown {
    const obj: any = {};
    if (message.messages?.length) {
      obj.messages = message.messages.map((e) => ChatMessageDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChatMessageDtoList>, I>>(base?: I): ChatMessageDtoList {
    return ChatMessageDtoList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatMessageDtoList>, I>>(object: I): ChatMessageDtoList {
    const message = createBaseChatMessageDtoList();
    message.messages = object.messages?.map((e) => ChatMessageDto.fromPartial(e)) || [];
    return message;
  },
};

function createBaseJoinedRoomData(): JoinedRoomData {
  return {
    roomID: "",
    roomName: "",
    roomType: 0,
    roomStatus: 0,
    roomSettings: undefined,
    roomOwner: "",
    selfHostedCountryCode: undefined,
  };
}

export const JoinedRoomData = {
  encode(message: JoinedRoomData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomID !== "") {
      writer.uint32(10).string(message.roomID);
    }
    if (message.roomName !== "") {
      writer.uint32(18).string(message.roomName);
    }
    if (message.roomType !== 0) {
      writer.uint32(24).int32(message.roomType);
    }
    if (message.roomStatus !== 0) {
      writer.uint32(32).int32(message.roomStatus);
    }
    if (message.roomSettings !== undefined) {
      RoomSettings.encode(message.roomSettings, writer.uint32(42).fork()).ldelim();
    }
    if (message.roomOwner !== "") {
      writer.uint32(50).string(message.roomOwner);
    }
    if (message.selfHostedCountryCode !== undefined) {
      writer.uint32(58).string(message.selfHostedCountryCode);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): JoinedRoomData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseJoinedRoomData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomName = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.roomType = reader.int32() as any;
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.roomStatus = reader.int32() as any;
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.roomSettings = RoomSettings.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.roomOwner = reader.string();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.selfHostedCountryCode = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): JoinedRoomData {
    return {
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
      roomType: isSet(object.roomType) ? roomTypeFromJSON(object.roomType) : 0,
      roomStatus: isSet(object.roomStatus) ? roomStatusFromJSON(object.roomStatus) : 0,
      roomSettings: isSet(object.roomSettings) ? RoomSettings.fromJSON(object.roomSettings) : undefined,
      roomOwner: isSet(object.roomOwner) ? globalThis.String(object.roomOwner) : "",
      selfHostedCountryCode: isSet(object.selfHostedCountryCode)
        ? globalThis.String(object.selfHostedCountryCode)
        : undefined,
    };
  },

  toJSON(message: JoinedRoomData): unknown {
    const obj: any = {};
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    if (message.roomType !== 0) {
      obj.roomType = roomTypeToJSON(message.roomType);
    }
    if (message.roomStatus !== 0) {
      obj.roomStatus = roomStatusToJSON(message.roomStatus);
    }
    if (message.roomSettings !== undefined) {
      obj.roomSettings = RoomSettings.toJSON(message.roomSettings);
    }
    if (message.roomOwner !== "") {
      obj.roomOwner = message.roomOwner;
    }
    if (message.selfHostedCountryCode !== undefined) {
      obj.selfHostedCountryCode = message.selfHostedCountryCode;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<JoinedRoomData>, I>>(base?: I): JoinedRoomData {
    return JoinedRoomData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JoinedRoomData>, I>>(object: I): JoinedRoomData {
    const message = createBaseJoinedRoomData();
    message.roomID = object.roomID ?? "";
    message.roomName = object.roomName ?? "";
    message.roomType = object.roomType ?? 0;
    message.roomStatus = object.roomStatus ?? 0;
    message.roomSettings = (object.roomSettings !== undefined && object.roomSettings !== null)
      ? RoomSettings.fromPartial(object.roomSettings)
      : undefined;
    message.roomOwner = object.roomOwner ?? "";
    message.selfHostedCountryCode = object.selfHostedCountryCode ?? undefined;
    return message;
  },
};

function createBaseCommandResponse(): CommandResponse {
  return {
    messageType: 0,
    stringValue: undefined,
    roomsList: undefined,
    roomDto: undefined,
    validationErrorList: undefined,
    joinRoomFailResponse: undefined,
    enterRoomPasswordResponse: undefined,
  };
}

export const CommandResponse = {
  encode(message: CommandResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.messageType !== 0) {
      writer.uint32(8).int32(message.messageType);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.roomsList !== undefined) {
      CommandResponse_RoomsList.encode(message.roomsList, writer.uint32(26).fork()).ldelim();
    }
    if (message.roomDto !== undefined) {
      RoomDto.encode(message.roomDto, writer.uint32(34).fork()).ldelim();
    }
    if (message.validationErrorList !== undefined) {
      CommandResponse_ClientValidationErrorList.encode(message.validationErrorList, writer.uint32(42).fork()).ldelim();
    }
    if (message.joinRoomFailResponse !== undefined) {
      CommandResponse_JoinRoomFailResponse.encode(message.joinRoomFailResponse, writer.uint32(50).fork()).ldelim();
    }
    if (message.enterRoomPasswordResponse !== undefined) {
      CommandResponse_EnterRoomPasswordResponse.encode(message.enterRoomPasswordResponse, writer.uint32(58).fork())
        .ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CommandResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.messageType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomsList = CommandResponse_RoomsList.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.roomDto = RoomDto.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.validationErrorList = CommandResponse_ClientValidationErrorList.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.enterRoomPasswordResponse = CommandResponse_EnterRoomPasswordResponse.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandResponse {
    return {
      messageType: isSet(object.messageType) ? commandResponseTypeFromJSON(object.messageType) : 0,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      roomsList: isSet(object.roomsList) ? CommandResponse_RoomsList.fromJSON(object.roomsList) : undefined,
      roomDto: isSet(object.roomDto) ? RoomDto.fromJSON(object.roomDto) : undefined,
      validationErrorList: isSet(object.validationErrorList)
        ? CommandResponse_ClientValidationErrorList.fromJSON(object.validationErrorList)
        : undefined,
      joinRoomFailResponse: isSet(object.joinRoomFailResponse)
        ? CommandResponse_JoinRoomFailResponse.fromJSON(object.joinRoomFailResponse)
        : undefined,
      enterRoomPasswordResponse: isSet(object.enterRoomPasswordResponse)
        ? CommandResponse_EnterRoomPasswordResponse.fromJSON(object.enterRoomPasswordResponse)
        : undefined,
    };
  },

  toJSON(message: CommandResponse): unknown {
    const obj: any = {};
    if (message.messageType !== 0) {
      obj.messageType = commandResponseTypeToJSON(message.messageType);
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.roomsList !== undefined) {
      obj.roomsList = CommandResponse_RoomsList.toJSON(message.roomsList);
    }
    if (message.roomDto !== undefined) {
      obj.roomDto = RoomDto.toJSON(message.roomDto);
    }
    if (message.validationErrorList !== undefined) {
      obj.validationErrorList = CommandResponse_ClientValidationErrorList.toJSON(message.validationErrorList);
    }
    if (message.joinRoomFailResponse !== undefined) {
      obj.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.toJSON(message.joinRoomFailResponse);
    }
    if (message.enterRoomPasswordResponse !== undefined) {
      obj.enterRoomPasswordResponse = CommandResponse_EnterRoomPasswordResponse.toJSON(
        message.enterRoomPasswordResponse,
      );
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandResponse>, I>>(base?: I): CommandResponse {
    return CommandResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandResponse>, I>>(object: I): CommandResponse {
    const message = createBaseCommandResponse();
    message.messageType = object.messageType ?? 0;
    message.stringValue = object.stringValue ?? undefined;
    message.roomsList = (object.roomsList !== undefined && object.roomsList !== null)
      ? CommandResponse_RoomsList.fromPartial(object.roomsList)
      : undefined;
    message.roomDto = (object.roomDto !== undefined && object.roomDto !== null)
      ? RoomDto.fromPartial(object.roomDto)
      : undefined;
    message.validationErrorList = (object.validationErrorList !== undefined && object.validationErrorList !== null)
      ? CommandResponse_ClientValidationErrorList.fromPartial(object.validationErrorList)
      : undefined;
    message.joinRoomFailResponse = (object.joinRoomFailResponse !== undefined && object.joinRoomFailResponse !== null)
      ? CommandResponse_JoinRoomFailResponse.fromPartial(object.joinRoomFailResponse)
      : undefined;
    message.enterRoomPasswordResponse =
      (object.enterRoomPasswordResponse !== undefined && object.enterRoomPasswordResponse !== null)
        ? CommandResponse_EnterRoomPasswordResponse.fromPartial(object.enterRoomPasswordResponse)
        : undefined;
    return message;
  },
};

function createBaseCommandResponse_RoomsList(): CommandResponse_RoomsList {
  return { list: [] };
}

export const CommandResponse_RoomsList = {
  encode(message: CommandResponse_RoomsList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.list) {
      BasicRoomDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CommandResponse_RoomsList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandResponse_RoomsList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.list.push(BasicRoomDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandResponse_RoomsList {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => BasicRoomDto.fromJSON(e)) : [],
    };
  },

  toJSON(message: CommandResponse_RoomsList): unknown {
    const obj: any = {};
    if (message.list?.length) {
      obj.list = message.list.map((e) => BasicRoomDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandResponse_RoomsList>, I>>(base?: I): CommandResponse_RoomsList {
    return CommandResponse_RoomsList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandResponse_RoomsList>, I>>(object: I): CommandResponse_RoomsList {
    const message = createBaseCommandResponse_RoomsList();
    message.list = object.list?.map((e) => BasicRoomDto.fromPartial(e)) || [];
    return message;
  },
};

function createBaseCommandResponse_ClientValidationErrorList(): CommandResponse_ClientValidationErrorList {
  return { data: [], errorType: 0 };
}

export const CommandResponse_ClientValidationErrorList = {
  encode(message: CommandResponse_ClientValidationErrorList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.data) {
      ClientValidationError.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    if (message.errorType !== 0) {
      writer.uint32(16).int32(message.errorType);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CommandResponse_ClientValidationErrorList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandResponse_ClientValidationErrorList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.data.push(ClientValidationError.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.errorType = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandResponse_ClientValidationErrorList {
    return {
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => ClientValidationError.fromJSON(e))
        : [],
      errorType: isSet(object.errorType) ? clientValidationErrorMsgTypeFromJSON(object.errorType) : 0,
    };
  },

  toJSON(message: CommandResponse_ClientValidationErrorList): unknown {
    const obj: any = {};
    if (message.data?.length) {
      obj.data = message.data.map((e) => ClientValidationError.toJSON(e));
    }
    if (message.errorType !== 0) {
      obj.errorType = clientValidationErrorMsgTypeToJSON(message.errorType);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandResponse_ClientValidationErrorList>, I>>(
    base?: I,
  ): CommandResponse_ClientValidationErrorList {
    return CommandResponse_ClientValidationErrorList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandResponse_ClientValidationErrorList>, I>>(
    object: I,
  ): CommandResponse_ClientValidationErrorList {
    const message = createBaseCommandResponse_ClientValidationErrorList();
    message.data = object.data?.map((e) => ClientValidationError.fromPartial(e)) || [];
    message.errorType = object.errorType ?? 0;
    return message;
  },
};

function createBaseCommandResponse_JoinRoomFailResponse(): CommandResponse_JoinRoomFailResponse {
  return { roomID: "", reason: 0, roomName: "" };
}

export const CommandResponse_JoinRoomFailResponse = {
  encode(message: CommandResponse_JoinRoomFailResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomID !== "") {
      writer.uint32(10).string(message.roomID);
    }
    if (message.reason !== 0) {
      writer.uint32(16).int32(message.reason);
    }
    if (message.roomName !== "") {
      writer.uint32(26).string(message.roomName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CommandResponse_JoinRoomFailResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandResponse_JoinRoomFailResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.reason = reader.int32() as any;
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.roomName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandResponse_JoinRoomFailResponse {
    return {
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      reason: isSet(object.reason) ? joinRoomFailTypeFromJSON(object.reason) : 0,
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
    };
  },

  toJSON(message: CommandResponse_JoinRoomFailResponse): unknown {
    const obj: any = {};
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.reason !== 0) {
      obj.reason = joinRoomFailTypeToJSON(message.reason);
    }
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandResponse_JoinRoomFailResponse>, I>>(
    base?: I,
  ): CommandResponse_JoinRoomFailResponse {
    return CommandResponse_JoinRoomFailResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandResponse_JoinRoomFailResponse>, I>>(
    object: I,
  ): CommandResponse_JoinRoomFailResponse {
    const message = createBaseCommandResponse_JoinRoomFailResponse();
    message.roomID = object.roomID ?? "";
    message.reason = object.reason ?? 0;
    message.roomName = object.roomName ?? "";
    return message;
  },
};

function createBaseCommandResponse_EnterRoomPasswordResponse(): CommandResponse_EnterRoomPasswordResponse {
  return { roomID: "", roomName: "" };
}

export const CommandResponse_EnterRoomPasswordResponse = {
  encode(message: CommandResponse_EnterRoomPasswordResponse, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.roomID !== "") {
      writer.uint32(10).string(message.roomID);
    }
    if (message.roomName !== "") {
      writer.uint32(18).string(message.roomName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): CommandResponse_EnterRoomPasswordResponse {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseCommandResponse_EnterRoomPasswordResponse();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.roomID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.roomName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): CommandResponse_EnterRoomPasswordResponse {
    return {
      roomID: isSet(object.roomID) ? globalThis.String(object.roomID) : "",
      roomName: isSet(object.roomName) ? globalThis.String(object.roomName) : "",
    };
  },

  toJSON(message: CommandResponse_EnterRoomPasswordResponse): unknown {
    const obj: any = {};
    if (message.roomID !== "") {
      obj.roomID = message.roomID;
    }
    if (message.roomName !== "") {
      obj.roomName = message.roomName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<CommandResponse_EnterRoomPasswordResponse>, I>>(
    base?: I,
  ): CommandResponse_EnterRoomPasswordResponse {
    return CommandResponse_EnterRoomPasswordResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommandResponse_EnterRoomPasswordResponse>, I>>(
    object: I,
  ): CommandResponse_EnterRoomPasswordResponse {
    const message = createBaseCommandResponse_EnterRoomPasswordResponse();
    message.roomID = object.roomID ?? "";
    message.roomName = object.roomName ?? "";
    return message;
  },
};

function createBaseUserDtoList(): UserDtoList {
  return { userDto: [] };
}

export const UserDtoList = {
  encode(message: UserDtoList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.userDto) {
      UserDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): UserDtoList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseUserDtoList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userDto.push(UserDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): UserDtoList {
    return {
      userDto: globalThis.Array.isArray(object?.userDto) ? object.userDto.map((e: any) => UserDto.fromJSON(e)) : [],
    };
  },

  toJSON(message: UserDtoList): unknown {
    const obj: any = {};
    if (message.userDto?.length) {
      obj.userDto = message.userDto.map((e) => UserDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<UserDtoList>, I>>(base?: I): UserDtoList {
    return UserDtoList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserDtoList>, I>>(object: I): UserDtoList {
    const message = createBaseUserDtoList();
    message.userDto = object.userDto?.map((e) => UserDto.fromPartial(e)) || [];
    return message;
  },
};

function createBaseClientSideUserDtoList(): ClientSideUserDtoList {
  return { list: [] };
}

export const ClientSideUserDtoList = {
  encode(message: ClientSideUserDtoList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.list) {
      ClientSideUserDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ClientSideUserDtoList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientSideUserDtoList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.list.push(ClientSideUserDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClientSideUserDtoList {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ClientSideUserDto.fromJSON(e)) : [],
    };
  },

  toJSON(message: ClientSideUserDtoList): unknown {
    const obj: any = {};
    if (message.list?.length) {
      obj.list = message.list.map((e) => ClientSideUserDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClientSideUserDtoList>, I>>(base?: I): ClientSideUserDtoList {
    return ClientSideUserDtoList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientSideUserDtoList>, I>>(object: I): ClientSideUserDtoList {
    const message = createBaseClientSideUserDtoList();
    message.list = object.list?.map((e) => ClientSideUserDto.fromPartial(e)) || [];
    return message;
  },
};

function createBaseSocketIdList(): SocketIdList {
  return { socketIDs: [] };
}

export const SocketIdList = {
  encode(message: SocketIdList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.socketIDs) {
      writer.uint32(10).string(v!);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SocketIdList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSocketIdList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.socketIDs.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SocketIdList {
    return {
      socketIDs: globalThis.Array.isArray(object?.socketIDs)
        ? object.socketIDs.map((e: any) => globalThis.String(e))
        : [],
    };
  },

  toJSON(message: SocketIdList): unknown {
    const obj: any = {};
    if (message.socketIDs?.length) {
      obj.socketIDs = message.socketIDs;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SocketIdList>, I>>(base?: I): SocketIdList {
    return SocketIdList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SocketIdList>, I>>(object: I): SocketIdList {
    const message = createBaseSocketIdList();
    message.socketIDs = object.socketIDs?.map((e) => e) || [];
    return message;
  },
};

function createBaseFriendDtoList(): FriendDtoList {
  return { friendDto: [] };
}

export const FriendDtoList = {
  encode(message: FriendDtoList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.friendDto) {
      FriendDto.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): FriendDtoList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseFriendDtoList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.friendDto.push(FriendDto.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): FriendDtoList {
    return {
      friendDto: globalThis.Array.isArray(object?.friendDto)
        ? object.friendDto.map((e: any) => FriendDto.fromJSON(e))
        : [],
    };
  },

  toJSON(message: FriendDtoList): unknown {
    const obj: any = {};
    if (message.friendDto?.length) {
      obj.friendDto = message.friendDto.map((e) => FriendDto.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<FriendDtoList>, I>>(base?: I): FriendDtoList {
    return FriendDtoList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FriendDtoList>, I>>(object: I): FriendDtoList {
    const message = createBaseFriendDtoList();
    message.friendDto = object.friendDto?.map((e) => FriendDto.fromPartial(e)) || [];
    return message;
  },
};

function createBasePendingFriendRequestList(): PendingFriendRequestList {
  return { pendingFriendRequest: [] };
}

export const PendingFriendRequestList = {
  encode(message: PendingFriendRequestList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.pendingFriendRequest) {
      PendingFriendRequest.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): PendingFriendRequestList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePendingFriendRequestList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.pendingFriendRequest.push(PendingFriendRequest.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): PendingFriendRequestList {
    return {
      pendingFriendRequest: globalThis.Array.isArray(object?.pendingFriendRequest)
        ? object.pendingFriendRequest.map((e: any) => PendingFriendRequest.fromJSON(e))
        : [],
    };
  },

  toJSON(message: PendingFriendRequestList): unknown {
    const obj: any = {};
    if (message.pendingFriendRequest?.length) {
      obj.pendingFriendRequest = message.pendingFriendRequest.map((e) => PendingFriendRequest.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<PendingFriendRequestList>, I>>(base?: I): PendingFriendRequestList {
    return PendingFriendRequestList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PendingFriendRequestList>, I>>(object: I): PendingFriendRequestList {
    const message = createBasePendingFriendRequestList();
    message.pendingFriendRequest = object.pendingFriendRequest?.map((e) => PendingFriendRequest.fromPartial(e)) || [];
    return message;
  },
};

function createBaseKickedUsersList(): KickedUsersList {
  return { kickedUsers: [] };
}

export const KickedUsersList = {
  encode(message: KickedUsersList, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    for (const v of message.kickedUsers) {
      KickedUserData.encode(v!, writer.uint32(10).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): KickedUsersList {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseKickedUsersList();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.kickedUsers.push(KickedUserData.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): KickedUsersList {
    return {
      kickedUsers: globalThis.Array.isArray(object?.kickedUsers)
        ? object.kickedUsers.map((e: any) => KickedUserData.fromJSON(e))
        : [],
    };
  },

  toJSON(message: KickedUsersList): unknown {
    const obj: any = {};
    if (message.kickedUsers?.length) {
      obj.kickedUsers = message.kickedUsers.map((e) => KickedUserData.toJSON(e));
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<KickedUsersList>, I>>(base?: I): KickedUsersList {
    return KickedUsersList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickedUsersList>, I>>(object: I): KickedUsersList {
    const message = createBaseKickedUsersList();
    message.kickedUsers = object.kickedUsers?.map((e) => KickedUserData.fromPartial(e)) || [];
    return message;
  },
};

function createBaseWelcomeDto(): WelcomeDto {
  return { userClientDto: undefined, settings: undefined };
}

export const WelcomeDto = {
  encode(message: WelcomeDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.userClientDto !== undefined) {
      UserClientDto.encode(message.userClientDto, writer.uint32(10).fork()).ldelim();
    }
    if (message.settings !== undefined) {
      writer.uint32(18).string(message.settings);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): WelcomeDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseWelcomeDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.userClientDto = UserClientDto.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.settings = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): WelcomeDto {
    return {
      userClientDto: isSet(object.userClientDto) ? UserClientDto.fromJSON(object.userClientDto) : undefined,
      settings: isSet(object.settings) ? globalThis.String(object.settings) : undefined,
    };
  },

  toJSON(message: WelcomeDto): unknown {
    const obj: any = {};
    if (message.userClientDto !== undefined) {
      obj.userClientDto = UserClientDto.toJSON(message.userClientDto);
    }
    if (message.settings !== undefined) {
      obj.settings = message.settings;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<WelcomeDto>, I>>(base?: I): WelcomeDto {
    return WelcomeDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WelcomeDto>, I>>(object: I): WelcomeDto {
    const message = createBaseWelcomeDto();
    message.userClientDto = (object.userClientDto !== undefined && object.userClientDto !== null)
      ? UserClientDto.fromPartial(object.userClientDto)
      : undefined;
    message.settings = object.settings ?? undefined;
    return message;
  },
};

function createBaseAvatarMessageDto(): AvatarMessageDto {
  return { commandType: 0, socketID: "", worldPosition: undefined, intValue: undefined };
}

export const AvatarMessageDto = {
  encode(message: AvatarMessageDto, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.commandType !== 0) {
      writer.uint32(8).int32(message.commandType);
    }
    if (message.socketID !== "") {
      writer.uint32(18).string(message.socketID);
    }
    if (message.worldPosition !== undefined) {
      AvatarWorldDataDto_AvatarMessageWorldPosition.encode(message.worldPosition, writer.uint32(26).fork()).ldelim();
    }
    if (message.intValue !== undefined) {
      writer.uint32(32).int32(message.intValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AvatarMessageDto {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAvatarMessageDto();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.commandType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.socketID = reader.string();
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }

          message.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.intValue = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AvatarMessageDto {
    return {
      commandType: isSet(object.commandType)
        ? avatarMessageDto_AvatarMessageCommandTypeFromJSON(object.commandType)
        : 0,
      socketID: isSet(object.socketID) ? globalThis.String(object.socketID) : "",
      worldPosition: isSet(object.worldPosition)
        ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromJSON(object.worldPosition)
        : undefined,
      intValue: isSet(object.intValue) ? globalThis.Number(object.intValue) : undefined,
    };
  },

  toJSON(message: AvatarMessageDto): unknown {
    const obj: any = {};
    if (message.commandType !== 0) {
      obj.commandType = avatarMessageDto_AvatarMessageCommandTypeToJSON(message.commandType);
    }
    if (message.socketID !== "") {
      obj.socketID = message.socketID;
    }
    if (message.worldPosition !== undefined) {
      obj.worldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.toJSON(message.worldPosition);
    }
    if (message.intValue !== undefined) {
      obj.intValue = Math.round(message.intValue);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AvatarMessageDto>, I>>(base?: I): AvatarMessageDto {
    return AvatarMessageDto.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvatarMessageDto>, I>>(object: I): AvatarMessageDto {
    const message = createBaseAvatarMessageDto();
    message.commandType = object.commandType ?? 0;
    message.socketID = object.socketID ?? "";
    message.worldPosition = (object.worldPosition !== undefined && object.worldPosition !== null)
      ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(object.worldPosition)
      : undefined;
    message.intValue = object.intValue ?? undefined;
    return message;
  },
};

function createBaseClientMessage(): ClientMessage {
  return {
    messageType: 0,
    stringValue: undefined,
    intValue: undefined,
    midiMessageOutputDto: undefined,
    chatMessageDto: undefined,
    roomChatHistory: undefined,
    joinedRoomData: undefined,
    userDto: undefined,
    userClientDto: undefined,
    commandResponse: undefined,
    userDtoList: undefined,
    welcomeDto: undefined,
    basicRoomDto: undefined,
    userUpdateCommand: undefined,
    socketId: undefined,
    usertag: undefined,
    roomSettings: undefined,
    friendDtoList: undefined,
    friendDto: undefined,
    pendingFriendRequestList: undefined,
    pendingFriendRequest: undefined,
    userId: undefined,
    socketIDList: undefined,
    kickedUsersList: undefined,
    roomFullDetails: undefined,
    boolValue: undefined,
    clientSideUserDtoList: undefined,
    chatMessageDtoList: undefined,
    avatarMessageDto: undefined,
  };
}

export const ClientMessage = {
  encode(message: ClientMessage, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.messageType !== 0) {
      writer.uint32(8).int32(message.messageType);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.intValue !== undefined) {
      writer.uint32(24).int32(message.intValue);
    }
    if (message.midiMessageOutputDto !== undefined) {
      MidiMessageOutputDto.encode(message.midiMessageOutputDto, writer.uint32(34).fork()).ldelim();
    }
    if (message.chatMessageDto !== undefined) {
      ChatMessageDto.encode(message.chatMessageDto, writer.uint32(42).fork()).ldelim();
    }
    if (message.roomChatHistory !== undefined) {
      RoomChatHistory.encode(message.roomChatHistory, writer.uint32(50).fork()).ldelim();
    }
    if (message.joinedRoomData !== undefined) {
      JoinedRoomData.encode(message.joinedRoomData, writer.uint32(58).fork()).ldelim();
    }
    if (message.userDto !== undefined) {
      UserDto.encode(message.userDto, writer.uint32(66).fork()).ldelim();
    }
    if (message.userClientDto !== undefined) {
      UserClientDto.encode(message.userClientDto, writer.uint32(74).fork()).ldelim();
    }
    if (message.commandResponse !== undefined) {
      CommandResponse.encode(message.commandResponse, writer.uint32(82).fork()).ldelim();
    }
    if (message.userDtoList !== undefined) {
      UserDtoList.encode(message.userDtoList, writer.uint32(90).fork()).ldelim();
    }
    if (message.welcomeDto !== undefined) {
      WelcomeDto.encode(message.welcomeDto, writer.uint32(98).fork()).ldelim();
    }
    if (message.basicRoomDto !== undefined) {
      BasicRoomDto.encode(message.basicRoomDto, writer.uint32(106).fork()).ldelim();
    }
    if (message.userUpdateCommand !== undefined) {
      UserUpdateCommand.encode(message.userUpdateCommand, writer.uint32(114).fork()).ldelim();
    }
    if (message.socketId !== undefined) {
      writer.uint32(122).string(message.socketId);
    }
    if (message.usertag !== undefined) {
      writer.uint32(130).string(message.usertag);
    }
    if (message.roomSettings !== undefined) {
      RoomSettings.encode(message.roomSettings, writer.uint32(138).fork()).ldelim();
    }
    if (message.friendDtoList !== undefined) {
      FriendDtoList.encode(message.friendDtoList, writer.uint32(146).fork()).ldelim();
    }
    if (message.friendDto !== undefined) {
      FriendDto.encode(message.friendDto, writer.uint32(154).fork()).ldelim();
    }
    if (message.pendingFriendRequestList !== undefined) {
      PendingFriendRequestList.encode(message.pendingFriendRequestList, writer.uint32(162).fork()).ldelim();
    }
    if (message.pendingFriendRequest !== undefined) {
      PendingFriendRequest.encode(message.pendingFriendRequest, writer.uint32(170).fork()).ldelim();
    }
    if (message.userId !== undefined) {
      writer.uint32(178).string(message.userId);
    }
    if (message.socketIDList !== undefined) {
      SocketIdList.encode(message.socketIDList, writer.uint32(186).fork()).ldelim();
    }
    if (message.kickedUsersList !== undefined) {
      KickedUsersList.encode(message.kickedUsersList, writer.uint32(194).fork()).ldelim();
    }
    if (message.roomFullDetails !== undefined) {
      RoomFullDetails.encode(message.roomFullDetails, writer.uint32(202).fork()).ldelim();
    }
    if (message.boolValue !== undefined) {
      writer.uint32(208).bool(message.boolValue);
    }
    if (message.clientSideUserDtoList !== undefined) {
      ClientSideUserDtoList.encode(message.clientSideUserDtoList, writer.uint32(218).fork()).ldelim();
    }
    if (message.chatMessageDtoList !== undefined) {
      ChatMessageDtoList.encode(message.chatMessageDtoList, writer.uint32(226).fork()).ldelim();
    }
    if (message.avatarMessageDto !== undefined) {
      AvatarMessageDto.encode(message.avatarMessageDto, writer.uint32(234).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ClientMessage {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseClientMessage();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.messageType = reader.int32() as any;
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.intValue = reader.int32();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.midiMessageOutputDto = MidiMessageOutputDto.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.chatMessageDto = ChatMessageDto.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.roomChatHistory = RoomChatHistory.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.joinedRoomData = JoinedRoomData.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.userDto = UserDto.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.userClientDto = UserClientDto.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.commandResponse = CommandResponse.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.userDtoList = UserDtoList.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.welcomeDto = WelcomeDto.decode(reader, reader.uint32());
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.basicRoomDto = BasicRoomDto.decode(reader, reader.uint32());
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.userUpdateCommand = UserUpdateCommand.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.roomSettings = RoomSettings.decode(reader, reader.uint32());
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.friendDtoList = FriendDtoList.decode(reader, reader.uint32());
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.friendDto = FriendDto.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.pendingFriendRequestList = PendingFriendRequestList.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.pendingFriendRequest = PendingFriendRequest.decode(reader, reader.uint32());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.userId = reader.string();
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }

          message.socketIDList = SocketIdList.decode(reader, reader.uint32());
          continue;
        case 24:
          if (tag !== 194) {
            break;
          }

          message.kickedUsersList = KickedUsersList.decode(reader, reader.uint32());
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.roomFullDetails = RoomFullDetails.decode(reader, reader.uint32());
          continue;
        case 26:
          if (tag !== 208) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
        case 27:
          if (tag !== 218) {
            break;
          }

          message.clientSideUserDtoList = ClientSideUserDtoList.decode(reader, reader.uint32());
          continue;
        case 28:
          if (tag !== 226) {
            break;
          }

          message.chatMessageDtoList = ChatMessageDtoList.decode(reader, reader.uint32());
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.avatarMessageDto = AvatarMessageDto.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ClientMessage {
    return {
      messageType: isSet(object.messageType) ? clientMessageTypeFromJSON(object.messageType) : 0,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      intValue: isSet(object.intValue) ? globalThis.Number(object.intValue) : undefined,
      midiMessageOutputDto: isSet(object.midiMessageOutputDto)
        ? MidiMessageOutputDto.fromJSON(object.midiMessageOutputDto)
        : undefined,
      chatMessageDto: isSet(object.chatMessageDto) ? ChatMessageDto.fromJSON(object.chatMessageDto) : undefined,
      roomChatHistory: isSet(object.roomChatHistory) ? RoomChatHistory.fromJSON(object.roomChatHistory) : undefined,
      joinedRoomData: isSet(object.joinedRoomData) ? JoinedRoomData.fromJSON(object.joinedRoomData) : undefined,
      userDto: isSet(object.userDto) ? UserDto.fromJSON(object.userDto) : undefined,
      userClientDto: isSet(object.userClientDto) ? UserClientDto.fromJSON(object.userClientDto) : undefined,
      commandResponse: isSet(object.commandResponse) ? CommandResponse.fromJSON(object.commandResponse) : undefined,
      userDtoList: isSet(object.userDtoList) ? UserDtoList.fromJSON(object.userDtoList) : undefined,
      welcomeDto: isSet(object.welcomeDto) ? WelcomeDto.fromJSON(object.welcomeDto) : undefined,
      basicRoomDto: isSet(object.basicRoomDto) ? BasicRoomDto.fromJSON(object.basicRoomDto) : undefined,
      userUpdateCommand: isSet(object.userUpdateCommand)
        ? UserUpdateCommand.fromJSON(object.userUpdateCommand)
        : undefined,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : undefined,
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : undefined,
      roomSettings: isSet(object.roomSettings) ? RoomSettings.fromJSON(object.roomSettings) : undefined,
      friendDtoList: isSet(object.friendDtoList) ? FriendDtoList.fromJSON(object.friendDtoList) : undefined,
      friendDto: isSet(object.friendDto) ? FriendDto.fromJSON(object.friendDto) : undefined,
      pendingFriendRequestList: isSet(object.pendingFriendRequestList)
        ? PendingFriendRequestList.fromJSON(object.pendingFriendRequestList)
        : undefined,
      pendingFriendRequest: isSet(object.pendingFriendRequest)
        ? PendingFriendRequest.fromJSON(object.pendingFriendRequest)
        : undefined,
      userId: isSet(object.userId) ? globalThis.String(object.userId) : undefined,
      socketIDList: isSet(object.socketIDList) ? SocketIdList.fromJSON(object.socketIDList) : undefined,
      kickedUsersList: isSet(object.kickedUsersList) ? KickedUsersList.fromJSON(object.kickedUsersList) : undefined,
      roomFullDetails: isSet(object.roomFullDetails) ? RoomFullDetails.fromJSON(object.roomFullDetails) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      clientSideUserDtoList: isSet(object.clientSideUserDtoList)
        ? ClientSideUserDtoList.fromJSON(object.clientSideUserDtoList)
        : undefined,
      chatMessageDtoList: isSet(object.chatMessageDtoList)
        ? ChatMessageDtoList.fromJSON(object.chatMessageDtoList)
        : undefined,
      avatarMessageDto: isSet(object.avatarMessageDto) ? AvatarMessageDto.fromJSON(object.avatarMessageDto) : undefined,
    };
  },

  toJSON(message: ClientMessage): unknown {
    const obj: any = {};
    if (message.messageType !== 0) {
      obj.messageType = clientMessageTypeToJSON(message.messageType);
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.intValue !== undefined) {
      obj.intValue = Math.round(message.intValue);
    }
    if (message.midiMessageOutputDto !== undefined) {
      obj.midiMessageOutputDto = MidiMessageOutputDto.toJSON(message.midiMessageOutputDto);
    }
    if (message.chatMessageDto !== undefined) {
      obj.chatMessageDto = ChatMessageDto.toJSON(message.chatMessageDto);
    }
    if (message.roomChatHistory !== undefined) {
      obj.roomChatHistory = RoomChatHistory.toJSON(message.roomChatHistory);
    }
    if (message.joinedRoomData !== undefined) {
      obj.joinedRoomData = JoinedRoomData.toJSON(message.joinedRoomData);
    }
    if (message.userDto !== undefined) {
      obj.userDto = UserDto.toJSON(message.userDto);
    }
    if (message.userClientDto !== undefined) {
      obj.userClientDto = UserClientDto.toJSON(message.userClientDto);
    }
    if (message.commandResponse !== undefined) {
      obj.commandResponse = CommandResponse.toJSON(message.commandResponse);
    }
    if (message.userDtoList !== undefined) {
      obj.userDtoList = UserDtoList.toJSON(message.userDtoList);
    }
    if (message.welcomeDto !== undefined) {
      obj.welcomeDto = WelcomeDto.toJSON(message.welcomeDto);
    }
    if (message.basicRoomDto !== undefined) {
      obj.basicRoomDto = BasicRoomDto.toJSON(message.basicRoomDto);
    }
    if (message.userUpdateCommand !== undefined) {
      obj.userUpdateCommand = UserUpdateCommand.toJSON(message.userUpdateCommand);
    }
    if (message.socketId !== undefined) {
      obj.socketId = message.socketId;
    }
    if (message.usertag !== undefined) {
      obj.usertag = message.usertag;
    }
    if (message.roomSettings !== undefined) {
      obj.roomSettings = RoomSettings.toJSON(message.roomSettings);
    }
    if (message.friendDtoList !== undefined) {
      obj.friendDtoList = FriendDtoList.toJSON(message.friendDtoList);
    }
    if (message.friendDto !== undefined) {
      obj.friendDto = FriendDto.toJSON(message.friendDto);
    }
    if (message.pendingFriendRequestList !== undefined) {
      obj.pendingFriendRequestList = PendingFriendRequestList.toJSON(message.pendingFriendRequestList);
    }
    if (message.pendingFriendRequest !== undefined) {
      obj.pendingFriendRequest = PendingFriendRequest.toJSON(message.pendingFriendRequest);
    }
    if (message.userId !== undefined) {
      obj.userId = message.userId;
    }
    if (message.socketIDList !== undefined) {
      obj.socketIDList = SocketIdList.toJSON(message.socketIDList);
    }
    if (message.kickedUsersList !== undefined) {
      obj.kickedUsersList = KickedUsersList.toJSON(message.kickedUsersList);
    }
    if (message.roomFullDetails !== undefined) {
      obj.roomFullDetails = RoomFullDetails.toJSON(message.roomFullDetails);
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.clientSideUserDtoList !== undefined) {
      obj.clientSideUserDtoList = ClientSideUserDtoList.toJSON(message.clientSideUserDtoList);
    }
    if (message.chatMessageDtoList !== undefined) {
      obj.chatMessageDtoList = ChatMessageDtoList.toJSON(message.chatMessageDtoList);
    }
    if (message.avatarMessageDto !== undefined) {
      obj.avatarMessageDto = AvatarMessageDto.toJSON(message.avatarMessageDto);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ClientMessage>, I>>(base?: I): ClientMessage {
    return ClientMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientMessage>, I>>(object: I): ClientMessage {
    const message = createBaseClientMessage();
    message.messageType = object.messageType ?? 0;
    message.stringValue = object.stringValue ?? undefined;
    message.intValue = object.intValue ?? undefined;
    message.midiMessageOutputDto = (object.midiMessageOutputDto !== undefined && object.midiMessageOutputDto !== null)
      ? MidiMessageOutputDto.fromPartial(object.midiMessageOutputDto)
      : undefined;
    message.chatMessageDto = (object.chatMessageDto !== undefined && object.chatMessageDto !== null)
      ? ChatMessageDto.fromPartial(object.chatMessageDto)
      : undefined;
    message.roomChatHistory = (object.roomChatHistory !== undefined && object.roomChatHistory !== null)
      ? RoomChatHistory.fromPartial(object.roomChatHistory)
      : undefined;
    message.joinedRoomData = (object.joinedRoomData !== undefined && object.joinedRoomData !== null)
      ? JoinedRoomData.fromPartial(object.joinedRoomData)
      : undefined;
    message.userDto = (object.userDto !== undefined && object.userDto !== null)
      ? UserDto.fromPartial(object.userDto)
      : undefined;
    message.userClientDto = (object.userClientDto !== undefined && object.userClientDto !== null)
      ? UserClientDto.fromPartial(object.userClientDto)
      : undefined;
    message.commandResponse = (object.commandResponse !== undefined && object.commandResponse !== null)
      ? CommandResponse.fromPartial(object.commandResponse)
      : undefined;
    message.userDtoList = (object.userDtoList !== undefined && object.userDtoList !== null)
      ? UserDtoList.fromPartial(object.userDtoList)
      : undefined;
    message.welcomeDto = (object.welcomeDto !== undefined && object.welcomeDto !== null)
      ? WelcomeDto.fromPartial(object.welcomeDto)
      : undefined;
    message.basicRoomDto = (object.basicRoomDto !== undefined && object.basicRoomDto !== null)
      ? BasicRoomDto.fromPartial(object.basicRoomDto)
      : undefined;
    message.userUpdateCommand = (object.userUpdateCommand !== undefined && object.userUpdateCommand !== null)
      ? UserUpdateCommand.fromPartial(object.userUpdateCommand)
      : undefined;
    message.socketId = object.socketId ?? undefined;
    message.usertag = object.usertag ?? undefined;
    message.roomSettings = (object.roomSettings !== undefined && object.roomSettings !== null)
      ? RoomSettings.fromPartial(object.roomSettings)
      : undefined;
    message.friendDtoList = (object.friendDtoList !== undefined && object.friendDtoList !== null)
      ? FriendDtoList.fromPartial(object.friendDtoList)
      : undefined;
    message.friendDto = (object.friendDto !== undefined && object.friendDto !== null)
      ? FriendDto.fromPartial(object.friendDto)
      : undefined;
    message.pendingFriendRequestList =
      (object.pendingFriendRequestList !== undefined && object.pendingFriendRequestList !== null)
        ? PendingFriendRequestList.fromPartial(object.pendingFriendRequestList)
        : undefined;
    message.pendingFriendRequest = (object.pendingFriendRequest !== undefined && object.pendingFriendRequest !== null)
      ? PendingFriendRequest.fromPartial(object.pendingFriendRequest)
      : undefined;
    message.userId = object.userId ?? undefined;
    message.socketIDList = (object.socketIDList !== undefined && object.socketIDList !== null)
      ? SocketIdList.fromPartial(object.socketIDList)
      : undefined;
    message.kickedUsersList = (object.kickedUsersList !== undefined && object.kickedUsersList !== null)
      ? KickedUsersList.fromPartial(object.kickedUsersList)
      : undefined;
    message.roomFullDetails = (object.roomFullDetails !== undefined && object.roomFullDetails !== null)
      ? RoomFullDetails.fromPartial(object.roomFullDetails)
      : undefined;
    message.boolValue = object.boolValue ?? undefined;
    message.clientSideUserDtoList =
      (object.clientSideUserDtoList !== undefined && object.clientSideUserDtoList !== null)
        ? ClientSideUserDtoList.fromPartial(object.clientSideUserDtoList)
        : undefined;
    message.chatMessageDtoList = (object.chatMessageDtoList !== undefined && object.chatMessageDtoList !== null)
      ? ChatMessageDtoList.fromPartial(object.chatMessageDtoList)
      : undefined;
    message.avatarMessageDto = (object.avatarMessageDto !== undefined && object.avatarMessageDto !== null)
      ? AvatarMessageDto.fromPartial(object.avatarMessageDto)
      : undefined;
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
