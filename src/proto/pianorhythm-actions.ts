// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.181.2
//   protoc               v4.24.4
// source: pianorhythm-actions.proto

/* eslint-disable */
import Long from "long";
import _m0 from "protobufjs/minimal";
import {
  ChatMessageDto,
  ClientMessage,
  ClientSideUserDtoList,
  CommandResponse,
  CommandResponse_JoinRoomFailResponse,
  CommandResponse_RoomsList,
  FriendDtoList,
  JoinedRoomData,
  KickedUsersList,
  MidiMessageOutputDto,
  PendingFriendRequestList,
  RoomChatHistory,
  SocketIdList,
  UserDtoList,
  WelcomeDto,
} from "./client-message";
import {
  ActiveChannelsMode,
  activeChannelsModeFromJSON,
  activeChannelsModeToJSON,
  AudioChannel,
  Instrument,
  InstrumentsList,
  MidiNoteSource,
  midiNoteSourceFromJSON,
  midiNoteSourceToJSON,
  SetChannelDetailsPayload,
  SetChannelInstrumentPayload,
  SynthEventProgramChangePayload,
  UpdateChannelPayload,
} from "./midi-renditions";
import {
  AppCommonEnvironment,
  AppKeyboardMappingVisualizeVec,
  AppMidiTrack,
  AppNotificationConfig,
  AppPianoKey,
  AppPianoPedal,
  AppRenderableEntity,
  AppSettings,
  AppVPSequencerFileLoad,
} from "./pianorhythm-app-renditions";
import { BasicRoomDto, RoomFullDetails, RoomSettings } from "./room-renditions";
import {
  RoomIDWithPassword,
  RoomOwnerCommandDU,
  ServerCommandDU,
  ServerMessage_JoinRoomByNameRequest,
} from "./server-message";
import {
  AvatarWorldDataDto_AvatarMessageWorldPosition,
  PendingFriendRequest,
  UserClientDto,
  UserDto,
  UserUpdateCommand,
} from "./user-renditions";

export const protobufPackage = "PianoRhythm.AppStateActions";

export interface SocketIdWithInt32 {
  socketId: string;
  int32Value: number;
}

export interface ChannelWithBool {
  channel: number;
  boolValue: boolean;
}

export interface ChannelWithUint32 {
  channel: number;
  uint32Value: number;
}

export interface AudioSynthActionData {
  channel: number;
  note: number;
  velocity: number;
  noteSource: MidiNoteSource;
}

export interface AudioSynthActions {
  action: AudioSynthActions_Action;
  sourceSocketID?: string | undefined;
  sourceHashedSocketID?: number | undefined;
  socketId?: string | undefined;
  channel?: number | undefined;
  channelWithBool?: ChannelWithBool | undefined;
  instrument?: Instrument | undefined;
  uint32Value?: number | undefined;
  instrumentsList?: InstrumentsList | undefined;
  boolValue?: boolean | undefined;
  channelWithUint32?: ChannelWithUint32 | undefined;
  synthData?: AudioSynthActionData | undefined;
}

export enum AudioSynthActions_Action {
  NoteOn = 0,
  NoteOff = 1,
  AddUser = 2,
  RemoveUser = 3,
  AddClient = 4,
  SetChannelVolume = 5,
  SetChannelPan = 6,
  SetChannelExpression = 7,
  UNRECOGNIZED = -1,
}

export function audioSynthActions_ActionFromJSON(object: any): AudioSynthActions_Action {
  switch (object) {
    case 0:
    case "NoteOn":
      return AudioSynthActions_Action.NoteOn;
    case 1:
    case "NoteOff":
      return AudioSynthActions_Action.NoteOff;
    case 2:
    case "AddUser":
      return AudioSynthActions_Action.AddUser;
    case 3:
    case "RemoveUser":
      return AudioSynthActions_Action.RemoveUser;
    case 4:
    case "AddClient":
      return AudioSynthActions_Action.AddClient;
    case 5:
    case "SetChannelVolume":
      return AudioSynthActions_Action.SetChannelVolume;
    case 6:
    case "SetChannelPan":
      return AudioSynthActions_Action.SetChannelPan;
    case 7:
    case "SetChannelExpression":
      return AudioSynthActions_Action.SetChannelExpression;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AudioSynthActions_Action.UNRECOGNIZED;
  }
}

export function audioSynthActions_ActionToJSON(object: AudioSynthActions_Action): string {
  switch (object) {
    case AudioSynthActions_Action.NoteOn:
      return "NoteOn";
    case AudioSynthActions_Action.NoteOff:
      return "NoteOff";
    case AudioSynthActions_Action.AddUser:
      return "AddUser";
    case AudioSynthActions_Action.RemoveUser:
      return "RemoveUser";
    case AudioSynthActions_Action.AddClient:
      return "AddClient";
    case AudioSynthActions_Action.SetChannelVolume:
      return "SetChannelVolume";
    case AudioSynthActions_Action.SetChannelPan:
      return "SetChannelPan";
    case AudioSynthActions_Action.SetChannelExpression:
      return "SetChannelExpression";
    case AudioSynthActions_Action.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AppStateActions {
  action: AppStateActions_Action;
  sourceSocketID?: string | undefined;
  stringValue?: string | undefined;
  boolValue?: boolean | undefined;
  userDto?: UserDto | undefined;
  userClientDto?: UserClientDto | undefined;
  basicRoomDto?: BasicRoomDto | undefined;
  userUpdateCommand?: UserUpdateCommand | undefined;
  userDtoList?: UserDtoList | undefined;
  socketIdList?: SocketIdList | undefined;
  friendDtoList?: FriendDtoList | undefined;
  socketId?: string | undefined;
  usertag?: string | undefined;
  userid?: string | undefined;
  joinedRoomData?: JoinedRoomData | undefined;
  roomChatHistory?: RoomChatHistory | undefined;
  chatMessageDto?: ChatMessageDto | undefined;
  roomSettings?: RoomSettings | undefined;
  roomFullDetails?: RoomFullDetails | undefined;
  joinRoomFailResponse?: CommandResponse_JoinRoomFailResponse | undefined;
  roomsList?: CommandResponse_RoomsList | undefined;
  joinRoomByNameRequest?: ServerMessage_JoinRoomByNameRequest | undefined;
  serverCommand?: ServerCommandDU | undefined;
  roomOwnerCommand?: RoomOwnerCommandDU | undefined;
  roomIdWithPassword?: RoomIDWithPassword | undefined;
  roomId?: string | undefined;
  midiMessageOutputDto?: MidiMessageOutputDto | undefined;
  welcomeDto?: WelcomeDto | undefined;
  pendingFriendRequestList?: PendingFriendRequestList | undefined;
  kickedUsersList?: KickedUsersList | undefined;
  clientSideUserDtoList?: ClientSideUserDtoList | undefined;
  pendingFriendRequest?: PendingFriendRequest | undefined;
  clientMsg?: ClientMessage | undefined;
  commandResponse?: CommandResponse | undefined;
  audioSynthAction?: AudioSynthActions | undefined;
  int32Value?: number | undefined;
  uint32Value?: number | undefined;
  socketIdWithIn32?: SocketIdWithInt32 | undefined;
  instrument?: Instrument | undefined;
  instrumentsList?: InstrumentsList | undefined;
  setChannelInstrumentPayload?: SetChannelInstrumentPayload | undefined;
  setChannelDetailsPayload?: SetChannelDetailsPayload | undefined;
  slotMode?: ActiveChannelsMode | undefined;
  updateChannelPayload?: UpdateChannelPayload | undefined;
  synthEventProgramChangePayload?: SynthEventProgramChangePayload | undefined;
  audioChannel?: AudioChannel | undefined;
  appPianoKey?: AppPianoKey | undefined;
  appPianoPedal?: AppPianoPedal | undefined;
  appRenderableEntity?: AppRenderableEntity | undefined;
  loadMeshDetails?: AppStateActions_RendererLoadMeshDetails | undefined;
  appNotificationConfig?: AppNotificationConfig | undefined;
  appSettings?: AppSettings | undefined;
  channelWithBool?: ChannelWithBool | undefined;
  doubleValue?: number | undefined;
  vpFileLoad?: AppVPSequencerFileLoad | undefined;
  appMidiTrack?: AppMidiTrack | undefined;
  appCommonEnvironment?: AppCommonEnvironment | undefined;
  keyboardVisualizeMappings?: AppKeyboardMappingVisualizeVec | undefined;
  avatarWorldPosition?: AvatarWorldDataDto_AvatarMessageWorldPosition | undefined;
}

export enum AppStateActions_Action {
  Unknown = 0,
  DisableUI = 87,
  ResetState = 998,
  EnableUI = 1,
  SetCanvasLoaded = 2,
  SetCurrentRoomName = 3,
  SetCurrentRoomOwner = 4,
  SetLoggedIn = 5,
  SetMaintenanceModeActive = 6,
  SetClientLoaded = 7,
  UpdateClient = 8,
  UpdateClientByCommand = 9,
  UpdateUser = 10,
  AddUser = 11,
  RemoveUser = 12,
  SetUsers = 13,
  JoinedRoom = 14,
  FailToJoinRoom = 15,
  AddRoom = 16,
  UpdateRoom = 17,
  DeleteRoom = 18,
  SetRooms = 19,
  SetUsersTyping = 20,
  JoinRoomByName = 21,
  JoinRoomById = 22,
  JoinNextAvailableLobby = 23,
  EmitToast = 24,
  EnterRoomPassword = 25,
  WebsocketDisconnected = 26,
  ServerToClientMessage = 27,
  AddRoomChatMessage = 28,
  EditRoomChatMessage = 29,
  DeleteRoomChatMessage = 30,
  SetMaintenanceMode = 31,
  SetRoomChatMessages = 32,
  SetRoomWelcomeMessage = 33,
  SetRoomSettings = 34,
  ClearChat = 35,
  ClearChatByMessageID = 36,
  ClearChatByUsername = 37,
  ClearChatByAmount = 38,
  ClearChatBySocketID = 39,
  ServerCommandResponse = 40,
  GetRoomFullDetails = 41,
  HandleMidiMessage = 42,
  MuteEveryoneElse = 43,
  SetServerTimeOffset = 44,
  SynthAction = 45,
  AddHashedSynthUser = 46,
  RemoveHashedSynthUser = 47,
  InitializeAudioState = 48,
  SetLoadedInstruments = 49,
  SetRoomIsSelfHosted = 50,
  SetChannelActive = 51,
  SetInstrumentOnChannel = 52,
  SetPrimaryChannel = 53,
  SetMaxMultiModeChannels = 54,
  SetIsDrumChannelMuted = 55,
  SetSlotMode = 56,
  SetUseSeparateDrumKit = 57,
  SetOutputOwnNotesToOutput = 58,
  SetUseDefaultBankWhenMissing = 59,
  RemoveInstrumentFromChannel = 60,
  ClearAllAudioChannels = 61,
  IncrementSlotMode = 62,
  ResetAudioChannelsToDefault = 63,
  ToggleChannelActive = 64,
  SetClientIsMuted = 65,
  SetListenToProgramChanges = 66,
  UpdateChannelParameter = 67,
  SetIsPlayingDrumsMode = 68,
  SetMousePositionSetsVelocity = 69,
  SetIsMobile = 70,
  SetCanPlayKeys = 71,
  UpdateChannelFromSynthProgramChangeEvent = 72,
  SetUserVolume = 73,
  SetUserVelocityPercentage = 74,
  SetEqualizerEnabled = 76,
  SetReverbEnabled = 77,
  SetAudioChannel = 78,
  SetRoomStageLoading = 79,
  SetRoomStageLoaded = 80,
  PersistSettings = 82,
  InitializeAppState = 83,
  SetAppSettings = 84,
  Logout = 85,
  TriggerOfflineMode = 86,
  SetConnectionState = 88,
  SetUserMuted = 89,
  SetUserChatMuted = 90,
  SetCommonEnvironment = 91,
  AudioSetApplyVelocityCurve = 92,
  SynthEngineCreated = 93,
  /** RendererPianoKeyModelLoaded - Render related */
  RendererPianoKeyModelLoaded = 1000,
  RendererPianoPedalModelLoaded = 1001,
  RendererMainCameraLoaded = 1002,
  RendererLoadMesh = 1003,
  RendererDrumSetMeshLoaded = 1004,
  RendererResetCamera = 1005,
  RendererToggleLockCamera = 1006,
  RendererEnableRenderLoop = 1007,
  RendererDisableRenderLoop = 1008,
  RendererSetKeyboardMappings = 1009,
  RendererToggleDisplayKeyboardMappings = 1010,
  RendererSetCameraTopPosition = 1011,
  /** MidiSequencerResume - Midi File Sequencer related */
  MidiSequencerResume = 2000,
  MidiSequencerPause = 2001,
  MidiSequencerStop = 2002,
  MidiSequencerEnableLoop = 2003,
  MidiSequencerDisableLoop = 2004,
  MidiSequencerSeekPosition = 2005,
  MidiSequencerMuteTrack = 2006,
  MidiSequencerSoloTrack = 2007,
  MidiSequencerSetSpeed = 2008,
  MidiSequencerSetBPM = 2009,
  MidiSequencerRewind = 2010,
  MidiSequencerForward = 2011,
  MidiSequencerEnablePreviewOnly = 2012,
  MidiSequencerDisablePreviewOnly = 2013,
  /** VPSequencerLoadData - VP Sheet Music Sequencer */
  VPSequencerLoadData = 3000,
  VPSequencerResume = 3001,
  VPSequencerPause = 3002,
  VPSequencerStop = 3003,
  VPSequencerEnableLoop = 3004,
  VPSequencerDisableLoop = 3005,
  VPSequencerDownloadAsMidi = 3006,
  VPSequencerEnableSustain = 3007,
  VPSequencerDisableSustain = 3008,
  VPSequencerSetPlaybackSpeed = 3009,
  VPSequencerSetBPM = 3010,
  /** AppMidiLooperRecord - App Midi Track (Looper) */
  AppMidiLooperRecord = 4000,
  AppMidiLooperPlay = 4001,
  AppMidiLooperStop = 4002,
  AppMidiLooperSetBPM = 4003,
  AppMidiLooperDispose = 4004,
  AppMidiLooperStopRecord = 4005,
  AppMidiLooperEnableTrim = 4006,
  AppMidiLooperDisableTrim = 4007,
  AppMidiLooperSetTrack = 4008,
  AppMidiLooperGetTracks = 4009,
  AppMidiLooperClearTrack = 4010,
  AppMidiLooperToggleTrackPlaying = 4011,
  /** SetAvatarPosition - Avatar */
  SetAvatarPosition = 5000,
  SetAvatarPianoBench = 5001,
  UNRECOGNIZED = -1,
}

export function appStateActions_ActionFromJSON(object: any): AppStateActions_Action {
  switch (object) {
    case 0:
    case "Unknown":
      return AppStateActions_Action.Unknown;
    case 87:
    case "DisableUI":
      return AppStateActions_Action.DisableUI;
    case 998:
    case "ResetState":
      return AppStateActions_Action.ResetState;
    case 1:
    case "EnableUI":
      return AppStateActions_Action.EnableUI;
    case 2:
    case "SetCanvasLoaded":
      return AppStateActions_Action.SetCanvasLoaded;
    case 3:
    case "SetCurrentRoomName":
      return AppStateActions_Action.SetCurrentRoomName;
    case 4:
    case "SetCurrentRoomOwner":
      return AppStateActions_Action.SetCurrentRoomOwner;
    case 5:
    case "SetLoggedIn":
      return AppStateActions_Action.SetLoggedIn;
    case 6:
    case "SetMaintenanceModeActive":
      return AppStateActions_Action.SetMaintenanceModeActive;
    case 7:
    case "SetClientLoaded":
      return AppStateActions_Action.SetClientLoaded;
    case 8:
    case "UpdateClient":
      return AppStateActions_Action.UpdateClient;
    case 9:
    case "UpdateClientByCommand":
      return AppStateActions_Action.UpdateClientByCommand;
    case 10:
    case "UpdateUser":
      return AppStateActions_Action.UpdateUser;
    case 11:
    case "AddUser":
      return AppStateActions_Action.AddUser;
    case 12:
    case "RemoveUser":
      return AppStateActions_Action.RemoveUser;
    case 13:
    case "SetUsers":
      return AppStateActions_Action.SetUsers;
    case 14:
    case "JoinedRoom":
      return AppStateActions_Action.JoinedRoom;
    case 15:
    case "FailToJoinRoom":
      return AppStateActions_Action.FailToJoinRoom;
    case 16:
    case "AddRoom":
      return AppStateActions_Action.AddRoom;
    case 17:
    case "UpdateRoom":
      return AppStateActions_Action.UpdateRoom;
    case 18:
    case "DeleteRoom":
      return AppStateActions_Action.DeleteRoom;
    case 19:
    case "SetRooms":
      return AppStateActions_Action.SetRooms;
    case 20:
    case "SetUsersTyping":
      return AppStateActions_Action.SetUsersTyping;
    case 21:
    case "JoinRoomByName":
      return AppStateActions_Action.JoinRoomByName;
    case 22:
    case "JoinRoomById":
      return AppStateActions_Action.JoinRoomById;
    case 23:
    case "JoinNextAvailableLobby":
      return AppStateActions_Action.JoinNextAvailableLobby;
    case 24:
    case "EmitToast":
      return AppStateActions_Action.EmitToast;
    case 25:
    case "EnterRoomPassword":
      return AppStateActions_Action.EnterRoomPassword;
    case 26:
    case "WebsocketDisconnected":
      return AppStateActions_Action.WebsocketDisconnected;
    case 27:
    case "ServerToClientMessage":
      return AppStateActions_Action.ServerToClientMessage;
    case 28:
    case "AddRoomChatMessage":
      return AppStateActions_Action.AddRoomChatMessage;
    case 29:
    case "EditRoomChatMessage":
      return AppStateActions_Action.EditRoomChatMessage;
    case 30:
    case "DeleteRoomChatMessage":
      return AppStateActions_Action.DeleteRoomChatMessage;
    case 31:
    case "SetMaintenanceMode":
      return AppStateActions_Action.SetMaintenanceMode;
    case 32:
    case "SetRoomChatMessages":
      return AppStateActions_Action.SetRoomChatMessages;
    case 33:
    case "SetRoomWelcomeMessage":
      return AppStateActions_Action.SetRoomWelcomeMessage;
    case 34:
    case "SetRoomSettings":
      return AppStateActions_Action.SetRoomSettings;
    case 35:
    case "ClearChat":
      return AppStateActions_Action.ClearChat;
    case 36:
    case "ClearChatByMessageID":
      return AppStateActions_Action.ClearChatByMessageID;
    case 37:
    case "ClearChatByUsername":
      return AppStateActions_Action.ClearChatByUsername;
    case 38:
    case "ClearChatByAmount":
      return AppStateActions_Action.ClearChatByAmount;
    case 39:
    case "ClearChatBySocketID":
      return AppStateActions_Action.ClearChatBySocketID;
    case 40:
    case "ServerCommandResponse":
      return AppStateActions_Action.ServerCommandResponse;
    case 41:
    case "GetRoomFullDetails":
      return AppStateActions_Action.GetRoomFullDetails;
    case 42:
    case "HandleMidiMessage":
      return AppStateActions_Action.HandleMidiMessage;
    case 43:
    case "MuteEveryoneElse":
      return AppStateActions_Action.MuteEveryoneElse;
    case 44:
    case "SetServerTimeOffset":
      return AppStateActions_Action.SetServerTimeOffset;
    case 45:
    case "SynthAction":
      return AppStateActions_Action.SynthAction;
    case 46:
    case "AddHashedSynthUser":
      return AppStateActions_Action.AddHashedSynthUser;
    case 47:
    case "RemoveHashedSynthUser":
      return AppStateActions_Action.RemoveHashedSynthUser;
    case 48:
    case "InitializeAudioState":
      return AppStateActions_Action.InitializeAudioState;
    case 49:
    case "SetLoadedInstruments":
      return AppStateActions_Action.SetLoadedInstruments;
    case 50:
    case "SetRoomIsSelfHosted":
      return AppStateActions_Action.SetRoomIsSelfHosted;
    case 51:
    case "SetChannelActive":
      return AppStateActions_Action.SetChannelActive;
    case 52:
    case "SetInstrumentOnChannel":
      return AppStateActions_Action.SetInstrumentOnChannel;
    case 53:
    case "SetPrimaryChannel":
      return AppStateActions_Action.SetPrimaryChannel;
    case 54:
    case "SetMaxMultiModeChannels":
      return AppStateActions_Action.SetMaxMultiModeChannels;
    case 55:
    case "SetIsDrumChannelMuted":
      return AppStateActions_Action.SetIsDrumChannelMuted;
    case 56:
    case "SetSlotMode":
      return AppStateActions_Action.SetSlotMode;
    case 57:
    case "SetUseSeparateDrumKit":
      return AppStateActions_Action.SetUseSeparateDrumKit;
    case 58:
    case "SetOutputOwnNotesToOutput":
      return AppStateActions_Action.SetOutputOwnNotesToOutput;
    case 59:
    case "SetUseDefaultBankWhenMissing":
      return AppStateActions_Action.SetUseDefaultBankWhenMissing;
    case 60:
    case "RemoveInstrumentFromChannel":
      return AppStateActions_Action.RemoveInstrumentFromChannel;
    case 61:
    case "ClearAllAudioChannels":
      return AppStateActions_Action.ClearAllAudioChannels;
    case 62:
    case "IncrementSlotMode":
      return AppStateActions_Action.IncrementSlotMode;
    case 63:
    case "ResetAudioChannelsToDefault":
      return AppStateActions_Action.ResetAudioChannelsToDefault;
    case 64:
    case "ToggleChannelActive":
      return AppStateActions_Action.ToggleChannelActive;
    case 65:
    case "SetClientIsMuted":
      return AppStateActions_Action.SetClientIsMuted;
    case 66:
    case "SetListenToProgramChanges":
      return AppStateActions_Action.SetListenToProgramChanges;
    case 67:
    case "UpdateChannelParameter":
      return AppStateActions_Action.UpdateChannelParameter;
    case 68:
    case "SetIsPlayingDrumsMode":
      return AppStateActions_Action.SetIsPlayingDrumsMode;
    case 69:
    case "SetMousePositionSetsVelocity":
      return AppStateActions_Action.SetMousePositionSetsVelocity;
    case 70:
    case "SetIsMobile":
      return AppStateActions_Action.SetIsMobile;
    case 71:
    case "SetCanPlayKeys":
      return AppStateActions_Action.SetCanPlayKeys;
    case 72:
    case "UpdateChannelFromSynthProgramChangeEvent":
      return AppStateActions_Action.UpdateChannelFromSynthProgramChangeEvent;
    case 73:
    case "SetUserVolume":
      return AppStateActions_Action.SetUserVolume;
    case 74:
    case "SetUserVelocityPercentage":
      return AppStateActions_Action.SetUserVelocityPercentage;
    case 76:
    case "SetEqualizerEnabled":
      return AppStateActions_Action.SetEqualizerEnabled;
    case 77:
    case "SetReverbEnabled":
      return AppStateActions_Action.SetReverbEnabled;
    case 78:
    case "SetAudioChannel":
      return AppStateActions_Action.SetAudioChannel;
    case 79:
    case "SetRoomStageLoading":
      return AppStateActions_Action.SetRoomStageLoading;
    case 80:
    case "SetRoomStageLoaded":
      return AppStateActions_Action.SetRoomStageLoaded;
    case 82:
    case "PersistSettings":
      return AppStateActions_Action.PersistSettings;
    case 83:
    case "InitializeAppState":
      return AppStateActions_Action.InitializeAppState;
    case 84:
    case "SetAppSettings":
      return AppStateActions_Action.SetAppSettings;
    case 85:
    case "Logout":
      return AppStateActions_Action.Logout;
    case 86:
    case "TriggerOfflineMode":
      return AppStateActions_Action.TriggerOfflineMode;
    case 88:
    case "SetConnectionState":
      return AppStateActions_Action.SetConnectionState;
    case 89:
    case "SetUserMuted":
      return AppStateActions_Action.SetUserMuted;
    case 90:
    case "SetUserChatMuted":
      return AppStateActions_Action.SetUserChatMuted;
    case 91:
    case "SetCommonEnvironment":
      return AppStateActions_Action.SetCommonEnvironment;
    case 92:
    case "AudioSetApplyVelocityCurve":
      return AppStateActions_Action.AudioSetApplyVelocityCurve;
    case 93:
    case "SynthEngineCreated":
      return AppStateActions_Action.SynthEngineCreated;
    case 1000:
    case "RendererPianoKeyModelLoaded":
      return AppStateActions_Action.RendererPianoKeyModelLoaded;
    case 1001:
    case "RendererPianoPedalModelLoaded":
      return AppStateActions_Action.RendererPianoPedalModelLoaded;
    case 1002:
    case "RendererMainCameraLoaded":
      return AppStateActions_Action.RendererMainCameraLoaded;
    case 1003:
    case "RendererLoadMesh":
      return AppStateActions_Action.RendererLoadMesh;
    case 1004:
    case "RendererDrumSetMeshLoaded":
      return AppStateActions_Action.RendererDrumSetMeshLoaded;
    case 1005:
    case "RendererResetCamera":
      return AppStateActions_Action.RendererResetCamera;
    case 1006:
    case "RendererToggleLockCamera":
      return AppStateActions_Action.RendererToggleLockCamera;
    case 1007:
    case "RendererEnableRenderLoop":
      return AppStateActions_Action.RendererEnableRenderLoop;
    case 1008:
    case "RendererDisableRenderLoop":
      return AppStateActions_Action.RendererDisableRenderLoop;
    case 1009:
    case "RendererSetKeyboardMappings":
      return AppStateActions_Action.RendererSetKeyboardMappings;
    case 1010:
    case "RendererToggleDisplayKeyboardMappings":
      return AppStateActions_Action.RendererToggleDisplayKeyboardMappings;
    case 1011:
    case "RendererSetCameraTopPosition":
      return AppStateActions_Action.RendererSetCameraTopPosition;
    case 2000:
    case "MidiSequencerResume":
      return AppStateActions_Action.MidiSequencerResume;
    case 2001:
    case "MidiSequencerPause":
      return AppStateActions_Action.MidiSequencerPause;
    case 2002:
    case "MidiSequencerStop":
      return AppStateActions_Action.MidiSequencerStop;
    case 2003:
    case "MidiSequencerEnableLoop":
      return AppStateActions_Action.MidiSequencerEnableLoop;
    case 2004:
    case "MidiSequencerDisableLoop":
      return AppStateActions_Action.MidiSequencerDisableLoop;
    case 2005:
    case "MidiSequencerSeekPosition":
      return AppStateActions_Action.MidiSequencerSeekPosition;
    case 2006:
    case "MidiSequencerMuteTrack":
      return AppStateActions_Action.MidiSequencerMuteTrack;
    case 2007:
    case "MidiSequencerSoloTrack":
      return AppStateActions_Action.MidiSequencerSoloTrack;
    case 2008:
    case "MidiSequencerSetSpeed":
      return AppStateActions_Action.MidiSequencerSetSpeed;
    case 2009:
    case "MidiSequencerSetBPM":
      return AppStateActions_Action.MidiSequencerSetBPM;
    case 2010:
    case "MidiSequencerRewind":
      return AppStateActions_Action.MidiSequencerRewind;
    case 2011:
    case "MidiSequencerForward":
      return AppStateActions_Action.MidiSequencerForward;
    case 2012:
    case "MidiSequencerEnablePreviewOnly":
      return AppStateActions_Action.MidiSequencerEnablePreviewOnly;
    case 2013:
    case "MidiSequencerDisablePreviewOnly":
      return AppStateActions_Action.MidiSequencerDisablePreviewOnly;
    case 3000:
    case "VPSequencerLoadData":
      return AppStateActions_Action.VPSequencerLoadData;
    case 3001:
    case "VPSequencerResume":
      return AppStateActions_Action.VPSequencerResume;
    case 3002:
    case "VPSequencerPause":
      return AppStateActions_Action.VPSequencerPause;
    case 3003:
    case "VPSequencerStop":
      return AppStateActions_Action.VPSequencerStop;
    case 3004:
    case "VPSequencerEnableLoop":
      return AppStateActions_Action.VPSequencerEnableLoop;
    case 3005:
    case "VPSequencerDisableLoop":
      return AppStateActions_Action.VPSequencerDisableLoop;
    case 3006:
    case "VPSequencerDownloadAsMidi":
      return AppStateActions_Action.VPSequencerDownloadAsMidi;
    case 3007:
    case "VPSequencerEnableSustain":
      return AppStateActions_Action.VPSequencerEnableSustain;
    case 3008:
    case "VPSequencerDisableSustain":
      return AppStateActions_Action.VPSequencerDisableSustain;
    case 3009:
    case "VPSequencerSetPlaybackSpeed":
      return AppStateActions_Action.VPSequencerSetPlaybackSpeed;
    case 3010:
    case "VPSequencerSetBPM":
      return AppStateActions_Action.VPSequencerSetBPM;
    case 4000:
    case "AppMidiLooperRecord":
      return AppStateActions_Action.AppMidiLooperRecord;
    case 4001:
    case "AppMidiLooperPlay":
      return AppStateActions_Action.AppMidiLooperPlay;
    case 4002:
    case "AppMidiLooperStop":
      return AppStateActions_Action.AppMidiLooperStop;
    case 4003:
    case "AppMidiLooperSetBPM":
      return AppStateActions_Action.AppMidiLooperSetBPM;
    case 4004:
    case "AppMidiLooperDispose":
      return AppStateActions_Action.AppMidiLooperDispose;
    case 4005:
    case "AppMidiLooperStopRecord":
      return AppStateActions_Action.AppMidiLooperStopRecord;
    case 4006:
    case "AppMidiLooperEnableTrim":
      return AppStateActions_Action.AppMidiLooperEnableTrim;
    case 4007:
    case "AppMidiLooperDisableTrim":
      return AppStateActions_Action.AppMidiLooperDisableTrim;
    case 4008:
    case "AppMidiLooperSetTrack":
      return AppStateActions_Action.AppMidiLooperSetTrack;
    case 4009:
    case "AppMidiLooperGetTracks":
      return AppStateActions_Action.AppMidiLooperGetTracks;
    case 4010:
    case "AppMidiLooperClearTrack":
      return AppStateActions_Action.AppMidiLooperClearTrack;
    case 4011:
    case "AppMidiLooperToggleTrackPlaying":
      return AppStateActions_Action.AppMidiLooperToggleTrackPlaying;
    case 5000:
    case "SetAvatarPosition":
      return AppStateActions_Action.SetAvatarPosition;
    case 5001:
    case "SetAvatarPianoBench":
      return AppStateActions_Action.SetAvatarPianoBench;
    case -1:
    case "UNRECOGNIZED":
    default:
      return AppStateActions_Action.UNRECOGNIZED;
  }
}

export function appStateActions_ActionToJSON(object: AppStateActions_Action): string {
  switch (object) {
    case AppStateActions_Action.Unknown:
      return "Unknown";
    case AppStateActions_Action.DisableUI:
      return "DisableUI";
    case AppStateActions_Action.ResetState:
      return "ResetState";
    case AppStateActions_Action.EnableUI:
      return "EnableUI";
    case AppStateActions_Action.SetCanvasLoaded:
      return "SetCanvasLoaded";
    case AppStateActions_Action.SetCurrentRoomName:
      return "SetCurrentRoomName";
    case AppStateActions_Action.SetCurrentRoomOwner:
      return "SetCurrentRoomOwner";
    case AppStateActions_Action.SetLoggedIn:
      return "SetLoggedIn";
    case AppStateActions_Action.SetMaintenanceModeActive:
      return "SetMaintenanceModeActive";
    case AppStateActions_Action.SetClientLoaded:
      return "SetClientLoaded";
    case AppStateActions_Action.UpdateClient:
      return "UpdateClient";
    case AppStateActions_Action.UpdateClientByCommand:
      return "UpdateClientByCommand";
    case AppStateActions_Action.UpdateUser:
      return "UpdateUser";
    case AppStateActions_Action.AddUser:
      return "AddUser";
    case AppStateActions_Action.RemoveUser:
      return "RemoveUser";
    case AppStateActions_Action.SetUsers:
      return "SetUsers";
    case AppStateActions_Action.JoinedRoom:
      return "JoinedRoom";
    case AppStateActions_Action.FailToJoinRoom:
      return "FailToJoinRoom";
    case AppStateActions_Action.AddRoom:
      return "AddRoom";
    case AppStateActions_Action.UpdateRoom:
      return "UpdateRoom";
    case AppStateActions_Action.DeleteRoom:
      return "DeleteRoom";
    case AppStateActions_Action.SetRooms:
      return "SetRooms";
    case AppStateActions_Action.SetUsersTyping:
      return "SetUsersTyping";
    case AppStateActions_Action.JoinRoomByName:
      return "JoinRoomByName";
    case AppStateActions_Action.JoinRoomById:
      return "JoinRoomById";
    case AppStateActions_Action.JoinNextAvailableLobby:
      return "JoinNextAvailableLobby";
    case AppStateActions_Action.EmitToast:
      return "EmitToast";
    case AppStateActions_Action.EnterRoomPassword:
      return "EnterRoomPassword";
    case AppStateActions_Action.WebsocketDisconnected:
      return "WebsocketDisconnected";
    case AppStateActions_Action.ServerToClientMessage:
      return "ServerToClientMessage";
    case AppStateActions_Action.AddRoomChatMessage:
      return "AddRoomChatMessage";
    case AppStateActions_Action.EditRoomChatMessage:
      return "EditRoomChatMessage";
    case AppStateActions_Action.DeleteRoomChatMessage:
      return "DeleteRoomChatMessage";
    case AppStateActions_Action.SetMaintenanceMode:
      return "SetMaintenanceMode";
    case AppStateActions_Action.SetRoomChatMessages:
      return "SetRoomChatMessages";
    case AppStateActions_Action.SetRoomWelcomeMessage:
      return "SetRoomWelcomeMessage";
    case AppStateActions_Action.SetRoomSettings:
      return "SetRoomSettings";
    case AppStateActions_Action.ClearChat:
      return "ClearChat";
    case AppStateActions_Action.ClearChatByMessageID:
      return "ClearChatByMessageID";
    case AppStateActions_Action.ClearChatByUsername:
      return "ClearChatByUsername";
    case AppStateActions_Action.ClearChatByAmount:
      return "ClearChatByAmount";
    case AppStateActions_Action.ClearChatBySocketID:
      return "ClearChatBySocketID";
    case AppStateActions_Action.ServerCommandResponse:
      return "ServerCommandResponse";
    case AppStateActions_Action.GetRoomFullDetails:
      return "GetRoomFullDetails";
    case AppStateActions_Action.HandleMidiMessage:
      return "HandleMidiMessage";
    case AppStateActions_Action.MuteEveryoneElse:
      return "MuteEveryoneElse";
    case AppStateActions_Action.SetServerTimeOffset:
      return "SetServerTimeOffset";
    case AppStateActions_Action.SynthAction:
      return "SynthAction";
    case AppStateActions_Action.AddHashedSynthUser:
      return "AddHashedSynthUser";
    case AppStateActions_Action.RemoveHashedSynthUser:
      return "RemoveHashedSynthUser";
    case AppStateActions_Action.InitializeAudioState:
      return "InitializeAudioState";
    case AppStateActions_Action.SetLoadedInstruments:
      return "SetLoadedInstruments";
    case AppStateActions_Action.SetRoomIsSelfHosted:
      return "SetRoomIsSelfHosted";
    case AppStateActions_Action.SetChannelActive:
      return "SetChannelActive";
    case AppStateActions_Action.SetInstrumentOnChannel:
      return "SetInstrumentOnChannel";
    case AppStateActions_Action.SetPrimaryChannel:
      return "SetPrimaryChannel";
    case AppStateActions_Action.SetMaxMultiModeChannels:
      return "SetMaxMultiModeChannels";
    case AppStateActions_Action.SetIsDrumChannelMuted:
      return "SetIsDrumChannelMuted";
    case AppStateActions_Action.SetSlotMode:
      return "SetSlotMode";
    case AppStateActions_Action.SetUseSeparateDrumKit:
      return "SetUseSeparateDrumKit";
    case AppStateActions_Action.SetOutputOwnNotesToOutput:
      return "SetOutputOwnNotesToOutput";
    case AppStateActions_Action.SetUseDefaultBankWhenMissing:
      return "SetUseDefaultBankWhenMissing";
    case AppStateActions_Action.RemoveInstrumentFromChannel:
      return "RemoveInstrumentFromChannel";
    case AppStateActions_Action.ClearAllAudioChannels:
      return "ClearAllAudioChannels";
    case AppStateActions_Action.IncrementSlotMode:
      return "IncrementSlotMode";
    case AppStateActions_Action.ResetAudioChannelsToDefault:
      return "ResetAudioChannelsToDefault";
    case AppStateActions_Action.ToggleChannelActive:
      return "ToggleChannelActive";
    case AppStateActions_Action.SetClientIsMuted:
      return "SetClientIsMuted";
    case AppStateActions_Action.SetListenToProgramChanges:
      return "SetListenToProgramChanges";
    case AppStateActions_Action.UpdateChannelParameter:
      return "UpdateChannelParameter";
    case AppStateActions_Action.SetIsPlayingDrumsMode:
      return "SetIsPlayingDrumsMode";
    case AppStateActions_Action.SetMousePositionSetsVelocity:
      return "SetMousePositionSetsVelocity";
    case AppStateActions_Action.SetIsMobile:
      return "SetIsMobile";
    case AppStateActions_Action.SetCanPlayKeys:
      return "SetCanPlayKeys";
    case AppStateActions_Action.UpdateChannelFromSynthProgramChangeEvent:
      return "UpdateChannelFromSynthProgramChangeEvent";
    case AppStateActions_Action.SetUserVolume:
      return "SetUserVolume";
    case AppStateActions_Action.SetUserVelocityPercentage:
      return "SetUserVelocityPercentage";
    case AppStateActions_Action.SetEqualizerEnabled:
      return "SetEqualizerEnabled";
    case AppStateActions_Action.SetReverbEnabled:
      return "SetReverbEnabled";
    case AppStateActions_Action.SetAudioChannel:
      return "SetAudioChannel";
    case AppStateActions_Action.SetRoomStageLoading:
      return "SetRoomStageLoading";
    case AppStateActions_Action.SetRoomStageLoaded:
      return "SetRoomStageLoaded";
    case AppStateActions_Action.PersistSettings:
      return "PersistSettings";
    case AppStateActions_Action.InitializeAppState:
      return "InitializeAppState";
    case AppStateActions_Action.SetAppSettings:
      return "SetAppSettings";
    case AppStateActions_Action.Logout:
      return "Logout";
    case AppStateActions_Action.TriggerOfflineMode:
      return "TriggerOfflineMode";
    case AppStateActions_Action.SetConnectionState:
      return "SetConnectionState";
    case AppStateActions_Action.SetUserMuted:
      return "SetUserMuted";
    case AppStateActions_Action.SetUserChatMuted:
      return "SetUserChatMuted";
    case AppStateActions_Action.SetCommonEnvironment:
      return "SetCommonEnvironment";
    case AppStateActions_Action.AudioSetApplyVelocityCurve:
      return "AudioSetApplyVelocityCurve";
    case AppStateActions_Action.SynthEngineCreated:
      return "SynthEngineCreated";
    case AppStateActions_Action.RendererPianoKeyModelLoaded:
      return "RendererPianoKeyModelLoaded";
    case AppStateActions_Action.RendererPianoPedalModelLoaded:
      return "RendererPianoPedalModelLoaded";
    case AppStateActions_Action.RendererMainCameraLoaded:
      return "RendererMainCameraLoaded";
    case AppStateActions_Action.RendererLoadMesh:
      return "RendererLoadMesh";
    case AppStateActions_Action.RendererDrumSetMeshLoaded:
      return "RendererDrumSetMeshLoaded";
    case AppStateActions_Action.RendererResetCamera:
      return "RendererResetCamera";
    case AppStateActions_Action.RendererToggleLockCamera:
      return "RendererToggleLockCamera";
    case AppStateActions_Action.RendererEnableRenderLoop:
      return "RendererEnableRenderLoop";
    case AppStateActions_Action.RendererDisableRenderLoop:
      return "RendererDisableRenderLoop";
    case AppStateActions_Action.RendererSetKeyboardMappings:
      return "RendererSetKeyboardMappings";
    case AppStateActions_Action.RendererToggleDisplayKeyboardMappings:
      return "RendererToggleDisplayKeyboardMappings";
    case AppStateActions_Action.RendererSetCameraTopPosition:
      return "RendererSetCameraTopPosition";
    case AppStateActions_Action.MidiSequencerResume:
      return "MidiSequencerResume";
    case AppStateActions_Action.MidiSequencerPause:
      return "MidiSequencerPause";
    case AppStateActions_Action.MidiSequencerStop:
      return "MidiSequencerStop";
    case AppStateActions_Action.MidiSequencerEnableLoop:
      return "MidiSequencerEnableLoop";
    case AppStateActions_Action.MidiSequencerDisableLoop:
      return "MidiSequencerDisableLoop";
    case AppStateActions_Action.MidiSequencerSeekPosition:
      return "MidiSequencerSeekPosition";
    case AppStateActions_Action.MidiSequencerMuteTrack:
      return "MidiSequencerMuteTrack";
    case AppStateActions_Action.MidiSequencerSoloTrack:
      return "MidiSequencerSoloTrack";
    case AppStateActions_Action.MidiSequencerSetSpeed:
      return "MidiSequencerSetSpeed";
    case AppStateActions_Action.MidiSequencerSetBPM:
      return "MidiSequencerSetBPM";
    case AppStateActions_Action.MidiSequencerRewind:
      return "MidiSequencerRewind";
    case AppStateActions_Action.MidiSequencerForward:
      return "MidiSequencerForward";
    case AppStateActions_Action.MidiSequencerEnablePreviewOnly:
      return "MidiSequencerEnablePreviewOnly";
    case AppStateActions_Action.MidiSequencerDisablePreviewOnly:
      return "MidiSequencerDisablePreviewOnly";
    case AppStateActions_Action.VPSequencerLoadData:
      return "VPSequencerLoadData";
    case AppStateActions_Action.VPSequencerResume:
      return "VPSequencerResume";
    case AppStateActions_Action.VPSequencerPause:
      return "VPSequencerPause";
    case AppStateActions_Action.VPSequencerStop:
      return "VPSequencerStop";
    case AppStateActions_Action.VPSequencerEnableLoop:
      return "VPSequencerEnableLoop";
    case AppStateActions_Action.VPSequencerDisableLoop:
      return "VPSequencerDisableLoop";
    case AppStateActions_Action.VPSequencerDownloadAsMidi:
      return "VPSequencerDownloadAsMidi";
    case AppStateActions_Action.VPSequencerEnableSustain:
      return "VPSequencerEnableSustain";
    case AppStateActions_Action.VPSequencerDisableSustain:
      return "VPSequencerDisableSustain";
    case AppStateActions_Action.VPSequencerSetPlaybackSpeed:
      return "VPSequencerSetPlaybackSpeed";
    case AppStateActions_Action.VPSequencerSetBPM:
      return "VPSequencerSetBPM";
    case AppStateActions_Action.AppMidiLooperRecord:
      return "AppMidiLooperRecord";
    case AppStateActions_Action.AppMidiLooperPlay:
      return "AppMidiLooperPlay";
    case AppStateActions_Action.AppMidiLooperStop:
      return "AppMidiLooperStop";
    case AppStateActions_Action.AppMidiLooperSetBPM:
      return "AppMidiLooperSetBPM";
    case AppStateActions_Action.AppMidiLooperDispose:
      return "AppMidiLooperDispose";
    case AppStateActions_Action.AppMidiLooperStopRecord:
      return "AppMidiLooperStopRecord";
    case AppStateActions_Action.AppMidiLooperEnableTrim:
      return "AppMidiLooperEnableTrim";
    case AppStateActions_Action.AppMidiLooperDisableTrim:
      return "AppMidiLooperDisableTrim";
    case AppStateActions_Action.AppMidiLooperSetTrack:
      return "AppMidiLooperSetTrack";
    case AppStateActions_Action.AppMidiLooperGetTracks:
      return "AppMidiLooperGetTracks";
    case AppStateActions_Action.AppMidiLooperClearTrack:
      return "AppMidiLooperClearTrack";
    case AppStateActions_Action.AppMidiLooperToggleTrackPlaying:
      return "AppMidiLooperToggleTrackPlaying";
    case AppStateActions_Action.SetAvatarPosition:
      return "SetAvatarPosition";
    case AppStateActions_Action.SetAvatarPianoBench:
      return "SetAvatarPianoBench";
    case AppStateActions_Action.UNRECOGNIZED:
    default:
      return "UNRECOGNIZED";
  }
}

export interface AppStateActions_RendererLoadMeshDetails {
  filePath: string;
  meshName: string;
}

function createBaseSocketIdWithInt32(): SocketIdWithInt32 {
  return { socketId: "", int32Value: 0 };
}

export const SocketIdWithInt32 = {
  encode(message: SocketIdWithInt32, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.socketId !== "") {
      writer.uint32(10).string(message.socketId);
    }
    if (message.int32Value !== 0) {
      writer.uint32(16).uint32(message.int32Value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): SocketIdWithInt32 {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSocketIdWithInt32();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.int32Value = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): SocketIdWithInt32 {
    return {
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : "",
      int32Value: isSet(object.int32Value) ? globalThis.Number(object.int32Value) : 0,
    };
  },

  toJSON(message: SocketIdWithInt32): unknown {
    const obj: any = {};
    if (message.socketId !== "") {
      obj.socketId = message.socketId;
    }
    if (message.int32Value !== 0) {
      obj.int32Value = Math.round(message.int32Value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<SocketIdWithInt32>, I>>(base?: I): SocketIdWithInt32 {
    return SocketIdWithInt32.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SocketIdWithInt32>, I>>(object: I): SocketIdWithInt32 {
    const message = createBaseSocketIdWithInt32();
    message.socketId = object.socketId ?? "";
    message.int32Value = object.int32Value ?? 0;
    return message;
  },
};

function createBaseChannelWithBool(): ChannelWithBool {
  return { channel: 0, boolValue: false };
}

export const ChannelWithBool = {
  encode(message: ChannelWithBool, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.boolValue !== false) {
      writer.uint32(16).bool(message.boolValue);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChannelWithBool {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChannelWithBool();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChannelWithBool {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : false,
    };
  },

  toJSON(message: ChannelWithBool): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.boolValue !== false) {
      obj.boolValue = message.boolValue;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChannelWithBool>, I>>(base?: I): ChannelWithBool {
    return ChannelWithBool.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChannelWithBool>, I>>(object: I): ChannelWithBool {
    const message = createBaseChannelWithBool();
    message.channel = object.channel ?? 0;
    message.boolValue = object.boolValue ?? false;
    return message;
  },
};

function createBaseChannelWithUint32(): ChannelWithUint32 {
  return { channel: 0, uint32Value: 0 };
}

export const ChannelWithUint32 = {
  encode(message: ChannelWithUint32, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.uint32Value !== 0) {
      writer.uint32(16).uint32(message.uint32Value);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): ChannelWithUint32 {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseChannelWithUint32();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.uint32Value = reader.uint32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): ChannelWithUint32 {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      uint32Value: isSet(object.uint32Value) ? globalThis.Number(object.uint32Value) : 0,
    };
  },

  toJSON(message: ChannelWithUint32): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.uint32Value !== 0) {
      obj.uint32Value = Math.round(message.uint32Value);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<ChannelWithUint32>, I>>(base?: I): ChannelWithUint32 {
    return ChannelWithUint32.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChannelWithUint32>, I>>(object: I): ChannelWithUint32 {
    const message = createBaseChannelWithUint32();
    message.channel = object.channel ?? 0;
    message.uint32Value = object.uint32Value ?? 0;
    return message;
  },
};

function createBaseAudioSynthActionData(): AudioSynthActionData {
  return { channel: 0, note: 0, velocity: 0, noteSource: 0 };
}

export const AudioSynthActionData = {
  encode(message: AudioSynthActionData, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.channel !== 0) {
      writer.uint32(8).uint32(message.channel);
    }
    if (message.note !== 0) {
      writer.uint32(16).uint32(message.note);
    }
    if (message.velocity !== 0) {
      writer.uint32(24).uint32(message.velocity);
    }
    if (message.noteSource !== 0) {
      writer.uint32(32).int32(message.noteSource);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AudioSynthActionData {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAudioSynthActionData();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }

          message.note = reader.uint32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.velocity = reader.uint32();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }

          message.noteSource = reader.int32() as any;
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AudioSynthActionData {
    return {
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : 0,
      note: isSet(object.note) ? globalThis.Number(object.note) : 0,
      velocity: isSet(object.velocity) ? globalThis.Number(object.velocity) : 0,
      noteSource: isSet(object.noteSource) ? midiNoteSourceFromJSON(object.noteSource) : 0,
    };
  },

  toJSON(message: AudioSynthActionData): unknown {
    const obj: any = {};
    if (message.channel !== 0) {
      obj.channel = Math.round(message.channel);
    }
    if (message.note !== 0) {
      obj.note = Math.round(message.note);
    }
    if (message.velocity !== 0) {
      obj.velocity = Math.round(message.velocity);
    }
    if (message.noteSource !== 0) {
      obj.noteSource = midiNoteSourceToJSON(message.noteSource);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AudioSynthActionData>, I>>(base?: I): AudioSynthActionData {
    return AudioSynthActionData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AudioSynthActionData>, I>>(object: I): AudioSynthActionData {
    const message = createBaseAudioSynthActionData();
    message.channel = object.channel ?? 0;
    message.note = object.note ?? 0;
    message.velocity = object.velocity ?? 0;
    message.noteSource = object.noteSource ?? 0;
    return message;
  },
};

function createBaseAudioSynthActions(): AudioSynthActions {
  return {
    action: 0,
    sourceSocketID: undefined,
    sourceHashedSocketID: undefined,
    socketId: undefined,
    channel: undefined,
    channelWithBool: undefined,
    instrument: undefined,
    uint32Value: undefined,
    instrumentsList: undefined,
    boolValue: undefined,
    channelWithUint32: undefined,
    synthData: undefined,
  };
}

export const AudioSynthActions = {
  encode(message: AudioSynthActions, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.action !== 0) {
      writer.uint32(8).int32(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      writer.uint32(7994).string(message.sourceSocketID);
    }
    if (message.sourceHashedSocketID !== undefined) {
      writer.uint32(7984).uint64(message.sourceHashedSocketID);
    }
    if (message.socketId !== undefined) {
      writer.uint32(18).string(message.socketId);
    }
    if (message.channel !== undefined) {
      writer.uint32(24).uint32(message.channel);
    }
    if (message.channelWithBool !== undefined) {
      ChannelWithBool.encode(message.channelWithBool, writer.uint32(34).fork()).ldelim();
    }
    if (message.instrument !== undefined) {
      Instrument.encode(message.instrument, writer.uint32(42).fork()).ldelim();
    }
    if (message.uint32Value !== undefined) {
      writer.uint32(48).uint32(message.uint32Value);
    }
    if (message.instrumentsList !== undefined) {
      InstrumentsList.encode(message.instrumentsList, writer.uint32(58).fork()).ldelim();
    }
    if (message.boolValue !== undefined) {
      writer.uint32(64).bool(message.boolValue);
    }
    if (message.channelWithUint32 !== undefined) {
      ChannelWithUint32.encode(message.channelWithUint32, writer.uint32(74).fork()).ldelim();
    }
    if (message.synthData !== undefined) {
      AudioSynthActionData.encode(message.synthData, writer.uint32(82).fork()).ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AudioSynthActions {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAudioSynthActions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.action = reader.int32() as any;
          continue;
        case 999:
          if (tag !== 7994) {
            break;
          }

          message.sourceSocketID = reader.string();
          continue;
        case 998:
          if (tag !== 7984) {
            break;
          }

          message.sourceHashedSocketID = longToNumber(reader.uint64() as Long);
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.channel = reader.uint32();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.channelWithBool = ChannelWithBool.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.instrument = Instrument.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }

          message.uint32Value = reader.uint32();
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.instrumentsList = InstrumentsList.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.channelWithUint32 = ChannelWithUint32.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.synthData = AudioSynthActionData.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AudioSynthActions {
    return {
      action: isSet(object.action) ? audioSynthActions_ActionFromJSON(object.action) : 0,
      sourceSocketID: isSet(object.sourceSocketID) ? globalThis.String(object.sourceSocketID) : undefined,
      sourceHashedSocketID: isSet(object.sourceHashedSocketID)
        ? globalThis.Number(object.sourceHashedSocketID)
        : undefined,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : undefined,
      channel: isSet(object.channel) ? globalThis.Number(object.channel) : undefined,
      channelWithBool: isSet(object.channelWithBool) ? ChannelWithBool.fromJSON(object.channelWithBool) : undefined,
      instrument: isSet(object.instrument) ? Instrument.fromJSON(object.instrument) : undefined,
      uint32Value: isSet(object.uint32Value) ? globalThis.Number(object.uint32Value) : undefined,
      instrumentsList: isSet(object.instrumentsList) ? InstrumentsList.fromJSON(object.instrumentsList) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      channelWithUint32: isSet(object.channelWithUint32)
        ? ChannelWithUint32.fromJSON(object.channelWithUint32)
        : undefined,
      synthData: isSet(object.synthData) ? AudioSynthActionData.fromJSON(object.synthData) : undefined,
    };
  },

  toJSON(message: AudioSynthActions): unknown {
    const obj: any = {};
    if (message.action !== 0) {
      obj.action = audioSynthActions_ActionToJSON(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      obj.sourceSocketID = message.sourceSocketID;
    }
    if (message.sourceHashedSocketID !== undefined) {
      obj.sourceHashedSocketID = Math.round(message.sourceHashedSocketID);
    }
    if (message.socketId !== undefined) {
      obj.socketId = message.socketId;
    }
    if (message.channel !== undefined) {
      obj.channel = Math.round(message.channel);
    }
    if (message.channelWithBool !== undefined) {
      obj.channelWithBool = ChannelWithBool.toJSON(message.channelWithBool);
    }
    if (message.instrument !== undefined) {
      obj.instrument = Instrument.toJSON(message.instrument);
    }
    if (message.uint32Value !== undefined) {
      obj.uint32Value = Math.round(message.uint32Value);
    }
    if (message.instrumentsList !== undefined) {
      obj.instrumentsList = InstrumentsList.toJSON(message.instrumentsList);
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.channelWithUint32 !== undefined) {
      obj.channelWithUint32 = ChannelWithUint32.toJSON(message.channelWithUint32);
    }
    if (message.synthData !== undefined) {
      obj.synthData = AudioSynthActionData.toJSON(message.synthData);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AudioSynthActions>, I>>(base?: I): AudioSynthActions {
    return AudioSynthActions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AudioSynthActions>, I>>(object: I): AudioSynthActions {
    const message = createBaseAudioSynthActions();
    message.action = object.action ?? 0;
    message.sourceSocketID = object.sourceSocketID ?? undefined;
    message.sourceHashedSocketID = object.sourceHashedSocketID ?? undefined;
    message.socketId = object.socketId ?? undefined;
    message.channel = object.channel ?? undefined;
    message.channelWithBool = (object.channelWithBool !== undefined && object.channelWithBool !== null)
      ? ChannelWithBool.fromPartial(object.channelWithBool)
      : undefined;
    message.instrument = (object.instrument !== undefined && object.instrument !== null)
      ? Instrument.fromPartial(object.instrument)
      : undefined;
    message.uint32Value = object.uint32Value ?? undefined;
    message.instrumentsList = (object.instrumentsList !== undefined && object.instrumentsList !== null)
      ? InstrumentsList.fromPartial(object.instrumentsList)
      : undefined;
    message.boolValue = object.boolValue ?? undefined;
    message.channelWithUint32 = (object.channelWithUint32 !== undefined && object.channelWithUint32 !== null)
      ? ChannelWithUint32.fromPartial(object.channelWithUint32)
      : undefined;
    message.synthData = (object.synthData !== undefined && object.synthData !== null)
      ? AudioSynthActionData.fromPartial(object.synthData)
      : undefined;
    return message;
  },
};

function createBaseAppStateActions(): AppStateActions {
  return {
    action: 0,
    sourceSocketID: undefined,
    stringValue: undefined,
    boolValue: undefined,
    userDto: undefined,
    userClientDto: undefined,
    basicRoomDto: undefined,
    userUpdateCommand: undefined,
    userDtoList: undefined,
    socketIdList: undefined,
    friendDtoList: undefined,
    socketId: undefined,
    usertag: undefined,
    userid: undefined,
    joinedRoomData: undefined,
    roomChatHistory: undefined,
    chatMessageDto: undefined,
    roomSettings: undefined,
    roomFullDetails: undefined,
    joinRoomFailResponse: undefined,
    roomsList: undefined,
    joinRoomByNameRequest: undefined,
    serverCommand: undefined,
    roomOwnerCommand: undefined,
    roomIdWithPassword: undefined,
    roomId: undefined,
    midiMessageOutputDto: undefined,
    welcomeDto: undefined,
    pendingFriendRequestList: undefined,
    kickedUsersList: undefined,
    clientSideUserDtoList: undefined,
    pendingFriendRequest: undefined,
    clientMsg: undefined,
    commandResponse: undefined,
    audioSynthAction: undefined,
    int32Value: undefined,
    uint32Value: undefined,
    socketIdWithIn32: undefined,
    instrument: undefined,
    instrumentsList: undefined,
    setChannelInstrumentPayload: undefined,
    setChannelDetailsPayload: undefined,
    slotMode: undefined,
    updateChannelPayload: undefined,
    synthEventProgramChangePayload: undefined,
    audioChannel: undefined,
    appPianoKey: undefined,
    appPianoPedal: undefined,
    appRenderableEntity: undefined,
    loadMeshDetails: undefined,
    appNotificationConfig: undefined,
    appSettings: undefined,
    channelWithBool: undefined,
    doubleValue: undefined,
    vpFileLoad: undefined,
    appMidiTrack: undefined,
    appCommonEnvironment: undefined,
    keyboardVisualizeMappings: undefined,
    avatarWorldPosition: undefined,
  };
}

export const AppStateActions = {
  encode(message: AppStateActions, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.action !== 0) {
      writer.uint32(8).int32(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      writer.uint32(7994).string(message.sourceSocketID);
    }
    if (message.stringValue !== undefined) {
      writer.uint32(18).string(message.stringValue);
    }
    if (message.boolValue !== undefined) {
      writer.uint32(24).bool(message.boolValue);
    }
    if (message.userDto !== undefined) {
      UserDto.encode(message.userDto, writer.uint32(34).fork()).ldelim();
    }
    if (message.userClientDto !== undefined) {
      UserClientDto.encode(message.userClientDto, writer.uint32(42).fork()).ldelim();
    }
    if (message.basicRoomDto !== undefined) {
      BasicRoomDto.encode(message.basicRoomDto, writer.uint32(50).fork()).ldelim();
    }
    if (message.userUpdateCommand !== undefined) {
      UserUpdateCommand.encode(message.userUpdateCommand, writer.uint32(58).fork()).ldelim();
    }
    if (message.userDtoList !== undefined) {
      UserDtoList.encode(message.userDtoList, writer.uint32(66).fork()).ldelim();
    }
    if (message.socketIdList !== undefined) {
      SocketIdList.encode(message.socketIdList, writer.uint32(74).fork()).ldelim();
    }
    if (message.friendDtoList !== undefined) {
      FriendDtoList.encode(message.friendDtoList, writer.uint32(82).fork()).ldelim();
    }
    if (message.socketId !== undefined) {
      writer.uint32(90).string(message.socketId);
    }
    if (message.usertag !== undefined) {
      writer.uint32(98).string(message.usertag);
    }
    if (message.userid !== undefined) {
      writer.uint32(106).string(message.userid);
    }
    if (message.joinedRoomData !== undefined) {
      JoinedRoomData.encode(message.joinedRoomData, writer.uint32(114).fork()).ldelim();
    }
    if (message.roomChatHistory !== undefined) {
      RoomChatHistory.encode(message.roomChatHistory, writer.uint32(122).fork()).ldelim();
    }
    if (message.chatMessageDto !== undefined) {
      ChatMessageDto.encode(message.chatMessageDto, writer.uint32(130).fork()).ldelim();
    }
    if (message.roomSettings !== undefined) {
      RoomSettings.encode(message.roomSettings, writer.uint32(138).fork()).ldelim();
    }
    if (message.roomFullDetails !== undefined) {
      RoomFullDetails.encode(message.roomFullDetails, writer.uint32(146).fork()).ldelim();
    }
    if (message.joinRoomFailResponse !== undefined) {
      CommandResponse_JoinRoomFailResponse.encode(message.joinRoomFailResponse, writer.uint32(154).fork()).ldelim();
    }
    if (message.roomsList !== undefined) {
      CommandResponse_RoomsList.encode(message.roomsList, writer.uint32(162).fork()).ldelim();
    }
    if (message.joinRoomByNameRequest !== undefined) {
      ServerMessage_JoinRoomByNameRequest.encode(message.joinRoomByNameRequest, writer.uint32(170).fork()).ldelim();
    }
    if (message.serverCommand !== undefined) {
      ServerCommandDU.encode(message.serverCommand, writer.uint32(178).fork()).ldelim();
    }
    if (message.roomOwnerCommand !== undefined) {
      RoomOwnerCommandDU.encode(message.roomOwnerCommand, writer.uint32(186).fork()).ldelim();
    }
    if (message.roomIdWithPassword !== undefined) {
      RoomIDWithPassword.encode(message.roomIdWithPassword, writer.uint32(194).fork()).ldelim();
    }
    if (message.roomId !== undefined) {
      writer.uint32(202).string(message.roomId);
    }
    if (message.midiMessageOutputDto !== undefined) {
      MidiMessageOutputDto.encode(message.midiMessageOutputDto, writer.uint32(210).fork()).ldelim();
    }
    if (message.welcomeDto !== undefined) {
      WelcomeDto.encode(message.welcomeDto, writer.uint32(218).fork()).ldelim();
    }
    if (message.pendingFriendRequestList !== undefined) {
      PendingFriendRequestList.encode(message.pendingFriendRequestList, writer.uint32(226).fork()).ldelim();
    }
    if (message.kickedUsersList !== undefined) {
      KickedUsersList.encode(message.kickedUsersList, writer.uint32(234).fork()).ldelim();
    }
    if (message.clientSideUserDtoList !== undefined) {
      ClientSideUserDtoList.encode(message.clientSideUserDtoList, writer.uint32(242).fork()).ldelim();
    }
    if (message.pendingFriendRequest !== undefined) {
      PendingFriendRequest.encode(message.pendingFriendRequest, writer.uint32(250).fork()).ldelim();
    }
    if (message.clientMsg !== undefined) {
      ClientMessage.encode(message.clientMsg, writer.uint32(258).fork()).ldelim();
    }
    if (message.commandResponse !== undefined) {
      CommandResponse.encode(message.commandResponse, writer.uint32(266).fork()).ldelim();
    }
    if (message.audioSynthAction !== undefined) {
      AudioSynthActions.encode(message.audioSynthAction, writer.uint32(274).fork()).ldelim();
    }
    if (message.int32Value !== undefined) {
      writer.uint32(280).int32(message.int32Value);
    }
    if (message.uint32Value !== undefined) {
      writer.uint32(288).uint32(message.uint32Value);
    }
    if (message.socketIdWithIn32 !== undefined) {
      SocketIdWithInt32.encode(message.socketIdWithIn32, writer.uint32(298).fork()).ldelim();
    }
    if (message.instrument !== undefined) {
      Instrument.encode(message.instrument, writer.uint32(306).fork()).ldelim();
    }
    if (message.instrumentsList !== undefined) {
      InstrumentsList.encode(message.instrumentsList, writer.uint32(314).fork()).ldelim();
    }
    if (message.setChannelInstrumentPayload !== undefined) {
      SetChannelInstrumentPayload.encode(message.setChannelInstrumentPayload, writer.uint32(322).fork()).ldelim();
    }
    if (message.setChannelDetailsPayload !== undefined) {
      SetChannelDetailsPayload.encode(message.setChannelDetailsPayload, writer.uint32(330).fork()).ldelim();
    }
    if (message.slotMode !== undefined) {
      writer.uint32(336).int32(message.slotMode);
    }
    if (message.updateChannelPayload !== undefined) {
      UpdateChannelPayload.encode(message.updateChannelPayload, writer.uint32(346).fork()).ldelim();
    }
    if (message.synthEventProgramChangePayload !== undefined) {
      SynthEventProgramChangePayload.encode(message.synthEventProgramChangePayload, writer.uint32(354).fork()).ldelim();
    }
    if (message.audioChannel !== undefined) {
      AudioChannel.encode(message.audioChannel, writer.uint32(362).fork()).ldelim();
    }
    if (message.appPianoKey !== undefined) {
      AppPianoKey.encode(message.appPianoKey, writer.uint32(370).fork()).ldelim();
    }
    if (message.appPianoPedal !== undefined) {
      AppPianoPedal.encode(message.appPianoPedal, writer.uint32(378).fork()).ldelim();
    }
    if (message.appRenderableEntity !== undefined) {
      AppRenderableEntity.encode(message.appRenderableEntity, writer.uint32(386).fork()).ldelim();
    }
    if (message.loadMeshDetails !== undefined) {
      AppStateActions_RendererLoadMeshDetails.encode(message.loadMeshDetails, writer.uint32(394).fork()).ldelim();
    }
    if (message.appNotificationConfig !== undefined) {
      AppNotificationConfig.encode(message.appNotificationConfig, writer.uint32(402).fork()).ldelim();
    }
    if (message.appSettings !== undefined) {
      AppSettings.encode(message.appSettings, writer.uint32(410).fork()).ldelim();
    }
    if (message.channelWithBool !== undefined) {
      ChannelWithBool.encode(message.channelWithBool, writer.uint32(418).fork()).ldelim();
    }
    if (message.doubleValue !== undefined) {
      writer.uint32(425).double(message.doubleValue);
    }
    if (message.vpFileLoad !== undefined) {
      AppVPSequencerFileLoad.encode(message.vpFileLoad, writer.uint32(434).fork()).ldelim();
    }
    if (message.appMidiTrack !== undefined) {
      AppMidiTrack.encode(message.appMidiTrack, writer.uint32(442).fork()).ldelim();
    }
    if (message.appCommonEnvironment !== undefined) {
      AppCommonEnvironment.encode(message.appCommonEnvironment, writer.uint32(450).fork()).ldelim();
    }
    if (message.keyboardVisualizeMappings !== undefined) {
      AppKeyboardMappingVisualizeVec.encode(message.keyboardVisualizeMappings, writer.uint32(458).fork()).ldelim();
    }
    if (message.avatarWorldPosition !== undefined) {
      AvatarWorldDataDto_AvatarMessageWorldPosition.encode(message.avatarWorldPosition, writer.uint32(466).fork())
        .ldelim();
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateActions {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateActions();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }

          message.action = reader.int32() as any;
          continue;
        case 999:
          if (tag !== 7994) {
            break;
          }

          message.sourceSocketID = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.stringValue = reader.string();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }

          message.boolValue = reader.bool();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }

          message.userDto = UserDto.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }

          message.userClientDto = UserClientDto.decode(reader, reader.uint32());
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }

          message.basicRoomDto = BasicRoomDto.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }

          message.userUpdateCommand = UserUpdateCommand.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }

          message.userDtoList = UserDtoList.decode(reader, reader.uint32());
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }

          message.socketIdList = SocketIdList.decode(reader, reader.uint32());
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }

          message.friendDtoList = FriendDtoList.decode(reader, reader.uint32());
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }

          message.socketId = reader.string();
          continue;
        case 12:
          if (tag !== 98) {
            break;
          }

          message.usertag = reader.string();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }

          message.userid = reader.string();
          continue;
        case 14:
          if (tag !== 114) {
            break;
          }

          message.joinedRoomData = JoinedRoomData.decode(reader, reader.uint32());
          continue;
        case 15:
          if (tag !== 122) {
            break;
          }

          message.roomChatHistory = RoomChatHistory.decode(reader, reader.uint32());
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }

          message.chatMessageDto = ChatMessageDto.decode(reader, reader.uint32());
          continue;
        case 17:
          if (tag !== 138) {
            break;
          }

          message.roomSettings = RoomSettings.decode(reader, reader.uint32());
          continue;
        case 18:
          if (tag !== 146) {
            break;
          }

          message.roomFullDetails = RoomFullDetails.decode(reader, reader.uint32());
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }

          message.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.decode(reader, reader.uint32());
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }

          message.roomsList = CommandResponse_RoomsList.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }

          message.joinRoomByNameRequest = ServerMessage_JoinRoomByNameRequest.decode(reader, reader.uint32());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }

          message.serverCommand = ServerCommandDU.decode(reader, reader.uint32());
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }

          message.roomOwnerCommand = RoomOwnerCommandDU.decode(reader, reader.uint32());
          continue;
        case 24:
          if (tag !== 194) {
            break;
          }

          message.roomIdWithPassword = RoomIDWithPassword.decode(reader, reader.uint32());
          continue;
        case 25:
          if (tag !== 202) {
            break;
          }

          message.roomId = reader.string();
          continue;
        case 26:
          if (tag !== 210) {
            break;
          }

          message.midiMessageOutputDto = MidiMessageOutputDto.decode(reader, reader.uint32());
          continue;
        case 27:
          if (tag !== 218) {
            break;
          }

          message.welcomeDto = WelcomeDto.decode(reader, reader.uint32());
          continue;
        case 28:
          if (tag !== 226) {
            break;
          }

          message.pendingFriendRequestList = PendingFriendRequestList.decode(reader, reader.uint32());
          continue;
        case 29:
          if (tag !== 234) {
            break;
          }

          message.kickedUsersList = KickedUsersList.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 242) {
            break;
          }

          message.clientSideUserDtoList = ClientSideUserDtoList.decode(reader, reader.uint32());
          continue;
        case 31:
          if (tag !== 250) {
            break;
          }

          message.pendingFriendRequest = PendingFriendRequest.decode(reader, reader.uint32());
          continue;
        case 32:
          if (tag !== 258) {
            break;
          }

          message.clientMsg = ClientMessage.decode(reader, reader.uint32());
          continue;
        case 33:
          if (tag !== 266) {
            break;
          }

          message.commandResponse = CommandResponse.decode(reader, reader.uint32());
          continue;
        case 34:
          if (tag !== 274) {
            break;
          }

          message.audioSynthAction = AudioSynthActions.decode(reader, reader.uint32());
          continue;
        case 35:
          if (tag !== 280) {
            break;
          }

          message.int32Value = reader.int32();
          continue;
        case 36:
          if (tag !== 288) {
            break;
          }

          message.uint32Value = reader.uint32();
          continue;
        case 37:
          if (tag !== 298) {
            break;
          }

          message.socketIdWithIn32 = SocketIdWithInt32.decode(reader, reader.uint32());
          continue;
        case 38:
          if (tag !== 306) {
            break;
          }

          message.instrument = Instrument.decode(reader, reader.uint32());
          continue;
        case 39:
          if (tag !== 314) {
            break;
          }

          message.instrumentsList = InstrumentsList.decode(reader, reader.uint32());
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }

          message.setChannelInstrumentPayload = SetChannelInstrumentPayload.decode(reader, reader.uint32());
          continue;
        case 41:
          if (tag !== 330) {
            break;
          }

          message.setChannelDetailsPayload = SetChannelDetailsPayload.decode(reader, reader.uint32());
          continue;
        case 42:
          if (tag !== 336) {
            break;
          }

          message.slotMode = reader.int32() as any;
          continue;
        case 43:
          if (tag !== 346) {
            break;
          }

          message.updateChannelPayload = UpdateChannelPayload.decode(reader, reader.uint32());
          continue;
        case 44:
          if (tag !== 354) {
            break;
          }

          message.synthEventProgramChangePayload = SynthEventProgramChangePayload.decode(reader, reader.uint32());
          continue;
        case 45:
          if (tag !== 362) {
            break;
          }

          message.audioChannel = AudioChannel.decode(reader, reader.uint32());
          continue;
        case 46:
          if (tag !== 370) {
            break;
          }

          message.appPianoKey = AppPianoKey.decode(reader, reader.uint32());
          continue;
        case 47:
          if (tag !== 378) {
            break;
          }

          message.appPianoPedal = AppPianoPedal.decode(reader, reader.uint32());
          continue;
        case 48:
          if (tag !== 386) {
            break;
          }

          message.appRenderableEntity = AppRenderableEntity.decode(reader, reader.uint32());
          continue;
        case 49:
          if (tag !== 394) {
            break;
          }

          message.loadMeshDetails = AppStateActions_RendererLoadMeshDetails.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }

          message.appNotificationConfig = AppNotificationConfig.decode(reader, reader.uint32());
          continue;
        case 51:
          if (tag !== 410) {
            break;
          }

          message.appSettings = AppSettings.decode(reader, reader.uint32());
          continue;
        case 52:
          if (tag !== 418) {
            break;
          }

          message.channelWithBool = ChannelWithBool.decode(reader, reader.uint32());
          continue;
        case 53:
          if (tag !== 425) {
            break;
          }

          message.doubleValue = reader.double();
          continue;
        case 54:
          if (tag !== 434) {
            break;
          }

          message.vpFileLoad = AppVPSequencerFileLoad.decode(reader, reader.uint32());
          continue;
        case 55:
          if (tag !== 442) {
            break;
          }

          message.appMidiTrack = AppMidiTrack.decode(reader, reader.uint32());
          continue;
        case 56:
          if (tag !== 450) {
            break;
          }

          message.appCommonEnvironment = AppCommonEnvironment.decode(reader, reader.uint32());
          continue;
        case 57:
          if (tag !== 458) {
            break;
          }

          message.keyboardVisualizeMappings = AppKeyboardMappingVisualizeVec.decode(reader, reader.uint32());
          continue;
        case 58:
          if (tag !== 466) {
            break;
          }

          message.avatarWorldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateActions {
    return {
      action: isSet(object.action) ? appStateActions_ActionFromJSON(object.action) : 0,
      sourceSocketID: isSet(object.sourceSocketID) ? globalThis.String(object.sourceSocketID) : undefined,
      stringValue: isSet(object.stringValue) ? globalThis.String(object.stringValue) : undefined,
      boolValue: isSet(object.boolValue) ? globalThis.Boolean(object.boolValue) : undefined,
      userDto: isSet(object.userDto) ? UserDto.fromJSON(object.userDto) : undefined,
      userClientDto: isSet(object.userClientDto) ? UserClientDto.fromJSON(object.userClientDto) : undefined,
      basicRoomDto: isSet(object.basicRoomDto) ? BasicRoomDto.fromJSON(object.basicRoomDto) : undefined,
      userUpdateCommand: isSet(object.userUpdateCommand)
        ? UserUpdateCommand.fromJSON(object.userUpdateCommand)
        : undefined,
      userDtoList: isSet(object.userDtoList) ? UserDtoList.fromJSON(object.userDtoList) : undefined,
      socketIdList: isSet(object.socketIdList) ? SocketIdList.fromJSON(object.socketIdList) : undefined,
      friendDtoList: isSet(object.friendDtoList) ? FriendDtoList.fromJSON(object.friendDtoList) : undefined,
      socketId: isSet(object.socketId) ? globalThis.String(object.socketId) : undefined,
      usertag: isSet(object.usertag) ? globalThis.String(object.usertag) : undefined,
      userid: isSet(object.userid) ? globalThis.String(object.userid) : undefined,
      joinedRoomData: isSet(object.joinedRoomData) ? JoinedRoomData.fromJSON(object.joinedRoomData) : undefined,
      roomChatHistory: isSet(object.roomChatHistory) ? RoomChatHistory.fromJSON(object.roomChatHistory) : undefined,
      chatMessageDto: isSet(object.chatMessageDto) ? ChatMessageDto.fromJSON(object.chatMessageDto) : undefined,
      roomSettings: isSet(object.roomSettings) ? RoomSettings.fromJSON(object.roomSettings) : undefined,
      roomFullDetails: isSet(object.roomFullDetails) ? RoomFullDetails.fromJSON(object.roomFullDetails) : undefined,
      joinRoomFailResponse: isSet(object.joinRoomFailResponse)
        ? CommandResponse_JoinRoomFailResponse.fromJSON(object.joinRoomFailResponse)
        : undefined,
      roomsList: isSet(object.roomsList) ? CommandResponse_RoomsList.fromJSON(object.roomsList) : undefined,
      joinRoomByNameRequest: isSet(object.joinRoomByNameRequest)
        ? ServerMessage_JoinRoomByNameRequest.fromJSON(object.joinRoomByNameRequest)
        : undefined,
      serverCommand: isSet(object.serverCommand) ? ServerCommandDU.fromJSON(object.serverCommand) : undefined,
      roomOwnerCommand: isSet(object.roomOwnerCommand)
        ? RoomOwnerCommandDU.fromJSON(object.roomOwnerCommand)
        : undefined,
      roomIdWithPassword: isSet(object.roomIdWithPassword)
        ? RoomIDWithPassword.fromJSON(object.roomIdWithPassword)
        : undefined,
      roomId: isSet(object.roomId) ? globalThis.String(object.roomId) : undefined,
      midiMessageOutputDto: isSet(object.midiMessageOutputDto)
        ? MidiMessageOutputDto.fromJSON(object.midiMessageOutputDto)
        : undefined,
      welcomeDto: isSet(object.welcomeDto) ? WelcomeDto.fromJSON(object.welcomeDto) : undefined,
      pendingFriendRequestList: isSet(object.pendingFriendRequestList)
        ? PendingFriendRequestList.fromJSON(object.pendingFriendRequestList)
        : undefined,
      kickedUsersList: isSet(object.kickedUsersList) ? KickedUsersList.fromJSON(object.kickedUsersList) : undefined,
      clientSideUserDtoList: isSet(object.clientSideUserDtoList)
        ? ClientSideUserDtoList.fromJSON(object.clientSideUserDtoList)
        : undefined,
      pendingFriendRequest: isSet(object.pendingFriendRequest)
        ? PendingFriendRequest.fromJSON(object.pendingFriendRequest)
        : undefined,
      clientMsg: isSet(object.clientMsg) ? ClientMessage.fromJSON(object.clientMsg) : undefined,
      commandResponse: isSet(object.commandResponse) ? CommandResponse.fromJSON(object.commandResponse) : undefined,
      audioSynthAction: isSet(object.audioSynthAction)
        ? AudioSynthActions.fromJSON(object.audioSynthAction)
        : undefined,
      int32Value: isSet(object.int32Value) ? globalThis.Number(object.int32Value) : undefined,
      uint32Value: isSet(object.uint32Value) ? globalThis.Number(object.uint32Value) : undefined,
      socketIdWithIn32: isSet(object.socketIdWithIn32)
        ? SocketIdWithInt32.fromJSON(object.socketIdWithIn32)
        : undefined,
      instrument: isSet(object.instrument) ? Instrument.fromJSON(object.instrument) : undefined,
      instrumentsList: isSet(object.instrumentsList) ? InstrumentsList.fromJSON(object.instrumentsList) : undefined,
      setChannelInstrumentPayload: isSet(object.setChannelInstrumentPayload)
        ? SetChannelInstrumentPayload.fromJSON(object.setChannelInstrumentPayload)
        : undefined,
      setChannelDetailsPayload: isSet(object.setChannelDetailsPayload)
        ? SetChannelDetailsPayload.fromJSON(object.setChannelDetailsPayload)
        : undefined,
      slotMode: isSet(object.slotMode) ? activeChannelsModeFromJSON(object.slotMode) : undefined,
      updateChannelPayload: isSet(object.updateChannelPayload)
        ? UpdateChannelPayload.fromJSON(object.updateChannelPayload)
        : undefined,
      synthEventProgramChangePayload: isSet(object.synthEventProgramChangePayload)
        ? SynthEventProgramChangePayload.fromJSON(object.synthEventProgramChangePayload)
        : undefined,
      audioChannel: isSet(object.audioChannel) ? AudioChannel.fromJSON(object.audioChannel) : undefined,
      appPianoKey: isSet(object.appPianoKey) ? AppPianoKey.fromJSON(object.appPianoKey) : undefined,
      appPianoPedal: isSet(object.appPianoPedal) ? AppPianoPedal.fromJSON(object.appPianoPedal) : undefined,
      appRenderableEntity: isSet(object.appRenderableEntity)
        ? AppRenderableEntity.fromJSON(object.appRenderableEntity)
        : undefined,
      loadMeshDetails: isSet(object.loadMeshDetails)
        ? AppStateActions_RendererLoadMeshDetails.fromJSON(object.loadMeshDetails)
        : undefined,
      appNotificationConfig: isSet(object.appNotificationConfig)
        ? AppNotificationConfig.fromJSON(object.appNotificationConfig)
        : undefined,
      appSettings: isSet(object.appSettings) ? AppSettings.fromJSON(object.appSettings) : undefined,
      channelWithBool: isSet(object.channelWithBool) ? ChannelWithBool.fromJSON(object.channelWithBool) : undefined,
      doubleValue: isSet(object.doubleValue) ? globalThis.Number(object.doubleValue) : undefined,
      vpFileLoad: isSet(object.vpFileLoad) ? AppVPSequencerFileLoad.fromJSON(object.vpFileLoad) : undefined,
      appMidiTrack: isSet(object.appMidiTrack) ? AppMidiTrack.fromJSON(object.appMidiTrack) : undefined,
      appCommonEnvironment: isSet(object.appCommonEnvironment)
        ? AppCommonEnvironment.fromJSON(object.appCommonEnvironment)
        : undefined,
      keyboardVisualizeMappings: isSet(object.keyboardVisualizeMappings)
        ? AppKeyboardMappingVisualizeVec.fromJSON(object.keyboardVisualizeMappings)
        : undefined,
      avatarWorldPosition: isSet(object.avatarWorldPosition)
        ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromJSON(object.avatarWorldPosition)
        : undefined,
    };
  },

  toJSON(message: AppStateActions): unknown {
    const obj: any = {};
    if (message.action !== 0) {
      obj.action = appStateActions_ActionToJSON(message.action);
    }
    if (message.sourceSocketID !== undefined) {
      obj.sourceSocketID = message.sourceSocketID;
    }
    if (message.stringValue !== undefined) {
      obj.stringValue = message.stringValue;
    }
    if (message.boolValue !== undefined) {
      obj.boolValue = message.boolValue;
    }
    if (message.userDto !== undefined) {
      obj.userDto = UserDto.toJSON(message.userDto);
    }
    if (message.userClientDto !== undefined) {
      obj.userClientDto = UserClientDto.toJSON(message.userClientDto);
    }
    if (message.basicRoomDto !== undefined) {
      obj.basicRoomDto = BasicRoomDto.toJSON(message.basicRoomDto);
    }
    if (message.userUpdateCommand !== undefined) {
      obj.userUpdateCommand = UserUpdateCommand.toJSON(message.userUpdateCommand);
    }
    if (message.userDtoList !== undefined) {
      obj.userDtoList = UserDtoList.toJSON(message.userDtoList);
    }
    if (message.socketIdList !== undefined) {
      obj.socketIdList = SocketIdList.toJSON(message.socketIdList);
    }
    if (message.friendDtoList !== undefined) {
      obj.friendDtoList = FriendDtoList.toJSON(message.friendDtoList);
    }
    if (message.socketId !== undefined) {
      obj.socketId = message.socketId;
    }
    if (message.usertag !== undefined) {
      obj.usertag = message.usertag;
    }
    if (message.userid !== undefined) {
      obj.userid = message.userid;
    }
    if (message.joinedRoomData !== undefined) {
      obj.joinedRoomData = JoinedRoomData.toJSON(message.joinedRoomData);
    }
    if (message.roomChatHistory !== undefined) {
      obj.roomChatHistory = RoomChatHistory.toJSON(message.roomChatHistory);
    }
    if (message.chatMessageDto !== undefined) {
      obj.chatMessageDto = ChatMessageDto.toJSON(message.chatMessageDto);
    }
    if (message.roomSettings !== undefined) {
      obj.roomSettings = RoomSettings.toJSON(message.roomSettings);
    }
    if (message.roomFullDetails !== undefined) {
      obj.roomFullDetails = RoomFullDetails.toJSON(message.roomFullDetails);
    }
    if (message.joinRoomFailResponse !== undefined) {
      obj.joinRoomFailResponse = CommandResponse_JoinRoomFailResponse.toJSON(message.joinRoomFailResponse);
    }
    if (message.roomsList !== undefined) {
      obj.roomsList = CommandResponse_RoomsList.toJSON(message.roomsList);
    }
    if (message.joinRoomByNameRequest !== undefined) {
      obj.joinRoomByNameRequest = ServerMessage_JoinRoomByNameRequest.toJSON(message.joinRoomByNameRequest);
    }
    if (message.serverCommand !== undefined) {
      obj.serverCommand = ServerCommandDU.toJSON(message.serverCommand);
    }
    if (message.roomOwnerCommand !== undefined) {
      obj.roomOwnerCommand = RoomOwnerCommandDU.toJSON(message.roomOwnerCommand);
    }
    if (message.roomIdWithPassword !== undefined) {
      obj.roomIdWithPassword = RoomIDWithPassword.toJSON(message.roomIdWithPassword);
    }
    if (message.roomId !== undefined) {
      obj.roomId = message.roomId;
    }
    if (message.midiMessageOutputDto !== undefined) {
      obj.midiMessageOutputDto = MidiMessageOutputDto.toJSON(message.midiMessageOutputDto);
    }
    if (message.welcomeDto !== undefined) {
      obj.welcomeDto = WelcomeDto.toJSON(message.welcomeDto);
    }
    if (message.pendingFriendRequestList !== undefined) {
      obj.pendingFriendRequestList = PendingFriendRequestList.toJSON(message.pendingFriendRequestList);
    }
    if (message.kickedUsersList !== undefined) {
      obj.kickedUsersList = KickedUsersList.toJSON(message.kickedUsersList);
    }
    if (message.clientSideUserDtoList !== undefined) {
      obj.clientSideUserDtoList = ClientSideUserDtoList.toJSON(message.clientSideUserDtoList);
    }
    if (message.pendingFriendRequest !== undefined) {
      obj.pendingFriendRequest = PendingFriendRequest.toJSON(message.pendingFriendRequest);
    }
    if (message.clientMsg !== undefined) {
      obj.clientMsg = ClientMessage.toJSON(message.clientMsg);
    }
    if (message.commandResponse !== undefined) {
      obj.commandResponse = CommandResponse.toJSON(message.commandResponse);
    }
    if (message.audioSynthAction !== undefined) {
      obj.audioSynthAction = AudioSynthActions.toJSON(message.audioSynthAction);
    }
    if (message.int32Value !== undefined) {
      obj.int32Value = Math.round(message.int32Value);
    }
    if (message.uint32Value !== undefined) {
      obj.uint32Value = Math.round(message.uint32Value);
    }
    if (message.socketIdWithIn32 !== undefined) {
      obj.socketIdWithIn32 = SocketIdWithInt32.toJSON(message.socketIdWithIn32);
    }
    if (message.instrument !== undefined) {
      obj.instrument = Instrument.toJSON(message.instrument);
    }
    if (message.instrumentsList !== undefined) {
      obj.instrumentsList = InstrumentsList.toJSON(message.instrumentsList);
    }
    if (message.setChannelInstrumentPayload !== undefined) {
      obj.setChannelInstrumentPayload = SetChannelInstrumentPayload.toJSON(message.setChannelInstrumentPayload);
    }
    if (message.setChannelDetailsPayload !== undefined) {
      obj.setChannelDetailsPayload = SetChannelDetailsPayload.toJSON(message.setChannelDetailsPayload);
    }
    if (message.slotMode !== undefined) {
      obj.slotMode = activeChannelsModeToJSON(message.slotMode);
    }
    if (message.updateChannelPayload !== undefined) {
      obj.updateChannelPayload = UpdateChannelPayload.toJSON(message.updateChannelPayload);
    }
    if (message.synthEventProgramChangePayload !== undefined) {
      obj.synthEventProgramChangePayload = SynthEventProgramChangePayload.toJSON(
        message.synthEventProgramChangePayload,
      );
    }
    if (message.audioChannel !== undefined) {
      obj.audioChannel = AudioChannel.toJSON(message.audioChannel);
    }
    if (message.appPianoKey !== undefined) {
      obj.appPianoKey = AppPianoKey.toJSON(message.appPianoKey);
    }
    if (message.appPianoPedal !== undefined) {
      obj.appPianoPedal = AppPianoPedal.toJSON(message.appPianoPedal);
    }
    if (message.appRenderableEntity !== undefined) {
      obj.appRenderableEntity = AppRenderableEntity.toJSON(message.appRenderableEntity);
    }
    if (message.loadMeshDetails !== undefined) {
      obj.loadMeshDetails = AppStateActions_RendererLoadMeshDetails.toJSON(message.loadMeshDetails);
    }
    if (message.appNotificationConfig !== undefined) {
      obj.appNotificationConfig = AppNotificationConfig.toJSON(message.appNotificationConfig);
    }
    if (message.appSettings !== undefined) {
      obj.appSettings = AppSettings.toJSON(message.appSettings);
    }
    if (message.channelWithBool !== undefined) {
      obj.channelWithBool = ChannelWithBool.toJSON(message.channelWithBool);
    }
    if (message.doubleValue !== undefined) {
      obj.doubleValue = message.doubleValue;
    }
    if (message.vpFileLoad !== undefined) {
      obj.vpFileLoad = AppVPSequencerFileLoad.toJSON(message.vpFileLoad);
    }
    if (message.appMidiTrack !== undefined) {
      obj.appMidiTrack = AppMidiTrack.toJSON(message.appMidiTrack);
    }
    if (message.appCommonEnvironment !== undefined) {
      obj.appCommonEnvironment = AppCommonEnvironment.toJSON(message.appCommonEnvironment);
    }
    if (message.keyboardVisualizeMappings !== undefined) {
      obj.keyboardVisualizeMappings = AppKeyboardMappingVisualizeVec.toJSON(message.keyboardVisualizeMappings);
    }
    if (message.avatarWorldPosition !== undefined) {
      obj.avatarWorldPosition = AvatarWorldDataDto_AvatarMessageWorldPosition.toJSON(message.avatarWorldPosition);
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateActions>, I>>(base?: I): AppStateActions {
    return AppStateActions.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateActions>, I>>(object: I): AppStateActions {
    const message = createBaseAppStateActions();
    message.action = object.action ?? 0;
    message.sourceSocketID = object.sourceSocketID ?? undefined;
    message.stringValue = object.stringValue ?? undefined;
    message.boolValue = object.boolValue ?? undefined;
    message.userDto = (object.userDto !== undefined && object.userDto !== null)
      ? UserDto.fromPartial(object.userDto)
      : undefined;
    message.userClientDto = (object.userClientDto !== undefined && object.userClientDto !== null)
      ? UserClientDto.fromPartial(object.userClientDto)
      : undefined;
    message.basicRoomDto = (object.basicRoomDto !== undefined && object.basicRoomDto !== null)
      ? BasicRoomDto.fromPartial(object.basicRoomDto)
      : undefined;
    message.userUpdateCommand = (object.userUpdateCommand !== undefined && object.userUpdateCommand !== null)
      ? UserUpdateCommand.fromPartial(object.userUpdateCommand)
      : undefined;
    message.userDtoList = (object.userDtoList !== undefined && object.userDtoList !== null)
      ? UserDtoList.fromPartial(object.userDtoList)
      : undefined;
    message.socketIdList = (object.socketIdList !== undefined && object.socketIdList !== null)
      ? SocketIdList.fromPartial(object.socketIdList)
      : undefined;
    message.friendDtoList = (object.friendDtoList !== undefined && object.friendDtoList !== null)
      ? FriendDtoList.fromPartial(object.friendDtoList)
      : undefined;
    message.socketId = object.socketId ?? undefined;
    message.usertag = object.usertag ?? undefined;
    message.userid = object.userid ?? undefined;
    message.joinedRoomData = (object.joinedRoomData !== undefined && object.joinedRoomData !== null)
      ? JoinedRoomData.fromPartial(object.joinedRoomData)
      : undefined;
    message.roomChatHistory = (object.roomChatHistory !== undefined && object.roomChatHistory !== null)
      ? RoomChatHistory.fromPartial(object.roomChatHistory)
      : undefined;
    message.chatMessageDto = (object.chatMessageDto !== undefined && object.chatMessageDto !== null)
      ? ChatMessageDto.fromPartial(object.chatMessageDto)
      : undefined;
    message.roomSettings = (object.roomSettings !== undefined && object.roomSettings !== null)
      ? RoomSettings.fromPartial(object.roomSettings)
      : undefined;
    message.roomFullDetails = (object.roomFullDetails !== undefined && object.roomFullDetails !== null)
      ? RoomFullDetails.fromPartial(object.roomFullDetails)
      : undefined;
    message.joinRoomFailResponse = (object.joinRoomFailResponse !== undefined && object.joinRoomFailResponse !== null)
      ? CommandResponse_JoinRoomFailResponse.fromPartial(object.joinRoomFailResponse)
      : undefined;
    message.roomsList = (object.roomsList !== undefined && object.roomsList !== null)
      ? CommandResponse_RoomsList.fromPartial(object.roomsList)
      : undefined;
    message.joinRoomByNameRequest =
      (object.joinRoomByNameRequest !== undefined && object.joinRoomByNameRequest !== null)
        ? ServerMessage_JoinRoomByNameRequest.fromPartial(object.joinRoomByNameRequest)
        : undefined;
    message.serverCommand = (object.serverCommand !== undefined && object.serverCommand !== null)
      ? ServerCommandDU.fromPartial(object.serverCommand)
      : undefined;
    message.roomOwnerCommand = (object.roomOwnerCommand !== undefined && object.roomOwnerCommand !== null)
      ? RoomOwnerCommandDU.fromPartial(object.roomOwnerCommand)
      : undefined;
    message.roomIdWithPassword = (object.roomIdWithPassword !== undefined && object.roomIdWithPassword !== null)
      ? RoomIDWithPassword.fromPartial(object.roomIdWithPassword)
      : undefined;
    message.roomId = object.roomId ?? undefined;
    message.midiMessageOutputDto = (object.midiMessageOutputDto !== undefined && object.midiMessageOutputDto !== null)
      ? MidiMessageOutputDto.fromPartial(object.midiMessageOutputDto)
      : undefined;
    message.welcomeDto = (object.welcomeDto !== undefined && object.welcomeDto !== null)
      ? WelcomeDto.fromPartial(object.welcomeDto)
      : undefined;
    message.pendingFriendRequestList =
      (object.pendingFriendRequestList !== undefined && object.pendingFriendRequestList !== null)
        ? PendingFriendRequestList.fromPartial(object.pendingFriendRequestList)
        : undefined;
    message.kickedUsersList = (object.kickedUsersList !== undefined && object.kickedUsersList !== null)
      ? KickedUsersList.fromPartial(object.kickedUsersList)
      : undefined;
    message.clientSideUserDtoList =
      (object.clientSideUserDtoList !== undefined && object.clientSideUserDtoList !== null)
        ? ClientSideUserDtoList.fromPartial(object.clientSideUserDtoList)
        : undefined;
    message.pendingFriendRequest = (object.pendingFriendRequest !== undefined && object.pendingFriendRequest !== null)
      ? PendingFriendRequest.fromPartial(object.pendingFriendRequest)
      : undefined;
    message.clientMsg = (object.clientMsg !== undefined && object.clientMsg !== null)
      ? ClientMessage.fromPartial(object.clientMsg)
      : undefined;
    message.commandResponse = (object.commandResponse !== undefined && object.commandResponse !== null)
      ? CommandResponse.fromPartial(object.commandResponse)
      : undefined;
    message.audioSynthAction = (object.audioSynthAction !== undefined && object.audioSynthAction !== null)
      ? AudioSynthActions.fromPartial(object.audioSynthAction)
      : undefined;
    message.int32Value = object.int32Value ?? undefined;
    message.uint32Value = object.uint32Value ?? undefined;
    message.socketIdWithIn32 = (object.socketIdWithIn32 !== undefined && object.socketIdWithIn32 !== null)
      ? SocketIdWithInt32.fromPartial(object.socketIdWithIn32)
      : undefined;
    message.instrument = (object.instrument !== undefined && object.instrument !== null)
      ? Instrument.fromPartial(object.instrument)
      : undefined;
    message.instrumentsList = (object.instrumentsList !== undefined && object.instrumentsList !== null)
      ? InstrumentsList.fromPartial(object.instrumentsList)
      : undefined;
    message.setChannelInstrumentPayload =
      (object.setChannelInstrumentPayload !== undefined && object.setChannelInstrumentPayload !== null)
        ? SetChannelInstrumentPayload.fromPartial(object.setChannelInstrumentPayload)
        : undefined;
    message.setChannelDetailsPayload =
      (object.setChannelDetailsPayload !== undefined && object.setChannelDetailsPayload !== null)
        ? SetChannelDetailsPayload.fromPartial(object.setChannelDetailsPayload)
        : undefined;
    message.slotMode = object.slotMode ?? undefined;
    message.updateChannelPayload = (object.updateChannelPayload !== undefined && object.updateChannelPayload !== null)
      ? UpdateChannelPayload.fromPartial(object.updateChannelPayload)
      : undefined;
    message.synthEventProgramChangePayload =
      (object.synthEventProgramChangePayload !== undefined && object.synthEventProgramChangePayload !== null)
        ? SynthEventProgramChangePayload.fromPartial(object.synthEventProgramChangePayload)
        : undefined;
    message.audioChannel = (object.audioChannel !== undefined && object.audioChannel !== null)
      ? AudioChannel.fromPartial(object.audioChannel)
      : undefined;
    message.appPianoKey = (object.appPianoKey !== undefined && object.appPianoKey !== null)
      ? AppPianoKey.fromPartial(object.appPianoKey)
      : undefined;
    message.appPianoPedal = (object.appPianoPedal !== undefined && object.appPianoPedal !== null)
      ? AppPianoPedal.fromPartial(object.appPianoPedal)
      : undefined;
    message.appRenderableEntity = (object.appRenderableEntity !== undefined && object.appRenderableEntity !== null)
      ? AppRenderableEntity.fromPartial(object.appRenderableEntity)
      : undefined;
    message.loadMeshDetails = (object.loadMeshDetails !== undefined && object.loadMeshDetails !== null)
      ? AppStateActions_RendererLoadMeshDetails.fromPartial(object.loadMeshDetails)
      : undefined;
    message.appNotificationConfig =
      (object.appNotificationConfig !== undefined && object.appNotificationConfig !== null)
        ? AppNotificationConfig.fromPartial(object.appNotificationConfig)
        : undefined;
    message.appSettings = (object.appSettings !== undefined && object.appSettings !== null)
      ? AppSettings.fromPartial(object.appSettings)
      : undefined;
    message.channelWithBool = (object.channelWithBool !== undefined && object.channelWithBool !== null)
      ? ChannelWithBool.fromPartial(object.channelWithBool)
      : undefined;
    message.doubleValue = object.doubleValue ?? undefined;
    message.vpFileLoad = (object.vpFileLoad !== undefined && object.vpFileLoad !== null)
      ? AppVPSequencerFileLoad.fromPartial(object.vpFileLoad)
      : undefined;
    message.appMidiTrack = (object.appMidiTrack !== undefined && object.appMidiTrack !== null)
      ? AppMidiTrack.fromPartial(object.appMidiTrack)
      : undefined;
    message.appCommonEnvironment = (object.appCommonEnvironment !== undefined && object.appCommonEnvironment !== null)
      ? AppCommonEnvironment.fromPartial(object.appCommonEnvironment)
      : undefined;
    message.keyboardVisualizeMappings =
      (object.keyboardVisualizeMappings !== undefined && object.keyboardVisualizeMappings !== null)
        ? AppKeyboardMappingVisualizeVec.fromPartial(object.keyboardVisualizeMappings)
        : undefined;
    message.avatarWorldPosition = (object.avatarWorldPosition !== undefined && object.avatarWorldPosition !== null)
      ? AvatarWorldDataDto_AvatarMessageWorldPosition.fromPartial(object.avatarWorldPosition)
      : undefined;
    return message;
  },
};

function createBaseAppStateActions_RendererLoadMeshDetails(): AppStateActions_RendererLoadMeshDetails {
  return { filePath: "", meshName: "" };
}

export const AppStateActions_RendererLoadMeshDetails = {
  encode(message: AppStateActions_RendererLoadMeshDetails, writer: _m0.Writer = _m0.Writer.create()): _m0.Writer {
    if (message.filePath !== "") {
      writer.uint32(10).string(message.filePath);
    }
    if (message.meshName !== "") {
      writer.uint32(18).string(message.meshName);
    }
    return writer;
  },

  decode(input: _m0.Reader | Uint8Array, length?: number): AppStateActions_RendererLoadMeshDetails {
    const reader = input instanceof _m0.Reader ? input : _m0.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseAppStateActions_RendererLoadMeshDetails();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }

          message.filePath = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }

          message.meshName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },

  fromJSON(object: any): AppStateActions_RendererLoadMeshDetails {
    return {
      filePath: isSet(object.filePath) ? globalThis.String(object.filePath) : "",
      meshName: isSet(object.meshName) ? globalThis.String(object.meshName) : "",
    };
  },

  toJSON(message: AppStateActions_RendererLoadMeshDetails): unknown {
    const obj: any = {};
    if (message.filePath !== "") {
      obj.filePath = message.filePath;
    }
    if (message.meshName !== "") {
      obj.meshName = message.meshName;
    }
    return obj;
  },

  create<I extends Exact<DeepPartial<AppStateActions_RendererLoadMeshDetails>, I>>(
    base?: I,
  ): AppStateActions_RendererLoadMeshDetails {
    return AppStateActions_RendererLoadMeshDetails.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppStateActions_RendererLoadMeshDetails>, I>>(
    object: I,
  ): AppStateActions_RendererLoadMeshDetails {
    const message = createBaseAppStateActions_RendererLoadMeshDetails();
    message.filePath = object.filePath ?? "";
    message.meshName = object.meshName ?? "";
    return message;
  },
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin ? T
  : T extends globalThis.Array<infer U> ? globalThis.Array<DeepPartial<U>>
  : T extends ReadonlyArray<infer U> ? ReadonlyArray<DeepPartial<U>>
  : T extends {} ? { [K in keyof T]?: DeepPartial<T[K]> }
  : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function longToNumber(long: Long): number {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is larger than Number.MAX_SAFE_INTEGER");
  }
  if (long.lt(globalThis.Number.MIN_SAFE_INTEGER)) {
    throw new globalThis.Error("Value is smaller than Number.MIN_SAFE_INTEGER");
  }
  return long.toNumber();
}

if (_m0.util.Long !== Long) {
  _m0.util.Long = Long as any;
  _m0.configure();
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}
