import { Box, notificationService } from "@hope-ui/solid";
import { Component, Setter } from "solid-js";
import { Portal } from "solid-js/web";
import { FILE_UPLOAD } from "~/util/const.common";
import ImageUploaderModal from "~/components/image-uploader";

type UserProfileBackgroundImageUploaderProps = {
  onSuccess?: () => void
  setShow: Setter<boolean>
}

const UserProfileBackgroundImageUploader: Component<UserProfileBackgroundImageUploaderProps> = (props) => {
  return (<>
    <Portal mount={document.body}>
      <Box position={"absolute"} top={0}>
        <ImageUploaderModal
          path={`/api/users/members/upload-profile-background-image`}
          fileSizeLimit={FILE_UPLOAD.MaxProfileBGImageFileSize}
          dimensions={{
            width: FILE_UPLOAD.MaxBackgroundImageWidth,
            height: FILE_UPLOAD.MaxBackgroundImageHeight,
          }}
          onClose={() => props.setShow(false)}
          headerLabel={"Profile Background Image Upload"}
          onSaveError={(error) => {
            notificationService.show({
              status: "danger",
              title: `Failed to upload image`,
              description: error.message
            })
          }}
          onSave={async (response) => {
            switch (response) {
              case "Success":
                notificationService.show({
                  status: "success",
                  title: "Profile Background Image Uploaded!"
                })
                if (props.onSuccess) props.onSuccess()
                break;
              case "PermissionDenied":
                notificationService.show({
                  status: "danger",
                  title: "You don't have permission to upload images."
                })
                break;
              default:
                notificationService.show({
                  status: "danger",
                  title: `Failed to upload image`,
                  description: response.toString()
                })
                break;
            }
          }}
        />
      </Box>
    </Portal>
  </>)
}

export default UserProfileBackgroundImageUploader