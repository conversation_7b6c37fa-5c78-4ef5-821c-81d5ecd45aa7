import { <PERSON>, Center, <PERSON><PERSON><PERSON>on, Container, Divider, HStack, Icon, Image, Input, InputGroup, InputLeftElement, Kbd, SkeletonCircle, Text, VStack } from "@hope-ui/solid";
import { createDraggable } from "@thisbeyond/solid-dnd";
import clsx from "clsx";
import isEqual from "lodash-es/isEqual";
import Mousetrap from "mousetrap";
import { BiSolidSearch } from 'solid-icons/bi';
import { batch, createEffect, createSignal, on, onCleanup, onMount, Setter, Show, Suspense } from "solid-js";
import { useService } from "solid-services";
import { Transition } from "solid-transition-group";
import inputSFX from "~/directives/input.directive";
import { Instrument, SetChannelInstrumentType } from "~/proto/midi-renditions";
import style from '~/sass/instruments-selection.list.module.sass';
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import { createWindowSize } from "@solid-primitives/resize-observer";

//@ts-ignore
import { VirtualContainer, VirtualItemProps } from "@minht11/solid-virtual-container";
import { FaSolidXmark } from "solid-icons/fa";
import ResourceService from "~/services/resource.service";
import { is_colliding as isColliding } from "~/util/helpers";
import MediaQueryService from "~/services/media-query.service";

const ULWidthDefaultScreen = "300px";
const ULWidthSmallScreen = "calc(98dvw - var(--sidebarListWidth))";
const ContainerMarginTop = 5;
const CardHeight = 45;
const CardPadding = 5;
const CardMarginLeftDefault = 70;
const SearchFilterHeight = 40;
const SearchFilterPaddingBottom = 0;

const [ULWidth, setULWidth] = createSignal(ULWidthDefaultScreen);

const ListItem = (props: VirtualItemProps<Instrument>) => {
  const audioService = useService(AudioService);
  const resourceService = useService(ResourceService);
  const mediaQueryService = useService(MediaQueryService);

  let clickInstrumentTimeout = -1;
  const [instrument, setInstrument] = createSignal<Instrument>(props.items[props.index]);
  const [index, setIndex] = createSignal("");
  const [loadedColor, setLoadedColor] = createSignal("gray");
  const [isLoaded, setIsLoaded] = createSignal(false);
  const onGetInstrument = () => instrument();
  const getInstrumentName = () => instrument().name;
  const draggable = createDraggable(`instrument-draggable-${props.index}`, { onGetInstrument });
  const [instrumentImagePath] = resourceService().instrumentImageFetch(getInstrumentName);

  createEffect(async () => {
    let currentInstrument = instrument();
    let newInstrument = props.items[props.index];

    if (currentInstrument != newInstrument) {
      setInstrument(newInstrument);
    }

    let _isLoaded = audioService().isInstrumentInChannel(newInstrument.bank, newInstrument.preset);
    const _loadedColor = _isLoaded ? "$accent1" : "gray";

    setIsLoaded(_isLoaded);
    setLoadedColor(_loadedColor);
    setIndex(`${newInstrument.bank}-${newInstrument.preset}`);
  });

  onCleanup(() => {
    window.clearTimeout(clickInstrumentTimeout);
  });

  return (<>
    {/* @ts-ignore */}
    <div
      style={props.style}
      class={[style.instrumentCardParent, "unselectable"].join(" ")}
      tabIndex={props.tabIndex}
      role="listitem"
    >
      <Box
        __tooltip_placement={mediaQueryService().isSmallScreen() ? "left" : "top"}
        __tooltip_show_arrow={!mediaQueryService().isSmallScreen()}
        __tooltip_title={
          <Box paddingBottom={10}>
            <Box fontWeight={"bold"} as="h3">{instrument().displayName}</Box>
            <Suspense>
              <Image
                borderRadius="$full"
                border={`3px solid ${loadedColor()}`}
                position={"absolute"} top={8} right={10}
                boxSize="35px" src={instrumentImagePath()} objectFit="cover"
              />
            </Suspense>
            <HStack spacing={"$2"} color={"$neutral11"}>
              <Box>Bank: {instrument().bank}</Box>
              <Box>Preset: {instrument().preset}</Box>
            </HStack>
            <Show when={isLoaded()} fallback={
              <Box
                color={"$neutral11"}
                marginTop={10}
              >Click to add to the instrument dock. <p>Or you can click and drag to one of the channel slots.</p></Box>
            }>
              <Box marginTop={10}>
                Loaded in channel:&NonBreakingSpace;
                <b>{audioService().getChannelFromInstrument(instrument().bank, instrument().preset)}</b>
              </Box>
            </Show>
            <Divider thickness={"2px"} marginBottom={10} marginTop={5} />
            <VStack w="100%" alignItems={"flex-start"} spacing={"$2"}>
              <Box>Hold <Kbd fontSize={10} padding={2}>shift</Kbd> and then click to add to the next <b>inactive</b> channel.</Box>
              <Box>Hold <Kbd fontSize={10} padding={2}>ctrl</Kbd> and then click to add to the next <b>empty</b> channel.</Box>
            </VStack>
          </Box>
        }
        width={"100%"}
        class={[style.instrumentSelectionCard].join(" ")}
        bg="$primaryAlpha"
        border={`solid 2px ${loadedColor()}`}
        marginBottom={`4px`}
        marginLeft={`${CardMarginLeftDefault}px`}
        height={`${CardHeight}px`}
        ref={draggable.ref}
        {...draggable.dragActivators}
        onClick={(evt: MouseEvent) => {
          if (evt.button == 0) {
            window.clearTimeout(clickInstrumentTimeout);
            clickInstrumentTimeout = window.setTimeout(() => {
              if (draggable.isActiveDraggable) return;

              const inst = instrument();
              let type: SetChannelInstrumentType = SetChannelInstrumentType.Add;
              if (evt.shiftKey) type = SetChannelInstrumentType.NextInactive;
              if (evt.ctrlKey) type = SetChannelInstrumentType.NextEmpty;
              let channel = type == SetChannelInstrumentType.Add ? audioService().primaryChannel() : 0;

              // analyticsService().trackEvent(
              //   analyticsService().GET_EVENT_IDS().INSTRUMENT_SELECTION,
              //   inst.displayName, 1
              // );

              audioService().setInstrumentOnChannel(channel, inst.bank, inst.preset, type);
            }, 100);
          }
        }}
      >
        <HStack spacing={"$4"}>
          <Suspense
            fallback={<SkeletonCircle
              position={"absolute"} bottom={3} left={5} size="$10"
            />}
          >
            <Image
              class={clsx([style.instrumentImage, "unselectable", "undraggable"])}
              fallback={<SkeletonCircle position={"absolute"} bottom={3} left={5} size="$10" />}
              src={instrumentImagePath()!}
              alt={`${instrument().displayName} instrument`}
            />
          </Suspense>

          <span class={style.instrumentIndex}>{index()}</span>

          <Box class={style.instrumentContainer}>
            <Text
              noOfLines={1}
              size="2xl"
              class={clsx([style.instrumentTitle, "draggable"])}
            >{instrument().displayName}</Text>
          </Box>

        </HStack>
      </Box>
    </div>
  </>);
};

const SearchFilter = (props: { instruments: Instrument[], setFilteredInstruments: Setter<Instrument[] | null>; }) => {
  const [filteredText, setFilteredText] = createSignal("");
  const displayService = useService(DisplaysService);

  const handleInput = (event: InputEvent) => {
    const text: string = (event.target as any).value;
    setFilteredText(text.toLowerCase());
  };

  createEffect(() => {
    const text = filteredText();
    const instruments =
      props.instruments.filter(x =>
        x.displayName.toLowerCase().includes(text) ||
        x.name.toLowerCase().includes(text) ||
        `${x.bank}-${x.preset}`.toLowerCase().includes(text)
      );

    let instsMatch = JSON.stringify(props.instruments) != JSON.stringify(instruments);
    if (instsMatch) props.setFilteredInstruments(instruments);
    else props.setFilteredInstruments(null);
  });

  return (
    <InputGroup
      pointerEvents={"all"}
      position={"relative"}
    >
      <InputLeftElement pointerEvents="none">
        <Icon
          marginBottom={8}
          color="$neutral8" as={BiSolidSearch} />
      </InputLeftElement>
      <Input
        autocomplete="off"
        width={`calc(${ULWidth()} - 45px)`}
        paddingLeft={30}
        minHeight={"30px !important"}
        border={"solid 2px $primary1 !important"}
        placeholder="Search by name or bank-preset"
        onInput={handleInput}
        ref={(el: HTMLElement) => inputSFX(el)}
        _hover={{ border: "solid 2px $neutral-400 !important" }}
        _focus={{ border: "solid 2px $neutral-100 !important" }}
        background={"$primary1 !important"}
        transition={"width 0.2s"}
      />
      <Center>
        <CloseButton
          icon={<FaSolidXmark />}
          onmousedown={() => { displayService().setDisplay("INSTRUMENT_SELECTION", false); }}
          marginLeft={5} marginRight={5}
        />
      </Center>
    </InputGroup>
  );
};

const InstrumentsList = () => {
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const mediaQueryService = useService(MediaQueryService);

  const [_ms, setMs] = createSignal<Mousetrap.MousetrapInstance>();
  const [loadedItems, setLoadedItems] = createSignal<Instrument[]>([], { equals: isEqual });
  const [items, setItems] = createSignal<Instrument[]>([], { equals: isEqual });
  const [filteredItems, setFilteredItems] = createSignal<Instrument[] | null>(null);
  const [isCollidingWithDock, setIsCollidingWithDock] = createSignal(false);
  const size = createWindowSize();
  let scrollTargetElement!: HTMLDivElement;

  onMount(() => {
    let onEscape = () => {
      displayService().setDisplay("INSTRUMENT_SELECTION", false);
    };

    let ms = new Mousetrap(document.body);
    ms.bind("escape", onEscape);
    setMs(ms);
  });

  onCleanup(() => _ms()?.reset());

  createEffect(() => {
    setULWidth(mediaQueryService().isSmallScreen() ? ULWidthDefaultScreen : ULWidthSmallScreen);
  });

  createEffect(on(() => [size.width, size.height, displayService().getDisplay("INSTRUMENT_DOCK")], () => {
    if (displayService().getDisplay("INSTRUMENT_DOCK")) {
      let result = isColliding(
        document.querySelector("[id='instrument-dock-container']"),
        scrollTargetElement
      );
      setIsCollidingWithDock(result);
    } else {
      setIsCollidingWithDock(false);
    }
  }));

  createEffect(() => {
    let instruments = audioService().instruments() || [];
    batch(() => {
      setLoadedItems(instruments);
      setItems(instruments);
    });
  });

  createEffect(() => {
    let filtered = filteredItems();

    if (filtered != null) {
      setItems(filtered);
    } else {
      setItems(loadedItems);
    }
  });

  return (<>
    <Transition name="fade">
      {displayService().getDisplay("INSTRUMENT_SELECTION") &&
        <Container
          position={"absolute"} top={`${ContainerMarginTop}px`} right={`0px`}>
          <Box position={"absolute"} top={0} right={0} zIndex={5} height={`${SearchFilterHeight}px`}>
            <SearchFilter instruments={loadedItems()} setFilteredInstruments={setFilteredItems} />
          </Box>
          <div
            class={style.instrumentSelectionList}
            ref={scrollTargetElement}
            id="instrument-selection-list-container"
            style={{
              top: `${SearchFilterHeight + SearchFilterPaddingBottom}px`,
              height: `calc(100vh - 15px - ${isCollidingWithDock() ? "65px" : "0px"} - var(--bottomBarHeight, 0px) - ${SearchFilterHeight - SearchFilterPaddingBottom + ContainerMarginTop * 2}px)`,
            }}
          >
            <Box
              width={ULWidth()}
              transition={"width 0.2s"}
            >
              <VirtualContainer
                items={items()}
                scrollTarget={scrollTargetElement}
                itemSize={{ height: CardHeight + CardPadding }}
              >
                {ListItem}
              </VirtualContainer>
            </Box>
          </div>
        </Container>
      }
    </Transition>
  </>);
};

export default InstrumentsList;