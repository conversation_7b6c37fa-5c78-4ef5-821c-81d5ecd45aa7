import { <PERSON><PERSON>, Center, VStack, <PERSON>ing, InputGroup, Input, Divider } from "@hope-ui/solid";
import { createSignal, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AlreadyHaveAccount_Anchor, CreateAccount_Anchor, EnterAsGuest_Anchor } from "../anchors";
import { CustomInput } from "./custom-input";
import style from '~/sass/login.page.module.sass';
import { USER_INPUT } from "~/util/const.common";
import I18nService from "~/services/i18n.service";
import LoginService, { CurrentForm } from "~/services/login.service";
import { z } from 'zod';
import { useForm } from "~/util/validation";
import notificationService from "~/services/notification.service";
import { createSimpleEmailFormData, delay, FormHelper } from "~/util/helpers";
import { useAction, useSubmission } from "@solidjs/router";
import { forgotPassword } from "~/lib";

const inputSettings = z.object({
  email: z.string().email().max(USER_INPUT.MaxEmailLength),
});

const ForgotPasswordForm = () => {
  const [loading, setLoading] = createSignal(false);
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);
  const forgotPasswordAction = useAction(forgotPassword);
  const forgotPasswordSubmission = useSubmission(forgotPassword);

  let inputElement!: HTMLButtonElement;
  let emailInput!: HTMLInputElement;

  const { validate, formSubmit } = useForm({
    errorClass: "error-input"
  });

  const [fields, setFields] = createStore({
    email: "",
  });

  onMount(() => {
    window.history.pushState(null, '', "/forgot-password");
    validate(emailInput, () => [FormHelper.isNotEmpty]);
  });

  const isFormValid = () => inputSettings.safeParse(fields).success;

  const fn = (_: HTMLFormElement) => {
    inputSettings
      .parseAsync(fields)
      .then((result) => {
        setLoading(true);
        return forgotPasswordAction(
          createSimpleEmailFormData(result.email)
        );
      })
      .then(() => {
        notificationService.show({
          type: "success",
          title: "Forgot Password Complete",
          description: i18nService().t_common("generalMessages.forgotPasswordSuccess"),
          duration: 1000 * 20
        });
        loginService().updateForm(CurrentForm.Main);
      })
      .catch(async (err) => {
        notificationService.show({
          type: "danger",
          title: "Forgot Password form Failed.",
          closable: true,
          description: err || "Something went wrong. Please try again later.",
          duration: 1000 * 30
        });
        await delay(1000);
        setLoading(false);
      }).finally(() => {
        setLoading(false);
      });
  };

  return (<>
    <form use:formSubmit={fn}>
      <Center class={style.eformFadeIn}>
        <VStack class={style.eloginFormContainer}>
          <Heading size="2xl" mb={"10px"}>Forgot Password</Heading>
          <InputGroup>
            <VStack spacing={"$4"} class={style.eloginFormInputContainer} alignItems="baseline">
              <CustomInput>
                <Input disabled={loading()} invalid={false} ref={emailInput} name="email"
                  maxLength={USER_INPUT.MaxEmailLength}
                  required placeholder="email" type="email"
                  /* @ts-ignore */
                  onInput={(e) => setFields("email", e.target.value)}
                />
              </CustomInput>
            </VStack>
          </InputGroup>
          <Button
            disabled={!isFormValid()}
            loading={loading()}
            ref={inputElement}
            type="submit"
            width="100%" mt={"10px"} mb={"10px"} variant="ghost">Submit</Button>
          <Divider />
          <VStack mt={"10px"}>
            <CreateAccount_Anchor />
            <EnterAsGuest_Anchor />
            <AlreadyHaveAccount_Anchor />
          </VStack>
        </VStack>
      </Center>
    </form>
  </>);
};

export default ForgotPasswordForm;