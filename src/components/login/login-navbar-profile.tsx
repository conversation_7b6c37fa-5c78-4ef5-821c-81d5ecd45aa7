import { Box, Button, Center, Divider, HStack, Skeleton, VStack } from "@hope-ui/solid";
import { useAction } from "@solidjs/router";
import Dismiss from "solid-dismiss";
import { createEffect, createSignal, lazy, Match, onMount, Show, Suspense, Switch } from "solid-js";
import { useService } from "solid-services";
import { ApiUserRecordProvider } from "~/contexts/user.context";
import { login } from "~/lib";
import { rolesFromJSON, UserClientDto, UserDto } from "~/proto/user-renditions";
import AppService from "~/services/app.service";
import LoginService from "~/services/login.service";
import { createLoginFormData } from "~/util/helpers";
import MotionFadeIn from "../motion/motion.fade-in";
import { Portal } from "solid-js/web";

const UserProfileImage = lazy(() => import('~/components/user-profile-image'));
const LoginForm = lazy(() => import('~/components/login/login.form'));

function handleLoginSuccess(appService: () => ReturnType<typeof AppService>):
  ((usertag?: string, username?: string, roles?: string[]) => void) | undefined {
  return (usertag, username, roles) => {
    appService().clearClient();
    appService().onClientLoaded(UserClientDto.create({
      userDto: UserDto.create({ usertag, username, roles: (roles ?? []).map(rolesFromJSON) }),
    }));
  };
}

const LoginNavbarGuest = () => {
  const currentPath = window.location.pathname;
  const appService = useService(AppService);

  return (<>
    <Suspense>
      <LoginForm
        hideMetaAnchors
        insidePopup
        targetPathAfterLogin={currentPath}
        onLoginSuccess={handleLoginSuccess(appService)}
      />
    </Suspense>
  </>);
};

const LoginNavbarMember = () => {
  const appService = useService(AppService);
  const loginService = useService(LoginService);
  const loginAction = useAction(login);

  return (<>
    <Center>
      <UserProfileImage
        width={"50% !important"}
        height={"10% !important"}
        disableBorder
        borderWidth="2px"
      />
    </Center>
    <Divider mt={5} />
    <VStack w="100%" marginTop={"$1"}>
      <Button w="100%" size={"sm"} onClick={() =>
        loginService().logout(false)
          // Log back in as a guest
          .then(() => loginAction(createLoginFormData("", undefined, true)))
          .then((res) => handleLoginSuccess(appService)?.(res?.usertag, res?.username, res?.roles))
      }>Logout</Button>
    </VStack>
  </>);
};

const LoginNavbarForm = () => {
  const appService = useService(AppService);
  const loginService = useService(LoginService);

  return (<>
    <Show
      fallback={<LoginNavbarGuest />}
      when={loginService().currentLoggedInUsername() && appService().isClientMember()}
    >
      <LoginNavbarMember />
    </Show>
  </>);
};

const LoginNavbarProfile = () => {
  const appService = useService(AppService);
  const loginService = useService(LoginService);
  const user = () => appService().client().toApiUserDto();
  const isLoggedIn = () => loginService().loggedIn() && appService().isClientMember();
  const tooltip = () => isLoggedIn() ? `Welcome, ${appService().client().usertag}` : "Click to login";
  const [showForm, setShowForm] = createSignal(false);

  let elementRef!: HTMLDivElement;

  return (
    <>
      <Show
        fallback={<Skeleton w="180px" h="40px" />}
        when={user()?.usertag}
      >
        <MotionFadeIn>
          <ApiUserRecordProvider value={{ default: user() }}>
            <HStack
              spacing={"$2"}
              right={20}
              minWidth={83}
              height={40}
            >
              <Box>{appService().isClientMember() ? appService().client().usertag : "guest"}</Box>
              <Suspense>
                <UserProfileImage
                  tooltip={tooltip()}
                  tooltipPlacement="bottom"
                  width={40}
                  minWidth={40}
                  borderWidth="2px"
                  borderRadius={"50% !important"}
                  onClick={() => setShowForm(true)}
                  onRef={(element) => elementRef = element}
                />
              </Suspense>
            </HStack>
            <Portal mount={document.body}>
              <Dismiss
                open={showForm}
                setOpen={setShowForm}
                deadMenuButton
                removeScrollbar
                closeWhenScrolling
                focusElementOnOpen="menuPopup"
                menuButton={elementRef}
              >
                <Box
                  w={isLoggedIn() ? 175 : 255}
                  border={"solid 2px $neutral11"}
                  borderRadius={5}
                  padding={"$2"}
                  position={"absolute"}
                  right={0}
                  top={60}
                  zIndex={"100"}
                  background={"$primaryDark1"}
                  overflow={"hidden"}
                >
                  <LoginNavbarForm />
                </Box>
              </Dismiss>
            </Portal>
          </ApiUserRecordProvider>
        </MotionFadeIn>
      </Show>
    </>
  );
};

export default LoginNavbarProfile;