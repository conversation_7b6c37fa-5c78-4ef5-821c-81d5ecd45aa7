import { Box, Button, Center, HStack, Image, Spinner, Text, VStack } from "@hope-ui/solid";
import { useSearchParams } from "@solidjs/router";
import Mousetrap from "mousetrap";
import { createEffect, createResource, createSignal, onCleanup, onMount, Suspense } from "solid-js";
import { useService } from "solid-services";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import { getUserProfileImage } from "~/server/general.api";
import I18nService from "~/services/i18n.service";
import LoginService from "~/services/login.service";
import ResourceService from "~/services/resource.service";

export default function LoggedInForm() {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = createSignal(false);
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);
  const resourceService = useService(ResourceService);
  const [usertag, setUserTag] = createSignal(loginService().currentLoggedInUsername()?.usertag);
  const [userProfileImageSource] = createResource(usertag, (tag) => getUserProfileImage(tag));
  let trap: Mousetrap.MousetrapInstance | undefined = undefined;

  const onEnter = async () => {
    await loginService().onEnterAppLoadingAfterLogin(searchParams.roomName as string);
  };

  onMount(async () => {
    window.history.pushState(null, '', "/");
    trap = new Mousetrap(document.body);
    trap.bind("enter", onEnter);
  });

  createEffect(() => {
    setUserTag(loginService().currentLoggedInUsername()?.usertag);
  });

  onCleanup(() => trap?.unbind("enter"));

  const onAvatarClick = () => {
    loginService().tryLogout();
  };

  return (<>
    <VStack spacing={"$1"}>
      <Text zIndex={2} fontFamily="Now" fontSize={"$5xl"} marginBottom={-3}>PianoRhythm</Text>
      <Center position={"relative"}>
        <Button
          fontFamily="Now"
          ref={(el: HTMLElement) => buttonSFX(el)}
          loading={loading()}
          fontSize={"18px"}
          border={"solid 2px $neutral11"}
          paddingTop={"20px"}
          paddingBottom={"20px"}
          paddingLeft={"40px"}
          paddingRight={"40px"}
          variant="ghost"
          onmousedown={onEnter}
          transition={"all 0.1s ease-in-out"}
          textTransform={"uppercase"}
          background={"$primaryDark2"}
          _hover={{
            color: "white",
            background: "primary.50",
            border: "solid 2px $neutral12",
            fontSize: "20px"
          }}
          clipPath={"inset(0 0 0 0)"}
        >
          <Box class="circle-effect" border={"15px solid white"}></Box>
          {i18nService().t_loginPage("guestForm.buttons.enter")}
        </Button>
      </Center>

      <HStack mt={"20px"} spacing={"$1"}>
        <Text fontFamily="Now" color={"$neutral11"}>Welcome back, <Text as="b" color={"lightgray"}>{loginService().currentLoggedInUsername()?.username}</Text></Text>
        <Suspense fallback={<Spinner />}>
          <Image
            __tooltip_title={"Click to logout!"}
            __tooltip_placement={"right"}
            fallbackSrc={resourceService().defaultProfileImage()}
            src={userProfileImageSource()}
            cursor={"pointer"}
            w={"40px"}
            marginBottom={"5px"}
            borderRadius={"50%"}
            border={"2px solid $neutral6"}
            _hover={{ transform: "scale(1.1)", border: "2px solid $neutral8" }}
            transition={"transform 0.2s ease"}
            onClick={onAvatarClick}
          />
        </Suspense>
      </HStack>

      {/* {(COMMON.IS_WEB_APP) &&
        <ButtonGroup
          class={style.mainFormButtons}
          variant="outlined"
          spacing={0}
          position={"absolute"}
          bottom={"50px"}
          opacity={0.8}
          transition={"opacity 0.2s ease"}
          _hover={{ opacity: 1 }}
        >
          <Suspense>
            <DownloadApp />
          </Suspense>
        </ButtonGroup>
      } */}
    </VStack>
  </>);
}