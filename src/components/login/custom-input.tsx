import { Pop<PERSON>, <PERSON>overTrigger, Box, <PERSON>overContent, <PERSON>overArrow, PopoverBody } from "@hope-ui/solid";
import { ParentComponent } from "solid-js";

export const CustomInput: ParentComponent<{ errorMessage?: string; }> = (props) => {
  return (<>
    <Popover opened={props.errorMessage != null} closeOnBlur={false} placement="right" >
      <PopoverTrigger width={"100%"} as={Box} variant="subtle" colorScheme="danger">{props.children}</PopoverTrigger>
      <PopoverContent>
        <PopoverArrow background={"$danger8"} />
        {/* <PopoverBody background={"$danger8"}> */}
          {props.errorMessage}
        {/* </PopoverBody> */}
      </PopoverContent>
    </Popover>
  </>);
};
