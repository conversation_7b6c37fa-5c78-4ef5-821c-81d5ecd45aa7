import { <PERSON><PERSON>, Center, VStack, Heading, InputGroup, Input, Divider } from "@hope-ui/solid";
import { createSignal, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AlreadyHaveAccount_Anchor, CreateAccount_Anchor, EnterAsGuest_Anchor } from "../anchors";
import { CustomInput } from "./custom-input";
import style from '~/sass/login.page.module.sass';
import { USER_INPUT } from "~/util/const.common";
import I18nService from "~/services/i18n.service";
import LoginService, { CurrentForm } from "~/services/login.service";
import notificationService from "~/services/notification.service";
import { z } from "zod";
import { useForm } from "~/util/validation";
import { createSimpleEmailFormData, delay, FormHelper } from "~/util/helpers";
import { useAction } from "@solidjs/router";
import { resendVerificationEmail } from "~/lib";

const inputSettings = z.object({
  email: z.string().email().max(USER_INPUT.MaxEmailLength),
});

const ResendEmailVerificationForm = () => {
  const [loading, setLoading] = createSignal(false);
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);
  const resendEmailVerificationAction = useAction(resendVerificationEmail);

  let inputElement!: HTMLButtonElement;
  let emailInput!: HTMLInputElement;

  const { validate, formSubmit } = useForm({
    errorClass: "error-input"
  });

  const [fields, setFields] = createStore({
    email: "",
  });

  const isFormValid = () => inputSettings.safeParse(fields).success;

  onMount(() => {
    window.history.pushState(null, '', "/resend-verification");
    validate(emailInput, () => [FormHelper.isNotEmpty]);
  });

  const fn = (_: HTMLFormElement) => {
    inputSettings
      .parseAsync(fields)
      .then((result) => {
        setLoading(true);
        return resendEmailVerificationAction(
          createSimpleEmailFormData(result.email)
        );
      })
      .then(() => {
        notificationService.show({
          type: "success",
          title: "Email Verification",
          description: i18nService().t_loginPage("resendVerificationEmailForm.onSubmitInfoToast"),
          duration: 1000 * 10
        });
        loginService().updateForm(CurrentForm.Main);
      })
      .catch(async (err) => {
        notificationService.show({
          type: "danger",
          title: "Resend Verification Form Failed.",
          closable: true,
          description: err || "Something went wrong. Please try again later.",
          duration: 1000 * 30
        });
        await delay(1000);
        setLoading(false);
      }).finally(() => {
        setLoading(false);
      });
  };

  return (<>
    <form use:formSubmit={fn}>
      <Center class={style.formFadeIn}>
        <VStack class={style.loginFormContainer}>
          <Heading size="2xl" mb={"10px"}>Resend Email Verification</Heading>
          <InputGroup>
            <VStack spacing="$4" class={style.loginFormInputContainer} alignItems="baseline">
              <CustomInput>
                <Input disabled={loading()} invalid={false} ref={emailInput} name="email"
                  maxLength={USER_INPUT.MaxEmailLength}
                  required placeholder="email" type="email"
                  /* @ts-ignore */
                  onInput={(e) => setFields("email", e.target.value)}
                />
              </CustomInput>
            </VStack>
          </InputGroup>
          <Button
            disabled={!isFormValid()}
            loading={loading()}
            type="submit"
            ref={inputElement}
            width="100%" mt={"10px"} mb={"10px"} variant="ghost">Submit</Button>
          <Divider />
          <VStack mt={"10px"}>
            <CreateAccount_Anchor />
            <EnterAsGuest_Anchor />
            <AlreadyHaveAccount_Anchor />
          </VStack>
        </VStack>
      </Center>
    </form>
  </>);
};

export default ResendEmailVerificationForm;