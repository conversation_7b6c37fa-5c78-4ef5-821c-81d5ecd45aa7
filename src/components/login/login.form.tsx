import { Box, Button, Center, Divider, Heading, Input, InputGroup, VStack } from "@hope-ui/solid";
import { Title } from "@solidjs/meta";
import { useAction, useLocation, useNavigate, useSearchParams, useSubmission } from "@solidjs/router";
import Mousetrap from "mousetrap";
import { FaBrandsDiscord } from "solid-icons/fa";
import { createEffect, createMemo, createSignal, onCleanup, onMount, Setter } from "solid-js";
import { useService } from "solid-services";
import { buttonConfirmSFX } from "~/directives/buttonsfx.directive";
import { login } from "~/lib";
import { userLoginSchema } from "~/lib/schema";
import style from "~/sass/login.page.module.sass";
import LoginService from "~/services/login.service";
import AppSettingsService from "~/services/settings-storage.service";
import { COMMON, USER_INPUT } from "~/util/const.common";
import { createLoginFormData } from "~/util/helpers";
import { CreateAccount_Anchor, EnterAsGuest_Anchor, ForgotPassword_Anchor, ResendEmailVerification_Anchor } from "../anchors";

type LoginFormProps = {
  hideMetaAnchors?: boolean;
  insidePopup?: boolean;
  targetPathAfterLogin?: string;
  onLoginSuccess?: (usertag: string, username: string, roles: string[]) => void;
};

export default function LoginForm(props: LoginFormProps) {
  const [searchParams] = useSearchParams();
  const [_usernameEmail, setUsernameEmail] = createSignal("");
  const [password, setPassword] = createSignal("");
  const [loading, setLoading] = createSignal(false);
  const appSettingsService = useService(AppSettingsService);
  const loginService = useService(LoginService);
  const loginAction = useAction(login);
  const loggingSubmission = useSubmission(login);
  const location = useLocation();
  const navigate = useNavigate();

  const [rememberMe, setRememberMe] = createSignal(
    (appSettingsService().getLocalStorage<boolean>("lastSavedLoggedInUsername") &&
      appSettingsService().getLocalStorage<boolean>("rememberMeChecked")) || false
  );
  let inputElement!: HTMLButtonElement;

  function usernameEmail() {
    // return emojifyService().encode(_usernameEmail()) as string;
    return _usernameEmail();
  }

  const canSubmit = createMemo(() => {
    let i1 = usernameEmail();
    let i2 = password();
    const result = userLoginSchema.safeParse({ username: i1, password: i2 });
    return result.success;
  }, false);

  const onEnter = async (event: KeyboardEvent | MouseEvent) => {
    if (!canSubmit() || loading()) return;
    if ('repeat' in event && event.repeat) return;

    setLoading(true);

    let result = await loginService().onLogin(
      loginAction(createLoginFormData(usernameEmail(), password(), false))
    );

    if (!result) return loginService().onLoginFail();;

    loginService().onLoggedIn();
    props.onLoginSuccess?.(result.usertag, result.username, result.roles);

    if (props.targetPathAfterLogin) {
      navigate(props.targetPathAfterLogin);
      return;
    }

    await loginService().onEnterAppLoadingAfterLogin(
      Array.isArray(searchParams.roomName) ? searchParams.roomName[0] : searchParams.roomName
    );
  };

  const onDiscordSignIn = () => {
    setLoading(true);
    setRememberMe(false);
    appSettingsService().setLocalStorage("rememberMeChecked", false);
    let currentUrl = encodeURIComponent(`${document.location.origin}${location.pathname}`);
    let output = `${COMMON.WS_HOST}/oauth2/discord?redirect=${currentUrl}`;
    window.location.replace(output);
  };

  onMount(() => {
    let trap = new Mousetrap(document.body);
    trap.bind("enter", onEnter);
    onCleanup(() => trap.unbind("enter"));

    if (!props.insidePopup) window.history.pushState(null, '', "/login");

    if (appSettingsService().getLocalStorage<boolean>("rememberMeChecked")) {
      let lastSavedUsername = appSettingsService().getLocalStorage<string>("lastSavedLoggedInUsername");
      if (lastSavedUsername) {
        setUsernameEmail(lastSavedUsername);
        setLoading(true);
        return;
      }
    }
  });

  createEffect(() => {
    setLoading(loggingSubmission.pending ?? false);
    appSettingsService().setLocalStorage("rememberMeChecked", rememberMe());
  });

  const handleInput = (setter: Setter<string | undefined>) => (event: InputEvent) => {
    const text: string | undefined = (event.target as any).value;
    setter(text?.trim());
  };

  return (<>
    <Title>PianoRhythm - Login</Title>
    <Center as="form">
      <VStack class={style.loginFormContainer}>
        <Heading size="2xl" marginBottom={10}>Login</Heading>
        <InputGroup>
          <VStack spacing={"$4"} class={style.loginFormInputContainer} alignItems="baseline">

            {/* Username */}
            <Input
              data-testid="login-username"
              minLength={USER_INPUT.MinUsernameLength}
              maxLength={USER_INPUT.MaxUsernameLength}
              disabled={loading()}
              value={usernameEmail()}
              onInput={handleInput(setUsernameEmail)}
              autocomplete="username"
              placeholder="username / email" />

            {/* Password */}
            <Input
              data-testid="login-password"
              minLength={USER_INPUT.MinPasswordLength}
              maxLength={USER_INPUT.MaxPasswordLength}
              autocomplete="current-password"
              disabled={loading()} onInput={handleInput(setPassword)} placeholder="password" type="password" />

            {/* Remember Me */}
            {/* <Checkbox
              onChange={(e: Event) => { setRememberMe((e.target as any).checked); }}
              defaultChecked={globalService().autoLogin()}
              class={
                (
                  !_usernameEmail() ||
                  !password() ||
                  loading()
                ) ? "disabled" : ""
              }
              size="sm" variant="filled">Remember me
            </Checkbox> */}
          </VStack>
        </InputGroup>
        <Button
          onmousedown={onEnter}
          disabled={!canSubmit() || loading()}
          ref={(el: HTMLButtonElement) => {
            buttonConfirmSFX(el);
          }}
          width="100%" marginTop={"10px"} marginBottom={"10px"}
          variant="ghost">
          {loading() ? "Logging in..." : "Submit"}
        </Button>
        <Divider />
        <VStack marginTop={"10px"}>
          <VStack spacing={"$1"} marginBottom={"5px"}>
            <Button
              data-testid="login-discord"
              width={"100%"}
              disabled={loading()}
              ref={(el: HTMLButtonElement) => { buttonConfirmSFX(el); }}
              onmousedown={onDiscordSignIn}
              leftIcon={<FaBrandsDiscord />}
              size="sm" variant="ghost">Sign in with Discord</Button>
            <Box
              class={"disabled"}
              ref={(el: HTMLElement) => { buttonConfirmSFX(el); }}
              id="google-sign-in" border={"1px solid white"} borderRadius={"$sm"}></Box>
          </VStack>

          {!props.hideMetaAnchors && <>
            <CreateAccount_Anchor />
            <EnterAsGuest_Anchor />

            <VStack>
              <ForgotPassword_Anchor />
              <ResendEmailVerification_Anchor />
            </VStack>
          </>
          }
        </VStack>
      </VStack>
    </Center>
  </>);
}