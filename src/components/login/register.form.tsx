import { Box, Button, ButtonGroup, Center, Checkbox, Divider, Heading, Icon, Input, InputGroup, Modal, ModalBody, ModalCloseButton, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON>dal<PERSON>eader, ModalOverlay, VStack } from "@hope-ui/solid";
import { Title } from "@solidjs/meta";
import { useAction } from "@solidjs/router";
import { FaSolidFileContract } from "solid-icons/fa";
import { Component, createResource, createSignal, onMount, Show } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import { AlreadyHaveAccount_Anchor, EnterAsGuest_Anchor } from "~/components/anchors";
import { register } from "~/lib";
import { userRegisterSchema } from "~/lib/schema";
import style from '~/sass/login.page.module.sass';
import EmojifyService from "~/services/emojify.service";
import I18nService from "~/services/i18n.service";
import LoginService, { CurrentForm } from "~/services/login.service";
import notificationService from "~/services/notification.service";
import { USER_INPUT, VALIDATIONS } from "~/util/const.common";
import { createRegisterFormData, delay, FormHelper } from "~/util/helpers";
import content from "~/util/raw/tos_index.html?raw";
import { useForm } from "~/util/validation";
import { CustomInput } from "./custom-input";

const fetchTosContent = async () => {
  const response = await fetch('/tos/tos_index.html');
  return response.text();
};

const RegisterForm: Component = () => {
  let usernameInput!: HTMLInputElement;
  let emailInput!: HTMLInputElement;
  let passwordInput!: HTMLInputElement;
  let password2Input!: HTMLInputElement;
  let tosCheckboxInput!: HTMLInputElement;

  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);
  const emojifyService = useService(EmojifyService);

  const registerAction = useAction(register);
  const [tosContent] = createResource(fetchTosContent);

  const [showTOS, setShowTOS] = createSignal(false);
  const [loading, setLoading] = createSignal(false);
  const { validate, formSubmit, errors } = useForm({
    errorClass: "error-input"
  });

  const [fields, setFields] = createStore({
    username: "",
    email: "",
    password: "",
    password2: "",
    tos: false
  });

  const userNameExists = async ({ value }: { value: string; }) => {
    const exists = false; //await fetchUserName(value);
    return exists && `${value} is not available.`;
  };

  const matchesPassword = ({ value }: { value: string; }) =>
    value === fields.password ? false : "Passwords must match";

  const isFormValid = () => userRegisterSchema.safeParse(fields).success;

  const fn = (_: HTMLFormElement) => {
    userRegisterSchema
      .parseAsync(fields)
      .then((result) => {
        setLoading(true);
        return registerAction(
          createRegisterFormData(result)
        );
      })
      .then((output) => {
        notificationService.show({
          type: "success",
          title: "Registration Success.",
          description: output ? "You may now login!" : i18nService().t_common("generalMessages.registerSuccess"),
          duration: output ? 5000 : 1000 * 30
        });
        loginService().updateForm(CurrentForm.Login);
      })
      .catch(async (err) => {
        // sfxService().playErrorSFX();
        notificationService.show({
          type: "danger",
          title: "Registration Failed.",
          description: err ?? "Something went wrong trying to register. Please try again later."
        });
        await delay(1000);
        setLoading(false);
      }).finally(() => {
        setLoading(false);
      });
  };

  onMount(() => {
    window.history.pushState(null, '', "/register");
    validate(usernameInput, () => [userNameExists]);
    validate(emailInput, () => [FormHelper.isNotEmpty]);
    validate(passwordInput, () => []);
    validate(password2Input, () => [matchesPassword]);
    validate(tosCheckboxInput, () => []);
  });

  const onCloseTosModal = () => {
    setShowTOS(false);
  };

  return (<>
    <Title>PianoRhythm - Register</Title>
    {/* ~/ts-ignore */}
    <form use:formSubmit={fn}>
      <Center class={style.formFadeIn}>
        <VStack class={style.loginFormContainer}>
          <Heading size="2xl" mb={"10px"}>Register</Heading>
          <InputGroup>
            <VStack spacing={"$2"} class={style.loginFormInputContainer}>
              <CustomInput errorMessage={errors.username}>
                <Input disabled={loading()} invalid={errors.username} ref={usernameInput}
                  maxLength={USER_INPUT.MaxUsernameLength}
                  oninvalid={(event: Event) => {
                    (event.target as HTMLInputElement).setCustomValidity(`
                      - Must be between ${USER_INPUT.MinUsernameLength} and ${USER_INPUT.MaxUsernameLength} characters.
                      - Cannot contain any special characters
                    `);
                  }}
                  pattern={VALIDATIONS.usernameValidationRegex(USER_INPUT.MinUsernameLength, USER_INPUT.MaxUsernameLength)}
                  minlength={USER_INPUT.MinUsernameLength} name="username" required={true} placeholder="username"
                  /* ~/ts-ignore */
                  onInput={(e) => setFields("username", emojifyService().encode(e.target.value))}
                />
              </CustomInput>

              <CustomInput errorMessage={errors.email}>
                <Input disabled={loading()} invalid={errors.email} ref={emailInput} name="email"
                  maxLength={USER_INPUT.MaxEmailLength}
                  required placeholder="email" type="email"
                  /* ~/ts-ignore */
                  onInput={(e) => setFields("email", e.target.value)}
                />
              </CustomInput>

              <CustomInput errorMessage={errors.password}>
                <Input disabled={loading()} invalid={errors.password} ref={passwordInput}
                  maxLength={USER_INPUT.MaxPasswordLength}
                  minlength={USER_INPUT.MinPasswordLength} name="password"
                  required placeholder="password" type="password"
                  /* ~/ts-ignore */
                  onInput={(e) => setFields("password", e.target.value)} />
              </CustomInput>

              <CustomInput errorMessage={errors.password2}>
                <Input disabled={loading()} invalid={errors.password2} ref={password2Input}
                  maxLength={USER_INPUT.MaxPasswordLength}
                  minlength={USER_INPUT.MinPasswordLength} name="password2"
                  required placeholder="confirm password" type="password"
                  /* ~/ts-ignore */
                  onInput={(e) => setFields("password2", e.target.value)} />
              </CustomInput>

              <Checkbox
                ref={tosCheckboxInput}
                size="sm"
                variant="outline"
                required
                name="tos"
                checked={fields.tos}
                onClick={(e: Event) => { setFields("tos", !fields.tos); }}
                disabled={loading()}
              >I agree that I'm at least 13 years old
                and I have read the
                <Box as="a" onclick={() => setShowTOS(true)}> Terms of Use and Conditions</Box>
              </Checkbox>
            </VStack>
          </InputGroup>
          <Button
            loading={loading()}
            disabled={loading() || !isFormValid()}
            type="submit"
            width="100%" mt={"10px"} mb={"10px"} variant="ghost">
            Register
          </Button>
          <Divider />
          <VStack mt={"10px"}>
            <AlreadyHaveAccount_Anchor />
            <EnterAsGuest_Anchor />
          </VStack>
        </VStack>
      </Center>
    </form>
    {showTOS() &&
      <Modal
        centered
        opened={true}
        onClose={onCloseTosModal}
        scrollBehavior={"inside"}
        size="3xl"
      >
        <ModalOverlay />
        <ModalContent>
          <ModalCloseButton />
          <ModalHeader><Icon as={FaSolidFileContract} /> Terms of Service</ModalHeader>
          <Show when={!tosContent.loading}
            fallback={<ModalBody><Center>Loading Terms of Service...</Center></ModalBody>}
          >
            <ModalBody innerHTML={tosContent()} />
          </Show>
          <ModalFooter>
            <ButtonGroup variant={"outline"} size="sm">
              <Button onmousedown={() => { onCloseTosModal(); }}>Okay</Button>
            </ButtonGroup>
          </ModalFooter>
        </ModalContent>
      </Modal>
    }
  </>);
};

export default RegisterForm;