import { <PERSON>, <PERSON>lex, <PERSON><PERSON>tack, <PERSON><PERSON><PERSON><PERSON><PERSON>, Spacer } from "@hope-ui/solid";
import Mousetrap from "mousetrap";
import { FaSolidRectangleXmark } from "solid-icons/fa";
import { Component, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import css from '~/sass/chat.module.sass';
import ChatService from "~/services/chat.service";
import { ChatMessageRecord } from "~/types/chat.types";
import { substringEllipses } from "~/util/helpers";
import { scrollToChatMessageElement } from "./chat.common";

const MessageBeingEdited: Component = () => {
  const chatService = useService(ChatService);
  const [chatRecord, setChatRecord] = createSignal<ChatMessageRecord>();

  let onEscape = () => {
    chatService().setChatMessageBeingEdited(undefined);
    chatService().setChatBarValue("");
  };

  onMount(() => {
    let record = chatService().getMessageByID(chatService().chatMessageBeingEdited() || "undefined");
    setChatRecord(record);

    let ms = new Mousetrap(document.body);
    ms.bind("escape", onEscape);
    onCleanup(() => ms.unbind("escape"));
  });

  return (<>
    {chatRecord() &&
      <Box className={css.chatBarReplyToMessageContainer}>
        <Flex className="unselectable">
          <HStack
            spacing={"$1"}
            height={19}
            alignItems="flex-start"
            overflow={"hidden"}
          >
            <Box flex="1 0 auto">Editing message:</Box>
            <Box
              __tooltip_title={chatRecord()?.message}
              onMouseDown={() => scrollToChatMessageElement(chatRecord()?.id)}
              className={css.chatBarReplyToMessageTarget} as="span">{substringEllipses(chatRecord()?.message, 35)}</Box>
          </HStack>
          <Spacer />
          <IconButton
            __tooltip_title="Close"
            as="span"
            height={"20px !important"}
            _hover={{ "color": "$neutral12" }}
            background="$accent1"
            marginRight={10}
            onMouseDown={onEscape}
            borderRadius={0} size="xs" aria-label={"Close"} icon={<FaSolidRectangleXmark />} />
        </Flex>
      </Box>
    }
  </>);
};

export default MessageBeingEdited;