import { Box, Flex, <PERSON><PERSON><PERSON><PERSON>on, Spacer } from "@hope-ui/solid";
import Mousetrap from "mousetrap";
import { FaSolidRectangleXmark } from "solid-icons/fa";
import { Component, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import css from '~/sass/chat.module.sass';
import ChatService from "~/services/chat.service";
import { ChatMessageRecord } from "~/types/chat.types";
import { scrollToChatMessageElement } from "./chat.common";

const MessageBeingRepliedTo: Component = () => {
  const chatService = useService(ChatService);
  const [chatRecord, setChatRecord] = createSignal<ChatMessageRecord>();

  let onEscape = () => {
    chatService().setChatMessageBeingRepliedTo(undefined);
  };

  onMount(() => {
    let record = chatService().getMessageByID(chatService().chatMessageBeingRepliedTo() || "undefined");
    setChatRecord(record);

    let ms = new Mousetrap(document.body);
    ms.bind("escape", onEscape);
    onCleanup(() => ms.unbind("escape"));
  });

  return (<>
    {chatRecord() &&
      <Box className={css.chatBarReplyToMessageContainer}>
        <Flex className="unselectable">
          <Box>Replying to <Box
            onclick={() => scrollToChatMessageElement(chatRecord()?.id)}
            className={css.chatBarReplyToMessageTarget} as="span">{chatRecord()?.username}</Box></Box>
          <Spacer />
          <IconButton
            __tooltip_title="Close"
            as="span"
            height={"20px !important"}
            _hover={{
              "color": "$neutral12"
            }}
            background="$accent1"
            marginRight={10}
            onClick={onEscape}
            borderRadius={0} size="xs" aria-label={"Close"} icon={<FaSolidRectangleXmark />} />
        </Flex>
      </Box>
    }
  </>);
};

export default MessageBeingRepliedTo;