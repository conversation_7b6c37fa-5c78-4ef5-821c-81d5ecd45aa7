import "croppie/croppie.css";

import { ImageUploadResponse } from "~/types/api.types";
import { Box, Button, Center, createDisclosure, HStack, Icon, Modal, ModalBody, ModalCloseButton, Modal<PERSON>ontent, <PERSON>dal<PERSON><PERSON>er, ModalHeader, ModalOverlay, VStack } from "@hope-ui/solid";
import { createFileUploader } from "@solid-primitives/upload";
import { TransparentPixel } from "~/util/const.common";
import { arrayBufferToBase64 } from "~/util/helpers";
import { logError } from "~/util/logger";
import convertSize from "convert-size";
import * as noUiSlider from 'nouislider';
import { FaSolidFolderPlus, FaSolidImage } from "solid-icons/fa";
import { Component, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import style from "~/sass/image-uploader.module.sass";
import AppService from "~/services/app.service";
import SoundEffectsService from "~/services/sound-effects.service";
import notificationService from "~/services/notification.service";
import clsx from "clsx";

// @ts-ignore
import Croppie from "croppie";
import { uploadImage } from "~/server/general.api";

const DefaultMinZoom = 0;
const DefaultMaxZoom = 1.5;

type ImageUploaderModalProps = {
  headerLabel: string;
  fileSizeLimit?: number;
  path: string;
  onClose: () => void;
  onSave: (response: ImageUploadResponse) => Promise<void>;
  onSaveError?: (ex: Error) => void;
  dimensions?: { width: number, height: number; };
};

const UploadGifError = "Uploading GIFs is a PRO feature.";

const ImageUploaderModal: Component<ImageUploaderModalProps> = (props) => {
  const { onOpen, onClose } = createDisclosure();
  const appService = useService(AppService);
  const sfxService = useService(SoundEffectsService);

  const [croppie, setCroppie] = createSignal<Croppie>();
  const [imageUploaded, setImageUploaded] = createSignal(false);
  const [isGif, setIsGif] = createSignal(false);
  const [loading, setLoading] = createSignal(false);
  const [zoom, setZoom] = createSignal(DefaultMinZoom);
  const [minZoom, setMinZoom] = createSignal(DefaultMinZoom);
  const [maxZoom, setMaxZoom] = createSignal(DefaultMaxZoom);
  const { files, selectFiles } = createFileUploader({ accept: "image/*" });
  let imageContainerRef!: HTMLDivElement;

  onMount(() => {
    onOpen();
    sfxService().playModalOpenSFX();
  });

  onCleanup(() => {
    croppie()?.destroy();
    sfxService().playModalCloseSFX();
  });

  function onMountContainer(element: HTMLDivElement) {
    imageContainerRef = element;
    let crp = new Croppie(element, {
      showZoomer: false,
      viewport: {
        width: props.dimensions?.width || 100,
        height: props.dimensions?.height || 100,
        type: "square"
      }
    });
    window.setTimeout(() => { setZoom(0); }, 300);
    setCroppie(crp);
  }

  function reset() {
    setImageUploaded(false);
    setIsGif(false);
    setLoading(false);
    setMinZoom(DefaultMinZoom);
    setMaxZoom(DefaultMaxZoom);
    setZoom(DefaultMinZoom);
  }

  function closeModal() {
    reset();
    onClose();
    props.onClose();
  }

  function getCroppedData(): Promise<string> {
    return new Promise(async (resolve, reject) => {
      let _croppie = croppie();
      if (!_croppie) return reject("Croppie not initialized.");

      if (isGif()) {
        if (!appService().client().isProMember) {
          return reject(UploadGifError);
        }
        let file = files()[0];
        if (!file) return reject("No file uploaded.");

        let data = arrayBufferToBase64(await file.file.arrayBuffer());
        resolve(`data:image/gif;base64,${data}`);
        return;
      }

      let data = await _croppie.result({
        type: "base64",
        format: "jpeg"
      });

      resolve(data);
    });
  }

  async function onSave() {
    setLoading(true);
    try {
      let data = await getCroppedData();
      let response = await uploadImage(props.path, data);

      if (!response) throw new Error("Failed to upload image.");
      if (response instanceof Error) throw response;

      await props.onSave(response);
    } catch (ex) {
      logError(`[ImageUpload] ${ex}`);
      if (props.onSaveError) props.onSaveError(ex as Error);
    }
    closeModal();
  }

  async function onChooseImage() {
    selectFiles(async ([{ source, file, size }]) => {
      if (props.fileSizeLimit && size > props.fileSizeLimit) {
        setLoading(false);
        sfxService().playErrorSFX();
        notificationService.show({
          type: "danger",
          title: `File size exceeds max limit of: ${convertSize(props.fileSizeLimit, { accuracy: 0 })}`,
          description: "Please try a different file."
        });
        return;
      }

      let isgif = file.type.toLowerCase() == "image/gif";
      setIsGif(isgif);

      if (isgif && !appService().client().isProMember) {
        await croppie()?.bind({ url: TransparentPixel });

        sfxService().playErrorSFX();
        notificationService.show({
          type: "danger",
          description: UploadGifError
        });

        setLoading(false);
        setImageUploaded(false);
        return;
      }

      await croppie()?.bind({ url: source as any });

      let _element = imageContainerRef.querySelector(".cr-slider");
      let min = parseFloat(_element?.getAttribute("min") || "0");
      let max = parseFloat(_element?.getAttribute("max") || "0");
      let currentZoom = parseFloat(_element?.getAttribute("aria-valuenow") || "0");
      setMinZoom(min);
      setMaxZoom(max);
      setZoom(currentZoom);
      setImageUploaded(true);

      if (isgif) setZoom(0);
    });
  }

  createEffect(() => {
    croppie()?.setZoom(zoom());
  });

  const onVolumeBarSlider = (element: HTMLElement) => {
    if (element.className.includes("noUi-target")) return;
    let slider = noUiSlider.create(element, {
      orientation: "horizontal",
      connect: true,
      tooltips: true,
      start: zoom(),
      step: 0.0001,
      range: {
        'min': minZoom(),
        'max': maxZoom()
      },
    });

    slider.on("update", async (values) => {
      let value: number = parseFloat(values[0] as any);
      setZoom(value);
    });
  };

  return (<>
    <Modal
      opened={true}
      centered
      size={{ "@initial": "sm", "@sm": "lg" }}
      onClose={closeModal}
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidFolderPlus} marginRight={5} marginBottom={5} />{props.headerLabel}</ModalHeader>

        {/* Body */}
        <ModalBody minHeight={150} padding={10} paddingLeft={15} paddingRight={15}>
          <VStack w="100%" h="100%" spacing={"$2"}>

            {/* Image Container */}
            <Box className={style.imageContainer}>
              <Center w="100%" h="100%">
                <Box ref={onMountContainer} className={clsx([style.imageContainerPreview, "croppie-container"])}></Box>
              </Center>
            </Box>

            {/* Zoom Slider */}
            <HStack
              className={clsx((!imageUploaded() || isGif()) && "disabled")}
              w="100%" h="100%">
              <Center h="100%"><Icon fontSize={20}><FaSolidImage /></Icon></Center>
              <Box
                marginLeft={5}
                id={style.zoomSliderBar}
                class={"noUi-target noUi-horizontal"}
                w="90%"
                ref={onVolumeBarSlider}
              />
              <Center h="100%"><Icon fontSize={28} w={50}><FaSolidImage /></Icon></Center>
            </HStack>
          </VStack>
        </ModalBody>

        {/* Footer */}
        <ModalFooter className={style.footer}>
          <HStack w="100%" justifyContent={"space-between"}>
            {/* Choose Image */}
            <Button loading={loading()} variant={"dashed"} onclick={onChooseImage}>
              <Box>Choose Image</Box>
            </Button>

            {/* Cancel && Save */}
            <HStack spacing={"$1"}>
              <Button
                disabled={loading()}
                onclick={closeModal}
                className={style.cancelButton} variant={"solid"}>Cancel</Button>
              <Button
                disabled={loading() || !imageUploaded()}
                onclick={onSave}
                variant={"outline"}>Save</Button>
            </HStack>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default ImageUploaderModal;