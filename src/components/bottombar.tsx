import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>G<PERSON>, hope, <PERSON><PERSON><PERSON><PERSON><PERSON>, VStack } from "@hope-ui/solid";
import { debounce, inRange } from "lodash-es";
import * as noUiSlider from 'nouislider';
import { BiSolidEdit } from 'solid-icons/bi';
import { BsVolumeDownFill, BsVolumeMuteFill, BsVolumeUpFill } from "solid-icons/bs";
import { FaSolidBars, FaSolidFolderPlus, FaSolidPlug, FaSolidUserShield } from "solid-icons/fa";
import { VsSignOut } from 'solid-icons/vs';
import { createEffect, createSignal, For, JSXElement, onCleanup, onMount, Show, splitProps } from "solid-js";
import { useService } from "solid-services";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import style from "~/sass/bottombar.module.sass";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import LoginService from "~/services/login.service";
import AppSettingsService from "~/services/settings-storage.service";
import { AUDIO } from "~/util/const.common";
import { isWebMidiSupported } from "~/util/helpers.dom";

const hideButtonsResponsive1 = { "@initial": "none", "@mobile_l": "block", "@desktop_s": "block" };
const hideButtonsResponsive2 = { "@initial": "none", "@mobile_l": "block" };
const hideButtonsResponsive3 = { "@initial": "none", "@mobile_l": "block" };

/**
 * Represents the bottom bar component.
 *
 * @returns The rendered BottomBar component.
 */
export default function BottomBar() {
  const loginService = useService(LoginService);
  const displayService = useService(DisplaysService);
  const appService = useService(AppService);
  const i18nService = useService(I18nService);
  const audioService = useService(AudioService);
  const appSettingsService = useService(AppSettingsService);

  const [show, setShow] = createSignal(false);
  const [hideBarTimeout, setHideBarTimeout] = createSignal(-1);
  const [volumeSlider, setVolumeSlider] = createSignal<noUiSlider.API>();
  const [hiddenButtons, setHiddenButtons] = createSignal<HTMLButtonElement[]>([]);
  const [showHiddenButtons, setShowHiddenButtons] = createSignal(false);
  let elementRef!: HTMLDivElement;

  createEffect(() => {
    displayService().setDisplay("BOTTOM_BAR_FULLY_SHOWN", show());
  });

  createEffect(() => {
    let root = document.documentElement;
    root.style.setProperty("--bottomBarHeight", show() ? "30px" : "5px");
  });

  createEffect(() => {
    let autoHide = displayService().autoHideBottomBar();
    if (!autoHide) { window.clearTimeout(hideBarTimeout()); }
    setShow(!autoHide);
  });

  onMount(() => {
    window.addEventListener("resize", onResize);
    onResize();
  });

  onCleanup(() => {
    window.clearTimeout(hideBarTimeout());
    window.removeEventListener("resize", onResize);
  });

  const onResize = debounce(() => {
    let buttons = Array.from(elementRef.querySelectorAll("button"))
      .filter((el: HTMLElement) => el.computedStyleMap().get("display") == "none");

    if (buttons.length > 0 && showHiddenButtons()) setShowHiddenButtons(false);
    setHiddenButtons(buttons);
  }, 100);

  type BottomBarButtonProps = {
    icon?: JSXElement;
    leftIcon?: JSXElement;
    label: string;
    id?: string;
    onClick: () => void;
    tooltipKey?: string;
    tooltipAdditionalText?: string;
    tooltipSkipTranslate?: boolean;
    rawText?: JSXElement;
  };

  const _BottomBarButton = (props: BottomBarButtonProps) => {
    const i18nService = useService(I18nService);
    const [local, others] = splitProps(
      props,
      ["icon", "leftIcon", "label", "id", "onClick", "tooltipKey", "tooltipAdditionalText", "tooltipSkipTranslate", "rawText"]
    );

    let tooltip: string | undefined = undefined;
    if (props.tooltipKey) {
      tooltip = `${local.tooltipSkipTranslate ? local.tooltipKey?.trim() : i18nService().t_roomPageBottomBarTooltips(local.tooltipKey ?? "").trim()}${local.tooltipAdditionalText ?? ""}`;
    }

    return (<>
      <Button
        __tooltip_title={tooltip}
        __tooltip_placement="top"
        __tooltip_open_delay={400}
        name={"bottomBarButton-" + local.label}
        id={local.id}
        leftIcon={local.icon}
        iconSpacing={"$1"}
        tabIndex={-1} ref={(el: HTMLButtonElement) => buttonSFX(el)}
        onMouseDown={(evt: MouseEvent) => { if (evt.button == 0) local.onClick(); }}
        {...others}
      >
        {local.rawText ?? i18nService().t_roomPageBottomBarButtons(local.label)}
      </Button>
    </>);
  };

  const BottomBarButton = hope(_BottomBarButton);

  const onVolumeBarSlider = (element: HTMLElement) => {
    if (element.className.includes("noUi-target")) return;
    let slider = noUiSlider.create(element, {
      orientation: "horizontal",
      connect: true,
      start: audioService().volume(),
      step: 1,
      range: { 'min': 0, 'max': AUDIO.MAX_CHANNEL_VOLUME },
    });

    slider.on("update", (values) => {
      let value = parseInt(values[0] as any);
      audioService().setVolume(value);
      audioService().setVolumeMuted(value == 0);
      appSettingsService().saveSetting("VOLUME_SAVED", value);
    });

    slider.on("set", () => {
      appSettingsService().persistSettings();
    });

    setVolumeSlider(slider);
  };

  return (
    <Box
      class={style.bottomBar}
      background={show() ? "$primaryAlpha2" : "$accent1"}
      cursor={show() ? "default" : "pointer"}
      height={"var(--bottomBarHeight)"}
      transition={"height 0.3s, background 0.3s"}
      ref={elementRef}
      onmouseenter={() => {
        if (displayService().autoHideBottomBar()) {
          window.clearTimeout(hideBarTimeout()); setShow(true);
        }
      }}
      onmouseleave={() => {
        if (displayService().autoHideBottomBar()) {
          window.clearTimeout(hideBarTimeout());
          setHideBarTimeout(window.setTimeout(() => {
            setShow(false);
          }, 3000));
        }
      }}
    >
      <Show when={show()}>
        {showHiddenButtons() && hiddenButtons().length > 0 &&
          <VStack
            alignItems={"flex-start"}
            borderTopLeftRadius={5}
            borderTopRightRadius={5}
            background={"$primary1"}
            padding={5}
            minW={100}
            position={"absolute"}
            left={0}
            bottom={"var(--bottomBarHeight)"}
            zIndex={5}
          >
            <For each={hiddenButtons()}>{(button: HTMLButtonElement) => {
              let copy = button.cloneNode(true) as HTMLButtonElement;
              copy.setAttribute("style", "display: block !important; width: 100%;");
              return copy;
            }}</For>
          </VStack>
        }

        <ButtonGroup size={"xs"} spacing="$1" alignItems={"center"} height="100%">
          {hiddenButtons().length > 0 &&
            <IconButton
              __tooltip_title="Click here for more options!"
              __tooltip_show_arrow={false}
              icon={<FaSolidBars />}
              onClick={() => setShowHiddenButtons(v => !v)}
            />
          }

          {/* New Room */}
          <BottomBarButton
            label="newRoom"
            tooltipKey="newRoom"
            icon={<FaSolidFolderPlus />}
            onClick={() => displayService().toggleDisplay("NEW_ROOM_MODAL")}
          />

          {/* Update Room */}
          {(appService().isClientRoomOwner() || appService().isClientAdmin()) &&
            <BottomBarButton
              label="roomSettings"
              tooltipKey="roomSettings"
              display={hideButtonsResponsive2}
              icon={<BiSolidEdit />}
              onClick={() => displayService().toggleDisplay("UPDATE_ROOM_MODAL")}
            />
          }

          {/* Settings */}
          <BottomBarButton
            label="settings"
            tooltipKey="settings"
            icon={<BiSolidEdit />}
            onClick={() => displayService().toggleDisplay("SETTINGS_MODAL")}
          />

          {/* MIDI I/O */}
          {isWebMidiSupported() && <BottomBarButton
            label="midi"
            tooltipKey="midi"
            display={hideButtonsResponsive2}
            icon={<FaSolidPlug />}
            onClick={() => displayService().toggleDisplay("MIDI_IO_MODAL")}
          />}

          {/* Mod Dashboard */}
          {appService().isClientMod() &&
            <BottomBarButton
              label="modDashboard"
              tooltipKey="modDashboard"
              icon={<FaSolidUserShield />}
              onClick={() => displayService().toggleDisplay("MOD_DASHBOARD_MODAL")}
            />}

          {/* Sustain */}
          <BottomBarButton
            label="sustain"
            tooltipKey="sustain"
            background={audioService().sustained() ? "$accent1" : "transparent"}
            onClick={() => audioService().setSustained(v => !v)}
            display={hideButtonsResponsive1}
            width={"80px"}
            rawText={<Box>{i18nService().t_roomPageBottomBarButtons("sustain")}: {audioService().isSustainActive() ? "On" : "Off"}</Box>}
          />

          {/* Volume */}
          <BottomBarButton
            display={hideButtonsResponsive1}
            label="volume"
            tooltipKey="volume"
            background={audioService().volume() == 0 ? "$warning9" : undefined}
            icon={[
              audioService().volume() >= 50 && <BsVolumeUpFill />,
              audioService().volume() == 0 && <BsVolumeMuteFill />,
              inRange(audioService().volume(), 1, 50) && <BsVolumeDownFill />
            ]}
            onClick={() => {
              let currentVolume = audioService().volume();
              let muted = currentVolume == 0;
              let lastSaved = audioService().lastSavedVolume();
              audioService().setLastSavedVolume(currentVolume ?? AUDIO.DEFAULT_CHANNEL_VOLUME);
              audioService().setVolume(muted ? lastSaved : 0);
              volumeSlider()?.set(audioService().volume());
            }}
          />

          {/* Volume Slider */}
          <Box
            __tooltip_title={audioService().volume()}
            __tooltip_close_delay={250}
            id={style.volumeSliderBar}
            class={"noUi-target noUi-horizontal"}
            ref={onVolumeBarSlider}
          />

          {/* Logout */}
          <BottomBarButton
            id={style.logoutButton}
            icon={<VsSignOut />}
            onClick={() => loginService().tryLogout()}
            label="logout"
            tooltipKey="logout"
            tooltipAdditionalText=" PianoRhythm"
          />
        </ButtonGroup>
      </Show>
    </Box>
  );
}