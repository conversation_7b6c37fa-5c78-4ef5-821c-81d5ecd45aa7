import { Box, HStack } from "@hope-ui/solid";
import { Component, Suspense, createEffect, createSignal, onCleanup, useContext } from "solid-js";
import { useService } from "solid-services";
import { ApiUserRecordProvider, useApiUserRecordContext } from "~/contexts/user.context";
import EmojifyService from "~/services/emojify.service";
import UsersService from "~/services/users-service";
import { ApiUserDto } from "~/types/user.types";
import UserMiniProfileCardTooltip from "./user-mini-profile-card-tooltip";
import { Roles, rolesToJSON } from "~/proto/user-renditions";

const UsernameWithMiniProfile: Component<{ user: ApiUserDto; textColor?: string, showTagInstead?: boolean; }> = (props) => {
  const [openMiniProfile, setOpenMiniProfile] = createSignal(false);
  const [userTag, setUserTag] = createSignal<string>("");
  const [user, setUser] = createSignal<ApiUserDto>(props.user);
  const [color] = createSignal<string>(props.textColor || user().color || "$neutral12");

  const usersService = useService(UsersService);
  const emojifyService = useService(EmojifyService);
  const [openMiniProfileTimeout, setOpenMiniProfileTimeout] = createSignal<number>(-1);
  const [closeMiniProfileTimeout, setCloseMiniProfileTimeout] = createSignal<number>(-1);

  createEffect(async () => {
    setUserTag(props.user.usertag);
    let _user = await usersService().fetchUserBasicAccount(userTag());
    if (_user) {
      setUser({ ..._user, lastOnline: _user.lastOnline ? new Date(_user.lastOnline) : undefined });
    }
  });

  const closeTooltip = (immediate = false) => {
    clearTimeout(openMiniProfileTimeout());
    clearTimeout(closeMiniProfileTimeout());
    if (immediate) return setOpenMiniProfile(false);

    setCloseMiniProfileTimeout(window.setTimeout(() => {
      setOpenMiniProfile(false);
    }, 100));
  };

  onCleanup(() => {
    closeTooltip(true);
  });

  return (<>
    <HStack spacing={"$1"} marginTop={3}>
      <HStack spacing={"$1"}>
        <Suspense>
          {user() &&
            <ApiUserRecordProvider value={{ default: user() }}>
              <UserMiniProfileCardTooltip
                placement="top"
                marginTop={5}
                forChat
                opened={openMiniProfile()}
                closeTooltip={() => closeTooltip()}
              >
                <Box
                  onmouseenter={() => {
                    clearTimeout(closeMiniProfileTimeout());
                    clearTimeout(openMiniProfileTimeout());
                    setOpenMiniProfileTimeout(window.setTimeout(() => {
                      setOpenMiniProfile(true);
                    }, 375));
                  }}
                  onmouseleave={() => closeTooltip()}
                  color={color()}
                  fontWeight={600}
                  _hover={{ "textDecoration": "underline", cursor: "pointer" }}
                >{emojifyService().decode(props.showTagInstead ? user().usertag : user().username)}</Box>
              </UserMiniProfileCardTooltip>
            </ApiUserRecordProvider>
          }
        </Suspense>
      </HStack>
    </HStack>
  </>);
};

export const UsertagMiniProfileDefault: Component<{ usertag: string; textColor?: string; isMember?: boolean; }> = (props) =>
  <UsernameWithMiniProfile showTagInstead textColor={props.textColor} user={{
    ...ApiUserDto.DefaultWithUsername2(props.usertag),
    roles: props.isMember ? [rolesToJSON(Roles.MEMBER)] : [],
  }} />;

export const UsernameWithMiniProfileInContext: Component<{ textColor?: string, showTagInstead?: boolean; }> = () => {
  const { user } = useApiUserRecordContext();
  return <UsernameWithMiniProfile textColor="$neutral12" showTagInstead user={user} />;
};

export default UsernameWithMiniProfile;