
import css from '~/sass/chat.module.sass';
import inputSFX from "~/directives/input.directive";
import anime from "animejs";
import { createEffect, createSignal, lazy, Match, onCleanup, onMount, Suspense, Switch } from 'solid-js';
import { Box, HStack, Textarea, Center, Icon } from '@hope-ui/solid';
import { isEmpty, orderBy } from 'lodash-es';
import Mousetrap from 'mousetrap';
import { FaSolidPaperPlane } from 'solid-icons/fa';
import { useService } from 'solid-services';
import { BasicRoomDto } from '~/proto/room-renditions';
import AppService from '~/services/app.service';
import ChatService from '~/services/chat.service';
import EmojifyService from '~/services/emojify.service';
import RoomsService from '~/services/rooms.service';
import SoundEffectsService from '~/services/sound-effects.service';
import UsersService from '~/services/users-service';
import { UserDtoUtil } from '~/types/user-helper';
import { ClientSideUserDtoHelper } from '~/types/user.types';
import { COMMANDS, USER_INPUT } from '~/util/const.common';
import { onRemoveAnimeInstance } from '~/util/helpers.anime-js';
import WebsocketService from '~/services/websocket.service';
import clsx from 'clsx';

const CommandsContainer = lazy(() => import("~/components/commands.container"));
const ChatBarTriggerList = lazy(() => import("~/components/chat-bar-trigger-list"));
const MessageBeingEdited = lazy(() => import("~/components/chat-bar-message-edit"));
const MessageBeingRepliedTo = lazy(() => import("~/components/chat-bar-message-reply"));
const TARGET_USER_MENTION_THRESHOLD = 2;

const ChatBar = () => {
  const [lastEmitIsTyping, setLastEmitIsTyping] = createSignal(false);
  const [isNotTypingTimeout, setIsNotTypingTimeout] = createSignal(-1);
  const [_onKeyEvent, _setOnKeyEvent] = createSignal<(evt: KeyboardEvent) => false | undefined>();
  const [_onBlurEvent, _setOnBlurEvent] = createSignal<(evt: FocusEvent) => false | undefined>();
  const websocketService = useService(WebsocketService);
  const appService = useService(AppService);
  const chatService = useService(ChatService);
  const emojifyService = useService(EmojifyService);
  const usersService = useService(UsersService);
  const roomsService = useService(RoomsService);
  const sfxService = useService(SoundEffectsService);

  const maxHeight = 100;
  const [disableChatBar, setDisableChatBar] = createSignal(false);
  let [inputElement, setInputElement] = createSignal<HTMLTextAreaElement>();
  let updateInputElementHeightTimeout = -1;

  function emitIsTyping(value: boolean) {
    if (appService().clientIsSelfChatMuted()) return;
    if (value != lastEmitIsTyping()) websocketService().emitIsTyping(value);
    setLastEmitIsTyping(value);
  }

  const handleInput = (event: InputEvent) => {
    chatService().setChatBarValue((event.target as any).value as string);
    emitIsTyping(true);
    window.clearTimeout(isNotTypingTimeout());
    setIsNotTypingTimeout(window.setTimeout(() => emitIsTyping(false), 3000));
    chatService().setChatOpacity(1);
  };

  function onSubmit() {
    let input = chatService().chatBarValue()?.trim();

    // Check if service has a selected recent mentioned user
    if (chatService().selectedMentionedUser()) {
      let target = `${COMMANDS.PREFIX.User}${chatService().selectedMentionedUser()}`;
      // if the target is already in the input, remove it
      if (input?.indexOf(target) == 0) { input = input?.substring(target.length).trim(); }

      if (isEmpty(input)) { return inputElement()?.blur(); }
      input = `${target} ${input}`;
    }

    chatService().setChatOpacity(1);

    if (!isEmpty(input)) {
      let messageBeingEditedID = chatService().chatMessageBeingEdited();
      let messageReplyID = chatService().chatMessageBeingRepliedTo();

      sfxService().playChatMessageSendSFX();

      if (!appService().clientIsSelfChatMuted()) {
        websocketService().emitChatMessage(input as string, messageReplyID, messageBeingEditedID);
      }

      emitIsTyping(false);
      chatService().setChatBarValue("");

      if (messageReplyID) {
        chatService().setChatMessageBeingRepliedTo(undefined);
      }

      if (messageBeingEditedID) {
        chatService().setChatMessageBeingEdited(undefined);
      }

      updateInputElementHeight();
    } else {
      inputElement()?.blur();
    }
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (!event.repeat && (event.key == "Enter") && !event.shiftKey) {
      event.preventDefault();
      if (!chatService().preventDefaultIfChatCommandsDisplayed(event)) {
        onSubmit();
      }
      return false;
    }
  };

  function updateInputElementHeight() {
    let _inputElement = inputElement();
    if (!_inputElement) return;

    let root = document.documentElement;
    _inputElement.style.height = "30px";

    if (_inputElement.scrollHeight != 0) {
      _inputElement.style.height = _inputElement.scrollHeight + "px";
    }

    if (parseInt(_inputElement.style.height) >= maxHeight) {
      _inputElement.style.height = maxHeight + "px";
    }
    root.style.setProperty("--readOnly_ChatBarHeight", _inputElement.style.height);
  }

  onMount(() => {
    let onEscape = (_: KeyboardEvent) => {
      if (!chatService().chatMessageBeingRepliedTo()) {
        inputElement()?.blur();
      }
    };

    let ms = new Mousetrap(document.body);
    ms.bind("escape", onEscape);

    let onKeyEvent = (event: KeyboardEvent) => {
      if (event.repeat) return;
      let key = event.key.toLowerCase();

      if (key == "arrowup" || key == "arrowdown" || key == "enter" || key == "tab") {
        if (chatService().preventDefaultIfChatCommandsDisplayed(event)) return false;
      }

      // This will prevent creating a new line after pressing ENTER
      // and putting the chat bar into focus
      if (event.type == "keyup" && key == "enter" && !event.shiftKey) {
        event.preventDefault();
        return false;
      }

      if (event.type == "keyup") {
        chatService().setChatBarValue(emojifyService().decodeRaw);
      }

      updateInputElementHeight();
    };
    _setOnKeyEvent(() => onKeyEvent);

    let onBlurEvent = (event: FocusEvent) => {
      if (chatService().preventDefaultIfChatCommandsDisplayed(event as any)) {
        return false;
      }
    };
    _setOnBlurEvent(() => onBlurEvent);

    let _inputElement = inputElement();

    if (_inputElement) {
      _inputElement.style.maxHeight = maxHeight + "px";
      _inputElement.addEventListener("keydown", onKeyEvent);
      _inputElement.addEventListener("keypress", onKeyEvent);
      _inputElement.addEventListener("keyup", onKeyEvent);
      _inputElement.addEventListener("blur", onBlurEvent);
    }

    onCleanup(ms.reset);
  });

  onCleanup(() => {
    window.clearTimeout(updateInputElementHeightTimeout);
    window.clearTimeout(isNotTypingTimeout());
    setIsNotTypingTimeout(-1);
    let target = chatService().chatBarElement();
    if (target) anime.remove(target);

    let _inputElement = inputElement();
    let onKeyEvent = _onKeyEvent();
    let onBlurEvent = _onBlurEvent();

    if (_inputElement) {
      if (onKeyEvent) {
        _inputElement?.removeEventListener("keydown", onKeyEvent);
        _inputElement?.removeEventListener("keypress", onKeyEvent);
        _inputElement?.removeEventListener("keyup", onKeyEvent);
      }

      if (onBlurEvent) {
        _inputElement?.removeEventListener("blur", onBlurEvent);
      }
    }
  });

  createEffect(() => {
    let messageBeingEdited = chatService().chatMessageBeingEdited();
    if (chatService().chatMessageBeingRepliedTo() || messageBeingEdited) {
      let element = inputElement();
      if (!element) return;

      element.focus();
      window.clearTimeout(updateInputElementHeightTimeout);
      updateInputElementHeightTimeout = window.setTimeout(updateInputElementHeight, 100);
    }
  });

  createEffect(() => {
    let serverChatMuted = appService().client()?.serverChatMuted;
    let onlyOwnerCanChat = (appService().roomSettings()?.OnlyOwnerCanChat && !appService().isClientRoomOwner()) || false;
    setDisableChatBar(serverChatMuted || onlyOwnerCanChat);
  });

  createEffect(() => {
    // If the most recent mentioned users is the same for the last x times, then notify
    let recentUsers = chatService().recentMentionedUsers();
    if (recentUsers.length == 0) return;

    let lastThreeUsers = recentUsers.slice(-TARGET_USER_MENTION_THRESHOLD);
    if (lastThreeUsers.length < TARGET_USER_MENTION_THRESHOLD) return;

    let targetUser = lastThreeUsers[0];
    if (targetUser == null || targetUser == chatService().selectedMentionedUser()) return;

    if (lastThreeUsers.every(x => x == targetUser)) {
      chatService().addMessage(`You've mentioned ${targetUser} multiple times. Now in reply mode.`, true, true);
      chatService().setSelectedMentionedUser(targetUser);
    }
  });

  function onAnimateElement(element: HTMLDivElement) {
    onRemoveAnimeInstance(element);
    anime({
      targets: element,
      width: [`0%`, `100%`],
      easing: 'easeOutElastic(0.1, 1)',
      duration: 200,
    });
  }

  return (<Box className="unselectable">
    <CommandsContainer
      commands={chatService().chatCommands()}
      modules={chatService().chatCommandModules()}
      input={chatService().chatBarValue()}
      trigger="/"
    />

    <Suspense>
      <ChatBarTriggerList
        data={orderBy(
          usersService().users.map(ClientSideUserDtoHelper.ToNonProxy), (u) => [
            UserDtoUtil.isBot(u.userDto!.roles)
          ], ["desc"]
        )}
        inputElement={inputElement as any}
        header={"Users"}
        onInputTriggerMatch={(text, item) => {
          return text ? item.userDto!.usertag.includes(text.substring(1)) : false;
        }}
        onRender={(item) => {
          return (
            <HStack justifyContent={"space-between"} alignItems="flex-start">
              <HStack spacing={"$2"} alignItems="flex-start">
                <Box>{item.userDto?.nickname ?? item.userDto?.username}</Box>
              </HStack>
              <Box color="gray">{item.userDto?.usertag}</Box>
            </HStack>
          );
        }}
        onHighlightedItem={(item) => item.userDto!.usertag}
        onSelectItem={(item) => {
          // Keep track of the last user selected
          chatService().setLastSelectedUser(item.userDto!.usertag);
        }}
        triggerChar={COMMANDS.PREFIX.User}
      />
    </Suspense>

    {appService().isClientMod() && <Suspense>
      <ChatBarTriggerList
        removeTriggerOnSelected
        data={roomsService().rooms()}
        inputElement={inputElement as any}
        header={"Rooms"}
        onInputTriggerMatch={(text, item: BasicRoomDto) => {
          return text ? item.roomName.includes(text.substring(1)) : false;
        }}
        onRender={(item) => {
          return (
            <HStack justifyContent={"space-between"} alignItems="flex-start">
              <HStack spacing={"$2"} alignItems="flex-start">
                <Box>{item.roomName}</Box>
              </HStack>
              <Box color="gray">{item.roomID}</Box>
            </HStack>
          );
        }}
        onHighlightedItem={(item) => item.roomID}
        triggerChar="!"
      />
    </Suspense>
    }

    <Box
      opacity={chatService().chatOpacity() == 1 ? 1 : 0.2}
      ref={(element: HTMLDivElement) => {
        chatService().setChatBarElement(element);
        onAnimateElement(element);
      }}
      class={clsx([
        css.chatBar,
        (disableChatBar() && "disabled unselectable")
      ])}
    >
      <Switch>
        <Match when={chatService().chatMessageBeingRepliedTo()}>
          <Suspense><MessageBeingRepliedTo /></Suspense>
        </Match>
        <Match when={chatService().chatMessageBeingEdited()}>
          <Suspense><MessageBeingEdited /></Suspense>
        </Match>
      </Switch>

      <HStack className={css.chatBarWrapper}>
        {(chatService().selectedMentionedUser()) &&
          <Box
            __tooltip_title={
              <Box textAlign={"center"}>
                <p>Now only replying to: <Box as="span" color={"$accent10"}>{chatService().selectedMentionedUser() ?? "Unknown User"}</Box></p>
                <p>Click to exit</p>
              </Box>
            }
            ml={"$2"} mr={"$2"}
            className={css.chatMentionedUser}
            onMouseDown={() => chatService().clearRecentMentionedUsers()}
          >{chatService().selectedMentionedUser()}</Box>
        }

        <Textarea
          className={clsx([
            css.chatBarInput, "unselectable",
            (chatService().chatCommandsDisplayed() && css.chatBarInputCommandsDisplayed)
          ])}
          disabled={disableChatBar()}
          value={chatService().chatBarValue() ?? ""}
          ref={(elem) => {
            setInputElement(elem);
            inputSFX(elem);
            chatService().setChatBarInputElement(elem);
          }}
          wrap={"hard"}
          maxLength={USER_INPUT.MaxChatMessageLength}
          onKeyDown={handleKeyDown}
          onInput={handleInput}
          autocomplete="off"
          onfocus={() => chatService().setChatOpacity(1)}
          onBlur={() => {
            chatService().setChatOpacity(chatService().defaultChatOpacity);
            emitIsTyping(false);

            if (chatService().chatBarValue()?.trim() == "") {
              chatService().setChatBarValue("");
              updateInputElementHeight();
            }
          }}
          background={chatService().chatOpacity() == 1 ? "$primaryAlpha2" : "transparent"}
          placeholder={"Say something nice here..."}
        />
        <Center
          class={
            clsx([
              css.chatBarIconsContainer,
              (isEmpty(chatService().chatBarValue()) && "disabled")
            ])}
        >
          <HStack spacing={"$2"}>
            <Box onclick={onSubmit}>
              <Icon
                __tooltip_title="Send Message"
                as={FaSolidPaperPlane}
              />
            </Box>
          </HStack>
        </Center>
      </HStack>
    </Box>
  </Box>);
};

export default ChatBar;