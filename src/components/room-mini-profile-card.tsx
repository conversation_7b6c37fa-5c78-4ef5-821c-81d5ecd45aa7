import { Box, Divider, Heading, HStack, Icon, Image, Skeleton, VStack } from "@hope-ui/solid";
import { FaSolidLock } from "solid-icons/fa";
import { Accessor, Component, createEffect, createResource, createSignal, onMount, Show, Suspense } from "solid-js";
import { useService } from "solid-services";
import { ApiUserRecordProvider } from "~/contexts/user.context";
import { BasicRoomDto, roomStagesToJSON } from "~/proto/room-renditions";
import style from "~/sass/sidebar.module.sass";
import { getCountryFlagImage } from "~/server/general.api";
import EmojifyService from "~/services/emojify.service";
import RoomsService from "~/services/rooms.service";
import UsersService from "~/services/users-service";
import { ApiUserDto } from "~/types/user.types";
import { splitAndJoinWordsByCapitalLetter } from "~/util/helpers";
import { tryDecodeURI } from "~/util/helpers.dom";
import UserProfileImage from "./user-profile-image";

const MiniProfileServerOwnerInfo: Component<{ roomOwnerID: string; }> = (props) => {
  const usersService = useService(UsersService);
  const [user, setUser] = createSignal<ApiUserDto | null>();

  onMount(async () => {
    setUser(await usersService().fetchActiveUser(props.roomOwnerID));
  });

  return (<>
    <HStack spacing={"$1"}>
      <HStack spacing={"$1"} padding={5} borderRadius={5}>
        <Suspense>
          {user() &&
            <ApiUserRecordProvider value={{ default: user()! }}>
              <UserProfileImage disableBorder width={30} />
            </ApiUserRecordProvider>
          }
        </Suspense>
        <Box as="span" color="$neutral11">{user()?.usertag}</Box>
      </HStack>
    </HStack>
  </>);
};

const RoomMiniProfileCard: Component<{ room: Accessor<BasicRoomDto | undefined>; }> = (props) => {
  let room = props.room;
  const [roomOwner, setRoomOwner] = createSignal<string>();
  const [selfHosted, setSelfHosted] = createSignal<boolean>(room()?.hostDetails != null);
  const [stage, setStage] = createSignal<string>();
  const roomsService = useService(RoomsService);
  const emojifyService = useService(EmojifyService);
  const [roomDetails] = createResource(room()?.roomID, roomsService().fetchRoomSettings);
  const [hostCountryFlag] = createResource(room()?.hostDetails?.CountryCode, getCountryFlagImage);

  createEffect(() => {
    setSelfHosted(room()?.hostDetails?.CountryCode != null);
  });

  createEffect(() => {
    let details = roomDetails();
    if (details) {
      let _roomOwner = details.roomOwner;
      if (_roomOwner && _roomOwner.toLowerCase() != "system") setRoomOwner(_roomOwner);
      setStage(roomStagesToJSON(details.stage).replace("_", " "));
    }
  });

  return (<>
    <Box padding={5} id={encodeURI(`room-mini-profile-card-${room()?.roomID}`)} >
      <VStack spacing="$1" marginLeft={0} alignItems="flex-start">
        <Heading as="h4" fontSize={16} className={style.usernameHeader}>
          {tryDecodeURI(emojifyService().decode(room()?.roomName.trim())!)}
        </Heading>

        {/* Room Owner */}
        <Show when={roomOwner()}>
          <HStack>
            {/* <Box as="span" color="$neutral11">Owner: </Box> */}
            <Suspense fallback={<Skeleton marginLeft={2} w={130} h={34}></Skeleton>}>
              <MiniProfileServerOwnerInfo roomOwnerID={roomOwner() as string} />
            </Suspense>
          </HStack>
        </Show>
        <Divider color="gray" marginTop={2} />
        <HStack spacing={"$2"} marginTop={5} >
          {/* Room Type */}
          {room()!.roomType &&
            <Box padding={2} borderRadius={3} background="$accent1" textTransform={"uppercase"} fontSize={10}>{splitAndJoinWordsByCapitalLetter(room()!.roomType)}</Box>
          }

          {stage() &&
            <Box padding={2} borderRadius={3} background="$accent1" textTransform={"uppercase"} fontSize={10}>{stage()}</Box>
          }

          {/* Self Hosting Specs */}
          {selfHosted() &&
            <>
              <HStack spacing={"$1"} paddingRight={"2px !important"} padding={2} borderRadius={3} background="$accent1" fontSize={10} h="100%">
                {room()?.hostDetails?.ContinentCode &&
                  <Box marginLeft={2} fontSize={10}>{`${room()?.hostDetails?.ContinentCode || "Unknown"}`}</Box>
                }

                {/* TODO */}
                {/* {room()?.hostDetails?.CountryCode &&
                  <Box borderRadius={3} background="$accent1" fontSize={10}>{
                    ` - ${selfHostService().getCountryData(room()?.hostDetails?.CountryCode as any).name || "Unknown"}`
                  }</Box>
                } */}

                {room()?.hostDetails?.CountryCode && <Suspense>
                  <Image
                    w={30}
                    src={hostCountryFlag()}
                  />
                </Suspense>
                }
                {/* {(!isEmpty(selfHostCountryFlagUrl())) &&
                  <Image
                    w={30}
                    src={selfHostCountryFlagUrl()}
                  />
                } */}
              </HStack>
            </>
          }

          {/* Password protected */}
          {room()?.isPasswordProtected && <Icon as={FaSolidLock} />}
        </HStack>

      </VStack>
    </Box>
  </>);
};

export default RoomMiniProfileCard;