import { Box, Center, VStack } from "@hope-ui/solid";
import { createElementSize } from "@solid-primitives/resize-observer";
import { Component, For, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { Motion } from "solid-motionone";
import { useService } from "solid-services";
import style from "~/sass/pageloader.module.scss";
import AppService from "~/services/app.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { COMMON } from "~/util/const.common";
import { getRandomItem } from "~/util/helpers";
import { AppSoundFx } from "../types/app.types";
import debounce from "lodash-es/debounce";
import clsx from "clsx";

const animatedLogoLetters = ["R", "P", "H", "I", "Y", "T", "H", "M", "A", "N", "O", "*",];

const GetTooltips =
  [
    `<image src='${COMMON.ASSETS_URL}/pageload-tooltips/blob-1.gif' crossorigin/>`,
    "Did you know that you can press F1 to show the instruments list? No? Get to it, then!",
    "Want to change your <b><span class='rainbow-text'>color</span></b>? <br> You can do so by going to Settings > General > User",
    `<div>Type /help in the chatbar to see helpful commands.</div>
    <br>
    <image src='${COMMON.ASSETS_URL}/pageload-tooltips/ac1415659b822517e308bd51988c8f19.png' crossorigin/>
    `,
    `<div>You can right click on a user for more options.</div>
    <br>
    <image src='${COMMON.ASSETS_URL}/pageload-tooltips/e31589a0131efe4b0f36282257136ce6.png' crossorigin/>
    `,
    `You can press F1 to show the list of available instruments for the loaded soundfont.`,
    `“Life is like a piano. What you get out of it depends on how you play it.” <br><br>-Tom Lehrer`,
    `“These fingers of mine, they got brains in 'em. You don't tell them what to do - they do it.” <br><br>-Jerry Lee Lewis`,
    `“I'm able to sometimes express things even more articulately on the piano than I am with singing.” <br><br>-Harry Connick Jr.`,
    `“Sometimes I can only groan, and suffer, and pour out my despair at the piano.” <br><br>-Frederic Chopin`,
    `“What has keys but can't listen to the beauty it unlocks? A piano.” <br><br>-Jarod Kintz`,
    `“The important thing is to feel your music, really feel it and believe it.” <br><br>-Ray Charles`,
    `“Life is like a piano; the white keys represent happiness and the black show sadness. <br>But as you go through life's journey, remember that the black keys also create music.” <br><br>-Author unknown`,
    `Every great work of art has two faces. One toward its own time and one toward the future, toward eternity. <br><br>-Daniel Barenboim`,
  ];

const Pageloader: Component = () => {
  const appService = useService(AppService);
  const sfxService = useService(SoundEffectsService);

  const [isActive, setIsActive] = createSignal(appService().activatePageLoader());
  const [activeToolTip, setActiveToolTip] = createSignal("");
  const [isAtBottom, setIsAtBottom] = createSignal(false);
  const [containerElement, setContainerElement] = createSignal<HTMLDivElement>();
  const containerElementResize = createElementSize(containerElement);

  const onActive = debounce(() => {
    setActiveToolTip(appService().activePageLoaderToolTip() || getRandomItem(GetTooltips));
    setIsActive(true);
    appService().setPagerLoaderAnimating(true);
    sfxService().playSFX(AppSoundFx.WOOSH_DOWN);
  });

  const onNotActive = debounce(() => {
    setIsActive(false);
    sfxService().playSFX(AppSoundFx.WOOSH_UP);
    appService().setActivePageLoaderToolTip("");
    appService().setPagerLoaderAnimating(false);
  });

  createEffect(() => {
    if (containerElementResize.height != null) {
      setIsAtBottom(Math.round(containerElementResize.height) >= Math.round(window.innerHeight) - 1);
    }
  });

  createEffect(() => {
    if (isActive()) {
      let tooltip = appService().activePageLoaderToolTip();
      if (tooltip) setActiveToolTip(tooltip);
    }
  });

  createEffect(() => {
    // Only hide the page loader after it reaches the bottom!
    if (!appService().activatePageLoader() && isActive() && isAtBottom()) {
      onNotActive();
    }
  });

  onMount(() => {
    onActive();
  });

  onCleanup(() => {
    onNotActive();
  });

  return (
    <>
      <Motion
        ref={setContainerElement}
        id="pianorhythm-page-loader"
        class={clsx([style.pageloader, style.ispianorhythm, "unselectable"])}
        initial={{ height: "0vh" }}
        animate={{ height: "100dvh" }}
        exit={{ height: "0vh" }}
        transition={{ duration: 1.2 }}
      >
        <Center h="100dvh">
          <VStack spacing={"$10"}>
            <Box id="pageLoaderTitle" fontFamily="Now" class={style.title} marginBottom={50}>PianoRhythm</Box>
            <Center>
              <Box class={style.pageloaderAnimatedLogoContainer}>
                <For each={animatedLogoLetters} fallback={<div>Loading...</div>}>
                  {(item) =>
                    <span class={clsx([style.pageloaderAnimatedLogoAnimate, style.pageloaderLogoAnimation])}>
                      {item}
                    </span>
                  }
                </For>
              </Box>
            </Center>
            <Box class={style.pageloaderProTipsContainer}>
              <Center flexDirection={"column"}>
                <Box
                  class={style.pageloaderProTipsContent}
                  innerHTML={activeToolTip()}
                >
                </Box>
              </Center>
            </Box>
          </VStack>
        </Center>
      </Motion>
    </>
  );
};

export default Pageloader;