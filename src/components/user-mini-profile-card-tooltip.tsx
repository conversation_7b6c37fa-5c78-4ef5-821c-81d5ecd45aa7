import { Box, Skeleton } from "@hope-ui/solid";
import { ParentComponent, Suspense, lazy } from "solid-js";
import { useApiUserRecordContext } from "~/contexts/user.context";
import { ClientMetaDetails } from "~/types/user.types";

const UserMiniProfileCard = lazy(() => import('./user-mini-profile-card'));

type Alignment = 'start' | 'end';
type Side = 'top' | 'right' | 'bottom' | 'left';
type AlignedPlacement = `${Side}-${Alignment}`;
type Placement = Side | AlignedPlacement;

const UserMiniProfileCardTooltip: ParentComponent<{
  opened: boolean,
  marginTop?: number;
  meta?: ClientMetaDetails;
  placement?: Placement;
  forChat?: boolean;
  closeTooltip?: () => void;
}> = (props) => {
  const { user } = useApiUserRecordContext();

  let Container = (containerProps: any) => {
    return <Box
      marginTop={props.marginTop ?? 0}
      border="2px solid $primary2"
      borderRadius={5}
      backgroundColor={"$primaryDarkAlpha"}
      boxShadow={"3px 3px 5px 2px #00000066"}
      onMouseEnter={() => { props.closeTooltip?.(); }}
      onMouseOver={() => { props.closeTooltip?.(); }}
      minH={!props.forChat && user.profileBackgroundImageLastModified ? 160 : undefined}
      h={props.forChat ? 75 : undefined}
      padding={5}
      minW={200}
      maxW={265}
      overflow={"hidden"}
    >
      {containerProps.children}
    </Box>;
  };

  return (
    <>
      <Box
        __tooltip_show_arrow={false}
        __tooltip_title={
          <Suspense fallback={<Skeleton borderRadius={5} h={80} w={250}></Skeleton>}>
            <Container>
              <UserMiniProfileCard />
            </Container>
          </Suspense>
        }
      >
        {props.children}
      </Box>
    </>
  );
};

export default UserMiniProfileCardTooltip;