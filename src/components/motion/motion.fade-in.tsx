import { JSX } from "solid-js";
import { mergeProps, ParentComponent } from "solid-js";
import { Motion } from "solid-motionone";

type MotionFadeInProps = {
  duration?: number;
  easing?: string;
  delay?: number;
  style?: JSX.CSSProperties | string | undefined;
};

const MotionFadeIn: ParentComponent<MotionFadeInProps> = (props) => {
  const defaultProps = mergeProps({
    duration: 1,
    easing: "ease-in-out",
    delay: 0
  }, props);

  return (
    <Motion
      style={props.style}
      animate={{ opacity: [0, 1] }}
      transition={{
        delay: defaultProps.delay,
        duration: defaultProps.duration,
        // @ts-ignore
        easing: defaultProps.easing
      }}>
      {props.children}
    </Motion>
  );
};

export default MotionFadeIn;