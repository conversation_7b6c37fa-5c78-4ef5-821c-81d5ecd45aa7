import { <PERSON>, <PERSON>ton, ButtonGroup, VStack } from "@hope-ui/solid";
import { <PERSON>FillLock, AiFillUnlock } from "solid-icons/ai";
import { BiRegularReset } from "solid-icons/bi";
import { FaSolidAngleUp } from "solid-icons/fa";
import { Component, createEffect, createMemo, createSignal, lazy, Suspense } from "solid-js";
import { useService } from "solid-services";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { appSceneModeFromJSON } from "~/proto/pianorhythm-app-renditions";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import ChatService from "~/services/chat.service";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import KeyboardService from "~/services/keyboard.service";
import AppSettingsService from "~/services/settings-storage.service";
import { SidebarService } from "~/services/sidebar.service";
import { PianoRhythmSceneMode } from "~/types/app.types";
import { HTML_IDS } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";
import { rendererEngine2D, rendererEngine3D } from "../piano.canvas3D";

const ActionButtonsList = lazy(() => import("~/components/canvas/widgets/action-buttons-list"));

const ActionWidgets: Component = () => {
  const WIDGETS_OPACITY_INACTIVE = 0.6;
  const WIDGETS_ZINDEX_INACTIVE = 2;

  const chatService = useService(ChatService);
  const displayService = useService(DisplaysService);
  const keyboardService = useService(KeyboardService);
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const i18nService = useService(I18nService);
  const sidebarService = useService(SidebarService);
  const audioService = useService(AudioService);

  const hideDisplayButtons = () => displayService().getDisplay("INSTRUMENT_SELECTION");
  const displaySideWidget = () => displayService().getDisplay("SCENE_WIDGET_BUTTONS");
  const canInteract = () => appService().canInteract();
  const [widgetsOpacity, setWidgetsOpacity] = createSignal(WIDGETS_OPACITY_INACTIVE);
  const [widgetsZIndex, setWidgetsZIndex] = createSignal(WIDGETS_ZINDEX_INACTIVE);
  const [cameraLocked, setCameraLocked] = createSignal(false);
  const [canPlayDrums, setCanPlayDrums] = createSignal(false);

  createEffect(async () => {
    setCanPlayDrums(
      Boolean(appService().sceneMode() == PianoRhythmSceneMode.THREE_D && appSettingsService().getSetting("AUDIO_ENABLE_DRUM_CHANNEL", false) && (await audioService().hasAtleastADrumInstrumentInSoundfont()))
    );
  });

  const isPlayingDrums = () => audioService().primaryChannel() == MIDI.DRUM_CHANNEL;

  const showCommonActionsWidgetList = () => true;

  const showCameraActionsWidgetList = () => true;

  const showStageVisualEffectsWidgetList = createMemo(() => {
    return (appService().isClientRoomOwner() || appService().isClientAdmin()) &&
      (appSettingsService().getSettings().GRAPHICS_ENABLE_SPECIAL_EFFECTS);
  });

  const is3DMode = () => appService().sceneMode() == PianoRhythmSceneMode.THREE_D;

  const getRenderingEngine = () => {
    return rendererEngine2D() ?? rendererEngine3D();
  };

  const emitAppActionToEngine = (action: AppStateActions) => {
    appService().coreService()?.send_app_action(action);
  };

  const onSetSceneMode = (mode: PianoRhythmSceneMode) => {
    appService().setSceneMode(mode);
    getRenderingEngine()?.setSceneMode(appSceneModeFromJSON(mode));

    if (mode == PianoRhythmSceneMode.THREE_D) {
      emitAppActionToEngine(AppStateActions.create({ action: AppStateActions_Action.RendererEnableRenderLoop }));
    } else {
      emitAppActionToEngine(AppStateActions.create({ action: AppStateActions_Action.RendererDisableRenderLoop }));
    }
  };

  return (
    <VStack
      id={HTML_IDS.ACTION_WIDGETS_CONTAINER}
      display={{
        "@initial": !chatService().chatMinimized() ? "none" : "flex",
        "@sm": "flex"
      }}
      userSelect="none"
      opacity={hideDisplayButtons() ? 0 : 1}
      pointerEvents={hideDisplayButtons() ? "none" : "auto"}
      height={"calc(100% - var(--bottomBarHeight))"}
      overflowY={"auto"}
      paddingRight={7}
      spacing={"$2"}
      position={"absolute"}
      right={5}
      top={0}
      alignItems="flex-end"
    >
      {displayService().getDisplay("FPS") &&
        <Box
          __tooltip_title={
            <Box w={300}>
              A green color means that the play area is focused and you can play with your keyboard!
            </Box>
          }
          __tooltip_placement="left"
          cursor="pointer"
          position={"absolute"}
          marginTop={34}
          marginRight={15}
          zIndex={10}
          boxSize={7} borderRadius={100}
          background={keyboardService().canPlayKeys() ? "lightgreen" : "gray"}
        />
      }

      {(canInteract() && displaySideWidget()) &&
        //@ts-ignore
        <ButtonGroup
          onmouseenter={() => {
            setWidgetsOpacity(1);
            setWidgetsZIndex(5);
          }}
          onmouseleave={() => {
            setWidgetsOpacity(WIDGETS_OPACITY_INACTIVE);
            setWidgetsZIndex(WIDGETS_ZINDEX_INACTIVE);
          }}
          marginTop={25}
          transition={"opacity 0.25s ease"}
          size="xs"
          opacity={widgetsOpacity()}
          ref={sidebarService().setActionWidgetsContainer}
          zIndex={widgetsZIndex()}
        >
          <VStack
            spacing={"$2"}
            alignItems="flex-end"
          >
            {/* Camera Actions */}
            {showCameraActionsWidgetList() && <>
              <Suspense>
                <ActionButtonsList title={i18nService().t_roomPageActionWidgets("cameraActions", "header")}>
                  <Button tabIndex={-1}
                    __tooltip_title={"Toggle between a 2D or 3D view of the camera."}
                    __tooltip_placement="left"
                    w="100%"
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    onmousedown={() => {
                      let target = appService().sceneMode() == PianoRhythmSceneMode.THREE_D ? PianoRhythmSceneMode.TWO_D : PianoRhythmSceneMode.THREE_D;
                      onSetSceneMode(target);
                      appSettingsService().setLocalStorage("sceneMode", target);
                    }}
                  >
                    Camera Mode: {appService().sceneMode() == PianoRhythmSceneMode.THREE_D ? "3D" : "2D"}
                  </Button>

                  {is3DMode() && <>
                    <Button
                      w="100%"
                      ref={(el: HTMLElement) => buttonSFX(el)}
                      tabIndex={-1} onmousedown={() => {
                        emitAppActionToEngine(AppStateActions.create({
                          action: AppStateActions_Action.RendererResetCamera
                        }));
                      }}>
                      <Box marginRight={5}><BiRegularReset /></Box>
                      Reset Camera
                    </Button>

                    <Button
                      w="100%"
                      ref={(el: HTMLElement) => buttonSFX(el)}
                      tabIndex={-1} onmousedown={() => {
                        emitAppActionToEngine(AppStateActions.create({
                          action: AppStateActions_Action.RendererSetCameraTopPosition
                        }));
                      }}>
                      <Box marginRight={5}><FaSolidAngleUp /></Box>
                      Set Top View
                    </Button>

                    <Button
                      w="100%"
                      __tooltip_title="Locks the camera in place. Panning and rotating are disabled."
                      __tooltip_placement="left"
                      ref={(el: HTMLElement) => buttonSFX(el)}
                      background={cameraLocked() ? "$accent1" : "$primary1"}
                      tabIndex={-1}
                      onmousedown={async () => {
                        emitAppActionToEngine(AppStateActions.create({ action: AppStateActions_Action.RendererToggleLockCamera }));
                        setCameraLocked(v => !v);
                      }}>
                      <Box marginRight={5}>
                        {cameraLocked() ? <AiFillUnlock /> : <AiFillLock />}
                      </Box>
                      {cameraLocked() ? "Unlock " : "Lock "}
                      Camera
                    </Button>
                  </>
                  }
                </ActionButtonsList>
              </Suspense>
            </>}

            {/* Common Actions */}
            {showCommonActionsWidgetList() &&
              <ActionButtonsList title={i18nService().t_roomPageActionWidgets("commonActions", "header")}>
                <Button
                  w="100%"
                  __tooltip_title="Open the Instrument Selection list"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().setDisplay("INSTRUMENT_SELECTION", true); }}>
                  Instruments
                </Button>

                <Button
                  w="100%"
                  __tooltip_title="Open the Soundfonts list"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().setDisplay("SOUNDFONTS_LIST_MODAL", true); }}>
                  Soundfonts
                </Button>

                {/* label_disabled="Your current soundfont doesn't have any standard drums loaded." */}
                <Button
                  w="100%"
                  __tooltip_title="Makes the drums your primary instrument so you can play them!"
                  __tooltip_placement="left"
                  disabled={!canPlayDrums()}
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => {
                    if (isPlayingDrums()) {
                      audioService().setPrimaryChannel(0);
                      audioService().setIsPlayingDrumsMode(false);
                      return;
                    }

                    audioService().setIsPlayingDrumsMode(true);
                  }}>
                  {!isPlayingDrums() ? "Play" : "Reset"} Drums
                </Button>

                <Button
                  w="100%"
                  __tooltip_title="Show the keyboard mapping"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  // disabled={is3DMode() && !is2DMode()}
                  onmousedown={async () => { displayService().toggleDisplay("KEYBOARD_MAPPING_OVERLAY"); }}>
                  {displayService().getDisplay("KEYBOARD_MAPPING_OVERLAY") ? "Hide" : "Show"} Input Mapping
                </Button>

                <Button
                  w="100%"
                  display={"none"}
                  __tooltip_title="Open the Midi Looper"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().toggleDisplay("MIDI_LOOPER"); }}>
                  Looper
                </Button>

                <Button
                  w="100%"
                  __tooltip_title="Open the Audio Equalizer"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().setDisplay("AUDIO_EQUALIZER_MODAL", true); }}>
                  Audio Equalizer
                </Button>

                <Button
                  w="100%"
                  __tooltip_title="Open the Audio Reverb"
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().setDisplay("AUDIO_REVERB_MODAL", true); }}>
                  Audio Reverb
                </Button>

                <Button
                  w="100%"
                  __tooltip_title="Set the volume, pan and other parameters for the channels."
                  __tooltip_placement="left"
                  ref={(el: HTMLElement) => buttonSFX(el)}
                  tabIndex={-1}
                  onmousedown={async () => { displayService().setDisplay("CHANNELS_SLIDERS_MODAL", true); }}>
                  Channels Parameters
                </Button>

                {appSettingsService().isDebugMode() &&
                  <Button
                    w="100%"
                    __tooltip_title="Debug Stats"
                    __tooltip_placement="left"
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    tabIndex={-1}
                    onmousedown={async () => { displayService().toggleDisplay("DEBUG_STATS"); }}>
                    Debug Stats
                  </Button>
                }
              </ActionButtonsList>
            }
          </VStack>
        </ButtonGroup>
      }
    </VStack>
  );
};

export default ActionWidgets;
