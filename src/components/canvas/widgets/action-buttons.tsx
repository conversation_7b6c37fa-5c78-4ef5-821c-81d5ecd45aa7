import { Button } from "@hope-ui/solid";
import { Component, createSignal } from "solid-js";
import { useService } from "solid-services";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import { RoomStageVisualEffects, RoomStageAudioEffects, RoomStageAudioEffect } from "~/proto/room-renditions";
import AppService from "~/services/app.service";
import WebsocketService from "~/services/websocket.service";
import { RoomStageAudioEffectsNS } from "~/types/room.types";

export const StageVisualEffectButton: Component<{ label: string, tooltip: string, stageEffects?: RoomStageVisualEffects, key: keyof RoomStageVisualEffects; }> = (props) => {
  const appService = useService(AppService);
  const socketService = useService(WebsocketService);

  return (<>
    <Button
      __tooltip_title={props.tooltip}
      w="100%"
      ref={(el: HTMLElement) => buttonSFX(el)}
      background={props.stageEffects?.[props.key] ? "$accent1" : "$primary1"}
      tabIndex={-1}
      onmousedown={async () => {
        try {
          let roomParam = appService().updateRoomDetails((details) => {
            return {
              ...details,
              effects: {
                ...details.effects,
                [props.key]: !props.stageEffects?.[props.key] || false
              }
            };
          });
          if (roomParam) socketService().createOrUpdateRoom(roomParam, "UpdateRoom");
        } catch (ex) {
          console.error("Failed to update room details", ex);
        }
      }}>
      {/* <Box marginRight={5}>
          {props.stageEffects?.[props.key] ? <AiFillUnlock /> : <AiFillLock />}
        </Box> */}
      {props.stageEffects?.[props.key] ? "Turn Off " : "Turn On "}
      {props.label}
    </Button>
  </>);
};

export const StageAudioEffectButton: Component<{ label: string, tooltip: string, stageAudioEffects?: RoomStageAudioEffects, key: keyof RoomStageAudioEffects; }> = (props) => {
  const appService = useService(AppService);
  const socketService = useService(WebsocketService);
  const [prevEffectValue, setPrevEffectValue] = createSignal<RoomStageAudioEffect | undefined>();
  const [effectValue, setEffectValue] = createSignal<RoomStageAudioEffect | undefined>(props.stageAudioEffects?.[props.key]);

  return (<>
    <Button
      __tooltip_title={props.tooltip}
      w="100%"
      ref={(el: HTMLElement) => buttonSFX(el)}
      background={effectValue() != null ? "$accent1" : "$primary1"}
      tabIndex={-1}
      onmousedown={async () => {
        try {
          if (effectValue() == null) {
            let defaultEffect: RoomStageAudioEffect = RoomStageAudioEffectsNS.DEFAULT[props.key]!;
            defaultEffect.volume = prevEffectValue()?.volume || RoomStageAudioEffectsNS.DEFAULT_VOLUME;
            defaultEffect.playbackRate = prevEffectValue()?.playbackRate || RoomStageAudioEffectsNS.DEFAULT_PLAYBACK_RATE;
            if (prevEffectValue()?.loop != null) defaultEffect.loop = prevEffectValue()!.loop;
            setEffectValue(defaultEffect);
          } else {
            setPrevEffectValue(effectValue());
            setEffectValue(undefined);
          }

          let roomParam = appService().updateRoomDetails((details) => {
            return {
              ...details,
              audioEffects: {
                ...details.audioEffects,
                [props.key]: effectValue()
              }
            };
          });

          if (roomParam) socketService().createOrUpdateRoom(roomParam, "UpdateRoom");
        } catch (ex) {
          console.error("Failed to update room details", ex);
        }
      }}>
      {props.stageAudioEffects?.[props.key] ? "Turn Off " : "Turn On "}
      {props.label}
    </Button>
  </>);
};

export const CommonActionButton: Component<{ label: string, tooltip: string, onClick: () => void; }> = (props) => {
  return (<>
    <Button
      __tooltip_title={props.tooltip}
      w="100%"
      ref={(el: HTMLElement) => buttonSFX(el)}
      tabIndex={-1}
      onmousedown={async () => { props.onClick(); }}>
      {props.label}
    </Button>
  </>);
};
