import { Box, VStack } from "@hope-ui/solid";
import { ParentComponent } from "solid-js";
import css from '~/sass/piano-container.module.sass';

const ActionButtonsList: ParentComponent<{ title: string; }> = (props) => {
  return (<>
    <Box
      border={"1px solid white"}
      padding={5}
      paddingRight={0}
      borderRadius={5}
      w={140}
      maxH={165}
      overflowY={"hidden"}
      className={css.actionButtonsList}
    >
      <Box fontSize={10} textAlign={"center"}>{props.title}</Box>
      <VStack
        spacing={"$1"}
        h="90%"
        overflowY={"scroll"}
        overflowX={"hidden"}
        padding={5}
      >
        {props.children}
      </VStack>
    </Box>
  </>);
};

export default ActionButtonsList;