import { BevyRenderer } from "~/types/renderer.types";
import { useEventListener } from "solidjs-use";
import { createRoot } from "solid-js";

export function createEventHandlers(
  canvas: HTMLCanvasElement,
  bevyRenderer: BevyRenderer | undefined,
  appHandle: bigint,
  resizeCanvasBy: () => void
) {
  createRoot(() => {
    window.addEventListener("resize", resizeCanvasBy);

    const mediaQuery = window.matchMedia(
      `(resolution: ${window.devicePixelRatio}dppx)`
    );

    const onMediaQueryChange = (_event: MediaQueryListEvent) => {
      bevyRenderer?.scale_factor_change(appHandle, window.devicePixelRatio);
      resizeCanvasBy();
    };

    mediaQuery.addEventListener("change", onMediaQueryChange);

    let clientX: number;
    let clientY: number;

    canvas.addEventListener(
      "touchstart",
      function (e) {
        e.preventDefault();
        if (e.touches.length > 0) {
          clientX = e.touches[0]!.clientX;
          clientY = e.touches[0]!.clientY;
          bevyRenderer?.mouse_bt_down(appHandle, 0, clientX, clientY);
        }
      },
      true
    );

    let lastDeltaX = 0;
    let lastDeltaY = 0;
    canvas.addEventListener(
      "touchmove",
      function (e) {
        e.preventDefault();

        const _clientX =
          e.touches && e.touches[0] ? e.touches[0].clientX : 0;
        const _clientY =
          e.touches && e.touches[0] ? e.touches[0].clientY : 0;
        const deltaX =
          e.touches && e.touches[0] ? e.touches[0].clientX - clientX : 0;
        const deltaY =
          e.touches && e.touches[0] ? e.touches[0].clientY - clientY : 0;

        if (deltaX !== lastDeltaX) clientX = _clientX;
        if (deltaY !== lastDeltaY) clientY = _clientY;
        lastDeltaX = deltaX;
        lastDeltaY = deltaY;

        bevyRenderer?.mouse_move(appHandle, clientX, clientY, deltaX, deltaY);
      },
      true
    );

    canvas.addEventListener(
      "touchend",
      function (e) {
        e.preventDefault();
        bevyRenderer?.mouse_bt_up(appHandle, 0);
      },
      true
    );

    canvas.addEventListener(
      "touchcancel",
      function (e) {
        e.preventDefault();
        bevyRenderer?.mouse_bt_up(appHandle, 0);
      },
      true
    );

    canvas.addEventListener("mousedown", (e) => {
      let x = e.clientX; // / window.devicePixelRatio;
      let y = e.clientY; // / window.devicePixelRatio;
      // console.log("mousedown", x, y);
      bevyRenderer?.mouse_bt_down(appHandle, e.button, x, y);
    });

    useEventListener(document, "mouseup", (e) => {
      bevyRenderer?.mouse_bt_up(appHandle, e.button);
    });

    useEventListener(window, "focus", () => {
      // bevyRenderer?.window_focused(appHandle, true);
    });

    useEventListener(window, "blur", () => {
      // bevyRenderer?.window_focused(appHandle, false);
    });

    useEventListener(window, "mouseenter", () => {
      // bevyRenderer?.window_cursor_entered(appHandle);
    });

    useEventListener(window, "mouseleave", () => {
      // bevyRenderer?.window_cursor_left(appHandle);
    });

    useEventListener(document, "visibilitychange", () => {
      // bevyRenderer?.window_occluded(
      //   appHandle,
      //   document.visibilityState === "hidden"
      // );
    });

    useEventListener(canvas, "blur", () => {
      // bevyRenderer?.canvas_keyboard_focus_lost(appHandle);
    });

    canvas.addEventListener("mousemove", function (e) {
      bevyRenderer?.mouse_move(
        appHandle,
        e.offsetX,
        e.offsetY,
        e.movementX,
        e.movementY
      );
    });

    canvas.addEventListener("wheel", function (e) {
      e.preventDefault();
      bevyRenderer?.mouse_wheel(
        appHandle,
        e.deltaX,
        e.deltaY * -1,
        e.deltaZ
      );
    });

    canvas.addEventListener("contextmenu", function (e) {
      e.preventDefault();
    });

    useEventListener(document, "keydown", (e) => {
      // e.preventDefault();
      bevyRenderer?.key_down(appHandle, e.code, e.key);
    });

    useEventListener(document, "keyup", (e) => {
      // e.preventDefault();
      bevyRenderer?.key_up(appHandle, e.code, e.key);
    });
  });
}
