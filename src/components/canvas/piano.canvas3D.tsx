import { Box, Center, VStack } from "@hope-ui/solid";
import { createActiveElement } from "@solid-primitives/active-element";
import { useNavigate } from "@solidjs/router";
import { invoke } from "@tauri-apps/api/core";
import debounce from "lodash-es/debounce";
import { FaSolidBarsProgress } from "solid-icons/fa";
import {
  createEffect,
  createReaction,
  createRoot,
  createSignal,
  ErrorBoundary,
  lazy,
  onCleanup,
  onMount,
  Show,
  Suspense
} from "solid-js";
import { useService } from "solid-services";
import { Fn, useEventListener } from 'solidjs-use';
import * as webWorkerProxy from 'web-worker-proxy';
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppKeyboardMappingVisualizeVec, AppSettings } from "~/proto/pianorhythm-app-renditions";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import css from '~/sass/piano-container.module.sass';
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import KeyboardService from "~/services/keyboard.service";
import NotificationService from "~/services/notification.service";
import PlatformService from "~/services/platform.service";
import AppSettingsService from "~/services/settings-storage.service";
import { PianoRhythmSceneMode } from "~/types/app.types";
import { BevyRenderer, DefaultBevyRenderer, DefaultRenderingEngine, IRenderingEngine } from "~/types/renderer.types";
import { AppSettings as ClientAppSettings } from "~/types/settings.types";
import { CHANNELS, COMMON } from "~/util/const.common";
import { canCreateSharedArrayBuffer, encodeForProto } from "~/util/helpers";
import { checkForPassiveEvents, isWebWorkerSupported, supportsOffscreenCanvas } from "~/util/helpers.dom";
import { logDebug, logError, logInfo } from "~/util/logger";

// @ts-ignore
import * as webgpuRenderer from '@core/pkg/webgpu/pianorhythm_bevy_renderer';

// @ts-ignore
import * as webgl2Renderer from '@core/pkg/webgl2/pianorhythm_bevy_renderer';
import { RendererErrorHandler } from "./renderer/canvas-render-error-handler";
import { useCanvasRenderer } from "./renderer/canvas-renderer-provider";
import { setupTouchEvents } from "./renderer/canvas-touch-events";
import { createEventHandlers } from "./canvas.event-handler";

const Canvas2D = lazy(() => import("~/components/canvas/piano.canvas2D"));
const ActionWidgets = lazy(() => import("~/components/canvas/widgets/action-widgets"));
const AppMetaInfo = lazy(() => import("~/components/canvas/app-meta-info"));

const CANVAS_3D_CONTAINER_ID = "piano-canvas-3d-container";
const CANVAS_RENDERER_ID = "piano-canvas-renderer";

export const [rendererEngine3D, setRendererEngine3D] = createSignal<IRenderingEngine>();
export const [rendererEngine2D, setRendererEngine2D] = createSignal<IRenderingEngine>();

let validIDs = new Set([css.pianoCanvasContainer, CANVAS_RENDERER_ID, CANVAS_3D_CONTAINER_ID]);

export default function Canvas3D() {
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const keyboardService = useService(KeyboardService);
  const platformService = useService(PlatformService);
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);

  const navigate = useNavigate();
  const [eventListenerCleanups, setEventListenerCleanups] = createSignal<Fn[]>([]);
  const activeEl = createActiveElement();
  const [lastClicked, setLastClicked] = createSignal<HTMLElement | null>(null);
  const is2DMode = () => appService().sceneMode() == PianoRhythmSceneMode.TWO_D;
  const [canvasID, setCanvasID] = createSignal<string>();

  // State
  const {
    renderer, setRenderer,
    rendererPtr, setRendererPtr,
    canvasWorker, setCanvasWorker,
    webGpuSupported, useWebGPU, setUseWebGPU,
    runningRendererInMainThread, setRunningRendererInMainThread,
    canvasMounted, setCanvasMounted,
    initWebGPUSupport
  } = useCanvasRenderer();

  let canvasContainerRef!: HTMLDivElement;
  let canvasElementRef!: HTMLCanvasElement;
  let onCleanUpFunc: (() => void) | undefined = undefined;
  let animFram = 0;
  let isPaused = false;
  let on2DMountTimeout = -1;
  let appHandle: bigint = BigInt(0);

  // Error handler
  const errorHandler = new RendererErrorHandler(appService(), () => navigate("/"));

  onMount(async () => {
    await initWebGPUSupport();
    document.addEventListener('mousedown', handleClick);
  });

  // Click handler for focus tracking
  const handleClick = (event: MouseEvent) => {
    setLastClicked(event.target as HTMLElement);
  };

  const onSetRenderer = createReaction(() => {
    let engine: IRenderingEngine = {
      ...DefaultRenderingEngine
    };
    setRendererEngine3D(engine);
  });

  onSetRenderer(renderer);

  /**
   * Determines if keyboard events should be processed based on the currently focused element.
   *
   * @param activeElement - The currently focused DOM element, or null if no element is focused
   * @returns {boolean} Returns true if keyboard events should be processed, false otherwise
   *
   * The function returns true in the following cases:
   * - When no element is focused (activeElement is null)
   * - When the focused element is not an input control (input, textarea, select)
   * - When the focused element's ID matches specific valid containers
   */
  const handlePlayAreaFocus = (activeElement: HTMLElement | null) => {
    if (!activeElement) return true;

    const elementID = activeElement?.getAttribute?.("id") ?? "";
    const tagName = activeElement?.tagName?.toLowerCase() ?? "";

    const isInputElement = ["input", "textarea", "select"].includes(tagName);
    const isValidContainer = validIDs.has(elementID) || elementID === CANVAS_3D_CONTAINER_ID;

    return !isInputElement || isValidContainer;
  };

  // Update keyboard focus state
  createEffect(() => {
    const isFocused = handlePlayAreaFocus(lastClicked());
    keyboardService().setPlayAreaIsFocused(isFocused);
  });

  // Update keyboard mapping visualization
  createEffect(() => {
    let show = displayService().getDisplay("KEYBOARD_MAPPING_OVERLAY");
    if (show) {
      let input = AppKeyboardMappingVisualizeVec.create({
        mappings: keyboardService().getAllMappings(
          audioService().octave(),
          audioService().transpose()
        )
      });

      appService().coreService()?.send_app_action(
        AppStateActions.create({
          action: AppStateActions_Action.RendererSetKeyboardMappings,
          keyboardVisualizeMappings: input
        })
      );
    }

    appService().coreService()?.send_app_action(
      AppStateActions.create({
        action: AppStateActions_Action.RendererToggleDisplayKeyboardMappings,
        boolValue: show
      })
    );
  });

  // Handle desktop-specific behaviors
  createEffect(() => {
    if (COMMON.IS_DESKTOP_APP) {
      document.body.style.background =
        appService().sceneMode() == PianoRhythmSceneMode.THREE_D ? "transparent" : "#000";

      if (!keyboardService().playAreaIsFocused())
        renderer()?.canvas_keyboard_focus_lost(appHandle);
    }
  });

  // Pause 3D rendering if 2D mode is active
  createEffect(() => {
    let status = appService().sceneMode() == PianoRhythmSceneMode.TWO_D ? "pause" : "resume";

    if (runningRendererInMainThread()) {
      if (status == "pause") {
        isPaused = true;
        cancelAnimationFrame(animFram);
      } else {
        isPaused = false;
        animFram = requestAnimationFrame(enterFrame);
      }
    } else {
      canvasWorker()?.postMessage({ event: status });
    }
  });

  // Offscreen canvas support detection
  const useOffscreenCanvas = () =>
    supportsOffscreenCanvas() &&
    isWebWorkerSupported() &&
    (COMMON.USE_CORE_WASM_RENDERER && canCreateSharedArrayBuffer()) &&
    appSettingsService().getSetting<boolean>("GRAPHICS_USE_OFFSCREEN_CANVAS", true);

  let onErrorNotification = (disconnect = false) => {
    NotificationService.show({
      title: "3D Renderer Error",
      description: "An error occurred while loading the renderer. \n\nPlease check the console logs for more details.",
      type: "danger",
      closable: true,
      duration: 20_000
    });

    if (disconnect) {
      appService().onDisconnect();
      navigate("/");
    }
  };

  const onErrorEvent = (event: ErrorEvent) => {
    if (
      event.message.includes("Uncaught RuntimeError: unreachable") ||
      event.filename.includes("pianorhythm_bevy_renderer_bg")
    ) {
      onErrorNotification(true);
    }
  };

  const onRendererActionsReceived = (event: Event) => {
    let data = ((event as any)?.detail as Uint8Array);
    if (data == undefined) return;
    try {
      appService().coreService()?.send_app_action_bytes(data);
    } catch (ex) {
    }
  };

  const onRendererEffectsReceived = (event: Event) => {
    let bytes = ((event as any).detail as Uint8Array);
    if (bytes == undefined) {
      console.error("Renderer effect bytes not found.");
      return;
    }

    const effect = appService().decodeAppEffect(bytes);
    if (effect == undefined) return;
    processRendererEffect(effect);
  };

  const processRendererEffect = (effect: AppStateEffects) => {
    if (effect == undefined) return;

    switch (effect.action) {
      case AppStateEffects_Action.RendererLoaded: {
        logInfo("Renderer loaded.");
        let initSettings: ClientAppSettings = { ...appSettingsService().getSettings() };

        // Disables certain graphic features on mobile devices for first time
        if (platformService().isMobile()) {
          initSettings.GRAPHICS_ENABLE_SHADOWS = false;
          initSettings.GRAPHICS_ENABLE_ALL_PARTICLES = false;
          initSettings.GRAPHICS_ENABLE_ANTIALIAS = false;
        }

        let channel: BroadcastChannel | undefined = undefined;

        if (COMMON.IS_WEB_APP) {
          channel = new BroadcastChannel(CHANNELS.PIANORHYTHM_CORE_TO_RENDERER);

          channel.postMessage({
            event: "app_effects",
            data: AppStateEffects.encode(
              AppStateEffects.create({
                action: AppStateEffects_Action.AppSettingsUpdated,
                appSettings: AppSettings.fromJSON(encodeForProto(initSettings))
              })
            ).finish()
          });
        } else {
          appService().coreService()?.core_to_renderer_effects(
            AppStateEffects.encode(
              AppStateEffects.create({
                action: AppStateEffects_Action.AppSettingsUpdated,
                appSettings: AppSettings.fromJSON(encodeForProto(initSettings))
              })
            ).finish()
          );
        }

        appService().queuedEffectsForRenderer().forEach((effect) => {
          if (COMMON.IS_WEB_APP)
            channel?.postMessage({ event: "app_effects", data: effect });

          if (COMMON.IS_DESKTOP_APP)
            appService().coreService()?.core_to_renderer_effects(effect);
        });

        appService().queuedEventsForRenderer().forEach((effect) => {
          if (COMMON.IS_WEB_APP)
            channel?.postMessage({ event: "app_events", data: effect });

          if (COMMON.IS_DESKTOP_APP)
            appService().coreService()?.core_to_renderer_events(effect);
        });

        appService().setQueuedEffectsForRenderer([]);
        appService().setQueuedEventsForRenderer([]);
        appService().setQueuedEffectsForRendererConsumed(true);
        appService().sendIsMobileAppAction(platformService().isMobile());
        renderer()?.scale_factor_change(appHandle, window.devicePixelRatio);
        channel?.close();
        break;
      }
    }
  };

  const onRendererEventsReceived = (event: Event) => {
    let data = ((event as any)?.detail as number);
    if (data == undefined) return;
    appService().onHandleAppEvent(data ?? 0);
  };

  let initFinished = 0;

  function enterFrame(_dt: any) {
    if (appHandle <= 0 || isPaused || !renderer()) return;

    if (initFinished > 0) {
      renderer()?.enter_frame(appHandle);
    } else {
      initFinished = renderer()?.is_preparation_completed(appHandle) ?? 0;
    }
    animFram = requestAnimationFrame(enterFrame);
  }

  async function mountNoRenderer() {
    dispatchRendererLoadedEffect(onRendererEffectsReceived);

    on2DMountTimeout = window.setTimeout(() => {
      appService().setActivatePageLoader(false);
    }, 3000);
  }

  const resizeCanvasBy = debounce(() => {
    let elem = document.getElementById(canvasID()!);
    if (!elem) return;

    if (canvasElementRef) {
      canvasElementRef.style.width = window.innerWidth + "px";
      canvasElementRef.style.height = window.innerHeight + "px";
      canvasElementRef.style.minWidth = `${180}px`;
      canvasElementRef.style.minHeight = `${120}px`;
    }

    renderer()?.window_resize(appHandle,
      window.innerWidth, window.innerHeight,
      elem.clientWidth, elem.clientHeight
    );

    renderer()?.scale_factor_change(appHandle, window.devicePixelRatio);
  }, 100);

  function mount3DCanvas(mountedCanvas: HTMLCanvasElement) {
    if (!mountedCanvas || canvasMounted() || canvasWorker() || COMMON.IS_AUTOMATED_TEST_MODE) return;

    if (!appSettingsService().getSetting("GRAPHICS_ENABLE_ENGINE", true)) {
      return mountNoRenderer();
    }

    setCanvasMounted(true);
    setCanvasID(mountedCanvas.parentElement?.id ?? mountedCanvas.id);
    canvasElementRef = mountedCanvas;

    // Determine which rendering approach to use
    let shouldUseOffscreenCanvas = determineCanvasMode();

    setupRendererEventListeners();

    if (shouldUseOffscreenCanvas) {
      loadViaOffscreenCanvas(mountedCanvas);
    } else {
      loadRenderingEngine(true);
    }

    createEventHandlers(mountedCanvas, renderer(), appHandle, resizeCanvasBy);
  }

  // Determine whether to use offscreen canvas
  function determineCanvasMode(): boolean {
    let shouldUseOffscreen = useOffscreenCanvas();

    // Check localStorage for override
    try {
      const storedValue = appSettingsService().getLocalStorage("useOffscreenCanvas", "false");
      shouldUseOffscreen = JSON.parse(storedValue as string) ?? shouldUseOffscreen;
    } catch {
    }

    // WebGPU requirements for offscreen canvas
    if (!useWebGPU() || !canCreateSharedArrayBuffer()) shouldUseOffscreen = false;

    // Check localStorage for WebGPU preference
    try {
      const storedWebGpuValue = JSON.parse(appSettingsService().getLocalStorage("useWebGpu", "false") as string);
      if (webGpuSupported()) {
        setUseWebGPU(JSON.parse(storedWebGpuValue as string) ?? useWebGPU());
      }
    } catch {
    }

    return shouldUseOffscreen;
  }

  // Set up event listeners for renderer communication
  function setupRendererEventListeners() {
    if (COMMON.IS_WEB_APP) {
      self.addEventListener("renderer_effects", onRendererEffectsReceived);
      self.addEventListener("renderer_events", onRendererEventsReceived);
      self.addEventListener("renderer_actions", onRendererActionsReceived);
      self.addEventListener("error", errorHandler.handleErrorEvent);
    } else if (COMMON.IS_DESKTOP_APP) {
      appService().appStateEffects.listen(processRendererEffect);
    }
  }

  // Load the rendering engine
  async function loadRenderingEngine(mainThread: boolean = true, forceUseWebGl2 = false) {
    try {
      setRunningRendererInMainThread(mainThread);
      if (forceUseWebGl2) setUseWebGPU(false);

      if (appSettingsService().isDebugMode()) {
        logDebug(`Loading Bevy renderer: ${mainThread ? "Main thread" : "Web worker"}`);
      }

      checkForPassiveEvents();
      const devicePixelRatio = window.devicePixelRatio ?? 1;

      // Setup renderer based on platform
      if (COMMON.IS_DESKTOP_APP) {
        setupDesktopRenderer();
      } else if (mainThread) {
        await setupMainThreadRenderer(devicePixelRatio);
      } else {
        await setupWorkerRenderer();
      }

      // Set up common event listeners
      setupCommonEventListeners(renderer());

    } catch (err) {
      if (!String(err).toLowerCase().includes("this isn't actually an error")) {
        errorHandler.handleError("Error loading Bevy renderer");
      }
    }
  }

  // Set up desktop renderer
  function setupDesktopRenderer() {
    const bevyRenderer = {
      ...DefaultBevyRenderer,
      mouse_wheel: (_: bigint, deltaX: number, deltaY: number, delta: number) => {
        invoke("mouse_wheel", { deltaX, deltaY, delta });
      },
      scale_factor_change: (_: bigint, scale: number) => {
        invoke("scale_factor_change", { scale });
      },
      window_cursor_entered: (_: bigint) => {
        invoke("window_cursor_entered");
      },
      window_cursor_left: (_: bigint) => {
        invoke("window_cursor_left");
      },
      window_occluded: (_: bigint, occluded: boolean) => {
        invoke("window_occluded", { occluded });
      },
      window_focused: (_: bigint, focused: boolean) => {
        invoke("window_focused", { focused });
      },
      window_resize: (_: bigint, width: number, height: number, clientWidth: number, clientHeight: number) => {
        invoke("window_resize", { width, height, clientWidth, clientHeight });
      },
      canvas_keyboard_focus_lost: (_: bigint) => {
        invoke("canvas_keyboard_focus_lost");
      },
      key_down: (_: bigint, code: string, key: string) => {
        invoke("keyboard_key_down", { code, key });
      },
      key_up: (_: bigint, code: string, key: string) => {
        invoke("keyboard_key_up", { code, key });
      },
      mouse_bt_down(_: any, index: number, x: number, y: number) {
        invoke("mouse_bt_down", { button: index, x, y });
      },
      mouse_bt_up(_: any, index: number) {
        invoke("mouse_bt_up", { button: index });
      },
      mouse_move(_: any, x: number, y: number, deltaX: number, deltaY: number) {
        invoke("mouse_move", {
          x: x * window.devicePixelRatio,
          y: y * window.devicePixelRatio,
          deltaX, deltaY
        });
      }
    };
    setRenderer(bevyRenderer);

    // Immediately set that the renderer loaded
    dispatchRendererLoadedEffect(onRendererEffectsReceived);
    logInfo("Loaded Bevy renderer: Desktop");
  }

  // Set up renderer in main thread
  async function setupMainThreadRenderer(devicePixelRatio: number) {
    // @ts-ignore
    const bevyRenderer = useWebGPU() ? webgpuRenderer : webgl2Renderer;
    logDebug(`Using ${useWebGPU() ? "WebGPU" : "WebGL2"} renderer.`);

    if (!bevyRenderer) {
      errorHandler.handleError("Bevy renderer not found");
      throw new Error("Bevy renderer not found.");
    }

    await bevyRenderer.default();

    if (useWebGPU()) {
      setRenderer(bevyRenderer as any);

      // @ts-ignore
      appHandle = bevyRenderer.create_app(false, audioService().synth_events_shared_buffer());
      setRendererPtr(appHandle);

      bevyRenderer?.create_window_by_canvas(appHandle, canvasID()!, devicePixelRatio);

      animFram = requestAnimationFrame(enterFrame);
    }
  }

  // Set up renderer in worker
  function setupWorkerRenderer() {
    return new Promise(async (resolve, reject) => {
      if (!canvasWorker()) {
        errorHandler.handleError("Canvas worker not found");
        reject("Canvas worker not found.");
      }

      canvasWorker()!.onmessage = (evt) => {
        switch (evt.data.event) {
          case "wasm-module-loaded": {
            appHandle = evt.data.payload.handle;
            setRendererPtr(appHandle);
            resizeCanvasBy();
            let proxy = webWorkerProxy.create(canvasWorker()!);
            setRenderer(proxy);
            resolve(null);
            break;
          }
          case "renderer_effects": {
            onRendererEffectsReceived(new CustomEvent("renderer_effects", { detail: evt.data.payload }));
            break;
          }
          case "renderer_events": {
            onRendererEventsReceived(new CustomEvent("renderer_events", { detail: evt.data.payload }));
            break;
          }
          case "renderer_actions": {
            onRendererActionsReceived(new CustomEvent("renderer_actions", { detail: evt.data.payload }));
            break;
          }
        }
      };
    });
  }

  // Setup common event listeners
  function setupCommonEventListeners(bevyRenderer?: BevyRenderer) {
    if (!bevyRenderer) {
      console.error("Bevy renderer not found.");
      return;
    }

    window.addEventListener("resize", resizeCanvasBy);

    const mediaQuery = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`);
    const onMediaQueryChange = () => {
      bevyRenderer?.scale_factor_change(appHandle, window.devicePixelRatio);
      resizeCanvasBy();
    };
    mediaQuery.addEventListener('change', onMediaQueryChange);

    onCleanUpFunc = () => {
      window.removeEventListener("resize", resizeCanvasBy);
      mediaQuery.removeEventListener('change', onMediaQueryChange);
    };

    // Set up document-level event listeners
    setEventListenerCleanups(v => [
      ...v,
      ...setupDocumentEventListeners(bevyRenderer, appHandle)
    ]);

    // Set up canvas-specific event listeners
    let canvas = canvasElementRef;
    if (canvas) {
      const cleanupTouch = setupTouchEvents(canvas, bevyRenderer!, appHandle);
      setEventListenerCleanups(v => [...v, cleanupTouch]);

      createRoot(() => {
        useEventListener(canvas, "blur", () => bevyRenderer?.canvas_keyboard_focus_lost(appHandle));

        useEventListener(canvas, "mousemove", (e) => {
          bevyRenderer?.mouse_move(appHandle, e.offsetX, e.offsetY, e.movementX, e.movementY);
        });

        useEventListener(canvas, "wheel", (e) => {
          bevyRenderer?.mouse_wheel(appHandle, e.deltaX, e.deltaY * -1, e.deltaZ);
        });

        useEventListener(canvas, "contextmenu", (e) => e.preventDefault());
      })
    }
  }

  // Set up document-level event listeners
  function setupDocumentEventListeners(bevyRenderer?: BevyRenderer, appHandle?: bigint) {
    if (!bevyRenderer) {
      console.error("Bevy renderer not found.");
      return [];
    }

    return createRoot(() => [
      useEventListener(document, 'mousedown', e => {
        bevyRenderer?.mouse_bt_down(appHandle!, e.button, e.clientX, e.clientY);
      }),

      useEventListener(document, 'mouseup', e => {
        bevyRenderer?.mouse_bt_up(appHandle!, e.button);
      }),

      useEventListener(window, "mouseenter", () => {
        bevyRenderer?.window_cursor_entered(appHandle!);
      }),

      useEventListener(window, "mouseleave", () => {
        bevyRenderer?.window_cursor_left(appHandle!);
      }),

      useEventListener(document, "visibilitychange", () => {
        bevyRenderer?.window_occluded(appHandle!, document.visibilityState == "hidden");
      }),

      useEventListener(document, "keydown", e => {
        bevyRenderer?.key_down(appHandle!, e.code, e.key);
      }),

      useEventListener(document, "keyup", e => {
        bevyRenderer?.key_up(appHandle!, e.code, e.key);
      })
    ]);
  }

  let loadViaOffscreenCanvas = (mountedCanvas: HTMLCanvasElement) => {
    // Note: WebGL2 can only be used in the main thread and not in a web worker
    const attemptWebgl2 = () => {
      NotificationService.show({
        title: "WebGPU Error",
        description: "An error occurred while loading WebGPU. \n\nAttempting to load the WebGL2 renderer instead.",
        type: "warning",
        closable: true,
        duration: 10_000
      });

      // Try loading via webgl2
      loadRenderingEngine(true, true)
        .then(() => {
          // Remove canvas worker
          canvasWorker()?.terminate();
        })
        .catch((err) => {
          logError("Error loading WebGL2 renderer" + err);
          onErrorNotification();
        });
    };

    logDebug("Using offscreen canvas.");
    import('~/workers/canvas.worker.ts?worker')
      .then(async (module) => {
        const worker: Worker = new (module).default({ name: "canvas-worker" });
        setCanvasWorker(worker as any);

        if (!worker) {
          throw new Error("Canvas worker not found.");
        }

        if ("navigator" in window && "gpu" in navigator) {
          (navigator.gpu as any)?.requestAdapter?.()
            .then(async (adapter: any) => {
              if (!adapter) {
                throw new Error("WebGPU adapter not found.");
              }

              const device = await adapter.requestDevice();
              if (!device) {
                throw new Error("WebGPU device not found.");
              }

              let offscreenCanvas = mountedCanvas.transferControlToOffscreen();
              worker.postMessage({
                event: "load-module",
                payload: {
                  offscreenCanvas,
                  devicePixelRatio,
                  useWebGPU: useWebGPU(),
                  synthEventsSharedBuffer: audioService().synth_events_shared_buffer()
                }
              }, [offscreenCanvas]);
            }).catch((err: any) => {
            logError("Error loading WebGPU adapter: " + err);
            attemptWebgl2();
          });
        } else {
          throw new Error("WebGPU not supported.");
        }
      })
      .then(() => loadRenderingEngine(false))
      .catch((err) => {
        logError("Error loading Bevy renderer" + err);
        attemptWebgl2();
      });
  };

  function dispatchRendererLoadedEffect(onRendererEffectsReceived: (event: Event) => void) {
    onRendererEffectsReceived(new CustomEvent("renderer_effects", {
      detail: new Uint8Array(AppStateEffects.encode(
        AppStateEffects.create({
          action: AppStateEffects_Action.RendererLoaded
        })
      ).finish())
    }));
  }

  onCleanup(() => {
    if (rendererPtr() != null) renderer()?.release_app?.(rendererPtr()!);
    canvasWorker()?.terminate();
    cancelAnimationFrame(animFram);

    if (COMMON.IS_WEB_APP) {
      self.removeEventListener("renderer_effects", onRendererEffectsReceived);
      self.removeEventListener("renderer_events", onRendererEventsReceived);
      self.removeEventListener("renderer_actions", onRendererActionsReceived);
      self.removeEventListener("error", onErrorEvent);
    }

    onCleanUpFunc?.();
    eventListenerCleanups().forEach(fn => fn());
    window.clearTimeout(on2DMountTimeout);
    keyboardService().setPlayAreaIsFocused(false);
    document.removeEventListener('mousedown', handleClick);
  });

  return (
    <>
      {/* Build Meta Data */}
      <Suspense>
        <ErrorBoundary fallback={
          <Box
            position={"absolute"}
            bottom={"calc(var(--bottomBarHeight) + 5px)"}
            right={5}
            fontSize={"0.35em"}
            color="rgba(255,255,255,0.3)"
            userSelect={"none"}
            display={{ "@initial": "none", "@sm": "flex" }}
          >
            <VStack spacing={"$1"}>
              <Box>Version: {COMMON.CLIENT_VERSION} (ALPHA)</Box>
              <Box>Build Date: {COMMON.CLIENT_BUILD_DATE}</Box>
            </VStack>
          </Box>
        }>
          {appService().canInteract() && <AppMetaInfo/>}
        </ErrorBoundary>
      </Suspense>

      {/* Piano Canvas 3D */}
      <Show
        when={appService().initialized()}
        fallback={
          <Center ref={mountNoRenderer} w="100vw" h="100dvh">
            <Box mr="1"><FaSolidBarsProgress/></Box>
            <Box>Loading canvas. Please wait...</Box>
          </Center>
        }
      >
        <ErrorBoundary fallback={(err) => {
          console.error("Mount canvas error", err);
          return <Box ref={mountNoRenderer}></Box>;
        }}>
          {/* Canvas 3D */}
          <Box
            id={CANVAS_3D_CONTAINER_ID}
            w="100%"
            h="100%"
            ref={canvasContainerRef}
            tabIndex={0}
          >
            <canvas
              draggable={false}
              ref={mount3DCanvas}
              raw-window-handle="1"
              style={{
                "display": (is2DMode()) ? "none" : "block",
                "pointer-events": (!appService().canInteract() || is2DMode()) ? "none" : "all"
              }}
              width={window.innerWidth}
              height={window.innerHeight}
              class="unselectable undraggable"
              id={CANVAS_RENDERER_ID}
            />
          </Box>

          {/* Piano Canvas 2D */}
          {(is2DMode()) &&
              <Suspense>
                  <ErrorBoundary
                      fallback={<Box textAlign={"center"} color={"$danger10"}>Failed to render 2D Piano</Box>}>
                      <Canvas2D
                          onMount={async (input, containerIDs) => {
                            setRendererEngine2D(input);
                            containerIDs.forEach(x => validIDs = validIDs.add(x));
                            window.clearTimeout(on2DMountTimeout);
                            on2DMountTimeout = window.setTimeout(() => {
                              appService().setActivatePageLoader(false);
                            }, 3000);
                          }}
                          onCleanup={() => {
                            setRendererEngine2D();
                            window.clearTimeout(on2DMountTimeout);
                          }}
                          useOffscreenCanvas={useOffscreenCanvas()}/>
                  </ErrorBoundary>
              </Suspense>
          }

          <Suspense>
            <ErrorBoundary
              fallback={<Box textAlign={"center"} color={"$danger10"}>Failed to render action widgets</Box>}>
              <ActionWidgets/>
            </ErrorBoundary>
          </Suspense>
        </ErrorBoundary>
      </Show>
    </>
  );
}