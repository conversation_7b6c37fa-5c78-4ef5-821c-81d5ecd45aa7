import { debounce } from "lodash-es";
import { Subject, Subscription, filter, throttleTime } from "rxjs";
import { createSignal } from "solid-js";
import { useService } from "solid-services";
import { AppKeyboardMappingVisualizeVec } from "~/proto/pianorhythm-app-renditions";
import { ClientSideUserDto, UserClientDto } from "~/proto/user-renditions";
import AudioService from "~/services/audio.service";
import { PianoRhythmSynthEvent, PianoRhythmSynthEventName, SynthEventsProcessor } from "~/types/audio.types";
import { DefaultRenderingEngine, IRenderingEngine } from "~/types/renderer.types";
import { UserClientDomain } from "~/types/user-helper";
import { AUDIO, CHANNELS, COMMON } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";
import { getNow, rainbowColorArray } from "~/util/helpers";
import { INOTE, PianoKey2D, PianoMsgParam, eAnimationType } from "./piano-canvas.types";

/**
 * http://stackoverflow.com/questions/1255512/how-to-draw-a-rounded-rectangle-on-html-canvas
 * Draws a rounded rectangle using the current state of the canvas.
 * If you omit the last three params, it will draw a rectangle
 * outline with a 5 pixel border radius
 * @param {CanvasRenderingContext2D} ctx
 * @param {Number} x The top left x coordinate
 * @param {Number} y The top left y coordinate
 * @param {Number} width The width of the rectangle
 * @param {Number} height The height of the rectangle
 * @param {Number} [radius = 5] The corner radius; It can also be an object
 *                 to specify different radii for corners
 * @param {Number} [radius.tl = 0] Top left
 * @param {Number} [radius.tr = 0] Top right
 * @param {Number} [radius.br = 0] Bottom right
 * @param {Number} [radius.bl = 0] Bottom left
 * @param {Boolean} [fill = false] Whether to fill the rectangle.
 * @param {Boolean} [stroke = true] Whether to stroke the rectangle.
 */
function roundRect(ctx: CanvasRenderingContext2D, x: number, y: number, width: number, height: number, radius: number | {
  tl: number;
  tr: number;
  bl: number;
  br: number;
}, fill: boolean, stroke: boolean) {
  if (typeof stroke == 'undefined') {
    stroke = true;
  }
  if (typeof radius === 'undefined') {
    radius = 5;
  }
  if (typeof radius === 'number') {
    radius = { tl: radius, tr: radius, br: radius, bl: radius };
  } else {
    let defaultRadius = { tl: 0, tr: 0, br: 0, bl: 0 };
    for (let side in defaultRadius) {
      // @ts-ignore
      radius[side] = radius[side] || defaultRadius[side];
    }
  }
  ctx.beginPath();
  ctx.moveTo(x + radius.tl, y);
  ctx.lineTo(x + width - radius.tr, y);
  ctx.quadraticCurveTo(x + width, y, x + width, y + radius.tr);
  ctx.lineTo(x + width, y + height - radius.br);
  ctx.quadraticCurveTo(x + width, y + height, x + width - radius.br, y + height);
  ctx.lineTo(x + radius.bl, y + height);
  ctx.quadraticCurveTo(x, y + height, x, y + height - radius.bl);
  ctx.lineTo(x, y + radius.tl);
  ctx.quadraticCurveTo(x, y, x + radius.tl, y);
  ctx.closePath();
  if (fill) {
    ctx.fill();
  }
  if (stroke) {
    ctx.stroke();
  }
}

const PianoCanvasRenderingEngine = (_synthEvents?: Subject<PianoRhythmSynthEvent>) => {
  let keyToNote = Object(); // C8  == 108
  let noteToKey = Object(); // 108 ==  C8
  let initialized = false;
  let fpsInterval: number = -1;
  let fpsTimes: number[] = [];
  let fps = 0;
  let now: number = 0;
  let then: number = 0;
  let elapsed: number = 0;
  let lastCalledTime: number = 0;
  let _now: number = 0;
  let _time: number = 0;
  let WHITE_KEY_WIDTH: number;
  let BLACK_KEY_WIDTH: number;
  let WHITE_KEY_HEIGHT: number;
  let CANVAS_2D_CTX: CanvasRenderingContext2D;
  let WHITE_KEY_HEIGHT_PERCENTAGE = 0.9;
  let BLACK_KEY_HEIGHT_PERCENTAGE = 0.55;
  let BLACK_KEY_HEIGHT: number;
  let BLACK_KEY_OFFSET: number;
  let _key: PianoKey2D;
  let KEYS = Object();
  let BLACK_KEYS = Object();
  let WHITE_KEYS = Object();
  let _keyPress: PianoKey2D | undefined = undefined;
  let _keyRelease: PianoKey2D | undefined = undefined;
  let REDRAW_ALL_CANVAS: boolean = true;
  let BLIP_ANIMATE_TIME_TYPE: number = 0;
  let BLIP_GRAVITY: number = 0;
  let BLIP_EFFECT: number = 0;
  let KEY_ANIMATION_BACKGROUND: boolean = true;
  let disposing = false;
  let raf = 0;
  let showFPSInterval = -1;
  let wasmInstantiating = false;
  let wasmInstantiated = false;
  let processor: ReturnType<typeof SynthEventsProcessor>;

  const pianoKeys2D = new Map<number, PianoKey2D>();
  const [users, setUsers] = createSignal<ClientSideUserDto[]>([]);
  const [client, setClient] = createSignal<UserClientDomain>();
  const [keyboardMappings, setKeyboardMappings] = createSignal<AppKeyboardMappingVisualizeVec>();
  const MidiRainbowColorArray = rainbowColorArray(128);

  let ANIMATION_TYPE: eAnimationType = eAnimationType.SQUARES;
  let DEFAULT_WHITEKEY_COLOR = "white";
  let DEFAULT_BLACKKEY_COLOR = "#3a3e4c";
  let proxyReceiverObject2D: { canvas: HTMLCanvasElement, proxy: any; } | null = null;
  const audioService = useService(AudioService);

  let canvasChannel: BroadcastChannel | undefined = undefined;
  let synthEvents: BroadcastChannel | undefined = undefined;
  let synthEventsToProcess: Subject<{ eventName: PianoRhythmSynthEventName, event: PianoRhythmSynthEvent; }>;

  const [subscriptions, setSubscriptions] = createSignal<Subscription[]>([]);
  const distinctEvents: PianoRhythmSynthEventName[] = [
    "DAMPER_PEDAL",
    "ALL_NOTES_OFF",
    "ALL_SOUND_OFF"
  ];

  const eventsThatCanBeProcessed: PianoRhythmSynthEventName[] = [
    "NOTE_ON",
    "NOTE_OFF",
    "DAMPER_PEDAL",
    "ALL_NOTES_OFF",
    "ALL_SOUND_OFF"
  ];

  const [canvasLoaded, setCanvasLoaded] = createSignal<{ twoD: boolean, threeD: boolean; }>(
    { twoD: false, threeD: false }
  );

  function getRainborColorFromNote(note: number) {
    let color = MidiRainbowColorArray[note];
    return color || "#000";
  }

  const getUserColor = (note: number, socketID?: string) => {
    let color: string | undefined;

    if (socketID) {
      let user = users().find(x => x.socketID == socketID);
      color = user?.userDto?.color?.toLowerCase();
    } else {
      color = client()?.usercolor?.toLowerCase();
    }

    if (color == "rainbow") {
      return getRainborColorFromNote(note);
    }

    return color ?? "#000";
  };

  // const getUserColor = memoize(_getUserColor);

  const initialize = () => {
    if (initialized) return;
    initialized = true;

    (function () {
      let A0 = 0x15; // first note
      let C8 = 0x6C; // last note
      let number2key = ['C', 'Db', 'D', 'Eb', 'E', 'F', 'Gb', 'G', 'Ab', 'A', 'Bb', 'B'];
      for (let n = A0; n <= C8; n++) {
        let octave = (n - 12) / 12 >> 0;
        let name = number2key[n % 12] ?? octave;

        keyToNote[name] = n;
        noteToKey[n] = name;
      }
    })();

    canvasChannel = new BroadcastChannel(CHANNELS.CANVAS_BUS);
    synthEventsToProcess = new Subject();

    processor = SynthEventsProcessor((eventName: PianoRhythmSynthEventName, event: PianoRhythmSynthEvent, source?: string) => {
      if (event.source == 2) {
        return;
      }

      emitForHandleSynthEvent(eventName, event);
    });

    if (COMMON.IS_WEB_APP) {
      synthEvents = new BroadcastChannel(CHANNELS.PIANORHYTHM_SYNTH_EVENTS);
      synthEvents.onmessage = (event) => {
        let data = event.data as PianoRhythmSynthEvent;
        if (data) processor.processSynthEvent(data);
      };
    }

    // Usual events
    setSubscriptions([
      _synthEvents?.subscribe((output) => {
        processor.processSynthEvent(output);
      }),
      synthEventsToProcess
        .pipe(filter(x =>
          !distinctEvents.includes(x.eventName) &&
          eventsThatCanBeProcessed.includes(x.eventName)
        ))
        .subscribe((output) => {
          let { eventName, event } = output;
          onHandleSynthEvent(eventName, event);
        }),

      // Distinct all sound/notes off events
      synthEventsToProcess
        .pipe(filter(x => (x.eventName == "ALL_SOUND_OFF" || x.eventName == "ALL_NOTES_OFF")))
        .pipe(throttleTime(1000))
        .subscribe((output) => onHandleSynthEvent(output.eventName, output.event))
    ].filter(Boolean) as any);

    onShowFPS();
  };

  const emitForHandleSynthEvent = (eventName: PianoRhythmSynthEventName, event: PianoRhythmSynthEvent) => {
    synthEventsToProcess.next({ eventName, event });
  };

  const BlackKeyRender = (text?: string, options = {
    width: null,
    height: null,
    solid: null,
    color: null,
    rounded: null
  }) => {
    let blackKeyRender = document.createElement("canvas");
    blackKeyRender.width = options.width || BLACK_KEY_WIDTH + 10;
    blackKeyRender.height = (options.height || BLACK_KEY_HEIGHT) + 1;
    let ctx = blackKeyRender.getContext("2d");
    if (ctx == null) return null;

    let rounded = !!(options && options.rounded);
    let solid = !!(options && options.solid);
    let color = (options && options.color) ? options.color : DEFAULT_BLACKKEY_COLOR;

    if (!solid) {
      let grd = ctx.createLinearGradient(0, 0, BLACK_KEY_WIDTH, BLACK_KEY_HEIGHT);
      grd.addColorStop(0, 'rgba(60, 60, 65, 1)');
      grd.addColorStop(0.3, 'rgba(35, 35, 45, 0.95)');
      grd.addColorStop(1, color);
      ctx.fillStyle = grd;

    } else {
      ctx.fillStyle = color;
    }

    if (rounded) {
      let roundedVal = { tl: 0, tr: 0, bl: 5, br: 5 };
      // @ts-ignore
      if (options && options["roundVal"]) roundedVal = options["roundVal"];

      roundRect(ctx, 0, 0, blackKeyRender.width - 1, blackKeyRender.height, roundedVal, true, false);
    } else {
      ctx.fillRect(ctx.lineWidth / 2, (ctx.lineWidth / 2) - 1, BLACK_KEY_WIDTH - ctx.lineWidth, blackKeyRender.height - ctx.lineWidth);
    }

    if (text) {
      ctx.fillStyle = "white";
      ctx.font = "12px Segoe UI";
      ctx.fillText(text, 2, blackKeyRender.height - 15);
    }

    return blackKeyRender;
  };

  const WhiteKeyRender = (text?: string, options = {
    width: null,
    height: null,
    solid: null,
    color: null,
    rounded: null
  }) => {
    let whiteKeyRender = document.createElement("canvas");
    whiteKeyRender.width = options.width || WHITE_KEY_WIDTH;
    whiteKeyRender.height = options.height || WHITE_KEY_HEIGHT;
    let ctx = whiteKeyRender.getContext("2d");
    if (ctx == null) return null;

    let rounded = !!(options && options.rounded);
    let solid = !!(options && options.solid);
    let color = (options && options.color) ? options.color : DEFAULT_WHITEKEY_COLOR;

    if (!solid) {

      if (ctx.createLinearGradient) {
        let gradient = ctx.createLinearGradient(0, 0, 0, WHITE_KEY_HEIGHT);
        gradient.addColorStop(0.000, 'rgba(220, 220, 220, 1)');
        gradient.addColorStop(1.000, color);
        ctx.fillStyle = gradient;
      }

    } else {
      ctx.fillStyle = color;
    }

    ctx.strokeStyle = 'rgba(0, 0, 0, 0.2)';

    if (rounded) {
      let roundedVal = { tl: 0, tr: 0, bl: 5, br: 5 };
      // @ts-ignore
      if (options && options["roundVal"]) roundedVal = options["roundVal"];
      roundRect(ctx, 0, 0, whiteKeyRender.width - 1, whiteKeyRender.height, roundedVal, true, true);
    } else {
      if (!REDRAW_ALL_CANVAS) ctx.lineWidth = 3;
      ctx.fillRect(ctx.lineWidth / 2, ctx.lineWidth / 2, WHITE_KEY_WIDTH - ctx.lineWidth, whiteKeyRender.height - ctx.lineWidth);

    }

    if (text) {
      ctx.fillStyle = "black";
      ctx.font = "20px Segoe UI";
      ctx.fillText(text, 5, whiteKeyRender.height - 20);
    }

    return whiteKeyRender;
  };

  let setDimensions = (canvas: HTMLCanvasElement) => {
    WHITE_KEY_HEIGHT = Math.floor(canvas.height * (WHITE_KEY_HEIGHT_PERCENTAGE));
    WHITE_KEY_WIDTH = Math.floor(canvas.width / 52);
    BLACK_KEY_HEIGHT = Math.floor(canvas.height * (BLACK_KEY_HEIGHT_PERCENTAGE));
    BLACK_KEY_WIDTH = Math.floor(WHITE_KEY_WIDTH * (0.75));
    BLACK_KEY_OFFSET = Math.floor(WHITE_KEY_WIDTH - (BLACK_KEY_WIDTH / 2));
  };

  const drawPiano = () => {
    let keys = ['A', 'B', 'C', 'D', 'E', 'F', 'G'];
    let keys2 = ['A', 'B', 'D', 'E', 'G'];
    let keyID = 0;
    let noteLoop = 0;
    let whiteKeys = [];
    let blackKeys = [];
    let white_spatial = 0;
    let black_spatial = 0;

    let black_it = 0;
    let black_lut = [2, 1, 2, 1, 1];

    for (let i = 0; i < 52; i++) {

      if (noteLoop > 6) noteLoop = 0;
      let note = keys[noteLoop];
      if (!note) continue;

      if (note === 'C' || note === 'F') {
        //White Keys
        let key = new PianoKey2D("white", WhiteKeyRender()!);

        key.width = WHITE_KEY_WIDTH;
        key.height = WHITE_KEY_HEIGHT;
        key.key = i;
        key.id = note + keyID;
        key.midiID = noteToMidi(key.id) || -1;

        key.spatial = white_spatial;
        ++white_spatial;
        key.x = key.spatial * key.width;
        key.y = 0;
        whiteKeys.push(key);
      } else {
        //White Keys
        let key = new PianoKey2D("white", WhiteKeyRender()!);
        key.width = WHITE_KEY_WIDTH;
        key.height = WHITE_KEY_HEIGHT;
        key.key = i;
        key.id = note + keyID;
        key.midiID = noteToMidi(key.id) || -1;
        key.spatial = white_spatial;
        ++white_spatial;
        key.x = key.spatial * key.width;
        key.y = 0;
        whiteKeys.push(key);

        let nv = keys2.indexOf(note) + 1;
        if (nv >= keys2.length) nv = 0;

        let id = keys2[nv] + "b" + keyID;

        if (keys2[nv] === "D")
          id = keys2[nv] + "b" + (keyID + 1);

        let bkey = new PianoKey2D("black", BlackKeyRender()!);
        bkey.width = BLACK_KEY_WIDTH;
        bkey.height = BLACK_KEY_HEIGHT;
        bkey.id = id;
        bkey.key = i;
        bkey.spatial = black_spatial;
        black_spatial += black_lut[black_it % 5] ?? 0;
        ++black_it;
        bkey.y = 0;
        bkey.x = BLACK_KEY_OFFSET + WHITE_KEY_WIDTH * bkey.spatial;
        bkey.midiID = noteToMidi(bkey.id) || -1;
        blackKeys.push(bkey);
      }
      noteLoop++;

      //Increase octave id on every C note
      if (keys[noteLoop] === 'C') keyID++;
    }

    //Render
    for (let w in whiteKeys) {
      if (whiteKeys[w]?.id) {
        KEYS[whiteKeys[w].id] = whiteKeys[w];
        WHITE_KEYS[whiteKeys[w].id] = whiteKeys[w];
      }
    }

    //Pop out last black key
    blackKeys.pop();

    for (let b in blackKeys) {
      if (!blackKeys[b]?.id) continue;
      KEYS[blackKeys[b].id] = blackKeys[b];
      BLACK_KEYS[blackKeys[b].id] = blackKeys[b];
    }

    Object.values(KEYS).forEach((x: any) => pianoKeys2D.set(x.midiID, x));
  };

  const startAnimating = (fps = 60) => {
    fpsInterval = 1000 / fps;
    then = getNow();
    render();
  };

  const render = () => {
    if (disposing) return;

    // calc elapsed time since last loop
    now = getNow();
    elapsed = now - then;

    // if enough time has elapsed, draw the next frame
    if (elapsed > fpsInterval) {
      then = now - (elapsed % fpsInterval);
      redraw();
    }

    while (fpsTimes.length > 0 && (fpsTimes?.[0] ?? 0) <= now - 1000) {
      fpsTimes.shift();
    }
    fpsTimes.push(now);
    fps = fpsTimes.length;

    // request another frame
    raf = requestAnimationFrame(render);
  };

  const redraw = () => {
    if (!lastCalledTime) {
      lastCalledTime = getNow();
      return;
    }
    let CANVAS_2D = proxyReceiverObject2D?.canvas;
    if (CANVAS_2D == null) return;

    if (REDRAW_ALL_CANVAS) CANVAS_2D_CTX.clearRect(0, 0, CANVAS_2D.width, CANVAS_2D.height);

    _now = getNow();

    switch (ANIMATION_TYPE) {
      case eAnimationType.BASIC:
        _time = 250;
        break;
      case eAnimationType.SQUARES:
        _time = 350;
        break;
      case eAnimationType.CIRCLES:
        _time = 400;
        break;
    }

    let timeBlipEnd = _now - _time;

    // Draw Piano Keys
    for (let [, key] of pianoKeys2D) {
      _key = key;
      _key.y = (0.5 + _key.y) | 0;

      if (!REDRAW_ALL_CANVAS) {
        if (_key.lastY === _key.y)
          CANVAS_2D_CTX.drawImage(_key.render, _key.x, _key.y);
        else
          CANVAS_2D_CTX.clearRect(_key.x, _key.y, _key.width,
            _key.height + (_key.type == "white" ? 10 : 0));
        _key.lastY = _key.y;
      } else {
        CANVAS_2D_CTX.drawImage(_key.render, _key.x, _key.y);
      }

      if (_key && _key.blips.length > 0) {
        for (let b = 0; b < _key.blips.length; b++) {
          let blip = _key.blips[b];
          if (!blip) continue;

          if (BLIP_ANIMATE_TIME_TYPE) {
            if (_key.keyDown) {
              blip.time += 35;
              blip.keyUp = false;
            } else {
              if (!blip.keyUp) {
                blip.time = timeBlipEnd + (_time * 0.75);
                blip.velY = (Math.random() * 5) + 1;
              }
              blip.keyUp = true;
            }
          }

          if (blip.time > timeBlipEnd) {

            //BG color
            CANVAS_2D_CTX.fillStyle = blip.color;
            CANVAS_2D_CTX.globalAlpha = blip.alpha - ((_now - blip.time) / _time);

            //Main BG
            if (KEY_ANIMATION_BACKGROUND)
              CANVAS_2D_CTX.fillRect(_key.x, _key.y, _key.width, _key.height);

            //Blip color
            CANVAS_2D_CTX.fillStyle = blip.color;

            switch (ANIMATION_TYPE) {
              case eAnimationType.CIRCLES:
                CANVAS_2D_CTX.beginPath();
                CANVAS_2D_CTX.arc((_key.x + _key.width / 2), blip.y, blip.h, 0, Math.PI * 2, true);
                CANVAS_2D_CTX.fill();
                CANVAS_2D_CTX.closePath();

                switch (BLIP_EFFECT) {
                  case 1:
                    if (BLIP_ANIMATE_TIME_TYPE !== 1) blip.h += 0.5;
                    break;
                }
                break;

              case eAnimationType.SQUARES:
                CANVAS_2D_CTX.fillRect(_key.x + 5, blip.y, _key.width - 10, blip.h);

                switch (BLIP_EFFECT) {
                  case 1:
                    blip.h += 0.5;
                    break;
                }

                break;
            }

          } else {
            _key.blips.splice(b, 1);
            --b;
          }

          switch (BLIP_ANIMATE_TIME_TYPE) {
            case 0:
              //Animation Type 1
              blip.y -= Math.floor(blip.h * 0.08 + ((_now - blip.time) / _time));
              break;
            case 1:
              blip.velY += BLIP_GRAVITY;
              blip.y -= blip.velY;

              let heightLimit = (_key.type == "white") ? (_key.height / 2) : (_key.height / 4);

              if (!blip.keyUp) {
                if (blip.y < heightLimit) {
                  //blip.velY = (Math.random() * - ((blip.h + heightLimit) * 0.20) ) - 1;
                  blip.velY = (Math.random() * -15) - 5;
                }

                if (blip.y + blip.h > (_key.height)) {
                  blip.velY = (Math.random() * -15) - 5;
                  blip.y = (_key.height - blip.h) + blip.velY;
                }
              }
              break;
          }
        }
      }
      CANVAS_2D_CTX.globalAlpha = 1;
    }
    ;

    drawKeyboardMappings();

    lastCalledTime = getNow();
  };

  const onShowFPS = (() => {
    self.clearInterval(showFPSInterval);
    showFPSInterval = self.setInterval(() => {
      canvasChannel?.postMessage({ event: "worker.fps", payload: fps || -1 });
    }, 5000);
  });

  const onLoadWeb = (_: any) => {
    if (wasmInstantiating || wasmInstantiated) return;
    wasmInstantiating = true;
    wasmInstantiated = true;
    wasmInstantiating = false;
  };

  const getKeyFromNoteId = (id: number): PianoKey2D | undefined => pianoKeys2D.get(id);

  const getKeyFromHit = (x: number, y: number) => {
    for (let j = 0; j < 2; j++) {
      let sharp = j ? "white" : "black";
      for (let [, value] of pianoKeys2D) {
        if (value.type != sharp) continue;

        if (value.contains(x, y)) {
          return { "key": value };
        }
      }
    }

    return null;
  };

  function getMousePos(canvas: { getBoundingClientRect: () => any; }, evt: { clientX: number; clientY: number; }) {
    var rect = canvas.getBoundingClientRect();
    return {
      x: evt.clientX - rect.left,
      y: evt.clientY - rect.top
    };
  }

  function emitNoteOnAudioMessage(data: PianoMsgParam) {
    let note = data.note;
    let velocity = data?.velocity || 0;
    audioService().parseMidiData(
      new Uint8Array([MIDI.NOTE_ON_BYTE + (data.channel || 0), note, velocity]),
      undefined, 2
    );
  }

  function emitNoteOffAudioMessage(data: PianoMsgParam) {
    let note = data.note;
    audioService().parseMidiData(
      new Uint8Array([MIDI.NOTE_OFF_BYTE + (data.channel || 0), note, 0]),
      undefined, 2
    );
  }

  const onPress = (note: INOTE, playAudio = false) => {
    if (!note) return;

    _keyPress = pianoKeys2D.get(note.note);
    if (!_keyPress) return;

    _keyPress.pressDown(note);

    if (playAudio) {
      emitNoteOnAudioMessage({ note: note.note, channel: 0, velocity: note.velocity });
    }
  };

  const onRelease = (note: INOTE, playAudio = false) => {
    if (!note) return;

    _keyRelease = pianoKeys2D.get(note.note);
    if (!_keyRelease) return;
    _keyRelease.pressUp(note);

    if (playAudio) {
      emitNoteOffAudioMessage({ note: note.note, channel: 0 });
    }
  };

  const onResize = (targetWidth: number = 0, _targetHeight: number = 0) => {
    let canvas = proxyReceiverObject2D?.canvas;
    if (!canvas) return;

    //TODO: Fix this since it's not reliable. On my laptop, it's 2.5.
    //const dpr = window.devicePixelRatio;
    let width = (targetWidth || window.innerWidth) * 1.0;
    // let height = (targetHeight || window.innerHeight) * dpr;

    canvas.width = width;
    canvas.height = Math.floor(width * 0.2);

    setDimensions(canvas);

    let white_spatial = 0;
    let black_spatial = 0;

    let black_it = 0;
    let black_lut = [2, 1, 2, 1, 1];

    let actualWidth = 0;
    let actualHeight = WHITE_KEY_HEIGHT;

    pianoKeys2D.forEach(key => {
      if (key.type === "white") {
        key.width = WHITE_KEY_WIDTH;
        key.height = WHITE_KEY_HEIGHT;
        key.spatial = white_spatial;
        ++white_spatial;
        key.x = key.spatial * key.width;
        key.y = 0;
        key.render = WhiteKeyRender();
        actualWidth += key.width;
      } else {
        key.width = BLACK_KEY_WIDTH;
        key.height = BLACK_KEY_HEIGHT;
        key.spatial = black_spatial;
        black_spatial += black_lut[black_it % 5] ?? 0;
        ++black_it;
        key.y = 0;
        key.x = BLACK_KEY_OFFSET + WHITE_KEY_WIDTH * key.spatial;
        key.render = BlackKeyRender();
      }
    });

    canvas.width = actualWidth;
    let parent = canvas.parentElement;
    if (parent) {
      parent.style.width = `${canvas.width}px`;
      parent.style.height = `${actualHeight + 5}px`;
    }
  };

  const addProxyElement = (canvas: HTMLCanvasElement, proxyObj: any) => {
    if (proxyReceiverObject2D?.canvas) return;

    proxyReceiverObject2D = { canvas, proxy: proxyObj };
    setCanvasLoaded(v => ({ ...v, twoD: true }));

    CANVAS_2D_CTX = canvas.getContext('2d')!;

    setDimensions(canvas);
    drawPiano();
    startAnimating(60);
    initialize();

    let last_key: PianoKey2D | null = null;

    canvas.onmousedown = ((event) => {
      event.preventDefault();

      let pos = getMousePos(canvas, event);
      let hit = getKeyFromHit(pos.x, pos.y);
      if (!hit) return;

      let note = hit.key.midiID;

      onPress({
        velocity: AUDIO.DEFAULT_VELOCITY,
        channel: 0,
        note: hit.key.midiID,
        color: getUserColor(note, client()?.socketID) ?? client()?.usercolor
      }, true);

      last_key = hit.key;
    });

    canvas.onmouseup = (function () {
      if (last_key) {
        onRelease({
          channel: 0,
          emit: true,
          note: last_key.midiID
        }, true);
      }
      last_key = null;
    });
  };

  const getUserSocketIDFromHash = (hash?: number) => {
    return audioService().hashedUsers().find(x => x.hash == hash)?.socketID;
  };

  const onHandleSynthEvent = (eventName: PianoRhythmSynthEventName, event: PianoRhythmSynthEvent) => {
    if (event.source == 2) return;

    let [_, note1, note2] = event.raw_bytes;
    let socketID = event.socketIDMappedOnClientSide;

    if (!socketID) {
      socketID = getUserSocketIDFromHash(event.socket_id);
    }

    switch (eventName) {
      case "NOTE_ON": {
        let note = note1;
        let velocity = note2;
        if (!note) return;

        let key = getKeyFromNoteId(note);
        let color = getUserColor(note || -1, socketID);
        key?.pressDown({ note, velocity, color });

        break;
      }
    }
  };

  const onSetClient = (_client: UserClientDto) => {
    setClient(new UserClientDomain(_client));
  };

  const onSetUsers = debounce((_users: ClientSideUserDto[]) => {
    // getUserColor?.cache?.clear?.();
    setUsers(_users);
  });

  const onCleanup = () => {
    self.clearInterval(showFPSInterval);
    synthEvents?.close();
    canvasChannel?.close();
    disposing = true;
    cancelAnimationFrame(raf);
    KEYS = Object();
    BLACK_KEYS = Object();
    WHITE_KEYS = Object();
    pianoKeys2D.clear();
    subscriptions().forEach(x => x.unsubscribe());
    setSubscriptions([]);
  };

  const drawKeyboardMappings = () => {
    let canvas = proxyReceiverObject2D?.canvas;
    if (!canvas) return;

    let ctx = canvas.getContext("2d");
    if (!ctx) return;

    let decoded = keyboardMappings();
    if (!decoded) return;

    let keyMappings = decoded.mappings;

    for (let i = 0; i < keyMappings.length; i++) {
      let keyMapping = keyMappings[i];
      if (!keyMapping) continue;

      let key = getKeyFromNoteId(keyMapping.note);
      if (!key) continue;

      // Draw text on canvas on top of key
      ctx.fillStyle = key.type == "white" ? "black" : "white";
      ctx.font = "bold 24px SEGOE UI";
      ctx.fillText(keyMapping.key, key.x + (key.type == "white" ? 5 : 2), key.y + key.height - 5);
    }
  };

  const showKeyboardVisualMapping = (data: Uint8Array) => {
    let decoded = AppKeyboardMappingVisualizeVec.decode(data);
    setKeyboardMappings(decoded);
  };

  const hideKeyboardVisualMapping = () => {
    setKeyboardMappings();
  };

  return {
    ...DefaultRenderingEngine,
    initialize,
    showKeyboardVisualMapping,
    hideKeyboardVisualMapping,
    onLoadWeb,
    onCleanup,
    setClient: onSetClient,
    getCanvasDimensions: () => ({
      width: proxyReceiverObject2D?.canvas?.width ?? 0,
      height: proxyReceiverObject2D?.canvas?.height ?? 0
    }),
    users: () => users(),
    setUsers: onSetUsers,
    addProxyElement,
    onResize
  } as unknown as IRenderingEngine;
};

/**
 * Converts a musical note to its corresponding MIDI number.
 * @param note - The musical note (e.g., "C4", "A#3").
 * @returns The MIDI number corresponding to the given note.
 */
function noteToMidi(note: string): number {
  const noteRegex = /^([A-Ga-g])([#b]?)(\d)$/;
  const match = note.match(noteRegex);

  if (!match) {
    throw new Error(`Invalid note format: ${note}`);
  }

  const [, noteName, accidental, octaveStr] = match;
  if (!octaveStr) return -1;
  if (!noteName) return -1;

  const octave = parseInt(octaveStr, 10);

  const baseNotes = {
    C: 0,
    D: 2,
    E: 4,
    F: 5,
    G: 7,
    A: 9,
    B: 11
  };

  let midiNumber = baseNotes[noteName.toUpperCase() as keyof typeof baseNotes] + (octave + 1) * 12;

  if (accidental === '#') {
    midiNumber += 1;
  } else if (accidental === 'b') {
    midiNumber -= 1;
  }

  return midiNumber;
}

export default PianoCanvasRenderingEngine;