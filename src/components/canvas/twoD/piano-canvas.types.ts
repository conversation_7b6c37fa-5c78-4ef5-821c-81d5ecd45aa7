import { memoize } from "lodash-es";
import tinycolor from "tinycolor2";
import { MidiNoteSource } from "~/types/midi.types";
import { getNow } from "~/util/helpers";

export type PianoMsgParam = {
  channel?: number;
  note: number;
  velocity?: number;
  targetColor?: string;
  socketID?: string;
  source?: MidiNoteSource;
};

export enum KEYBOARD_LAYOUT {
  ANY,
  VIRTUAL_PIANO,
  MPP,
  PIANORHYTHM
}

export enum NOTE_SOURCE {
  ANY,
  MIDI,
  KEYBOARD,
  MOUSE,
  TOUCH
}

export enum eAnimationType {
  BASIC,
  SQUARES,
  CIRCLES
}

export interface INOTE {
  note: number; //Ex "A1", "Bb2"
  velocity?: number;
  delay?: number;
  channel?: number;
  fromMouse?: boolean;
  userName?: string;
  color?: string;
  from?: string;
  source?: NOTE_SOURCE;
  kb_source?: KEYBOARD_LAYOUT;
  userID?: string;

  animate?: boolean;
  counter?: number;
  pianoKeyObject?: any;
  bufferNode?: any;
  value?: any;
  duration?: any;

  //Emit data to server?
  emit?: boolean;
}

abstract class PianoKey {
  public id: string = "";
  public spatial: number = 0;
  public type: string; //White or Black Key
  public key: number = 0;
  public midiID: number = 0;
  public render: any = Object();
  public color: string = "";
  private _x: number = 0;
  private _y: number = 0;
  private _width: number = 0;
  private _height: number = 0;
  public last = {
    x: -1,
    y: -1,
    width: -1,
    height: -1,
  };
  public keyDown: boolean = false;


  abstract pressDown(note: INOTE): void;
  abstract pressUp(note: INOTE): void;

  constructor(type: string) {
    this.type = type;
  }

  public contains(x: number, y: number) {
    let x2 = this.x + this.width, y2 = this.y + this.height;
    return (x >= this.x && x <= x2 && y >= this.y && y <= y2);
  };

  get x(): number {
    return this._x;
  }

  set x(value: number) {
    this._x = value;
  }

  get y(): number {
    return this._y;
  }

  set y(value: number) {
    this._y = value;
  }

  get width(): number {
    return this._width;
  }

  set width(value: number) {
    this._width = value;
  }

  get height(): number {
    return this._height;
  }

  set height(value: number) {
    this._height = value;
  }
}

type PianoBlip = {
  alpha: number,
  keyUp: boolean,
  time: number,
  color: string,
  y: number,
  h: number,
  duration: number | null,
  set: boolean,
  velY: number,
  velX: number;
};

const BLIP_ANIMATE_TIME_TYPE = 0;

const getBlipColor = (color: string) => {
  let tc = tinycolor(color || "#363942");
  return tc.darken().toHexString();
};

const blipColorMemo = memoize<(color: string) => string>(getBlipColor);

export const clearBlipColorMemo = () => {
  blipColorMemo.cache?.clear?.();
};

export class PianoKey2D extends PianoKey {
  animateDownVal: number = 3;
  ctx;
  blips: PianoBlip[] = [];
  instrumentSlot = null;
  lastX: number = 0;
  lastY: number = 0;
  particleStart = new Map();
  public _timer: number = -1;
  public meter = null;

  constructor(type: string, ctx?: { getContext: (arg0: string) => any; }) {
    super(type);

    if (ctx) {
      this.render = ctx;
      this.ctx = ctx.getContext("2d");
    }
  }

  public animateDown(vel = 1, color?: string, inote?: INOTE) {
    if (this.keyDown) return;
    this.keyDown = true;

    this.y += this.animateDownVal;
    if (this.y > this.animateDownVal) this.y = this.animateDownVal;

    // let max = 0, min = 0;
    // min = this.height * 0.05;
    // max = this.height * 0.15;

    let h = 20; //map(vel, 0, 1, min, max);

    this.blips.push({
      alpha: 1,
      keyUp: false,
      time: getNow(),
      color: getBlipColor(color || "#363942"),
      y: this.height - h,
      h: h,
      duration: (inote && inote.duration) ? inote.duration : null,
      set: false,
      velY: 10,
      velX: 0
    });

    this._timer = window.setTimeout(() => {
      //Reanimate to original position
      if (this.keyDown) this.animateUp();
    }, (BLIP_ANIMATE_TIME_TYPE == 0) ? 150 : 3000);
  }

  public animateUp() {
    if (this._timer) clearTimeout(this._timer);
    this.keyDown = false;
    this.y = 0;
  }

  pressDown(note: INOTE) {
    if (!note) return;
    if (!note.delay) note.delay = 0;
    this.animateDown(note.velocity, note.color, note);
  }

  pressUp(note: INOTE) {
    if (!note) return;
    this.animateUp();
  }
}