import AppService from "~/services/app.service";
import NotificationService from "~/services/notification.service";

export class RendererErrorHandler {
  private appService: ReturnType<typeof AppService>;
  private navigateCallback?: () => void;

  constructor(appService: ReturnType<typeof AppService>, navigateCallback?: () => void) {
    this.appService = appService;
    this.navigateCallback = navigateCallback;
  }

  public handleError = (message: string, disconnect = false): void => {
    NotificationService.show({
      title: "3D Renderer Error",
      description: message || "An error occurred while loading the renderer. \n\nPlease check the console logs for more details.",
      type: "danger",
      closable: true,
      duration: 20_000
    });

    if (disconnect) {
      this.appService.onDisconnect();
      this.navigateCallback?.();
    }

    console.error("Error loading PianoRhythm renderer", message);
  };

  public handleErrorEvent = (event: ErrorEvent): void => {
    if (
      event.message.includes("Uncaught RuntimeError: unreachable") ||
      event.filename.includes("pianorhythm_bevy_renderer_bg")
    ) {
      this.handleError("Runtime error in renderer module", true);
    }
  };
}