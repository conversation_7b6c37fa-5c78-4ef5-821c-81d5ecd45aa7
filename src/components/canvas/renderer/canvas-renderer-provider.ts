import { createEffect, createSignal, onCleanup } from "solid-js";
import { useService } from "solid-services";
import AppSettingsService from "~/services/settings-storage.service";
import { BevyRenderer, DefaultRenderingEngine, IRenderingEngine } from "~/types/renderer.types";
import { isWebGpuSupported } from "~/util/helpers.dom";

export const [rendererEngine3D, setRendererEngine3D] = createSignal<IRenderingEngine>();
export const [rendererEngine2D, setRendererEngine2D] = createSignal<IRenderingEngine>();

export function useCanvasRenderer() {
  const appSettingsService = useService(AppSettingsService);

  const [canvasWorker, setCanvasWorker] = createSignal<Worker>();
  const [renderer, setRenderer] = createSignal<BevyRenderer>();
  const [rendererPtr, setRendererPtr] = createSignal<bigint>();
  const [webGpuSupported, setWebGpuSupported] = createSignal(false);
  const [useWebGPU, setUseWebGPU] = createSignal(
    appSettingsService().getSetting<boolean>("GRAPHICS_ENABLE_WEBGPU", true)
  );
  const [runningRendererInMainThread, setRunningRendererInMainThread] = createSignal(false);
  const [canvasMounted, setCanvasMounted] = createSignal(false);

  // Initialize WebGPU support detection
  const initWebGPUSupport = async () => {
    const isSupported = await isWebGpuSupported();
    setWebGpuSupported(isSupported);
    if (!isSupported) setUseWebGPU(false);
  };

  // Set up renderer engine when renderer changes
  createEffect(() => {
    if (renderer()) {
      const engine: IRenderingEngine = {
        ...DefaultRenderingEngine,
      };
      setRendererEngine3D(engine);
    }
  });

  onCleanup(() => {
    if (rendererPtr() != null) renderer()?.release_app?.(rendererPtr()!);
    canvasWorker()?.terminate();
  });

  return {
    renderer,
    setRenderer,
    rendererPtr,
    setRendererPtr,
    canvasWorker,
    setCanvasWorker,
    webGpuSupported,
    useWebGPU,
    setUseWebGPU,
    runningRendererInMainThread,
    setRunningRendererInMainThread,
    canvasMounted,
    setCanvasMounted,
    initWebGPUSupport,
  };
}