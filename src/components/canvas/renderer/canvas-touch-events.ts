import { BevyRenderer } from "~/types/renderer.types";

export function setupTouchEvents(
  canvas: HTMLCanvasElement,
  bevyRenderer: Bevy<PERSON>enderer,
  appHandle: bigint
) {
  let touchState = {
    clientX: 0,
    clientY: 0,
    lastDeltaX: 0,
    lastDeltaY: 0
  };

  const onTouchStart = (e: TouchEvent) => {
    if (e.touches.length > 0) {
      touchState.clientX = e.touches[0]?.clientX ?? 0;
      touchState.clientY = e.touches[0]?.clientY ?? 0;
      bevyRenderer.mouse_bt_down(appHandle, 0, touchState.clientX, touchState.clientY);
    }
  };

  const onTouchMove = (e: TouchEvent) => {
    if (e.touches.length === 0) return;

    const touch = e.touches[0];
    const currentX = touch?.clientX ?? 0;
    const currentY = touch?.clientY ?? 0;
    const deltaX = currentX - touchState.clientX;
    const deltaY = currentY - touchState.clientY;

    // Update position only when there's meaningful movement
    if (deltaX !== touchState.lastDeltaX || deltaY !== touchState.lastDeltaY) {
      touchState.clientX = currentX;
      touchState.clientY = currentY;
      touchState.lastDeltaX = deltaX;
      touchState.lastDeltaY = deltaY;
    }

    bevyRenderer.mouse_move(appHandle, touchState.clientX, touchState.clientY, deltaX, deltaY);
  };

  const onTouchEnd = () => {
    bevyRenderer.mouse_bt_up(appHandle, 0);
  };

  canvas.addEventListener("touchstart", onTouchStart, { passive: false });
  canvas.addEventListener("touchmove", onTouchMove, { passive: false });
  canvas.addEventListener("touchend", onTouchEnd);
  canvas.addEventListener("touchcancel", onTouchEnd);

  return () => {
    canvas.removeEventListener("touchstart", onTouchStart);
    canvas.removeEventListener("touchmove", onTouchMove);
    canvas.removeEventListener("touchend", onTouchEnd);
    canvas.removeEventListener("touchcancel", onTouchEnd);
  };
}