import { Box } from "@hope-ui/solid";
import { createWindowSize } from "@solid-primitives/resize-observer";
import { cloneDeep, debounce } from "lodash-es";
import { Component, createEffect, createSignal, on, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import { AppKeyboardMappingVisualizeVec } from "~/proto/pianorhythm-app-renditions";
import { AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import css from '~/sass/piano-container.module.sass';
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import KeyboardService from "~/services/keyboard.service";
import { SidebarService } from "~/services/sidebar.service";
import UsersService from "~/services/users-service";
import { IRenderingEngine } from "~/types/renderer.types";
import { HTML_IDS } from "~/util/const.common";
import { logTrace } from "~/util/logger";
import PianoCanvasRenderingEngine from "./twoD/piano-canvas.renderer";
import { createActiveElement } from "@solid-primitives/active-element";
import AppThemesService from "~/services/app.themes.service";

type Canvas2DProps = {
  useOffscreenCanvas: boolean;
  onMount: (engine: IRenderingEngine, elementIDs: string[]) => Promise<void>;
  onCleanup: () => void;
};

const defaultZIndex = 0;
const zIndexOnFocus = 3;
const opacityOnBlur = 0.7;

const Canvas2D: Component<Canvas2DProps> = (props) => {
  const sidebarService = useService(SidebarService);
  const appService = useService(AppService);
  const appThemesService = useService(AppThemesService);
  const usersService = useService(UsersService);
  const displayService = useService(DisplaysService);
  const keyboardService = useService(KeyboardService);
  const audioService = useService(AudioService);

  const [appStateEffectListener, setAppStateEffectListener] = createSignal<VoidFunction>(() => { });
  const [zIndex, setZIndex] = createSignal(defaultZIndex);
  const [opacity, setOpacity] = createSignal(1);
  const [renderer, setRenderer] = createSignal<IRenderingEngine>();
  const [_canvas, setCanvas] = createSignal<HTMLCanvasElement>();
  const [pos, setPos] = createSignal({ x: 0, y: 0 });

  const activeEl = createActiveElement();
  const windowSize = createWindowSize();
  const backgroundID = "piano-canvas2D-background";
  const validIDs = [css.pianoCanvasContainer!, HTML_IDS.PIANO_CANVAS_2D_CONTAINER, backgroundID];

  let mounted = false;
  let onBlurTimeout = -1;
  const PADDING = 20;

  onMount(() => {
    setAppStateEffectListener(() => appService().appStateEffects.listen(effect => {
      switch (effect.action) {
        case AppStateEffects_Action.ClientUpdated: {
          if (!effect.userClientDto) return;
          renderer()?.setClient(effect.userClientDto);
          break;
        }
      }
    }));
  });

  const instrumentDockDisplayed = () => displayService().getDisplay("INSTRUMENT_DOCK");
  const bottomBarFullyShown = () => displayService().getDisplay("BOTTOM_BAR_FULLY_SHOWN");

  createEffect(on([
    instrumentDockDisplayed,
    bottomBarFullyShown,
    sidebarService().sideBarFullyShown,
    () => windowSize.width,
    () => windowSize.height
  ], () => {
    debouncedResize();
  }));

  // Set Users
  createEffect(() => {
    let users = usersService().users;
    renderer()?.setUsers(cloneDeep(users));
  });

  createEffect(() => {
    if (displayService().getDisplay("KEYBOARD_MAPPING_OVERLAY")) {
      let input = AppKeyboardMappingVisualizeVec.create({
        mappings: keyboardService().getAllMappings(
          audioService().octave(),
          audioService().transpose(),
        )
      });
      renderer()?.showKeyboardVisualMapping(AppKeyboardMappingVisualizeVec.encode(input).finish());
    } else {
      renderer()?.hideKeyboardVisualMapping();
    }
  });

  const calculateTargetY = (_canvasHeight = 0) => {
    let targetY = window.innerHeight - _canvasHeight;
    let root = document.documentElement;
    let bottomBarHeight = bottomBarFullyShown() ? parseFloat(root.style.getPropertyValue("--bottomBarHeight") ?? "0px") : 0;
    if (instrumentDockDisplayed()) targetY -= 65 + 5;
    if (bottomBarFullyShown()) targetY -= bottomBarHeight;
    return targetY;
  };

  let onResize = () => {
    let engine = renderer();
    if (!engine) return;

    let targetPosX = 0;

    let sidebar = sidebarService().containerElement();
    let sidebarWidth = 0;
    if (sidebar && document.body.contains(sidebar!)) {
      let rect = sidebar.getBoundingClientRect();
      sidebarWidth = rect.width;
    }
    targetPosX = sidebarWidth + PADDING;

    let width = window.innerWidth - targetPosX;
    engine.onResize?.(width);

    setPos(({ x: targetPosX, y: calculateTargetY(engine.getCanvasDimensions().height) }));
  };

  let debouncedResize = debounce(onResize, 50);

  let onFocus = () => {
    window.clearTimeout(onBlurTimeout);
    setOpacity(1);
    setZIndex(zIndexOnFocus);
  };

  let onBlurImmediate = () => {
    setOpacity(opacityOnBlur);
    setZIndex(defaultZIndex);
  };

  let onBlur = () => {
    window.clearTimeout(onBlurTimeout);
    onBlurTimeout = window.setTimeout(() => {
      onBlurImmediate();
    }, 3000);
  };

  let onMountCanvasParent = (container: HTMLDivElement) => {
    container.onmousedown = () => {
      container.focus();
      onFocus();
    };

    container.onmouseenter = onFocus;
    onBlur();
  };

  let onMountCanvas = (canvas: HTMLCanvasElement) => {
    if (mounted) return;
    mounted = true;

    setCanvas(canvas);
    let engine = PianoCanvasRenderingEngine(appService().desktopSynthEvents);
    engine.addProxyElement(canvas);
    engine.setClient(appService().client().getDto());
    setRenderer(engine);

    debouncedResize();
    window.addEventListener("resize", debouncedResize);
    props.onMount(engine, validIDs);

    logTrace("Canvas2D mounted");
  };

  onCleanup(() => {
    window.clearTimeout(onBlurTimeout);
    renderer()?.onCleanup();
    window.removeEventListener("resize", debouncedResize);
    props.onCleanup();
    appStateEffectListener()();
  });

  createEffect(() => {
    let activeElement = activeEl();

    if (activeElement) {
      if (validIDs.includes(activeElement.id)) {
        onFocus();
      }
      else {
        onBlurImmediate();
      }
    }
  });

  return (<>
    <Box
      id={HTML_IDS.PIANO_CANVAS_2D_CONTAINER}
      background={"-webkit-gradient(linear, left 0%, left 20%, from(rgba(0,0,0,0.01)), to(lightgrey))"}
      zIndex={zIndex()}
      opacity={opacity()}
      tabIndex={"-1"}
      position={"absolute"}
      transform={`translate3D(${pos().x}px, ${pos().y}px, 0)`}
      ref={onMountCanvasParent}
      transition={"opacity 0.7s ease, z-index 0.5s ease"}
      pointerEvents={"all"}
    >
      <Box
        as="canvas"
        draggable={false}
        ref={onMountCanvas}
        tabIndex={"-1"}
        class="unselectable undraggable"
        id="piano-canvas2D"
      />
    </Box>

    <Box id={backgroundID}
      w="100vw" h="100vh"
      background={`linear-gradient(to bottom left, rgba(0,0,0,0.1), ${appThemesService().themeColors.primary})`}
      zIndex={0}
    >
    </Box>
  </>);
};

export default Canvas2D;