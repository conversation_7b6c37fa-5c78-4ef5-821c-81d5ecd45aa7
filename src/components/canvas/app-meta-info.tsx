import { HStack, VStack, Box } from "@hope-ui/solid";
import { Switch, Match } from "solid-js";
import { useService } from "solid-services";
import { graphicsPresetsToJSON } from "~/proto/pianorhythm-app-renditions";
import AudioService from "~/services/audio.service";
import AppSettingsService from "~/services/settings-storage.service";
import { COMMON } from "~/util/const.common";

export default function AppMetaInfo() {
  const appSettingsService = useService(AppSettingsService);
  const audioService = useService(AudioService);

  const graphicsUsingWorkerThread = appSettingsService().getSetting<boolean>("GRAPHICS_USE_OFFSCREEN_CANVAS");

  return (<>
    <Box
      position={"absolute"}
      bottom={"calc(var(--bottomBarHeight) + 5px)"}
      right={5}
      fontSize={"0.35em"}
      color="rgba(255,255,255,0.3)"
      userSelect={"none"}
      display={{ "@initial": "none", "@sm": "flex" }}
    >
      <VStack spacing="$0_5" style={{ "align-items": "flex-end" }}>
        <HStack spacing={"$1"}>
          {appSettingsService().getSetting<boolean>("GRAPHICS_ENABLE_WEBGPU", false) && <Box color="gray">{"WebGPU"}</Box>}
          {!appSettingsService().getSetting<boolean>("GRAPHICS_ENABLE_WEBGPU", false) && <Box color="gray">{"WebGL2"}</Box>}
          | <Box color="gray">{graphicsUsingWorkerThread ? "Worker" : "Main"}</Box>
          | <Box color="gray">
            {
              graphicsPresetsToJSON(appSettingsService().getSetting<number>("GRAPHICS_PRESET", false))
                ?.toLowerCase().replaceAll("preset_", "Graphics: ") ?? "Unknown Preset"}
          </Box>
        </HStack>
        <HStack spacing={"$1"}>
          <Box color="gray">{appSettingsService().getSetting<string>("AUDIO_SYNTH_ENGINE", false)?.toLowerCase() ?? "Unknown Synth"}</Box>
          | <Box color="gray">{audioService().sampleRate() ?? appSettingsService().getSetting<number>("AUDIO_SAMPLE_RATE", false) ?? "Unknown Sample Rate"} kHz</Box>
        </HStack>

        <HStack spacing={"$1"}>
          <Box>Version: {COMMON.CLIENT_VERSION} (ALPHA)</Box>
          <Box>
            <Switch fallback={"(WEB)"}>
              <Match when={COMMON.IS_DESKTOP_APP}>{"(DESKTOP)"}</Match>
              <Match when={COMMON.IS_WEB_APP}>{"(WEB)"}</Match>
            </Switch>
          </Box>
          {COMMON.IS_STAGING && <Box color="yellow">{"(STAGING)"}</Box>}
          {COMMON.IS_DEV_MODE && <Box color="yellow">{"(DEV)"}</Box>}
          {COMMON.IS_TEST_MODE && <Box color="yellow">{"(TEST)"}</Box>}
        </HStack>
        <Box>Build Date: {COMMON.CLIENT_BUILD_DATE}</Box>
      </VStack>
    </Box>
  </>);
}