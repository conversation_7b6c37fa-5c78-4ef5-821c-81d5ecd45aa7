import { Box, Center, Spinner } from '@hope-ui/solid';
import { Component, lazy, Suspense } from 'solid-js';
import css from '~/sass/piano-container.module.sass';

const Canvas3D = lazy(() => import("~/components/canvas/piano.canvas3D"));

const CanvasLoader = () => {
  return <Center
    bg="$primaryDark1"
    h="100vh"
    color="$neutral12"
  >
    <Spinner
      thickness="4px"
      size="xl"
    />
  </Center>;
};

const PianoContainer: Component = () => {
  return (
    <Suspense fallback={<CanvasLoader />}>
      <Box draggable={false} id={css.pianoCanvasContainer} class="noselect" tabindex="0">
        <Canvas3D />
      </Box>
    </Suspense>
  );
};

export default PianoContainer;