import { AvatarBadge, Box, Center, Divider, HStack, Icon, Image, Skeleton, VStack } from "@hope-ui/solid";
import clsx from "clsx";
import { FaSolidBellSlash, FaSolidLock, FaSolidMicrophoneSlash, FaSolidMusic } from "solid-icons/fa";
import { Component, createEffect, createResource, createSignal, ErrorBoundary, lazy, Match, onMount, Suspense, Switch } from "solid-js";
import { useService } from "solid-services";
import { useUserRecordContext } from "~/contexts/user.context";
import { ClientSideUserDto } from "~/proto/user-renditions";
import style from "~/sass/sidebar.module.sass";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import ChatService from "~/services/chat.service";
import { UserDtoUtil } from "~/types/user-helper";
import { COMMON_MESSAGES } from "~/util/const.common";

// @ts-ignore
import { VirtualItemProps } from '@minht11/solid-virtual-container';
import { getCrownImage } from "~/server/general.api";

const UserProfileImage = lazy(() => import("./user-profile-image"));
const UserMiniProfileCard = lazy(() => import("./user-mini-profile-card"));

const ELEMENT_CORE_MARGIN_TOP = 5;
const SidebarUserItem: Component<VirtualItemProps<ClientSideUserDto>> = (props) => {
  const { user, parsedMetaDetails } = useUserRecordContext();

  const appService = useService(AppService);
  const chatService = useService(ChatService);
  const audioService = useService(AudioService);

  const [displayName, setDisplayName] = createSignal<string>();
  const [totalRoleIcons, setTotalRoleIcons] = createSignal(0);
  const [crownImage] = createResource(getCrownImage, { name: "crown" });

  createEffect(() => {
    setDisplayName(user.userDto?.nickname || user.userDto?.username);
  });

  const isClient = () => user.socketID == appService().client().socketID;

  const isProMember = () => user.userDto?.isProMember;

  const isBot = () => UserDtoUtil.isBot(user.userDto!.roles);

  const isDev = () => UserDtoUtil.isDeveloperExactly(user.userDto!.roles) || false;

  const isMod = () => UserDtoUtil.isMod(user.userDto!.roles);

  const isUserTyping = () => chatService().usersTyping().includes(user.socketID);

  const activeSoundfont = () => parsedMetaDetails()?.activeSoundfont;

  const doesNotHaveSameSoundfont = () => !isClient() && activeSoundfont() != null && activeSoundfont() != audioService().loadedSoundfontName();

  const getUserColor = () => (user.userDto?.color == "rainbow" ? "#FFFFFF" : user.userDto?.color) || "#FFFFFF";

  return (
    <div
      style={{ ...props.style, padding: "3px" }}
      id={UserDtoUtil.getUserSideBarElementID(user)}
      data-testid={"sidebar-user-item"}
      tabIndex={props.tabIndex}
      class={style.listItem}
      role="listitem"
    >
      <ErrorBoundary fallback={
        <Box
          __tooltip_title={"Failed to load user element."}
          __tooltip_placement="right"
        >{user?.userDto?.usertag}
        </Box>
      }>
        <Suspense fallback={<Skeleton borderRadius={5} h={"100%"}></Skeleton>}>
          <Box
            __tooltip_title={<Suspense><UserMiniProfileCard /></Suspense>}
            __tooltip_placement="right"
            className={style.userElement}
            position={"relative"}
            transition={"background 0.15s ease-out"}
            borderRadius={5}
            h="100%"
          >
            {/* Room Owner Crown */}
            {((user.socketID == appService().roomOwner())) &&
              <Box
                position={"absolute"}
                top={-20}
                left={8}
                zIndex={2}
                width={50}
                transform={"rotate(30deg) scale(0.8)"}
                id="room-owner-crown"
              >
                <Image
                  loading="lazy"
                  src={crownImage()}
                  alt={`${user?.userDto?.username}'s crown`}
                  fallback={<Skeleton height={"44.2px"}></Skeleton>}
                />
              </Box>
            }

            {/* Core Content */}
            <HStack spacing={"$2"} marginTop={ELEMENT_CORE_MARGIN_TOP}>
              <Box position={"relative"}>
                {/* Profile Image */}
                <Suspense fallback={<Skeleton borderRadius={5} h={45}></Skeleton>}>
                  <UserProfileImage
                    width={35}
                    height={35}
                    borderWidth={"2px"}
                  />
                </Suspense>

                {/* User Status Badge */}
                <AvatarBadge
                  transition={"background 0.1s ease"}
                  boxSize="0.8em"
                  zIndex={1}
                  bottom={totalRoleIcons() >= 2 ? 7 : 0}
                  bg={UserDtoUtil.getUserStatusColor(user.userDto?.status)}
                />
              </Box>

              {/* User display name */}
              <VStack w="100%" alignItems={"flex-start"} flexWrap="nowrap">
                <Box
                  __tooltip_title={displayName()}
                  w="70%"
                  overflow={"hidden"}
                  style={{ "white-space": "nowrap", "text-overflow": "ellipsis" }}
                  h="100%"
                  fontSize={"0.8em"}
                  textAlign={"start"}>{displayName()}</Box>
                <Divider w="100%" />

                {/* User status text */}
                <Box
                  minH={15}
                  fontSize={"0.5em"}
                  overflow={"hidden"}
                  textAlign={"start"}
                  style={{ "white-space": "nowrap", "text-overflow": "ellipsis" }}
                  className={style.userStatusText}>{user.userDto?.statusText}</Box>
              </VStack>

              {/* Role Icons */}
              <HStack
                className={clsx([style.rolesIconsContainer, "unselectable"])}
                spacing={"$0"}
                marginBottom={5}
              >
                <Switch>
                  <Match when={isMod()}>
                    <Box ref={() => setTotalRoleIcons(v => v + 1)} background="$primaryDark1" className={style.roleIcon}>MOD</Box>
                  </Match>
                  <Match when={isDev()}>
                    <Box ref={() => setTotalRoleIcons(v => v + 1)} background="$primaryDark1" className={style.roleIcon}>DEV</Box>
                  </Match>
                  <Match when={isBot()}>
                    <Box ref={() => setTotalRoleIcons(v => v + 1)} background="#7289da" className={style.roleIcon}>
                      <Center>
                        <HStack>
                          <Box as="span">BOT</Box>
                          {isUserTyping() && <AnimatedEllipsis fontSize={10} />}
                        </HStack>
                      </Center>
                    </Box>
                  </Match>
                </Switch>

                {/* Pro member icon */}
                {isProMember() && <>
                  <Box zIndex={2} ref={() => setTotalRoleIcons(v => v + 1)} className={clsx([style.roleIcon, style.roleIconRainbow, "rainbow-text"])}>PRO</Box>
                  <Box zIndex={1} borderRadius={3} background={"$primaryDark1"} w="100%" h="100%" position={"absolute"}></Box>
                </>}
              </HStack>

              {/* Other icons */}
              <HStack
                zIndex={1}
                className={style.elementIconsContainer}
                spacing="$0_5"
                marginTop={-ELEMENT_CORE_MARGIN_TOP - 5}
              >
                {/* Mute Icons */}
                {(user.localNotesMuted || user.localChatMuted) &&
                  <Icon
                    __tooltip_title={"Locally muted by you."}
                    __tooltip_placement="right"
                    className={style.elementIcon} as={FaSolidLock} />
                }

                {(user.userDto?.serverNotesMuted || user.userDto?.serverChatMuted) &&
                  <Icon
                    __tooltip_title={"Server muted."}
                    __tooltip_placement="right"
                    className={style.elementIcon} as={FaSolidLock} color="red" />
                }

                {user.userDto?.selfMuted &&
                  <Icon
                    __tooltip_title={"User has muted themselves."}
                    __tooltip_placement="right"
                    className={style.elementIcon} as={FaSolidMicrophoneSlash} color="yellow" />
                }

                {parsedMetaDetails()?.currentMidiFilePlaying?.includes(".mid") &&
                  <Box
                    __tooltip_title={"User is currently playing a midi file with the built-in player."}
                    __tooltip_placement="right"
                  >
                    <FaSolidMusic font-size="9px" class={style.playingMidiFile} />
                  </Box>
                }

                {/* Different Soundfont */}
                {doesNotHaveSameSoundfont() &&
                  <Icon
                    __tooltip_title={
                      <Box
                        innerHTML={COMMON_MESSAGES.DIFFERENT_SOUNDFONT_MESSAGE(
                          audioService().loadedSoundfontName(),
                          activeSoundfont()
                        )}
                      />
                    }
                    className={
                      clsx([style.elementIcon, "pr-has-different-soundfont-icon", "unselectable"])
                    }
                    as={FaSolidBellSlash} color="rgba(255, 252, 127, 1) !important" />
                }

                {/* ME Icon */}
                {appService().client().socketID == user.socketID &&
                  <Box
                    __tooltip_title={"This is you!"}
                    __tooltip_placement="top"
                    fontSize={8}
                    className={clsx([style.elementIcon, style.userElementMEIcon, "unselectable"])}
                  >ME</Box>
                }
              </HStack>
            </HStack>
          </Box>
        </Suspense>
      </ErrorBoundary>
    </div>
  );
};

type AnimatedEllipsisProps = {
  fontSize: number | string;
};

const AnimatedEllipsis: Component<AnimatedEllipsisProps> = (props) => {
  return (<>
    <Box fontSize={props.fontSize} class="ellipsis-anim" as="span">
      <span>.</span><span>.</span><span>.</span>
    </Box>
  </>);
};

export default SidebarUserItem;