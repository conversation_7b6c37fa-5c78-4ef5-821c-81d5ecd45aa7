import {
  Box,
  Button,
  createDisclosure,
  Icon,
  Input,
  Modal,
  <PERSON>dal<PERSON>ody,
  ModalClose<PERSON>utton,
  <PERSON>dal<PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalOverlay,
} from '@hope-ui/solid';
import { cloneDeep } from 'lodash-es';
import { FaSolidFolderPlus } from 'solid-icons/fa';
import { createEffect, createSignal, onCleanup, onMount } from 'solid-js';
import { createStore } from 'solid-js/store';
import { useService } from 'solid-services';
import { z } from 'zod';
import DisplaysService from '~/services/displays.service';
import RoomsService from '~/services/rooms.service';
import SoundEffectsService from '~/services/sound-effects.service';
import WebsocketService from '~/services/websocket.service';
import { ROOM_CONST } from '~/types/room.types';
import { logError } from '~/util/logger';
import SwalPR from '~/util/sweetalert';
import style from "~/sass/enter-password.module.sass";

const inputSettings = z.object({
  password: z.string().max(ROOM_CONST.MAX_PASSWORD_LENGTH)
});

type InputSettings = { password?: string; };

const EnterPasswordModal = () => {
  const displayService = useService(DisplaysService);
  const websocketService = useService(WebsocketService);
  const sfxService = useService(SoundEffectsService);
  const roomsService = useService(RoomsService);

  const { isOpen, onOpen, onClose } = createDisclosure();
  const [loading, setLoading] = createSignal(false);
  const [fields, setFields] = createStore<InputSettings>({ password: undefined });
  let loadingTimeout = -1;

  const isFormValid = () => inputSettings.safeParse(fields).success;

  function closeModal(passwordEnteredCorrectly = false) {
    let closeForReal = (joinNextAvailableLobby = false) => {
      if (!isOpen()) return;
      // sfxService?.().playSFX(AppSoundFx.POPUP_CLOSE);
      displayService().setDisplay("ENTER_ROOM_PASSWORD_MODAL", false);
      roomsService().setRoomIDofRoomWaitingForPassword(null);
      onClose();
      if (joinNextAvailableLobby) websocketService().joinNextAvailableLobby();
    };

    if (roomsService().roomIDofRoomWaitingForPassword()?.newSession && !passwordEnteredCorrectly) {
      console.log("closeModal: SwalPR");

      SwalPR().fire({
        html: "Are you sure want to cancel trying to enter this room's password? " +
          "If so, you'll be directed to the next available lobby.",
        showCancelButton: true,
        icon: "warning",
        cancelButtonText: "Nah, I'll try again.",
        confirmButtonText: "Yes!",
        showConfirmButton: true,
        allowEscapeKey: true,

      }).then((res) => {
        if (res.isConfirmed) closeForReal(true);
      });
    } else {
      closeForReal();
    }
  }

  createEffect(() => {
    let input = roomsService().roomIDofRoomWaitingForPassword();
    if (!input && isOpen()) return closeModal();

    if (!isOpen() && input?.roomID) {
      displayService().setDisplay("ENTER_ROOM_PASSWORD_MODAL", true);
      onOpen();
    }
  });

  function onSubmit() {
    if (loading()) return;
    setLoading(true);

    try {
      let result = inputSettings.safeParse(fields);
      if (result.error) throw new Error("Invalid input.");

      let input = cloneDeep<InputSettings>(result.data as any);
      let waiting = roomsService().roomIDofRoomWaitingForPassword();

      if (waiting && waiting.roomID && input.password) {
        let response = websocketService().joinRoomByID(waiting.roomID, input.password);
        if (response == "Failed") throw new Error("Failed to join room.");

        window.clearTimeout(loadingTimeout);
        loadingTimeout = window.setTimeout(() => {
          setLoading(false);
        }, 1000);
      } else {
        throw new Error("Invalid input.");
      }

    } catch (e) {
      setLoading(false);
      logError(`[EnterPasswordModal.onSubmit] ${e}`);
    }
  }

  onCleanup(() => {
    window.clearTimeout(loadingTimeout);
  });

  const onModalRef = (el: HTMLElement) => {
    setTimeout(() => {
      let nextSibling = el.parentElement?.querySelector(".hope-modal__content-container");
      if (!nextSibling) return;
      (nextSibling as HTMLElement).style.setProperty("z-index", "calc(var(--hope-zIndices-modal) + 500)", "important");
    });
  };

  return (
    <>
      <Modal
        centered
        size={"md"}
        opened={isOpen()}
        onClose={closeModal}
        scrollBehavior={"inside"}
        closeOnOverlayClick={!roomsService().roomIDofRoomWaitingForPassword()?.newSession}
      >
        <ModalOverlay className={style.enter_password_overlay} ref={onModalRef} />
        <ModalContent >
          <ModalCloseButton />
          <ModalHeader><Icon as={FaSolidFolderPlus} marginRight={5} marginBottom={5} />Enter password for: {roomsService().roomIDofRoomWaitingForPassword()?.roomName}</ModalHeader>
          <ModalBody>
            <Input
              disabled={loading()}
              variant="outline"
              placeholder="Enter room password..."
              type="password"
              autocomplete="false"
              maxlength={ROOM_CONST.MAX_PASSWORD_LENGTH}
              value={fields.password}
              /* @ts-ignore */
              onInput={(e) => setFields("password", e.target.value)}
            />
          </ModalBody>
          <ModalFooter>
            <Button
              loading={loading()}
              loadingText="Please wait..."
              onMouseDown={onSubmit}
              size="sm" variant="outline"
              disabled={!isFormValid() || loading()}>Submit</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>);
};

export default EnterPasswordModal;