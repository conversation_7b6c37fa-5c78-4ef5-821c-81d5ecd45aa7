import { V<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Spinner, <PERSON><PERSON>, Tr, Th, Tbody, <PERSON><PERSON><PERSON>, Td, <PERSON><PERSON>ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@hope-ui/solid";
import { sortBy, keys, uniq, toPairs, isString, isArray } from "lodash-es";
import { Accessor, Component, createEffect, createResource, createSignal, For, JSX, JSXElement, Setter, Show } from "solid-js";
import { PaginatedDataResponse } from "~/types/api.types";
import { Pagination } from "./common";
import { DateTime } from "luxon";
import MotionFadeIn from "./motion/motion.fade-in";

type SimpleDataWidgetProps<T> = {
  label: string;
  fetcher(): Promise<T>;
  autoRefresh?: boolean;
  autoRefreshInterval?: number;
  icon?: JSXElement;
  iconColor?: string;
};

type FetchInputProps = {
  searchInput: Accessor<string | undefined>;
  sort: Accessor<string | undefined>;
  limit: Accessor<number>;
  skip: Accessor<number>;
  currentPage: Accessor<number>;
  setTotalCount: Setter<number>;
  setTotalPages: Setter<number>;
};

type SimpleTableWidgetProps<T> = {
  label: string;
  initialLimit?: number;
  fetcher(props: FetchInputProps): Promise<PaginatedDataResponse<T>>;
  icon?: JSXElement;
  iconColor?: string;
  includedColumnHeaders?: string[];
  excludedColumnHeaders?: string[];
  minHeight?: number | string;
  height?: number | string;
  dense?: boolean;
  enablePagination?: boolean;
  searchBarEnabled?: boolean;
  allowingSorting?: boolean;
  defaultSort?: string;
  customColumnsToAdd?: string[];
  mapHeaderNames?(header: string): string | undefined;
  mapCellValue?(header: string, element: T, value: any): JSXElement | null | undefined;
  mapRow?(element: T): JSXElement;
  onRowClick?(element: T): void;
  rowToolTip?(element: T): string;
  actions?: (element: T, actions: { refresh: () => void, highlight: (v: boolean) => void; }) => JSXElement;
};

type TableWidgetColumnName = {
  original: string,
  mapped?: string;
  alignment?: string;
};

type SimpleDataWidgetIconButtonProps = {
  icon: JSX.Element;
  label: string;
  disabled?: boolean;
  onClick: () => void;
  tooltipPlacement?: string;
};

export const SimpleTableWidget = <T,>(props: SimpleTableWidgetProps<T>) => {
  const getSkip = (cp: number, lim: number) => lim * (cp - 1);
  const [totalCount, setTotalCount] = createSignal(0);
  const [totalPages, setTotalPages] = createSignal(0);
  const [noData, setNoData] = createSignal(false);
  const [searchInput, setSearchInput] = createSignal<string>();
  const [sort, setSort] = createSignal<string | undefined>(props.defaultSort);
  const [limit, setLimit] = createSignal(props.initialLimit || 10);
  const [currentPage, setCurrentPage] = createSignal(1);
  const [skip, setSkip] = createSignal(0);
  const [columnNames, setColumnNames] = createSignal<TableWidgetColumnName[]>([]);
  const [rowValues, setRowValues] = createSignal<{ element: T, values: any[]; }[]>();
  const [data, { refetch }] = createResource({ sort, skip, limit, currentPage, searchInput, setTotalCount, setTotalPages }, props.fetcher);

  const headerFilter = (header: string) => {
    let _header = header.toLowerCase();
    if (props.includedColumnHeaders && props.includedColumnHeaders.length > 0) {
      return props.includedColumnHeaders.map(x => x.toLowerCase()).includes(_header);
    } else if (props.excludedColumnHeaders) {
      return !props.excludedColumnHeaders.map(x => x.toLowerCase()).includes(_header);
    }
    return true;
  };

  const handlePage = (newPage: number) => {
    if (newPage < 1) return;
    setCurrentPage(newPage);
    setSkip(getSkip(newPage, limit()));
    refetch();
  };

  createEffect(() => {
    setNoData(!data.loading && (!data()?.data?.flat().length));
  });

  createEffect(() => {
    if (noData()) return;

    let _headers = sortBy((data()?.data?.flat() || []), x => keys(x).length).reverse().slice(0, 1);
    if (_headers.length == 0) return;

    let headerKeys = keys(_headers[0]);
    if (props.customColumnsToAdd) headerKeys = uniq([...headerKeys, ...props.customColumnsToAdd]);

    let columns = headerKeys.filter(headerFilter).map(x => {
      let original = x.toLowerCase();
      let mapped = props.mapHeaderNames ? props.mapHeaderNames(original) : original;
      return { original, mapped: mapped || original };
    });

    setColumnNames(columns);
  });

  createEffect(() => {
    if (noData()) return;

    let cNames = columnNames();
    let input = (data()?.data?.flat() as T[] | undefined || [])?.filter(Boolean);
    if (props.customColumnsToAdd) {
      props.customColumnsToAdd.forEach((c) => {
        input = input.map(x => {
          let o = { ...x }; (o as any)[c.toLowerCase()] = null;
          return o;
        });
      });
    }

    let values = input.map(element => {
      let pairs = toPairs(element as any);
      let rows =
        cNames
          .map(x => {
            return pairs.find(y => y[0].toLowerCase() == x.original.toLowerCase());
          })
          .map(x => {
            if (!x) return undefined;

            let value = x[1];
            let isValueString = isString(value);
            let isValueArray = isArray(value);
            let isDate = isValueString && DateTime.fromISO(value as any).isValid;
            if (isDate) value = DateTime.fromISO(value as any).toLocaleString();
            if (isValueArray) value = (value as any[]).join(",");

            if (props.mapCellValue) {
              let mappedCellValue = props.mapCellValue(x[0], element, value);
              if (mappedCellValue) value = mappedCellValue;
            }

            return value as any;
          });
      return { element, values: rows };
    });
    setRowValues(values || []);
  });

  return (<>
    <MotionFadeIn duration={0.5}>
      <VStack spacing={"$1"} alignItems="stretch" marginBottom={10}>
        <Box fontSize={14} textTransform="uppercase" fontWeight="bold" color="$tertiary1">{props.label}</Box>
        <Box
          padding={5}
          border={"2px solid rgba(255,255,255,0.05)"}
          boxShadow={"0 25px 20px -20px rgba(0,0,0,.1), 0 0 15px rgba(0,0,0,.1)"}
          borderRadius={5}
          w="100%"
          minH={props.minHeight}
          h={props.height || "100%"}
          overflow={"auto"}
        >
          <Table
            dense={props.dense} highlightOnHover position={"relative"}
            pointerEvents={data.loading ? "none" : "all"}
            className={data.loading ? "disabled" : "noselect"}
          >
            <Show when={data.loading}>
              <Center w="100%" h="90%" position={"absolute"} minH={props.minHeight}>
                <Spinner />
              </Center>
            </Show>
            <Show when={noData()}>
              <Center w="100%" h={props.minHeight}>
                <Box>No Data...</Box>
              </Center>
            </Show>
            <Thead>
              <Tr>
                <For each={[
                  (props.actions && !data.loading && !noData()) && { alignment: "center", original: "Actions", mapped: "" },
                  ...columnNames()
                ].filter(Boolean)}>
                  {(column) => <Th
                    // @ts-ignore
                    textAlign={column.alignment || "left"}
                    fontWeight={"bold"}>{column?.mapped ?? column?.original}
                  </Th>}
                </For>
              </Tr>
            </Thead>
            <Tbody>
              <For each={rowValues() ?? []}>
                {(rowData) => {
                  const [highlighted, setHighlighted] = createSignal(false);

                  return (
                    <Tr
                      __tooltip_title={props.rowToolTip ? props.rowToolTip(rowData.element) : undefined}
                      __tooltip_placement="top"
                      cursor={props.onRowClick ? "pointer" : "auto"}
                      onMouseDown={() => props.onRowClick?.(rowData.element)}
                      background={highlighted() ? "$accent1" : undefined}
                    >
                      {props.actions && <Td>{props.actions(rowData.element,
                        {
                          refresh: () => refetch(),
                          highlight: (value) => setHighlighted(value)
                        })
                      }</Td>}

                      <For each={rowData.values}>
                        {(rows) =>
                          <Td color="rgba(255,255,255,0.8)" >
                            {rows}
                          </Td>
                        }

                      </For>

                    </Tr>
                  );
                }}
              </For>
            </Tbody>
          </Table>
        </Box>

        {(props.enablePagination && totalPages() > 0 && !noData()) &&
          <Center>
            <Pagination
              minWidth={"50%"}
              currentPage={currentPage}
              totalPages={totalPages}
              handlePage={handlePage}
            />
          </Center>
        }
      </VStack>
    </MotionFadeIn>
  </>);
};

export const SimpleDataWidget = <T,>(props: SimpleDataWidgetProps<T>) => {
  const [data] = createResource(props.fetcher);

  return (<>
    <MotionFadeIn duration={0.5}>
      <Box
        padding={5}
        border={"2px solid rgba(255,255,255,0.05)"}
        boxShadow={"0 25px 20px -20px rgba(0,0,0,.1), 0 0 15px rgba(0,0,0,.1)"}
        borderRadius={5}
        overflow="hidden"
      >
        <HStack justifyContent={"space-evenly"} h="100%">
          {props.icon && <Box color={props.iconColor} fontSize={42} >{props.icon}</Box>}
          <VStack alignItems="flex-end">
            {/* Label */}
            <Box
              maxW={100}
              h={25}
              overflow="hidden"
              textAlign="right"
              fontSize={14} color="$tertiary1" fontWeight={"bold"}
            >{props.label}</Box>

            {/* Data */}
            <Show when={!data.loading}
              fallback={<Box fontSize={20}>
                <Spinner size="md" color="$primary5" />
              </Box>}
            >
              <Box fontSize={20} fontWeight={"bold"}>{data() as any}</Box>
            </Show>
          </VStack>
        </HStack>
      </Box>
    </MotionFadeIn>
  </>);
};

export const SimpleDataWidgetIconButton: Component<SimpleDataWidgetIconButtonProps> = (props) => {
  return (<>
    <Center>
      <IconButton
        __tooltip_title={props.label}
        __tooltip_placement={props.tooltipPlacement ?? "top"}
        disabled={props.disabled}
        size={"sm"}
        onMouseDown={(evt: MouseEvent) => { if (evt.button == 0) props.onClick(); }}
        aria-label={props.label}
        icon={props.icon}
      />
    </Center>
  </>);
};