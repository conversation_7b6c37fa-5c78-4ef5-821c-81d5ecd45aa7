import { Box, Center } from "@hope-ui/solid";
import { ParentComponent, Suspense } from "solid-js";
import { Motion } from "solid-motionone";
import MainBackgroundImage from "~/components/home.background";
import HomeMeta from "~/components/home.meta";
import style from "~/sass/login.page.module.sass";

type HomeBaseParentProps = {
  hideMeta?: boolean;
};

const HomeBaseParent: ParentComponent<HomeBaseParentProps> = (props) => {
  return (
    <>
      <Box w="100vw" h="100vh">
        <MainBackgroundImage />
        {!props.hideMeta && <HomeMeta />}
        <Motion
          animate={{ opacity: [0, 1] }}
          transition={{ duration: 1.5, easing: "ease-in-out" }}
        >
          <Center class={style.mainForm} mt={"25px"}>
            <Suspense fallback={<Box>Loading...</Box>}>
              {props.children}
            </Suspense >
          </Center>
        </Motion>
      </Box>
    </>

  );
};

export default HomeBaseParent;