import { Center, Image, Skeleton, Spinner } from "@hope-ui/solid";
import clsx from "clsx";
import { Component, Suspense, createEffect, createResource, createSignal, splitProps } from "solid-js";
import { useService } from "solid-services";
import { useApiUserRecordContext } from "~/contexts/user.context";
import style from '~/sass/sidebar.module.sass';
import ResourceService from "~/services/resource.service";
import { RolesHelper, UserDtoUtil } from "~/types/user-helper";
import MotionFadeIn from "./motion/motion.fade-in";

const UserProfileImageFallBack: Component<{ color?: string; }> = (props) => {
  const resourceService = useService(ResourceService);
  const [imageSource] = createResource(resourceService().getDefaultProfileImage);

  return (<>
    <Suspense fallback={<Spinner />}>
      <Image
        border={`solid 2px ${props.color ?? "$accent1"}`}
        background="$primary1"
        src={imageSource()}
      />
    </Suspense>
  </>);
};

type ProfileImageProps = {
  transform?: string,
  height?: number | string,
  width?: number | string,
  minHeight?: number | string,
  minWidth?: number | string,
  className?: string,
  ignoreDefaultClasses?: boolean;
  onClick?: () => void;
  onRef?: (element: HTMLDivElement) => void;
  disableBorder?: boolean;
  imageSource?: string;
  customBorder?: string;
  borderWidth?: string;
  forceUseDefault?: boolean;
  borderRadius?: number | string;
  tooltip?: string;
  tooltipPlacement?: string;
};

const UserProfileImage: Component<ProfileImageProps> = (props) => {
  const { user } = useApiUserRecordContext();
  const [local] = splitProps(user, ["profileImageLastModified", "roles", "usertag"]);
  const [userColor, setUserColor] = createSignal<string>();
  const getUserColor = () => (user.color == "rainbow" ? "#FFFFFF" : user.color) ?? "#FFFFFF";
  const isRainbowColor = () => user.color?.toLowerCase() == "rainbow";
  const resourceService = useService(ResourceService);

  const userProfileData = () => {
    return {
      profileImageLastModified: local.profileImageLastModified,
      roles: RolesHelper.mapRolesFromJSON(local.roles ?? []),
      usertag: local.usertag
    };
  };

  const getProfileImage = async (
    userData: ReturnType<typeof userProfileData>,
    defaultImageSource?: string
  ) => {
    let defaultImage = defaultImageSource ?? await resourceService().getDefaultProfileImage();
    if (!user) return defaultImage;

    if (UserDtoUtil.isBot(userData.roles)) {
      return await resourceService().getDefaultHelpBotProfileImage();
    }

    if (UserDtoUtil.cannotGetProfileImage(userData.roles) || UserDtoUtil.isGuest(userData.roles)) {
      return defaultImage;
    }

    if (userData.usertag) {
      try {
        const image = await resourceService().getServerProfileImage(
          userData.usertag,
          userData.profileImageLastModified
        );
        return image?.replace("application/octet-stream", "image/png") ?? defaultImage;
      } catch (error) {
        console.error("Failed to fetch profile image:", error);
        return defaultImage;
      }
    }

    return defaultImage;
  };

  const [imageSource] = createResource(
    userProfileData(),
    (currentUser) => getProfileImage(currentUser, props.imageSource)
  );

  createEffect(() => { setUserColor(getUserColor()); });

  return (<>
    <Center
      className={
        props.ignoreDefaultClasses ? props.className :
          clsx([
            style.userProfileImage
            , props.className
            , isRainbowColor() && "rainbow-border"
          ])
      }
      transform={props.transform}
      w={props.width}
      h={props.height}
      minW={props.minWidth}
      minH={props.minHeight}
      borderRadius={props.borderRadius}
      ref={(elem: HTMLDivElement) => props.onRef?.(elem)}
      onMouseDown={() => { if (props.onClick) props.onClick(); }}
    >
      <Suspense fallback={
        <MotionFadeIn duration={0.25}>
          <UserProfileImageFallBack color={userColor()} />
        </MotionFadeIn>
      }>
        <MotionFadeIn duration={0.25}>
          <Image
            __tooltip_title={props.tooltip}
            __tooltip_placement={props.tooltipPlacement}
            loading="lazy"
            border={isRainbowColor() || props.disableBorder ? '' : props.customBorder ?? `solid ${props.borderWidth ?? "3px"} ${userColor() || "$accent1"}`}
            src={imageSource()}
            zIndex={2}
            borderRadius={props.borderRadius}
            alt={`${user.username}'s profile image`}
            fallback={<Skeleton height={"44.2px"}></Skeleton>}
          />
        </MotionFadeIn>
      </Suspense>
    </Center>
  </>);
};

export default UserProfileImage;