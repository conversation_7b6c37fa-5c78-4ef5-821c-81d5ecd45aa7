import { useService } from "solid-services";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import PlatformService from "~/services/platform.service";
import { useMemory } from 'solidjs-use';
import { Box, Divider, HStack, VStack } from "@hope-ui/solid";
import convertSize from "convert-size";
import { Match, Switch } from "solid-js";
import BasicWindow from "./basic-window";
import ToolTipHelp from "./tooltip-help";

export default function DebugStats() {
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);
  const platformService = useService(PlatformService);

  return (<>
    <Switch>
      <Match when={!platformService().isMobile()}>
        <BasicWindow
          title="Debug Stats"
          width={400}
          height={245}
          minWidth={200}
          minHeight={220}
          onClose={() => displayService().setDisplay("DEBUG_STATS", false)}
          initPosition={{ x: "45%" }}
        >
          <VStack spacing={"$1"} padding={10}
                  alignItems={"flex-start"} w="100%" h="100%"
          >
            {/* <Box>Connection State:&nbsp;
              <Box
                as="span"
                color={getColorForConnectionState()}
              >
                {appService().connectionState()}
              </Box>
            </Box> */}
            {/* <Box>Server Time Offset: {webSocketService().serverTimeOffset()}ms</Box> */}
            {/* <Box>Client Is Self Muted: {`${audioService().clientNotesMuted() ?? false}`}</Box> */}
            <HStack spacing={"$1"}>
              <Box>Emitting Notes to Server</Box>
              <ToolTipHelp
                tooltipLabel={
                  <Box w={200}>
                    When enabled, the client will send note events to the server.
                    <br/><br/>This is disabled when the connection is offline or the user is alone in a room.
                  </Box>
                }
              />
              <Box>: {`${appService().isEmittingNotes()}`}</Box>
            </HStack>
            <Divider/>
            <MemoryStats/>
          </VStack>
        </BasicWindow>
      </Match>

      <Match when={platformService().isMobile()}>
        <Box
          position={"absolute"}
          top={30}
          right={0}
        >
          <MemoryStats/>
        </Box>
      </Match>
    </Switch>
  </>);
}

const MemoryStats = () => {
  const { isSupported, memory } = useMemory();
  const platformService = useService(PlatformService);

  return (<>
      {isSupported() &&
          <Box>
              <Box>Memory Stats (JS Heap)</Box>
              <VStack
                  paddingRight={platformService().isMobile() ? 10 : 0}
                  marginLeft={platformService().isMobile() ? 0 : 10}
                  justifyContent={platformService().isMobile() ? "flex-end" : "flex-start"}
                  alignItems={platformService().isMobile() ? "flex-end" : "flex-start"}
              >
                  <Box>Used: <Box as="span" color={"$accent1"}>{convertSize(memory()?.usedJSHeapSize ?? 0)}</Box></Box>
                  <Box>Allocated: <Box as="span"
                                       color={"$tertiary1"}>{convertSize(memory()?.totalJSHeapSize ?? 0)}</Box></Box>
                  <Box>Limit: {convertSize(memory()?.jsHeapSizeLimit ?? 0)}</Box>
              </VStack>
          </Box>
      }
    </>
  );
};