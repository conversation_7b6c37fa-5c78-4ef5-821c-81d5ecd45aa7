import { Box } from "@hope-ui/solid";
import clsx from "clsx";
import rehypeRaw from "rehype-raw";
import rehypeSanitize, { defaultSchema } from "rehype-sanitize";
import remarkGfm from "remark-gfm";
import { Component, createEffect, createSignal, onCleanup, Show } from "solid-js";
import SolidMarkdown from "solid-markdown";
import { useService } from "solid-services";
import { rolesToJSON } from "~/proto/user-renditions";
import UsersService from "~/services/users-service";
import { ApiUserDto } from "~/types/user.types";
import UsernameWithMiniProfile from "./username.w-miniprofile";

let newSchema: any = { ...defaultSchema };

// Custom Text Tag
// Examples:
//<text background="green" border_radius="5px">FONT TEXT</text>
//<text background="$accent1" border-radius="5px">FONT TEXT</text>
//<text color="blue" background="red" padding="5px" border="1px solid white" borderRadius="10px">YOO</text>
const customTextTagName = "text";
newSchema.tagNames?.push(customTextTagName);
if (newSchema.attributes) {
  newSchema.attributes[customTextTagName] = ["color", "background", "padding", "border", "border_radius", "border-radius"];
}

// Custom User tag
// Examples:
// <user uuid="test#62a411" />
const customUserTagName = "user";
newSchema.tagNames?.push(customUserTagName);
if (newSchema.attributes) {
  newSchema.attributes[customUserTagName] = ["uuid"];
}

// Custom Modal tags
// Examples:
// <prmodal id="test" menu-id="test" label-id="test">Modal Content</prmodal>
// <prmodal id="test">Modal Content</prmodal>
// const customModalTagName = "prmodal";
// newSchema.tagNames?.push(customModalTagName);
// if (newSchema.attributes) {
//   // newSchema.attributes[customModalTagName] = ["title", "id", "menu-id", "sub-menu-id", "label-id"];
//   newSchema.attributes[customModalTagName] = ["id"];
// }

newSchema.tagNames = Array.from(new Set(newSchema.tagNames));

export const SolidMarkDownText: Component<{ className?: string; text: string; allowImages?: boolean; }> = (props) => {
  const [message, setMessage] = createSignal<string>();
  const [update, setUpdate] = createSignal(false);
  const [updateTimeout, setUpdateTimeout] = createSignal(-1);
  const onClean = () => window.clearTimeout(updateTimeout());
  const usersService = useService(UsersService);

  // Hacky way to force refresh the SolidMarkdown component
  // since it doesn't seem to reload when the children string
  // is updated...
  createEffect(() => {
    if (message() == props.text) return;
    onClean();
    setUpdate(true);
    setMessage(props.text);
    setUpdateTimeout(window.setTimeout(() => setUpdate(false), 1));
  });

  onCleanup(onClean);

  return (<>
    <Show fallback={"Loading..."} when={!update()}>
      {/* @ts-ignore */}
      <SolidMarkdown
        class={props.className ? clsx(["solid-markdown", props.className]) : "solid-markdown"}
        linkTarget="null"
        components={{
          // ---- BEGIN CUSTOM TAGS -----
          //@ts-ignore
          // "prmodal": ({ ...props }) => {
          //   // let { title, id: modalID, "menu-id": menuID, "label-id": labelID } = props;
          //   console.log("chat-modal", props);

          //   return <Box>Modal Here!</Box>;
          // },
          "text": ({ node, ...props }) => {
            //@ts-ignore
            return (<Box as="span" {...props} borderRadius={props["border_radius"] || props["border-radius"]} />);
          },
          //@ts-ignore
          "user": ({ ...props }) => {
            let value = props["uuid"] ?? props?.["children"]?.[0];
            let apiUser = ApiUserDto.DefaultWithUsername2(value);

            let user = usersService().getUserByUsertag(value) ?? usersService().getUserBySocketID(value);
            if (user && user.userDto) {
              let _apiUser = ApiUserDto.DefaultWithUsername2(user.userDto?.usertag);
              _apiUser.username = user.userDto?.username;
              _apiUser.roles = (user.userDto?.roles ?? []).map(rolesToJSON);
              _apiUser.color = user.userDto?.color;
              apiUser = _apiUser;
            }

            return <UsernameWithMiniProfile user={apiUser} textColor="white" />;
          },
          // ---- END CUSTOM TAGS -----
          "code": ({ node, ...props }) => {
            props.children = props.children.map((x: any) => {
              if (x.replace) {
                return (x as string).replaceAll("&lt;", "<").replaceAll("&gt;", ">");
              }
              return x;
            });

            //@ts-ignore
            return (<code {...props} />);
          },
          //@ts-ignore
          "li": ({ node, ...props }) => {
            props.children = props.children.map((x: any) => {
              if (x.replace) return x.replaceAll("\\", "");
              return x;
            });

            //@ts-ignore
            return (<li {...props} />);
          },
          //@ts-ignore
          "a": ({ node, ...props }) => {
            // Do not parse emails
            if (props.href?.includes("mailto:")) props.href = undefined;

            //@ts-ignore
            return (<a {...props} />);
          },
        }}
        remarkPlugins={[
          remarkGfm,
        ]}
        rehypePlugins={[
          [rehypeRaw, { passThrough: false }],
          [rehypeSanitize, newSchema],
        ]}
        disallowedElements={[
          !props.allowImages && 'img',
        ].filter(Boolean) as string[]}
        unwrapDisallowed
        children={message()} />
    </Show>
  </>);
};
