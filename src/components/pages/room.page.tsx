import { Box, HStack, Image, SkeletonCircle } from "@hope-ui/solid";
import { Title } from "@solidjs/meta";
import { useNavigate, useParams } from "@solidjs/router";
import {
  DragDropProvider,
  DragDropSensors,
  DragOverlay,
  mostIntersecting,
  useDragDropContext
} from "@thisbeyond/solid-dnd";
import clsx from "clsx";
import clone from "lodash-es/clone";
import { createEffect, createSignal, ErrorBoundary, lazy, onCleanup, onMount, ParentComponent, Show, Suspense } from "solid-js";
import { Portal } from "solid-js/web";
import { Motion, Presence } from "solid-motionone";
import { useService } from "solid-services";
import { validateUserSessionInfo } from "~/lib";
import { Instrument } from "~/proto/midi-renditions";
import { AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import NotificationService from "~/services/notification.service";
import ResourceService from "~/services/resource.service";
import WebsocketService from "~/services/websocket.service";
import { CurrentPage } from "~/types/app.types";
import { COMMON, IDS } from "~/util/const.common";
import { logWarn } from "~/util/logger";
import ComponentLoader from "../component-loader";
import { FallBackSkeletonHope } from "../modals/settings-content/common.content";
import MotionFadeIn from "../motion/motion.fade-in";
import { Subscription, fromEvent } from "rxjs";
import AudioService from "~/services/audio.service";
import { map } from "~/util/helpers";

const ChannelVolumeModal = lazy(() => import('~/components/modals/channel-volume'));
const ChannelPanModal = lazy(() => import('~/components/modals/channel-pan'));
const ChannelsSlidersModal = lazy(() => import('~/components/modals/channels-sliders'));
const PianoContainer = lazy(() => import("~/components/canvas/piano.canvas.container"));
const BottomBar = lazy(() => import("~/components/bottombar"));
const InstrumentsList = lazy(() => import("~/components/instrument-selection.list"));
const InstrumentDock = lazy(() => import("~/components/instrument-dock"));
const ShortcutsManager = lazy(() => import("~/services/shortcuts.service"));
const SettingsModal = lazy(() => import('~/components/modals/settings'));
const MidiIOModal = lazy(() => import('~/components/modals/midi-io'));
const RoomMessagesDisplay = lazy(() => import('~/components/room-message'));
const CreditsModal = lazy(() => import('~/components/modals/credits'));
const ReleaseNotesModal = lazy(() => import('~/components/modals/release-notes'));
const SidebarDocsPanel = lazy(() => import('~/components/modals/sidebar-docs-panel'));
const NewRoomModal = lazy(() => import('~/components/modals/new-room'));
const SideBarList = lazy(() => import('~/components/sidebar'));
const DebugStats = lazy(() => import('~/components/debug-stats'));
const ChatComponent = lazy(() => import('~/components/chat'));
const EnterPasswordModal = lazy(() => import('~/components/enter-password'));
const DocsModal = lazy(() => import('~/components/modals/docs'));
const ModDashboardModal = lazy(() => import('../modals/mod-dashboard/mod-dashboard.modal'));
const SoundfontsListModal = lazy(() => import('~/components/modals/soundfonts-list'));
const AudioEqualizerModal = lazy(() => import('~/components/modals/equalizer'));
const AudioReverbModal = lazy(() => import('~/components/modals/reverb'));

export default function RoomPage() {
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);
  const websocketService = useService(WebsocketService);
  const audioService = useService(AudioService);

  const params = useParams();
  const navigate = useNavigate();
  const [appStateEffectListener, setAppStateEffectListener] = createSignal<VoidFunction>(() => { });
  const [eventSubs, _setEventSubs] = createSignal<VoidFunction[]>([]);

  onMount(async () => {
    if (COMMON.IS_DEV_MODE) console.log("[Room] Mounting...");
    let targetRoomName = decodeURI(params.roomName ?? "");
    NotificationService.hide(IDS.AUDIO_INITIALIZATION);
    NotificationService.hide(IDS.USER_LOGGING_IN);

    appService().setCurrentPage(CurrentPage.Room);

    if (!appService().initialized()) {
      logWarn("[Room] App must initialize first.");
      let path = "/app-loading";
      if (targetRoomName) path += `?roomName=${targetRoomName}`;
      navigate(path, { replace: true });
      return;
    }

    try {
      // Validate user session info
      await validateUserSessionInfo();

      // Validate user is connected to the websocket
      if (!websocketService().connected()) throw new Error("Websocket not connected, yet.");
    } catch (e) {
      console.error("[Room]", e);
      logWarn("[Room] User session not valid or not connected to server.");
      let path = "/login";
      if (targetRoomName) path += `?roomName=${targetRoomName}`;
      return navigate(path, { replace: true });
    }

    if (targetRoomName) {
      NotificationService.show({
        title: "PianoRhythm",
        description: `Joining room: ${targetRoomName}`,
        type: "info",
        duration: 3000,
      });
      websocketService().joinRoomByName(targetRoomName, true);
    } else {
      NotificationService.show({
        title: "PianoRhythm",
        description: `Joining next available room...`,
        type: "info",
        duration: 3000,
      });
      websocketService().joinNextAvailableLobby();
    }

    let sub = appService().appStateEffects.listen(effect => {
      switch (effect.action) {
        case AppStateEffects_Action.JoinedRoomSuccess: {
          let data = effect.joinedRoomData;
          if (!data) return;
          window.history.pushState(null, '', `/room/${data.roomName}`);
          break;
        }
      }
    });
    setAppStateEffectListener(() => sub);
  });

  onCleanup(() => {
    if (appService().initialized()) appService().onDisconnect();
    websocketService().disconnect();
    appStateEffectListener()();
    eventSubs().forEach(sub => sub());
  });

  return (
    <>
      {appService().initialized() && <MotionFadeIn duration={1}>
        <Box
          w="100vw"
          h="100dvh"
          pointerEvents={appService().activatePageLoader() ? "none" : "auto"}
        >
          <Title>PianoRhythm</Title>

          {/* Piano Canvas */}
          <Suspense><PianoContainer /></Suspense>

          {/* @ts-ignore */}
          <DragDropProvider collisionDetector={mostIntersecting}>
            <DragDropSensors>
              <Suspense>
                <Portal><InstrumentsList /></Portal>
                <InstrumentDock />
              </Suspense>
              <InstrumentsDragDropSandbox />
            </DragDropSensors>
          </DragDropProvider>

          {/* Shortcuts Manager */}
          <Suspense><ShortcutsManager /></Suspense>

          {/* Sidebar */}
          <Suspense fallback={<FallBackSkeletonHope left={0} top={0} w={"calc(var(--sidebarListWidth))"} h={"calc(100vh - var(--bottomBarHeight))"} />}>
            {!appService().offlineMode() &&
              <ErrorBoundary fallback={"Failed to load sidebar."}>
                <SideBarList />
              </ErrorBoundary>
            }
          </Suspense>

          {/* Modals */}
          {displayService().getDisplay("CHANNELS_SLIDERS_MODAL") &&
            <Suspense>
              <ChannelsSlidersModal />
            </Suspense>
          }

          {displayService().getDisplay("SET_CHANNEL_VOLUME_MODAL") &&
            <Suspense>
              <ChannelVolumeModal />
            </Suspense>
          }

          {displayService().getDisplay("SET_CHANNEL_PAN_MODAL") &&
            <Suspense>
              <ChannelPanModal />
            </Suspense>
          }

          {displayService().getDisplay("SOUNDFONTS_LIST_MODAL") &&
            <Suspense>
              <SoundfontsListModal />
            </Suspense>
          }

          {displayService().getDisplay("MIDI_IO_MODAL") &&
            <Suspense fallback={<ComponentLoader />}><MidiIOModal /></Suspense>}

          {displayService().getDisplay("SETTINGS_MODAL") &&
            <Suspense fallback={<ComponentLoader />}>
              <MotionFadeIn><SettingsModal /></MotionFadeIn>
            </Suspense>}

          {/* Chat */}
          {!appService().offlineMode() &&
            <Suspense fallback={<ComponentLoader />}>
              <ChatComponent />
            </Suspense>
          }

          {displayService().getDisplay("BOTTOM_BAR") &&
            <Suspense ><BottomBar /></Suspense>
          }

          {displayService().getDisplay("CREDITS") &&
            <Suspense fallback={<ComponentLoader />}><CreditsModal /></Suspense>
          }

          {displayService().getDisplay("RELEASE_NOTES") &&
            <Suspense fallback={<ComponentLoader />}><ReleaseNotesModal /></Suspense>
          }

          {(displayService().getDisplay("NEW_ROOM_MODAL") || displayService().getDisplay("UPDATE_ROOM_MODAL")) &&
            <Suspense fallback={<ComponentLoader />}><NewRoomModal /></Suspense>
          }

          {displayService().getDisplay("SIDEBAR_HELP_DOCS") &&
            <Suspense fallback={<ComponentLoader />}>
              <SidebarDocsPanel />
            </Suspense>
          }

          {displayService().getDisplay("DEBUG_STATS") &&
            <Suspense>
              <DebugStats />
            </Suspense>
          }

          {displayService().getDisplay("AUDIO_EQUALIZER_MODAL") &&
            <Suspense><AudioEqualizerModal /></Suspense>
          }

          {displayService().getDisplay("AUDIO_REVERB_MODAL") &&
            <Suspense><AudioReverbModal /></Suspense>
          }

          {displayService().getDisplay("DOCS_MODAL") &&
            <Suspense><DocsModal /></Suspense>
          }

          {/* Mod dashboard */}
          {displayService().getDisplay("MOD_DASHBOARD_MODAL") &&
            <Suspense><ModDashboardModal /></Suspense>
          }

          {/* Enter Password Modal */}
          <Suspense><EnterPasswordModal /></Suspense>

          <RoomMessagesDisplay />

          {(audioService().mousePosSetsVelocity()) && <CaptureMouseMovementForAudioVelocity />}

        </Box>
      </MotionFadeIn>
      }
    </>
  );
}

const InstrumentsDragDropSandbox: ParentComponent = (props) => {
  //@ts-ignore
  const [state] = useDragDropContext();
  const resourceService = useService(ResourceService);
  const [activeInstrument, setActiveInstrument] = createSignal<Instrument>();
  const instrumentName = () => activeInstrument()?.name ?? "piano";
  const [instrumentImagePath, { refetch }] = resourceService().instrumentImageFetch(instrumentName);

  createEffect(() => {
    let draggableItem = state.active.draggable;
    setActiveInstrument(draggableItem ? clone(draggableItem.data?.onGetInstrument?.()) : undefined);
    if (draggableItem) refetch();
  });

  const ImageSourceFallback = () => <Box h={25} w={25}>
    <SkeletonCircle position={"absolute"} bottom={3} left={5} size="$5" />
  </Box>;

  return (<>
    <DragOverlay>
      <Presence exitBeforeEnter>
        <Show when={activeInstrument()}>
          <Motion
            initial={{ opacity: 0, scale: 0.6 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.6 }}
            transition={{ duration: 0.3 }}
          >
            <Box
              zIndex={99999999}
              position="absolute"
              class="draggable"
              padding={5}
              borderRadius={5}
              cursor={"grabbing"}
              background={"var(--hope-colors-primaryDark2)"}
              border={"2px solid var(--hope-colors-accent1)"}
            >
              <HStack spacing={"$1"} w="100%" h="100%">
                <Suspense
                  fallback={<ImageSourceFallback />}
                >
                  <Image
                    width={25}
                    class={clsx(["unselectable"])}
                    fallback={<ImageSourceFallback />}
                    src={instrumentImagePath()}
                    alt={`${activeInstrument()?.displayName} instrument`} />
                </Suspense>
                <Box w="100%" h="100%">{activeInstrument()?.name}</Box>
              </HStack>
            </Box>
          </Motion>
        </Show>
      </Presence>
    </DragOverlay>
    {props.children}
  </>);
};

const CaptureMouseMovementForAudioVelocity = () => {
  const audioService = useService(AudioService);
  const [mouseMoveObs, setMouseMoveObs] = createSignal<Subscription>();

  function mapMousePosToVelocity(posY: number) {
    let vel = Math.max(0, posY) / window.innerHeight;
    return map(vel, 0, 1, 10, 127);
  }

  onMount(() => {
    let obs = fromEvent(document, "mousemove");
    setMouseMoveObs(obs.subscribe(evt => {
      let mouseEvent = evt as MouseEvent;
      audioService().setMousePosVelocity(mapMousePosToVelocity(mouseEvent.clientY));
    }));
  });

  onCleanup(() => {
    mouseMoveObs()?.unsubscribe();
  });

  return undefined;
};