import { Badge, Box, HStack } from "@hope-ui/solid";
import { FaSolidEye, FaSolidHeart } from "solid-icons/fa";

interface SupportedRepoType {
  id: string;
  title: string;
  category?: string;
  creatorUsername: string;
  approved?: boolean;
  views: number;
  favorites: number;
}

export const GenericDefaultBadges = (item: SupportedRepoType) => {
  return [
    <Badge
      __tooltip_title={`Views: ${item.views ?? 0}`}
      fontSize={"8px !important"} opacity={0.7} zIndex={2}>
      <HStack spacing={"$1"}><FaSolidEye /><Box>{item.views ?? 0}</Box></HStack>
    </Badge>,

    <Badge
      __tooltip_title={`Favorites: ${item.favorites ?? 0}`}
      fontSize={"8px !important"} opacity={0.7} zIndex={2}>
      <HStack spacing={"$1"}><FaSolidHeart color="coral" /><Box>{item.favorites ?? 0}</Box></HStack>
    </Badge>];
};
