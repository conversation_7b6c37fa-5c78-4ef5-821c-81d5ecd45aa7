.search-result {
  border: 1px solid var(--hope-colors-neutral12);
  border-radius: 4px;
  align-items: center;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.search-result__heading {
  text-align: center;
  margin: 0;
}

.search-result__body {
  display: flex;
  justify-content: flex-start;
  align-self: flex-start;
}

.search-result__data {
  padding-top: 50px;
}

.search-result__flavor {
  margin: 0;
}

.search-result__image {
  flex: none;
  width: 256px;
  height: 387px;
}

.search-section__search-results {
  display: flex;
  flex-wrap: wrap;
}

.search-section__search-result {
  width: 48%;
  margin: 1%;
  // background: white;
}

.sui-layout-main {
  padding: 16px 0 0px 0px;
}

.sui-layout-body {
  background: var(--hope-colors-primary1);
}

.sui-layout-body:after {
  display: none;
  background: transparent;
}

.sui-layout-header {
  border-bottom: 1px solid var(--hope-colors-neutral8);
  padding: 0px;
}

.sui-search-box__text-input {
  border-radius: 4px;
  border: 1px solid #ccc;
  padding: 5px;
  outline: none;
  position: relative;
  font-family: inherit;
  font-size: 14px;
  width: 100%;
}
