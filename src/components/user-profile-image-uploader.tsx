import { Box } from "@hope-ui/solid";
import { Component, ErrorBoundary, Setter } from "solid-js";
import { Portal } from "solid-js/web";
import { FILE_UPLOAD } from "~/util/const.common";
import ImageUploaderModal from "~/components/image-uploader";
import notificationService from "~/services/notification.service";

type UserProfileImageUploaderProps = {
  onSuccess?: () => void;
  setShow: Setter<boolean>;
};

const UserProfileImageUploader: Component<UserProfileImageUploaderProps> = (props) => {
  return (<>
    <Portal mount={document.body}>
      <Box position={"absolute"} top={0}>
        <ErrorBoundary fallback={<Box w={100} h={100} background={"$primary1"}>Failed to load Image Uploader...</Box>}>
          <ImageUploaderModal
            path={`/api/users/members/upload-profile-image`}
            fileSizeLimit={FILE_UPLOAD.MaxProfileImageFileSize}
            dimensions={{
              width: FILE_UPLOAD.MaxImageWidth,
              height: FILE_UPLOAD.MaxImageHeight,
            }}
            onClose={() => props.setShow(false)}
            headerLabel={"Profile Image Upload"}
            onSaveError={(error) => {
              notificationService.show({
                type: "danger",
                title: `Failed to upload image`,
                description: error.message
              });
            }}
            onSave={async (response) => {
              switch (response) {
                case "Success":
                  notificationService.show({
                    type: "success",
                    description: "Profile Image Uploaded!"
                  });
                  if (props.onSuccess) props.onSuccess();
                  break;
                case "PermissionDenied":
                  notificationService.show({
                    type: "danger",
                    description: "You don't have permission to upload images."
                  });
                  break;
                default:
                  notificationService.show({
                    type: "danger",
                    title: `Failed to upload image`,
                    description: response.toString()
                  });
                  break;
              }
            }}
          />
        </ErrorBoundary>
      </Box>
    </Portal>
  </>);
};

export default UserProfileImageUploader;