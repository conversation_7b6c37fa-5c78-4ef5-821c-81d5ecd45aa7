import { Box, Button, ButtonGroup, HStack, <PERSON>over, PopoverAnchor, PopoverBody, PopoverContent, PopoverHeader, VStack, hope } from "@hope-ui/solid";
import { BiSolidEdit } from "solid-icons/bi";
import { FaSolidFlag, FaSolidReply, FaSolidTrash } from "solid-icons/fa";
import { Component, JSX } from "solid-js";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import ChatService from "~/services/chat.service";
import { ChatMessageRecord } from "~/types/chat.types";
import { deleteMessageByID } from "./chat.common";
import WebsocketService from "~/services/websocket.service";
import ReportService from "~/services/report.service";

type ChatMessageItemMoreMenuProps = {
  isOpen: () => boolean;
  onClose: () => void;
  record: ChatMessageRecord;
};

const MenuButton_: Component<{ label: string, icon?: JSX.Element; onClick?: () => void; }> = (props) => {
  return (
    <Button
      _hover={{
        "color": "white"
      }}
      onMouseDown={(evt: MouseEvent) => {
        if (evt.button == 0) props.onClick?.();
      }}
      borderRadius={3}
      background={"$primaryDark1"}
      w="100%" >
      <HStack w="100%" justifyContent={"space-between"} {...props}>
        <Box>{props.label}</Box>
        {props.icon && props.icon}
      </HStack>
    </Button>
  );
};

const MenuButton = hope(MenuButton_);

const ChatMessageItemMoreMenu: Component<ChatMessageItemMoreMenuProps> = (props) => {
  const appService = useService(AppService);
  const reportService = useService(ReportService);
  const chatService = useService(ChatService);
  const websocketService = useService(WebsocketService);

  const canDelete = () => chatService().canDeleteMessage(props.record);

  const canEdit = () => chatService().canEditMessage(props.record);

  const canReply = () => chatService().canReplyToMessage(props.record);

  const canReport = () => chatService().canReportMessage(props.record);

  return (<>
    <Popover
      placement="left-start"
      opened={props.isOpen()}
      onClose={props.onClose}
      closeOnBlur
      closeOnEsc
    >
      <PopoverAnchor
        as={Box}
        position={"absolute"}
        w="100%"
        h="100%"
        top={10}
        right={168}
        class="testy"
      />
      <PopoverContent
        background={"$primaryDark1"}
        w={150}
      >
        <PopoverHeader>Options</PopoverHeader>
        <PopoverBody padding={5}>
          <ButtonGroup variant={"ghost"} w="100%" size={"sm"}>
            <VStack w="100%">
              {canReply() && <MenuButton label="Reply" onClick={() => chatService().setChatMessageBeingRepliedTo(props.record.id)} icon={<FaSolidReply />} />}
              {canEdit() && <MenuButton label="Edit" onClick={() => chatService().onEditMessage(props.record)} icon={<BiSolidEdit />} />}
              {canReport() && <MenuButton label="Report" onClick={() => {
                chatService().setChatMessageToHighlight(props.record.id);
                reportService()
                  .reportMessage(appService().client().usertag, props.record)
                  .finally(() => {
                    chatService().setChatMessageToHighlight(undefined);
                  });
              }} icon={<FaSolidFlag />} />}
              {canDelete() && <MenuButton
                onClick={() => {
                  deleteMessageByID(
                    props.record.id,
                    (id) => websocketService().emitServerCommand(["DeleteChatMessageById", id]),
                    chatService()
                  );
                }}
                color="red" label="Delete" icon={<FaSolidTrash />} />}
            </VStack>
          </ButtonGroup>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  </>);
};

export default ChatMessageItemMoreMenu;