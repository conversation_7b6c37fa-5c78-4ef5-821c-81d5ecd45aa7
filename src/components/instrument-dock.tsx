import { Box, Button, ButtonGroup, Center, Container, Divider, Icon, IconButton, Image, Input, SimpleGrid, Spinner, VStack } from '@hope-ui/solid';
import { createDraggable, Draggable, Droppable, useDragDropContext } from "@thisbeyond/solid-dnd";
import anime from 'animejs';
import clsx from "clsx";
import clamp from 'lodash-es/clamp';
import { FaSolidArrowLeft, FaSolidArrowRight, FaSolidTrash, FaSolidVolumeHigh, FaSolidVolumeXmark } from 'solid-icons/fa';
import { TiTimes } from 'solid-icons/ti';
import { createImmerSignal } from 'solid-immer';
import { batch, Component, createEffect, createSignal, For, Match, onCleanup, onMount, Show, Suspense, Switch } from 'solid-js';
import { createStore } from 'solid-js/store';
import { useService } from 'solid-services';
import { Transition } from 'solid-transition-group';
import MotionFadeIn from "~/components/motion/motion.fade-in";
import { buttonSFX } from '~/directives/buttonsfx.directive';
import { Item, Menu, Separator, Submenu, useContextMenu } from "~/packages/solid-contextmenu/index";
import { activeChannelsModeToJSON, AudioChannel, SetChannelInstrumentType } from '~/proto/midi-renditions';
import style from '~/sass/instrument-dock.module.sass';
import AudioService from '~/services/audio.service';
import DisplaysService from '~/services/displays.service';
import I18nService from '~/services/i18n.service';
import MediaQueryService from '~/services/media-query.service';
import ResourceService from '~/services/resource.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import { AppSoundFx } from '~/types/app.types';
import { AudioChannelHelper } from '~/types/audio.types';
import { AUDIO, HTML_IDS } from '~/util/const.common';
import { MIDI } from '~/util/const.midi';
import { arrayChunks } from '~/util/helpers';
import { onRemoveAnimeInstance } from "~/util/helpers.anime-js";

const DEFAULT_DOCK_OPACITY = 0.3;
const TOOL_BUTTONS_BORDER_COLOR = "$neutral11";

enum CHANNEL_DISPLAYS {
  FOUR = 4,
  EIGHT = 8,
  SIXTEEN = 16
}

const [activeDisplayedChannels, setActiveDisplayedChannels] = createSignal(CHANNEL_DISPLAYS.EIGHT);
const [activeDroppableByChannel, setActiveDroppableByChannel] = createStore<{ [key: number]: boolean; }>({});
const activeDroppables = () => Object.entries(activeDroppableByChannel).filter(x => x[1]).map(x => parseInt(x[0]));

const InstrumentChannel: Component<{ maxDisplays: CHANNEL_DISPLAYS, midiChannel: number; }> = (props) => {
  const MENU_ID = `instrument-dock-channel-${props.midiChannel}`;
  const boxHeight = 30;
  const audioService = useService(AudioService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);
  const resourceService = useService(ResourceService);

  // @ts-ignore
  const [state, { onDragEnd }] = useDragDropContext();
  const [slotColor, setSlotColor] = createSignal("$primaryDark1");
  const [disabled, setDisabled] = createSignal(false);
  const [animate, setAnimate] = createSignal(false);
  const [activeDroppable, setActiveDroppable] = createSignal(false);
  const [isDrumChannelMuted, setIsDrumChannelMuted] = createSignal(false);
  const [isDrumChannelDisabled, setIsDrumChannelDisabled] = createSignal(false);
  const [obscureWhileOtherChannelIsDroppable, setObscureWhileOtherChannelIsDroppable] = createSignal(false);
  const [deleteInstOpacity, setDeleteInstOpacity] = createSignal(1);
  const [channelVolume, setChannelVolume] = createSignal(1);
  const [activeClasses, setActiveClasses] = createImmerSignal<string[]>([]);
  const [instrumentName, setInstrumentName] = createSignal<string | undefined>(undefined);
  const { onShowContextMenu } = useContextMenu({ id: MENU_ID });
  const [instrumentImagePath] = resourceService().instrumentImageFetch(instrumentName);
  const [channel, setChannel] = createSignal<AudioChannel>();

  let element!: HTMLDivElement;
  const midiChannel = props.midiChannel;
  const draggable = createDraggable(`dock-slot-draggable-${props.midiChannel}`, {
    onGetInstrument: () => channel()?.instrument
  });

  const draggableItemStart = () => state.active.draggable != null;

  const isChannelActive = () => channel()?.active;

  const isChannelPrimary = () => audioService().primaryChannel() == channel()?.channel;

  const isChannelDrums = () => channel()?.channel == MIDI.DRUM_CHANNEL;

  const removeInstrumentFromChannel = () => {
    audioService().removeInstrumentFromChannel(midiChannel);
    //Set Primary channel to default if it was the primary
    if (isChannelPrimary()) audioService().setPrimaryChannel(0);
  };

  createEffect(() => {
    setIsDrumChannelDisabled(
      Boolean(
        isChannelDrums() && !appSettingsService().getSetting("AUDIO_ENABLE_DRUM_CHANNEL")
      )
    );
  });

  createEffect(() => {
    let _channel = audioService()
      .channels()
      .find(x => x.channel == midiChannel);

    if (!_channel) return;

    let name =
      !isDrumChannelMuted() && _channel?.channel == MIDI.DRUM_CHANNEL ?
        "drum" : (_channel?.instrument?.name);

    setChannelVolume(_channel?.volume ?? 0);
    if (_channel?.instrument) setInstrumentName(name);
    if (!_channel?.active || !_channel?.instrument) setAnimate(false);
    setChannel(_channel);
  });

  function reanimate(className: string) {
    //source: https://css-tricks.com/restart-css-animation/
    if (element) {
      element.classList.remove(className);
      void element.offsetWidth;
    }
  }

  createEffect(() => {
    // Slot mode reference to help trigger for checking disabled channels
    audioService().slotMode();
    audioService().maxMultiModeChannels();
    setDisabled((audioService().getDisabledChannels() || []).includes(midiChannel));
  });

  createEffect(() => {
    let _channel = channel();
    batch(() => {
      if (!_channel) return;
      let _audioService = audioService();
      let midiChannel = _channel.channel;
      let slotMode = _audioService.slotMode();
      let outputSlotColor = AudioChannelHelper.getSlotColor(midiChannel, slotMode, audioService().maxMultiModeChannels());

      if (_channel.active) {
        reanimate("elementInactive");
        setActiveClasses(["elementActive"]);
      } else {
        reanimate("elementActive");
        setActiveClasses(["elementInactive"]);
      }
      setSlotColor(outputSlotColor || "$primaryDark1");
    });
  });

  createEffect(() => {
    let active = activeDroppables();
    if (active.length > 0) {
      if (active.includes(midiChannel)) return setObscureWhileOtherChannelIsDroppable(true);
    }
    setObscureWhileOtherChannelIsDroppable(false);
  });

  onDragEnd((event: {
    draggable: Draggable;
    droppable?: Droppable | null;
  }) => {
    let draggable = event.draggable;
    if (activeDroppable()) {
      let inst = draggable.data?.onGetInstrument?.();
      if (isDrumChannelDisabled() || !inst) return;
      audioService().setInstrumentOnChannel(midiChannel, inst.bank, inst.preset, SetChannelInstrumentType.Add);
    }
  });

  createEffect(() => {
    if (disabled()) return;
    onRemoveAnimeInstance(element);
    anime({
      targets: element,
      scale: activeDroppable() ? 1.1 : 1,
      zIndex: activeDroppable() ? -999 : 0,
      opacity: activeDroppable() ? 1 : channel()?.active ? 1 : 0.5,
      duration: 200,
      easing: 'easeOutElastic(0.1, 1)',
      complete: () => { if (!activeDroppable()) onRemoveAnimeInstance(element); }
    });
  });

  createEffect(() => {
    setIsDrumChannelMuted(audioService().isDrumChannelMuted());
  });

  return (<>
    <MotionFadeIn duration={0.25} >
      <Box
        __tooltip_title={
          <VStack>
            {activeDroppable() && <Box color="$accent1" fontWeight={"$bold"}>Drop Instrument Here</Box>}
            {isChannelPrimary() && <Box fontWeight={"bold"}>Primary Channel</Box>}
            {isChannelDrums() && "(DRUM)"}
            <Box>Channel: {midiChannel}</Box>
            {channel()?.instrument &&
              <>
                <Box>Volume: {channel()?.volume}</Box>
                <Box>Pan: {channel()?.pan}</Box>
              </>
            }
            <Divider color={"$neutral10"} mb={3} />
            <Box color="$tertiary1" fontWeight={"$bold"}>{channel()?.instrument?.displayName || <Box class="disabled">No Instrument</Box>}</Box>
          </VStack>
        }
        border={"solid 2px rgba(255,255,255,0.2)"}
        outline={isChannelPrimary() ? "solid 2px rgba(255,255,255,1)" : ""}
        borderColor={isChannelActive() ? "$accent1" : "none"}
        height={`${boxHeight}px`}
        style={{
          filter: `${draggableItemStart() && !obscureWhileOtherChannelIsDroppable() ?
            "opacity(0.7)" : "none"}`
        }}
        transition={"opacity 0.1s, transform 0.1s, z-index 0.1s, filter 0.1s"}
        opacity={isChannelActive() ? 1 : 0.5}
        backgroundColor={animate() ? "$neutral11" : "transparent"}
        ref={(_element: any) => {
          element = _element;
          draggable.ref(_element);
        }}
        id={`instrument-dock-slot-${midiChannel}`}
        class={clsx([
          style.prDockSlot,
          ...activeClasses(),
          ((disabled() || (props.midiChannel == 9 && isDrumChannelMuted())) && "disabled")
        ])}
        onMouseEnter={() => {
          if (isDrumChannelDisabled()) return;
          sfxService().playSFX(AppSoundFx.SELECT, { volume: 0.02 });
        }}
        onMouseOver={() => {
          if (draggableItemStart()) {
            setActiveDroppable(true);
            setActiveDroppableByChannel({ [midiChannel]: true });
          }
        }}
        onMouseOut={() => {
          setActiveDroppable(false);
          setActiveDroppableByChannel({ [midiChannel]: false });
        }}
        onMouseDown={(event: MouseEvent) => {
          if (isDrumChannelDisabled()) return;
          if (event.button == 0 && channel()?.instrument) {
            audioService().toggleChannelActive(midiChannel);
            sfxService().playSFX(channel()?.active ? AppSoundFx.TONE_NEGATIVE : AppSoundFx.TONE_POSITIVE);
          }
        }}
        oncontextmenu={onShowContextMenu}
        {...draggable.dragActivators}
      >
        {/* Remove instrument */}
        {channel()?.instrument &&
          <Icon
            __tooltip_title={"Remove Instrument"}
            onMouseEnter={() => setDeleteInstOpacity(0.7)}
            onMouseLeave={() => setDeleteInstOpacity(1)}
            onMouseDown={(evt: MouseEvent) => { if (evt.button == 0 && channel()?.instrument) removeInstrumentFromChannel(); }}
            class={style.prDockTrashButton} boxSize={"$3"} as={TiTimes} />
        }

        {/* Instrument image */}
        <Center h="100%" w="100%"
          class={clsx([
            style.prDockSlot_ImageWrapper,
            (deleteInstOpacity() != 1 ? style.prDockSlot_ImageWrapperFade : "")
          ])}
          opacity={deleteInstOpacity()}
        >
          {(channel()?.instrument) &&
            <Suspense fallback={<MotionFadeIn><Center><Spinner /></Center></MotionFadeIn>}>
              <MotionFadeIn duration={0.5} delay={0.15}>
                <Image
                  class={clsx([style.prDockSlot_Image, "undraggable"])}
                  src={instrumentImagePath()}
                  alt={`${channel()?.instrument?.displayName ?? "Unknown"} instrument`}
                />
              </MotionFadeIn>
            </Suspense>
          }
        </Center>

        {/* Channel Index */}
        <Box
          class={clsx([style.prDockSlot_Triangle, "unselectable"])}
          borderColor={`rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) ${disabled() ? "gray" : slotColor()}`}
        ></Box>
        <Box class={clsx([style.prDockSlot_Index, "unselectable"])}>{midiChannel}</Box>

        {/* Volume */}
        <Show when={channelVolume() == 0} fallback={
          <Box
            backgroundColor={"$primary2"}
            class={style.prDockSlot_Volume}
            height={`${((channelVolume() || 0) / MIDI.MAX_VELOCITY) * 100}%`}
          />
        }>
          <Box class={style.prDockSlot_Muted}>
            <FaSolidVolumeXmark />
          </Box>
        </Show>
      </Box>
    </MotionFadeIn>

    {/* Context Menu */}
    <Menu id={MENU_ID}>
      {channel() &&
        <>
          {channel()!.instrument && <Item disabled>{channel()!.instrument?.name}</Item>}
          <Item disabled>Channel: {channel()!.channel}</Item>
          <Item disabled>{channel()!.active ? 'Active' : 'Inactive'}</Item>
        </>
      }
      <Separator />
      {!isChannelPrimary() &&
        <Item onClick={() => audioService().setPrimaryChannel(midiChannel)}>
          Make Channel Primary
        </Item>
      }
      {isChannelPrimary() &&
        <Item disabled>Channel is Primary</Item>
      }
      {channel()?.instrument &&
        <>
          <Submenu label="Options">
            <Item onClick={() => audioService().toggleChannelActive(midiChannel)}>
              Set Channel {channel()?.active ? 'Inactive' : 'Active'}
            </Item>
            <Item onClick={() => {
              audioService().setCurrentChannelToEdit(midiChannel);
              displayService().setDisplay("SET_CHANNEL_VOLUME_MODAL", true);
            }}>
              Set Channel Volume
            </Item>
            <Item onClick={() => {
              audioService().setCurrentChannelToEdit(midiChannel);
              displayService().setDisplay("SET_CHANNEL_PAN_MODAL", true);
            }}>
              Set Channel Pan
            </Item>
            <Separator />
            <Item
              onClick={() => audioService().setChannelVolume(midiChannel, 0)}
              icon={<FaSolidVolumeXmark />}>
              Mute Channel
            </Item>
            <Item
              onClick={() => audioService().setChannelVolume(midiChannel, AUDIO.DEFAULT_CHANNEL_VOLUME)}
              icon={<FaSolidVolumeHigh />}>
              Unmute Channel
            </Item>
            <Separator />
            <Item
              onClick={() => removeInstrumentFromChannel()}
              icon={<FaSolidTrash color="red" />}>
              Remove Instrument
            </Item>
          </Submenu>
          <Item disabled></Item>
        </>
      }
    </Menu >
  </>);
};

const InstrumentDock: Component = () => {
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const i18nService = useService(I18nService);
  const mediaQueryService = useService(MediaQueryService);

  const [displayArrows, setDisplayArrows] = createSignal(false);
  const [channelsToBeDisplayed, setChannelsToBeDisplayed] = createSignal<number[]>([0, 1]);
  const [channelNumbers, setChannelNumbers] = createSignal<number[]>([0, 1]);
  const [activeChannelsDisplayIndex, setActiveChannelsDisplayIndex] = createSignal(0);
  const [dockOpacity, setDockOpacity] = createSignal(1);
  const [instDockOpacityTimeout, setInstDockOpacityTimeout] = createSignal(-1);
  const [animateDockTimeout, setAnimateDockTimeout] = createSignal(-1);

  function onExit() {
    window.clearTimeout(instDockOpacityTimeout());
    window.clearTimeout(animateDockTimeout());
  }

  function onShow() {
    onExit();
    setDockOpacity(1);
    setInstDockOpacityTimeout(window.setTimeout(() => setDockOpacity(DEFAULT_DOCK_OPACITY), 1000 * 3));
  }

  onMount(onShow);
  onCleanup(onExit);

  createEffect(() => {
    setChannelNumbers(audioService().getChannelNumbers());
  });

  createEffect(() => {
    setDisplayArrows(activeDisplayedChannels() != CHANNEL_DISPLAYS.SIXTEEN);
    const _activeDisplayedChannels = activeDisplayedChannels();
    const totalParts = (MIDI.MAX_CHANNEL + 1) / _activeDisplayedChannels;
    let activeIndex = activeChannelsDisplayIndex();
    if (activeIndex < 0) activeIndex = totalParts - 1;
    if (activeIndex >= totalParts) activeIndex = 0;
    setActiveChannelsDisplayIndex(activeIndex);

    const channelsChunk = arrayChunks(channelNumbers(), _activeDisplayedChannels);
    const t = channelsChunk[activeIndex];
    if (t != null) setChannelsToBeDisplayed(t);
  });

  function increaseChannelDisplays(current: CHANNEL_DISPLAYS) {
    switch (current) {
      case CHANNEL_DISPLAYS.FOUR: return CHANNEL_DISPLAYS.EIGHT;
      case CHANNEL_DISPLAYS.EIGHT: return CHANNEL_DISPLAYS.SIXTEEN;
      case CHANNEL_DISPLAYS.SIXTEEN: return CHANNEL_DISPLAYS.FOUR;
    }
  }

  const CommonButtonProps = {
    background: "$primaryAlpha2",
    border: `solid 1px ${TOOL_BUTTONS_BORDER_COLOR} !important`,
    borderRadius: 0,
    height: "25px",
    class: style.prDockButtonBase
  };

  return (<>
    <Transition
      name="fade"
      appear
      onExit={(_, done) => {
        done();
      }}
      onEnter={(_, done) => {
        onShow();
        done();
        clearTimeout(animateDockTimeout());
        setAnimateDockTimeout(window.setTimeout(() => {
          anime({
            targets: Array(16).fill(0).map((_, idx) => document.querySelector(`#instrument-dock-slot-${idx}`)).filter(Boolean),
            keyframes: [
              { scale: 1 },
              { scale: 1.5 },
              { scale: 1 }
            ],
            duration: 500,
            easing: 'easeOutElastic(1, .8)',
            delay: anime.stagger(100),
          });
        }, 3000));
      }}
      onAfterEnter={onShow}
    >
      {(displayService().getDisplay("INSTRUMENT_DOCK")) && <Container
        class="unselectable"
        onmouseenter={() => setDockOpacity(1)}
        onmousemove={() => setDockOpacity(1)}
        onmouseleave={() => setDockOpacity(DEFAULT_DOCK_OPACITY)}
        ondragenter={() => setDockOpacity(1)}
        position={"absolute"}
        left="50%"
        bottom={`${displayService().getDisplay("BOTTOM_BAR_FULLY_SHOWN") ? 40 : 20}px`}
        opacity={dockOpacity()}
        transform={"translateX(-50%)"}
        transition={"opacity 1s, bottom 0.3s, transform 0.3s ease-in-out"}
        maxWidth={500}
        zIndex={5}
        id={HTML_IDS.INSTRUMENT_DOCK_CONTAINER}
        data-test-id={HTML_IDS.INSTRUMENT_DOCK_CONTAINER}
        role="channel-dock-container"
        style={{ "user-select": "none" }}
      >
        <Center>
          <ButtonGroup
            display={"flex"}
            size='sm'
            marginBottom={0}
            height={35}
            width={`100%`}
            justifyContent={"center"}
            class={style.prDockButtonGroups}
          >
            <ButtonGroup size="sm" attached>
              <Button
                __tooltip_title={"Click here to reset the transpose value to default!"}
                ref={(el: HTMLButtonElement) => buttonSFX(el)}
                onMouseDown={() => { audioService().setTranspose(AUDIO.DEFAULT_TRANSPOSE); }}
                {...CommonButtonProps}
              >{i18nService().t_roomPage("common.labels.transpose")}</Button>
              <Input
                borderRadius={0}
                background={"$primaryAlpha2"}
                min={AUDIO.MIN_TRANSPOSE}
                max={AUDIO.MAX_TRANSPOSE}
                onInput={(evt) => {
                  let value = parseInt((evt.target as any).value);
                  if (!Number.isNaN(value)) audioService().setTranspose(clamp(value, AUDIO.MIN_TRANSPOSE, AUDIO.MAX_TRANSPOSE));
                }}
                h={CommonButtonProps.height} minH={0}
                value={audioService().transpose()}
                size="sm" type="number" placeholder="Transpose" />
            </ButtonGroup>

            <ButtonGroup size="sm" attached>
              <Button
                __tooltip_title={"Click here to reset the octave value to default!"}
                ref={(el: HTMLButtonElement) => buttonSFX(el)}
                onMouseDown={() => { audioService().setOctave(AUDIO.DEFAULT_OCTAVE); }}
                {...CommonButtonProps}
              >{i18nService().t_roomPage("common.labels.octave")}</Button>
              <Input
                background={"$primaryAlpha2"}
                borderRadius={0}
                min={AUDIO.MIN_OCTAVE}
                max={AUDIO.MAX_OCTAVE}
                onInput={(evt) => {
                  let value = parseInt((evt.target as any).value);
                  if (!Number.isNaN(value)) audioService().setOctave(clamp(value, AUDIO.MIN_OCTAVE, AUDIO.MAX_OCTAVE));
                }}
                h={CommonButtonProps.height} minH={0}
                value={audioService().octave()} size="sm" type="number" placeholder="Octave" />
            </ButtonGroup>

            <Button
              __tooltip_title={"Click here to change the channel mode!"}
              ref={(el: HTMLButtonElement) => buttonSFX(el)}
              minW={{ "@initial": 35, "@sm": 100 }}
              overflow={"hidden"}
              onMouseDown={() => audioService().incrementSlotMode()}
              fontSize={"xx-small !important"}
              {...CommonButtonProps}
            >
              <Switch>
                <Match when={mediaQueryService().isSmallScreen()}>
                  {
                    i18nService()
                      .t_roomPage("instrumentDock.inputButtons.slotMode", { mode: "" })
                      .replace("Slot", "Channel")
                  }
                  <Box mr={3}>: </Box>{activeChannelsModeToJSON(audioService().slotMode())}
                </Match>
                <Match when={mediaQueryService().isMobileScreen()}>
                  {activeChannelsModeToJSON(audioService().slotMode())}
                </Match>
              </Switch>
            </Button>

            <Button
              __tooltip_title={i18nService().t_roomPageInstDockToolNamesToolTips("ResetToDefault").trim()}
              ref={(el: HTMLButtonElement) => buttonSFX(el)}
              onMouseDown={() => audioService().resetChannelsToDefault()}
              display={{ "@initial": "none", "@sm": "flex" }}
              w={65}
              style={{ "white-space": "pre-wrap" }}
              {...CommonButtonProps}
            > {i18nService().t_roomPageInstDockToolNames("ResetToDefault")}
            </Button>

            <Button
              __tooltip_title={
                i18nService().t_roomPageInstDockToolNamesToolTips("ChannelDisplayMode").trim()
              }
              ref={(el: HTMLButtonElement) => buttonSFX(el)}
              onMouseDown={() => setActiveDisplayedChannels(
                increaseChannelDisplays(activeDisplayedChannels())
              )}
              display={{ "@initial": "none", "@sm": "flex" }}
              {...CommonButtonProps}
              w={65}
              style={{ "white-space": "pre-wrap" }}
              overflow={"hidden"}
            > {i18nService().t_roomPageInstDockToolNames("ChannelDisplayMode")}
            </Button>

            <ButtonGroup
              __tooltip_title={"Click to view the next or previous set of channels!"}
              size="sm" attached>
              <IconButton
                disabled={!displayArrows()}
                ref={(el: HTMLButtonElement) => buttonSFX(el)}
                onMouseDown={() => { setActiveChannelsDisplayIndex(activeChannelsDisplayIndex() - 1); }}
                aria-label="Search"
                display={{ "@initial": "none", "@sm": "flex" }}
                {...CommonButtonProps}
              ><FaSolidArrowLeft /></IconButton>
              <IconButton
                disabled={!displayArrows()}
                ref={(el: HTMLButtonElement) => buttonSFX(el)}
                onMouseDown={() => { setActiveChannelsDisplayIndex(activeChannelsDisplayIndex() + 1); }}
                aria-label="Search"
                {...CommonButtonProps}
              ><FaSolidArrowRight /></IconButton>
            </ButtonGroup>
          </ButtonGroup>
        </Center>

        <SimpleGrid columns={activeDisplayedChannels()}>
          <For each={channelNumbers().filter((_, idx) => channelsToBeDisplayed().includes(idx))}>{(channel) =>
            <InstrumentChannel maxDisplays={activeDisplayedChannels()} midiChannel={channel} />
          }
          </For>
        </SimpleGrid>
      </Container>}
    </Transition >
  </>);
};

export default InstrumentDock;