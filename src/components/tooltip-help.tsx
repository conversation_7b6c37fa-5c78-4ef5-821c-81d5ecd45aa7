import { Box, Icon, IconProps, hope } from "@hope-ui/solid";
import { BsQuestionCircleFill } from 'solid-icons/bs';
import { Component, JSXElement } from "solid-js";

interface ToolTipHelpProps extends IconProps {
  tooltipLabel: JSXElement;
  tooltipSize?: string | number;
  tooltipPlacement?: string;
}

const ToolTipHelp_: Component<ToolTipHelpProps> = (props) => {
  return (<>
    <Box
      __tooltip_title={props.tooltipLabel}
      __tooltip_placement={props.__tooltip_placement ?? props.tooltipPlacement ?? "right"}
      __tooltip_open_delay={250}
    >
      {/* @ts-ignore */}
      <Icon font-size="10px" {...props} size={props.tooltipSize || "10px"} cursor={"help"} as={BsQuestionCircleFill}></Icon>
    </Box>
  </>);
};

const ToolTipHelp = hope(ToolTipHelp_);

export default ToolTipHelp;