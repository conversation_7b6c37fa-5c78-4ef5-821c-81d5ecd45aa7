import { Box, Button, Center, Flex, Heading, List, VStack, useColorMode } from "@hope-ui/solid";
import { ReactiveMap } from "@solid-primitives/map";
import clsx from "clsx";
import { FaSolidAngleDown, FaSolidAngleRight } from "solid-icons/fa";
import { ErrorBoundary, For, JSX, JSXElement, Show, Suspense, children, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import tinycolor from "tinycolor2";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import { Roles } from "~/proto/user-renditions";
import AppThemesService from "~/services/app.themes.service";
import { getRootUITheme } from "~/util/helpers.dom";
import { MenuContentDivider } from "./modals/settings-content/common.content";
import MotionFadeIn from "./motion/motion.fade-in";

export function SettingsMenu<Headers extends string, Labels extends string>(props: SettingsMenuProps<Headers, Labels>) {
  const themesService = useService(AppThemesService);

  const [activeSetting, setActiveSetting] = createSignal<ActiveSetting<Headers, Labels>>();
  const [activeContent, setActiveContent] = createSignal<JSX.Element>();
  const [menuBackgroundColor, setMenuBackgroundColor] = createSignal<string>("transparent");
  const [menuTextColor, setMenuTextColor] = createSignal<string>("$neutral12");
  const [hasContent, setHasContent] = createSignal<boolean>(false);
  const [textGroups, setTextGroups] = createSignal<SettingsSubTextGroups<Headers, Labels>[]>(props.textGroups);
  const [resetActiveSetting, setResetActiveSetting] = createSignal<() => void>();
  const { colorMode } = useColorMode();
  const allChildren = new ReactiveMap<string, () => JSX.Element>();

  const getID = (header: Headers, label: Labels, childLabel?: Labels) => `${header}-${label}${childLabel ? `-${childLabel}` : ""}`;

  onMount(() => {
    let _textGroups = props.textGroups;
    setTextGroups(_textGroups);

    _textGroups.forEach(text => {
      text.texts.forEach((t) => {
        //@ts-ignore
        if (t.content) allChildren.set(getID(text.header || "" as Headers, t.label), () => children(() => t.content));

        if (t.children) {
          t.children.forEach((c) => {
            //@ts-ignore
            if (c.content) allChildren.set(getID(text.header || "" as Headers, t.label, c.label), () => children(() => c.content));

            if (c.children) {
              c.children.forEach((cc) => {
                //@ts-ignore
                if (cc.content) allChildren.set(getID(`${text.header || ""}-${t.label}` as Headers, c.label, cc.label), () => children(() => cc.content));
              });
            }
          });
        }
      });
    });

    try {
      let firstElement = _textGroups[0];
      let firstHeader = firstElement?.header;
      let firstLabel = firstElement?.texts?.[0]?.label;

      if (firstHeader && firstLabel) {
        setActiveSetting({ group: firstHeader, label: firstLabel });
        let getContent = allChildren.get(getID(firstHeader, firstLabel));

        if (getContent) {
          setHasContent(true);
          setActiveContent(() => getContent as any);
        }

        setResetActiveSetting(() => {
          return () => {
            if (firstHeader && firstLabel) setActiveSetting({ group: firstHeader, label: firstLabel });
            if (getContent) { setActiveContent(() => getContent as any); }
          };
        });
      }
    } catch { }
  });

  onCleanup(() => {
    allChildren.clear();
  });

  createEffect(() => {
    let setting = activeSetting();

    if (setting) {
      let getContent = allChildren.get(getID(setting.group, setting.label, setting.childLabel));
      if (!getContent) {
        try {
          // Find first child content
          let firstChild = textGroups().find(t => t.header == setting.group)?.texts.find(t => t.label == setting.label)?.children?.[0];
          if (firstChild) {
            setting.childLabel = firstChild.label;
            getContent = allChildren.get(getID(setting.group, setting.label, setting.childLabel));
            setActiveSetting({ group: setting.group, label: setting.label, childLabel: firstChild.label });
          }
        } catch { }
      }

      setHasContent(getContent != null);
      // @ts-ignore
      if (getContent) setActiveContent(() => getContent);
      if (props.onActiveSetting) props.onActiveSetting(setting);
    }
  });

  createEffect(() => {
    //Note: this is just to capture any theme changes
    themesService().themeColors.accent;
    let root = getRootUITheme();
    let bgColor = root?.style.getPropertyValue(`--hope-colors-${menuBackgroundColor().replace("$", "")}`);
    let tinyBgColor = tinycolor(bgColor);
    setMenuTextColor(tinyBgColor.isLight() ? "$neutral1" : "$neutral12");
    setMenuBackgroundColor(`${colorMode() == "dark" ? "$accent10" : "$accent1"}`);
  });

  const MenuButtonDisplay = (props: {
    header: Headers,
    menuProps: SettingsMenuProps<Headers, Labels>,
    subText: SettingsSubText<Labels>;
    childLabel?: string;
  }) => {
    let { header, menuProps, subText, childLabel } = props;

    let isParentButtonActive = () => {
      return (getID(header, subText.label) == getID(activeSetting()?.group!, activeSetting()?.label!)) ||
        subText.children &&
        subText.children.some(c =>
          getID(header, subText.label, c.label) == getID(activeSetting()?.group!, activeSetting()?.label!)) || false;
    };

    let fontColor = () => subText.color ?? isParentButtonActive() ? menuTextColor() : "$neutral12";

    return <Box w="100%">
      <Button
        leftIcon={subText.icon}
        _hover={{ "background": `${colorMode() == "dark" ? "$accent1" : "$accent5"}  !important` }}
        background={isParentButtonActive() ? menuBackgroundColor() : "transparent"}
        onMouseDown={(evt: MouseEvent) => {
          if (evt.button == 0) {
            //@ts-ignore
            setActiveSetting({ group: header, label: subText.label, childLabel });
            if (menuProps.onLabelClick) menuProps.onLabelClick(subText.label);
            if (subText.onClick) subText.onClick({ resetSettingToDefault: resetActiveSetting });
          }
        }}
        ref={(el: HTMLElement) => buttonSFX(el)}
        width={"100%"}
        display="flex"
        justifyContent={"flex-start"}
        padding={0}
        paddingLeft={5}
        height={30}
        color={fontColor()}
      >
        {subText.label}
        {subText.children &&
          <Center marginLeft={2} fontSize={12} color={fontColor()}>
            <Show when={isParentButtonActive()} fallback={<FaSolidAngleRight />}>
              <FaSolidAngleDown />
            </Show>
          </Center>}
      </Button>

      {/* Sub Menu Buttons */}
      <Show when={isParentButtonActive() && subText.children}>
        <MenuChildButtons
          header={header}
          menuProps={menuProps}
          subText={subText}
        />
      </Show>
    </Box>;
  };

  const MenuChildButtons = (props: { header?: string, menuProps: SettingsMenuProps<Headers, Labels>, subText: SettingsSubText<Labels>; }) => {
    let { header, subText, menuProps } = props;

    return <VStack spacing={"$0_5"} padding={5}>
      <For each={subText.children}>{(child) => {
        let isChildActive = () => {
          return activeSetting()?.childLabel == child.label;
        };

        return <Show
          when={!child.children || child.children?.length == 0}
          fallback={
            <MenuButtonDisplay
              header={`${header}-${subText.label}` as any as Headers}
              menuProps={menuProps}
              subText={child}
              childLabel={child.label}
            />}
        >
          <Button
            leftIcon={child.icon}
            _hover={{ "background": `${colorMode() == "dark" ? "$accent1" : "$accent5"}  !important` }}
            background={isChildActive() ? menuBackgroundColor() : "transparent"}
            onMouseDown={(evt: MouseEvent) => {
              if (evt.button == 0) {
                // @ts-ignore
                setActiveSetting({ group: header, label: subText.label, childLabel: child.label });
                menuProps.onLabelClick?.(child.label);
              }
            }}
            ref={(el: HTMLElement) => buttonSFX(el)}
            width={"100%"}
            display="flex"
            justifyContent={"flex-start"}
            padding={0}
            paddingLeft={5}
            height={30}
            color={child.color ?? isChildActive() ? menuTextColor() : "$neutral12"}
          >
            {child.label}
          </Button>
        </Show>;
      }}
      </For>
    </VStack>;
  };

  return (<>
    <Flex
      class={clsx([props.disabled && "disabled"])}
      margin={0}
    >
      {/* Left Menu */}
      <Box
        width={"210px"}
        borderRight={"solid 1px gray"}
        paddingTop={10} paddingBottom={5}
        overflowX="hidden" overflowY={"scroll"}
      >
        <List spacing="$2">
          <For each={textGroups()}>{(input) => <>
            {input.addDivider && <MenuContentDivider marginBoth={0} />}

            {/* Menu Headers */}
            {input.header && <Heading
              textTransform="uppercase"
              color={"$neutral11"}
              fontSize={"10px !important"}>
              {input.header}
            </Heading>}

            {/* Menu Buttons */}
            <List spacing="$1">
              <For each={input.texts}>{(subText) => {
                return <MenuButtonDisplay header={input.header || "" as Headers} menuProps={props} subText={subText} />;
              }}
              </For>
            </List>
          </>}
          </For>
        </List>
      </Box>

      {/* Body Content */}
      <Box
        position="relative"
        padding={props.bodyPadding}
        paddingRight={props.bodyPaddingRight}
        marginLeft={props.bodyMarginLeft || 28}
        width="100%" height="100%"
        overflowY={"auto"}
        overflowX="hidden"
      >
        <Show
          when={hasContent()}
          fallback={<Box>No content available, yet.</Box>}
        >
          <ErrorBoundary fallback={<Box>⛔ Failed to load to content. ⛔</Box>}>
            <Suspense fallback={<Box>Loading...</Box>}>
              {/* @ts-ignore */}
              {activeContent}
            </Suspense>
          </ErrorBoundary>
        </Show>
      </Box>
    </Flex>
  </>);
}

export type SettingsSubTextGroups<Headers, Labels> = {
  header?: Headers;
  addDivider?: boolean;
  texts: SettingsSubText<Labels>[];
};

export type ActiveSetting<Headers, Labels> = { group: Headers; label: Labels; childLabel?: Labels; };

export type SettingsMenuProps<Headers, Labels> = {
  bodyMarginLeft?: string | number;
  bodyPadding?: string | number;
  bodyPaddingRight?: string | number;
  textGroups: SettingsSubTextGroups<Headers, Labels>[];
  disabled?: boolean;
  onLabelClick?(label: string): void;
  onChildLabelClick?(label: string): void;
  onActiveSetting?(input: ActiveSetting<Headers, Labels>): void;
};

export type SettingsSubText<T> = {
  label: T;
  icon?: JSXElement;
  content?: any;
  // children?: { label: string, content: JSXElement | (() => JSXElement); }[];
  children?: SettingsSubText<T>[];
  skipTranslate?: boolean;
  requireRole?: Roles[];
  isNew?: boolean;
  color?: string;
  onClick?(input: { resetSettingToDefault: () => void; }): void;
};

