import { Box, <PERSON>Button, Divider, VStack } from "@hope-ui/solid";
import interact from "interactjs";
import { JSX, ParentComponent, createSignal, onCleanup, onMount } from "solid-js";
import { generateUUID } from "~/util/helpers";

type BasicWindowProps = {
  onClose: () => void;
  title?: string | JSX.Element;
  titleIcon?: JSX.Element;
  footer?: JSX.Element;
  content?: string | JSX.Element;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number | string;
  maxHeight?: number | string;
  width?: number;
  class?: string;
  height?: number | string;
  border?: string;
  initPosition?: { x?: number | string, y?: number | string; };
  ref?: ((e: HTMLElement) => void);
  onScroll?: any;
  overflowY?: any;
};

const BasicWindow: ParentComponent<BasicWindowProps> = (props) => {
  const id = generateUUID();
  const position = { x: 0, y: 0 };
  const PADDING = 10;
  let onHandleContainerHeightTimeout = -1;
  let containerElement!: HTMLDivElement;
  const minWidth = props.minWidth ?? 100;
  const minHeight = props.minHeight ?? 100;
  const [containerInteractable, setContainerInteractable] = createSignal<Interact.Interactable>();

  const onHandleContainerHeight = () => {
    window.clearTimeout(onHandleContainerHeightTimeout);
    onHandleContainerHeightTimeout = window.setTimeout(() => {
      let rect = containerElement.getClientRects().item(0);
      if (!rect) return;

      let root = document.documentElement;
      let bottomBarHeight = parseFloat(root.style.getPropertyValue("--bottomBarHeight") ?? "0px");

      if (rect.bottom >= window.innerHeight - bottomBarHeight) {
        position.y -= rect.height;
        if (position.y < 0) position.y = 0;
        containerElement.style.transform = `translate(${position.x}px, ${position.y}px)`;
      }

    }, 100);
  };

  const onElementRef = (element: HTMLElement) => {
    const updatePosition = (dx: number, dy: number, containerElement: HTMLElement) => {
      position.x += dx;
      position.y += dy;
      containerElement.style.transform = `translate(${position.x}px, ${position.y}px)`;
    };

    let output = interact(element)
      .draggable({
        listeners: {
          move(event) {
            let parent = (event.target.parentElement as HTMLElement);
            if (!parent) return;

            let containerParent = parent.getClientRects().item(0);
            if (!containerParent) return;

            let dx = event.dx;
            let dy = event.dy;

            // Prevent container from going beyond the top of the window
            if (containerParent.top <= 0 + PADDING && dy < 0) {
              dy = 0;
            }

            // Prevent container from going beyond the bottom of the window
            let root = document.documentElement;
            let bottomBarHeight = parseFloat(root.style.getPropertyValue("--bottomBarHeight") ?? "0px");

            if (containerParent.bottom >= window.innerHeight - bottomBarHeight - PADDING && dy > 0) {
              dy = 0;
            }

            // Prevent container from going beyond the right of the window
            if (containerParent.right >= window.innerWidth - PADDING && dx > 0) {
              dx = 0;
            }

            // Prevent container from going beyond the left of the window
            let sideBarWidth = parseFloat(root.style.getPropertyValue("--sidebarListWidth") ?? "0px");

            if (containerParent.left <= sideBarWidth + PADDING && dx < 0) {
              dx = 0;
            }

            updatePosition(dx, dy, containerElement);
          },
        }
      });

    setContainerInteractable(output);
  };

  onMount(() => {
    onHandleContainerHeight();

    interact(containerElement)
      .resizable({
        // resize from all edges and corners
        edges: { left: true, right: true, bottom: true, top: false },
        modifiers: [
          // keep the edges inside the parent
          interact.modifiers.restrictEdges({
            outer: 'parent'
          }),

          // minimum size
          interact.modifiers.restrictSize({
            min: { width: minWidth, height: minHeight },
            max: { width: parseFloat(props.maxWidth?.toString() ?? "1000"), height: parseFloat(props.maxHeight?.toString() ?? "1000") }
          })
        ],

        inertia: true,
        listeners: {
          move(event) {
            let { x, y } = event.target.dataset;

            x = (parseFloat(x) || 0) + event.deltaRect.left;
            y = (parseFloat(y) || 0) + event.deltaRect.top;

            Object.assign(event.target.style, {
              width: `${event.rect.width}px`,
              height: `${event.rect.height}px`,
              // transform: `translate(${x}px, ${y}px)`
            });

            Object.assign(event.target.dataset, { x, y });
          }
        },
      });
  });

  onCleanup(() => {
    window.clearTimeout(onHandleContainerHeightTimeout);
    containerInteractable()?.unset();
  });

  return <>
    <Box
      id={`basic-window-${id}`}
      class={props.class}
      minW={minWidth}
      minH={minHeight}
      h={props.height}
      w={props.width}
      position={"absolute"}
      top={props.initPosition?.y || "5%"}
      left={props.initPosition?.x ?? `calc(50% - (${props.width || minWidth}px / 2))`}
      background={"$primaryAlpha2"}
      borderRadius={5}
      border={props.border}
      onScroll={props?.onScroll}
      ref={(elem: HTMLDivElement) => {
        if (!elem) return;
        containerElement = elem as HTMLDivElement;
        props.ref?.(elem);
      }}
      overflow={"hidden"}
    >
      <VStack w="100%" h="100%">
        <VStack w="100%" padding={PADDING} ref={onElementRef} position={"relative"}>
          <CloseButton
            __tooltip_title="Close"
            onClick={props.onClose}
            position={"absolute"} right={15} top={4} fontSize={10}
          />

          {props.title && <Box userSelect={"none"}>{props.title}</Box>}
        </VStack>
        <Divider
          boxShadow={"0px 1px 1px 1px rgba(0,0,0,0.1)"}
          color={"rgba(255,255,255,0.2) !important"} w="100%"
        />
        {props.children}
        {props.footer &&
          <Box w="100%" h="100%" flex={1}>
            <Divider
              boxShadow={"0px 1px 1px 1px rgba(0,0,0,0.1)"}
              color={"rgba(255,255,255,0.2) !important"} w="100%"
              marginBottom={10}
            />
            {props.footer}
          </Box>
        }
      </VStack>

    </Box>
  </>;
};

export default BasicWindow;