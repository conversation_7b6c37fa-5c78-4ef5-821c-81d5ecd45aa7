import { Box, VStack, HStack, Text } from "@hope-ui/solid";
import { ParentComponent, createSignal, onMount, onCleanup, For, Show } from "solid-js";
import { useService } from "solid-services";
import { AppMidiSequencerEventType } from "~/proto/pianorhythm-app-renditions";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import MidiPlayerService from "~/services/midi-player.service";
import { P, match } from "ts-pattern";
import css from "./sass/midi-player-ui.module.sass";

const MidiLyricsDisplay: ParentComponent<{ lyrics: string[]; }> = (props) => {
  const appService = useService(AppService);
  const audioService = useService(AudioService);
  const midiPlayerService = useService(MidiPlayerService);

  const [processedLyrics, setProcessedLyrics] = createSignal<string[]>([]);
  const [midiSequencerEventSub, setMidiSequencerEventSub] = createSignal<VoidFunction>();

  onMount(() => {
    let sub =
      midiPlayerService().appMidiSequencerEvents.listen((event) => {
        match(event)
          .with({ eventType: AppMidiSequencerEventType.FILE_OUTPUT },
            () => {
              setProcessedLyrics([]);
            })
          .with({ eventType: AppMidiSequencerEventType.SEEK_POSITION_CHANGED },
            () => {
              setProcessedLyrics([]);
            })
          .with({ eventType: AppMidiSequencerEventType.STOPPED },
            () => {
              setProcessedLyrics([]);
            })
          .with({ eventType: AppMidiSequencerEventType.FINISHED },
            () => {
              setProcessedLyrics([]);
            })
          .with({ eventType: AppMidiSequencerEventType.LYRIC, dataStr: P.string, index: P.number },
            ({ dataStr: lyric, index }) => {
              let target = `pr_${index}_idx_${lyric}`.trim().replaceAll("\n", "").replaceAll("\r", "").replaceAll("\t", "");
              setProcessedLyrics(v => [...v, target]);
            })
          .otherwise(() => { });
      });
    setMidiSequencerEventSub(() => sub);
  });

  onCleanup(() => {
    midiSequencerEventSub()?.();
  });

  const removeIndexInLyricWord = (input: string): string => {
    return input.replace(/pr_\d+_idx_/g, '');
  };

  return (<>
    <Box className={css.lyrics_container}>
      <For each={props.lyrics.join("").split("\r")}>
        {(line) =>
          <VStack alignItems={"self-start"} spacing={"$5"} flexWrap={"wrap"}>
            <HStack
              justifyContent={"center"}
              w="100%" flexWrap={"wrap"} alignItems={"self-start"}
            >
              <For each={line.split(/(?=pr_\d+_idx_)(?<=\W?)/)}>
                {(word) => {
                  let processedWord = word.trim().replaceAll("\n", "").replaceAll("\r", "");
                  let targetWord = removeIndexInLyricWord(word);

                  return <Text as="pre"
                    class={processedLyrics().includes(processedWord) ? css.lyric_animated : css.lyric}
                    onAnimationEnd={(elem: any) => {
                      let target = elem.target as HTMLElement;
                      if (midiPlayerService().midiSequencerState.autoScroll === false) return;

                      if (elem.target.scrollIntoViewIfNeeded) {
                        // return elem.target.scrollIntoViewIfNeeded();
                      }

                      // target.scrollIntoView({ behavior: "smooth", block: "start" });
                      target.scrollIntoView({ behavior: "smooth", block: "nearest", inline: "nearest" });
                    }}
                  >
                    {targetWord}
                  </Text>;
                }}
              </For>
            </HStack>
            <Show when={line.includes("\n")}><Box></Box></Show>
          </VStack>
        }
      </For>
    </Box>
  </>);
};

export default MidiLyricsDisplay;