import { <PERSON>, HStack, <PERSON><PERSON><PERSON>utton, Progress, ProgressIndicator, Text } from "@hope-ui/solid";
import { BsQuestionCircleFill } from "solid-icons/bs";
import { FaSolidB, FaSolidBullhorn, FaSolidClockRotateLeft, FaSolidForward, FaSolidM, FaSolidP, FaSolidPause, FaSolidPlay, FaSolidScroll, FaSolidStop, FaSolidToggleOff, FaSolidToggleOn } from "solid-icons/fa";
import { createEffect, createSignal, onCleanup, onMount, ParentComponent, Show } from "solid-js";
import { useService } from "solid-services";
import { match, P } from "ts-pattern";
import { AppStateActions, AppStateActions_Action } from "~/proto/pianorhythm-actions";
import { AppMidiSequencerEventType, AppVPSequencerFileLoad } from "~/proto/pianorhythm-app-renditions";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import MidiPlayerService from "~/services/midi-player.service";
import MonitorTrackingService, { TRACKING_EVENT_IDS } from "~/services/monitor-tracking.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { MidiPlayerState } from "~/types/midi-player.types";
import SwalPR from "~/util/sweetalert";
import css from "./sass/midi-player-ui.module.sass";

const BROADCAST_NOTES_LABEL = "<b>Broadcast Notes</b> <br/><br/>If enabled, other users will be able to hear the notes.";

const CommonUITooltip: ParentComponent<{ label?: string; }> = (props) => {
  return (<Box
    __tooltip_show_arrow={false}
    __tooltip_title={<Box innerHTML={props.label}></Box>
    }>{props.children}</Box>);
};

const VPSheetSequencerControlsUI: ParentComponent<{ data?: string; input?: AppVPSequencerFileLoad; }> = (props) => {
  const appService = useService(AppService);
  const sfxService = useService(SoundEffectsService);
  const analyticsService = useService(MonitorTrackingService);
  const displayService = useService(DisplaysService);
  const midiPlayerService = useService(MidiPlayerService);
  const appSettingsService = useService(AppSettingsService);

  const [midiSequencerEventSub, setMidiSequencerEventSub] = createSignal<VoidFunction>();
  const [currentTime, setCurrentTime] = createSignal<number>();
  const [targetBPM, setTargetBPM] = createSignal<number>();
  const [broadcastNotes, setBroadcastNotes] = createSignal<boolean>(false);
  const [targetBPMModalOpened, setTargetBPMModalOpened] = createSignal<boolean>(false);
  const [currentTimeFormatted, setCurrentTimeFormatted] = createSignal<string>("00:00:00");

  const onBroadcastNotes = () => {
    appService().coreService()?.send_app_action(AppStateActions.create({
      action: !broadcastNotes() ? AppStateActions_Action.MidiSequencerEnablePreviewOnly : AppStateActions_Action.MidiSequencerDisablePreviewOnly,
    }), true);
  };

  const onLoad = () => {
    if (appSettingsService().isDebugMode()) console.log("VP Sequencer State: ", midiPlayerService().midiSequencerState);
    setTargetBPM(midiPlayerService().midiSequencerState.bpm || midiPlayerService().DEFAULT_BPM);
    onBroadcastNotes();
  };

  const isStopped = () => midiPlayerService().midiSequencerState.playerState == MidiPlayerState.NotLoaded
    || midiPlayerService().midiSequencerState.playerState == MidiPlayerState.Stopped;

  const onStopped = (finished = false) => {
    midiPlayerService().setMidiSequencerState("playerState", finished ? MidiPlayerState.NotLoaded : MidiPlayerState.Stopped);
    setCurrentTime(0);
    midiPlayerService().setTotalTime(0);
  };

  createEffect(() => {
    midiPlayerService().onUpdateTotalTime(midiPlayerService().midiSequencerState.totalTime);
  });

  createEffect(() => {
    let time = midiPlayerService().formatTime(currentTime() || 0, true);
    if (time) setCurrentTimeFormatted(time);
  });

  createEffect(() => {
    if (midiPlayerService().midiSequencerState.playerState == MidiPlayerState.NotLoaded) {
      return;
    }

    appService().coreService()?.send_app_action(AppStateActions.create({
      action: midiPlayerService().midiSequencerState.enableSustain ? AppStateActions_Action.VPSequencerEnableSustain :
        AppStateActions_Action.VPSequencerDisableSustain,
    }), true);
  });

  onMount(() => {
    setTargetBPM(midiPlayerService().midiSequencerState.bpm || props.input?.tracks?.[0]?.tempo || midiPlayerService().DEFAULT_BPM);

    if (midiPlayerService().loadedMidiFileName()) {
      onLoad();
    }

    midiPlayerService().setMidiSequencerState("broadcastNotes", broadcastNotes());

    let sub =
      midiPlayerService().appMidiSequencerEvents.listen((event) => {
        match(event)
          .with({ eventType: AppMidiSequencerEventType.CURRENT_TIME, dataFloat: P.number },
            ({ dataFloat: currentTime }) => {
              setCurrentTime(currentTime);
            })
          .with({ eventType: AppMidiSequencerEventType.STOPPED },
            () => { onStopped(); })
          .with({ eventType: AppMidiSequencerEventType.FINISHED },
            () => { onStopped(true); })
          .with({ eventType: AppMidiSequencerEventType.FILE_OUTPUT },
            () => {
              onLoad();
              analyticsService().trackEvent(TRACKING_EVENT_IDS.VP_SEQUENCER_PLAYED, event.fileName ?? "Unknown");
            })
          .otherwise(() => { });
      });

    setMidiSequencerEventSub(() => sub);
  });

  onCleanup(() => {
    midiSequencerEventSub()?.();
  });

  return (<>
    <HStack
      justifyContent={"center"}
      id="vp-sequencer-controls-header"
      marginBottom={5}
      spacing={"$2"}
    >
      <CommonUITooltip label="Rewind (10s)">
        <IconButton
          disabled={isStopped()}
          onclick={() => {
            appService().coreService()?.send_app_action(AppStateActions.create({
              action: AppStateActions_Action.MidiSequencerRewind,
            }), true);
          }}
          size="xs" aria-label="Rewind (10s)" icon={<FaSolidClockRotateLeft />} />
      </CommonUITooltip>

      <CommonUITooltip label="Play" >
        <Show
          when={midiPlayerService().midiSequencerState.playerState != MidiPlayerState.Playing}
          fallback={<IconButton
            onclick={() => {
              appService().coreService()?.send_app_action(AppStateActions.create({
                action: AppStateActions_Action.MidiSequencerPause,
              }), true);
              midiPlayerService().setMidiSequencerState("playerState", MidiPlayerState.Paused);
            }}
            size="xs" aria-label="Pause" icon={<FaSolidPause />} />
          }
        >
          <IconButton
            onclick={() => {
              appService().coreService()?.send_app_action(AppStateActions.create({
                action:
                  isStopped() ?
                    AppStateActions_Action.VPSequencerLoadData :
                    AppStateActions_Action.MidiSequencerResume,
                stringValue: props.data,
                vpFileLoad: props.input
              }), true);
              midiPlayerService().setMidiSequencerState("playerState", MidiPlayerState.Playing);
            }}
            size="xs" aria-label="Play VP" icon={<FaSolidPlay />}
          />
        </Show>
      </CommonUITooltip>

      <CommonUITooltip label="Stop">
        <IconButton
          disabled={isStopped()}
          onclick={() => {
            appService().coreService()?.send_app_action(AppStateActions.create({
              action: AppStateActions_Action.MidiSequencerStop,
            }), true);
            onStopped();
          }}
          size="xs" aria-label="Stop" icon={<FaSolidStop />} />
      </CommonUITooltip>

      <CommonUITooltip label="Forward">
        <IconButton
          disabled={isStopped()}
          onclick={() => {
            appService().coreService()?.send_app_action(AppStateActions.create({
              action: AppStateActions_Action.MidiSequencerForward,
            }), true);
          }}
          size="xs" aria-label="Forward" icon={<FaSolidForward />} />
      </CommonUITooltip>

      <CommonUITooltip label="Auto Scroll">
        <IconButton
          color={midiPlayerService().midiSequencerState.autoScroll ? "$accent1" : "$neutral10"}
          onclick={() => { midiPlayerService().setMidiSequencerState("autoScroll", !midiPlayerService().midiSequencerState.autoScroll); }}
          size="xs" aria-label="Auto Scroll" icon={<FaSolidScroll />} />
      </CommonUITooltip>

      <CommonUITooltip label={BROADCAST_NOTES_LABEL}>
        <IconButton
          color={broadcastNotes() ? "$accent1" : "$neutral10"}
          onclick={() => {
            setBroadcastNotes(v => !v);
            onBroadcastNotes();
          }}
          size="xs" aria-label="Broadcast Notes" icon={<FaSolidBullhorn />} />
      </CommonUITooltip>

      <CommonUITooltip label="Toggle Sustain">
        <IconButton
          color={midiPlayerService().midiSequencerState.enableSustain ? "$accent1" : "$neutral10"}
          onclick={() => {
            midiPlayerService().setMidiSequencerState("enableSustain", !midiPlayerService().midiSequencerState.enableSustain);
          }}
          size="xs" aria-label="Toggle Sustain"
          icon={midiPlayerService().midiSequencerState.enableSustain ? <FaSolidToggleOn /> : <FaSolidToggleOff />}
        />
      </CommonUITooltip>

      <CommonUITooltip label={`Change Tempo - Current: ${targetBPM()}`}>
        <IconButton
          disabled={midiPlayerService().midiSequencerState.playerState == MidiPlayerState.NotLoaded}
          display={targetBPMModalOpened() ? "none" : "block"}
          onclick={() => {
            SwalPR(sfxService).fire({
              title: `Change Tempo`,
              input: "number",
              inputValue: targetBPM(),
              inputPlaceholder: "Sheet Tempo",
              inputAttributes: {
                min: "1",
                max: "1000",
              },
              showCancelButton: true,
              showConfirmButton: true,
              allowEscapeKey: true,
              didOpen: () => { setTargetBPMModalOpened(true); },
              didClose: () => { setTargetBPMModalOpened(false); }
            }).then((res) => {
              if (res.isConfirmed) {
                let value = parseInt(res.value as string);
                appService().coreService()?.send_app_action(AppStateActions.create({
                  action: AppStateActions_Action.VPSequencerSetBPM,
                  uint32Value: value
                }), true);
                setTargetBPM(value);
                midiPlayerService().setMidiSequencerState("bpm", value);
              }
            });
          }}
          size="xs"
          fontSize={10}
          width={"38px !important"}
          aria-label="Change Tempo"
          icon={<HStack><FaSolidB /><FaSolidP /><FaSolidM /></HStack>}
        />
      </CommonUITooltip>

      <HStack spacing={"$1"}>
        <Text fontSize={9} color="$neutral12">{currentTimeFormatted()}</Text>
        <Text fontSize={9} color="$neutral12">/</Text>
        <Text fontSize={9} color="$neutral12">{midiPlayerService().totalTimeFormatted()}</Text>
      </HStack>

      <Box position={"absolute"} right={10}>
        <CommonUITooltip label={"Virtual Piano Player Help"}>
          <Box className={css.common_button}
            onClick={() => { displayService().setSidebarDocsPath(`/guides/midi-player/vp-sequencer/`); }}>
            <BsQuestionCircleFill />
          </Box>
        </CommonUITooltip>
      </Box>
    </HStack>

    <Progress
      size="xs"
      value={((currentTime() ?? 0) / ((midiPlayerService().totalTime() ?? 1) / 1000)) * 100}
      trackColor="$primary1"
    >
      <ProgressIndicator color="$accent1" />
    </Progress>
  </>);
};

export default VPSheetSequencerControlsUI;