.parent_container
  --border-top-color: transparent
  z-index: 2
  width: 350px
  background: var(--hope-colors-primaryAlpha2)
  position: absolute
  top: 30px
  right: 175px
  border: 1px solid var(--hope-colors-neutral12)
  border-radius: 5px
  max-height: 450px
  overflow-x: hidden
  border-top: 3px solid var(--hope-colors-neutral12)
  transition: border 100ms ease
  padding-left: 5px

.parent_container::-webkit-scrollbar
  width: 14px

.parent_container::-webkit-scrollbar-thumb
  border: 4px solid rgba(0, 0, 0, 0)
  background-clip: padding-box
  border-radius: 9999px

.dragBar
  width: 100%
  height: 15px
  border-radius: 5px

.main_container
  width: 100%
  height: 110px
  color: var(--hope-colors-neutral12)
  position: relative

.meta_container
  width: 100%
  padding: 10px

.image_section
  width: 40%

.common_button
  user-select: none
  cursor: pointer
  transition: transform 50ms ease, color 50ms

  &:hover
    transform: scale(1.05)
    color: var(--hope-colors-accent1)
    svg
      color: var(--hope-colors-accent1) !important

  &:active
    transform: scaleX(1.1)

.controls_section
  width: 100%
  height: 100%
  padding: 5px
  padding-top: 0px

  .title
    margin-top: -3px
    margin-bottom: 5px
    text-align: center
    user-select: none
    width: 100%
    font-weight: bold
    min-width: 0
    white-space: nowrap
    overflow: hidden
    text-overflow: ellipsis

  .controls
    font-size: 20px
    margin-top: -15px
    margin-left: 15px

    .ctrl_button
      @extend .common_button
      color: var(--hope-colors-neutral11)

    .play_button
      @extend .ctrl_button
      color: var(--hope-colors-neutral12)
      font-size: larger

.options_buttons_container
  padding-top: 15px
  position: absolute
  bottom: 5px
  left: 5px

.channel_button
  @extend .common_button

.lyrics_container
  width: 100%
  height: 100%
  padding: 10px
  overflow-y: scroll
  overflow-wrap: break-word
  overflow-x: hidden

.tracks_container
  @extend .lyrics_container

.program_changes_container
  @extend .lyrics_container

.track
  white-space: normal

.lyric
  color: lightgray

.lyric_animated
  animation: pop 0.2s
  animation-fill-mode: forwards
  transition: color 0.2s ease, transform 0.2s ease

@keyframes pop
  0%
    transform: scale(1)
  20%
    transform: scale(0.9)
  70%
    transform: scale(1.1)
    color: var(--hope-colors-accent10)
  100%
    transform: scale(1)
    color: var(--hope-colors-accent1)
    font-weight: bolder

#trackSliderBar
  border-radius: 15px
  margin: 0
