import { Box, Divider, HStack, Icon, Image, Spinner, VStack } from '@hope-ui/solid';
import clsx from 'clsx';
import { orderBy, uniqBy } from 'lodash-es';
import { FaSolidEyeLowVision, FaSolidLock } from 'solid-icons/fa';
import { Component, createEffect, createMemo, createResource, createSignal, For, JSXElement, lazy, Match, onCleanup, Suspense, Switch } from 'solid-js';
import { useService } from 'solid-services';
import { BasicRoomDto, RoomStatus, roomTypeFromJSON } from '~/proto/room-renditions';
import style from '~/sass/sidebar.module.sass';
import { getCountryFlagImage } from '~/server/general.api';
import AppService from '~/services/app.service';
import EmojifyService from '~/services/emojify.service';
import RoomsService from '~/services/rooms.service';
import { SelfHostingService } from '~/services/selfhosting.service';
import SoundEffectsService from '~/services/sound-effects.service';
import WebsocketService from '~/services/websocket.service';
import { RoomModes, RoomSortOptions, RoomStagesNS, RoomTypeHelper } from '~/types/room.types';
import { tryDecodeURI } from '~/util/helpers.dom';

const RoomMiniProfileCard = lazy(() => import('./room-mini-profile-card'));

const [onJoinRoomClickTimeout, setOnJoinRoomClickTimeout] = createSignal(-1);
const [onJoinRoomClick, setOnJoinRoomClick] = createSignal<string>();

const RoomItem: Component<{ item: BasicRoomDto; }> = (props) => {
  const appService = useService(AppService);
  const emojifyService = useService(EmojifyService);
  const websocketService = useService(WebsocketService);
  const sfxService = useService(SoundEffectsService);
  const selfHostingService = useService(SelfHostingService);

  let trackedRoom: BasicRoomDto = props.item;
  const isClientInRoom = createMemo(() => appService().isClientInRoom(trackedRoom.roomID || ""));
  const isRoomPrivate = () => appService().roomSettings()?.RoomStatus == RoomStatus.Private;
  const [selfHosted, setSelfHosted] = createSignal<boolean>(trackedRoom.hostDetails != null);
  const [selfHostedTooltip, setSelfHostedTooltip] = createSignal<JSXElement>();
  const [hostCountryFlag] = createResource(trackedRoom.hostDetails?.CountryCode, getCountryFlagImage);

  createEffect(() => {
    if (trackedRoom.hostDetails && trackedRoom.hostDetails.CountryCode) {
      setSelfHosted(true);
      let countryCode = trackedRoom.hostDetails.CountryCode;
      let countryName = selfHostingService().getCountryData(countryCode as any)?.name ?? trackedRoom.hostDetails.CountryCode;
      setSelfHostedTooltip(
        <Box>Hosted in: <Box as="span" fontWeight={"bold"}>
          {trackedRoom.hostDetails.ContinentCode} - {countryName}
        </Box></Box>
      );
    } else {
      setSelfHosted(false);
    }
  });

  onCleanup(() => {
    window.clearTimeout(onJoinRoomClickTimeout());
  });

  return (<>
    <div
      style={{ padding: "3px" }}
      class={style.listItem}
      role="listitem"
    >
      <Box
        __tooltip_title={<Suspense><RoomMiniProfileCard room={() => trackedRoom} /></Suspense>}
        __tooltip_placement='right'
        id={encodeURI(`pr-sidebar-room-element-${trackedRoom.roomID}`)}
        className={clsx([style.roomElement, !isClientInRoom() && style.roomElement_canBeActive])}
        border="solid 2px white"
        borderColor={isClientInRoom() ? "$neutral12" : "rgba(255, 255, 255, 0.2)"}
        color={isClientInRoom() ? "$neutral12" : "$neutral11"}
        onContextMenu={(e: MouseEvent) => e.preventDefault()}
        onMouseDown={(async (evt: MouseEvent) => {
          if (!isClientInRoom() && evt.button == 0) {
            sfxService().playClickSFX();
            setOnJoinRoomClick(trackedRoom.roomID);
            window.clearTimeout(onJoinRoomClickTimeout());
            setOnJoinRoomClickTimeout(window.setTimeout(async () => {
              let roomID = trackedRoom.roomID;
              if (!roomID) return;

              websocketService().joinRoomByID(roomID);
              setOnJoinRoomClick();
            }, 100));
          }
        })}
        onmouseenter={() => { sfxService().playHoverSFX(); }}
        pointerEvents={onJoinRoomClick() ? "none" : "auto"}
        h="55px"
        paddingLeft={5}
      >
        {onJoinRoomClick() == trackedRoom.roomID && <Spinner position={"absolute"} left="45%" top="25%" />}

        <HStack spacing="$1" h="100%">
          <Box marginRight={5}>
            <Switch>
              <Match when={selfHosted()}>
                <VStack>
                  {selfHosted() &&
                    <Suspense fallback={
                      <Box
                        __tooltip_title={selfHostedTooltip()}
                        __tooltip_placement="right"
                        fontSize={10}>Hosted</Box>
                    }>
                      <Image
                        __tooltip_title={selfHostedTooltip()}
                        __tooltip_placement="right"
                        w={30}
                        src={hostCountryFlag()}
                      />
                    </Suspense>
                  }
                  <HStack>
                    <Box fontSize={10}>{trackedRoom.userCount}/{trackedRoom.maxUsers}</Box>
                  </HStack>
                </VStack>
              </Match>
              <Match when={!selfHosted()}>
                <Box>{trackedRoom.userCount}</Box>
                <Box className={style.roomUserCountSlash}></Box>
                <Box marginLeft={5}>{trackedRoom.maxUsers}</Box>
              </Match>
            </Switch>
          </Box>

          <VStack w="inherit" alignItems={"flex-start"} overflow={"hidden"}>
            <Box
              as="span"
              w="100% !important"
              className={style.roomElementName}
              overflow={"hidden"}
              style={{ "white-space": "nowrap", "text-overflow": "ellipsis" }}
              h="100%"
              fontSize={"0.8em"}
            >
              {tryDecodeURI(emojifyService().decode(trackedRoom.roomName.trim())!)}
            </Box>
            <Divider w="100%" color="gray" />
            <Box
              overflow={"hidden"}
              fontSize={12} marginTop={2}
              w="100%"
              h={18}
              style={{ "white-space": "nowrap", "text-overflow": "ellipsis" }}
              textTransform={"uppercase"} color="$neutral11"
            >
              {/* {splitAndJoinWordsByCapitalLetter(trackedRoom.roomType)} */}
              {(trackedRoom.roomType)}
            </Box>
          </VStack>
        </HStack>

        <HStack className={style.elementIconsContainer}>
          {/* Password protected icon */}
          {trackedRoom.isPasswordProtected &&
            <Box __tooltip_placement="top" __tooltip_title={"Password protected."}>
              <Icon as={FaSolidLock} />
            </Box>
          }

          {/* Private room icon */}
          {(isRoomPrivate() && isClientInRoom()) &&
            <Box __tooltip_placement="right" __tooltip_title={"This room is private."}>
              <Icon as={FaSolidEyeLowVision} />
            </Box>
          }
        </HStack>
      </Box>
    </div >
  </>);
};

type RoomsListProps = {
  scrollTargetElement: HTMLDivElement;
  sortOption: RoomSortOptions;
};

const RoomsList: Component<RoomsListProps> = (props) => {
  const roomService = useService(RoomsService);
  const appService = useService(AppService);
  const [items, setItems] = createSignal<BasicRoomDto[]>([]);

  createEffect(() => {
    let store = roomService().store;
    let isClientInRoom = (o: BasicRoomDto) => [appService().isClientInRoom(o.roomID)];
    let targetIter = (_: BasicRoomDto) => [] as any;
    let targetSort: any = "desc";

    switch (props.sortOption) {
      case RoomSortOptions.MostUsers:
        targetIter = (o: BasicRoomDto) => {
          return [
            ...isClientInRoom(o),
            o.userCount > 0,
          ];
        };
        break;
      case RoomSortOptions.LeastUsers:
        targetIter = (o: BasicRoomDto) => {
          return [
            ...isClientInRoom(o),
            o.userCount == 0,
          ];
        };
        break;
      case RoomSortOptions.Newest:
        targetIter = (o: BasicRoomDto) => {
          return [
            ...isClientInRoom(o),
            Date.parse(o.createdDate),
          ];
        };
        targetSort = ["desc", "asc"];
        break;
      case RoomSortOptions.Oldest:
        targetIter = (o: BasicRoomDto) => {
          return [
            ...isClientInRoom(o),
            Date.parse(o.createdDate),
          ];
        };
        targetSort = ["desc", "desc"];
        break;
      default:
        targetIter = (o: BasicRoomDto) => {
          return [
            ...isClientInRoom(o),
            RoomTypeHelper.IsLobby(roomTypeFromJSON(o.roomType)),
            o.hostDetails != null,
            o.roomName,
            o.roomType
          ];
        };
        break;
    }

    let sortedRooms = orderBy(store.rooms.filter(x => {
      // Ignore rooms that are not supposed to be shown in the room list
      return !RoomModes.IGNORED_MODES.includes(roomTypeFromJSON(x.roomType) as any) &&
        RoomStagesNS.IGNORED_STAGES.indexOf(x.roomStage) == -1;
    }), targetIter, targetSort as any);

    setItems(uniqBy(sortedRooms, "roomID"));
  });

  onCleanup(() => {
    setOnJoinRoomClick();
  });

  return (<For each={items()}>{(room) => <RoomItem item={room} />}</For>);
};

export default RoomsList;