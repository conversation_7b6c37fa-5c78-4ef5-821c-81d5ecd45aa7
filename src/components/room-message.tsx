import {Box, Button, Center, HStack, Text, VStack} from "@hope-ui/solid";
import {useService} from "solid-services";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import AppSettingsService from "~/services/settings-storage.service";
import {WARNING_MESSAGES} from "~/util/const.common";
import ToolTipHelp from "./tooltip-help";
import WebsocketService from "~/services/websocket.service";

const RoomMessagesDisplay = () => {
  const appSettingsService = useService(AppSettingsService);
  const appService = useService(AppService);
  const audioService = useService(AudioService);
  const webSocketService = useService(WebsocketService);
  const muteText = () => appService().clientHasEveryoneElseMuted() ? "Unmute" : "Mute";

  // @ts-ignore
  return (<>
    <Box position={"absolute"} top={5} width="100%" pointerEvents={"none"}>
      <Center>
        <VStack class='unselectable' fontSize={10}>
          {!webSocketService().connected() && <Text color="red" fontWeight={"bold"}>You are currently disconnected from the server.</Text>}
          {appService().offlineMode() && <Text color="red" fontWeight={"bold"}>OFFLINE MODE</Text>}
          {appService().maintenanceModeActive() && <Text color="yellow" fontWeight={"bold"}>Server undergoing maintenance!</Text>}
          {audioService().allChannelsAreInactive() &&
            <HStack spacing={"$1"}>
              <Text color="yellow">{WARNING_MESSAGES.NoActiveChannels}</Text>
              <ToolTipHelp
                tooltipPlacement="bottom"
                tooltipLabel={<Box
                  width={"300px"}
                  innerHTML={
                    `
                    <p>All of the channels in the instrument dock below are currently <b>inactive</b>.</p>
                    <br/><p>This means that you and other users <i>should not be hearing any of your notes</i> since
                    nothing is being sent to the server.</p>
                  `
                  } />}
              />
            </HStack>
          }

          {appSettingsService().getSetting<boolean>("AUDIO_MIDI_OUTPUT_ONLY") &&
            <HStack spacing={"$1"}>
              <Text color="yellow">Audio to midi output only</Text>
              <ToolTipHelp tooltipLabel={<Box
                innerHTML={
                  `
                  All notes will be sent to your MIDI output device only and not through PianoRhythm's internal
                  audio engine. <br><br>
                  You can toggle this setting under <b>Settings > Soundfont > Enable audio to output only</b>
                `
                } />}
              />
            </HStack>
          }

          {appService().clientIsSelfNotesMuted() &&
            <HStack spacing={"$2"}>
              <Text>Click to {muteText().toLowerCase()} everyone else:</Text>
              <Button
                pointerEvents={"all"}
                onclick={(evt: MouseEvent) => {
                  if (evt.button == 0) {
                    appService().setClientHasEveryoneElseMuted(!appService().clientHasEveryoneElseMuted());
                  }
                }}
                variant="outline"
                size={"sm"}
              >
                {muteText()} All
              </Button>
            </HStack>
          }

          {/* TODO: Implement GRAPHICS_ENABLE_ENGINE*/}
          {!appSettingsService().getSetting<boolean>("GRAPHICS_ENABLE_ENGINE") && <Text>3D Graphics Engine Disabled</Text>}
          {appService().roomSettings()?.OnlyOwnerCanPlay && <Text>Only Room Owner Can Play</Text>}
          {appService().roomSettings()?.OnlyOwnerCanChat && <Text>Only Room Owner Can Chat</Text>}
          {appService().roomSettings()?.NoChatAllowed && <Text>No Chat Allowed</Text>}
          {appService().roomSettings()?.NoPlayingAllowed && <Text>No Playing Allowed</Text>}
        </VStack>
      </Center>
    </Box>
  </>);
};

export default RoomMessagesDisplay;