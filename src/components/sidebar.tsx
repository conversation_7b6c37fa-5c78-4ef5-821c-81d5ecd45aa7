import {Box, Center, HStack, Skeleton, VStack} from "@hope-ui/solid";
import clsx from "clsx";
import {FaSolidDoorOpen, FaSolidUserGroup, FaSolidUsers} from "solid-icons/fa";
import {Component, createEffect, createSignal, JSXElement, lazy, Match, Setter, Suspense, Switch} from "solid-js";
import {useService} from "solid-services";
import {Transition} from "solid-transition-group";
import {buttonSFX} from "~/directives/buttonsfx.directive";
import style from '~/sass/sidebar.module.sass';
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import PlatformService from "~/services/platform.service";
import {SidebarService} from "~/services/sidebar.service";
import {RoomSortOptions} from "~/types/room.types";
import {MenuContentSelect} from "./modals/settings-content/common.content";

const SidebarUsersList = lazy(() => import("./sidebar.users-list"));
const SidebarRoomsList = lazy(() => import("./sidebar.rooms"));

enum Tabs {
    "Users",
    "Friends",
    "Rooms",
    "None"
}

const SidebarList: Component = () => {
    const appService = useService(AppService);
    const platformService = useService(PlatformService);
    const displaysService = useService(DisplaysService);
    const sidebarService = useService(SidebarService);

    const [roomSortOption, setRoomSortOption] = createSignal(RoomSortOptions.Default);

    const [show] = createSignal(!platformService().isMobile());
    const [activeTab, setActiveTab] = createSignal(Tabs.Users);
    let scrollContainer!: HTMLDivElement;

    createEffect(() => {
        let root = document.documentElement;
        root.style.setProperty("--sidebarListWidth", show() ? "115px" : "60px");
        root.style.setProperty("--sidebarSortByHeight", activeTab() == Tabs.Rooms ? "30px" : "0px");
    });

    return <Transition name="fade">
        {
            displaysService().getDisplay("SIDEBAR_LIST") &&
            <Box
                class={style.parentContainer}
                ref={sidebarService().setContainerElement}
            >
                {/* Tabs */}
                <HStack h={20} w="100%" spacing={"$0"} justifyContent="space-between" padding={0} pointerEvents="all">
                    <Tab icon={<FaSolidUsers/>} setActiveTab={setActiveTab} tab={Tabs.Users} activeTab={activeTab()}/>
                    <Tab
                        icon={
                            <Box position={"relative"}>
                                <FaSolidUserGroup/>
                                {/* {hasPendingFriendRequests() &&
                  <Badge position={"absolute"} right={-5} top={-5} fontSize={"8px"} color="$tertiary1" fontWeight={"bolder"}>
                    {friendsService().pendingFriendRequests().length}
                  </Badge>
                } */}
                            </Box>
                        }
                        // additionalTooltip={` - Pending: ${friendsService().pendingFriendRequests().length}`}
                        setActiveTab={setActiveTab} tab={Tabs.Friends} activeTab={activeTab()}
                    />
                    <Tab icon={<FaSolidDoorOpen/>} setActiveTab={setActiveTab} tab={Tabs.Rooms}
                         activeTab={activeTab()}/>
                </HStack>

                <Box class={style.listContainer}>
                    {/* Sort Button */}
                    {activeTab() == Tabs.Rooms &&
                        <HStack
                            justifyContent={"space-evenly"}
                            padding={5}
                            height={"var(--sidebarSortByHeight)"}
                            spacing={"$1"}
                        >
                            <Box fontSize={"0.5em"}>Sort:</Box>
                            <MenuContentSelect
                                placeholder="Sort By"
                                showOnlySelect
                                defaultValue={RoomSortOptions.Default}
                                triggerFontSize={10}
                                triggerMinHeight={0}
                                selectOptionFontSize={10}
                                triggerTooltip={`Sort by: ${roomSortOption()}`}
                                options={Object.values(RoomSortOptions)}
                                onSelected={async (value) => {
                                    setRoomSortOption(value as RoomSortOptions);
                                }}
                            />

                        </HStack>
                    }

                    <Box
                        class={style.mainList}
                        ref={scrollContainer}
                    >
                        <Switch fallback={<Box style={"word-break: break-all"}>Feature coming soon...</Box>}>
                            <Match when={activeTab() == Tabs.Users}>
                                <Suspense fallback={<Skeleton height="60px" borderRadius={5}></Skeleton>}>
                                    <Suspense><SidebarUsersList/></Suspense>
                                </Suspense>
                            </Match>
                            <Match when={activeTab() == Tabs.Friends && appService().offlineMode()}>
                                <Box style={"word-break: break-all"}>You can't make friends if you're offline! 😜</Box>
                            </Match>
                            <Match when={activeTab() == Tabs.Friends && !appService().isClientMember()}>
                                <Box textAlign={"center"}>Login or sign up for a <i>free account</i> to add
                                    friends!</Box>
                            </Match>
                            {/* <Match when={activeTab() == Tabs.Friends}>
                <Suspense fallback={<Skeleton height="60px" borderRadius={5}></Skeleton>}>
                  <FriendsList scrollTargetElement={scrollTargetElement} />
                </Suspense>
              </Match> */}
                            <Match when={activeTab() == Tabs.Rooms}>
                                <Suspense fallback={<Skeleton height="60px" borderRadius={5}></Skeleton>}>
                                    <VStack spacing={"$2"}>
                                        <SidebarRoomsList
                                            scrollTargetElement={scrollContainer}
                                            sortOption={roomSortOption()}
                                        />
                                    </VStack>
                                </Suspense>
                            </Match>
                        </Switch>
                    </Box>
                </Box>
            </Box>
        }
    </Transition>;
};

const Tab: Component<{
    icon: JSXElement,
    additionalTooltip?: string,
    tab: Tabs,
    activeTab: Tabs,
    setActiveTab: Setter<Tabs>;
}> = (props) => {
    // const sidebarService = useService(SidebarService);
    return (<>
        <Box
            id={`sidebar-tab-${Tabs[props.tab].toLowerCase()}`}
            flex={1}
            h="100%"
            class={clsx([
                "unselectable",
                style.clickableTab,
                (props.activeTab == props.tab && style.activeTab)
            ])}
            ref={(el: HTMLElement) => buttonSFX(el)}
            onmousedown={(evt: MouseEvent) => {
                if (evt.button == 0) {
                    // sidebarService().setUserProfileCardActive(null);
                    props.setActiveTab(props.tab);
                }
            }}
        >
            <Center
                __tooltip_title={`${Tabs[props.tab]}${props.additionalTooltip || ""}`}
                __tooltip_placement="bottom"
                h="100%"
                fontSize={"0.8em"}
            >
                {props.icon}
            </Center>
        </Box>
    </>);
};

export default SidebarList;