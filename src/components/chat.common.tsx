import css from '~/sass/chat.module.sass';
import ChatService from '~/services/chat.service';
import SwalPR from '~/util/sweetalert';

export const triggerDeleteMessagesWarning = (affectedIDS: string[], onConfirm: () => void, onClose: () => void = () => { }) => {
  let isNotMoreThanOneMessage = affectedIDS.length < 2;
  let subtext = isNotMoreThanOneMessage ? "this message" : "these messages";

  return SwalPR().fire({
    icon: "warning",
    title: isNotMoreThanOneMessage ? "Delete Message" : "Delete Messages",
    text: `Are you sure you want to delete ${subtext}?`,
    confirmButtonText: "Delete",
    showCancelButton: true,
    didClose: () => {
      if (onClose) onClose();
    }
  }).then((result) => {
    if (result.isConfirmed) onConfirm();
  });
};

export const deleteMessageByID =
  async (messageID: string, emitCommand: (_: string) => void, chatService: ReturnType<typeof ChatService>, onClose?: () => void) => {
    let ids = chatService.findMessagesBy(x => x.id == messageID).map(x => x.id);
    chatService.setChatMessagesSetToBeDeleted(ids);
    await triggerDeleteMessagesWarning(ids, () => {
      emitCommand(messageID);
    }, () => {
      chatService.setChatMessagesSetToBeDeleted([]);
      if (onClose) onClose();
    });
  };

const onAnimationEnd = (event: Event) => {
  let animEvent = event as AnimationEvent;
  let element = animEvent.target as HTMLElement;

  if (animEvent.animationName == css.flash) {
    element?.classList.remove(css.messageFlash!);
    element?.removeEventListener("animationend", onAnimationEnd);
  }
};

export function scrollToChatMessageElement(messageID?: string) {
  if (messageID == null) return;

  let element = document.querySelector(`#chat-message-${messageID}`);
  element?.classList.remove(css.messageFlash!);
  element?.scrollIntoView({ behavior: 'smooth', block: "center" });
  element?.classList.add(css.messageFlash!);
  element?.addEventListener("animationend", onAnimationEnd);
}