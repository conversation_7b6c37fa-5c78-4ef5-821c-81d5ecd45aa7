import { Badge, Box, Center, HStack, IconButton, Text, VStack } from '@hope-ui/solid';
import { createEventBus } from '@solid-primitives/event-bus';
import { createElementSize, createWindowSize } from '@solid-primitives/resize-observer';
import anime from 'animejs';
import { debounce } from 'lodash-es';
import { FaRegularRectangleXmark, FaSolidWindowMaximize, FaSolidWindowMinimize, FaSolidWindowRestore } from 'solid-icons/fa';
import { Component, createEffect, createSignal, lazy, on, onCleanup, onMount, Show, Suspense } from 'solid-js';
import { useService } from 'solid-services';
import { Transition } from 'solid-transition-group';
import { ClientSideUserDto } from '~/proto/user-renditions';
import css from '~/sass/chat.module.sass';
import AppService from '~/services/app.service';
import ChatService from '~/services/chat.service';
import DisplaysService from '~/services/displays.service';
import I18nService from '~/services/i18n.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import UsersService from '~/services/users-service';
import { AppSoundFx2 } from '~/types/app.types';
import { UserDtoUtil } from '~/types/user-helper';
import { HTML_IDS } from '~/util/const.common';
import { isDefined } from '~/util/helpers';
import { onRemoveAnimeInstance } from '~/util/helpers.anime-js';
import SwalPR from '~/util/sweetalert';

const ChatBar = lazy(() => import('./chat-bar'));
const ChatList = lazy(() => import('./chat-list'));

const [bottomOffset, setBottomOffset] = createSignal(0);

const UsersTyping: Component = () => {
  const chatService = useService(ChatService);
  const usersService = useService(UsersService);
  const appService = useService(AppService);
  const [usersTyping, setUsersTyping] = createSignal("");
  const [botsCalculating, setBotsCalculating] = createSignal("");
  const chatBarElementSize = createElementSize(chatService().chatBarElement);
  const [bottom, setBottom] = createSignal(0);

  const onHandleUserTyping = (onEmpty = () => { }, onFilterUser = (user: ClientSideUserDto) => { return true; }, action = "typing") => {
    let currentUsersTyping =
      chatService()
        .usersTyping()
        .filter(socketID => socketID != appService().client().socketID)
        .map(usersService().getUserBySocketID)
        .filter(isDefined)
        .filter(onFilterUser)
        .map(x => x.userDto?.nickname || x.userDto?.username);

    if (currentUsersTyping.length == 0) {
      onEmpty();
      return;
    }

    let firstN_Users = currentUsersTyping.slice(0, 2);
    let namesLeft = currentUsersTyping.length - firstN_Users.length;
    let text = firstN_Users.join(", ");

    if (namesLeft >= 1) {
      text = `${text} and ${namesLeft.toString()} other(s) are ${action}`;
    } else {
      text = `${text} ${currentUsersTyping.length == 1 ? "is" : "are"} ${action}`;
    }

    return text + "...";
  };

  createEffect(() => {
    let usersTypingOutput = onHandleUserTyping(() => { setUsersTyping(""); }, (x) => !UserDtoUtil.isBot(x.userDto!.roles));
    if (usersTypingOutput) setUsersTyping(usersTypingOutput);

    let botsCalculatingOutput = onHandleUserTyping(() => { setBotsCalculating(""); }, (x) => UserDtoUtil.isBot(x.userDto!.roles), "calculating");
    if (botsCalculatingOutput) setBotsCalculating(botsCalculatingOutput);
  });

  createEffect(() => {
    setBottom(chatBarElementSize.height || 0);
  });

  return (<>
    <Box
      as="span"
      class={css.chatUsersTyping}
      bottom={bottom()}
      opacity={chatService().chatOpacity() == 1 ? 1 : 0.2}
    >
      <HStack spacing={"$1"}>
        <Show when={usersTyping()}>
          {usersTyping()}
          <Box fontSize={"10px"} as="span" class="loadingDots"></Box>
        </Show>
        <Show when={botsCalculating()}>
          {botsCalculating()}
          <Box fontSize={"10px"} as="span" class="loadingDots"></Box>
        </Show>
      </HStack>
    </Box>
  </>);
};

const ChatWindowButtons: Component = () => {
  const displayService = useService(DisplaysService);
  const chatService = useService(ChatService);
  const appSettingsService = useService(AppSettingsService);
  const sfxService = useService(SoundEffectsService);

  return (<>
    <Box class={css.chatWindowButtons} pointerEvents={"all"}>
      <HStack spacing="$1">
        <IconButton size={"xs"}
          __tooltip_title={`${chatService().chatMinimized() ? 'Restore' : 'Minimize'} Chat`}
          __tooltip_placement='bottom'
          background={"$primary2"}
          onMouseDown={(event: MouseEvent) => {
            if (event.button == 0) {
              if (!chatService().chatMinimized()) {
                sfxService().playSFX_ui2(AppSoundFx2.RESUME);
              }

              chatService().setChatMinimized(v => !v);
              chatService().setShowChatWindowButtons(false);
              if (!chatService().chatMinimized()) chatService().setShowChatBar(false);
              appSettingsService().saveSetting("CHAT_MESSAGES_MINIMIZED", chatService().chatMinimized());
            }
          }}
          aria-label={`${chatService().chatMinimized() ? 'Restore' : 'Minimize'} Chat`}
          icon={
            <Show when={chatService().chatMinimized()} fallback={<FaSolidWindowMinimize />}>
              <FaSolidWindowRestore />
            </Show>
          }
        />

        {!chatService().chatMinimized() &&
          <IconButton size={"xs"}
            background={"$primary2"}
            __tooltip_title={`${chatService().chatMaximized() ? 'Restore' : 'Maximize'} Chat`}
            __tooltip_placement='bottom'
            onMouseDown={(event: MouseEvent) => {
              if (event.button != 0) return;
              chatService().setChatMaximized(v => !v);
              appSettingsService().saveSetting("CHAT_MESSAGES_MAXIMIZED", chatService().chatMaximized());
            }}
            aria-label={`${chatService().chatMaximized() ? 'Restore' : 'Maximize'} Chat`}
            icon={
              <Show when={chatService().chatMaximized()} fallback={<FaSolidWindowMaximize />}>
                <FaSolidWindowRestore />
              </Show>
            }
          />
        }

        <IconButton size={"xs"}
          background={"$primary2"}
          __tooltip_title="Close Chat"
          __tooltip_placement='bottom'
          aria-label="Close Chat"
          icon={<FaRegularRectangleXmark />}
          onMouseDown={(evt: MouseEvent) => {
            if (evt.button != 0) return;
            displayService().setDisplay("CHAT_MESSAGES", false);
            appSettingsService().saveSetting("DISPLAY_CHAT", false);
            sfxService().playSFX_ui2(AppSoundFx2.CLOSE_MENU);
          }}
        />
      </HStack>
    </Box>
  </>);
};

const targetWidth = 180;
const initialWidth = 400;

const ChatMinimized: Component = () => {
  const [currentNumOfMessages, setCurrentNumOfMessages] = createSignal(0);
  const [newMessages, setNewMessages] = createSignal(0);
  const chatService = useService(ChatService);
  const appService = useService(AppService);

  onMount(() => {
    setCurrentNumOfMessages(chatService().messages().length);
  });

  function onElementRef(element: HTMLDivElement) {
    onRemoveAnimeInstance(element);
    anime({
      targets: element,
      width: [`${initialWidth}px`, `${targetWidth}px`],
      easing: 'easeOutElastic(0.1, 1)',
      duration: 500,
      complete: () => {
        chatService().setShowChatWindowButtons(true);
      }
    });
  }

  createEffect(() => {
    setNewMessages(chatService().messages().length - currentNumOfMessages());
  });

  return (<>
    <Box
      pointerEvents={appService().isRoomCurrentPage() ? "all" : "none"}
      background={"$primary1"}
      padding={"$2"}
      borderRadius={5}
      width={initialWidth}
      height={40}
      ref={onElementRef}
    >
      {newMessages() > 0 &&
        <Badge
          __tooltip_title={`New Messages: ${newMessages()}`}
          background="red" class={css.chatMinimizedBadge}>{newMessages()}
        </Badge>
      }
      <Text class={css.chatMinimizedText} marginLeft={newMessages() > 0 ? 11 : 0}>Chat Messages</Text>
    </Box>
  </>);
};

const ChatMinimizedLoading: Component = () => {
  return (<>
    <Box
      pointerEvents={"none"}
      background={"$primary1"}
      padding={"$2"}
      borderRadius={5}
      width={targetWidth}
      height={40}
    >
      <Text class={css.chatMinimizedText}>Loading Chat...</Text>
    </Box>
  </>);
};

const ChatComponent: Component = () => {
  const appService = useService(AppService);
  const displayService = useService(DisplaysService);
  const appSettingsService = useService(AppSettingsService);
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const chatService = useService(ChatService);
  const windowSize = createWindowSize();
  const onContainerTransitionEndEvents = createEventBus<void>();

  const [hidetimeout, setHideTimeout] = createSignal(-1);
  const [chatMinimized, setChatMinimized] = createSignal(false);

  function interceptClickEvent(e: MouseEvent) {
    let href: string | null;
    let target = e.target as HTMLElement || e.srcElement;
    if (target?.tagName === 'A') {
      href = target.getAttribute('href');
      if (href != null) {
        e.preventDefault();
        let openURL = () => { if (href) window.open(href); };

        if (!appSettingsService().getSetting("ONLINE_WARN_ABOUT_EXTERNAL_LINKS")) {
          openURL();
        } else {
          SwalPR(sfxService).fire({
            html: i18nService().t_roomPage("generalMessages.openingExternalLinks"),
            showCancelButton: true,
            icon: "warning",
            cancelButtonText: "Nah, sorry...",
            confirmButtonText: "Yes!",
            showConfirmButton: true,
            allowEscapeKey: true,
          }).then((res) => {
            if (res.isConfirmed) openURL();
          });
        }
      }
    }
  }

  const onHide = () => window.clearTimeout(hidetimeout());

  function onExit() {
    chatService().disposeContextMenu();
    chatService().setChatOpacity(0);
    window.clearTimeout(hidetimeout());
  }

  function onEnter(_: HTMLDivElement) {
    chatService().setChatOpacity(chatService().defaultChatOpacity);
  }

  onCleanup(onExit);

  function calculateChatOffset() {
    let instrumentDockDisplayed = displayService().getDisplay("INSTRUMENT_DOCK");
    let bottomBarFullyShown = displayService().getDisplay("BOTTOM_BAR_FULLY_SHOWN");
    let offset = 0;

    if (bottomBarFullyShown) {
      let root = document.documentElement;
      let bottomBarHeight = parseFloat(root.style.getPropertyValue("--bottomBarHeight") ?? "0px");
      offset += bottomBarHeight;
    }

    if (instrumentDockDisplayed) {
      let bounds = document.getElementById(HTML_IDS.INSTRUMENT_DOCK_CONTAINER)?.getBoundingClientRect();
      offset += bounds ? bounds.height : 65;
    }

    if (appService().is2DMode()) {
      let bounds = document.getElementById(HTML_IDS.PIANO_CANVAS_2D_CONTAINER)?.getBoundingClientRect();
      if (bounds) offset += bounds.height;
    }

    let chatBarHeight = chatService().chatBarElement()?.getBoundingClientRect().height || 34;
    offset += chatBarHeight + 20;

    setBottomOffset(offset);
  }

  const debounceCalculateChatOffset = debounce(calculateChatOffset, 100);

  createEffect(on(() => [
    windowSize.width, windowSize.height,
    displayService().getDisplay("INSTRUMENT_DOCK"),
    displayService().getDisplay("BOTTOM_BAR_FULLY_SHOWN"),
    appService().is2DMode(),
  ], () => {
    debounceCalculateChatOffset();
  }));

  createEffect(() => {
    setChatMinimized(chatService().chatMinimized());
  });

  // Forcefully set chat maximized if 2D mode is enabled
  createEffect(() => {
    if (appService().is2DMode() && !chatService().chatMinimized()) chatService().setChatMaximized(true);
  });

  const onRef = (element: HTMLDivElement) => {
    chatService().setChatContainerElement(element);
    element.addEventListener('click', interceptClickEvent);
    element.addEventListener('transitionend', (event) => {
      if (event.propertyName === 'bottom') onContainerTransitionEndEvents.emit();
    });

    onCleanup(() => {
      element.removeEventListener('click', interceptClickEvent);
    });
  };

  return (<>
    <Transition name="fade"
      appear
      onEnter={(el, done) => {
        onEnter(el as HTMLDivElement);
        done();
      }}
      onAfterExit={onExit}
    >
      {displayService().getDisplay("CHAT_MESSAGES") &&
        <Box >
          <Center
            ref={onRef}
            class={css.chatContainer}
            pointerEvents={chatService().chatMinimized() ? "none" : "auto"}
            flexDirection="column"
            opacity={chatService().chatOpacity()}
            onmousemove={() => {
              onHide();
              chatService().setChatOpacity(1);
            }}
            onmouseenter={() => {
              onHide();
              chatService().setChatOpacity(1);
            }}
            onmouseleave={() => {
              onHide();
              window.clearTimeout(hidetimeout());
              setHideTimeout(window.setTimeout(() => {
                if (!chatService().ctxMenuActive()) {
                  if (chatService().chatCommandsDisplayed()) return;
                  chatService().setChatOpacity(chatService().defaultChatOpacity);
                }
              }, 3000));
            }}
            ondragenter={() => {
              onHide();
              chatService().setChatOpacity(1);
            }}
            bottom={{
              "@initial": `${90 + bottomOffset()}px`,
            }}
            height={{
              "@initial": `calc(100vh - ${bottomOffset()}px)`,
            }}
          >
            <VStack h="100%" alignItems="flex-start" pointerEvents={
              chatMinimized() ? "none" : "auto"
            }>
              {/* Chat Window Buttons */}
              {chatService().showChatWindowButtons() && <ChatWindowButtons />}

              {/* Chat List */}
              <Show when={!chatMinimized()} fallback={<ChatMinimized />}>
                <Suspense fallback={<ChatMinimizedLoading />}>
                  <ChatList onContainerTransitionEndEvents={onContainerTransitionEndEvents} />
                </Suspense>

                {(chatService().showChatBar() && appSettingsService().getSetting<boolean>("DISPLAY_WHOISTYPING")) &&
                  <UsersTyping />}

                <Show when={chatService().showChatBar()}>
                  <Suspense><ChatBar /></Suspense>
                </Show>
              </Show>
            </VStack>
          </Center>
        </Box>
      }
    </Transition>
  </>);
};

export default ChatComponent;