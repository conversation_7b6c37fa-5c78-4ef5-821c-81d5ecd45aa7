import { Box, <PERSON>ton, ButtonGroup, createDisclosure, HStack, Icon, Modal, ModalBody, ModalCloseButton, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader, ModalOverlay, Select, SelectContent, SelectIcon, SelectListbox, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue, Tooltip, VStack } from "@hope-ui/solid";
import { cloneDeep } from "lodash-es";
import * as noUiSlider from 'nouislider';
import { FaSolidMusic } from "solid-icons/fa";
import { Component, createEffect, createSignal, For, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import AppSettingsService from "~/services/settings-storage.service";
import { Equalizer, EQUALIZER_FREQUENCIES, EQUALIZER_PRESETS, EqualizerBand, EqualizerPreset } from "~/types/audio.types";

const AudioEqualizerModal: Component = () => {
  const DISPLAY_KEY = "AUDIO_EQUALIZER_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const appSettingsService = useService(AppSettingsService);
  const appService = useService(AppService);
  const NUM_BANDS = EQUALIZER_FREQUENCIES.length;
  const MAX_RANGE = 15;

  const [sliders, setSliders] = createSignal<noUiSlider.API[]>([]);
  const [sliderValues, setSliderValues] = createStore<number[]>([]);
  const [currentPreset, setCurrentPreset] = createSignal<EqualizerPreset>(
    appSettingsService().getSetting("AUDIO_EQUALIZER_PRESET")
  );

  onMount(() => {
    onPresetChange(currentPreset());
  });

  function closeModal() { displayService().setDisplay(DISPLAY_KEY, false); }

  createEffect(() => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  let updateTimeout = -1;
  let presetGains = EQUALIZER_PRESETS.map(Equalizer.FromPreset).map(x => x.map(y => y.gain));

  function arrayEquals(a: number[], b: number[]) {
    return Array.isArray(a) &&
      Array.isArray(b) &&
      a.length === b.length &&
      a.every((val, index) => val === b[index]);
  }

  const onSliderMount = (element: HTMLElement, channel: number) => {
    if (element.className.includes("noUi-target")) return;

    let getBands = () => appSettingsService().getSetting<EqualizerBand[] | undefined>("AUDIO_EQUALIZER_BANDS");
    let band = getBands()?.find(x => x.chnl == channel);
    if (!band) return;
    band = cloneDeep(band);

    let slider = noUiSlider.create(element, {
      orientation: 'vertical',
      direction: "rtl",
      connect: true,
      start: band?.gain || 0,
      range: {
        'min': -MAX_RANGE,
        'max': MAX_RANGE
      },
    });

    slider.on("update", (values) => {
      let value = parseInt(values[0] as any);
      appService().coreService()?.set_equalizer_gain(channel, value);
      setSliderValues(channel, value);

      clearTimeout(updateTimeout);
      updateTimeout = window.setTimeout(() => {
        let isCustom = !presetGains.some(f => arrayEquals(f, [...sliderValues]));
        if (isCustom) setCurrentPreset(EqualizerPreset.Custom);
        // Set all band gains from sliderValues
        let bands = sliders()
          .map((slider, idx) => {
            let band = cloneDeep(getBands()?.find(x => x.chnl == idx));
            if (!band) return null;
            band.gain = slider.get() as number;
            return band;
          })
          .filter(Boolean) as EqualizerBand[];
        appSettingsService().saveSetting("AUDIO_EQUALIZER_BANDS", bands);
      }, 100);
    });

    setSliders(v => [...v, slider]);
  };

  const onPresetChange = (preset: EqualizerPreset) => {
    if (preset == EqualizerPreset.Custom) return;
    let presets = Equalizer.FromPreset(preset);
    presets.forEach((band, channel) => {
      appService().coreService()?.set_equalizer_band(
        channel, band.curve, band.freq, band.rsnce, band.gain
      );
    });

    presets.map(x => x.gain).forEach((x, idx) => {
      let slider = sliders().find((_, channel) => channel == idx);
      if (slider && x != null) slider.set(x);
    });

    appSettingsService().saveSetting("AUDIO_EQUALIZER_PRESET", preset);
    setCurrentPreset(preset);
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"outside"}
      size="3xl"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidMusic} /> Audio Equalizer (±{MAX_RANGE} dB)</ModalHeader>
        <ModalBody
          w="100%"
          className={!audioService().equalizerEnabled() ? "disabled" : ""}
        >
          <VStack>
            <Box w="200px">
              <Select
                defaultValue={EqualizerPreset[Equalizer.DEFAULT_PRESET]}
                value={EqualizerPreset[currentPreset()]}
                onChange={(value: string) => {
                  onPresetChange(EqualizerPreset[value as any] as any as EqualizerPreset);
                }}
              >
                <SelectTrigger>
                  <SelectPlaceholder>{"Select a preset..."}</SelectPlaceholder>
                  <SelectValue />
                  <SelectIcon />
                </SelectTrigger>
                <SelectContent>
                  <SelectListbox>
                    <For each={EQUALIZER_PRESETS.map(x => EqualizerPreset[x])}>
                      {item => (
                        <SelectOption value={item} disabled={item === EqualizerPreset[EqualizerPreset.Custom]}>
                          <SelectOptionText>{item}</SelectOptionText>
                          <SelectOptionIndicator />
                        </SelectOption>
                      )}
                    </For>
                  </SelectListbox>
                </SelectContent>
              </Select>
            </Box>
            <HStack
              userSelect={"none"}
              w="100%"
              h={"300px"}
              overflow={"hidden"}
              overflowX="scroll"
              spacing={"$5"}
              padding={20}
            >
              <For each={(new Array(NUM_BANDS)).fill(0)}>
                {(_, idx) => {
                  return (<>
                    <VStack
                      w="100%"
                      h="100%"
                      spacing={"$5"}
                    >
                      <Box
                        __tooltip_title={`Gain value for channel: ${idx()}`}
                        __tooltip_placement="top"
                        fontSize={12}
                        cursor="default"
                      >
                        {sliderValues[idx()]}db
                      </Box>
                      <Box
                        id={`audio-equalizer-slider-id-${idx()}`}
                        class={"noUi-target noUi-vertical"}
                        ref={(elem: HTMLElement) => onSliderMount(elem, idx())}
                        h="100%"
                      />
                      <Box fontSize={12}>
                        {EQUALIZER_FREQUENCIES?.[idx()] % 1000 == 0 ? `${EQUALIZER_FREQUENCIES?.[idx()] / 1000}k` : EQUALIZER_FREQUENCIES[idx()]}
                      </Box>
                    </VStack>
                  </>);
                }}
              </For>
            </HStack>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <ButtonGroup w="100%" variant={"outline"} size="sm" justifyContent={"space-between"}>
            <Button
              onmousedown={() => {
                audioService().setEqualizerEnabled(!audioService().equalizerEnabled());
              }}
            ><Box>{audioService().equalizerEnabled() ? `Disable` : `Enable`}</Box></Button>
            <Button
              disabled={!audioService().equalizerEnabled() || currentPreset() == Equalizer.DEFAULT_PRESET}
              onmousedown={() => {
                appService().coreService()?.reset_equalizer();
                onPresetChange(Equalizer.DEFAULT_PRESET);
              }}
            >Reset</Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal >
  </>);
};

export default AudioEqualizerModal;