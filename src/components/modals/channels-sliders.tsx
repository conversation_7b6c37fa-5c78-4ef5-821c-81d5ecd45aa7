import { Box, Button, ButtonGroup, Checkbox, HStack, Icon, Modal, ModalBody, ModalCloseButton, ModalContent, Modal<PERSON>ooter, ModalHeader, ModalOverlay, Select, SelectContent, SelectIcon, SelectListbox, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue, Tooltip, VStack, createDisclosure } from "@hope-ui/solid";
import { remove } from "lodash-es";
import * as noUiSlider from 'nouislider';
import { Subscription, filter } from "rxjs";
import { FaSolidMusic } from "solid-icons/fa";
import { Component, For, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import { AUDIO } from "~/util/const.common";

enum ChannelParameter {
  Volume,
  Panning
}

const DEFAULT_CHANNEL_PARAMETER = ChannelParameter.Volume;

export const PRESETS = [
  ChannelParameter.Volume,
  ChannelParameter.Panning,
];

const ChannelsSlidersModal: Component = () => {
  const DISPLAY_KEY = "CHANNELS_SLIDERS_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);

  const [activitiesSubject, setActivitiesSubject] = createSignal<Subscription>();
  const [mutedChannels, setMutedChannels] = createSignal<number[]>([]);
  const [lastSavedVolumeBeforeMute, setLastSavedVolumeBeforeMute] = createSignal<{ channel: number, volume: number; }[]>([]);
  const [sliders, setSliders] = createSignal<{ channel: number, slider: noUiSlider.API, element: HTMLElement; }[]>([]);
  const [sliderValues, setSliderValues] = createStore<number[][]>([[], []]);
  const [currentPreset, setCurrentPreset] = createSignal<ChannelParameter>(DEFAULT_CHANNEL_PARAMETER);

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  onMount(() => {
    let subscription =
      audioService().noteActivitiesSubjectNonPressured
        .pipe(filter(_ => audioService().clientEnabledSynthNoteActivities()))
        .pipe(filter(x => x.type == "On" && x.isClient))
        .subscribe(event => {
          let slider = sliders().find(x => x.channel == event.channel);
          if (mutedChannels().includes(event.channel) || slider?.slider.get() == 0) return;
          if (slider) {
            let element = slider.element.querySelector(".noUi-connects") as HTMLElement | null;
            if (element) {
              element.style.transform = `scaleY(${(event.velocity || 0) / 127})`;
              element.classList.add("noUi-flash-background");
              setTimeout(() => {
                element?.classList.remove("noUi-flash-background");
              }, 200);
            }
          }
        });
    setActivitiesSubject(subscription);

    audioService()
      .channels()
      .filter(x => x.volume == 0)
      .map(x => x.channel)
      .forEach(v => setMutedChannels(m => [...m, v]));
  });

  onCleanup(() => {
    activitiesSubject()?.unsubscribe();
  });

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  createEffect(() => {
    if (currentPreset() == ChannelParameter.Volume)
      mutedChannels().forEach(channel => {
        sliders().find(x => x.channel == channel)?.slider.set(0, false);
      });
  });

  const onPresetClick = () => {
    switch (currentPreset()) {
      case ChannelParameter.Volume: {
        sliders().forEach(({ channel, slider, element }) => {
          let audioChannel = audioService().getChannel(channel);
          if (audioChannel) {
            let isMuted = mutedChannels().includes(channel);
            if (isMuted) element.classList.add("disabled");
            let lastSaved = lastSavedVolumeBeforeMute().find(x => x.channel == channel);
            let volume = isMuted ? 0 : lastSaved?.volume || audioChannel.volume;
            slider.set(volume);
          }
        });
        break;
      }
      case ChannelParameter.Panning: {
        sliders().forEach(({ channel, slider, element }) => {
          element.classList.remove("disabled");
          let audioChannel = audioService().getChannel(channel);
          if (audioChannel) { slider.set(audioChannel.pan); }
        });
        break;
      }
    }
  };

  const onMuteChannel = (channel: number) => {
    let currentVolume = audioService().channels().find(x => x.channel == channel)?.volume || 0;
    setMutedChannels(v => [...v, channel]);
    setLastSavedVolumeBeforeMute(v => [...v, { channel, volume: currentVolume }]);

    let sliderInfo = sliders().find((x) => x.channel == channel);
    if (!sliderInfo) return;
    sliderInfo.element.classList.add("disabled");
    sliderInfo.slider.set(0, false); // Prevent 'set' event
    audioService().setChannelVolume(channel, 0);
  };

  const onUnmuteChannel = (channel: number) => {
    let sliderInfo = sliders().find((x) => x.channel == channel);
    if (sliderInfo) {
      sliderInfo.element.classList.remove("disabled");
      let muted = [...mutedChannels()];
      remove(muted, (x) => x == channel);
      setMutedChannels(muted);

      let lastSaved = lastSavedVolumeBeforeMute().find((x) => x.channel == channel);
      if (lastSaved && sliderInfo) {
        sliderInfo.slider.set(lastSaved.volume, false);
      } else {
        sliderInfo.slider.set(AUDIO.DEFAULT_CHANNEL_VOLUME, false);
      }
      audioService().setChannelVolume(channel, sliderInfo.slider.get() as number);

      setLastSavedVolumeBeforeMute((v) => v.filter((x) => x.channel !== channel));
    }
  };

  const onReset = () => {
    switch (currentPreset()) {
      case ChannelParameter.Volume: {
        sliders().forEach(({ channel, slider }) => {
          if (mutedChannels().includes(channel)) return;
          slider.set(AUDIO.DEFAULT_CHANNEL_VOLUME);
        });
        break;
      }
      case ChannelParameter.Panning: {
        sliders().forEach(({ slider }) => {
          slider.set(AUDIO.DEFAULT_PAN);
        });
        break;
      }
    }
  };

  const onSliderMount = (element: HTMLElement, channel: number) => {
    if (element.className.includes("noUi-target")) return;

    let slider = noUiSlider.create(element, {
      orientation: 'vertical',
      direction: "rtl",
      connect: true,
      keyboardSupport: false,
      start: 0,
      step: 1,
      tooltips: true,
      range: {
        'min': 0,
        'max': AUDIO.U8_MAX_VALUE
      }
    });

    setSliderValues(currentPreset(), channel, 0);

    slider.on("set", (values) => {
      let value = parseInt(values[0] as any);
      setSliderValues(currentPreset(), channel, value);

      let audioChannel = audioService().getChannel(channel);
      if (audioChannel) {
        switch (currentPreset()) {
          case ChannelParameter.Volume: {
            audioService().setChannelVolume(channel, value);
            break;
          }
          case ChannelParameter.Panning: {
            audioService().setChannelPan(channel, value);
            break;
          }
        }
      }
    });

    setSliders(v => [...v, { channel, slider, element }]);
    onPresetClick();
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"outside"}
      size="5xl"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidMusic} /> {`Channels Parameters`}
        </ModalHeader>
        <ModalBody w="100%">
          <VStack spacing={"$2"}>
            <Box w="200px">
              <Select
                defaultValue={ChannelParameter[DEFAULT_CHANNEL_PARAMETER]}
                value={ChannelParameter[currentPreset()]}
                onChange={(value: string) => {
                  let preset = ChannelParameter[value as any] as any as ChannelParameter;
                  setCurrentPreset(preset);
                  onPresetClick();
                }}
              >
                <SelectTrigger>
                  <SelectPlaceholder>{"Select a parameter..."}</SelectPlaceholder>
                  <SelectValue />
                  <SelectIcon />
                </SelectTrigger>
                <SelectContent>
                  <SelectListbox>
                    <For each={PRESETS.map(x => ChannelParameter[x])}>
                      {item => (
                        <SelectOption value={item}>
                          <SelectOptionText>{item}</SelectOptionText>
                          <SelectOptionIndicator />
                        </SelectOption>
                      )}
                    </For>
                  </SelectListbox>
                </SelectContent>
              </Select>
            </Box>
            <HStack
              userSelect={"none"}
              w="100%"
              overflow={"hidden"}
              overflowX="scroll"
              spacing={"$2"}
            >
              <For each={(new Array(16)).fill(0)}>
                {(_, idx) => {
                  return (<>
                    <VStack
                      w="100%"
                      h="100%"
                      spacing={"$4"}
                      marginTop={20}
                    >
                      {/* Current Value */}
                      <Box
                        __tooltip_title={`Value for channel: ${idx()}`}
                        fontSize={12} cursor="default"
                      >
                        {sliderValues[currentPreset()]?.[idx()]}
                      </Box>

                      <Box
                        id={`channel-parameters-slider-id-${idx()}`}
                        class={"noUi-target noUi-vertical"}
                        disabled
                        ref={(elem: HTMLElement) => onSliderMount(elem, idx())}
                        h="100%"
                      />

                      {/* Channel */}
                      <Box fontSize={12}>{idx()}</Box>

                      {/* Mute */}
                      <Checkbox
                        colorScheme={"accent"}
                        __tooltip_title={`Click here to mute channel: ${idx()}`}
                        checked={mutedChannels().includes(idx())}
                        onClick={(e: any) => {
                          let isChecked = mutedChannels().includes(idx());
                          (isChecked ? onUnmuteChannel : onMuteChannel)(idx());
                        }}
                        marginLeft={8}
                        marginBottom={10}
                      />
                    </VStack>
                  </>);
                }}
              </For>
            </HStack>
          </VStack>
        </ModalBody>
        <ModalFooter>
          <HStack spacing={"$2"} w="100%" justifyContent={"space-between"}>
            <Box fontSize={12} as="i">
              Note: The highlighted background animation per slider <br />
              represents the last Note-On activity, based on its velocity.
            </Box>
            <ButtonGroup variant={"outline"} size="sm">
              <Button disabled={mutedChannels().length == 0} onclick={() => {
                mutedChannels().forEach(onUnmuteChannel);
                setMutedChannels([]);
              }}>Unmute All</Button>
              <Button onclick={onReset}>Default</Button>
            </ButtonGroup>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default ChannelsSlidersModal;