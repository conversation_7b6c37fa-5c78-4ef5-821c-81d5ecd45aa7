import { Box, Button, Center, Icon, Modal, <PERSON>dal<PERSON>ody, Modal<PERSON>lose<PERSON><PERSON>on, <PERSON>dal<PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dalOverlay, VStack, createDisclosure } from "@hope-ui/solid";
import * as noUiSlider from 'nouislider';
import { FaSolidFileLines } from "solid-icons/fa";
import { Component, createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import { buttonConfirmSFX } from "~/directives/buttonsfx.directive";
import { AudioChannel } from "~/proto/midi-renditions";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import { AUDIO } from "~/util/const.common";
import { MIDI } from "~/util/const.midi";

const SetChannelVolumeModal: Component = () => {
  const DISPLAY_KEY = "SET_CHANNEL_VOLUME_MODAL";
  const { isO<PERSON>, onO<PERSON>, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const [channel, setChannel] = createSignal<AudioChannel>();
  const [volumeSlider, setVolumeSlider] = createSignal<noUiSlider.API>();
  const [currentValue, setCurrentValue] = createSignal(0);

  function closeModal() {
    audioService().setCurrentChannelToEdit();
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(async () => {
    let target = audioService().currentChannelToEdit();
    if (target != null) {
      let targetChannel = audioService().channels()[target];
      if (targetChannel) {
        setChannel(targetChannel);
        setCurrentValue(targetChannel.volume);
      }
    }
  });

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  const onSliderMount = (element: HTMLElement) => {
    if (element.className.includes("noUi-target")) return;

    let slider = noUiSlider.create(element, {
      orientation: 'horizontal',
      connect: true,
      start: currentValue(),
      range: {
        'min': 0,
        'max': MIDI.MAX_VOLUME
      },
    });

    slider.on("update", (values) => {
      let value = parseInt(values[0] as any);
      setCurrentValue(value);

      let _channel = channel();
      if (_channel) audioService().setChannelVolume(_channel.channel, value);
    });

    setVolumeSlider(slider);
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"outside"}
      size="lg"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidFileLines} /> {`Channel ${channel()?.channel || 0} - Volume`}
        </ModalHeader>
        <ModalBody padding={20}>
          <VStack spacing={"$2"} w="100%">
            <Box>Value: {currentValue()}</Box>
            <Box
              w="100%"
              id={`set-channel-volume-slider-id-${channel()?.channel || 0}`}
              class={"noUi-target noUi-horizontal"}
              ref={(elem: HTMLElement) => onSliderMount(elem)}
              h="100%"
            />
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Center w="100%">
            <Button
              ref={(el: HTMLElement) => buttonConfirmSFX(el)}
              disabled={currentValue() == AUDIO.DEFAULT_CHANNEL_VOLUME}
              onMouseDown={() => {
                volumeSlider()?.set(AUDIO.DEFAULT_CHANNEL_VOLUME);
              }}
              size="md" variant="outline"
            >Default</Button>
          </Center>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default SetChannelVolumeModal;