import { Box, Button, ButtonGroup, createDisclosure, HStack, Icon, Modal, ModalBody, ModalCloseButton, ModalContent, Modal<PERSON>ooter, ModalHeader, ModalOverlay, Select, SelectContent, SelectIcon, SelectListbox, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue, Tooltip, VStack } from "@hope-ui/solid";
import clsx from "clsx";
import * as noUiSlider from 'nouislider';
import { FaSolidEarListen } from "solid-icons/fa";
import { Component, createEffect, createSignal, For, onMount } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import AppSettingsService from "~/services/settings-storage.service";
import { AUDIO_REVERB_PRESETS, AudioReverb, AudioReverbPreset } from "~/types/audio.types";
import { arrayEquals, splitAndJoinWordsByCapitalLetter } from "~/util/helpers";

const AudioReverbModal: Component = () => {
  const DISPLAY_KEY = "AUDIO_REVERB_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const appSettingsServices = useService(AppSettingsService);

  const [currentPreset, setCurrentPreset] = createSignal<AudioReverbPreset>(
    AudioReverb.DEFAULT_PRESET
  );

  const ELEMENTS = ["Level", "Room Size", "Damp", "Width"];
  const NUM_BANDS = ELEMENTS.length;
  const MAX_RANGES = [1, 1.2, 1, 100];

  const [sliders, setSliders] = createSignal<noUiSlider.API[]>([]);
  const [sliderValues, setSliderValues] = createStore<number[]>([]);

  onMount(() => {
    let levels = appSettingsServices().getSetting<number>("AUDIO_REVERB_LEVEL");
    let roomsize = appSettingsServices().getSetting<number>("AUDIO_REVERB_ROOMSIZE");
    let damp = appSettingsServices().getSetting<number>("AUDIO_REVERB_DAMP");
    let width = appSettingsServices().getSetting<number>("AUDIO_REVERB_WIDTH");

    sliders().forEach((x, index) => {
      switch (index) {
        case 0: { if (levels != null) { x.set(levels); } break; }
        case 1: { if (roomsize != null) { x.set(roomsize); } break; }
        case 2: { if (damp != null) { x.set(damp); } break; }
        case 3: { if (width != null) { x.set(width); } break; }
      }
    });

    // Find the preset that matches the current settings
    let preset = AUDIO_REVERB_PRESETS.find(x => {
      let values = AudioReverb.FromPreset(x);
      return values.AUDIO_REVERB_LEVEL == levels &&
        values.AUDIO_REVERB_ROOMSIZE == roomsize &&
        values.AUDIO_REVERB_DAMP == damp &&
        values.AUDIO_REVERB_WIDTH == width;
    });
    if (preset) setCurrentPreset(preset);
  });

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  let updateTimeout = -1;
  const getReverbSettingValue = (index: number) => {
    switch (index) {
      case 0: return audioService().reverbLevel();
      case 1: return audioService().reverbRoomsize();
      case 2: return audioService().reverbDamp();
      case 3: return audioService().reverbWidth();
    }

    return -1;
  };

  const setReverbSettingValue = (index: number, value: number) => {
    switch (index) {
      case 0: return audioService().setReverbLevel(value);
      case 1: return audioService().setReverbRoomsize(value);
      case 2: return audioService().setReverbDamp(value);
      case 3: return audioService().setReverbWidth(value);
    }

    return -1;
  };

  const onPresetChange = (preset: AudioReverbPreset) => {
    if (preset == AudioReverbPreset.Custom) return;
    let presets = AudioReverb.FromPreset(preset);

    sliders().forEach((x, index) => {
      switch (index) {
        case 0: { x.set(presets.AUDIO_REVERB_LEVEL); break; }
        case 1: { x.set(presets.AUDIO_REVERB_ROOMSIZE); break; }
        case 2: { x.set(presets.AUDIO_REVERB_DAMP); break; }
        case 3: { x.set(presets.AUDIO_REVERB_WIDTH); break; }
      }
    });

    appSettingsServices().saveSetting("AUDIO_REVERB_LEVEL", presets.AUDIO_REVERB_LEVEL);
    appSettingsServices().saveSetting("AUDIO_REVERB_ROOMSIZE", presets.AUDIO_REVERB_ROOMSIZE);
    appSettingsServices().saveSetting("AUDIO_REVERB_DAMP", presets.AUDIO_REVERB_DAMP);
    appSettingsServices().saveSetting("AUDIO_REVERB_WIDTH", presets.AUDIO_REVERB_WIDTH);
    setCurrentPreset(preset);
  };

  let presetGains = AUDIO_REVERB_PRESETS.map(AudioReverb.FromPreset).map(x => [x.AUDIO_REVERB_LEVEL, x.AUDIO_REVERB_ROOMSIZE, x.AUDIO_REVERB_DAMP, x.AUDIO_REVERB_WIDTH]);

  const onSliderMount = (element: HTMLElement, index: number) => {
    if (element.className.includes("noUi-target")) return;

    let slider = noUiSlider.create(element, {
      orientation: 'vertical',
      direction: "rtl",
      connect: true,
      start: getReverbSettingValue(index),
      step: 0.01,
      range: {
        'min': 0,
        'max': MAX_RANGES[index] ?? 1,
      },
    });

    slider.on("update", (values) => {
      let value = parseFloat(values[0] as any);
      setReverbSettingValue(index, value);
      setSliderValues(index, value);

      clearTimeout(updateTimeout);
      updateTimeout = window.setTimeout(() => {
        let isCustom = !presetGains.some(f => arrayEquals(f, [...sliderValues]));
        if (isCustom) setCurrentPreset(AudioReverbPreset.Custom);
        appSettingsServices().persistSettings();
      }, 100);
    });

    setSliders(v => [...v, slider]);
  };

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"outside"}
      size="2xl"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidEarListen} /> Audio Reverb</ModalHeader>
        <ModalBody
          w="100%"
        >
          <VStack>
            <Box
              w="200px"
            >
              <Select
                defaultValue={AudioReverbPreset[AudioReverb.DEFAULT_PRESET]}
                value={AudioReverbPreset[currentPreset()]}
                onChange={(value: string) => {
                  onPresetChange(AudioReverbPreset[value as any] as any as AudioReverbPreset);
                }}
              >
                <SelectTrigger>
                  <SelectPlaceholder>{"Select a preset..."}</SelectPlaceholder>
                  <SelectValue />
                  <SelectIcon />
                </SelectTrigger>
                <SelectContent>
                  <SelectListbox>
                    <For each={AUDIO_REVERB_PRESETS.map(x => AudioReverbPreset[x])}>
                      {item => (
                        <SelectOption value={item} disabled={item === AudioReverbPreset[AudioReverbPreset.Custom]}>
                          <SelectOptionText>{splitAndJoinWordsByCapitalLetter(item)}</SelectOptionText>
                          <SelectOptionIndicator />
                        </SelectOption>
                      )}
                    </For>
                  </SelectListbox>
                </SelectContent>
              </Select>
            </Box>
            <HStack
              userSelect={"none"}
              w="100%"
              h={"300px"}
              overflow={"hidden"}
              overflowX="scroll"
              spacing={"$5"}
              padding={20}
            >
              <For each={(new Array(NUM_BANDS)).fill(0)}>
                {(_, idx) => {
                  return (<>
                    <VStack
                      w="100%"
                      h="100%"
                      spacing={"$5"}
                    >

                      <Box
                        __tooltip_title={`Gain value for channel: ${idx()}`}
                        __tooltip_placement="top"
                        fontSize={14} cursor="default"
                      >
                        <Box
                          marginLeft={-5}
                          w={20} textAlign={"center"} fontWeight={"bold"}>{sliderValues[idx()]}</Box>
                      </Box>
                      <Box
                        id={`audio-equalizer-slider-id-${idx()}`}
                        class={clsx(
                          [
                            "noUi-target",
                            "noUi-vertical",
                            (!audioService().reverbEnabled()) && "disabled"
                          ])
                        }
                        ref={(elem: HTMLElement) => onSliderMount(elem, idx())}
                        h="100%"
                      />
                      <Box fontSize={16}>
                        <Box>{ELEMENTS[idx()]}</Box>
                      </Box>
                    </VStack>
                  </>);
                }}
              </For>
            </HStack>
          </VStack>

        </ModalBody>
        <ModalFooter>
          <HStack spacing={"$2"} w="100%" justifyContent={"space-between"}>
            <Button
              variant={"outline"} size="sm"
              onmousedown={() => { audioService().setReverbEnabled(!audioService().reverbEnabled()); }}
            >
              <Box>{audioService().reverbEnabled() ? 'Disable' : 'Enable'}</Box>
            </Button>
            <ButtonGroup variant={"outline"} size="sm" disabled={!audioService().reverbEnabled()}>
              <Button onmousedown={() => { onPresetChange(AudioReverb.DEFAULT_PRESET); }}
              >Reset</Button>
            </ButtonGroup>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal >
  </>);
};

export default AudioReverbModal;