import { Box, Button, createDisclosure, <PERSON>ing, HStack, Icon, List, ListItem, Modal, ModalBody, ModalClose<PERSON>utton, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ModalOverlay } from "@hope-ui/solid";
import { FaSolidPlug } from "solid-icons/fa";
import { Component, createEffect, For, untrack } from "solid-js";
import { useService } from "solid-services";
import DisplaysService from "~/services/displays.service";
import WebMidiService from "~/services/webmidi.service";

type MidiListItemType = "Input" | "Output";

const MidiListItem: Component<{ name?: string, id: string, active: boolean, type: MidiListItemType; }> = (props) => {
  const webMidiService = useService(WebMidiService);

  return (
    <Box
      __tooltip_title={<Box>Click to <Box as="span"
        fontWeight={"bold"}
        color={props.active ? "red" : "lightgreen"}
      >{props.active ? "inactivate" : "activate"}
      </Box> the port!</Box>}
      __tooltip_placement={"right"}
      __tooltip_open_delay={600}
      __tooltip_open_on_focus={false}
      cursor={"pointer"}
      transition={"transform 0.15s ease, filter 0.5s ease"}
      _hover={{
        "@lg": {
          transform: "scale(1.025)",
          filter: "brightness(1.1)",
        }
      }}
      onMouseDown={() => {
        const func = props.type == "Input" ? webMidiService().toggleInput : webMidiService().toggleOutput;
        func(props.id);
      }}
      border={"solid 3px white"} padding={7}
      background={props.active ? "$accent1" : "none"}>
      {props.name}
    </Box>
  );
};

const MidiIOModal: Component = () => {
  const DISPLAY_KEY = "MIDI_IO_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const webMidiService = useService(WebMidiService);

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      // Refresh midi ports
      untrack(async () => {
        await webMidiService().refreshMidiState();
      });
      onOpen();
    } else { onClose(); }
  });

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"inside"}
    >
      <ModalOverlay />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidPlug} /> Midi I/O
        </ModalHeader>
        <ModalBody
          paddingTop={20}
          paddingBottom={20}
        >
          <HStack spacing={"$2"}>
            <Heading>MIDI Inputs</Heading>
            <Button
              __tooltip_title={"Inactivate all MIDI inputs"}
              size="xs"
              fontSize={"10px"}
              variant="outline"
              onClick={() => {
                webMidiService().midiInputs().forEach((input) => {
                  webMidiService().setInputActive(input.id, false, true);
                });
              }}>Inactivate All</Button>
            <Button
              __tooltip_title={"Activate all MIDI inputs"}
              size="xs"
              fontSize={"10px"}
              variant="outline"
              onClick={() => {
                webMidiService().midiInputs().forEach((input) => {
                  webMidiService().setInputActive(input.id, true, true);
                });
              }}>Activate All</Button>
          </HStack>
          <List spacing="$2">
            <For each={webMidiService().midiInputs()}>{(input) =>
              <ListItem>
                <MidiListItem type="Input" id={input.id} name={input.type.name} active={input.active} />
              </ListItem>
            }
            </For>
          </List>
          <br />
          <HStack spacing={"$2"}>
            <Heading>MIDI Outputs</Heading>
            <Button
              __tooltip_title={"Inactivate all MIDI outputs"}
              size="xs"
              fontSize={"10px"}
              variant="outline"
              onClick={() => {
                webMidiService().midiOutputs().forEach((output) => {
                  webMidiService().setOutputActive(output.id, false);
                });
              }}>Inactivate All</Button>
            <Button
              __tooltip_title={"Activate all MIDI outputs"}
              size="xs"
              fontSize={"10px"}
              variant="outline"
              onClick={() => {
                webMidiService().midiOutputs().forEach((output) => {
                  webMidiService().setOutputActive(output.id, true);
                });
              }}>Activate All</Button>
          </HStack>
          <List spacing="$2">
            <For each={webMidiService().midiOutputs()}>{(output) =>
              <ListItem>
                <MidiListItem type="Output" id={output.id} name={output.type.name} active={output.active} />
              </ListItem>
            }
            </For>
          </List>
        </ModalBody>
        <ModalFooter justifyContent={"center"}>
          {!webMidiService().hasMidiPermission() &&
            <Box
              fontSize={"0.8em"}
              color={"$warning10"}
              textAlign={"center"}
              padding={0}
            >Web midi devices disabled. Please allow MIDI access in your browser settings.</Box>
          }
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default MidiIOModal;