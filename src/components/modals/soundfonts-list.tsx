import { Box, Divider, Icon, Input, List, Modal, ModalBody, ModalCloseButton, Modal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON>eader, ModalOverlay, Tooltip, VStack, createDisclosure } from "@hope-ui/solid";
import isEqual from "lodash-es/isEqual";
import { FaSolidMusic } from "solid-icons/fa";
import { Component, For, createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import { MenuContentSelectItemMeta } from "./settings-content/common.content";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import { Soundfonts } from "~/types/audio.types";

const [loadingSF, setLoadingSF] = createSignal(false);

const ListItem: Component<{
  id: string;
  meta?: MenuContentSelectItemMeta;
  active?: boolean;
}> = (props) => {
  const audioService = useService(AudioService);
  const [loading, setLoading] = createSignal(false);
  const [isLoaded, setIsLoaded] = createSignal(false);

  createEffect(() => {
    setIsLoaded(audioService().loadedSoundfont()?.toLowerCase() === props.id.toLowerCase());
  });

  async function tryAndLoadSoundfont(soundfont: string, isCustomSoundfont = false, cacheFile = false) {
    return audioService()
      .loadSoundfont(soundfont, isCustomSoundfont, cacheFile)
      .finally(() => {
        setLoading(false);
        setLoadingSF(false);
      });
  }

  return (
    <Box
      __tooltip_title={isLoaded() ? "Soundfont loaded." : "Click to load the soundfont."}
      cursor={"pointer"}
      transition={"transform 0.1s ease, filter 0.5s ease"}
      disabled={loadingSF()}
      opacity={loadingSF() ? 0.5 : 1}
      _hover={{
        "@lg": { transform: "scale(1.05)", border: "solid 3px $accent1" }
      }}
      onMouseDown={async () => {
        if (isLoaded()) return;
        let value = props.id;

        setLoading(true);
        setLoadingSF(true);

        let _meta = props.meta;

        // If the soundfont is less than 5mb, we don't cache it.
        let cacheFile = (_meta && _meta.rawSize && parseFloat(_meta.rawSize) < 5 * 1024 * 1024) || false;

        try {
          await tryAndLoadSoundfont(value, false, cacheFile);
        } catch (ex) {
          console.error(ex);
          // let prev_loaded = loadedSoundfont();
          // window.clearTimeout(prevLoadedSfTimeout);
          // prevLoadedSfTimeout = window.setTimeout(() => setLoadedSoundfont(prev_loaded), 1000);
        }
      }}
      border={"solid 3px white"}
      padding={7}
      background={props.active ? "$accent1" : "none"}
    >
      <VStack>
        <Box fontWeight={"$bold"}>
          {props.id}
        </Box>
        <Box fontSize={"$sm"} color={props.active ? "black" : "lightgray"}>
          {props.meta?.tagLine}
        </Box>
      </VStack>
    </Box>

  );
};

const SoundfontsList = () => {
  const DISPLAY_KEY = "SOUNDFONTS_LIST_MODAL";

  const displayService = useService(DisplaysService);
  const audioService = useService(AudioService);
  const [soundfontOptions, setSoundfontOptions] = createSignal<string[]>([]);
  const [displayedSoundfonts, setDisplayedSoundfonts] = createSignal<string[]>([]);
  const [soundfontMeta, setSoundfontMeta] = createSignal<MenuContentSelectItemMeta[]>([], { equals: isEqual });
  const [loadedSoundfont, setLoadedSoundfont] = createSignal<string>();
  const [filteredText, setFilteredText] = createSignal("");

  const handleInput = (event: InputEvent) => {
    const text: string = (event.target as any).value;
    setFilteredText(text.toLowerCase());
  };

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(() => {
    let filter = filteredText();

    setDisplayedSoundfonts(soundfontOptions().filter(x =>
      x.toLowerCase().includes(filter.toLowerCase())
    ));
  });

  createEffect(() => {
    let set = new Set(Soundfonts.keys());
    let currentSFName = audioService().loadedSoundfontName();
    setLoadedSoundfont(currentSFName);

    setSoundfontMeta(
      [
        ...Array.from(Soundfonts).map(([id, details]) => {
          return {
            id: id, tagLine: `Size: ${details.size}`, rawSize: details.size
          };
        })
      ].filter(Boolean)
    );

    if (currentSFName && !set.has(currentSFName)) {
      set.add(currentSFName);
      //@ts-ignore
      setSoundfontMeta(v => [...v, { id: currentSFName, tagLine: `Custom` }]);
    }

    setSoundfontOptions(Array.from(set));
  });

  return (<>
    <Modal
      centered
      opened={true}
      onClose={closeModal}
      scrollBehavior={"inside"}
    >
      <ModalOverlay />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidMusic} /> Soundfonts
        </ModalHeader>
        <ModalBody
          paddingTop={20}
          paddingBottom={20}
          h="100%"
        >
          <Input
            border={"solid 2px white"}
            placeholder="Search by name"
            onInput={handleInput}
            type="text"
            marginBottom={15}
          />
          <Divider width={"100%"} marginBottom={15} />
          <List spacing="$3">
            <For each={displayedSoundfonts()}>
              {(sf) => <ListItem
                id={sf}
                meta={soundfontMeta().find((m) => m.id === sf)}
                active={loadedSoundfont() === sf}
              />}
            </For>
          </List>
        </ModalBody>
        <ModalFooter>
          <Box fontSize={"small"} color={"lightgray"}>
            {"To load your own soundfont (.sf2) file, go to 'Settings > Soundfont' and click on the 'Load Custom Soundfont' button."}
          </Box>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default SoundfontsList;