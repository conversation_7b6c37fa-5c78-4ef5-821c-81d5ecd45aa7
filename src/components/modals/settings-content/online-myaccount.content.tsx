import { <PERSON>, <PERSON>ton, ButtonGroup, Center, HStack, Input, VStack } from "@hope-ui/solid";
import ColorPicker from 'simple-color-picker';
import { Component, ParentComponent, createEffect, createMemo, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import UserProfileImage from "~/components/user-profile-image";
import UserProfileImageUploader from "~/components/user-profile-image-uploader";
import { ApiUserRecordProvider } from "~/contexts/user.context";
import { rolesToJSON } from "~/proto/user-renditions";
import { deleteMemberAccount } from "~/server/general.api";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import SoundEffectsService from "~/services/sound-effects.service";
import WebsocketService from "~/services/websocket.service";
import { USER_INPUT, VALIDATIONS } from "~/util/const.common";
import { obscureEmail } from "~/util/helpers";
import SwalPR from "~/util/sweetalert";
import { MenuContentButton, MenuContentSwitch, MenuGeneralSubSection } from "./common.content";

const _width = 215;
const hex_pattern = "^#(?:[0-9a-f]{3}){1,2}$";
const colorHexReg = new RegExp(hex_pattern, "i");
const testColor = (value?: string) => value == null ? false : colorHexReg.test(value);

const InfoTitle: Component<{ text: string; }> = (props) => <Box color="$neutral11" textTransform={"uppercase"}>{props.text}</Box>;
const Info: ParentComponent<{ title: string; }> = (props) =>
  <VStack alignItems={"flex-start"} w="100%"><InfoTitle text={props.title} />
    <Box fontWeight={"bold"} w="100%">{props.children}</Box>
  </VStack>;

const [email, setEmail] = createSignal<string>();
const [revealEmail, setRevealEmail] = createSignal(false);

const AccountEmail: Component = () => {
  const appService = useService(AppService);
  const _canChangeEmail = () => !appService().client().isOAuthAccount;

  return (
    <HStack spacing={"$4"} w="100%" justifyContent={"space-between"}>
      <Box
        __tooltip_title={`Click to ${revealEmail() ? "hide" : "reveal"} email...`}
        onClick={() => { setRevealEmail(v => !v); }}
        _hover={{
          "color": "$accent1",
          "textDecorationLine": "underline",
          "cursor": "pointer"
        }}
      >{email()}</Box>
      {/* <Button onclick={onClick} disabled={!props.canChangeEmail()} size={"xs"} variant="subtle">Edit</Button> */}
    </HStack>
  );
};

const AccountProfileInfo: Component = () => {
  const appService = useService(AppService);
  const [showImageUploader, setShowImageUploader] = createSignal(false);
  const canUploadImages = () => appService().isClientMember();

  const usertag = createMemo(() => appService().client().getClientSideUserDto().userDto?.usertag);

  createEffect(() => {
    const client = appService().client();
    const emailValue = client.email ? (revealEmail() ? client.email : obscureEmail(client.email)) : undefined;
    setEmail(emailValue);
  });

  onCleanup(() => { setShowImageUploader(false); });

  return (<>
    <Box w={"100%"} background="$primaryDark1" borderRadius={5}>
      <HStack w={"100%"} h="100%" spacing={"$2"} padding={10}>
        <Center w={100} h="100%"
          transition={"filter 0.3s ease"}
        // _hover={{ "filter": canUploadImages() ? "blur(0.8px) brightness(0.6)" : "" }}
        >
          <Box
            __tooltip_open_delay={150}
            __tooltip_title="Click to upload a profile image!"
            // @ts-ignore
            disabled={!canUploadImages()}
          >
            <ApiUserRecordProvider value={{ socketID: appService().client().socketID, default: appService().client().toApiUserDto() }}>
              <UserProfileImage height={100} width={100}
                onClick={() => { if (canUploadImages()) setShowImageUploader(true); }}
              />
            </ApiUserRecordProvider>
          </Box>
        </Center>

        <Box background="$primary1" w="100%" h={"100%"} borderRadius={5}>
          <VStack alignItems={"flex-start"} spacing="$2" fontSize={12} padding={10} w="100%">
            {usertag() && <Info title="username">{usertag()}</Info>}
            {email() && <Info title="email"> <AccountEmail /></Info>}
            <Info title="Roles">{appService().client().getRoles().map(rolesToJSON).join(", ")}</Info>
          </VStack>
        </Box>
      </HStack>
    </Box>

    {(canUploadImages() && showImageUploader()) &&
      <UserProfileImageUploader setShow={setShowImageUploader} />
    }
  </>);
};

const AccountColorInfo: Component = () => {
  const appService = useService(AppService);
  const websocketService = useService(WebsocketService);
  const [loading, setIsLoading] = createSignal(false);
  const [canSetRainbow, setCanSetRainbow] = createSignal(false);
  const [color, setColor] = createSignal<string>();
  const [colorPicker, setColorPicker] = createSignal<ColorPicker>();
  const [initialColor] = createSignal(appService().client().usercolor || "#000000");
  const colorsTheSame = () => color()?.toLowerCase() == initialColor().toLowerCase();
  const isRainbowColor = () => color()?.toLowerCase() == "rainbow";
  let colorPickElement!: HTMLDivElement;

  onMount(() => {
    setColor(initialColor);

    const _colorPicker = new ColorPicker({
      color: initialColor(),
      el: colorPickElement
    });

    _colorPicker.onChange(() => { setColor(_colorPicker.getHexString()); });
    setColorPicker(_colorPicker);
    setCanSetRainbow(appService().client().canUseProFeature);
  });

  return (<>
    <MenuGeneralSubSection label="Profile Color">
      <VStack spacing={"$2"} alignItems="flex-start">
        <Box ref={colorPickElement} background="$primaryDark1" w={_width} padding={10} borderRadius={10}></Box>

        <HStack width={_width} height={20} background={color()} borderRadius={5}>
          <Box
            className={isRainbowColor() ? "rainbow-text" : ""}
            w="100%" textAlign={"center"}>{color()}</Box>
        </HStack>

        <Input
          pattern={hex_pattern}
          maxlength={7}
          minlength={4}
          disabled={isRainbowColor()}
          type="text"
          onKeyUp={(event: KeyboardEvent) => {
            colorPicker()?.setColor((event.target as any).value);
          }}
          value={color()}
          w={_width} placeholder={"color"}></Input>

        <ButtonGroup size="sm" variant="outline">
          {/* Color Reset */}
          <Button
            onclick={() => colorPicker()?.setColor(initialColor())}
            disabled={colorsTheSame() || loading()}>Reset Color</Button>

          {/* Color Submit */}
          <Button
            onclick={async () => {
              setIsLoading(true);
              let _color = color();
              if (_color && (isRainbowColor() || testColor(_color))) {
                websocketService().emitUserUpdateCommand(["Color", _color.toLowerCase()]);
              }
              setIsLoading(false);
            }}
            loadingText="Submitting..."
            loading={loading()}
            disabled={appService().serverServiceDown() || colorsTheSame() || loading() || !testColor(color()) && !isRainbowColor()}
          >Submit Color</Button>
        </ButtonGroup>

        {/* Set Rainbow Color */}
        {canSetRainbow() && <Button
          w={_width}
          onclick={() => {
            colorPicker()?.setColor("#000");
            setColor("rainbow");
          }}
          variant="outline"
          disabled={isRainbowColor() || loading()}>Set Rainbow Color</Button>
        }
      </VStack>
    </MenuGeneralSubSection>
  </>);
};

const MyAccountContent = () => {
  const category = "online";
  const menu = "my account";
  const appService = useService(AppService);
  const websocketService = useService(WebsocketService);
  const sfxService = useService(SoundEffectsService);
  const i18nService = useService(I18nService);
  const displayService = useService(DisplaysService);
  const isOAuthAccount = createMemo(() => appService().client().getDto().isOAuthAccount);

  const deleteAccountQueue = SwalPR(sfxService).mixin({
    progressSteps: ["1", "2", !isOAuthAccount() ? "3" : ""].filter(Boolean),
    showClass: { backdrop: 'swal2-noanimation' },
    hideClass: { backdrop: 'swal2-noanimation' }
  });

  const transElement = (input: string) =>
    i18nService().t_roomPageSettingSubMenuElement(category, menu, input);

  const cleanupSwal = () => {
    // delete (window as any)["Swal"];
  };

  const onDeleteAccount = async (password?: string) => {
    try {
      let result = await deleteMemberAccount(password, isOAuthAccount());
      console.log("deleteMemberAccount", result);
      if (!result) throw new Error("Failed to delete account.");

      deleteAccountQueue.fire({
        icon: "success",
        currentProgressStep: 1,
        didClose: () => appService().onDisconnect(),
        html: `
          You've successfully deleted your account!<br><br>
          After this, your current session will end and you'll
          be directed to the login screen.
        `
      });

      return true;
    } catch (ex) {
      deleteAccountQueue.showValidationMessage("Something went wrong with the request.");
    }

    return false;
  };

  return (<>
    <MenuGeneralSubSection label="My Account" >
      <AccountProfileInfo />
      <AccountColorInfo />
    </MenuGeneralSubSection>

    {/* TODO: Fix this */}
    {/* <MenuGeneralSubSection label="General" >
      <MenuContentSwitch
        skipTranslate settingsKey="ALLOW_USERS_TO_NOTIFY_ME" category={category} menu={menu}
        tooltip="Toggle whether you get notifications when users @ a.k.a notify you, in a message."
        elementLabel="Allow users to notify me"
        onChange={async (value) => {
          if (!value) {
            // globalService().setNotificationPermissionGranted(false);
            return;
          }
          // await globalService().handleDesktopNotifications();
        }}
      />
    </MenuGeneralSubSection> */}

    <MenuGeneralSubSection label="Email Notifications" >
      <MenuContentSwitch
        category={category} menu={"multiplayer"}
        defaultValue={appService().userNotificationSettings().getEmailsForSheetMusicChanges}
        tooltip="tgl_sheetMusicChanges"
        elementLabel="tgl_sheetMusicChanges"
        onChange={async (value) => {
          websocketService().emitUserUpdateCommand(["NotificationSetting", ["GetSheetMusicEmails", value]]);
          setTimeout(() => {
            appService().setUserNotificationSettings(x => ({ ...x, getEmailsForSheetMusicChanges: value }));
          }, 1000);
        }}
      />

      <MenuContentSwitch
        category={category} menu={"multiplayer"}
        defaultValue={appService().userNotificationSettings().getNotificationsForFriendsComingOnline}
        tooltip="tgl_friendsOnlineStatus"
        elementLabel="tgl_friendsOnlineStatus"
        onChange={async (value) => {
          websocketService().emitUserUpdateCommand(["NotificationSetting", ["FriendsOnline", value]]);
          setTimeout(() => {
            appService().setUserNotificationSettings(x => ({ ...x, getNotificationsForFriendsComingOnline: value }));
          }, 1000);
        }}
      />

      <MenuContentSwitch
        category={category} menu={"multiplayer"}
        defaultValue={appService().userNotificationSettings().allowEmittingMyStatusToFriends}
        tooltip="tgl_emitMyStatusToFriends"
        elementLabel="tgl_emitMyStatusToFriends"
        onChange={async (value) => {
          websocketService().emitUserUpdateCommand(["NotificationSetting", ["EmitMyStatusToFriends", value]]);
          setTimeout(() => {
            appService().setUserNotificationSettings(x => ({ ...x, allowEmittingMyStatusToFriends: value }));
          }, 1000);
        }}
      />
    </MenuGeneralSubSection>

    <MenuGeneralSubSection label="Advanced" >
      {appService().isClientMember() &&
        <MenuContentButton
          elementLabel="Delete Account"
          skipTranslate
          backgroundColor={"red"}
          tooltip={`This will trigger the process to permanently delete your PianoRhythm account.`}
          onClick={async () => {
            SwalPR(sfxService).fire({
              title: "Delete your account",
              html: `
              <p>We're sorry to see you go!</p><br>
              <p>Please note that this process is final and there'll be no way to restore your account.</p><br>
              <p>Any active subscriptions will also be canceled.</p>
            `,
              showCancelButton: true,
              showCloseButton: true,
              confirmButtonColor: "red",
              cancelButtonText: "Never mind, keep my account",
              confirmButtonText: "Delete my account"
            }).then(async (result) => {
              if (result.isConfirmed) {
                displayService().setDisplay("SETTINGS_MODAL", false);
                deleteAccountQueue.fire({
                  title: "Confirmation Required",
                  currentProgressStep: 0,
                  html: `
                    <p>${transElement("txt_deleteAccountSubText")}.</p><br><br>
                    <label style='width:100%' for="swal-input1" class="pr-changepassword-label">Type <b style='color:red'>DELETE</b> to confirm</label><br>
                    <input
                      id="swal-input1"
                      autocomplete="off"
                      class="swal2-input pr-modal-text-input"
                    required >
                  `,
                  showCloseButton: true,
                  showCancelButton: true,
                  showLoaderOnConfirm: true,
                  allowEscapeKey: false,
                  allowOutsideClick: false,
                  preConfirm: async () => {
                    let inputValue = document.querySelector<HTMLInputElement>(`#swal-input1`)?.value;
                    if (inputValue?.toLowerCase().trim() == "delete") {
                      if (isOAuthAccount()) return await onDeleteAccount();
                      return true;
                    } else {
                      SwalPR(sfxService).showValidationMessage(transElement("txt_onIncorrectDeleteInputField"));
                    }
                  }
                }).then(async (result) => {
                  if (result.isConfirmed && result.value === true) {
                    (window as any)["Swal"] = SwalPR();
                    deleteAccountQueue.fire({
                      title: "Account Deletion",
                      currentProgressStep: 1,
                      html: `
                        <br>
                        <div class="pr-deleteaccount-subtext">${transElement("txt_confirmPasswordSubText")}</div>
                        <input
                          onchange="Swal.resetValidationMessage()"
                          id="swal-input1"
                          name="password"
                          type="password"
                          placeholder="enter password"
                          class="swal2-input pr-modal-text-input"
                          minlength="${USER_INPUT.MinPasswordLength}"
                          maxlength="${USER_INPUT.MaxPasswordLength}"
                          pattern="${VALIDATIONS.passwordValidationRegex(USER_INPUT.MinPasswordLength, USER_INPUT.MaxPasswordLength)}"
                        required >
                        <input
                          onchange="Swal.resetValidationMessage()"
                          id="swal-input2"
                          name="password"
                          type="password"
                          placeholder="confirm password"
                          class="swal2-input pr-modal-text-input"
                          minlength="${USER_INPUT.MinPasswordLength}"
                          maxlength="${USER_INPUT.MaxPasswordLength}"
                          pattern="${VALIDATIONS.passwordValidationRegex(USER_INPUT.MinPasswordLength, USER_INPUT.MaxPasswordLength)}"
                        required >
                      `,
                      showCloseButton: true,
                      showCancelButton: true,
                      showLoaderOnConfirm: true,
                      allowEscapeKey: false,
                      allowOutsideClick: false,
                      preConfirm: async () => {
                        let currentPassword = document.querySelector<HTMLInputElement>(`#swal-input1`)?.value;
                        let confirmPassword = document.querySelector<HTMLInputElement>(`#swal-input2`)?.value;

                        if (!currentPassword || !confirmPassword) {
                          return SwalPR(sfxService).showValidationMessage(transElement("txt_onIncorrectDeleteInputField"));
                        }

                        let regex = RegExp(VALIDATIONS.passwordValidationRegex(USER_INPUT.MinPasswordLength, USER_INPUT.MaxPasswordLength));
                        let o1 = regex.test(currentPassword);
                        let o2 = regex.test(confirmPassword);
                        if (o1 && o2 && currentPassword == confirmPassword) {
                          return await onDeleteAccount(currentPassword);
                        }

                        SwalPR(sfxService).showValidationMessage("Please make sure both fields are valid.");
                      }
                    });
                  }
                }).finally(cleanupSwal);
              }
            })
              .finally(cleanupSwal);;
          }}
        />
      }
    </MenuGeneralSubSection>
  </>);
};

export default MyAccountContent;