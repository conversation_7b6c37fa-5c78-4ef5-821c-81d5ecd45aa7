import { invoke } from "@tauri-apps/api/core";
import { Component, createEffect, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import { MenuContentSelect, NOTES } from "./common.content";
import AppSettingsService from "~/services/settings-storage.service";
import { COMMON } from "~/util/const.common";

const AudioDevicesMenuSelect: Component<{ showOnlySelect?: boolean; }> = (props) => {
  const appSettingsService = useService(AppSettingsService);
  let [devices, setDevices] = createSignal<string[]>([]);
  let [defaultDevice, setDefaultDevice] = createSignal<string | undefined>(appSettingsService().getLocalStorage("lastSavedAudioDevice"));

  onMount(async () => {
    if (!COMMON.IS_DESKTOP_APP) return;
    let available = await invoke<{}>("list_available_devices");
    let output: string[] = Object.values(available || {} as {});
    setDevices(output);
  });

  createEffect(async () => {
    if (!COMMON.IS_DESKTOP_APP) return;
    let defaultAudioDevice = await invoke<string>("get_default_output_device").catch(() => console.error("Default Output device not found..."));
    if (!defaultDevice() && defaultAudioDevice) setDefaultDevice(defaultAudioDevice);
  });

  return (<>
    <MenuContentSelect
      label="Audio Devices"
      placeholder="Select a device"
      options={devices()}
      showOnlySelect={props.showOnlySelect}
      defaultValue={defaultDevice()}
      onSelected={async (value) => {
        appSettingsService().setLocalStorage("lastSavedAudioDevice", value);
      }}
      tooltip={`
        The target audio device. <br><br>
        ${NOTES.REQUIRES_RELOAD}
      `}
    />
  </>);
};

export default AudioDevicesMenuSelect;