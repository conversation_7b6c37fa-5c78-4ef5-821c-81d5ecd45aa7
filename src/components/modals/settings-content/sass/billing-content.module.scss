.billing-component-container {
  display: flex;
  padding: 10px;
  flex-direction: column;
}

.billing-products-display {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  padding: 15px;
}

.billing-footer-container {
  width: 100%;
  display: flex;
  justify-content: center;

  .button {
    margin-right: 10px;
  }
}

.billing-fetching-loader {
  width: 100%;
  height: 300px;
  position: relative;
}

$box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.2), 0 1px 1px 0 rgba(0, 0, 0, 0.14),
    0 2px 1px -1px rgba(0, 0, 0, 0.12);

.billing-toggler-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .toggle, .toggler {
    display: inline-block;
    vertical-align: middle;
    margin: 10px;
  }

  .toggler {
    color: #ddd;
    transition: .2s;
    font-weight: bold;
    opacity: 0.3;
  }

  .toggler--is-active {
    opacity: 1;
    color: var(--hope-colors-accent1)
  }

  .b {
    display: block;
  }

  .toggle {
    position: relative;
    width: 80px;
    height: 35px;
    border-radius: 100px;
    background-color: var(--hope-colors-primaryDark1);
    overflow: hidden;
    box-shadow: inset 0 0 2px 1px rgba(0, 0, 0, 0.05);
  }

  .check {
    position: absolute;
    display: block;
    cursor: pointer;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    z-index: 6;
  }

  .check:checked ~ .switch {
    right: 2px;
    left: 57.5%;
    transition: 0.1s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    transition-property: left, right;
    transition-delay: .08s, 0s;
  }

  .switch {
    position: absolute;
    left: 2px;
    top: 2px;
    bottom: 2px;
    right: 57.5%;
    background-color: var(--hope-colors-accent1);
    border-radius: 36px;
    z-index: 1;
    transition: 0.1s cubic-bezier(0.785, 0.135, 0.15, 0.86);
    transition-property: left, right;
    transition-delay: 0s, .08s;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.pricing-table {
  display: flex;
  flex-direction: column;
  margin: 0 0 8px;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: $box-shadow;
  height: 350px;
  background: rgba(0, 0, 0, 0.3);
  transition: opacity 0.2s ease, background 0.2s ease, transform 0.1s ease-in-out;

  &.offer {
    box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14),
        0 6px 30px 5px rgba(0, 0, 0, 0.12),
        0 8px 10px -5px rgba(0, 0, 0, 0.4);
  }

  .package-title {
      display: flex;
      justify-content: center;
      padding: 16px 24px;
      background-color: var(--hope-colors-primaryDark1);
      font-size: 1.125rem;
      color: #fff;
      text-transform: uppercase;
      position: relative;

      .package-selected {
        background-color: var(--hope-colors-accent1);
        float: left;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
      }

      .sale {
        background-color: var(--hope-colors-accent5);
        position: absolute;
        top: 50px;
        right: 10px;
        padding: 0 0.5rem;
        border-radius: 3px;
        // background-color: #fff;
        font-size: 0.8125rem;
        font-weight: 700;
        line-height: 1.7;
      }
  }

  .package-layout {
      display: flex;
      justify-content: center;
      padding: 1rem 2rem 1rem;

      .package-currency {
          padding-right: 4px;
          font-size: 1.5rem;
          font-weight: 500;
          // @include theme('color', text-color);
      }

      .package-value {
          display: flex;
          align-items: flex-end;
          // @include theme('color', text-color);

          .value {
              font-size: 4.5rem;
              font-weight: 300;
              line-height: 1;
          }

          .period {
              padding: 0 0 5px 4px;
              font-size: 1.125rem;
              font-weight: 300;
              color: gray;
          }
      }
  }

  .terms {
      display: flex;
      flex-direction: column;
      padding: 1rem 1.5rem;
      font-size: 0.9125rem;
      // @include theme('color', text-color);
      overflow-y: scroll;
      height: 300px;

      .term + .term {
        margin-top: 10px;
      }

      .term {
          span {
              font-weight: 700;
          }
      }
  }

  .subscribe {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 10px;
    padding: 15px 18px;
    min-width: 128px;
    border: none;
    border-radius: 3px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    background-color: var(--hope-colors-primaryDark1);
    font-size: 0.875rem;
    font-weight: 500;
    color: #fff;
    line-height: 39px;
    text-transform: uppercase;
    overflow: hidden;
    will-change: box-shadow;
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 1, 1),
        background-color 0.2s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    cursor: pointer;
    text-decoration: none;
    text-align: center;
    vertical-align: middle;
    -webkit-tap-highlight-color: transparent;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);

    &:disabled {
      background-color: var(--hope-colors-primaryLight)
    }
  }

  .subscribe.--is-selected {
    background-color: var(--hope-colors-accent5);
  }

  .divider {
      display: block;
      border-top: 1px solid rgba(255, 255, 255, 0.25);
      margin: 8px 16px;
  }
}

// .pricing-table.--is-selected {
//   border: solid 2px var(--hope-colors-tertiary1)
// }