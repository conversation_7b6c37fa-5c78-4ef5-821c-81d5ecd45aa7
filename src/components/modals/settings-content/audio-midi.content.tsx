import AudioService from "~/services/audio.service";
import { AUDIO, COMMON_MESSAGES } from "~/util/const.common";
import { useService } from "solid-services";
import { MenuContentSlider, MenuContentSwitch, MenuGeneralSubSection, MenuSectionWithSearchFilter } from "./common.content";
import clamp from "lodash-es/clamp";

const AudioMidiContent = () => {
  const category = "audio";
  const menu = "midi";
  const audioService = useService(AudioService);

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection label="General" >
        <MenuContentSwitch settingsKey="AUDIO_ENABLE_VEL" category={category} menu={menu} tooltip="tgl_enableVelocity" elementLabel="tgl_enableVelocity" />
        <MenuContentSlider
          label={`max velocity: ${audioService().maxVelocity()}`}
          tooltip="Determines the max velocity of your midi notes."
          settingsKey="AUDIO_MAX_VELOCITY"
          id={`${category}-${menu}-max-velocity-slider`}
          value={clamp(audioService().maxVelocity(), 1, AUDIO.MAX_VELOCITY)}
          step={1}
          minValue={1}
          maxValue={AUDIO.MAX_VELOCITY} />
        <MenuContentSlider
          label={`min velocity: ${audioService().minVelocity()}`}
          tooltip="Determines the minimum velocity of your midi notes."
          settingsKey="AUDIO_MIN_VELOCITY"
          id={`${category}-${menu}-min-velocity-slider`}
          value={clamp(audioService().minVelocity(), AUDIO.MIN_VELOCITY, AUDIO.MAX_VELOCITY)}
          step={1}
          minValue={AUDIO.MIN_VELOCITY}
          maxValue={AUDIO.MAX_VELOCITY} />
        <MenuContentSlider
          label={`max multi mode channels: ${audioService().maxMultiModeChannels()}`}
          tooltip="Determines the max amount channels to enable during multi mode."
          settingsKey="AUDIO_MULTIMODE_MAX_CHANNELS"
          id={`${category}-${menu}-max-multimode-channels-slider`}
          value={clamp(audioService().maxMultiModeChannels(), 1, AUDIO.MAX_CHANNEL + 1)}
          step={1}
          minValue={1}
          maxValue={AUDIO.MAX_CHANNEL + 1} />
        <MenuContentSwitch settingsKey="MIDI_LISTEN_TO_PROGRAM_CHANGES" category={category} menu={menu} tooltip="tgl_listenToProgramChanges" elementLabel="tgl_listenToProgramChanges" />
        <MenuContentSwitch settingsKey="MIDI_AUTO_FILL_EMPTY_CHANNELS" category={category} menu={menu} tooltip="tgl_autoFillEmptyChannels" elementLabel="tgl_autoFillEmptyChannels" />
        <MenuContentSwitch settingsKey="MIDI_USE_DEFAULT_BANK_WHEN_MISSING" category={category} menu={menu} tooltip="tgl_useDefaultBankWhenMissing" elementLabel="tgl_useDefaultBankWhenMissing" />
        <MenuContentSwitch
          skipTranslate
          settingsKey="AUDIO_OUTPUT_OWN_NOTES_TO_MIDI"
          category={category} menu={menu}
          tooltip="If enabled, your own notes will be emitted to your MIDI Output devices."
          elementLabel="Output own notes to MIDI Outputs"
        />
        <MenuContentSwitch
          skipTranslate
          settingsKey="AUDIO_USE_VELOCITY_CURVE"
          category={category} menu={menu}
          tooltip="If enabled, applies a quadratic curve to the velocity of your MIDI notes. Emphasizes lower velocities."
          elementLabel="Use Velocity Curve"
        />
        <MenuContentSwitch settingsKey="AUDIO_ENABLE_DRUM_CHANNEL" category={"audio"} menu={"soundfont"} tooltip="tgl_enableDrums" elementLabel="tgl_enableDrums" />

      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Multiplayer" >
        <MenuContentSlider
          label={`Global User Velocity Percentage: ${audioService().globalVelocityPercentage()}%`}
          tooltip={
            `
              This controls the global total percentage of the raw velocity values that users emit onto your client.
              ${COMMON_MESSAGES.AUDIO_VELOCITY_PERCENTAGE_DESCRIPTION}
            `
          }
          settingsKey="AUDIO_GLOBAL_USERS_VELOCITY_PERCENTAGE"
          id={`${category}-${menu}-max-global-velocity-user-percentage`}
          value={clamp(audioService().globalVelocityPercentage(), 0, AUDIO.MAX_VELOCITY_USER_PERCENTAGE)}
          step={1}
          minValue={0}
          maxValue={AUDIO.MAX_VELOCITY_USER_PERCENTAGE} />
      </MenuGeneralSubSection>
    </MenuSectionWithSearchFilter>
  </>);
};

export default AudioMidiContent;