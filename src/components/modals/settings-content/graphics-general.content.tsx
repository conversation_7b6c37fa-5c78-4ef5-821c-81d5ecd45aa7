import { isNumber } from "lodash-es";
import { useService } from "solid-services";
import { GraphicShadowFilteringMethod, graphicShadowFilteringMethodFromJSON, graphicShadowFilteringMethodToJSON, GraphicsMsaaSamples, graphicsMsaaSamplesFromJSON, graphicsMsaaSamplesToJSON, GraphicsPresets, graphicsPresetsFromJSON, graphicsPresetsToJSON } from "~/proto/pianorhythm-app-renditions";
import PlatformService from "~/services/platform.service";
import AppSettingsService from "~/services/settings-storage.service";
import { DefaultAppSettings } from "~/types/settings.types";
import { COMMON } from "~/util/const.common";
import { MenuContentDivider, MenuContentSelect, MenuContentSwitch, MenuGeneralSubSection, MenuResetSettingsButton, MenuSectionWithSearchFilter, NOTES } from "./common.content";
import { isWebGpuSupported as webgpusupported } from "~/util/helpers.dom";
import { createSignal, onMount } from "solid-js";

const GraphicsGeneralContent = () => {
  const platformService = useService(PlatformService);
  const appSettingsService = useService(AppSettingsService);
  const category = "graphics";
  const menu = "general";
  const isMobile = platformService().isMobile;
  const [isWebGpuSupported, setIsWebGpuSupported] = createSignal(false);

  onMount(async () => {
    setIsWebGpuSupported(await webgpusupported());
  });

  const isEngineDisabled = () => !appSettingsService().getSetting("GRAPHICS_ENABLE_ENGINE");

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection label="General">
        {/* Graphics Preset Select */}
        <MenuContentSelect
          label="Graphics Preset"
          placeholder="Select a graphics preset..."
          settingsKey={"GRAPHICS_PRESET"}
          defaultValue={graphicsPresetsToJSON(DefaultAppSettings.GRAPHICS_PRESET)}
          disabled={isEngineDisabled()}
          onTranslateValue={(value) => {
            if (isNumber(value)) return graphicsPresetsToJSON(value);
            return graphicsPresetsFromJSON(value);
          }}
          options={
            Object.values(GraphicsPresets)
              .filter(isNumber)
              .filter(x => x != GraphicsPresets.UNRECOGNIZED && x != GraphicsPresets.Preset_None)
              .map(graphicsPresetsToJSON)
          }
          onDisplayOption={(option) => {
            return option.toUpperCase().replaceAll("PRESET_", "");
          }}
        />

        <MenuContentSelect
          hidden
          label="Target FPS"
          placeholder="Select a target FPS..."
          settingsKey={"GRAPHICS_TARGET_FPS"}
          preventAppSettingRefresh
          disabled={isEngineDisabled()}
          defaultValue={DefaultAppSettings.GRAPHICS_TARGET_FPS.toString()}
          options={[...[10, 30, 60, 120, 240].map(x => x.toString()), "unlimited"]}
          tooltip={`
          Select the target FPS for the rendering engine.
          <br/>
          <br/>
          ${NOTES.REQUIRES_RELOAD}
          `}
        />

        <MenuContentSwitch settingsKey="GRAPHICS_ENABLE_ENGINE"
          tooltip={`
            Toggle whether the graphics engine is initialized and used for rendering.
            <br/><br/>
            ${NOTES.REQUIRES_RELOAD}
          `}
          skipTranslate
          preventAppSettingRefresh
          elementLabel="Enable Graphics Engine"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_STAGE" category={category} menu={menu}
          tooltip={`Enable/disable the main stage in the scene.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Stage"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_LIGHTS" category={category} menu={menu}
          tooltip={`Enable/disable lights in the scene.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Lights"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_PIANO" category={category} menu={menu}
          tooltip={`
            Enable/disable showing the main piano in the scene.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Piano"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_DRUMS" category={category} menu={menu}
          tooltip={`
            Enable/disable showing the drum set (only will show when there's activity in the drum channel) in the scene.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Drums"
        />

        {/* <MenuContentSwitch
      skipTranslate settingsKey="GRAPHICS_ONLY_SHOW_PIANO_KEYS" category={category} menu={menu}
      tooltip={`
        Enable/disable only showing the piano keys in the scene. (The stage will still be displayed unless disabled)
      `}
      elementLabel="Only Show Piano Keys"
    /> */}

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_MOTION_BLUR" category={category} menu={menu}
          hidden
          tooltip={`Motion blur is an effect used primarily in games where the surroundings appear
          blurry on the screen as the camera moves quickly through a particular area.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Motion Blur"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_ALL_PARTICLES" category={category} menu={menu}
          hidden={isMobile()}
          disabled={!isWebGpuSupported() || isEngineDisabled()}
          tooltip={`
            A particle system is a collection of many many minute particles that together represent a fuzzy object.
            <br/><br/>
            <i>NOTE: When disabled, the particles won't immediately dissipate until their life time is complete.</i>
          `}
          elementLabel="Enable Particle System"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_FOG" category={category} menu={menu}
          tooltip={`
            Distance fog is a technique used in 3D computer graphics to enhance the perception of distance
            by shading distant objects differently.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Fog"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_POST_PROCESSING" category={category} menu={menu}
          tooltip={`
            Enable/disable post processing effects in the scene.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Post Processing"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_GLOW" category={category} menu={menu}
          tooltip={`
            Enable/disable glow effects on certain models.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Glow"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_SHADOWS" category={category} menu={menu}
          tooltip={`
            Enable/disable shadows in the scene.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Shadows"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_ANIMATIONS" category={category} menu={menu}
          tooltip={`Enable/disable animations in the scene.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Animations"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_USE_LOW_POLY_MODELS" category={category} menu={menu}
          tooltip="Enable/disable low poly models in the scene."
          disabled={isEngineDisabled()}
          elementLabel="Use Low Poly Models"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_SPECIAL_EFFECTS" category={category} menu={menu}
          disabled
          tooltip={`
            Enable/disable special effects (like rain) in the scene.
          `}
          elementLabel="Enable Special Effects"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_AUTO_ANIMATE_TO_INSTRUMENTS" category={category} menu={menu}
          disabled
          tooltip={`
        If enabled, the camera will animate and lock target with the instruments (camera, drums, etc) when you get close enough
        within range of them.
        `}
          elementLabel="Enable Auto Animate Camera To Instruments"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_ORCHESTRA_MODELS" category={category} menu={menu}
          disabled
          tooltip={`
            Enable/disable other user models being shown in Orchestra mode.
          `}
          elementLabel="Enable Orchestra Models"
        />
      </MenuGeneralSubSection>

      {/* WEB GPU */}
      <MenuGeneralSubSection label="Web GPU" hidden={isMobile()}>
        <MenuContentSwitch
          disabled={!isWebGpuSupported()}
          skipTranslate settingsKey="GRAPHICS_ENABLE_WEBGPU" category={category} menu={menu}
          tooltip={`
            WebGPU is a new API for the web, which exposes modern hardware capabilities and allows rendering and computation operations
            on a GPU, similar to Direct3D 12, Metal, and Vulkan.
            Unlike the WebGL family of APIs, WebGPU offers access to more advanced GPU
            features and provides first-class support for general computations on the GPU.
            <br/><br/>
            The promise behind WebGPU is an exceptionally faster API provided through lower
            level control to system graphics resources from JavaScript.
            (Still experimental and some features may not work completely. May also not be suppported by many devices.)
            <br/><br/>
            ${NOTES.REQUIRES_RELOAD}
          `}
          elementLabel="Use WebGPU"
        />
      </MenuGeneralSubSection>

      {/* Advanced */}
      <MenuGeneralSubSection
        label="Advanced"
        hidden={isMobile()}
      >
        <MenuContentSelect
          hidden
          label="WEBGL Power Preference"
          placeholder="Select a power preference..."
          settingsKey={"WEBGL_POWER_PREFERENCE"}
          defaultValue={"high-performance"}
          options={["default", "high-performance", "low-power"]}
          tooltip={`
            Provides a hint to WebGL regarding the preferred power
            preference mode.

            <ul style='margin-top:10px; margin-left:5px'>
              <li style='margin-bottom:10px'>
                When set to <b>Default</b>, the browser
                decides which GPU configuration is most suitable.
              </li>

              <li style='margin-bottom:10px'>
                When set to <b>High Performance</b>, a GPU configuration that
                prioritizes rendering performance over power
                consumption is selected.
              </li>

              <li style='margin-bottom:10px'>
                When set to <b>Low Power</b>, a GPU configuration that prioritizes power saving over
                rendering performance is selected.
              </li>
            </ul>
            ${NOTES.REQUIRES_RELOAD}
          `}
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_ENABLE_ANTIALIAS" category={category} menu={menu}
          tooltip={`
            Defines whether MSAA (Multisample anti-aliasing) is enabled on the canvas.
            This is a technique used in computer graphics to remove jagged edges. Disabling this
            may improve performance.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Multisample Anti-Aliasing (MSAA)"
        />

        <MenuContentSelect
          label="MSAA Samples"
          placeholder="Select MSAA samples..."
          settingsKey="GRAPHICS_MSAA_SAMPLES"
          defaultValue={
            graphicsMsaaSamplesToJSON(DefaultAppSettings.GRAPHICS_MSAA_SAMPLES)
          }
          disabled={appSettingsService().getSetting("GRAPHICS_ENABLE_ANTIALIAS") === false}
          onTranslateValue={(value) => {
            if (isNumber(value)) return graphicsMsaaSamplesToJSON(value);
            return graphicsMsaaSamplesFromJSON(value);
          }}
          onDisplayOption={(option) => {
            return option.toUpperCase().replaceAll("MSAA_", "");
          }}
          options={[
            GraphicsMsaaSamples.Msaa_Sample2,
            GraphicsMsaaSamples.Msaa_Sample4,
            GraphicsMsaaSamples.Msaa_Sample8,
          ]
            .filter(Boolean)
            .map(graphicsMsaaSamplesToJSON)
          }
          // @ts-ignore
          optionsMeta={[
            COMMON.IS_WEB_APP && {
              id: graphicsMsaaSamplesToJSON(GraphicsMsaaSamples.Msaa_Sample2),
              tagLine: "Not supported on web. Will fallback to MSAA 4x.",
            },
            COMMON.IS_WEB_APP && {
              id: graphicsMsaaSamplesToJSON(GraphicsMsaaSamples.Msaa_Sample8),
              tagLine: "Not supported on web. Will fallback to MSAA 4x.",
            },
          ].filter(Boolean)}
          tooltip={`
            Multisample anti-aliasing (MSAA) is a type of anti-aliasing, a technique used in computer graphics to improve image quality.
            <br/>
            <br/>
            The number of samples determines how many sub-pixel samples are taken. The more samples, the better the anti-aliasing effect.
          `}
        />

        <MenuContentSelect
          label="Shadow Filtering Method"
          placeholder="Select a shadow filtering method..."
          settingsKey="GRAPHICS_SHADOW_FILTER"
          defaultValue={
            graphicShadowFilteringMethodToJSON(DefaultAppSettings.GRAPHICS_SHADOW_FILTER)
          }
          disabled={appSettingsService().getSetting("GRAPHICS_ENABLE_SHADOWS") === false}
          onTranslateValue={(value) => {
            if (isNumber(value)) return graphicShadowFilteringMethodToJSON(value);
            return graphicShadowFilteringMethodFromJSON(value);
          }}
          onDisplayOption={(option) => {
            return option.toUpperCase().replaceAll("SHADOWFILTERING_", "");
          }}
          options={
            [
              GraphicShadowFilteringMethod.ShadowFiltering_Hardware2x2,
              GraphicShadowFilteringMethod.ShadowFiltering_Gaussian,
              GraphicShadowFilteringMethod.ShadowFiltering_Temporal,
            ]
              .map(graphicShadowFilteringMethodToJSON)
          }
          optionsMeta={[
            {
              id: graphicShadowFilteringMethodToJSON(GraphicShadowFilteringMethod.ShadowFiltering_Hardware2x2),
              tagLine: "Fast but poor quality.",
            },
            {
              id: graphicShadowFilteringMethodToJSON(GraphicShadowFilteringMethod.ShadowFiltering_Gaussian),
              tagLine: `
              Approximates a fixed Gaussian blur, good when TAA isn’t in use.

              Good quality, good performance.

              For directional and spot lights, this uses a method by Ignacio Castaño for The Witness using 9 samples and smart filtering to achieve the same as a regular 5x5 filter kernel.`,
            },
            {
              id: graphicShadowFilteringMethodToJSON(GraphicShadowFilteringMethod.ShadowFiltering_Temporal),
              tagLine: `
              A randomized filter that varies over time, good when TAA is in use.
              <br/>
              Good quality when used with TemporalAntiAliasSettings and good performance.

              For directional and spot lights, this uses a method by Jorge Jimenez for Call of Duty: Advanced Warfare using 8 samples in spiral pattern, randomly-rotated by interleaved gradient noise with spatial variation.
              `,
            },
          ]}
          tooltip={`
            Select the shadow filtering method to control how to anti-alias shadow edges.
            The different modes use different approaches to Percentage Closer Filtering.
          `}
        />

        <MenuContentSwitch
          hidden={isMobile()}
          skipTranslate settingsKey="GRAPHICS_ENABLE_BLOOM" category={category} menu={menu}
          tooltip={`Bloom is a post-process effect that simulates the way light interacts with the camera lens.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Bloom"
        />

        <MenuContentSwitch
          hidden={isMobile()}
          skipTranslate settingsKey="GRAPHICS_ENABLE_TONE_MAPPING" category={category} menu={menu}
          tooltip={`Tone mapping is a technique used in image processing and computer graphics to map one set of colors to another to approximate the appearance of high dynamic range images in a medium that has a more limited dynamic range.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable Tone Mapping"
        />

        <MenuContentSwitch
          hidden={isMobile()}
          skipTranslate settingsKey="GRAPHICS_ENABLE_HDR" category={category} menu={menu}
          tooltip={`HDR (High Dynamic Range) is a rendering technique that allows for a greater range of luminance between the lightest and darkest areas of an image.`}
          disabled={isEngineDisabled()}
          elementLabel="Enable HDR"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_AMBIENT_OCCLUSION" category={category} menu={menu}
          disabled
          tooltip={`
          This is a rendering pipeline (chained post-processes) that will compute the ambient occlusion
          of a given scene from the screen space.
          <br/><br/>
          Technical definition: Ambient occlusion is a computer graphics technique that simulates
          how light falls on an object. It's a global lighting method that adds realism to models by
          controlling the attenuation of ambient light due to occluded areas."
        `}
          elementLabel="Enable Screen Space - Ambient Occlusion"
        />

        <MenuContentSwitch
          // TODO
          hidden
          skipTranslate settingsKey="GRAPHICS_ENABLE_DEPTH_OF_FIELD" category={category} menu={menu}
          tooltip={`
            This is a predefined chain of postprocesses that helps achieving photograph-like realism.
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Depth Of Field"
        />

        <MenuContentSwitch
          hidden
          skipTranslate settingsKey="GRAPHICS_RENDER_EVEN_IN_BACKGROUND" category={category} menu={menu}
          tooltip={`
            Gets or sets a boolean indicating if the engine must keep rendering, even if the window is not in foreground.
            <br/><br/>
            <i>(Mostly applies when not using the offscreen canvas)</i>
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Rendering Even in Background"
        />

        <MenuContentSwitch
          skipTranslate settingsKey="GRAPHICS_USE_OFFSCREEN_CANVAS" category={category} menu={menu}
          tooltip={`
            "The OffscreenCanvas interface provides a canvas that can be rendered off screen, decoupling the DOM
            and the Canvas API so that the canvas element is no longer entirely dependent on the DOM."
            <br/><br/>
            <i>(More of a performance improvement)</i>
            <br/><br/>
            ${NOTES.REQUIRES_RELOAD}
          `}
          disabled={isEngineDisabled()}
          elementLabel="Enable Offscreen Canvas"
        />
      </MenuGeneralSubSection>

      <MenuContentDivider marginBottom={-10} />
      <MenuResetSettingsButton
        settingsName="Graphics Settings"
        onConfirm={() => { appSettingsService().resetGraphicsSettingsToDefault(); }}
      />
    </MenuSectionWithSearchFilter>
  </>);
};

export default GraphicsGeneralContent;