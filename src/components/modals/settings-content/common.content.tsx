import { <PERSON>, But<PERSON>, Divider, hope, Input, InputGroup, InputLeftElement, InputRightElement, Select, SelectContent, SelectIcon, SelectLabel, SelectListbox, SelectOptGroup, SelectOption, SelectOptionIndicator, SelectOptionText, SelectPlaceholder, SelectTrigger, SelectValue, Skeleton, Spinner, Switch, Text, Tooltip, VStack } from "@hope-ui/solid";
import * as noUiSlider from 'nouislider';
import { Accessor, Component, For, JSX, JSXElement, Match, ParentComponent, Show, Signal, Switch as SolidSwitch, createContext, createEffect, createSignal, onMount, useContext } from "solid-js";
import { useService } from "solid-services";
import { SweetAlertResult } from "sweetalert2";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import I18nService from "~/services/i18n.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import WebSocketService from "~/services/websocket.service";
import { AppSoundFx } from "~/types/app.types";
import { AppSettings } from "~/types/settings.types";
import { roundNumberToTwoDecimals } from "~/util/helpers";
import SwalPR from "~/util/sweetalert";
import { FaSolidMagnifyingGlass, FaSolidX } from "solid-icons/fa";
import notificationService from "~/services/notification.service";
import debounce from "lodash-es/debounce";
import isEmpty from "lodash-es/isEmpty";
import camelCase from "lodash-es/camelCase";
import ToolTipHelp from "~/components/tooltip-help";

const BaseToolTipHelp = {
  transition: "color 0.2s",
  _hover: { "color": "$accent1" },
  marginLeft: 5,
  marginBottom: 10,
  fontSize: 10,
};

const MenuDividerMargin = 40;
const ContentTextFontSize = 16;
const CommonMargin = 10;
const CommonMarginBottom = 20;
const CommonSwitchFontSize = 18;

export namespace NOTES {
  export const REQUIRES_RELOAD = "<i>NOTE: Requires a reload to take effect.</i>";
}

type ContextProps = [
  Accessor<string>,
  {
    setSearchTerm: (message: string) => void;
  }
];

const MenuSectionSearchFilterContext = createContext<ContextProps>([
  () => "",
  {
    setSearchTerm: () => { },
  }
]);

function MenuSectionSearchFilterProvider(props: {
  searchTerm: Signal<string>,
  children: JSX.Element;
}) {
  const filter: ContextProps = [
    props.searchTerm[0],
    {
      setSearchTerm: (term: string) => {
        props.searchTerm[1](term);
      },
    }
  ];

  return <MenuSectionSearchFilterContext.Provider value={filter} children={props.children} />;
}

export type MenuContentSwitchProps = {
  category?: string;
  menu?: string;
  elementLabel: string;
  tooltip?: string;
  disabled?: boolean;
  skipTranslate?: boolean;
  preventAppSettingRefresh?: boolean;
  settingsKey?: keyof AppSettings;
  ignoreAppSettings?: boolean;
  defaultValue?: boolean;
  hidden?: boolean;
  onChange?(value: boolean): Promise<void>;
  onAlertConfirm?: [boolean, () => Promise<SweetAlertResult<Awaited<any>>>];
};

export type MenuContentButtonProps = {
  category?: string;
  menu?: string;
  elementLabel: string;
  tooltip?: string;
  disabled?: boolean;
  skipTranslate?: boolean;
  backgroundColor?: string;
  onClick?(): Promise<void>;
};

type MenuContentSliderProps = {
  id: string;
  label: string;
  value: number;
  step?: number;
  maxValue?: number;
  minValue?: number;
  orientation?: "horizontal" | "vertical";
  settingsKey: keyof AppSettings,
  skipTranslate?: boolean;
  tooltip?: string;
  disabled?: boolean;
  onChange?(value: number): void;
};

const showRequireReloadNotification = (tooltip: string) => {
  if (tooltip.includes(NOTES.REQUIRES_RELOAD))
    notificationService.show({
      type: "info",
      description: "Setting won't take effect until next reload."
    });
};

const MenuDivider = () => <Divider
  variant="solid"
  color="rgba(255,255,255,0.2) !important"
  marginTop={CommonMargin}
  marginBottom={CommonMarginBottom}
/>;

type ContentMetaTextProps = {
  disabled?: boolean;
  text: string;
  marginTop?: number;
  onHasFilteredText?: (value: boolean | undefined) => void;
};

export const ContentMetaText: Component<ContentMetaTextProps> = (props) => {
  const [searchTerm] = useContext(MenuSectionSearchFilterContext);
  const [isFiltered, setIsFiltered] = createSignal<boolean>();
  const [joinedText, setJoinedText] = createSignal<string>(props.text);

  createEffect(() => {
    let search = searchTerm().toLowerCase();
    if (search.length == 0) {
      setIsFiltered();
      return;
    }

    setIsFiltered(props.text.toLowerCase().includes(search));
    props.onHasFilteredText?.(isFiltered());
  });

  createEffect(() => {
    let words = props.text.split(" ");

    let filteredWords = words.map((word) => {
      let search = searchTerm().toLowerCase();
      if (search.length > 0 && word.toLowerCase().includes(search)) {
        return `<b style="color:var(--hope-colors-warning10)">${word}</b>`;
      } else {
        return word;
      }
    });

    setJoinedText(filteredWords.join(" "));
  });

  return (<>
    <Box
      marginTop={props.marginTop || 0}
      className={props.disabled ? "disabled" : ""}
      color="$neutral11"
      fontSize={ContentTextFontSize}
      innerHTML={joinedText()} />
  </>);
};

export const MenuContentSwitch: Component<MenuContentSwitchProps> = (props) => {
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);
  const [tooltip, setTooltip] = createSignal("");
  const [checked, setChecked] = createSignal<boolean>(false);
  const [label, setLabel] = createSignal(props.elementLabel);

  const [searchTerm] = useContext(MenuSectionSearchFilterContext);
  const [isFiltered, setIsFiltered] = createSignal<boolean>();

  createEffect(() => {
    let search = searchTerm().toLowerCase();
    setIsFiltered(search.length == 0 ? undefined : label().toLowerCase().includes(search));
  });

  createEffect(() => {
    setChecked(Boolean(props.disabled ? false :
      (
        props.defaultValue ?? appSettingsService().getSetting<boolean>(props.settingsKey!)
      )));
  });

  createEffect(() => {
    setLabel(
      props.skipTranslate ?
        (props.elementLabel || "Missing label") :
        i18nService().t_roomPageSettingSubMenuElement(props.category || "", props.menu || "", props.elementLabel)
    );
  });

  createEffect(() => {
    setTooltip(
      props.skipTranslate ?
        (props.tooltip || "") :
        (i18nService().t_roomPageSettingTooltip(props.category || "", props.menu || "", props.tooltip || ""))
    );
  });

  const onChange = async (value: boolean) => {
    sfxService().playSFX(AppSoundFx.CLICK2);

    if (props.onChange) await props.onChange(value);
    if (!props.ignoreAppSettings && props.settingsKey) {
      appSettingsService().saveSetting(props.settingsKey, value);
      showRequireReloadNotification(tooltip());
    }
  };

  const onClick = debounce((e: Event) => {
    setChecked(v => !v);
    let value = checked();

    if (props.onAlertConfirm && value == props.onAlertConfirm[0]) {
      props
        .onAlertConfirm[1]()
        .then((result) => {
          if (result.isConfirmed) {
            onChange(value);
            setChecked(value);
          }
        });
    } else {
      onChange(value);
      setChecked(value);
    }
  });

  return (
    <>
      {!props.hidden &&
        <Box
          w={"100%"}
          display={isFiltered() === false ? "none" : "block"}
        >
          <Switch
            pointerEvents={props.disabled ? "none" : "auto"}
            disabled={props.disabled}
            marginBottom={!isEmpty(tooltip()) ? CommonMarginBottom / 2 : 0}
            checked={checked()}
            onClick={onClick}
            // @ts-ignore
            colorScheme="accent"
            fontSize={CommonSwitchFontSize}
            fontWeight={"$semibold"}
            display="flex"
            justifyContent={"space-between"}
          >
            <Box color={isFiltered() === true ? "$accent10" : "$neutral12"}>
              {label()}
            </Box>
          </Switch>

          {!isEmpty(tooltip()) &&
            <ContentMetaText
              text={tooltip()}
              onHasFilteredText={(value) => { if (value === true) setIsFiltered(true); }}
              disabled={props.disabled} />}
        </Box>
      }
    </>
  );
};

export const MenuContentButton: Component<MenuContentButtonProps> = (props) => {
  const i18nService = useService(I18nService);
  const [tooltip, setTooltip] = createSignal("");
  const [searchTerm] = useContext(MenuSectionSearchFilterContext);
  const [isFiltered, setIsFiltered] = createSignal<boolean>();
  const [label, setLabel] = createSignal(props.elementLabel);

  createEffect(() => {
    setTooltip(
      props.skipTranslate ?
        (props.tooltip || "") :
        (i18nService().t_roomPageSettingTooltip(props.category || "", props.menu || "", props.tooltip || ""))
    );
  });

  createEffect(() => {
    setLabel(
      props.skipTranslate ?
        (props.elementLabel || "Missing label") :
        i18nService().t_roomPageSettingSubMenuElement(props.category || "", props.menu || "", props.elementLabel)
    );
  });

  createEffect(() => {
    let search = searchTerm().toLowerCase();
    setIsFiltered(search.length == 0 ? undefined : label().toLowerCase().includes(search));
  });

  return (
    <Box
      display={isFiltered() === false ? "none" : "block"}
    >
      <Button
        ref={(el: HTMLElement) => buttonSFX(el)}
        disabled={props.disabled}
        marginBottom={!isEmpty(tooltip()) ? CommonMarginBottom : 0}
        variant="outline"
        fontWeight={"$semibold"}
        backgroundColor={props.backgroundColor}
        onmousedown={async () => { if (props.onClick) await props.onClick(); }}
      >
        <Box color={isFiltered() === true ? "$accent10" : "$neutral12"}>
          {label()}
        </Box>
      </Button>

      {!isEmpty(tooltip()) &&
        <ContentMetaText
          text={tooltip()}
          onHasFilteredText={(value) => { if (value === true) setIsFiltered(true); }}
          disabled={props.disabled}
        />}
    </Box>
  );
};

export const MenuResetSettingsButton:
  Component<{
    settingsName?: string;
    onClickOverride?: (() => void);
    onConfirm?: (() => void);
  }>
  = (props) => {
    const appSettingsService = useService(AppSettingsService);
    const sfxService = useService(SoundEffectsService);
    const [searchTerm] = useContext(MenuSectionSearchFilterContext);
    const [isFiltered, setIsFiltered] = createSignal<boolean>();

    createEffect(() => {
      let search = searchTerm().toLowerCase();
      let label = props.settingsName?.toLowerCase();
      setIsFiltered(search.length == 0 ? undefined : label?.includes(search));
    });

    return (<>
      <Box
        display={isFiltered() === false ? "none" : "block"}
      >
        <Box
          fontWeight={"bolder"}
          fontSize={CommonSwitchFontSize}
          marginBottom={CommonMargin}
        >Reset Settings</Box>

        <MenuContentButton
          elementLabel="Reset Settings To Default"
          backgroundColor="$warning8"
          tooltip={`Click to reset all of your ${props.settingsName?.toLowerCase() ?? "settings"} to their default.`}
          skipTranslate
          onClick={async () => {
            if (props.onClickOverride) {
              props.onClickOverride?.();
              return;
            }

            SwalPR(sfxService).fire({
              icon: "warning",
              title: "Reset Settings",
              html: `Are you sure you want to reset your <b>${props.settingsName?.toLowerCase()}</b> to the default? This action is irreversible!`,
              showCancelButton: true
            }).then(async (result) => {
              if (result.isConfirmed) {
                props.onConfirm?.();
                SwalPR(sfxService).fire({
                  icon: "success",
                  title: "Reset Settings",
                  html: `<b>${camelCase(props.settingsName)}</b> are now set to the default.
              You may have to reload the app to reflect certain changes.`,
                }).then(() => appSettingsService().persistSettings());
              }
            });
          }}
        />
        <br />
      </Box>
    </>);
  };

export const MenuContentHeader: Component<{ category?: string, menu?: string, label?: string, skipTranslate?: boolean, tooltip?: string; }> = (props) => {
  const i18nService = useService(I18nService);
  const [tooltip, setTooltip] = createSignal("");

  createEffect(() => {
    if (props.category && props.menu && props.tooltip)
      setTooltip(i18nService().t_roomPageSettingTooltip(props.category, props.menu, props.tooltip || ""));
  });

  return (
    <Box
      style={{
        "text-decoration": "underline",
        "text-underline-position": "under",
      }}
      color="rgba(255,255,255, 0.9)"
      fontWeight={"bold"} textTransform="uppercase"
    >
      <SolidSwitch>
        <Match when={props.skipTranslate && props.label}>
          {props.label}
        </Match>
        <Match when={props.category && props.menu}>
          {i18nService().t_roomPageSettingSubMenuLabel(props.category!, props.menu!)}
        </Match>
        <Match when={true}>
          Missing Label
        </Match>
      </SolidSwitch>

      {(props.tooltip && tooltip()) && <ToolTipHelp
        {...BaseToolTipHelp}
        tooltipLabel={tooltip()} />
      }
    </Box>
  );
};

export const MenuGeneralSubHeader: Component<{
  label: string,
  category?: string,
  menu?: string,
  color?: string,
  textTransform?: "capitalize" | "full-size-kana" | "full-width" | "lowercase" | "none" | "uppercase",
  translate?: boolean,
  marginTop?: number;
}> = (props) => {
  const i18nService = useService(I18nService);
  const [label, setLabel] = createSignal(props.label);

  createEffect(() => {
    if (!isEmpty(props.menu) && !isEmpty(props.category) && !isEmpty(props.label)) {
      setLabel(i18nService().t_roomPageSettingSubMenuElement(props.category!, props.menu!, props.label));
    }

    if (props.translate) {
      setLabel(i18nService().t_roomPageSettings(props.label));
    }
  });

  return (<>
    <Box
      marginTop={CommonMargin}
      textTransform={props.textTransform ?? "uppercase"} fontSize={ContentTextFontSize}
      color={props.color ?? "$tertiary5"} fontWeight={"bolder"}>
      {label()}
    </Box>
    <MenuDivider />
  </>);
};

export const MenuSectionWithSearchFilter: ParentComponent<{}> = (props) => {
  const currentSearch = createSignal("");

  return <>
    {/* Create Search Bar Input */}
    <VStack w={"100%"} spacing={"$1"} alignItems={"flex-start"}>
      <InputGroup>
        <InputLeftElement>
          <FaSolidMagnifyingGlass />
        </InputLeftElement>
        <Input
          placeholder="Search"
          w="100%"
          variant="filled"
          value={currentSearch[0]()}
          onInput={(e) => currentSearch[1](e.currentTarget.value)}
        />
        <InputRightElement>
          <Box
            __tooltip_title="Clear Search"
            __tooltip_open_delay={250}
          >
            <FaSolidX
              style={{ cursor: "pointer" }}
              onmousedown={() => { currentSearch[1](""); }}
            />
          </Box>
        </InputRightElement>
      </InputGroup>
    </VStack>

    <MenuSectionSearchFilterProvider searchTerm={currentSearch}>
      {props.children}
    </MenuSectionSearchFilterProvider>
  </>;
};

export const MenuGeneralSubSection: ParentComponent<{
  label: string,
  translate?: boolean,
  category?: string,
  menu?: string,
  tooltip?: string,
  hidden?: boolean;
}> = (props) => {
  return <>
    {!props.hidden && <Box w="100%">
      <MenuGeneralSubHeader
        translate={props.translate}
        label={props.label}
        category={props.category}
        menu={props.menu}
      />

      <VStack
        spacing={"$10"}
        alignItems={"flex-start"}
        width={"100%"}
        justifyContent={"space-between"}
      >
        {props.children}
      </VStack>
    </Box>
    }
  </>;
};

export const MenuContentSubHeader: Component<{ category: string, menu: string, label: string, tooltip?: string; }> = (props) => {
  const i18nService = useService(I18nService);
  const [tooltip, setTooltip] = createSignal("");

  createEffect(() => {
    setTooltip(i18nService().t_roomPageSettingTooltip(props.category, props.menu, props.tooltip ?? ""));
  });

  return (<>
    <Box
      textTransform="uppercase"
      fontSize={12} color="$neutral12" fontWeight={"bold"}
    >
      {i18nService().t_roomPageSettingSubMenuElement(props.category, props.menu, props.label)}

      {(props.tooltip && tooltip()) && <ToolTipHelp
        {...BaseToolTipHelp}
        tooltipLabel={tooltip()} />
      }
    </Box>
  </>);
};

export const MenuContentSlider: Component<MenuContentSliderProps> = (props) => {
  const i18nService = useService(I18nService);
  const appSettingsService = useService(AppSettingsService);
  const [tooltip, setTooltip] = createSignal("");
  const [, setVolumeSlider] = createSignal<noUiSlider.API>();
  const [label] = createSignal(props.label);

  const [searchTerm] = useContext(MenuSectionSearchFilterContext);
  const [isFiltered, setIsFiltered] = createSignal<boolean>();

  createEffect(() => {
    let search = searchTerm().toLowerCase();
    setIsFiltered(search.length == 0 ? undefined : label().toLowerCase().includes(search));
  });

  createEffect(() => {
    setTooltip(
      props.skipTranslate && props.tooltip ?
        props.tooltip :
        (i18nService().t_roomPageSettings(props.tooltip ?? ""))
    );
  });

  const onSave = debounce(async (value: number) => {
    if (props.onChange) props.onChange(value);
    appSettingsService().saveSetting(props.settingsKey, roundNumberToTwoDecimals(value));
  }, 10, { leading: true });

  const onVolumeBarSlider = (element: HTMLElement) => {
    if (element.className.includes("noUi-target")) return;
    let slider = noUiSlider.create(element, {
      orientation: props.orientation ?? "horizontal",
      connect: true,
      tooltips: true,
      start: props.value,
      step: props.step != null ? props.step : 1,
      range: {
        'min': props.minValue != null ? props.minValue : 0,
        'max': props.maxValue != null ? props.maxValue : 100
      },
    });

    slider.on("change", async (values) => {
      let value: number = parseFloat(values[0] as any);
      await onSave(value);
    });

    setVolumeSlider(slider);
  };

  return (<>
    <Box
      className={(props.disabled && "disabled") || ""}
      w="100%"
      display={isFiltered() === false ? "none" : "block"}
    >
      <Box
        fontSize={CommonSwitchFontSize}
        fontWeight={"$semibold"}
        textTransform="capitalize"
        color="$neutral12"
      >
        <Box color={isFiltered() === true ? "$accent10" : "$neutral12"}>
          {label()}
        </Box>
      </Box>

      <Box
        marginTop={CommonMargin}
        marginLeft={5}
        id={props.id}
        class={"noUi-target noUi-horizontal"}
        w="95%"
        ref={onVolumeBarSlider}
      />

      {!isEmpty(tooltip()) &&
        <Box
          marginTop={CommonMargin}
          color="$neutral11"
          fontSize={ContentTextFontSize}
          innerHTML={tooltip()} />}
    </Box>
  </>);
};

export type MenuContentSelectItemMeta = {
  id: string;
  tagLine?: string;
  element?: JSX.Element;
  rawSize?: string;
};

type MenuContentSelectGroup = {
  label: string,
  options: string[];
};

export type MenuContentSelectProps = {
  label?: string;
  placeholder?: string;
  tooltip?: string;
  disabled?: boolean;
  loading?: boolean;
  skipTranslate?: boolean;
  settingsKey?: keyof AppSettings;
  preventAppSettingRefresh?: boolean;
  options?: string[];
  groups?: MenuContentSelectGroup[];
  disabledOptions?: string[];
  optionsMeta?: MenuContentSelectItemMeta[];
  showOnlySelect?: boolean;
  ignoreOnSave?: string[];
  defaultValue?: string;
  value?: string;
  width?: string | number;
  hidden?: boolean;
  triggerFontSize?: number | string;
  triggerMinHeight?: number | string;
  triggerTooltip?: JSXElement;
  triggerTooltipPlacement?: "top" | "right" | "bottom" | "left";
  containerHeight?: number | string;
  selectOptionHeight?: number | string;
  selectOptionFontSize?: number | string;
  onSelected?(value: string): Promise<void>;
  onTranslateValue?(value: string | number): any;
  onDisplayOption?(value: string): string;
};

export const MenuContentDivider: Component<{ marginBoth?: number | string, marginTop?: number | string, marginBottom?: number | string; }> = (props) => {
  return <Divider marginTop={props.marginBoth != null ? props.marginBoth : props.marginTop ?? 20} marginBottom={props.marginBoth != null ? props.marginBoth : props.marginBottom ?? 20} color="rgba(255, 255, 255, 0.2) !important" />;
};

export const MenuContentSelect: Component<MenuContentSelectProps> = (props) => {
  const [tooltip, setTooltip] = createSignal("");
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);
  const [value, setValue] = createSignal<string>(props.value || "");
  const [isFiltered, setIsFiltered] = createSignal<boolean>();
  const [searchTerm] = useContext(MenuSectionSearchFilterContext);

  createEffect(() => {
    let default_Value = props.defaultValue;
    let settings_Value = props.settingsKey && appSettingsService().getSetting<any>(props.settingsKey, true);
    let _translatedValue = props.onTranslateValue ? props.onTranslateValue(settings_Value) : settings_Value;
    setValue((props.value ?? _translatedValue ?? settings_Value ?? default_Value ?? ""));
  });

  createEffect(() => {
    setTooltip(
      props.skipTranslate && props.tooltip ?
        props.tooltip :
        (i18nService().t_roomPageSettings(props.tooltip || ""))
    );
  });

  createEffect(() => {
    let search = searchTerm().toLowerCase();
    setIsFiltered(search.length == 0 ? undefined : props.label?.toLowerCase().includes(search));
  });

  return (<>
    {!props.hidden &&
      <Box
        w="100%"
        display={isFiltered() === false ? "none" : "block"}
        position={"relative"}
      >
        {(!props.showOnlySelect && props.label) &&
          <Box
            color="$neutral12"
            fontSize={ContentTextFontSize}
            fontWeight={"$semibold"}
            marginBottom={5}
          >{props.label} </Box>
        }

        <Select
          disabled={props.disabled}
          value={value().toString()}
          onChange={async (val: string) => {
            setValue(val);
            if (props.onSelected) await props.onSelected(val);
            if (props.ignoreOnSave != null && props.ignoreOnSave.includes(val)) return;

            if (props.settingsKey) {
              let value = props.onTranslateValue ? props.onTranslateValue(val) : val;

              appSettingsService().saveSetting(props.settingsKey, value);
              showRequireReloadNotification(tooltip());

              if (props.preventAppSettingRefresh == true) {
                return;
              }

              appSettingsService().persistSettings();
            }
          }}
        >
          <SelectTrigger
            onmousedown={() => sfxService().playClickSFX()}
            w={props.width}
            fontSize={props.triggerFontSize}
            minH={props.triggerMinHeight ?? "$10"}
          >
            <SelectPlaceholder>{props.placeholder}</SelectPlaceholder>
            <SelectValue
              __tooltip_title={props.triggerTooltip}
              __tooltip_placement={props.triggerTooltipPlacement ?? "right"}
            />
            <SelectIcon />
            {props.loading && <Spinner label="select loading spinner" />}
          </SelectTrigger>
          <SelectContent>
            <SelectListbox maxH="$xs">
              <SolidSwitch>
                <Match when={props.groups && !props.options}>
                  <For each={props.groups}>
                    {(group) => {
                      return (<>
                        <SelectOptGroup>
                          <SelectLabel>{group.label}</SelectLabel>
                          <For each={group.options}>
                            {item => (
                              <SelectOption
                                disabled={props.disabledOptions?.includes(item)}
                                textValue={item}
                                value={item}
                                px="$3" py="$2"
                                // h={props.selectOptionHeight}
                                fontSize={props.selectOptionFontSize}
                                onmouseenter={() => sfxService().playHoverSFX()}
                                onmousedown={() => sfxService().playSFX(AppSoundFx.CLICK2)}
                              >
                                <SelectOptionText>{item}</SelectOptionText>
                                <SelectOptionIndicator />
                              </SelectOption>
                            )}
                          </For>
                        </SelectOptGroup>
                      </>);
                    }}
                  </For>
                </Match>
                <Match when={props.options && !props.groups}>
                  <For each={props.options!.map(x => x.toString())}>
                    {item => (
                      <SelectOption
                        disabled={props.disabledOptions?.includes(item)}
                        textValue={props.onDisplayOption ? props.onDisplayOption(item) : item}
                        value={item}
                        px="$3" py="$2"
                        // h={props.selectOptionHeight}
                        fontSize={props.selectOptionFontSize}
                        onmouseenter={() => sfxService().playHoverSFX()}
                        onmousedown={() => sfxService().playSFX(AppSoundFx.CLICK2)}
                      >
                        <VStack alignItems={"flex-start"}>
                          <Text>{props.onDisplayOption ? props.onDisplayOption(item) : item}</Text>
                          <Show when={props.optionsMeta?.find(x => x.id == item)}>
                            {props.optionsMeta?.find(x => x.id == item)?.tagLine &&
                              <Text
                                size="sm"
                                color="$neutral11"
                                innerHTML={props.optionsMeta?.find(x => x.id == item)?.tagLine} s
                              >
                              </Text>
                            }
                            {props.optionsMeta?.find(x => x.id == item)?.element &&
                              props.optionsMeta?.find(x => x.id == item)?.element
                            }
                          </Show>
                        </VStack>
                        <SelectOptionIndicator />
                      </SelectOption>
                    )}
                  </For>
                </Match>
              </SolidSwitch>
            </SelectListbox>
          </SelectContent>
        </Select>

        {!props.showOnlySelect &&
          <>
            {!isEmpty(tooltip()) &&
              <ContentMetaText
                text={tooltip()}
                marginTop={CommonMargin}
                onHasFilteredText={(value) => { if (value === true) setIsFiltered(true); }}
                disabled={props.disabled}
              />
            }
          </>
        }
      </Box>
    }
  </>);
};

const FallBackSkeleton: Component = (props) => {
  return <Skeleton {...props} position="absolute" />;
};
export const FallBackSkeletonHope = hope(FallBackSkeleton);