import MonitorTrackingService from "~/services/monitor-tracking.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { save } from '@tauri-apps/plugin-dialog';
import { BaseDirectory, exists, readFile, writeFile } from '@tauri-apps/plugin-fs';
import { COMMON } from "~/util/const.common";
import { logError } from "~/util/logger";
import SwalPR from "~/util/sweetalert";
import { useService } from "solid-services";
import { MenuContentButton, MenuContentSwitch, MenuGeneralSubSection, MenuResetSettingsButton } from "./common.content";
import notificationService from "~/services/notification.service";

const ApplicationContent = () => {
  const category = "general";
  const menu = "application";

  const appSettingsService = useService(AppSettingsService);
  const sfxService = useService(SoundEffectsService);
  const monitorService = useService(MonitorTrackingService);

  return (<>
    <MenuGeneralSubSection label="General">
      <MenuContentSwitch
        skipTranslate settingsKey="AUTO_CHECK_FOR_UPDATES" category={category} menu={menu}
        tooltip={`Whether the application should check for updates periodically.`}
        elementLabel="Check For Updates"
      />

      {<MenuContentSwitch
        skipTranslate settingsKey="ENABLE_DEBUG_MODE" category={category} menu={menu}
        tooltip={`Puts PianoRhythm in a kind of debug mode. Logging should be a bit more verbose
        and certain features should/will be more debug friendly.
      `}
        elementLabel="Enable Debug Mode"
      />
      }
    </MenuGeneralSubSection>

    <MenuGeneralSubSection label="Advanced">
      <MenuContentButton
        elementLabel="Open Cookie Management"
        tooltip="Click to manage your cookie preferences."
        skipTranslate
        onClick={async () => {
          monitorService().showCookiePreferences();
        }}
      />

      {COMMON.IS_DESKTOP_APP && <MenuContentButton
        elementLabel="Export Log File"
        tooltip="Click to choose a location for PianoRhythm's log file."
        skipTranslate
        onClick={async () => {
          try {
            let filePath = `logs\\Pianorhythm.log`;
            let fileExists = await exists(filePath, { baseDir: BaseDirectory.AppLocalData });

            if (!fileExists) {
              notificationService.show({
                description: "Sorry, there's no log file to export at the moment. Please try again later.",
                type: "warning"
              });

              return;
            }

            const selected = await save({
              title: "Save PianoRhythm Log File",
              defaultPath: filePath,
              filters: [{ name: "Log Files", extensions: ["log"] }],
            });
            if (!selected) return;

            const contents = await readFile(filePath, { baseDir: BaseDirectory.AppLocalData });
            await writeFile(selected, contents);

            notificationService.show({
              description: "Log file has been exported successfully.",
              type: "success"
            });
          } catch (e) {
            notificationService.show({
              description: "Something went wrong trying to export the log file. Please check the console logs for more details.",
              type: "danger"
            });
            logError(`Something went wrong trying to export the log file: ${e}`);
          }
        }}
      />
      }

      {COMMON.IS_DESKTOP_APP && false && <MenuContentButton
        elementLabel="Open App Data Folder"
        tooltip="Click to open PianoRhythm's App Data folder."
        skipTranslate
        onClick={async () => {
          try {

          } catch (e) {
            notificationService.show({
              description: "Something went wrong trying to open the App Data folder. Please check the console logs for more details.",
              type: "danger"
            });
            logError(`Something went wrong trying to open the App Data folder: ${e}`);
          }
        }}
      />
      }
    </MenuGeneralSubSection>

    <MenuResetSettingsButton
      settingsName="Application Settings"
      onClickOverride={() => {
        SwalPR(sfxService).fire({
          icon: "warning",
          title: "Reset Settings",
          text: "Are you sure you want to reset your settings to the default? This action is irreversible!",
          showCancelButton: true,
        }).then((result) => {
          if (result.isConfirmed) {
            appSettingsService().resetSettingsToDefault();
            SwalPR(sfxService).fire({
              icon: "success",
              title: "Reset Settings",
              text: "Settings are now set to the default. You may have to reload the app to reflect certain changes.",
              showDenyButton: true,
              denyButtonText: "Reload",
            }).then((result) => {
              if (result.isDenied) {
                document.location.reload();
              }
            });
          }
        });
      }}
    />
  </>);
};

export default ApplicationContent;