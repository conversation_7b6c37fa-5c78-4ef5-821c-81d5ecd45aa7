import { Box, Input } from "@hope-ui/solid";
import { open } from '@tauri-apps/plugin-dialog';
import clamp from "lodash-es/clamp";
import isEqual from "lodash-es/isEqual";
import { createEffect, createSignal, lazy, onCleanup, Suspense } from "solid-js";
import { useService } from "solid-services";
import AudioService from "~/services/audio.service";
import DisplaysService from "~/services/displays.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { AudioSynthesizerEngine, DEFAULT_SOUNDFONT, FULLY_FEATURED_SYNTHS, Soundfonts, SoundfontSetting } from "~/types/audio.types";
import { AUDIO, COMMON } from "~/util/const.common";
import SwalPR from "~/util/sweetalert";
import { MenuContentButton, MenuContentSelect, MenuContentSelectItemMeta, MenuContentSlider, MenuContentSwitch, MenuGeneralSubSection, MenuResetSettingsButton, MenuSectionWithSearchFilter, NOTES } from "./common.content";
import notificationService from "~/services/notification.service";
import clsx from "clsx";

const AudioDevicesMenuSelect = lazy(() => import("./audio-devices-menuselect"));

const AudioSoundfontContent = () => {
  const appSettingsService = useService(AppSettingsService);
  const audioService = useService(AudioService);
  const sfxService = useService(SoundEffectsService);
  const displayService = useService(DisplaysService);

  const [loading, setLoading] = createSignal(false);
  const [loadedSoundfont, setLoadedSoundfont] = createSignal<string>();
  const [soundfontOptions, setSoundfontOptions] = createSignal<string[]>([]);
  const [soundfontMeta, setSoundfontMeta] = createSignal<MenuContentSelectItemMeta[]>([], { equals: isEqual });

  const category = "audio";
  const menu = "soundfont";
  const menu2 = "midi";
  const soundfontFileInputID = "#sound-font-reader";

  let soundfontFileInputElement!: HTMLInputElement;
  let soundfontSFZFileInputElement!: HTMLInputElement;
  const sfSettingsKey = "SELECTED_SOUNDFONT";
  const audioSampleSettingsKey = "AUDIO_SAMPLE_RATE";
  const channelInterpolationMethodSettingsKey = "AUDIO_CHANNEL_INTERPOLATION_METHOD";
  const loadCustomID = "-- Load Custom Soundfont --";
  const loadCustomID2 = "-- Load Custom SFZ Soundfont --";
  let prevLoadedSfTimeout = -1;

  async function tryAndLoadSoundfont(soundfont: string, isCustomSoundfont = false, cacheFile = false) {
    return audioService()
      .loadSoundfont(soundfont, isCustomSoundfont, cacheFile)
      .finally(() => setLoading(false));
  }

  const onLoadCustomSoundfont = async () => {
    if (COMMON.IS_DESKTOP_APP) {
      const selected = await open({
        multiple: false,
        directory: false,
        recursive: false,
        filters: [{
          name: 'Soundfont',
          extensions: [
            'sf2',
          ]
        }]
      });

      if (selected === null) {
        setLoading(false);
      } else {
        tryAndLoadSoundfont(selected.path as string, true).then(() => {
          appSettingsService().saveSetting("SELECTED_SOUNDFONT", { name: selected.name as string } as SoundfontSetting);
        }).catch(() => { });
      }
    } else {
      if (soundfontFileInputElement) soundfontFileInputElement.click();
    }
  };

  const onLoadDefaultSoundfont = async () => {
    try {
      await tryAndLoadSoundfont(DEFAULT_SOUNDFONT, false, true);
      appSettingsService().saveSetting("SELECTED_SOUNDFONT", { name: DEFAULT_SOUNDFONT } as SoundfontSetting);
    } catch (err) {
      sfxService().playErrorSFX();
      notificationService.show({
        id: "load-default-sf-settings-click",
        type: "danger",
        title: "Load Default Soundfont Error",
        description: (err || "Unknown error") as string
      });
      console.error("[Load Default Soundfont Error]", err);
    }
  };

  createEffect(() => {
    let set = new Set(Soundfonts.keys());
    let currentSFName = audioService().loadedSoundfontName();
    setLoadedSoundfont(currentSFName);

    setSoundfontMeta(
      [
        { id: loadCustomID, tagLine: `.sf2` },
        ...Array.from(Soundfonts).map(([id, details]) => {
          return {
            id: id, tagLine: `Size: ${details.size}`, rawSize: details.size
          };
        })
      ].filter(Boolean)
    );

    if (currentSFName && !set.has(currentSFName)) {
      set.add(currentSFName);
      //@ts-ignore
      setSoundfontMeta(v => [...v, { id: currentSFName, tagLine: `Custom` }]);
    }

    set.add(loadCustomID);
    setSoundfontOptions(Array.from(set));
  });

  const checkIfSynthIsSupported = () => !FULLY_FEATURED_SYNTHS.includes(audioService().audioSynthEngine());

  onCleanup(() => {
    window.clearTimeout(prevLoadedSfTimeout);
  });

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection label="General">
        <MenuContentSelect
          label="Main Soundfont"
          placeholder="Choose a soundfont..."
          settingsKey={sfSettingsKey}
          ignoreOnSave={[loadCustomID, loadCustomID2]}
          value={loadedSoundfont()}
          options={soundfontOptions()}
          optionsMeta={soundfontMeta()}
          disabled={loading() || audioService().loadingSoundfont()}
          onSelected={async (value) => {
            if (audioService().loadedSoundfont()?.toLowerCase() == value.toLowerCase()) return;

            if (value.toLowerCase() == loadCustomID.toLowerCase()) {
              await onLoadCustomSoundfont();
              return;
            }

            if (value.toLowerCase() == loadCustomID2.toLowerCase()) {
              if (soundfontSFZFileInputElement) soundfontSFZFileInputElement.click();
              return;
            }

            setLoading(true);
            let _meta = soundfontMeta().find(x => x.id == value);

            // If the soundfont is less than 5mb, we don't cache it.
            let cacheFile = (_meta && _meta.rawSize && parseFloat(_meta.rawSize) > 5 * 1024 * 1024) || false;

            try {
              await tryAndLoadSoundfont(value, false, cacheFile);
            } catch {
              let prev_loaded = loadedSoundfont();
              window.clearTimeout(prevLoadedSfTimeout);
              prevLoadedSfTimeout = window.setTimeout(() => setLoadedSoundfont(prev_loaded), 1000);
            }
          }}
        />

        <MenuContentButton
          elementLabel="Load Custom Soundfont"
          tooltip="Click to load your own soundfont located on your machine."
          skipTranslate
          disabled={loading() || audioService().loadingSoundfont()}
          onClick={async () => { await onLoadCustomSoundfont(); }}
        />

        <MenuContentButton
          elementLabel="Load Default Soundfont"
          tooltip="Click to load the default soundfont."
          skipTranslate
          disabled={loading() || audioService().loadingSoundfont() || audioService().loadedSoundfontName() == DEFAULT_SOUNDFONT}
          onClick={async () => {
            await onLoadDefaultSoundfont();
          }}
        />

        <MenuContentSelect
          label="Audio Synthesizer"
          placeholder="Select an synthesizer"
          options={Object.values(AudioSynthesizerEngine)}

          defaultValue={appSettingsService().getSetting("AUDIO_SYNTH_ENGINE")}
          settingsKey={"AUDIO_SYNTH_ENGINE"}
          tooltip={`
            The target audio synthesizer.
            If a soundfont doesn't sound proper, here's where you can try switching to a different synthesizer.
            <br><br>
            <i>Certain synthesizers may be experimental or not be fully featured. Use at your own discretion.</i>
            <br><br>
            ${NOTES.REQUIRES_RELOAD}
          `}
        />

        {COMMON.IS_DESKTOP_APP &&
          <Suspense>
            <AudioDevicesMenuSelect />
          </Suspense>
        }

        <MenuContentSelect
          label="Audio Sample Rate"
          placeholder="Select a sample rate..."
          settingsKey={audioSampleSettingsKey}

          defaultValue="AUTO"
          options={[
            "AUTO", "8000", "11025", "16000", "22050", "44100", "48000", "88200", "96000"
          ]}
          tooltip={`
        By default (AUTO), the AudioContext will pick a sample rate that matches the underlying device.
        <br/><br/>
        In developing an audio sound for computers or telecommunication, the sample rate is the number of samples of a
        sound that are taken per second to represent the event digitally. The more samples taken per second,
        the more accurate the digital representation of the sound can be.
        <br><br>
        ${NOTES.REQUIRES_RELOAD}
      `}
        />

        {/* <MenuContentSelect
      label="Audio Buffer Size"
      placeholder="Select a buffer size..."
      settingsKey={audioBufferSizeSettingsKey}

      options={[
        "32", "64", "128", "256", "512", "1024", "2048", "4096"
      ]}
      tooltip={`
        Buffer Size is the amount of time allowed for your computer to process the
        audio of your sound card or audio interface.
        <br><br>
        This applies when experiencing <b>latency, which is a delay in processing audio in real-time.</b>
        You can reduce your buffer size to reduce latency but this can result in a higher burden on your
        computer that can cause glitchy audio or drop-outs.
        <br><br>
        ${NOTES.REQUIRES_RELOAD}
      `}
    /> */}

        <Input
          id={soundfontFileInputID}
          ref={soundfontFileInputElement}
          className={"hidden-file-input"}
          pointerEvents="none"
          accept=".sf2, .sf3"
          type="file"
          onChange={async () => {
            let input = soundfontFileInputElement;
            if (input != null) {
              let file = input.files?.[0];

              if ((input.files?.length || 0) > 0 && file != null) {
                if (COMMON.IS_WEB_APP && file?.size > 2n * 1024n * 1024n * 1024n) {
                  audioService().soundfontLoadingFailedNotify(
                    <>
                      <Box as="b" color={"$danger10"}>File is too large! Max 2GB</Box>
                      <Box>{file?.name}</Box>
                    </>
                  );
                  return;
                }

                let fileReader = new FileReader();
                let fileName = `${file?.name || "Unknown-Soundfont-Name"}`;

                const cachedFileName = `${COMMON.SOUNDFONT_CACHE_PREFIX} ${fileName}`;

                function onSuccess() {
                  return tryAndLoadSoundfont(cachedFileName, true).then(() => {
                    appSettingsService().saveSetting("SELECTED_SOUNDFONT", { name: fileName } as SoundfontSetting);
                  }).catch((err) => {
                    audioService().soundfontLoadingFailedNotify(`Error: ${err}`);
                    setLoading(false);
                  });
                }

                fileReader.onload = async (event) => {
                  let result = event.target?.result;

                  if (result) {
                    let data = new Uint8Array(result as ArrayBuffer);
                    await audioService().saveCustomSoundfont(cachedFileName, data);
                    await onSuccess();
                  } else {
                    audioService().soundfontLoadingFailedNotify(
                      <>
                        Something went wrong! No results were found.
                        <br /><br />
                        File: {fileName}
                      </>
                    );
                    setLoading(false);
                  }
                };

                fileReader.onerror = (event) => {
                  console.error("Read soundfont error:", event.target?.error);
                  audioService().soundfontLoadingFailedNotify(
                    <>
                      Failed to read soundfont.
                      <br /><br />
                      File: {fileName}
                    </>
                  );
                  setLoading(false);
                };

                fileReader.onabort = (event) => {
                  audioService().soundfontLoadingFailedNotify(
                    <>
                      Soundfont file load was aborted!
                      <br /><br />
                      File: {fileName}
                    </>
                  );
                  setLoading(false);
                };

                if (file) {
                  setLoading(true);

                  if (await audioService().customSoundfontExist(cachedFileName)) {
                    return await onSuccess();
                  }

                  fileReader.readAsArrayBuffer(file);
                }
              }
            }
          }}
        />

        {/* <MenuContentSlider
      label={`Soundfont - volume release: ${audioService().noteVolumeRelease()}`}
      tooltip={`
        The final phase determines the speed (in seconds) at which a sound ends from the moment you release the key.
        Depending on the desired sound, the release time can be short or long.`}
      settingsKey="AUDIO_MIN_VOLUME_RELEASE"
      id={`${category}-${menu}-volume-release-slider`}
      value={audioService().noteVolumeRelease()}
      step={0.01}
      minValue={0.1}
      maxValue={3.01} />
   */}

        {/* <MenuContentSwitch
      skipTranslate
      settingsKey="AUDIO_USE_DEFAULT_INSTRUMENT_WHEN_MISSING_FOR_OTHER_USERS"
      category={category} menu={menu}
      tooltip={`
        When enabled, the audio engine will use the default instrument
        for other users that are using a bank/program that does not exist in your
        current soundfont.
        <br>
        This is to mitigate non-audible sounds when users are using different soundfonts.
      `}
      elementLabel="Use Default Instrument For Other Users When Missing"
    /> */}
        <MenuContentSwitch settingsKey="AUDIO_MOUSE_POS_SETS_VELOCITY" category={category} menu={menu2} tooltip="tgl_mousePosVelocity" elementLabel="tgl_mousePosVelocity" />
        <MenuContentSwitch settingsKey="AUDIO_MIDI_OUTPUT_ONLY" category={category} menu={menu2} tooltip="lbl_enableAudioToOutput" elementLabel="lbl_enableAudioToOutput" />

        {/* <MenuContentSwitch settingsKey="MIDI_ENABLE_STEREO_PANNING" category={category} menu={menu2} tooltip="tgl_enableStereoPanning" elementLabel="tgl_enableStereoPanning" /> */}
        <MenuContentSlider
          label={`max polyphony: ${audioService().maxPolyphony()}`}
          tooltip="Determines the max amount of distinct notes the audio engine can play at one time."
          settingsKey="AUDIO_MAX_POLYPHONY"
          id={`${category}-${menu}-max-polyphony-slider`}
          value={clamp(audioService().maxPolyphony(), 1, AUDIO.MAX_POLYPHONY)}
          disabled={checkIfSynthIsSupported()}
          step={1}
          minValue={1}
          maxValue={AUDIO.MAX_POLYPHONY} />

        <MenuContentSlider
          label={`max note on time: ${audioService().maxNoteOnTime()}`}
          tooltip="Determines the max amount of time (in seconds) that a sound can last, while a key is pressed."
          settingsKey="AUDIO_MAX_NOTE_ON_TIME"
          id={`${category}-${menu}-max-note-on-slider`}
          disabled={checkIfSynthIsSupported()}
          value={clamp(audioService().maxNoteOnTime(), 1, AUDIO.MAX_NOTE_ON_TIME)}
          step={0.1}
          minValue={1}
          maxValue={AUDIO.MAX_NOTE_ON_TIME} />
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Equalizer">
        <MenuContentButton
          elementLabel="Open Equalizer Settings"
          skipTranslate
          onClick={async () => { displayService().setDisplay("AUDIO_EQUALIZER_MODAL", true); }}
        />
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Reverb">
        <MenuContentSwitch skipTranslate settingsKey="AUDIO_ENABLE_REVERB"
          category={category} menu={menu}
          tooltip="Enable audio reverb. Reverb is the persistence of sound after a sound is produced."
          elementLabel="Enable Audio Reverb"
          disabled={checkIfSynthIsSupported()}
        />

        {!checkIfSynthIsSupported() && <Box
          className={clsx([
            !audioService().reverbEnabled() && appSettingsService().settingSaved() && "disabled"
          ])}
        >
          <MenuContentButton
            elementLabel="Open Reverb Settings"
            skipTranslate
            onClick={async () => { displayService().setDisplay("AUDIO_REVERB_MODAL", true); }}
          />
        </Box>
        }
      </MenuGeneralSubSection >

      <MenuGeneralSubSection label="Caching" >
        {COMMON.IS_DESKTOP_APP &&
          <>
            <MenuContentSwitch
              skipTranslate settingsKey="DESKTOP_SAVE_SOUNDFONTS" category={category} menu={menu}
              tooltip={`Determines whether soundfonts loaded from PianoRhythm's cloud should be saved locally on your machine.
            This should help reduce load times.
            `}
              elementLabel="Cache Soundfonts Locally"
            />
          </>
        }

        {COMMON.IS_WEB_APP &&
          <>
            <MenuContentSwitch
              skipTranslate settingsKey="AUDIO_CACHE_SOUNDFONTS_WEB" category={category} menu={menu}
              tooltip={`Determines whether soundfonts loaded from PianoRhythm's cloud should be saved locally on your machine.
            This should help reduce load times.
          `}
              elementLabel="Cache Soundfonts Locally"
            />
          </>
        }

        <MenuContentButton
          elementLabel="Clear Soundfonts Cache"
          skipTranslate
          tooltip={`This will clear your cache. If you're on the web client, it will clear up the soundfonts stored in IndexedDB.
          If you're on desktop, it will clear the cached soundfonts saved in the app's data folder.
      `}
          onClick={async () => {
            SwalPR(sfxService).fire({
              title: "Clear Soundfonts Cache",
              icon: "warning",
              text: "Are you sure you want to clear the soundfonts that have been cached? This action is irreversible.",
              showCancelButton: true
            }).then(async (result) => {
              if (result.isConfirmed) {

                let onSuccess = () => {
                  SwalPR(sfxService).fire({
                    title: "Clear Soundfonts Cache",
                    icon: "success",
                    text: "Soundfonts cache has been cleared.",
                  });
                };

                let onFail = () => {
                  SwalPR(sfxService).fire({
                    title: "Clear Soundfonts Cache",
                    icon: "error",
                    html: `
                  Something went wrong trying to clear the Soundfonts cache!
                  <br><br><b>Please check the console error logs for more info.</b>
                `,
                  });
                };

                await audioService().tryAndClearCache(onSuccess, onFail);
              }
            });
          }}
        />
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Advanced" >
        <MenuContentSelect
          label="Interpolation Method"
          placeholder="Select an interpolation method..."
          settingsKey={channelInterpolationMethodSettingsKey}
          disabled={checkIfSynthIsSupported()}

          defaultValue="SeventhOrder"
          options={[
            "None", "Linear", "FourthOrder", "SeventhOrder"
          ]}
          optionsMeta={[
            { id: "None", tagLine: `Fastest, but questionable audio quality.` },
            { id: "Linear", tagLine: `A bit slower, reasonable audio quality.` },
            { id: "FourthOrder", tagLine: `Requires 50% of the whole DSP processing time, good quality (default)` },
            { id: "SeventhOrder", tagLine: `Consumes more processing time but has highest quality.` },
          ]}
          tooltip={`
        Synthesis interpolation method. If you are experiencing any weird
        issues with certain soundfonts, messing around with this setting may help.
      `}
        />

        {COMMON.IS_WEB_APP &&
          < MenuContentSwitch
            skipTranslate
            settingsKey="AUDIO_USE_WORKLET"
            category={category} menu={menu}
            tooltip={`
          If enabled, the audio engine will use the AudioWorklet API for audio processing.
          This introduces some slight input latency but should provide better audio quality and stability.
          <br/><br/>
          ${NOTES.REQUIRES_RELOAD}
        `}
            elementLabel={`Use AudioWorklet for audio processing`}
          />
        }

        <MenuResetSettingsButton
          settingsName="Soundfont Settings"
          onConfirm={() => {
            appSettingsService().resetAudioSoundfontSettingsToDefault();
            onLoadDefaultSoundfont();
          }}
        />
      </MenuGeneralSubSection>
    </MenuSectionWithSearchFilter>
  </>);
};

export default AudioSoundfontContent;