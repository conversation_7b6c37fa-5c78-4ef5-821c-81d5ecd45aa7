import { Box, Button, ButtonGroup, Input, Skeleton, VStack } from "@hope-ui/solid";
import { Component, For, Show, createEffect, createResource, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import { MenuContentHeader, MenuGeneralSubSection } from "./common.content";
import css from "~/sass/billing-content.module.scss";
import AppService from "~/services/app.service";
import I18nService from "~/services/i18n.service";
import SoundEffectsService from "~/services/sound-effects.service";
import SwalPR from "~/util/sweetalert";
import { BillingSettingsHelper } from "~/types/user-helper";
import { CHANNELS } from "~/util/const.common";

interface IProduct {
  _id: string;
  name: string;
  description: string;
  features: string[];
}

interface IPrice {
  _id: string;
  productID: string;
  priceType: string;
  amount: number;
  interval: string;
}

type ActiveProductsWithPrices = {
  product: IProduct;
  prices: Array<IPrice>;
};

type GetProductsResponse = {
  products: ActiveProductsWithPrices[];
  level1ProductID: string;
  level2ProductID: string;
  level3ProductID: string;
};

type BillingCheckoutResponse = {
  error?: string;
  output: {
    sessionID: string,
    locationURL: string;
  } | string;
};

let findPriceForTarget = (target: string, prices: Array<IPrice>) => {
  try {
    return prices.find(x => x.interval?.toLowerCase().includes(target?.toLowerCase()));
  } catch {
    return null;
  }
};

let [chooseAnnual, setChooseAnnual] = createSignal(false);
let [sessionID, setSessionID] = createSignal<string>();
let [initialPrice, setInitialPrice] = createSignal<string>();
let [activePrice, setActivePrice] = createSignal<string>();

const Product: Component<{ activeProduct: ActiveProductsWithPrices; }> = (props) => {
  const [savePercentage, setSavePercentage] = createSignal(0);
  const [currentPrice, setCurrentPrice] = createSignal<IPrice | null>();
  const [currentAmount, setCurrentAmount] = createSignal(0);
  let monthlyPrice = findPriceForTarget("month", props.activeProduct.prices);
  let annualPrice = findPriceForTarget("year", props.activeProduct.prices);
  let isSelectedPrice = () => activePrice() == currentPrice()?._id;

  createEffect(() => {
    if (annualPrice && monthlyPrice) {
      let monthlyToAnnual = monthlyPrice.amount * 12;
      setSavePercentage(100 - ((annualPrice.amount / monthlyToAnnual) * 100));
    }
  });

  createEffect(() => {
    setCurrentPrice(chooseAnnual() ? annualPrice : monthlyPrice);
    setCurrentAmount(currentPrice()?.amount || - 1);
  });

  function onSelect() {
    let _currentPrice = currentPrice();
    if (_currentPrice == null) return;

    if (!isSelectedPrice()) {
      setActivePrice(_currentPrice._id);
    } else {
      if (initialPrice() && initialPrice() != _currentPrice._id) {
        setActivePrice(undefined);
      }
    }
  }

  return (<>
    <Box>
      <Box class={[
        css["pricing-table"],
        (isSelectedPrice() && css["--is-selected"])
      ].join(" ")}>
        <Box class={[css["package-title"]].join(" ")}>
          {isSelectedPrice() && <Box className={css["package-selected"]}></Box>}
          <Box as="span">{props.activeProduct.product.name}</Box>
          {(chooseAnnual() && savePercentage() > 0) &&
            <Box as="span" className={css["sale"]}>Save {Math.trunc(savePercentage())}%</Box>
          }
        </Box>
        <Box class={[css["package-layout"]].join(" ")}>
          <Box class={[css["package-currency"]].join(" ")}>$</Box>
          <Box class={[css["package-value"]].join(" ")}>
            <Box class={[css["value"]].join(" ")}>{currentAmount() / 100}</Box>
            <Box class={[css["period"]].join(" ")}>/ {chooseAnnual() ? "year" : "month"}</Box>
          </Box>
        </Box>
        <Box class={[css["divider"]].join(" ")}></Box>
        <Box class={[css["terms"]].join(" ")}>
          <For each={props.activeProduct.product.features}>
            {(feature) => {
              return <Box class={[css["term"]].join(" ")}>{feature}</Box>;
            }}
          </For>
        </Box>
        <Button
          class={[
            css["subscribe"],
            (isSelectedPrice() && css["--is-selected"])
          ].join(" ")}
          onclick={onSelect}
        >
          {isSelectedPrice() ? "Selected" : "Click here to Subscribe!"}
        </Button>
      </Box>
    </Box>
  </>);
};

function getProducts() {
  return axios.get<GetProductsResponse>('/api/billing/get-products').then((response) => {
    return Promise.resolve(response.data.products);
  });
}

const ProductsDisplay = () => {
  const [disableCheckout, setDisableCheckout] = createSignal(false);
  const [cancellationInProcess, setCancellationInProcess] = createSignal(false);
  const [checkingOut, setCheckingOut] = createSignal(false);
  const [initialUserPlan, setInitialUserplan] = createSignal<string>();
  const [planBillingDate, setPlanBillingDate] = createSignal<string>();
  const [loading, setLoading] = createSignal(false);
  const [products] = createResource<ActiveProductsWithPrices[]>(getProducts);
  const appService = useService(AppService);
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const t = i18nService().t_roomPageSettings;

  onMount(() => {
    let sub = appService().subscribeToAppDisconnected(() => {
      resetAll(true);
    });
    onCleanup(() => sub.unsubscribe());
  });

  onCleanup(() => resetAll(true));

  const onSubscriptionUpdated = () => {
    SwalPR(sfxService).fire({
      title: "Subscription Updated",
      html: t("menus.billing.messages.subscription_Updated"),
      icon: "success"
    });
    resetAll();
  };

  let resetCheckout = () => {
    setCheckingOut(false);
    setLoading(false);
  };

  let resetAll = (hardReset?: boolean) => {
    resetCheckout();
    setActivePrice(hardReset ? undefined : initialPrice());
    if (hardReset) setInitialPrice();
    setSessionID();
  };

  createEffect(() => {
    setCancellationInProcess(appService().client().billingCancellationInProcess || false);
  });

  createEffect(() => {
    setDisableCheckout(
      (
        checkingOut() ||
        loading() ||
        activePrice() == null ||
        activePrice() == initialPrice()
      ) && !cancellationInProcess()
    );
  });

  createEffect(() => {
    let _client = appService().client();
    let billingMeta = _client.billingMeta;
    let userCurrentPlan = _client.currentBillingPlan.toLowerCase();
    setInitialUserplan(userCurrentPlan);

    if (_client.hasProPlan && billingMeta) {
      let userPriceId = billingMeta.priceID;
      setActivePrice(userPriceId);
      setInitialPrice(userPriceId);
      // setPlanBillingDate(billingMeta.nextBillingDate?.toDateString());
      setPlanBillingDate(billingMeta.nextBillingDate?.toString());
    }
  });

  // After successful checkout
  createEffect(() => {
    let _client = appService().client();
    let initialPlan: string | undefined = initialUserPlan()?.toLowerCase() as any;
    let clientPlan: string = _client.currentBillingPlan.toLowerCase() as any;
    let clientPlanRank = BillingSettingsHelper.RankPlan(clientPlan);

    if (checkingOut() &&
      (clientPlanRank > 0 && initialPlan) &&
      (
        (initialPlan == clientPlan) ||
        (BillingSettingsHelper.RankPlan(initialPlan) != clientPlanRank)
      )
    ) {
      SwalPR(sfxService).fire({
        title: t("menus.billing.messages.checkout_Success"),
        icon: "success",
        showCancelButton: false,
        showDenyButton: false
      }).then(() => {
        resetCheckout();
      });
    }
  });

  function onCancelSubscription() {
    setLoading(true);
    const _client = appService().client();

    SwalPR(sfxService).fire({
      title: t("menus.billing.labels.cancelSubscription"),
      icon: "info",
      showDenyButton: true,
      confirmButtonText: t("menus.billing.buttons.cancelSubscription"),
      html: t("menus.billing.messages.cancellation_Confirm", { username: _client.username, planBillingDate: planBillingDate() }),
      preDeny: () => {
        resetAll();
      },
      preConfirm: () => {
        axios.post<{ output?: string; }>("/api/billing/cancel-subscription", { cache: false })
          .then((response) => {
            if (response.data.output == "subscription_canceled") {
              SwalPR(sfxService).fire({
                title: "Subscription Canceled",
                html: t("menus.billing.messages.cancellation_Success"),
                icon: "success"
              });
              resetAll(true);
              return;
            }

            throw new Error("error");
          })
          .catch((err) => {
            console.error(err);
            SwalPR(sfxService).fire({ icon: "error", text: t("menus.billing.messages.subscription_cancellationFail") });
            resetAll();
          });
      }
    });
  }

  function onCheckout() {
    setCheckingOut(true);
    setLoading(true);
    const _client = appService().client();

    let checkOutText =
      _client.billingCancellationInProcess ?
        t("menus.billing.messages.checkout_ResumePlan") :
        t("menus.billing.messages.checkout_Confirm");

    SwalPR(sfxService).fire({
      title: "Check Out",
      icon: "info",
      confirmButtonText: "Yes!",
      denyButtonText: "Uh, nevermind...",
      showDenyButton: true,
      html: checkOutText,
      preDeny: () => {
        resetAll();
      },
      preConfirm: () => {
        axios.post<BillingCheckoutResponse>("/api/billing/check-out", { priceID: activePrice() }, { cache: false })
          .then((response) => {
            let output = response.data.output;

            if (typeof output === "string") {
              if (output == "subscription_updated") {
                onSubscriptionUpdated();
              }
              return;
            }

            let sessionID = output.sessionID;
            setSessionID(sessionID);
            window.open(output.locationURL, "_blank");

            SwalPR(sfxService).fire({
              title: "Check Out Session",
              html: t("menus.billing.messages.checkout_Created"),
              icon: "info",
              showConfirmButton: false,
              showDenyButton: true,
              denyButtonText: "Cancel Checkout Session",
              preDeny: () => {
                axios
                  .post("/api/billing/cancel-checkout", { sessionID: sessionID })
                  .finally(() => {
                    resetAll();
                  });
              },
              backdrop: true,
              allowOutsideClick: () => {
                const popup = SwalPR(sfxService).getPopup();
                popup?.classList.remove('swal2-show');
                setTimeout(() => {
                  popup?.classList.add('animate__animated', 'animate__headShake');
                });
                setTimeout(() => {
                  popup?.classList.remove('animate__animated', 'animate__headShake');
                }, 500);
                return false;
              }
            }).then(() => {
              SwalPR(sfxService).fire(
                { icon: "info", text: t("menus.billing.messages.checkout_Cancelled") }
              );
            });
          })
          .catch((err: Error) => {
            logError("[Checkout Error]", err);
            let text =
              (err.message == "checkout_fail_already_subscribed") ?
                "Sorry, looks like you're already subscribed to that plan." :
                "Sorry, something went wrong. Failed to check out.";
            SwalPR(sfxService).fire({ icon: "error", text: text });
            resetAll();
          });
      }
    });
  }

  return (<>
    <Box
      class={[(checkingOut() && "disabled")].join(" ")}
    >
      {/* Switch Toggle Container */}
      <Box className={css["billing-toggler-container"]}>
        <Box
          id="filt-monthly"
          class={[
            css["toggler"],
            (!chooseAnnual() && css["toggler--is-active"])
          ].join(" ")}>Monthly</Box>
        <Box className={css["toggle"]}>
          <Input type="checkbox" id="switcher" className={css["check"]}
            // @ts-ignore
            onChange={(data) => setChooseAnnual(data.target.checked)}
          />
          <Box as="b" class={[css["b"], css["switch"]].join(" ")} />
        </Box>
        <Box
          id="filt-annually"
          class={[
            css["toggler"],
            (chooseAnnual() && css["toggler--is-active"])
          ].join(" ")}>Annually</Box>
      </Box>

      {/* Products Display */}
      <Box as="section" className={css["billing-products-display"]}>
        <Show when={!products.loading} fallback={<Skeleton height="350px" width="445px" margin={"0 0 8px"} />}>
          <For each={products()}>
            {(product) => {
              return (<Product activeProduct={product} />);
            }}
          </For>
        </Show>
      </Box>

      {/* Footer Container */}
      <Box as="section" className={css["billing-footer-container"]}>
        <ButtonGroup variant="outline"
        // className= {
        //   [
        //     (disableCheckout() && "disabled")
        //   ].join(" ")
        // }
        >
          <VStack spacing={"$4"}>
            <Button
              id="checkout-and-portal-button"
              w="100%"
              onclick={onCheckout}
              loading={loading() || checkingOut()}
              className={
                [
                  css["button"],
                  (disableCheckout() && "disabled")
                ].join(" ")
              } >
              {
                cancellationInProcess() ?
                  i18nService().t_roomPageSettings("menus.billing.buttons.cancellation_InProcess") :
                  i18nService().t_roomPageSettings("menus.billing.buttons.checkout")
              }
            </Button>
            {appService().client().hasProPlan && <Button
              id="unsubscribe"
              w="100%"
              onclick={onCancelSubscription}
              loading={loading()}
              className={
                [
                  css["button"],
                  (cancellationInProcess() && "disabled")
                ].join(" ")
              } >
              Cancel Subscription
            </Button>
            }
          </VStack>
        </ButtonGroup>
      </Box>
    </Box>
  </>);
};

const BillingInformation = () => {
  const [billingText, setBillingText] = createSignal<string>();
  const appService = useService(AppService);
  const i18nService = useService(I18nService);

  onMount(() => {
    let client = appService().client();
    let billingDate = client.nextBillingDate;
    let currencyText = client.billingCurrencyText;

    setBillingText(
      client.billingCancellationInProcess ?
        i18nService().t_roomPageSettings("menus.billing.messages.cancellation_InProcess", { billingDate }) :
        i18nService().t_roomPageSettings("menus.billing.messages.autoRenewInfo", { billingDate, currencyText })
    );
  });

  return (<>
    <MenuGeneralSubSection translate label="menus.billing.labels.billingInfo">
      {billingText() && <Box innerHTML={billingText()}></Box>}
    </MenuGeneralSubSection>
  </>);
};

const SubscriptionContent = () => {
  const category = "billing";
  const menu = "subscription";
  const appService = useService(AppService);
  const channel = new BroadcastChannel(CHANNELS.PRO_SUBSCRIPTION_CHECKOUT_RESULT);

  onMount(() => {
    channel.onmessage = (event) => {
      if (event.data == null) return;
      const data: { checkoutStatus: boolean; } = event.data;
    };
  });

  onCleanup(() => {
    channel.close();
  });

  return (<>
    <Box className={css["billing-component-container"]}>
      <MenuContentHeader category={category} menu={menu} />
      <br />
      <Show when={appService().client().isMember} fallback={<Box>🛑 You must be a member to access this page. Please login!</Box>}>
        <ProductsDisplay />
        {appService().client().hasProPlan && <BillingInformation />}
      </Show>
    </Box>
  </>);
};

export default SubscriptionContent;