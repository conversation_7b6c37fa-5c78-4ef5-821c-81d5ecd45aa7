import { Button, Radio, RadioGroup, VStack } from "@hope-ui/solid";
import { For, createSignal } from "solid-js";
import { useService } from "solid-services";
import style from "~/sass/settings.module.sass";
import DisplaysService from "~/services/displays.service";
import I18nService from "~/services/i18n.service";
import { LANGUAGES } from "~/util/const.common";
import { MenuGeneralSubSection } from "./common.content";

type SelectInput = {
  id: string;
  name: string;
};

const createValue = (lang: string[]): SelectInput => {
  return { id: lang[0], name: lang[1] };
};

const Languages = LANGUAGES.map(createValue);

const LanguageContent = () => {
  const i18nService = useService(I18nService);
  const displayService = useService(DisplaysService);

  const [selectedValues, setSelectedValues] = createSignal(createValue(i18nService().activeLanguage()));

  const onChange = async (selected: SelectInput) => {
    setSelectedValues(selected);
    i18nService().setActiveLanguage([selected.id, selected.name]);
  };

  return (<>
    <MenuGeneralSubSection label="Contributing">
      <Button
        onclick={() => displayService().setSidebarDocsPath(`/guides/contributing/locales/`)}
        variant={"outline"}>{"Open Contribution Guide"}
      </Button>
    </MenuGeneralSubSection>

    <MenuGeneralSubSection label="Select a language">
      <RadioGroup
        w="100%"
        marginBottom={20}
        onChange={(event: any) => {
          let lang = Languages.find(x => x.id == event);
          if (lang) onChange(lang);
        }}
        defaultValue={selectedValues().id} alignItems={"flex-start"}>
        <VStack
          spacing="$4"
          alignItems={"flex-start"} marginLeft={5}>
          <For each={Languages}>
            {(input) => {
              return (<>
                <Radio
                  class={style.language_radio}
                  onClick={() => onChange(input)}
                  colorScheme="primary"
                  value={input.id}>{input.name}</Radio>
              </>);
            }}
          </For>
        </VStack>
      </RadioGroup>
    </MenuGeneralSubSection>
  </>);
};

export default LanguageContent;