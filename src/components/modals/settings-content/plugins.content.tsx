import { PluginE<PERSON>or<PERSON>rovider, usePluginEditContext } from "@contexts/plugin-edit.context";
import { Box, HStack, <PERSON>u, <PERSON>uContent, <PERSON>uItem, MenuTrigger, <PERSON>ltip, VStack, createDisclosure, notificationService } from "@hope-ui/solid";
import { VirtualContainer, VirtualItemProps } from "@minht11/solid-virtual-container";
import { AppService } from "@services/app.service";
import { PluginService } from "@services/plugin.service";
import { AppSettingsService } from "@services/settings-storage.service";
import SoundEffectsService from "@services/sound-effects.service";
import { PluginInfo } from "@shared/models/plugins.models";
import { COMMON_MESSAGES } from "@util/const.common";
import SwalPR from "@util/sweetalert";
import { Component, Suspense, createEffect, createSignal, lazy } from "solid-js";
import { useService } from 'solid-services';
import { Menu<PERSON><PERSON>nt<PERSON><PERSON>on, <PERSON>u<PERSON>ontentD<PERSON>ider, MenuContentSwitch, MenuGeneralSubHeader } from "./common.content";
import isString from "lodash-es/isString";

const PluginEditor = lazy(() => import('@components/plugins/plugin.editor'));
const { isOpen, onOpen, onClose } = createDisclosure();
const CLEAR_PLUGINS_LABEL = "Clear All Plugins";

type ListItemData = { active: boolean, error?: string, plugin: PluginInfo; };

const ToolTipTableLabel = (columns: string[], data: string[]) => {
  return `<table>
    <tr>${columns.map(x => `<th>${x}</th>`)}</tr>
    <tr>${data.map(x => `<td>${x}</td>`)}</tr>
  </table>`;
};

const PluginList: Component<{ list: ListItemData[]; }> = (props) => {
  let activePluginsScrollTargetElement!: HTMLDivElement;

  return (
    <div
      ref={activePluginsScrollTargetElement}
      style={{
        overflow: "hidden scroll",
        "max-height": `500px`,
      }}
    >
      <VirtualContainer
        items={props.list}
        scrollTarget={activePluginsScrollTargetElement}
        itemSize={{ height: 125, width: 300 }}
        crossAxisCount={(measurements) => (
          Math.floor(measurements.container.cross / measurements.itemSize.cross)
        )}
        overscan={4}
      >
        {ListItem}
      </VirtualContainer>
    </div>
  );
};

const ListItem = (vprops: VirtualItemProps<ListItemData>) => {
  const pluginsService = useService(PluginService);
  const appService = useService(AppService);
  const [, , , { updateCodeData, updateHtmlData, updateCssData, setActivePluginID, setDoesNotBelongToClient }] = usePluginEditContext();

  return (<>
    <div
      style={{
        ...vprops.style,
        padding: "3px",
      }}
      tabIndex={vprops.tabIndex}
      role="listitem"
    >
      <Menu placement="bottom">
        {({ opened }) => (
          <>
            <MenuTrigger as="span" w="100%" h="100%">
              <Tooltip
                label={<Box
                  innerHTML={
                    ToolTipTableLabel([
                      "ID",
                      "Version",
                      "State",
                      vprops.item.error && "Error",
                    ].filter(Boolean), [
                      vprops.item.plugin.id,
                      vprops.item.plugin.meta.version,
                      vprops.item.error ? "<b style='color:red'>error</b>" : "loaded",
                      vprops.item.error || ""
                    ])
                  } />}
                placement="top"
                openDelay={500}
              >
                <Box
                  width={"100%"}
                  height="100%"
                  border="2px solid rgba(255,255,255,0.8)"
                  borderRadius={5}
                  padding={5}
                  overflow={"hidden"}
                  cursor={"pointer"}
                  transition={"150ms all ease-in-out"}
                  borderColor={opened() ? "$accent1" : vprops.item.error ? "red" : ""}
                  _hover={{
                    "background": "rgba(255,255,255,0.1)",
                    "border-color": "var(--hope-colors-accent1)",
                  }}
                >
                  <VStack spacing={"$1_5"} h="100%" alignItems={"flex-start"} justifyContent={"space-between"}>
                    <HStack w="100%" justifyContent={"space-between"}>
                      {/* Plugin Name */}
                      <Box
                        fontWeight={"bold"}
                        color={vprops.item.error ? "gray" : "white"}>
                        {vprops.item.plugin.meta.name}
                      </Box>
                      {/* Plugin Status */}
                      <Box color={vprops.item.active ? "limegreen" : "red"} fontSize={"small"}>{
                        vprops.item.error ? "Error" : (vprops.item.active ? "Active" : "Inactive")
                      }</Box>
                    </HStack>
                    {vprops.item.plugin.meta.short_description && <Box>{vprops.item.plugin.meta.short_description}</Box>}
                    <Box fontSize={"small"} color="gray">
                      By {vprops.item.plugin.usertag} |
                      Version {vprops.item.plugin.meta.version}
                    </Box>
                  </VStack>
                </Box>
              </Tooltip>
            </MenuTrigger>
            <MenuContent minW="$60">
              {appService().client().usertag == vprops.item.plugin.usertag &&
                <MenuItem onSelect={() => {
                  updateCodeData(vprops.item.plugin.data.code_data);
                  updateCssData(vprops.item.plugin.data.css_data);
                  updateHtmlData(vprops.item.plugin.data.html_data);
                  setActivePluginID(vprops.item.plugin.id);
                  onOpen();
                }}>
                  Edit
                </MenuItem>
              }
              {appService().client().usertag != vprops.item.plugin.usertag &&
                <MenuItem onSelect={() => {
                  updateCodeData(vprops.item.plugin.data.code_data);
                  updateCssData(vprops.item.plugin.data.css_data);
                  updateHtmlData(vprops.item.plugin.data.html_data);
                  setDoesNotBelongToClient(true);
                  onOpen();
                }}>
                  View
                </MenuItem>
              }
              {vprops.item.active &&
                <MenuItem onSelect={async () => {
                  await pluginsService().destroyPluginByID(vprops.item.plugin.id, false);
                }}>
                  Unload
                </MenuItem>
              }
              {!vprops.item.active &&
                <MenuItem onSelect={async () => {
                  try {
                    await pluginsService().executePlugin(vprops.item.plugin);
                  } catch {
                    notificationService.show({
                      title: "Execute Plugin - Failure",
                      description: `${COMMON_MESSAGES.CHECK_CONSOLE_LOGS}.`,
                      status: "danger"
                    })
                  }
                }}>
                  Load
                </MenuItem>
              }
              <MenuItem onSelect={async () => {
                await pluginsService().removePluginByID(vprops.item.plugin.id);
              }}>
                Remove
              </MenuItem>
            </MenuContent>
          </>
        )}</Menu>

    </div >
  </>);
};

const PluginsContent = () => {
  const appSettingsService = useService(AppSettingsService);
  const sfxService = useService(SoundEffectsService);
  const pluginsService = useService(PluginService);
  const [activePluginsHeader, setActivePluginsHeader] = createSignal("Active: 0");
  const [inactivePluginsHeader, setInactivePluginsHeader] = createSignal("Inactive: 0");
  const [hasPlugins, setHasPlugins] = createSignal(false);
  let activePluginsScrollTargetElement!: HTMLDivElement;

  createEffect(() => {
    setActivePluginsHeader(`Active: ${pluginsService().totalCount().active}`);
    setInactivePluginsHeader(`Inactive: ${pluginsService().totalCount().inactive}`);
    setHasPlugins(pluginsService().totalCount().active > 0 || pluginsService().totalCount().inactive > 0);
  });

  return (<>
    <MenuGeneralSubHeader label={`General`} />
    <MenuContentSwitch
      skipTranslate
      settingsKey="ENABLE_PLUGINS"
      elementLabel="Enable Plugins"
      tooltip="Toggle whether custom plugins are loaded in the application."
      onAlertConfirm={
        [false, () => SwalPR(sfxService).fire({
          title: "Disable Plugins",
          icon: "warning",
          text: "Are you sure you want to disable all plugins?",
          showCancelButton: true
        }).then(async (result) => {
          if (result.isConfirmed) await pluginsService().unloadAllPlugins(false);
          return result;
        })]
      }
    />

    <MenuContentButton
      elementLabel="Create New Plugin"
      skipTranslate
      disabled={isOpen() || Boolean(appSettingsService().settingSaved() && !appSettingsService().getSetting("ENABLE_PLUGINS"))}
      onClick={async () => { onOpen(); }}
      tooltip={`Create a new local plugin.`}
    />

    <PluginEditorProvider>
      {hasPlugins() &&
        < MenuGeneralSubHeader label={`${activePluginsHeader()} | ${inactivePluginsHeader()}`} />
      }
      <PluginList list={pluginsService()
        .pluginStates()
        .filter(x => !isString(x.loadedWithError))
        .map(x => ({ plugin: x.plugin, active: x.active, error: x.loadedWithError }))
        .sort(x => x.active ? 1 : 0)} />

      {(hasPlugins() && pluginsService()
        .pluginStates()
        .filter(x => isString(x.loadedWithError)).length > 0) &&
        < MenuGeneralSubHeader label={"Failed"} marginTop={20} />
      }
      <PluginList list={pluginsService()
        .pluginStates()
        .filter(x => isString(x.loadedWithError))
        .map(x => ({ plugin: x.plugin, active: false, error: x.loadedWithError }))
        .sort(x => x.active ? 1 : 0)} />
      <Suspense>
        {isOpen() && <PluginEditor onClose={onClose} /> }
      </Suspense>
    </PluginEditorProvider>

    {hasPlugins() && <MenuContentDivider />}

    <MenuGeneralSubHeader label={`Advanced`} />
    <MenuContentButton
      elementLabel="Clear All Plugins"
      skipTranslate
      tooltip={`This will delete all plugins stored in the application.`}
      onClick={async () => {
        SwalPR(sfxService).fire({
          title: CLEAR_PLUGINS_LABEL,
          icon: "warning",
          text: "Are you sure you want to clear the plugins that you have stored? This action is irreversible.",
          showCancelButton: true
        }).then(async (result) => {
          if (result.isConfirmed) {

            let onSuccess = () => {
              SwalPR(sfxService).fire({
                title: CLEAR_PLUGINS_LABEL,
                icon: "success",
                text: "All plugins have been cleared.",
              });
            };

            let onFail = () => {
              SwalPR(sfxService).fire({
                title: CLEAR_PLUGINS_LABEL,
                icon: "error",
                html: `
                  Something went wrong trying to clear the Plugins!
                  <br><br><b>Please check the console error logs for more info.</b>
                `,
              });
            };

            await pluginsService().tryAndClearAllPlugins(onSuccess, onFail);
          }
        });
      }}
    />
  </>);
};

export default PluginsContent;