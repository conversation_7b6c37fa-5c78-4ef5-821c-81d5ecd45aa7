import clamp from "lodash-es/clamp";
import { createMemo } from "solid-js";
import { useService } from "solid-services";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import { AUDIO } from "~/util/const.common";
import { MenuContentSlider, MenuContentSwitch, MenuGeneralSubSection, MenuResetSettingsButton, MenuSectionWithSearchFilter } from "./common.content";

const AudioSoundEffectsContent = () => {
  const category = "audio";
  const menu = "soundeffects";
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);

  const sfxEnabled = createMemo(() => appSettingsService().getSetting("AUDIO_SFX_ENABLE"));

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection label="General">
        <MenuContentSwitch
          skipTranslate
          settingsKey="AUDIO_SFX_ENABLE"
          category={category} menu={menu}
          tooltip="If enabled, sound effects will be emitted throughout certain UI elements."
          elementLabel="Enable Sound Effects"
        />

        <MenuContentSwitch
          skipTranslate
          settingsKey="AUDIO_SFX_CHAT_ENABLE"
          category={category} menu={menu}
          disabled={!sfxEnabled()}
          tooltip="If enabled, sound effects will be emitted for related chat UI elements."
          elementLabel="Enable Chat Sound Effects"
        />

        {/* <MenuContentSlider
          label={`Global Volume`}
          tooltip="Determines the global volume for all sound effects."
          settingsKey="AUDIO_SFX_GLOBAL_VOLUME"
          id={`${category}-${menu}-sfx-global-vol-slider`}
          value={clamp(sfxService().globalVolume(), 0, AUDIO.MAX_SFX_VOLUME)}
          step={0.01}
          minValue={0}
          disabled={!sfxEnabled()}
          maxValue={AUDIO.MAX_SFX_VOLUME} /> */}

        <MenuContentSlider
          label={`Stage Effects Volume`}
          tooltip="Determines the global volume for stage sound effects."
          settingsKey="AUDIO_SFX_STAGE_EFFECTS_GLOBAL_VOLUME"
          id={`${category}-${menu}-sfx-stage-fx-global-vol-slider`}
          value={clamp(sfxService().stageEffectsVolume(), 0, AUDIO.MAX_SFX_VOLUME)}
          step={0.01}
          minValue={0}
          // disabled={!sfxEnabled()}
          maxValue={AUDIO.MAX_SFX_VOLUME} />

        {/* <MenuContentSlider
      label={`UI Element Hover Volume: ${sfxService().hoverVolume()}`}
      tooltip="Determines the volume for only the hovering over an element sound effect."
      settingsKey="AUDIO_SFX_HOVER_VOLUME"
      id={`${category}-${menu}-sfx-hover-vol-slider`}
      value={clamp(sfxService().hoverVolume(), 0, AUDIO.MAX_SFX_VOLUME)}
      disabled
      step={0.01}
      minValue={0}
      maxValue={AUDIO.MAX_SFX_VOLUME} /> */}
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Advanced" >
        <MenuResetSettingsButton
          settingsName="Sound Effects Settings"
          onConfirm={() => { appSettingsService().resetSoundFxSettingsToDefault(); }}
        />
      </MenuGeneralSubSection>
    </MenuSectionWithSearchFilter>
  </>);
};

export default AudioSoundEffectsContent;