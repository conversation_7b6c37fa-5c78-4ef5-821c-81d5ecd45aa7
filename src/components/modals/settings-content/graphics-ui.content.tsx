import { useService } from "solid-services";
import AppThemesService from "~/services/app.themes.service";
import { AppThemes } from "~/types/app.types";
import { MenuContentSelect, MenuContentSwitch, MenuGeneralSubSection, MenuSectionWithSearchFilter } from "./common.content";

const GraphicsUIContent = () => {
  const category = "graphics";
  const menu = "ui";
  const themesService = useService(AppThemesService);

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection label="General">
        <MenuContentSelect
          label="Theme"
          placeholder="Select a theme..."
          settingsKey={"UI_THEME"}
          defaultValue={AppThemes[AppThemes.DEFAULT]}
          onTranslateValue={(value) => AppThemes[value as any]}
          options={Object.values(AppThemes).filter(value => isNaN(Number(value)) === true) as string[]}
          tooltip={`UI theme presets.`}
          onSelected={async (value) => {
            themesService().setTheme(AppThemes[value as any] as any as number);
          }}
        />
        <MenuContentSwitch
          skipTranslate settingsKey="AUTOHIDE_BOTTOMBAR" category={category} menu={menu}
          tooltip="Toggle whether the menu bottom bar auto hides, once the cursor leaves the area"
          elementLabel="Auto hide bottom bar"
        />
        <MenuContentSwitch settingsKey="DISPLAY_INST_DOCK" category={category} menu={menu} tooltip="tgl_instDock" elementLabel="tgl_instDock" />
        <MenuContentSwitch
          skipTranslate settingsKey="DISPLAY_SCENE_WIDGET_BUTTONS"
          category={category}
          menu={"general"}
          tooltip={`
        Display the side widget buttons (on the right). A lot of common actions (locking camera, stage effects, etc) will appear here.
      `}
          elementLabel="Display Side Widget Buttons"
        />
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Graphics Engine">
        <MenuContentSwitch
          skipTranslate settingsKey="DISPLAY_FPS"
          category={category}
          menu={"general"}
          tooltip={`
            Display the FPS (frames per second) counter of the scene.
          `}
          elementLabel="Display FPS"
        />
        <MenuContentSwitch
          disabled //TODO: Implement this later
          skipTranslate settingsKey="GRAPHICS_DISPLAY_RENDER_STATS"
          category={category}
          menu={"general"}
          tooltip={`
            Display rendering statistics about the current scene.
          `}
          elementLabel="Display Render Stats"
        />
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Chat">
        <MenuContentSwitch settingsKey="DISPLAY_WHOISTYPING" category={category} menu={menu} tooltip="tgl_whoistyping" elementLabel="tgl_whoistyping" />
        <MenuContentSwitch settingsKey="DISPLAY_CHAT" category={category} menu={menu} tooltip="tgle_displayChat" elementLabel="tgle_displayChat" />
        <MenuContentSwitch skipTranslate settingsKey="CHAT_AUTO_SCROLL" category={category} menu={menu} tooltip="Always auto scroll to the bottom for new messages" elementLabel="Always Auto Scroll Chat" />
        <MenuContentSwitch skipTranslate settingsKey="CHAT_ENABLE_IMAGE_URL_PREVIEW" category={category} menu={menu} tooltip="Enable showing url previews using meta info from the site." elementLabel="Enable URL Preview" />
        <MenuContentSwitch skipTranslate settingsKey="CHAT_DISABLE_MARKDOWN" category={category} menu={menu}
          tooltip="Disable Markdown (text formatting/markup) in chat messages. <br><br><i>Note: Takes effect on next restart.</i>" elementLabel="Disable Chat MarkDown" />
        <MenuContentSwitch disabled settingsKey="KEEP_CHAT_IN_FOCUS" category={category} menu={menu} tooltip="tgle_keepChatInFocus" elementLabel="tgle_keepChatInFocus" />
        <MenuContentSwitch disabled settingsKey="SHOW_EMBEDDED_LINKS" category={category} menu={menu} tooltip="tgle_showEmbeddedLinks" elementLabel="tgle_showEmbeddedLinks" />
      </MenuGeneralSubSection>

      {/* TODO */}
      {/* <MenuGeneralSubSection label="Notifications">
        <MenuContentSwitch
          skipTranslate settingsKey="ENABLE_DESKTOP_NOTIFICATIONS" category={category} menu={menu}
          tooltip={`
        Toggle whether PianoRhythm can emit desktop notifications to you in your system tray.
        <br><br>
        <i>Note: You must have Notifications enabled in your browser in order for it to work.</i>
      `}
          elementLabel="Enable Desktop Notifications"
        />
      </MenuGeneralSubSection> */}

      {/* TODO */}
      {/* <MenuGeneralSubSection label="SideBar" >
        <MenuContentSwitch skipTranslate
          settingsKey="UI_ENABLE_USER_NOTE_ACTIVITIES" category={category} menu={menu}
          tooltip="Enable seeing any note activities being reflected (like the user icon going up and down) on the user item in the sidebar."
          elementLabel="Enable User Note Activities" />
      </MenuGeneralSubSection> */}

      <MenuGeneralSubSection label="Advanced" >
        <MenuContentSwitch
          skipTranslate settingsKey="ONLINE_WARN_ABOUT_EXTERNAL_LINKS"
          tooltip={`If enabled, a popup will be displayed about warning you about opening external links in chat.`}
          elementLabel="Warn about opening external links"
        />
        <MenuContentSwitch skipTranslate
          disabled //TODO: Implement this later
          settingsKey="UI_ENABLE_SYNTH_NOTE_ACTIVITIES" category={category} menu={menu}
          tooltip="Enable seeing any note activities being reflected in certain UI components like the channels flashing in the instrument dock."
          elementLabel="Enable Synth Note Activities" />
      </MenuGeneralSubSection >
    </MenuSectionWithSearchFilter>
  </>);
};

export default GraphicsUIContent;;