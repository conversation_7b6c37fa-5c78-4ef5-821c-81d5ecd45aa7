import { MenuContentSwitch, MenuGeneralSubSection, NOTES } from "./common.content";

const GraphicsWorldContent = () => {
  return (<>
    <MenuGeneralSubSection label="General" >
      <MenuContentSwitch settingsKey="GRAPHICS_ENABLE_AVATARS"
        tooltip={`
          Toggle whether 3D avatars should show up in the scene.
        `}
        skipTranslate
        disabled
        elementLabel="Enable 3D Avatars"
      />
      {/* <MenuContentSwitch
        skipTranslate settingsKey="GRAPHICS_ENABLE_PHYSICS"
        disabled
        tooltip={`
      Toggle the game world's physics engine.
      <br><br>
      ${NOTES.REQUIRES_RELOAD}
      `}
        elementLabel="Enable Physics Engine"
      /> */}
    </MenuGeneralSubSection>
  </>);
};

export default GraphicsWorldContent;