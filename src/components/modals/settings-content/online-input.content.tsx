import { Box, Button, ButtonGroup, Center, Drawer, <PERSON>er<PERSON><PERSON>, Drawer<PERSON>lose<PERSON>utton, <PERSON>er<PERSON><PERSON>nt, <PERSON>er<PERSON>eader, DrawerOverlay, HStack, Icon, Input, Kbd, Modal, ModalBody, ModalCloseButton, ModalContent, ModalFooter, ModalHeader, ModalOverlay, Radio, RadioGroup, SimpleGrid, Table, Tbody, Td, Th, Thead, Tr, VStack, createDisclosure } from "@hope-ui/solid";
import { cloneDeep, isEqual, isMatch, remove, sortBy, uniq } from "lodash-es";
import { FaSolidFolderPlus, FaSolidKeyboard, FaSolidTerminal } from "solid-icons/fa";
import { Component, For, Show, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import { CustomKeyboardKeyMap, KeyboardLayout } from "~/types/app.types";
import { Keybind, KeyboardShortcuts } from "~/types/settings.types";
import SwalPR from "~/util/sweetalert";
// import { Frequency, Midi } from "tone";
// import * as Tone from "tone";
import keyMapCSS from "~/sass/keyboard.mapping.module.scss";
import style from "~/sass/settings.module.sass";
import I18nService from "~/services/i18n.service";
import KeyboardService from "~/services/keyboard.service";
import ResourceService from "~/services/resource.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import WebsocketService from "~/services/websocket.service";
import { COMMON, COMMON_KEYS_MAP } from "~/util/const.common";
import { MPP_KEY_MAP, VP_KEY_MAP } from "~/util/v2.keyboard-helper";
import { MenuContentDivider, MenuContentSwitch, MenuGeneralSubSection, MenuSectionWithSearchFilter } from "./common.content";
import notificationService from "~/services/notification.service";

const disposeTone = () => {
  // Tone.getContext().dispose();
  // Tone.Transport.dispose();
};
disposeTone();

const Inputs: KeyboardLayout[] = ["VP", "MPP", "CUSTOM"];
const splitChar = "+";
const sanitizeBinding = (key?: string) => key?.replace(splitChar, ` ${splitChar} `);
const [editedKeybinds, setEditedKeybinds] = createSignal<Keybind[]>([], { equals: isEqual });

const KeybindShortcut: Component<{ bind: Keybind, onClick?: () => void; }> = (props) => {
  const [edited, setEdited] = createSignal(false);
  const [originalBinding, setOriginalBinding] = createSignal<string>();
  const [binding, setBinding] = createSignal<string>();

  createEffect(() => {
    let editedKeybind = editedKeybinds().find(x => x.command == props.bind.command);
    let inputBinding = sanitizeBinding(props.bind.binding);
    let editedKeyBinding = sanitizeBinding(editedKeybind?.binding);
    setOriginalBinding(inputBinding);
    setBinding(editedKeybind ? editedKeyBinding : inputBinding);
    setEdited(editedKeybind != null && (inputBinding != editedKeyBinding));
  });

  return (<>
    <Kbd
      __tooltip_title={
        `Click to edit ${(edited() && originalBinding())
          ? `(previously: ${originalBinding()?.toUpperCase()})` : ""}`
      }
      __tooltip_open_delay={150}
      onclick={() => { if (props.onClick) props.onClick(); }}
      className={"unselectable"}
      cursor={"pointer"}
      position="absolute"
      backgroundColor={edited() ? "$accent1" : undefined}
      top="20%"
      _hover={{
        "backgroundColor": "$primaryDark1 !important",
        "transform": "translateY(2px)"
      }}
      padding={8}>{binding()?.toUpperCase() || "UNASSIGNED"}</Kbd>
  </>);
};

type KeybindEditModalProps = {
  onChange: (bind: Keybind) => void;
  onClose: () => void;
  initialBind: Keybind;
  header?: string;
  ignoreKeyCombinations?: boolean;
  onCommandExists: (initial: Keybind, bind?: string) => Keybind | undefined;
};

const KeybindEditModal: Component<KeybindEditModalProps> = (props) => {
  const { isOpen, onOpen, onClose } = createDisclosure();
  const [bind, setBind] = createSignal<string | undefined>(props.initialBind.binding);
  const [existingCommand, setExistingCommand] = createSignal<string>();
  const [isEdited, setIsEdited] = createSignal<boolean>(false);
  let inputArray: string[] = [];

  onMount(() => {
    onOpen();
  });

  createEffect(() => {
    let editedKeybind = editedKeybinds().find(x => x.command == props.initialBind.command);
    if (editedKeybind) {
      setIsEdited(true);
      setBind(editedKeybind.binding);
    } else {
      setIsEdited(false);
    }
  });

  function onMountElement(element: HTMLDivElement) {
    let handleOnKeyDown = (event: KeyboardEvent) => {
      if (event.repeat) return;
      event.preventDefault();

      let key = event.key.toLowerCase();

      if (props.ignoreKeyCombinations) {
        inputArray = [];
        setBind(key);
      } else {
        if (key == "+") key = "plus";
        if (key == " ") key = "space";
        if (event.shiftKey || event.altKey || event.ctrlKey || event.metaKey) {
          if (inputArray.length > 4) return;
          if (!inputArray.includes(key)) inputArray.push(key);
          setBind(inputArray.join(` ${splitChar} `));
        } else {
          inputArray = [];
          setBind(key);
        }
      }

      setExistingCommand(props.onCommandExists(props.initialBind, bind())?.command);
    };
    document.addEventListener("keydown", handleOnKeyDown);
    onCleanup(() => document.removeEventListener("keydown", handleOnKeyDown));
  }

  function closeModal() {
    onClose();
    props.onClose();
  }

  return (<>
    <Modal size={"lg"} centered opened={isOpen()} onClose={closeModal} scrollBehavior={"inside"} >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>{props.header || "Press desired key combination"}</ModalHeader>
        <ModalBody ref={onMountElement}>
          <Center w="100%" textAlign={"center"} padding={25}>
            <KeyboardKeyDisplay kbdKey={bind() || "unassigned"} />
          </Center>
        </ModalBody>
        <ModalFooter>
          <Center w="100%" textAlign={"center"}>
            {existingCommand() &&
              <Box>
                <i style='color:yellow;padding-right:3px'>Warning!</i><br />
                There's already a command that shares the same binding: <b>{existingCommand()}</b>
              </Box>}
          </Center>
          <HStack spacing={"$2"}>
            <Button onclick={closeModal} className={style.cancelButton}>Cancel</Button>
            <Button
              disabled={bind() == null}
              onclick={() => {
                setBind(undefined);
                inputArray = [];
              }}>Clear</Button>
            <Button
              onclick={() => {
                props.onChange({ ...props.initialBind, binding: bind() });
                closeModal();
              }}
              disabled={!isEdited() && props.initialBind.binding == bind()}
              variant={"outline"}>Save</Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

const KeyboardKeyDisplay: Component<{ kbdKey: string; }> = (props) => {
  return (<>
    <For each={props.kbdKey.split(splitChar)}>
      {(key, index) => {
        return (
          <>
            {index() > 0 && <Box marginLeft={5} marginRight={5}>+</Box>}
            <Kbd fontSize={24} padding={10}>{key.toUpperCase()}</Kbd>
          </>
        );
      }}
    </For>
  </>);
};

type CustomizeKeyProps = {
  keyMaps: CustomKeyboardKeyMap[];
  input: string;
  onKeyClick: (input: string) => void;
};

const keysDict = Object.fromEntries(
  Object.entries(COMMON_KEYS_MAP).map(([k, v]) => [k.toLowerCase(), v])
);

const getKeyCode = (input?: string) => {
  return (keysDict as any)[(input?.toLowerCase() || '')] as number | undefined;
};

const getName = (elem: string) => {
  let input = elem.toLowerCase().trim();
  if (input.includes("disabled")) input = input.replaceAll("disabled", "");
  if (input == "lshift" || input == "rshift") return "shift";
  if (input == "lctrl" || input == "rctrl") return "ctrl";
  return input;
};

const CustomizeKey: Component<CustomizeKeyProps> = (props) => {
  const keyCode = getKeyCode(getName(props.input));
  const isKeyAssigned = () => {
    return getKeyMapFromElem() != null;
  };

  const getClasses = (input: string) => {
    let className = keyMapCSS["key"];
    let elem = input.trim().toLowerCase();
    if (elem.includes("disabled")) className += " " + keyMapCSS["disabled"];
    if (elem.includes("backspace")) className += " " + keyMapCSS["backspace"];
    if (elem.includes("tab")) className += " " + keyMapCSS["tab"];
    if (elem.includes("\\")) className += " " + keyMapCSS["backslash"];
    if (elem.includes("capslock")) className += " " + keyMapCSS["capslock"];
    if (elem.includes("enter")) className += " " + keyMapCSS["return"];
    if (elem.includes("lshift")) className += " " + keyMapCSS["leftshift"];
    if (elem.includes("rshift")) className += " " + keyMapCSS["rightshift"];
    if (elem.includes("lctrl")) className += " " + keyMapCSS["leftctrl"];
    if (elem.includes("rctrl")) className += " " + keyMapCSS["rightctrl"];
    if (elem.includes("command")) className += " " + keyMapCSS["command"];
    if (elem.includes("space") && !elem.includes("backspace")) className += " " + keyMapCSS["space"];
    if (elem.includes("alt")) className += " " + keyMapCSS["alt"];

    if (!isKeyAssigned() && !className?.includes(keyMapCSS?.["disabled"])) {
      className += " " + keyMapCSS["unassigned"];
    }

    return className;
  };

  const getKeyMapFromElem = () => {
    return props.keyMaps.find(x => x.keyCode == keyCode);
  };

  const getNoteFromElem = () => {
    let note = getKeyMapFromElem()?.note;
    // return note != null ? Frequency(note, "midi").toNote() : undefined;
  };

  const getKeyFromElem = () => {
    return getKeyMapFromElem()?.note;
  };

  return (<>
    <Box
      // disabled={!Boolean(keyCode)}
      __tooltip_title={`Keyboard KeyCode: ${keyCode}`}
      __tooltip_open_delay={250}
      // @ts-ignore
      data-assigned-note={getNoteFromElem()}
      // @ts-ignore
      data-assigned-key={getKeyFromElem()}
      // @ts-ignore
      data-assigned-key-code={keyCode}
      onClick={() => props.onKeyClick(props.input)}
      className={getClasses(props.input)}>
      {getName(props.input)}
    </Box>
  </>);
};

const CustomizeKeysModal: Component<{ onClose: () => void; }> = (props) => {
  type PianoKey = {
    key: number;
    note: string;
    isWhiteKey: boolean;
  };

  type CustomKeyboardKeyMapTarget = {
    key: string;
    note?: number;
  };

  const appSettingsService = useService(AppSettingsService);
  const keyboardService = useService(KeyboardService);

  const ROW_ONE = ["`", "1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "-", "=", "backspace"];
  const ROW_TWO = ["Tab", "q", "w", "e", "r", "t", "y", "u", "i", "o", "p", "[", "]", "\\"];
  const ROW_THREE = ["Capslock", "a", "s", "d", "f", "g", "h", "j", "k", "l", ";", "'", "enter"];
  const ROW_FOUR = ["lshift", "z", "x", "c", "v", "b", "n", "m", ",", ".", "/", "rshift"];
  const ROW_FIVE = ["lctrl", " disabled", "alt", "space", "alt", "fn", "rctrl"];
  const [showEditModal, setShowEditModal] = createSignal<CustomKeyboardKeyMapTarget>();
  const [highlightedCustomKey, setHighlightedCustomKey] = createSignal<string>();
  const [customizeKeyModalHeader, setCustomizeKeyModalHeader] = createSignal<string>();
  const [initialKeyMaps, setInitialKeyMaps] = createSignal<CustomKeyboardKeyMap[]>([]);
  const [keyMaps, setKeyMaps] = createSignal<CustomKeyboardKeyMap[]>([]);
  const [showOptions, setShowOptions] = createSignal(false);
  const [initialKeybindsBeforeTest, setInitialKeybindsBeforeTest] = createSignal<CustomKeyboardKeyMap[]>([], { equals: isEqual });
  const keys: PianoKey[] = [];
  const TOTAL_KEYS = 88;
  let keyboardBaseHTML!: HTMLDivElement;

  let handleOnKeyDown = (event: KeyboardEvent) => {
    if (event.repeat) return;
    if (!keyboardService().isInCustomizeLayoutKeysMode()) return;
    event.preventDefault();

    let keyCode = event.keyCode;
    let element = keyboardBaseHTML.querySelector<HTMLDivElement>(`[data-assigned-key-code="${keyCode}"]`);

    if (element) {
      element.classList.add("shake-square");
      element.style.background = "var(--hope-colors-accent1)";
      const onRemove = () => {
        element!.style.background = "";
        element?.classList.remove("shake-square");
        element?.removeEventListener("animationend", onRemove);
      };
      element.addEventListener("animationend", onRemove);
    }
  };

  onMount(() => {
    Array(TOTAL_KEYS).fill(0).forEach((_, idx) => {
      // TODO: Fix this
      // let midi = Midi(21 + idx);
      // keys.push({
      //   key: midi.toMidi(),
      //   note: midi.toNote(),
      //   isWhiteKey: !midi.toNote().includes("#"),
      // });
    });

    let current = appSettingsService().getSetting<CustomKeyboardKeyMap[]>("CUSTOM_KEYBOARD_LAYOUT_KEYBINDS");
    setInitialKeyMaps(current);
    setKeyMaps(current);

    document.addEventListener("keydown", handleOnKeyDown);
  });

  onCleanup(() => {
    document.removeEventListener("keydown", handleOnKeyDown);
    keyboardService().setIsInCustomizeLayoutKeysMode(false);
  });

  createEffect(() => {
    let current = showEditModal();
    if (!current) return;

    let note: string | undefined = undefined;
    let currentNoteOutput: string = "";

    //TODO: Fix this
    // if (current.note != null) note = Frequency(current.note, "midi").toNote();
    if (note) currentNoteOutput = ` (${note} - ${current.note})`;

    setCustomizeKeyModalHeader(`
      Keyboard Key:
        <b>${current?.key.toUpperCase()}${currentNoteOutput}</b>
        | ${highlightedCustomKey() || "None"}
    `);
  });

  const onKeyClick = (key: string) => {
    let note = keyMaps().find(x => x.keyCode == getKeyCode(key));
    setShowEditModal({ key, note: note?.note });
  };

  const canResetToDefault = () => !isEqual(keyMaps(), initialKeyMaps());

  const onResetToDefault = () => {
    SwalPR().fire({
      html: `Are you sure you want to reset the layout to what you had initially?`,
      showCancelButton: true,
      icon: "warning",
      cancelButtonText: "Nah, sorry...",
      confirmButtonText: "Yes!",
      showConfirmButton: true,
      allowEscapeKey: true,
    }).then((res) => {
      if (res.isConfirmed) {
        setKeyMaps(initialKeyMaps());
      }
    });
  };

  const onApplyChanges = (close = true) => {
    let sanitizedKeyMaps = uniq(keyMaps());
    appSettingsService().saveSetting("CUSTOM_KEYBOARD_LAYOUT_KEYBINDS", sanitizedKeyMaps);

    notificationService.show({
      type: "success",
      description: "Custom Keys Layout Saved!"
    });

    if (close) props.onClose();
  };

  const onClear = () => {
    SwalPR().fire({
      html: `Are you sure you want to clear the current mapping?`,
      showCancelButton: true,
      icon: "warning",
      cancelButtonText: "Nah, sorry...",
      confirmButtonText: "Yes!",
      showConfirmButton: true,
      allowEscapeKey: true,
    }).then((res) => {
      if (res.isConfirmed) {
        setKeyMaps([]);
        onApplyChanges(false);
      }
    });
  };

  const onHighlightCustomKey = (key: PianoKey) => {
    setHighlightedCustomKey(`Highlighted (Note: ${key.note} - Key: ${key.key})`);
  };

  const onUnhighlight = () => {
    setHighlightedCustomKey();
  };

  const onUnbindCustomKey = () => {
    let keyCode = getKeyCode(getName(showEditModal()!.key));
    let cleaned = cloneDeep(keyMaps());
    let r = remove(cleaned, v => v.keyCode == keyCode);
    setKeyMaps([...cleaned]);
    onCloseCustomizeKeysModal();
  };

  const onClickCustomKey = (key: PianoKey) => {
    let keyCode = getKeyCode(getName(showEditModal()!.key));

    if (keyCode != null) {
      let cleaned = cloneDeep(keyMaps());
      remove(cleaned, v => v.keyCode == keyCode);
      setKeyMaps([...cleaned, { keyCode: keyCode!, note: key.key }]);
    }
    onCloseCustomizeKeysModal();
  };

  const onCloseCustomizeKeysModal = () => {
    setShowEditModal();
    setHighlightedCustomKey();
  };

  const onSetOctave = (increase: boolean) => {
    setKeyMaps(v => v.map(x => ({
      ...x,
      note: x.note + (increase ? 12 : -12)
    })));

    notificationService.show({
      type: "success",
      description: `Mapped Keys Notes have been ${increase ? "increased" : "decreased"} by one octave!`,
      duration: 1500,
    });
  };

  const onImportLayout = (layout: KeyboardLayout) => {
    const onSuccess = () => {
      notificationService.show({
        type: "success",
        description: `Imported ${layout} layout.`,
        duration: 1500,
      });
    };

    const getMapped = (obj: any) => {
      return Object.values(keysDict).map(x => {
        let keyCode = x;
        let note = obj[keyCode] as number;
        return { keyCode, note };
      })
        .filter(x => Boolean(x.note));
    };

    switch (layout) {
      case "MPP": {
        let mapped = getMapped(MPP_KEY_MAP);
        setKeyMaps(mapped);
        if (mapped.length > 0) onSuccess();
        break;
      }
      case "VP": {
        let mapped = getMapped(VP_KEY_MAP);
        setKeyMaps(mapped);
        if (mapped.length > 0) onSuccess();
        break;
      }
    }
  };

  const onTestLayout = () => {
    let currentState = keyboardService().isInCustomizeLayoutKeysMode();
    if (currentState) {
      keyboardService().setCustomLayoutKeybinds(initialKeybindsBeforeTest());
    } else {
      setInitialKeybindsBeforeTest(keyboardService().customLayoutKeybinds());
      keyboardService().setCustomLayoutKeybinds(keyMaps());
    }
    keyboardService().setIsInCustomizeLayoutKeysMode(v => !v);
  };

  createEffect(() => {
    // Automatically update keybinds in kb service when in test layout mode
    if (keyboardService().isInCustomizeLayoutKeysMode()) {
      keyboardService().setCustomLayoutKeybinds(keyMaps());
    }
  });

  return (<>
    <Modal
      scrollBehavior={"inside"}
      size={"3xl"}
      closeOnOverlayClick={false}
      centered opened={true}
      onClose={props.onClose}>
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>Customize Layout Keys</ModalHeader>
        <ModalBody>
          <SimpleGrid columns={30} gap="$1" ref={keyboardBaseHTML} className={keyMapCSS["keyboard-base"]}>
            <For each={ROW_ONE}>
              {(elem) => <CustomizeKey keyMaps={keyMaps()} input={elem} onKeyClick={onKeyClick} />}
            </For>
            <For each={ROW_TWO}>
              {(elem) => <CustomizeKey keyMaps={keyMaps()} input={elem} onKeyClick={onKeyClick} />}
            </For>
            <For each={ROW_THREE}>
              {(elem) => <CustomizeKey keyMaps={keyMaps()} input={elem} onKeyClick={onKeyClick} />}
            </For>
            <For each={ROW_FOUR}>
              {(elem) => <CustomizeKey keyMaps={keyMaps()} input={elem} onKeyClick={onKeyClick} />}
            </For>
            <For each={ROW_FIVE}>
              {(elem) => <CustomizeKey keyMaps={keyMaps()} input={elem} onKeyClick={onKeyClick} />}
            </For>
          </SimpleGrid>
        </ModalBody>
        <ModalFooter>
          <ButtonGroup size="md" w="100%" variant={"outline"}>
            <HStack w="100%" justifyContent={"space-between"}>
              <HStack spacing={"$2"}>
                <Button
                  disabled={!canResetToDefault()}
                  onclick={onResetToDefault}
                  className={style.resetToDefaultButton}>Reset</Button>
                <Button
                  disabled={keyMaps().length == 0}
                  onclick={onClear}
                  className={style.resetToDefaultButton}>Clear</Button>
                <Button
                  onclick={() => setShowOptions(v => !v)}
                  className={style.resetToDefaultButton}>Options</Button>
                <Button
                  onclick={onTestLayout}
                  className={
                    [
                      style.testLayoutButton,
                      keyboardService().isInCustomizeLayoutKeysMode() && "blink"
                    ].join(" ")
                  }>
                  {keyboardService().isInCustomizeLayoutKeysMode() ? "Stop Testing" : "Test"}
                </Button>
              </HStack>
              <HStack spacing={"$2"}>
                <Button
                  onclick={props.onClose}
                  className={style.cancelButton}>Cancel</Button>
                <Button
                  disabled={!canResetToDefault()}
                  onclick={() => onApplyChanges()}
                  variant={"outline"}>Save and Exit</Button>
              </HStack>
            </HStack>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>

    {/* Customize Key From Piano Modal */}
    {showEditModal() &&
      <Modal
        scrollBehavior={"inside"}
        size={"4xl"}
        centered opened={true}
        closeOnOverlayClick={false}
        onClose={onCloseCustomizeKeysModal}
      >
        <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
        <ModalContent>
          <ModalCloseButton />
          <ModalHeader innerHTML={customizeKeyModalHeader()}></ModalHeader>
          <ModalBody padding={5}>
            <HStack spacing={"$0_5"} position={"relative"} w="100%" onMouseLeave={onUnhighlight}>
              <For each={keys.filter(x => x.isWhiteKey)}>
                {(key) => {
                  let hasBlackKey = keys.find(x => x.key == key.key + 1 && !x.isWhiteKey);
                  let isAssigned = (key: number) => key == showEditModal()?.note;

                  return (<>
                    <Box w="100%" h="100%" position={"relative"}>
                      <Box
                        onMouseOver={() => onHighlightCustomKey(key)}
                        onClick={() => onClickCustomKey(key)}
                        className={[keyMapCSS["piano-key"], isAssigned(key.key) && keyMapCSS["piano-key-assigned"]].join(" ")}
                      />
                      {hasBlackKey &&
                        <Box
                          onClick={() => onClickCustomKey(hasBlackKey!)}
                          onMouseOver={() => onHighlightCustomKey(hasBlackKey!)}
                          className={[keyMapCSS["piano-key-black"], isAssigned(hasBlackKey.key!) && keyMapCSS["piano-key-assigned"]].join(" ")}
                        />}
                    </Box>
                  </>);
                }}
              </For>
            </HStack>
          </ModalBody>
          <ModalFooter>
            {(showEditModal()?.note != null) && <Button
              variant={"outline"}
              onclick={onUnbindCustomKey}
              className={style.resetToDefaultButton}>Remove Binding</Button>
            }
          </ModalFooter>
        </ModalContent>
      </Modal>
    }

    {/* Help Options Panel */}
    {showOptions() &&
      <Drawer
        opened={true}
        placement="right"
        onClose={() => setShowOptions(false)}
        size={"sm"}
      >
        <DrawerOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
        <DrawerContent >
          <DrawerCloseButton />
          <DrawerHeader>Customize Keys Options</DrawerHeader>
          <DrawerBody>
            <VStack w="100%">
              {/* Layout Import */}
              <VStack spacing={"$2"} w="100%">
                <Box as="h1" mb="$2">Layout Import</Box>
                <ButtonGroup variant={"outline"} w="100%">
                  <VStack spacing={"$2"} w="100%">
                    <Button w="100%" onClick={() => onImportLayout("MPP")}>Import Layout from&nbsp;<b> MPP</b></Button>
                    <Button w="100%" onClick={() => onImportLayout("VP")}>Import Layout from&nbsp;<b> VP</b></Button>
                  </VStack>
                </ButtonGroup>
              </VStack>
              <MenuContentDivider marginTop={"$8"} />
              {/* Octave */}
              <VStack spacing={"$2"} w="100%">
                <Box as="h1" mb="$2">Set Octave</Box>
                <ButtonGroup variant={"outline"} w="100%">
                  <VStack spacing={"$2"} w="100%">
                    <Button w="100%" onClick={() => onSetOctave(true)}>Increase +</Button>
                    <Button w="100%" onClick={() => onSetOctave(false)}>Decrease -</Button>
                  </VStack>
                </ButtonGroup>
              </VStack>
            </VStack>
          </DrawerBody>
        </DrawerContent>
      </Drawer>
    }
  </>);
};

const KeybindModal: Component<{ onClose: () => void; }> = (props) => {
  const { isOpen, onOpen, onClose } = createDisclosure();
  const keyboardService = useService(KeyboardService);
  const sfxService = useService(SoundEffectsService);
  const appSettingsService = useService(AppSettingsService);

  const [initialKeybinds, setInitialKeybinds] = createSignal<Keybind[]>([], { equals: isEqual });
  const [activeKeybinds, setActiveKeybinds] = createSignal<Keybind[]>([], { equals: isEqual });
  const [filterInput, setFilterInput] = createSignal<string>("");
  const [showEditModal, setShowEditModal] = createSignal<Keybind>();
  const [canApplyChanges, setCanApplyChanges] = createSignal(false);
  const [canResetToDefault, setCanResetToDefault] = createSignal(false);

  onMount(() => {
    onOpen();
    let binds = keyboardService().keybinds();
    setActiveKeybinds(binds);
    setInitialKeybinds(binds);
    keyboardService().setEditingKeybinds(true);
  });

  function closeModal() {
    onClose();
    props.onClose();
    keyboardService().setEditingKeybinds(false);
    setEditedKeybinds([]);
  }

  const mapKeyBind = (keybind: Keybind) => ({ command: keybind.command, binding: keybind.binding });

  createEffect(() => {
    let initial = sortBy(initialKeybinds().map(mapKeyBind), "command");
    let edited = sortBy(editedKeybinds().map(mapKeyBind), "command");
    let defaultBinds = sortBy(keyboardService().defaultKeybinds().map(mapKeyBind), "command");
    setCanApplyChanges((initial.length > 0 && edited.length > 0 && !isMatch(initial, edited)));
    setCanResetToDefault(defaultBinds.some(x =>
      edited.find(e => e.command == x.command && e.binding != x.binding) ||
      initial.find(e => e.command == x.command && e.binding != x.binding)
    ));
  });

  createEffect(() => {
    let filtered =
      keyboardService()
        .keybinds()
        .filter(x =>
          x.command.toLowerCase().includes(filterInput().toLowerCase())
        );
    setActiveKeybinds(filterInput().length > 0 ? filtered : keyboardService().keybinds());
  });

  function onReset() {
    setEditedKeybinds(keyboardService().defaultKeybinds());
  }

  function onApplyChanges() {
    if (editedKeybinds().length >= 0) {
      editedKeybinds().forEach(newKeyBind => {
        keyboardService().setKeybinds(v => v.map(x => {
          if (x.command == newKeyBind.command) return newKeyBind;
          return x;
        }));
      });

      appSettingsService().saveSetting("KEYBINDS", keyboardService().keybinds());
      sfxService().playNotificationSuccessSFX();
      notificationService.show({
        type: "success",
        description: "Changes saved!"
      });
    }
    closeModal();
  }

  return (<>
    <Modal size={"2xl"} centered opened={isOpen()} onClose={closeModal} scrollBehavior={"inside"} >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidFolderPlus} marginRight={5} marginBottom={5} />Keyboard Shortcut Binding</ModalHeader>

        {/* Body */}
        <ModalBody>
          <Input
            variant={"filled"}
            border={"1px solid gray"}
            marginBottom={20}
            placeholder="Search for commands..."
            oninput={(event: InputEvent) => {
              let value = (event.target as any).value as string;
              setFilterInput(value);
            }}
          >
          </Input>
          <Table highlightOnHover>
            <Thead>
              <Tr>
                <Th fontWeight={"bolder"}><Icon as={FaSolidTerminal} marginRight={5} marginBottom={5} />Command</Th>
                <Th fontWeight={"bolder"}><Icon as={FaSolidKeyboard} marginRight={5} marginBottom={5} />Key Bind</Th>
              </Tr>
            </Thead>
            <Tbody>
              <Show when={activeKeybinds().length > 0} fallback={<Box marginLeft={20}>No command(s) found...</Box>}>
                <For each={activeKeybinds()}>
                  {(keybind) => {
                    let _keybind: Keybind = { ...keybind, binding: sanitizeBinding(keybind.binding) };
                    return (<>
                      <Tr>
                        <Td>{KeyboardShortcuts.toString(keybind.command).split(/(?=[A-Z])/).join(" ")}</Td>
                        <Td position={"relative"}>{
                          <KeybindShortcut
                            onClick={() => setShowEditModal(_keybind)}
                            bind={_keybind}
                          />}
                        </Td>
                      </Tr>
                    </>);
                  }}
                </For>
              </Show>
            </Tbody>
          </Table>
        </ModalBody>

        {/* Footer */}
        <ModalFooter>
          <ButtonGroup size="md" w="100%">
            <HStack w="100%" justifyContent={"space-between"}>
              <Button
                disabled={!canResetToDefault()}
                onclick={onReset} className={style.resetToDefaultButton}>Reset to Default</Button>
              <HStack spacing={"$2"}>
                <Button onclick={closeModal} className={style.cancelButton}>Cancel</Button>
                <Button
                  disabled={!canApplyChanges()}
                  onclick={onApplyChanges} variant={"outline"}>Apply Changes</Button>
              </HStack>
            </HStack>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>

    {showEditModal() &&
      <KeybindEditModal
        initialBind={showEditModal() as Keybind}
        onCommandExists={((initialBind, bind) => {
          return keyboardService().keybinds().find(x =>
            x.binding?.toLowerCase() == bind &&
            bind != initialBind.binding?.toLowerCase()
          );
        })}
        onChange={(newKeyBind) => {
          if (editedKeybinds().find(x => (x.command == newKeyBind.command)) == null) {
            return setEditedKeybinds(v => [...v, newKeyBind]);
          }

          setEditedKeybinds(v => v.map(x => {
            if (x.command == newKeyBind.command) return newKeyBind;
            return x;
          }));
        }}
        onClose={() => setShowEditModal(undefined)} />
    }
  </>);
};

const InputContent = () => {
  const appSettingsService = useService(AppSettingsService);
  const keyboardService = useService(KeyboardService);
  const i18nService = useService(I18nService);
  const resourceService = useService(ResourceService);
  const [showKeybindModal, setShowKeybindModal] = createSignal(false);
  const [showCustomKeyMappingModal, setShowCustomKeyMappingModal] = createSignal(false);
  const [selectedValues, setSelectedValues] = createSignal(
    keyboardService().keyboardLayout()
  );
  const [imagePath, setImagePath] = createSignal<string | undefined>("");
  const [isUsingQwertyMod, setIsUsingQwertyMod] = createSignal(false);

  const category = "online";
  const menu = "input";

  onMount(() => {
    resourceService().getAssetImage(`/images/midi_to_qwerty_piano.png`).then(setImagePath);
  });

  const onChange = (selected: KeyboardLayout) => {
    setSelectedValues(selected);
    keyboardService().setKeyboardLayout(selected);
    appSettingsService().saveSetting("KEYBOARD_LAYOUT", selected);
    return true;
  };

  createEffect(() => {
    setIsUsingQwertyMod(appSettingsService().getSetting("INPUT_MIDI_TO_QWERTY_MOD", false));
    setSelectedValues(keyboardService().keyboardLayout());
  });

  return (<>
    <MenuSectionWithSearchFilter>
      <MenuGeneralSubSection category={category} menu={menu} label="lbl_pianoKeylayout">
        <RadioGroup
          w={"100%"}
          value={selectedValues()}
          alignItems={"flex-start"}
        >
          <VStack spacing="$4" alignItems={"flex-start"} marginLeft={5}>
            <For each={Inputs}>
              {(input) => {
                return (<>
                  <Radio
                    class={style.language_radio}
                    onClick={() => onChange(input)}
                    value={input}>{input}</Radio>
                </>);
              }}
            </For>
          </VStack>
        </RadioGroup>
      </MenuGeneralSubSection>

      <MenuGeneralSubSection category={category} menu={menu} label="lbl_shortcutBinds" tooltip="lbl_shortcutBinds">
        <Button
          onclick={() => setShowKeybindModal(true)}
          variant={"outline"}>{i18nService().t_roomPageSettingSubMenuElement(category, menu, "btn_openKeybinder")}
        </Button>
      </MenuGeneralSubSection>

      <MenuGeneralSubSection label="Advanced" >
        <Button
          disabled={selectedValues() != "CUSTOM"}
          onclick={() => setShowCustomKeyMappingModal(true)}
          variant={"outline"}>{"Customize Layout Keys"}
        </Button>
        <MenuContentSwitch
          skipTranslate settingsKey="INPUT_CTRL_KEY_LOWERS_OCTAVE"
          tooltip={`
          Pressing the Ctrl key will lower the octave of the notes played.
          <br /><br />
          <i style="color:gray">Note: This feature is disabled when using the midi to qwerty converter layout for VP.</i>
        `}
          elementLabel="Ctrl Key Lowers Octave"
        />
        <MenuContentSwitch
          disabled={selectedValues() != "VP"}
          skipTranslate settingsKey="INPUT_MIDI_TO_QWERTY_MOD"
          tooltip={`
          Use a similar extended layout compared the midi to VP/qwerty converter tool.
          <img src="${imagePath()}">
          <br />
          <div style="color:gray">
            Link to: <a href="https://arijanj.github.io/midi-converter/" target="_blank">MIDI to Virtual Piano Converter</a>
          </div>
        `}
          elementLabel="MIDI to VP/QWERTY Layout"
        />
        <MenuContentSwitch
          skipTranslate settingsKey="INPUT_MIDI_TO_QWERTY_MOD_USE_CAPSLOCK"
          disabled={selectedValues() != "VP" || isUsingQwertyMod() == false}
          tooltip={`
          This is to provide an alternative key because certain shortcuts in the browser may conflict with the default ctrl key binding.
          For example, ctrl + w closes the current tab in most browsers.
        `}
          elementLabel={`Use capslock instead of ctrl for MIDI to QWERTY Mod`}
        />
        <MenuContentSwitch
          skipTranslate settingsKey="KEYBOARD_SHIFT_KEY_AUTO_NOTE_OFF"
          tooltip={`TODO: Details`}
          elementLabel={`Shift Key Note Off`}
        />
      </MenuGeneralSubSection>
    </MenuSectionWithSearchFilter>

    {showKeybindModal() && <KeybindModal onClose={() => setShowKeybindModal(false)} />}
    {showCustomKeyMappingModal() && <CustomizeKeysModal onClose={() => setShowCustomKeyMappingModal(false)} />}

  </>);
};

export default InputContent;