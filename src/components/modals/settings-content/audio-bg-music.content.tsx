import { Component } from "solid-js";
import { MenuContentSwitch, MenuGeneralSubSection } from "./common.content";

const AudioSoundBGMusicContent: Component = () => {
  const category = "audio";
  const menu = "bgmusic";

  return (<>
    <MenuGeneralSubSection label="General">
      <MenuContentSwitch
        skipTranslate
        settingsKey="AUDIO_BG_MUSIC_ENABLE"
        category={category} menu={menu}
        tooltip="If enabled, background piano music will play during certain screens."
        elementLabel="Enable Background Music"
      />
    </MenuGeneralSubSection>
  </>);
};

export default AudioSoundBGMusicContent;