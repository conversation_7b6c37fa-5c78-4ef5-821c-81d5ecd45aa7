import { Box, Button, createDisclosure, Icon, Modal, ModalBody, ModalClose<PERSON>utton, <PERSON>dal<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON>, ModalOverlay } from "@hope-ui/solid";
import { FaSolidFileLines } from "solid-icons/fa";
import { Component, createEffect, createSignal, onCleanup } from "solid-js";
import { useService } from "solid-services";
import DisplaysService from "~/services/displays.service";
import { COMMON } from "~/util/const.common";
import { CenteredLoadingIcon } from "../common";

const Docs: Component = () => {
  const DISPLAY_KEY = "DOCS_MODAL";
  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);
  const [frameSrc, setFrameSrc] = createSignal<string>(COMMON.DOCS_HOST);
  const [isLoading, setLoading] = createSignal(true);
  const [changeLogLatestPostMessageSent, setChangeLogLatestPostMessageSent] = createSignal(false);

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(async () => {
    if (displayService().getDisplay(DISPLAY_KEY)) {
      onOpen();
    } else { onClose(); }
  });

  onCleanup(() => {
    displayService().setDocsURLParameters();
    displayService().setDocsModalTitle();
    setChangeLogLatestPostMessageSent(false);
  });

  return (<>
    <Modal
      centered opened={isOpen()} onClose={closeModal}
      scrollBehavior={"outside"}
      closeOnOverlayClick={false}
      closeOnEsc={false}
      size="8xl"
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent h="80vh">
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidFileLines} /> {displayService().docsModalTitle() || "Docs"}
        </ModalHeader>
        <ModalBody w="100%" h="100%" padding={0} paddingEnd={0}>
          {isLoading() && <CenteredLoadingIcon />}

          {frameSrc() &&
            <>
              <Box
                //@ts-ignore
                credentialless
                as="iframe" w="100%" h="100%"
                src={frameSrc()}
                title="PianoRhythm Docs"
                onload={() => {
                  setLoading(false);
                  if (changeLogLatestPostMessageSent()) return;
                  let params = displayService().docsURLParameters();
                  if (params == "changelog-latest") {
                    setFrameSrc(`${COMMON.DOCS_HOST}/changelog/?showLatest`);
                    setChangeLogLatestPostMessageSent(true);
                  }
                }}
              />
            </>
          }
        </ModalBody>
        <ModalFooter h={60}>
          <Button
            size={"sm"}
            marginBottom={-5}
            variant={"outline"}
            onClick={onClose}
          >Close</Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default Docs;