import { Box, Drawer, DrawerBody, Drawer<PERSON><PERSON><PERSON><PERSON>on, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DrawerOverlay, createDisclosure } from "@hope-ui/solid";
import { Component, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import DisplaysService from "~/services/displays.service";
import { COMMON } from "~/util/const.common";
import { CenteredLoadingIcon } from "../common";

const SidebarDocsPanel: Component = () => {
  const DISPLAY_KEY = "SIDEBAR_HELP_DOCS";
  const displayService = useService(DisplaysService);

  const { isOpen, onOpen, onClose } = createDisclosure();
  const [frameSrc, setFrameSrc] = createSignal<string>(COMMON.DOCS_HOST);
  const [isLoading, setLoading] = createSignal(true);

  onMount(() => {
    onOpen();
  });

  function closeModal() {
    onClose();
    displayService().clearSidebarDocsPath();
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  return (<>
    <Drawer
      closeOnOverlayClick={false}
      opened={isOpen()}
      placement="right"
      onClose={closeModal}
      size={"lg"}
      fullHeight={true}
    >
      <DrawerOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <DrawerContent >
        <DrawerCloseButton />
        <DrawerHeader>PianoRhythm Docs</DrawerHeader>

        <DrawerBody padding={0} overflow={"hidden"}>
          {isLoading() && <CenteredLoadingIcon />}
          <Box
            //@ts-ignore
            credentialless
            as="iframe" w="100%" h="100%"
            src={`${frameSrc()}${displayService().sidebarDocsPath() || ""}`}
            title="PianoRhythm Docs"
            onload={() => { setLoading(false); }}
          />
        </DrawerBody>

        {/* <DrawerFooter>
          <Button variant="outline" onClick={closeModal}>Close</Button>
        </DrawerFooter> */}
      </DrawerContent>
    </Drawer>
  </>);
};

export default SidebarDocsPanel;