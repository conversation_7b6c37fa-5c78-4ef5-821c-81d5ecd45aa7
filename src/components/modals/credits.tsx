import { Button, Center, Icon, Modal, Modal<PERSON>ody, ModalClose<PERSON>utton, <PERSON>dal<PERSON>nt, <PERSON><PERSON><PERSON><PERSON>er, <PERSON>dal<PERSON>eader, ModalOverlay } from "@hope-ui/solid";
import { FaSolidNoteSticky } from "solid-icons/fa";
import { Component, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import { buttonConfirmSFX } from "~/directives/buttonsfx.directive";
import { getAssetAsText } from "~/server/general.api";
import DisplaysService from "~/services/displays.service";
import { SolidMarkDownText } from "../solid-markdown-text";

const CreditsModal: Component = () => {
  const DISPLAY_KEY = "CREDITS";
  const displayService = useService(DisplaysService);
  const [changelog, setChangeLog] = createSignal<string>();

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  onMount(async () => {
    try {
      let data = await getAssetAsText("/other/credits.md");
      setChangeLog(data);
    } catch {
      setChangeLog("Failed to get credits...");
    }
  });

  return (<>
    <Modal
      centered opened={true} onClose={closeModal}
      scrollBehavior={"inside"}
      size="3xl"
      closeOnOverlayClick={false}
      closeOnEsc={false}
    >
      <ModalOverlay zIndex={"calc(var(--hope-zIndices-overlay) + 100)"} />
      <ModalContent>
        <ModalCloseButton />
        <ModalHeader>
          <Icon as={FaSolidNoteSticky} /> Credits
        </ModalHeader>
        <ModalBody
          paddingBottom={20}
          paddingLeft={30}
          paddingRight={10}
        >
          <SolidMarkDownText
            text={changelog() || "Loading..."}
            allowImages
          />
        </ModalBody>
        <ModalFooter>
          <Center w="100%">
            <Button
              ref={(el: HTMLElement) => buttonConfirmSFX(el)}
              onMouseDown={closeModal}
              size="md" variant="outline"
            >OKAY</Button>
          </Center>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
};

export default CreditsModal;