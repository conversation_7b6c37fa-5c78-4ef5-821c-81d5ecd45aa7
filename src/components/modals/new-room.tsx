import { Box, Button, ButtonGroup, createDisclosure, Heading, HStack, Icon, Input, InputGroup, Modal, ModalBody, ModalCloseButton, ModalContent, <PERSON>dal<PERSON>ooter, ModalHeader, ModalOverlay, Switch, Textarea, VStack } from "@hope-ui/solid";
import { raceTimeout, until } from "@solid-primitives/promise";
import { getCountryCode } from "countries-list";
import { cloneDeep, defaultTo, isArray, isEmpty, isEqual, omit, pick } from "lodash-es";
import * as noUiSlider from 'nouislider';
import { FaSolidFolderPlus } from "solid-icons/fa";
import { Component, createEffect, createMemo, createSignal, JSX, onCleanup, onMount } from "solid-js";
import { createStore, SetStoreFunction, unwrap } from "solid-js/store";
import { useService } from "solid-services";
import { match } from "ts-pattern";
import { z } from "zod";
import inputSFX from "~/directives/input.directive";
import { AppStateEffects, AppStateEffects_Action } from "~/proto/pianorhythm-effects";
import { RoomHostDetails, RoomStageDetails, RoomStages, roomStagesToJSON, RoomStatus, roomStatusToJSON, RoomType, roomTypeFromJSON, roomTypeToJSON } from "~/proto/room-renditions";
import { CreateRoomParam, ServerCommandDU, ServerCommandDU_CommandType } from "~/proto/server-message";
import AppService from "~/services/app.service";
import DisplaysService from "~/services/displays.service";
import EmojifyService from "~/services/emojify.service";
import I18nService from "~/services/i18n.service";
import MonitorTrackingService, { TRACKING_EVENT_IDS } from "~/services/monitor-tracking.service";
import notificationService from "~/services/notification.service";
import { CountriesOptions, DEFAULT_CODE, DEFAULT_CONTINENT, DEFAULT_COUNTRY, SelfHostingService } from "~/services/selfhosting.service";
import AppSettingsService from "~/services/settings-storage.service";
import SoundEffectsService from "~/services/sound-effects.service";
import WebsocketService from "~/services/websocket.service";
import { AppSoundFx } from "~/types/app.types";
import { DEFAULT_CREATE_ROOM_PARAM, ROOM_CONST, RoomModes, RoomStagesNS, RoomTypeHelper } from "~/types/room.types";
import { COMMON } from "~/util/const.common";
import { generateUUID } from "~/util/helpers";
import { blurActiveElement } from "~/util/helpers.dom";
import { logError } from "~/util/logger";
import { MembersOnlyFeature } from "../common";
import { SettingsMenu, SettingsSubTextGroups } from "../SettingsMenu";
import ToolTipHelp from "../tooltip-help";
import { MenuContentDivider, MenuContentSelect } from "./settings-content/common.content";
import { getRoomSettings } from "~/server/general.api";

const HostDetailsSchema = z.object({
  continentCode: z.string().max(10),
  countryCode: z.string().max(10).optional(),
});

const RoomSettingsObjectSchema = z.object({
  WelcomeMessage: z.string().max(ROOM_CONST.MAX_WELCOME_MESSAGE_LENGTH).optional(),
  RoomName: z.string().max(ROOM_CONST.MAX_ROOMNAME_LENGTH),
  RoomType: z.number(),
  StageDetailsPROTO: z.instanceof(Uint8Array).nullable().optional(),
  RoomStatus: z.number(),
  MaxPlayers: z.number().min(1).max(ROOM_CONST.MAX_TOTAL_PLAYERS),
  OnlyOwnerCanPlay: z.boolean(),
  OnlyOwnerCanChat: z.boolean(),
  AllowBots: z.boolean(),
  AllowGuests: z.boolean(),
  AllowBlackMidi: z.boolean(),
  FilterProfanity: z.boolean(),
  Password: z.string().max(ROOM_CONST.MAX_PASSWORD_LENGTH).optional(),
}).omit({ StageDetailsPROTO: true });

const NEW_ROOM_DISPLAY_KEY = "NEW_ROOM_MODAL";
const UPDATE_ROOM_DISPLAY_KEY = "UPDATE_ROOM_MODAL";

type LocalSettings = {
  rememberSettings: boolean;
  enableSelfHosting: boolean;
};

const [gettingGeoLocation, setGettingGeoLocation] = createSignal(false);
const [fields, setFields] = createStore<CreateRoomParam>({ ...DEFAULT_CREATE_ROOM_PARAM });
const [hostDetails, setHostDetails] = createSignal<RoomHostDetails>();
const [localFields, setLocalFields] = createStore<LocalSettings>({ enableSelfHosting: false, rememberSettings: true });
const [invalidFields, setInvalidFields] = createSignal<string[]>([]);
const [countrySelectOptions, setCountrySelectOptions] = createSignal<string[]>(CountriesOptions);
const defaultStageDetails = RoomStageDetails.create({
  stage: RoomStagesNS.DEFAULT_STAGE
});

const generateRoomName = () => `Room_${generateUUID().substring(0, 10).replaceAll("-", "")}`;

const onDisableSelfHostingToggle = (appSettingsService: ReturnType<typeof AppSettingsService>) => {
  setLocalFields("enableSelfHosting", false);
  appSettingsService.setLocalStorage("lastSavedRoomSettings", undefined);
};

const getMaxPlayers = (isPro: boolean = false) => {
  if (localFields.enableSelfHosting && isPro) return ROOM_CONST.MAX_SELF_HOSTED_TOTAL_PLAYERS;
  return RoomTypeHelper.IsLobby(fields.RoomType) ? ROOM_CONST.MAX_LOBBY_PLAYERS : ROOM_CONST.MAX_TOTAL_PLAYERS;
};

let onSetContinentCode = (service: ReturnType<typeof SelfHostingService>, code: string, name?: string) => {
  service.setContinent({ code, name: name ?? DEFAULT_CONTINENT });
  setCountrySelectOptions(
    [
      DEFAULT_COUNTRY,
      ...service.countriesEntries.filter(x => x[1].continent == code).map(x => x[1].name)
    ]
  );
};

type MenuSwitchProps = {
  defaultValue: boolean;
  fieldName: string;
  i18Key?: string;
  label?: string;
  disabled?: boolean;
  set: SetStoreFunction<any>;
  onChange?: (value: boolean) => void;
};

const MenuHeading: Component<{ membersOnly?: boolean, label: string, tooltip?: JSX.Element; }> = (props) => {
  const appService = useService(AppService);
  const [showMembersOnly] = createSignal(props.membersOnly && !appService().isClientMember());

  return (<Box position={"relative"}>
    <HStack spacing={"$2"} marginBottom={5}>
      <Heading fontSize={"$md !important"} color={"$tertiary5"}>{props.label}</Heading>
      {props.tooltip && <ToolTipHelp tooltipLabel={props.tooltip} />}

      {(showMembersOnly()) &&
        <Box position={"absolute"} right={-20} top={10.5} w="10px">
          <MembersOnlyFeature borderRadius={10} w={14} h={14} border={"1px solid white"} />
        </Box>
      }
    </HStack>

  </Box>);
};

const Divider = () => <MenuContentDivider marginBoth={10} />;

const MenuSwitch: Component<MenuSwitchProps> = (props) => {
  const i18nService = useService(I18nService);
  const sfxService = useService(SoundEffectsService);
  const [value, setValue] = createSignal(props.defaultValue);

  return (<>
    <Switch
      // @ts-ignore
      size="md"
      onClick={() => {
        setValue(!value());
        sfxService().playSFX(AppSoundFx.CLICK2);
        /* @ts-ignore */
        props.set(props.fieldName, value());
        //@ts-ignore
        props.onChange?.(value());
      }}
      disabled={props.disabled}
      checked={value()}
      variant="outline" labelPlacement="end" colorScheme="accent" >
      {props.i18Key ? i18nService().t_roomPage(props.i18Key) : props.label}</Switch>
  </>);
};

const RoomBasicMenu = () => {
  let roomStageDetails = { ...defaultStageDetails };

  try {
    let decoded = RoomStageDetails.decode(fields.StageDetailsPROTO);
    roomStageDetails = decoded;
  } catch (ex) { }

  const appService = useService(AppService);
  const emojifyService = useService(EmojifyService);
  const displayService = useService(DisplaysService);

  const isUpdateRoom = () => displayService().getDisplay(UPDATE_ROOM_DISPLAY_KEY);
  const [stageDetails] = createSignal<RoomStageDetails>(roomStageDetails);

  onMount(() => {
    if (isEmpty(fields.RoomName)) setFields("RoomName", generateRoomName());
  });

  return (<>
    <MenuHeading label="General" />
    <InputGroup width="100%" >
      <VStack spacing="$2" alignItems={"baseline"} width="100%">
        <Input
          disabled={isUpdateRoom()}
          required placeholder="name"
          maxlength={ROOM_CONST.MAX_ROOMNAME_LENGTH}
          value={fields.RoomName}
          invalid={invalidFields().includes("RoomName")}
          ref={(el: HTMLElement) => inputSFX(el)}
          /* @ts-ignore */
          onInput={(e) => setFields("RoomName", e.target.value)}
        />
        <Input
          autocomplete="false"
          placeholder="password"
          maxlength={ROOM_CONST.MAX_PASSWORD_LENGTH}
          type="password"
          value={fields.Password}
          ref={(el: HTMLElement) => inputSFX(el)}
          invalid={invalidFields().includes("Password")}
          /* @ts-ignore */
          onInput={(e) => setFields("Password", e.target.value)}
        />
        <HStack spacing={"$1_5"} marginTop={10}>
          <Switch
            defaultChecked={fields.RoomStatus == RoomStatus.Private}
            onClick={() => setFields("RoomStatus", fields.RoomStatus == RoomStatus.Private ? RoomStatus.Public : RoomStatus.Private)}
            marginLeft={5}
            marginBottom={5}
            checked={fields.RoomStatus == RoomStatus.Private}
            labelPlacement="end"
            size="sm"
            colorScheme="accent"
          >
            Make room private</Switch>
          <ToolTipHelp
            tooltipLabel={"Making a room private will hide it from the global rooms list."}
            marginBottom={6} />
        </HStack>
        <Divider />

        <MenuHeading
          label="Room Stage"
          membersOnly
          tooltip={"Select a predefined 3D stage for your room. (Disabled in 2D mode)"}
        />
        <Box w="100%" className={!appService().isClientMember() ? "disabled" : ""}>
          <MenuContentSelect
            label="Room Stage"
            placeholder="Select a room stage"
            // TODO: Add scene mode for 2D
            // disabled={isUpdateRoom() || globalService().sceneMode() == PianoRhythmSceneMode.TWO_D}
            disabled={isUpdateRoom()}
            defaultValue={`${stageDetails().stage != null ? RoomStages[stageDetails().stage!] : RoomStages[RoomStagesNS.DEFAULT_STAGE]}`}
            showOnlySelect
            options={
              Object.values(RoomStages)
                .filter(x =>
                  !isArray(x) &&
                  !RoomStagesNS.IGNORED_STAGES.includes(x as any) &&
                  !RoomStagesNS.IGNORED_STAGES.includes(RoomStages[x as any] as any)
                )
                .filter(value =>
                  isNaN(Number(value)) === true && value != RoomStages[RoomStages.UNKNOWN]
                ) as string[]
            }
            onSelected={async (value) => {
              let details: RoomStageDetails = { ...stageDetails(), stage: (RoomStages[value as any] as any) };
              //@ts-ignore
              setFields("StageDetailsPROTO", Array.from(RoomStageDetails.encode(details).finish()));
            }}
          />
        </Box>

        <Divider />
        <MenuHeading
          label="Room Mode"
          membersOnly
          tooltip={
            <Box w={200}>
              Select a variety of modes.
              <br /><br />
              From playing the piano with friends to playing different games.
            </Box>
          } />
        <Box w="100%" className={!appService().isClientMember() ? "disabled" : ""}>
          <MenuContentSelect
            disabled={isUpdateRoom()}
            label="Room Mode"
            placeholder="Select a room mode"
            defaultValue={roomTypeToJSON(RoomType.Normal)}
            value={roomTypeToJSON(fields.RoomType)}
            showOnlySelect
            options={
              [
                roomTypeToJSON(RoomType.Normal),
                roomTypeToJSON(RoomType.Orchestra),
              ]
                .filter(x => !RoomModes.IGNORED_MODES.includes(roomTypeFromJSON(x as any) as any))
            }
            onSelected={async (value) => {
              setFields("RoomType", roomTypeFromJSON(value as any));
            }}
          />
        </Box>

        <Divider />
        <MenuHeading
          label="Welcome Message"
          tooltip={
            <Box width={200}>
              <i>(Optional)</i> Set the welcome message for when users enter your room.
              <br /><br />
              The <code style={{ background: "var(--hope-colors-accent1)", "margin-right": "5px" }}>%%user%%</code>
              is a special token that will be replaced with the name of the user that enters your room.
            </Box>
          } />
        <Textarea
          resize="none"
          maxlength={ROOM_CONST.MAX_WELCOME_MESSAGE_LENGTH}
          value={emojifyService().encode(fields.WelcomeMessage)}
          invalid={invalidFields().includes("WelcomeMessage")}
          /* @ts-ignore */
          onInput={(e) => setFields("WelcomeMessage", e.target.value)}
          placeholder="Type a welcome message here..." />

      </VStack>
    </InputGroup>
  </>);
};

const RoomAdvancedMenu = () => {
  const selfHostService = useService(SelfHostingService);
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const [maxPlayersSlider, setMaxPlayersSlider] = createSignal<noUiSlider.API>();

  createEffect(() => {
    maxPlayersSlider()?.updateOptions({ range: { 'min': 1, 'max': getMaxPlayers(appService().isClientProMember()) } }, true);
  });

  const onMaxPlayersSliderMount = (element: HTMLElement) => {
    if (element.className.includes("noUi-target")) return;
    let slider = noUiSlider.create(element, {
      orientation: "horizontal",
      connect: true,
      start: fields.MaxPlayers,
      step: 1,
      range: { 'min': 1, 'max': getMaxPlayers(appService().isClientProMember()) },
    });

    slider.on("update", (values) => {
      let value = parseInt(values[0] as any);
      setFields("MaxPlayers", value);
    });

    setMaxPlayersSlider(slider);
  };

  const selfHostingDisabled = () => !localFields.enableSelfHosting;

  return (<>
    <MenuHeading label="Advanced Settings" />
    <InputGroup variant="outline">
      <VStack spacing="$2" alignItems={"baseline"} padding="$2" w="100%">
        <MenuSwitch set={setFields} defaultValue={fields.OnlyOwnerCanChat} fieldName="OnlyOwnerCanChat" i18Key="modals.room.labels.onlyOwnerCanChat" />
        <MenuSwitch set={setFields} defaultValue={fields.OnlyOwnerCanPlay} fieldName="OnlyOwnerCanPlay" i18Key="modals.room.labels.onlyOwnerCanPlay" />
        <MenuSwitch set={setFields} defaultValue={fields.AllowGuests} fieldName="AllowGuests" i18Key="modals.room.labels.allowGuests" />
        <MenuSwitch set={setFields} defaultValue={fields.AllowBots} fieldName="AllowBots" i18Key="modals.room.labels.allowBots" />
        <MenuSwitch set={setFields} defaultValue={fields.AllowBlackMidi} fieldName="AllowBlackMidi" i18Key="modals.room.labels.allowBlackMidi" />
        <MenuSwitch set={setFields} defaultValue={fields.FilterProfanity} fieldName="FilterProfanity" i18Key="modals.room.labels.filterProfanity" />
        <Divider />
        <MenuHeading label={`Max Players - ${fields.MaxPlayers}`} />
        <Box
          id={`room-slider-id-max-players`}
          class={"noUi-target noUi-horizontal"}
          w="100%"
          ref={onMaxPlayersSliderMount}
        />
        <>
          <Divider />
          <MenuHeading
            label={`Self Hosting`}
            membersOnly
            tooltip="You'll be able to partially host your own room."
          />
          <MenuSwitch
            set={setLocalFields}
            label="Enable"
            defaultValue={localFields.enableSelfHosting}
            onChange={(value) => { if (!value) onDisableSelfHostingToggle(appSettingsService()); }}
            fieldName="enableSelfHosting" />
          <Box w="100%" className={selfHostingDisabled() ? "disabled" : ""}>
            <MenuContentSelect
              label="Continent"
              placeholder="Select a continent"
              value={selfHostService().continent().name}
              showOnlySelect
              loading={gettingGeoLocation()}
              options={selfHostService().continentOptions}
              optionsMeta={selfHostService().continentOptionsMeta as any}
              onSelected={async (name) => {
                let code = selfHostService().getContinentCode(name);
                onSetContinentCode(selfHostService(), code, name);
                selfHostService().setCountry({ code: DEFAULT_CODE, name: DEFAULT_COUNTRY });
              }}
            />
          </Box>
          <Box w="100%" className={selfHostingDisabled() ? "disabled" : ""}>
            <MenuContentSelect
              label="Country"
              placeholder="Select a country"
              value={selfHostService().country().name}
              showOnlySelect
              loading={gettingGeoLocation()}
              options={countrySelectOptions()}
              optionsMeta={selfHostService().countriesOptionsMeta}
              onSelected={async (name) => {
                selfHostService().setCountry({ code: getCountryCode(name) || DEFAULT_COUNTRY, name });
              }}
            />
          </Box>
        </>
        <Divider />
        <MenuHeading label="Other" />
        <MenuSwitch set={setLocalFields} defaultValue={localFields.rememberSettings} fieldName="rememberSettings" label="Remember last settings" />
      </VStack>
    </InputGroup>
  </>);
};

export default function NewRoomModal() {
  let submitOnFinallyTimeout = -1;
  const { isOpen, onOpen, onClose } = createDisclosure();

  const selfHostService = useService(SelfHostingService);
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);
  const webSocketService = useService(WebsocketService);
  const appService = useService(AppService);
  const emojifyService = useService(EmojifyService);
  const analyticsService = useService(MonitorTrackingService);
  const sfxService = useService(SoundEffectsService);

  const [loading, setLoading] = createSignal(false);
  const isNewRoom = createMemo(() => displayService().getDisplay(NEW_ROOM_DISPLAY_KEY));
  const isUpdateRoom = createMemo(() => displayService().getDisplay(UPDATE_ROOM_DISPLAY_KEY));
  const infoText = createMemo(() => isUpdateRoom() ? "Update Room" : "Create Room");
  const [currentRoomSettings, setCurrentRoomSettings] = createStore<CreateRoomParam>({ ...DEFAULT_CREATE_ROOM_PARAM });

  const TextGroups: SettingsSubTextGroups<any, any>[] = [
    {
      header: "Menu", texts: [
        { label: "Basic", content: RoomBasicMenu },
        { label: "Advanced", content: RoomAdvancedMenu },
      ]
    }
  ];

  function closeModal(forceClose = false) {
    if (isNewRoom()) {
      if (loading() && !forceClose) return;

      displayService().setDisplay(NEW_ROOM_DISPLAY_KEY, false);
      if (!localFields.rememberSettings) {
        setFields({ ...DEFAULT_CREATE_ROOM_PARAM });
        onSetHostDetails();
        appSettingsService().setLocalStorage("lastSavedRoomSettings", undefined);
      }
    }

    if (isUpdateRoom()) displayService().setDisplay(UPDATE_ROOM_DISPLAY_KEY, false);
    setInvalidFields([]);
  }

  const onSetHostDetails = (details?: RoomHostDetails) => {
    if (details) {
      setHostDetails({
        ContinentCode: details.ContinentCode,
        CountryCode: details.CountryCode
      });
    } else {
      if (!hostDetails()) return;
      setHostDetails();
      selfHostService().resetCountryAndContinent();
    }
  };

  const submitOnFinally = (result: CreateRoomParam, isCreate = true) => {
    window.clearTimeout(submitOnFinallyTimeout);
    submitOnFinallyTimeout = window.setTimeout(() => {
      setLoading(false);
      if (isCreate) {
        analyticsService().trackEvent(TRACKING_EVENT_IDS.ROOM_TYPE_CREATED, roomTypeToJSON(result.RoomType));
        analyticsService().trackEvent(TRACKING_EVENT_IDS.ROOM_STATUS_CREATED, roomStatusToJSON(result.RoomStatus));

        try {
          let decoded = RoomStageDetails.decode(fields.StageDetailsPROTO);
          analyticsService().trackEvent(TRACKING_EVENT_IDS.ROOM_STAGE_CREATED, roomStagesToJSON(decoded.stage));
        } catch (ex) { }
      }
    }, 1000);
  };

  function onCreateRoom(result: CreateRoomParam) {
    return webSocketService().createOrUpdateRoom(result).finally(() => submitOnFinally(result));
  }

  function onUpdateRoom(result: CreateRoomParam) {
    return webSocketService().createOrUpdateRoom(result, "UpdateRoom").finally(() => submitOnFinally(result, false));
  }

  function onSubmit() {
    setLoading(true);

    if (fields.StageDetailsPROTO == null) {
      try {
        setFields("StageDetailsPROTO",
          RoomStageDetails.encode(RoomStageDetails.create({
            stage: RoomStagesNS.DEFAULT_STAGE
          })).finish()
        );
      } catch (ex) { }
    }

    RoomSettingsObjectSchema
      .parseAsync(fields)
      .then(async () => {
        let input = cloneDeep<CreateRoomParam>(fields as any);

        if (isNewRoom()) input.RoomID = undefined;
        input.RoomName = emojifyService().encode(input.RoomName) as string;
        input.WelcomeMessage = emojifyService().encode(input.WelcomeMessage);

        if (!localFields.enableSelfHosting || selfHostService().areDefaultCountryAndContinent()) {
          input.HostDetails = undefined;
        } else {
          input.HostDetails = RoomHostDetails.create({
            ContinentCode: selfHostService()?.continent().code,
            CountryCode: selfHostService()?.country().code
          });
        }

        isNewRoom() ? await onCreateRoom(input) : await onUpdateRoom(input);
        let validationTimeout = isNewRoom() ? 5 : 10;
        let action = isNewRoom() ? "create" : "update";

        try {
          const [createRoomEffect, setCreateRoomEffect] = createSignal<AppStateEffects>();

          const listenForJoinRoomEffect =
            appService().appStateEffects.listen((effect) => {
              let action = effect.action;
              let onTargetEffect =
                action == AppStateEffects_Action.JoinedRoomSuccess ||
                action == AppStateEffects_Action.SetRoomSettings ||
                action == (isNewRoom() ? AppStateEffects_Action.CreateRoomValidationErrors : AppStateEffects_Action.UpdateRoomValidationErrors);

              if (onTargetEffect) setCreateRoomEffect(effect);
            });

          await raceTimeout(until(() => createRoomEffect()),
            1000 * validationTimeout, true, "Create room timeout");

          listenForJoinRoomEffect();

          let validationErrors =
            match(createRoomEffect())
              .with({ action: AppStateEffects_Action.CreateRoomValidationErrors }, (action) =>
                action.clientValidationErrorList?.data || []
              )
              .with({ action: AppStateEffects_Action.UpdateRoomValidationErrors }, (action) =>
                action.clientValidationErrorList?.data || []
              )
              .otherwise(() => []);

          validationErrors.forEach(x => {
            notificationService.show({
              type: "danger",
              title: `Failed to ${action} room`,
              description: `${x.FieldName} -> ${x.Reason}`
            });
          });

          if (validationErrors.length == 0) {
            if (isNewRoom()) appSettingsService().setLocalStorage("lastSavedRoomSettings", input);
            appService().setCurrentRoomParam(input);
            closeModal(true);
          } else {
            setInvalidFields(validationErrors.map(x => x.FieldName));
          }
        } catch (ex) {
          logError(`[${action}] ${ex}`);
          sfxService().playErrorSFX();
          notificationService.show({
            type: "danger",
            title: `Failed to ${action} room`,
            description: `Something went wrong when trying to ${action} the room. Please try again later...`
          });
        }
      })
      .catch((ex) => {
        logError(`[RoomSettings] ${ex}`);
        notificationService.show({
          type: "danger",
          title: "Validation Error",
          description: "Please check the form for any errors."
        });
        setLoading(false);
      });
  }

  function isFormValid() {
    let valid = RoomSettingsObjectSchema.safeParse(cloneDeep(fields));
    if (!valid.success) return false;

    let h1 = unwrap(cloneDeep(currentRoomSettings.HostDetails));
    let h2 = RoomHostDetails.create({
      ContinentCode: selfHostService()?.continent().code,
      CountryCode: selfHostService()?.country().code
    });

    if (localFields.enableSelfHosting) {
      let hostDetailsValid = HostDetailsSchema.safeParse({
        continentCode: h2.ContinentCode,
        countryCode: h2.CountryCode
      });
      if (!hostDetailsValid.success) return false;
    }

    if (isUpdateRoom()) {
      let targetFields = Object.keys(currentRoomSettings);
      let omitted = ["RoomName", "RoomID", "RoomOwner"];
      let c1 = omit(pick(cloneDeep(currentRoomSettings), targetFields), omitted);
      let c2 = omit(pick(cloneDeep(fields), targetFields), omitted);

      let coreSettingsMatch = isEqual(c1, c2);
      let hostDetailsMatch = Boolean(h1 && h2 && isEqual(h1, h2));

      if (coreSettingsMatch) {
        if (!hostDetailsMatch) {
          if (localFields.enableSelfHosting && h2) return true;
        } else {
          if (!localFields.enableSelfHosting && h1) return true;
        }
        return false;
      }
    }

    return true;
  }

  onMount(() => {
    blurActiveElement();
    let cont = selfHostService().continent();
    onSetContinentCode(selfHostService(), cont.code, cont.name);
  });

  onCleanup(() => {
    window.clearTimeout(submitOnFinallyTimeout);
  });

  createEffect(async () => {
    if (isNewRoom() || isUpdateRoom()) {
      onOpen();

      if (isUpdateRoom()) {
        try {
          let details = await getRoomSettings(appService().roomID()!);
          // appService().setRoomSettings(details);
        } catch {

        }

        webSocketService().emitProtoServerMessageOfCommand(ServerCommandDU.create({
          commandType: ServerCommandDU_CommandType.GetRoomFullDetails,
          roomID: appService().roomID(),
        }));
      }

      if (isNewRoom()) {
        // Check local storage for settings
        let lastSavedSettings = appSettingsService().getLocalStorage<CreateRoomParam>("lastSavedRoomSettings");
        if (lastSavedSettings) {
          setFields(lastSavedSettings);
          onSetHostDetails(lastSavedSettings.HostDetails);
          setLocalFields("enableSelfHosting", lastSavedSettings.HostDetails != null);
        }
      }

    } else { onClose(); }
  });

  createEffect(() => {
    if (isUpdateRoom()) {
      let currentParam = appService().currentRoomParam();
      let roomSettings = appService().roomSettings();
      let isRoomOwner = appService().isClientRoomOwner();

      // If the room settings is null or if current param is null and we're not a moderator,
      // then we can't update the room.
      if (roomSettings == null || (currentParam == null && !appService().isClientMod())) {
        return;
      }

      let settings: CreateRoomParam;
      if (isRoomOwner) {
        settings = {
          ...DEFAULT_CREATE_ROOM_PARAM,
          RoomID: appService().roomID(),
          RoomName: appService().roomName() || "Unavailable",
          RoomType: appService().roomType()!,
          RoomOwner: appService().roomOwner()!,
          RoomStatus: currentParam?.RoomStatus || roomSettings.RoomStatus,
          Password: currentParam?.Password || roomSettings.Password,
          MaxPlayers: currentParam?.MaxPlayers || roomSettings.MaxPlayers,
          WelcomeMessage: defaultTo(currentParam?.WelcomeMessage, roomSettings.WelcomeMessage),
          OnlyOwnerCanChat: defaultTo(currentParam?.OnlyOwnerCanChat, roomSettings.OnlyOwnerCanChat),
          OnlyOwnerCanPlay: defaultTo(currentParam?.OnlyOwnerCanPlay, roomSettings.OnlyOwnerCanPlay),
          AllowBlackMidi: defaultTo(currentParam?.AllowBlackMidi, roomSettings.AllowBlackMidi),
          AllowGuests: defaultTo(currentParam?.AllowGuests, roomSettings.AllowGuests),
          AllowBots: defaultTo(currentParam?.AllowBots, roomSettings.AllowBots),
          OnlyMods: defaultTo(currentParam?.OnlyMods, roomSettings.OnlyMods),
          FilterProfanity: defaultTo(currentParam?.FilterProfanity, roomSettings.FilterProfanity),
          StageDetailsPROTO: defaultTo(currentParam?.StageDetailsPROTO, DEFAULT_CREATE_ROOM_PARAM.StageDetailsPROTO) ?? new Uint8Array(),
          HostDetails: defaultTo(currentParam?.HostDetails, roomSettings.HostDetails),
        };
      } else {
        settings = {
          ...DEFAULT_CREATE_ROOM_PARAM,
          RoomID: appService().roomID(),
          RoomName: appService().roomName() || "Unavailable",
          RoomType: roomSettings.RoomType,
          RoomOwner: roomSettings.RoomOwner,
          RoomStatus: roomSettings.RoomStatus,
          Password: roomSettings.Password,
          MaxPlayers: roomSettings.MaxPlayers,
          WelcomeMessage: roomSettings.WelcomeMessage,
          OnlyOwnerCanChat: roomSettings.OnlyOwnerCanChat,
          OnlyOwnerCanPlay: roomSettings.OnlyOwnerCanPlay,
          AllowBlackMidi: roomSettings.AllowBlackMidi,
          AllowGuests: roomSettings.AllowGuests,
          AllowBots: roomSettings.AllowBots,
          OnlyMods: roomSettings.OnlyMods,
          FilterProfanity: roomSettings.FilterProfanity,
          StageDetailsPROTO: roomSettings.StageDetails ? RoomStageDetails.encode(roomSettings.StageDetails).finish() : new Uint8Array(),
          HostDetails: roomSettings.HostDetails,
        };
      }

      if (COMMON.IS_DEV_MODE) console.log("roomSettings", appService().roomName(), roomSettings, currentParam, settings);

      setLocalFields("enableSelfHosting", settings.HostDetails != null);
      setFields(cloneDeep(settings));
      setCurrentRoomSettings(cloneDeep(settings));

      let details = settings.HostDetails;
      if (details) {
        let continent = selfHostService().continentOptionsMeta.find(x => x.tagLine == details!.ContinentCode);
        if (continent) {
          selfHostService().setContinent({ name: continent.id!, code: details.ContinentCode });
        }
        if (details.CountryCode) {
          let country = selfHostService().getCountryData(details.CountryCode as any);
          if (country) selfHostService().setCountry({ name: country.name, code: details.CountryCode });
        }
      }
    } else {
      setFields("RoomID", undefined);
    }
  });

  createEffect(async () => {
    if (localFields.enableSelfHosting) {
      if (selfHostService().areDefaultCountryAndContinent()) {
        try {
          if (navigator.geolocation) {
            setGettingGeoLocation(true);
            navigator.geolocation.getCurrentPosition((position) => {
              setGettingGeoLocation(false);
              let { latitude, longitude } = position.coords;
              let data = selfHostService().getCountryByLongAndLat(longitude, latitude);

              if (data.continent) onSetContinentCode(selfHostService(), data.continent,
                selfHostService().getContinentNameByCode(data.continent));

              if (data.iso2) selfHostService().setCountry({ code: data.iso2, name: data.name });
            });
          }
        } catch (ex) {
          console.error(ex);
        }
      }
    }
  });

  return (<>
    <Modal centered size={"xl"} opened={isOpen()} onClose={closeModal} scrollBehavior={"inside"}>
      <ModalOverlay />
      <ModalContent h={500}>
        <ModalCloseButton />
        <ModalHeader><Icon as={FaSolidFolderPlus} marginRight={5} marginBottom={5} />{infoText()}</ModalHeader>
        <ModalBody >
          {isOpen() && <SettingsMenu<any, any> textGroups={TextGroups} disabled={loading()} bodyMarginLeft={10} bodyPaddingRight={15} />}
        </ModalBody>
        <ModalFooter>
          <ButtonGroup spacing={"$2"}>
            <Button
              onMouseDown={() => {
                setFields({ ...DEFAULT_CREATE_ROOM_PARAM, RoomName: generateRoomName() });
                setLocalFields("enableSelfHosting", false);
                onDisableSelfHostingToggle(appSettingsService());
                selfHostService().resetCountryAndContinent();
              }}
              size="sm" variant="outline"
              disabled={isEqual(cloneDeep(fields), DEFAULT_CREATE_ROOM_PARAM) || loading()}
            >{"Reset to Default"}
            </Button>
            <Button
              loading={loading()}
              loadingText="Please wait..."
              onMouseDown={onSubmit}
              size="sm" variant="outline"
              disabled={!isFormValid() || loading()}>{infoText()}
            </Button>
          </ButtonGroup>
        </ModalFooter>
      </ModalContent>
    </Modal>
  </>);
}