import { FaSolidUsers } from "solid-icons/fa";
import { SimpleTableWidget } from "~/components/dashboard-widgets";
import { getActiveUsers } from "~/server/mod-dashboard.api";
import { ApiUserDto } from "~/types/user.types";

const ModDashboard_GeneralActiveUsers = () => {
  return (<>
    <SimpleTableWidget<ApiUserDto>
      label="Active Users"
      minHeight={200}
      dense
      enablePagination
      includedColumnHeaders={["username", "usertag", "nickname", "joined", "color", "statusText"]}
      icon={<FaSolidUsers />}
      fetcher={(input) => new Promise(async (resolve) => {
        let result = await getActiveUsers(input.limit(), input.skip());
        result.data.forEach(x => x.color = x.color?.toLowerCase());
        let meta = result.metaData;
        input.setTotalCount(meta.totalCount);
        input.setTotalPages(meta.totalPages);
        resolve(result);
      })}
    />
  </>);
};

export default ModDashboard_GeneralActiveUsers;