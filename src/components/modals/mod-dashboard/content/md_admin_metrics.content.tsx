import { Show } from "solid-js";
import { useService } from "solid-services";
import AppService from "~/services/app.service";
import { COMMON } from "~/util/const.common";
import { AdminViewFail } from "./md-common";

const ModDashboard_AdminServerMetrics = () => {
  const appService = useService(AppService);

  return (<>
    <Show when={appService().isClientAdmin()} fallback={<AdminViewFail />}>
      <iframe
        class="mod-dashboard-iframe"
        id="metrics-logs-iframe"
        title="PianoRhythm Server Metrics"
        src={COMMON.METRICS_URL}
      >
      </iframe>
    </Show>
  </>);
};

export default ModDashboard_AdminServerMetrics;