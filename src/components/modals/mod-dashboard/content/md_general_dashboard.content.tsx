import { VStack, SimpleGrid, Box } from "@hope-ui/solid";
import { FaSolidUsers, FaSolidDoorOpen, FaSolidUsersGear } from "solid-icons/fa";
import { SimpleDataWidget, SimpleTableWidget } from "~/components/dashboard-widgets";
import { getActiveRoomsCount, getActiveUsersCount, getMembersCount, getNewestMembers, getNewestRooms } from "~/server/mod-dashboard.api";
import { PaginatedDataResponse } from "~/types/api.types";
import { RoomDtoAPI } from "~/types/room.types";
import { ApiUserDto } from "~/types/user.types";

const ModDashboard_GeneralDashboard = () => {
  return (<>
    <VStack w="100%" spacing={"$2"} alignItems="stretch">
      <SimpleGrid columns={3} gap="$10" marginBottom={10}>
        <SimpleDataWidget
          label="Users Online"
          icon={<FaSolidUsers />}
          fetcher={() => new Promise(async (resolve) => {
            let request = await getActiveUsersCount();
            resolve(request.count);
          })}
        />
        <SimpleDataWidget
          label="Active Rooms"
          icon={<FaSolidDoorOpen />}
          fetcher={() => new Promise(async (resolve) => {
            let request = await getActiveRoomsCount();
            resolve(request.count);
          })}
        />
        <SimpleDataWidget
          label="Registered"
          icon={<FaSolidUsersGear />}
          fetcher={() => new Promise(async (resolve) => {
            let request = await getMembersCount();
            resolve(request.count);
          })}
        />
      </SimpleGrid>

      {/* Newest Members */}
      <SimpleTableWidget<ApiUserDto>
        label="Newest Members"
        height={200}
        includedColumnHeaders={["username", "usertag", "joined"]}
        icon={<FaSolidUsers />}
        fetcher={(input) => new Promise(async (resolve) => {
          let request = await getNewestMembers(input.limit() || 5);
          resolve(request);
        })}
      />

      {/* Newest Rooms */}
      <SimpleTableWidget<RoomDtoAPI>
        label="Newest Rooms"
        height={200}
        includedColumnHeaders={["roomName", "roomOwner", "roomType", "created"]}
        icon={<FaSolidDoorOpen />}
        mapCellValue={(header, _, value) => {
          if (header == "created")
            return <Box>{(new Date(parseInt(value))).toLocaleString()}</Box>;
        }}
        fetcher={(input) => new Promise(async (resolve) => {
          let request = await getNewestRooms(input.limit() || 5);
          resolve(request);
        })}
      />
    </VStack>
  </>);
};

export default ModDashboard_GeneralDashboard;