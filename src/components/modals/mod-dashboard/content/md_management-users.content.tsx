import { Box } from "@hope-ui/solid";
import { FaSolidUsers } from "solid-icons/fa";
import { SimpleTableWidget } from "~/components/dashboard-widgets";
import { getAllMembers } from "~/server/mod-dashboard.api";
import { ApiUserDto } from "~/types/user.types";

const ModDashboard_ManagementUsers = () => {
  return (<>
    <SimpleTableWidget<ApiUserDto>
      label="Members"
      minHeight={200}
      enablePagination
      includedColumnHeaders={[
        "username"
        , "nickname"
        , "usertag"
        , "roles"
        , "color"
        , "joined"
        , "lastOnline"
      ]}
      mapCellValue={(header, _, value) => {
        if (header == "joined") return <Box>{(new Date(value)).toLocaleDateString()}</Box>;
        if (header == "lastOnline") return <Box>{(new Date(value)).toLocaleDateString()}</Box>;
        if (header == "color") return <Box color={value}>{`${value}`.toLocaleUpperCase()}</Box>;
      }}
      icon={<FaSolidUsers />}
      fetcher={(input) => new Promise(async (resolve) => {
        let sort = input.sort();
        let skip = input.skip();
        let result = await getAllMembers({
          limit: input.limit(),
          skip,
          query: input.searchInput(),
          sort
        });

        let meta = result.metaData;
        input.setTotalCount(meta.totalCount);
        input.setTotalPages(meta.totalPages);
        result.data.forEach(x => x.color = x.color?.toLowerCase());
        resolve(result);
      })}
    />
  </>);
};

export default ModDashboard_ManagementUsers;