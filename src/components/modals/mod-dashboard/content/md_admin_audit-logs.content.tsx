import { Box } from "@hope-ui/solid";
import { FaSolidUsers } from "solid-icons/fa";
import { SimpleTableWidget } from "~/components/dashboard-widgets";
import { getAuditLogs } from "~/server/mod-dashboard.api";
import { AuditLogDto } from "~/types/api.types";

const ModDashboard_AdminAuditLogs = () => {
  return (<>
    <SimpleTableWidget<AuditLogDto>
      label="Audit Logs"
      minHeight={200}
      dense={true}
      enablePagination={true}
      excludedColumnHeaders={["changes", "uuid", "targetID", "reason"]}
      defaultSort="createdDate desc"
      mapHeaderNames={(header) => {
        switch (header) {
          case "createddate": return "created";
          case "actiontype": return "action";
          case "usertag": return "user";
          default: return header;
        }
      }}
      mapCellValue={(header, _, value) => {
        if (header.toLocaleLowerCase() == "createddate") return <Box>{(new Date(value)).toLocaleString()}</Box>;
      }}
      icon={<FaSolidUsers />}
      fetcher={(input) => new Promise(async (resolve) => {
        let sort = input.sort();
        let skip = input.skip();

        let request = await getAuditLogs(
          input.limit(),
          skip,
          input.searchInput(),
          sort
        );

        let meta = request.metaData;
        input.setTotalCount(meta.totalCount);
        input.setTotalPages(meta.totalPages);
        resolve(request);
      })}
    />
  </>);
};

export default ModDashboard_AdminAuditLogs;