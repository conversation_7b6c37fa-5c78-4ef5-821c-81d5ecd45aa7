import { Box } from "@hope-ui/solid";
import { FaSolidDoorOpen } from "solid-icons/fa";
import { SimpleTableWidget } from "~/components/dashboard-widgets";
import { getActiveRooms } from "~/server/mod-dashboard.api";
import { RoomDtoAPI } from "~/types/room.types";

const ModDashboard_GeneralActiveRooms = () => {
  return (<>
    <SimpleTableWidget<RoomDtoAPI>
      label="Active Rooms"
      includedColumnHeaders={["roomName", "roomOwner", "roomType", "created"]}
      minHeight={200}
      dense
      enablePagination
      icon={<FaSolidDoorOpen />}
      mapCellValue={(header, _, value) => {
        if (header == "created")
          return <Box>{(new Date(parseInt(value))).toLocaleString()}</Box>;
      }}
      fetcher={(input) => new Promise(async (resolve) => {
        let result = await getActiveRooms(input.limit(), input.skip());
        let meta = result.metaData;
        input.setTotalCount(meta.totalCount);
        input.setTotalPages(meta.totalPages);
        resolve(result);
      })}
    />
  </>);
};

export default ModDashboard_GeneralActiveRooms;