import { Box, HStack } from "@hope-ui/solid";
import { useAction } from "@solidjs/router";
import { FaSolidMagnifyingGlass, FaSolidTrash } from "solid-icons/fa";
import { onCleanup } from "solid-js";
import { useService } from "solid-services";
import { SimpleDataWidgetIconButton, SimpleTableWidget } from "~/components/dashboard-widgets";
import { deleteRoom, getActiveRooms } from "~/server/mod-dashboard.api";
import SoundEffectsService from "~/services/sound-effects.service";
import UsersService from "~/services/users-service";
import { RoomDtoAPI } from "~/types/room.types";
import SwalPR, { SwalDetailsRow } from "~/util/sweetalert";

const ModDashboard_ManagementRooms = () => {
  const sfxService = useService(SoundEffectsService);
  const usersService = useService(UsersService);
  const deleteRoomAction = useAction(deleteRoom);

  onCleanup(() => {
    SwalPR(sfxService).close();
  });

  return (<>
    <SimpleTableWidget<RoomDtoAPI>
      label="Rooms"
      minHeight={200}
      dense
      enablePagination
      excludedColumnHeaders={["stageDetailsJSON", "users"]}
      mapCellValue={(header, _, value) => {
        if (header == "created") return <Box>{(new Date(parseInt(value))).toLocaleString()}</Box>;
      }}
      fetcher={(input) => new Promise(async (resolve) => {
        let request = await getActiveRooms(input.limit(), input.skip());
        let meta = request.metaData;
        input.setTotalCount(meta.totalCount);
        input.setTotalPages(meta.totalPages);
        resolve(request);
      })}
      actions={(element, { highlight }) => {
        return (
          <HStack w="100%" spacing={"$1"}>
            {(element.users.filter(Boolean).length > 0) && <SimpleDataWidgetIconButton
              onClick={() => {
                highlight(true);
                Promise.all(
                  element
                    .users
                    .map(usersService().fetchActiveUser)
                ).then((users) => {
                  let _users = users.filter(Boolean);
                  if (_users.length > 0) {
                    let html = _users.map(x => `${SwalDetailsRow("Username", x?.usertag)}`);

                    SwalPR(sfxService).fire({
                      title: "Users",
                      html,
                    }).finally(() => highlight(false));
                  } else {
                    highlight(false);
                  }
                });
              }}
              icon={<FaSolidMagnifyingGlass />} label="View Users" />
            }
            <SimpleDataWidgetIconButton
              onClick={() => {
                highlight(true);
                SwalPR(sfxService).fire({
                  icon: "warning",
                  title: "Delete Room",
                  html: `
                  Are you sure you want to delete room
                  <b>${element.roomName}</b>?
                `,
                  showCancelButton: true
                }).then(async (result) => {
                  if (result.isConfirmed) {
                    try {
                      let request = await deleteRoomAction(element.roomId);
                      SwalPR(sfxService).fire({
                        icon: "success",
                        html: request.message,
                      });
                    } catch (err) {
                      console.error(err);
                      let message = "Sorry, something went wrong";
                      SwalPR(sfxService).fire({
                        title: "Result",
                        icon: "error",
                        html: message,
                      });
                    }
                  }
                })
                  .finally(() => highlight(false));
              }}
              icon={<FaSolidTrash />} label="Delete Room" />
          </HStack>);
      }}
    />
  </>);
};

export default ModDashboard_ManagementRooms;