import { createDisclosure, Icon, <PERSON>dal, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>lose<PERSON>utton, <PERSON>dal<PERSON>nt, <PERSON><PERSON>eader, <PERSON>dalOverlay } from "@hope-ui/solid";
import { FaSolidUserShield } from "solid-icons/fa";
import { Component, createEffect, lazy, Show } from "solid-js";
import { useService } from "solid-services";
import { SettingsMenu, SettingsSubTextGroups } from "~/components/SettingsMenu";
import DisplaysService from "~/services/displays.service";

const ModDashboard_AdminServerMetrics = lazy(() => import("./content/md_admin_metrics.content"));
const ModDashboard_AdminServerLogs = lazy(() => import("./content/md_admin_server-logs.content"));
const ModDashboard_GeneralActiveUsers = lazy(() => import("./content/md_general_active-users.content"));
const ModDashboard_GeneralActiveRooms = lazy(() => import("./content/md_general_active-rooms.content"));
const ModDashboard_GeneralDashboard = lazy(() => import("./content/md_general_dashboard.content"));
const ModDashboard_AdminAuditLogs = lazy(() => import("./content/md_admin_audit-logs.content"));
const ModDashboard_ManagementUsers = lazy(() => import("./content/md_management-users.content"));
const ModDashboard_ManagementRooms = lazy(() => import("./content/md_management-rooms.content"));

const TextGroups: SettingsSubTextGroups<any, any>[] = [
  {
    header: "General", texts: [
      { label: "Dashboard", content: () => ModDashboard_GeneralDashboard },
      { label: "Active Users", content: () => ModDashboard_GeneralActiveUsers },
      { label: "Active Rooms", content: () => ModDashboard_GeneralActiveRooms },
    ]
  },
  {
    header: "Admin", texts: [
      { label: "Server Logs", content: () => ModDashboard_AdminServerLogs },
      { label: "Audit Logs", content: () => ModDashboard_AdminAuditLogs },
      { label: "Metrics", content: () => ModDashboard_AdminServerMetrics },
      // { label: "Status", content: () => ModDashboard_AdminStatusPage },
    ]
  },
  {
    header: "Management", texts: [
      { label: "Members", content: () => ModDashboard_ManagementUsers },
      { label: "Rooms", content: () => ModDashboard_ManagementRooms },
      // { label: "Banned IPs", content: () => ModDashboard_BannedIPs },
      // { label: "Banned Accounts", content: () => ModDashboard_BannedAccounts },
      // { label: "Reports - Chats", content: () => ModDashboard_MessageReports },
      // { label: "Reports - Users", content: () => ModDashboard_UserReports },
    ]
  }
];

const ModDashboardModal: Component = () => {
  const DISPLAY_KEY = "MOD_DASHBOARD_MODAL";

  const { isOpen, onOpen, onClose } = createDisclosure();
  const displayService = useService(DisplaysService);

  function closeModal() {
    displayService().setDisplay(DISPLAY_KEY, false);
  }

  createEffect(() => {
    if (displayService().getDisplay(DISPLAY_KEY)) { onOpen(); } else { onClose(); }
  });

  return (<>
    <Modal
      centered
      opened={isOpen()} onClose={closeModal}
      scrollBehavior={"inside"}
      size="6xl"
      closeOnOverlayClick={false}
    >
      <ModalOverlay />
      <ModalContent overflow={"hidden"} height="100vh">
        <ModalCloseButton _hover={{ "background": "$accent1" }} />
        <ModalHeader><Icon as={FaSolidUserShield} marginRight={5} marginBottom={5} />
          {"Mod Dashboard"}
        </ModalHeader>
        <ModalBody background={"$primaryGradient1"}>
          <Show when={isOpen()}>
            <SettingsMenu<any, any> textGroups={TextGroups} bodyPadding={20} />
          </Show>
        </ModalBody>
      </ModalContent>
    </Modal>
  </>);
};

export default ModDashboardModal;