import { Box, Button, createDisclosure, Skeleton } from '@hope-ui/solid';
import { EventBus } from '@solid-primitives/event-bus';
import { createElementSize } from '@solid-primitives/resize-observer';
import anime from 'animejs';
import { debounce } from 'lodash-es';
import { Subscription } from 'rxjs';
import {
  Accessor,
  Component,
  createEffect,
  createSignal,
  ErrorBoundary,
  For,
  lazy,
  onCleanup,
  onMount,
  Show,
  Suspense
} from 'solid-js';
import { useService } from 'solid-services';
import { ChatMessageRecordProvider } from '~/contexts/chat-record.context';
import css from '~/sass/chat.module.sass';
import AppService from '~/services/app.service';
import ChatService from '~/services/chat.service';
import DisplaysService from '~/services/displays.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import { AppSoundFx2 } from '~/types/app.types';
import { HTML_IDS } from '~/util/const.common';
import { onRemoveAnimeInstance } from '~/util/helpers.anime-js';

const ChatMessageItem = lazy(() => import("./chat-message-item"));
const ChatMessageItemMoreMenu = lazy(() => import("./chat-message-item-more-menu"));

const ItemPadding = 10;

const [checkedNewMessages, setCheckedNewMessages] = createSignal(false);
const [newMessageAmount, setNewMessageAmount] = createSignal(0);

const NewMessages: Component<{
  scrollElementToBottom: () => void,
  parentElement: Accessor<HTMLDivElement | undefined>;
}> = (props) => {
  const parentElementSize = createElementSize(props.parentElement);
  const [newMessagesText, setNewMessagesText] = createSignal("");

  createEffect(() => {
    setNewMessagesText(newMessageAmount() > 1 ? `s (${newMessageAmount()})` : ``);
  });

  return (<>
    {(newMessageAmount() >= 1) &&
        <Box position={"fixed"} zIndex={999} top={parentElementSize.height || 0} h={20} w={200}>
            <Button
                __tooltip_title="Click to scroll chat to bottom!"
                __tooltip_placement='top'
                onMouseDown={() => {
                  props.scrollElementToBottom();
                  setCheckedNewMessages(true);
                  setNewMessageAmount(0);
                }}
                w={"100%"} h="100%" background={"orange"}>
                <Box>New Message{newMessagesText()}</Box>
            </Button>
        </Box>
    }
  </>);
};

const ChatItemSkeleton: Component = () => <Skeleton w={"100%"} h={85} marginBottom={5}/>;

type ChatListProps = {
  onContainerTransitionEndEvents?: EventBus<void>;
};

const ChatList: Component<ChatListProps> = (props) => {
  const chatService = useService(ChatService);
  const appService = useService(AppService);
  const appSettingsService = useService(AppSettingsService);
  const displayService = useService(DisplaysService);
  const sfxService = useService(SoundEffectsService);

  const [scrollToBottom, setScrollToBottom] = createSignal(false);
  const [isScrollAtBottom, setIsScrollAtBottom] = createSignal(false);
  const [showNewMessages, setShowNewMessages] = createSignal(false);
  const [isScrolling, setIsScrolling] = createSignal(false);
  const [animatingMount, setAnimatingMount] = createSignal(true);
  const [subscriptions, setSubscriptions] = createSignal<Subscription[]>([]);
  const { isOpen, onClose, onOpen } = createDisclosure();
  const [chatMessagesUpdated, setChatMessagesUpdated] = createSignal(chatService().alwaysAutoScroll(), { equals: false });
  const [scrollTargetElement, setScrollTargetElement] = createSignal<HTMLDivElement>();
  const margin = 15;
  const chatBarElementSize = createElementSize(chatService().chatBarElement);
  let animateChatListTimeout = -1;

  onMount(() => {
    setNewMessageAmount(0);

    let subscription = chatService().addedMessagesEvents.subscribe(v => {
      let belongsToClient = appService().client().socketID == v.socketID;

      if ((!v.canBeIgnoredOnScroll() && !chatService().alwaysAutoScroll()) || isScrollAtBottom()) {
        setChatMessagesUpdated(true);

        if (belongsToClient) {
          setCheckedNewMessages(true);
          setNewMessageAmount(0);
        } else {
          setNewMessageAmount(v => v + 1);
        }

        setScrollToBottom(true);
        scrollElementToBottom();
      }
    });
    setSubscriptions([...subscriptions(), subscription]);

    scrollTargetElement()?.addEventListener('scroll', onScrollEvent);
    scrollTargetElement()?.addEventListener('transitionend', (event) => {
      if (!chatService().alwaysAutoScroll()) return;
      if (event.propertyName == 'width') setScrollToBottom(true);
    });
    scrollTargetElement()?.addEventListener('scrollend', () => {
      setIsScrolling(false);

      if (isScrollAtBottom()) {
        setScrollToBottom(false);

        if (chatMessagesUpdated()) {
          setChatMessagesUpdated(false);
          setCheckedNewMessages(false);
          setNewMessageAmount(0);
        }
      }
    });
    window.addEventListener('resize', onResize);

    let sub = props.onContainerTransitionEndEvents?.listen(scrollElementToBottom);
    if (sub) onCleanup(() => sub());
  });

  onCleanup(() => {
    chatService().setChatMessageToHighlight(undefined);
    chatService().setActiveMessageItemIndexToShowOptionsMenu(undefined);
    chatService().setChatMessageBeingRepliedTo(undefined);
    chatService().setChatMessageBeingEdited(undefined);
    setNewMessageAmount(0);
    subscriptions().forEach(x => x.unsubscribe());

    let target = scrollTargetElement();
    if (target) anime.remove(target);

    scrollTargetElement()?.removeEventListener('scroll', onScrollEvent);
    window.removeEventListener('resize', onResize);
    window.clearTimeout(animateChatListTimeout);
  });

  const onSetScrollTargetElement = (element: HTMLDivElement) => {
    setScrollTargetElement(element);
    onQueryMediaScreenResize();
  };

  const getChatMaximizedWidth = () => {
    let widgetsDisplayed = displayService().getDisplay("SCENE_WIDGET_BUTTONS");
    let widgets = document.getElementById(HTML_IDS.ACTION_WIDGETS_CONTAINER);
    let widgetWidth = 0;

    if (widgetsDisplayed && widgets) {
      let rect2 = widgets.getBoundingClientRect();
      widgetWidth = rect2.width + 10;
    }

    return `calc(100vw - (var(--sidebarListWidth) + 20px + ${widgetWidth}px))`;
  };

  const defaultChatListWidth = `${chatService().DEFAULT_CHAT_LIST_WIDTH}px`;

  createEffect(() => {
    chatService().setAlwaysAutoScroll(appSettingsService().getSetting("CHAT_AUTO_SCROLL"));
  });

  // Set width on chat minimized/maximized
  createEffect(() => {
    if (animatingMount()) return;

    let containerListElement = scrollTargetElement();

    if (!containerListElement) return;
    anime.remove(containerListElement);

    containerListElement.style.width = chatService().chatMaximized() || isMobileScreen() ?
      getChatMaximizedWidth() : defaultChatListWidth;

    if (chatService().chatMaximized()) sfxService().playSFX_ui2(AppSoundFx2.PAUSE);
  });

  createEffect(() => {
    scrollElementToBottom();
  }, [
    isScrollAtBottom,
    scrollToBottom,
    chatMessagesUpdated,
    chatService().chatMaximized,
    chatService().alwaysAutoScroll,
  ]);

  createEffect(() => {
    setShowNewMessages(!isScrollAtBottom() && !checkedNewMessages() && chatMessagesUpdated());
  });

  const scrollElementToBottom = debounce(() => {
    if (!scrollToBottom() && !chatService().alwaysAutoScroll()) return;
    let element = scrollTargetElement();
    if (element) element.scrollTop = element.scrollHeight;
  }, 100);

  const onScrollEvent = debounce(() => {
    let element = scrollTargetElement();
    if (!element) return;

    setIsScrolling(true);
    let scrollTop = Math.round(element.scrollTop);
    let height = Math.round(element.scrollHeight - element.offsetHeight - 2);
    setIsScrollAtBottom(scrollTop >= height);
  });

  const isMobileScreen = () => {
    return window.matchMedia("(max-width: 768px)").matches;
  };

  const onQueryMediaScreenResize = () => {
    let containerListElement = scrollTargetElement();
    if (!containerListElement) return;

    // Query media screen width size for mobile
    if (isMobileScreen()) {
      containerListElement.style.width = getChatMaximizedWidth();
    }
  };

  const onResize = debounce(() => {
    scrollElementToBottom();
    onQueryMediaScreenResize();
  });

  const updateScrollTargetElementHeight = () => {
    let _scrollTargetElement = scrollTargetElement();
    if (_scrollTargetElement && _scrollTargetElement.style) {
      _scrollTargetElement.style.height = `calc(100% - ${chatBarElementSize.height}px - ${margin}px)`;
    }
  };

  createEffect(() => {
    if (!isOpen()) chatService().setActiveMessageItemIndexToShowOptionsMenu(undefined);
  });

  createEffect(() => {
    updateScrollTargetElementHeight();
  });

  let initialHeight = 40;
  let targetDuration = 500;

  function onAnimateChatList(element: HTMLDivElement) {
    onRemoveAnimeInstance(element);
    let playedSfx = false;
    let targetWidth = isMobileScreen() ? getChatMaximizedWidth() : defaultChatListWidth;

    anime({
      targets: element,
      keyframes: [
        {
          width: `180px`,
          height: `${initialHeight}px`
        },
        {
          width: targetWidth,
        },
      ],
      easing: 'easeOutElastic(0.1, 1)',
      duration: targetDuration,
      update: (cb) => {
        if (cb.currentTime > targetDuration * 0.7 && !playedSfx) {
          playedSfx = true;
          sfxService().playSFX_ui2(AppSoundFx2.PAUSE);
        }
      },
      complete: () => {
        element.style.height = "";
        element.style.minHeight = "100px";
        chatService().setShowChatWindowButtons(true);
        chatService().setShowChatBar(true);
        updateScrollTargetElementHeight();

        window.clearTimeout(animateChatListTimeout);
        animateChatListTimeout = window.setTimeout(() => {
          updateScrollTargetElementHeight();
          setAnimatingMount(false);
          scrollElementToBottom();
        }, 200);
      }
    });
  }

  function onContainerElement(element: HTMLDivElement) {
    let containerListElement = scrollTargetElement();

    if (containerListElement) {
      // interact(containerListElement)
      //   .resizable({
      //     edges: { left: true, right: true, bottom: false, top: false },
      //     listeners: {
      //       down(evt) { console.log("DOWN", evt); },
      //       move(event) {
      //         Object.assign(event.target.style, {
      //           width: `${event.rect.width}px`,
      //         });
      //       },
      //     },
      //     modifiers: [
      //       // minimum size
      //       interact.modifiers.restrictSize({
      //         min: { width: 285, height: 50 }
      //       })
      //     ],

      //     inertia: true
      //   });
    }
  }

  const scrollToBottomAfterLatestMessageLoad = debounce(scrollElementToBottom, 100);

  return (<>
    <Box
      class={css.chatContainerList}
      ref={(element: HTMLDivElement) => {
        onSetScrollTargetElement(element);
        onAnimateChatList(element);
      }}

      style={{
        padding: `${ItemPadding}px`,
        background: `${chatService().chatOpacity() == 1 ? "var(--hope-colors-primaryAlpha2)" : "var(--hope-colors-primaryAlpha3)"}`,
        "overflow-y": `${chatService().chatOpacity() == 1 ? "scroll" : "hidden"}`,
      }}
    >
      <Box ref={onContainerElement}>
        <For each={chatService().messages()}>
          {(item, index) => {
            let props = {
              items: chatService().messages(),
              item: item,
              index: index(),
              tabIndex: -1,
              style: {},
            };

            return (<Show when={!animatingMount()} fallback={<ChatItemSkeleton/>}>
              <Suspense fallback={<ChatItemSkeleton/>}>
                <ChatMessageRecordProvider record={item}>
                  <Box position={"relative"} w="100%" h="100">
                    <ErrorBoundary fallback={"Failed to load chat message."}>
                      <ChatMessageItem
                        onMoreOptionsClick={() => {
                          if (!isOpen() || chatService().activeMessageItemIndexToShowOptionsMenu() != item.id) {
                            onOpen();
                            chatService().setActiveMessageItemIndexToShowOptionsMenu(item.id);
                          } else onClose();
                        }}
                        onLoaded={() => {
                          if (index() != chatService().messages().length - 1) return;
                          setScrollToBottom(true);
                          scrollToBottomAfterLatestMessageLoad();

                          if (item.canBeIgnoredOnScroll()) return;
                          if (item.socketID == appService().client().socketID) return;
                          sfxService().playChatMessageInSFX({ volume: 0.2 });
                        }}
                        {...props}
                      />
                    </ErrorBoundary>

                    {chatService().activeMessageItemIndexToShowOptionsMenu() == item.id &&
                        <ChatMessageItemMoreMenu
                            isOpen={isOpen}
                            onClose={onClose}
                            record={item}
                        />
                    }
                  </Box>
                </ChatMessageRecordProvider>
              </Suspense>
            </Show>);
          }}
        </For>
      </Box>

      {(!animatingMount() && !isScrolling() && showNewMessages()) && <NewMessages
          scrollElementToBottom={scrollElementToBottom} parentElement={scrollTargetElement}/>}
    </Box>
  </>);
};

export default ChatList;