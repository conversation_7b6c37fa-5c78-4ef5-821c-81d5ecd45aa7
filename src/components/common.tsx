import {<PERSON>, Button, Center, hope, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>} from "@hope-ui/solid";
import clsx from "clsx";
import {chunk, isString} from "lodash-es";
import {FaSolidChevronLeft, FaSolidChevronRight, FaSolidUserCheck} from "solid-icons/fa";
import {
    Accessor,
    children,
    Component,
    createEffect,
    createSignal,
    For,
    JSX,
    JSXElement,
    ParentComponent,
    Show
} from "solid-js";
import {buttonSFX} from "~/directives/buttonsfx.directive";

type VirtualListGroupHeaderProps = {
    label: string;
    color?: string;
    marginTop?: number | string;
    marginBottom?: number | string;
    marginBoth?: number | string;
};

export const VirtualListGroupHeader: Component<VirtualListGroupHeaderProps> = (props) => {
    return (<>
        <Box
            id={`virtual-list-group-header-${props.label}`}
            data-testid={`virtual-list-group-header`}
            width={"fit-content"}
            fontSize={"0.5em"}
            marginTop={props.marginBoth ?? props.marginTop}
            marginBottom={props.marginBoth ?? props.marginBottom}
            textTransform="uppercase"
            color={props.color || "$neutral11"}
            padding={3}
            borderRadius={5}
            background={"$primaryDark2"}>{props.label}</Box>
    </>);
};

const MembersOnlyFeature_: Component = (props) => {
    return (<>
        <Box
            __tooltip_title={
                <Box>
                    This feature is not available for <b>guests</b>.
                    <br/><br/>
                    Sign up for a <i>free account</i> today!
                </Box>
            }
            position={"absolute"}
            top={-5}
            left={-5}
            fontSize={12}
            color="white"
            cursor={"help !important"}
            textTransform={"uppercase"}
            background={"$primary2"}
            padding={1}
            borderRadius={2}
            zIndex={2}
            border={"1px solid transparent"}
            _hover={{
                border: "1px solid white"
            }}
            {...props}
        >
            <Center w="100%" h="100%">
                <FaSolidUserCheck/>
            </Center>
        </Box>
    </>);
};

export const MembersOnlyFeature = hope(MembersOnlyFeature_);

type PaginationProps = {
    currentPage: Accessor<number>;
    totalPages: Accessor<number>;
    handlePage(page: number): void;
    width?: number | string;
    minWidth?: number | string;
};

export const Pagination: Component<PaginationProps> = (props) => {
    const MaxPagesToDisplay = 10;
    const [allPages, setAllPages] = createSignal<number[]>([]);
    const [pageChunks, setPageChunks] = createSignal<number[][]>([]);

    createEffect(() => {
        let all = [...Array(props.totalPages()).keys()];
        let chunks = chunk(all, MaxPagesToDisplay);
        setPageChunks(chunks);
    });

    createEffect(() => {
        let index = pageChunks().findIndex(x => (x.find(y => y == (props.currentPage() - 1)) != null));
        if (index != -1) setAllPages(pageChunks()[index]!);
    });

    return (
        <Box as="nav" class="pagination"
             w={props.width}
             minWidth={props.minWidth}
        >
            <HStack justifyContent="space-between">
                <HStack
                    __tooltip_title="Go to First Page"
                    __tooltip_placement="top"
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    onMouseDown={() => props.handlePage(1)}
                    className={props.currentPage() == 1 ? "disabled" : "page-btn"}>
                    <FaSolidChevronLeft/>
                    <Box marginLeft={-5}><FaSolidChevronLeft/></Box>
                </HStack>

                <FaSolidChevronLeft
                    __tooltip_title="Go to Previous Page"
                    __tooltip_placement="top"
                    //@ts-ignore
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    class={props.currentPage() == 1 ? "disabled" : "page-btn"}
                    onMouseDown={() => props.handlePage(props.currentPage() - 1)}
                />

                <For each={allPages()}>
                    {v => (
                        <Box
                            __tooltip_title={`Go to: Page ${v + 1}`}
                            class={clsx([
                                "page-item unselectable",
                                props.currentPage() === v + 1 && "--active",
                            ])}
                            onMouseDown={() => props.currentPage() != v + 1 ? props.handlePage(v + 1) : {}}
                        >
                            {v + 1}
                        </Box>
                    )}
                </For>

                <FaSolidChevronRight
                    __tooltip_title="Go to Next Page"
                    __tooltip_placement="top"
                    //@ts-ignore
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    class={props.currentPage() == props.totalPages() ? "disabled" : "page-btn"}
                    onMouseDown={() => props.handlePage(props.currentPage() + 1)}
                />

                <HStack
                    __tooltip_title="Go to Last Page"
                    __tooltip_placement="top"
                    ref={(el: HTMLElement) => buttonSFX(el)}
                    onMouseDown={() => props.handlePage(props.totalPages())}
                    className={props.currentPage() == props.totalPages() ? "disabled" : "page-btn"}>
                    <FaSolidChevronRight/>
                    <Box marginLeft={-5}><FaSolidChevronRight/></Box>
                </HStack>
            </HStack>
        </Box>
    );
};

const ButtonToolTip_: Component<{
    label: string;
    tooltip?: string;
    openDelay?: number;
    loading?: boolean;
    loadingText?: string;
    disabled?: boolean;
    leftIcon?: JSXElement;
    rightIcon?: JSXElement;
    onClick?: (evt: MouseEvent) => void;
}> = (props) => {
    return (<>
        <Tooltip
            closeOnClick
            closeOnMouseDown
            openDelay={props.openDelay || 500}
            placement="top"
            disabled={!props.tooltip}
            label={props.tooltip}
            withArrow
            padding={10}
            background={"$neutral12"}
            color={"$primary1"}
            fontWeight={450}
        >
            <HStack spacing={"$1"} w="100%" h="100%">
                <Button
                    {...props}
                    loading={props.loading}
                    loadingText={props.loadingText}
                    disabled={props.disabled}
                    onClick={(evt: MouseEvent) => props.onClick?.(evt)}
                    ref={(el: HTMLElement) => buttonSFX(el)}
                >{props.label}</Button>
            </HStack>
        </Tooltip>
    </>);
};

export const CenteredLoadingIcon = () => {
    return (
        <Center w="100%" h="100%">
            <HStack spacing={"$2"}>
                <Spinner/>
                <Box zIndex={1}>Loading...</Box>
            </HStack>
        </Center>
    );
};

export const ButtonToolTip = hope(ButtonToolTip_);