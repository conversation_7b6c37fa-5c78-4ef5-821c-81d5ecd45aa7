import { Box } from "@hope-ui/solid";
import { createResource, Show } from "solid-js";
import { getAssetImage } from "~/server/general.api";

export default function MainBackgroundImage() {
  const [image] = createResource(() => getAssetImage("/images/mainBG.webp"));

  return (<>
    <Show when={image.state == "ready"}>
      <Box
        class="elementFadeIn"
        w="100vw"
        h="100dvh"
        top={0}
        left={0}
        zIndex={-1}
      >
        <img
          src={image()}
          alt="background image"
          style={{ width: "100%", height: "100%", "object-fit": "cover" }}
        />
        <Box class="glassBackgroundFull" />
      </Box>
    </Show>

  </>);
};