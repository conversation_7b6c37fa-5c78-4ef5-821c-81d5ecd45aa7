import { HStack, I<PERSON><PERSON><PERSON>on } from "@hope-ui/solid";
import { BiSolidEdit } from 'solid-icons/bi';
import { FaSolidEllipsis, FaSolidReply, FaSolidTrash } from "solid-icons/fa";
import { Component, JSXElement, createEffect, createSignal } from "solid-js";
import { useService } from "solid-services";
import css from '~/sass/chat.module.sass';
import AppService from "~/services/app.service";
import ChatService from "~/services/chat.service";
import WebsocketService from "~/services/websocket.service";
import { ChatMessageRecord } from "~/types/chat.types";
import { deleteMessageByID } from "./chat.common";

const OptionsButton: Component<{ icon: JSXElement, label: string, disabled?: boolean, onclick?: () => void; }> = (props) => {
  return (<>
    <IconButton
      __tooltip_title={props.label}
      disabled={props.disabled}
      onMouseDown={(evt: MouseEvent) => { if (evt.button == 0) props.onclick?.(); }}
      borderRadius={0} size="xs"
      aria-label={props.label} icon={props.icon} />
  </>);
};

type ChatMessageItemOptionsProps = {
  onMoreOptionsClick?: () => void;
  record: ChatMessageRecord;
};

const ChatMessageItemOptions: Component<ChatMessageItemOptionsProps> = (props) => {
  const appService = useService(AppService);
  const chatService = useService(ChatService);
  const websocketService = useService(WebsocketService);
  const [canDelete, setCanDelete] = createSignal(false);
  const [canShowMoreOptions, setCanShowMoreOptions] = createSignal(false);
  const isSystemMessage = () => chatService().isSystemMessage(props.record);

  createEffect(() => {
    setCanDelete(chatService().canDeleteMessage(props.record));
    setCanShowMoreOptions(!isSystemMessage() && appService().isClientMember());
  });

  const canEdit = () => chatService().canEditMessage(props.record);

  const canReply = () => chatService().canReplyToMessage(props.record);

  return (<>
    <HStack className={css.chatMessageItemOptionsContainer} background="$primary1">
      {canReply() &&
        <OptionsButton
          onclick={() => { chatService().setChatMessageBeingRepliedTo(props.record.id); }}
          icon={<FaSolidReply />} label="Reply" />
      }

      {canEdit() &&
        <OptionsButton
          onclick={() => chatService().onEditMessage(props.record)}
          icon={<BiSolidEdit />} label="Edit" />
      }

      {canDelete() &&
        <OptionsButton
          onclick={async () => {
            await deleteMessageByID(
              props.record.id,
              (id) => websocketService().emitServerCommand(["DeleteChatMessageById", id]),
              chatService()
            );
          }}
          icon={<FaSolidTrash />} label="Delete" />
      }

      <OptionsButton
        onclick={() => { props.onMoreOptionsClick?.(); }}
        disabled={!canShowMoreOptions()} icon={<FaSolidEllipsis />} label="More" />
    </HStack>
  </>);
};

export default ChatMessageItemOptions;