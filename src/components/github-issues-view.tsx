import { <PERSON><PERSON>, <PERSON>, <PERSON>, Divider, Drawer, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON><PERSON>, <PERSON>er<PERSON><PERSON>lay, HStack, Skeleton, Tag, VStack, createDisclosure } from "@hope-ui/solid";
import { FaSolidCircleCheck, FaSolidCircleDot } from "solid-icons/fa";
import { Accessor, Component, For, ParentComponent, Show, createEffect, createResource, createSignal, onMount } from "solid-js";
import { capitalizeFirstLetter, stringToColor } from "~/util/helpers";
import { SolidMarkDownText } from "./solid-markdown-text";
import { getGitHubIssues as _getGitHubIssues } from "~/server/github.api";

type GitHubLabel = {
  name: string,
  color: string,
  description?: string,
};

type GitHubBranch = {
  name: string,
  commit: {
    sha: string,
    url: string,
  },
  protected: boolean,
};

type GitHubIssue = {
  html_url: string,
  id: number,
  number: number,
  title: string,
  state: string,
  body?: string,
  locked: boolean,
  createdAt: string,
  updatedAt: string,
  closedAt: string,
  author: {
    login: string,
  };
  labels: GitHubLabel[];
  milestone?: {
    title: string,
    html_url: string,
    id: number,
    number: number,
  };
  assignees?: {
    login: string,
    avatar_url: string,
  }[];
  pull_request?: {
    url: string,
  };
  branch?: GitHubBranch;
};

type GitHubIssuesInput = {
  state?: "open" | "closed" | "all",
  repo?: string,
  labels?: string,
  sort?: "created" | "updated" | "comments",
};

type GitHubIssuesResponse = {
  status: "success" | "error";
  data: {
    data: GitHubIssue[];
    total_count: number;
  };
};

type ItemCardProps = {
  issue: GitHubIssue;
};

const { isOpen, onOpen, onClose } = createDisclosure();
const [activeIssue, setActiveIssue] = createSignal<GitHubIssue | null>(null);

const GitHubLabelsView: Component<{ labels: GitHubLabel[]; }> = (props) => {
  const [labels, setLabels] = createSignal<GitHubLabel[]>([]);

  onMount(() => {
    props.labels.forEach(label => {
      setLabels([...labels(), ...label.name.split(",").map(x => ({ name: x, color: label.color, description: label.description }))]);
    });
  });

  return (
    <HStack spacing={"$1"}>
      <For each={labels()}>
        {(label) =>
          <Tag
            variant="solid"
            size="sm"
            title={label.description}
            background={`#${label.color}3d`}
            colorScheme="neutral"
            color={`#${label.color}`}
          >
            {label.name}
          </Tag>}
      </For>
    </HStack>
  );
};

const ItemCard: Component<ItemCardProps> = (props) => {
  const [versionText, setVersionText] = createSignal<string>("");

  onMount(() => {
    // Extract version number from issue title (example text: "version 1.0.0")
    let version = props.issue.milestone?.title?.toLowerCase().match(/version\s(\d+\.\d+\.\d+)/i);
    if (version) {
      setVersionText(version[1]);
    }
  });

  const onClick = () => {
    setActiveIssue(props.issue);
    onOpen();
  };

  return (
    <Box
      background={"$primaryDarkAlpha"}
      maxH={150}
      padding={"$2"}
      borderRadius={5}
      border={"1px solid $neutral8"}
      w="100%"
      _hover={{ background: "$primaryDark2", cursor: "pointer" }}
      onClick={onClick}
    >
      <VStack spacing={"$2"} alignItems={"flex-start"}>
        <HStack>
          <Box fontSize={"$sm"} color="$neutral10">pianorhythm #{props.issue.number}</Box>
        </HStack>

        {/* Set title with ellipsis if too wide */}
        <Box
          overflow={"hidden"}
          style={{ "white-space": "nowrap", "text-overflow": "ellipsis" }}
          w={"100%"}
          title={props.issue.title}
          _hover={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={onClick}
        >
          {props.issue.title}
        </Box>

        <HStack spacing={"$1"} overflowX={"hidden"}>
          {/* Check if issue has milestone */}
          <Show when={props.issue.milestone}>
            <Tag
              variant="solid"
              size="sm"
              background={versionText() ? stringToColor(versionText()) + "3d" : "$neutral10"}
              color={versionText() ? stringToColor(versionText()) : "$neutral12"}
              title={props.issue.milestone!.title}
            >{versionText() ? versionText() : (props.issue.milestone!.title.toLowerCase())}</Tag>
          </Show>
        </HStack>

        <GitHubLabelsView labels={props.issue.labels} />
      </VStack>
    </Box>
  );
};

const perPage = 10;

const getGitHubIssues = async (page: number, state: string, labels: string, additional?: any) => {
  // const response = await axios.post<GitHubIssuesResponse>(`/api/v1/github/issues`,
  //   { state, labels, page, per_page: perPage, ...additional },
  //   { baseURL: COMMON.EXPRESS_API_HOST });

  const response = await _getGitHubIssues({
    state,
    labels,
    page,
    per_page: perPage,
    ...additional,
  });

  return { data: response };
};

const fetchOpenIssues = async (page: number) => getGitHubIssues(page, "open", "bug,enhancement,feature");

const fetchInProgressIssues = async () => getGitHubIssues(1, "open", "bug,enhancement,feature", { per_page: 100, in_progress: true });

const fetchClosedIssues = async (page: number) => getGitHubIssues(page, "closed", "bug,enhancement,feature");

type ContainerProps = {
  title: string,
  handlePage?: (newPage: number) => {};
  totalCount?: Accessor<number>;
};

const Container: ParentComponent<ContainerProps> = (props) => {
  const [currentPage, setCurrentPage] = createSignal(1);
  const [totalPages, setTotalPages] = createSignal(0);

  createEffect(() => {
    if (props.totalCount) {
      setTotalPages(Math.ceil(props.totalCount() / perPage));
    }
  });

  const handlePage = (newPage: number) => {
    if (newPage < 1) return;
    setCurrentPage(newPage);
    props.handlePage?.(newPage);
  };

  return (
    <VStack spacing={"$1"}>
      <Box fontSize={"$lg"} color="$neutral12">{props.title}</Box>
      <VStack
        spacing={"$1"}
        background={"$primaryDark1"}
        padding={"$2"}
        borderRadius={5}
        border={"1px solid $neutral10"}
        overflowY={"scroll"}
        h={"calc(100vh - 100px)"}
        w={250}
      >
        {props.children}
      </VStack>

      {/* Pagination */}
      {/* {props.totalCount && <Pagination
        minWidth={"50%"}
        currentPage={currentPage}
        totalPages={totalPages}
        handlePage={handlePage}
      />
      } */}
    </VStack>
  );
};

const GitHubIssuesView = () => {
  const [openIssuesPageNumber, setOpenIssuesPageNumber] = createSignal(1);
  const [closedIssuesPageNumber, setClosedIssuesPageNumber] = createSignal(1);
  const [totalOpenIssues, setTotalOpenIssues] = createSignal(0);
  const [totalClosedIssues, setTotalClosedIssues] = createSignal(0);

  const [openIssuesData] = createResource(openIssuesPageNumber, fetchOpenIssues);
  const [inProgressIssuesData] = createResource(fetchInProgressIssues);
  const [closedIssuesData] = createResource(closedIssuesPageNumber, fetchClosedIssues);
  const [readyIssues, setReadyIssues] = createSignal<GitHubIssue[]>([]);
  const [inProgressIssues, setInProgressIssues] = createSignal<GitHubIssue[]>([]);
  const [doneIssues, setDoneIssues] = createSignal<GitHubIssue[]>([]);

  createEffect(() => {
    if (openIssuesData.loading) return;
    let data = openIssuesData()?.data.data!;
    setTotalOpenIssues(openIssuesData()?.data.total_count!);
    setReadyIssues(data.filter(x => x.branch == null && x.pull_request == null));
  });

  createEffect(() => {
    if (inProgressIssuesData.loading) return;
    let data = inProgressIssuesData()?.data.data!;
    setInProgressIssues(data.filter(x => x.branch != null || x.pull_request != null));
  });

  createEffect(() => {
    if (closedIssuesData.loading) return;
    let data = closedIssuesData()?.data.data!;
    setTotalClosedIssues(closedIssuesData()?.data.total_count!);
    setDoneIssues(data.filter(x => x.pull_request == null));
  });

  return (<>
    <HStack
      spacing={"$2"}
      overflow={"scroll"}
      alignItems={"flex-start"}
      h="100%"
      w="100%"
    >
      <Show when={!openIssuesData.loading || !closedIssuesData.loading}
        fallback={<Skeleton h={"calc(100vh - 100px)"} width="100vh" />}
      >
        <Container title="Ready" handlePage={setOpenIssuesPageNumber} totalCount={totalOpenIssues}>
          {/* Show loading message if data is still loading */}
          <Show when={openIssuesData.loading}>
            <Center w="100%" h="100%">Loading...</Center>
          </Show>
          {!openIssuesData.loading && <For each={readyIssues()}>
            {issue => (<ItemCard issue={issue} />)}
          </For>}
        </Container>
        <Container title="In Progress">
          <Show when={inProgressIssuesData.loading}>
            <Center w="100%" h="100%">Loading...</Center>
          </Show>
          {!inProgressIssuesData.loading && <For each={inProgressIssues()}>
            {issue => (<ItemCard issue={issue} />)}
          </For>
          }
        </Container>
        <Container title="Completed" handlePage={setClosedIssuesPageNumber} totalCount={totalClosedIssues}>
          <Show when={closedIssuesData.loading}>
            <Center w="100%" h="100%">Loading...</Center>
          </Show>
          {!closedIssuesData.loading && <For each={doneIssues()}>
            {issue => (<ItemCard issue={issue} />)}
          </For>
          }
        </Container>
      </Show>
    </HStack>
    <Box as="i" bottom={-10} position={"absolute"} fontSize={"$sm"} color="lightgray">Shows the {perPage} most recent issues/features...</Box>

    <Drawer
      opened={isOpen() && activeIssue() !== null}
      placement="right"
      onClose={() => {
        onClose();
        setActiveIssue(null);
      }}
      size={"lg"}
    >
      <DrawerOverlay zIndex={"var(--hope-zIndices-modal)"} />
      <DrawerContent>
        <DrawerCloseButton />
        <DrawerBody w="100%">
          <VStack alignItems={"flex-start"} spacing={"$3"} w="100%">
            <HStack spacing={"$2"}>
              <Box as="h2">{activeIssue()?.title}</Box>
              <Box as="h2" color={"$neutral9"}>#{activeIssue()?.number}</Box>
            </HStack>
            <Box
              background={activeIssue()?.state.toLowerCase() === "open" ? "$success10" : "purple"}
              borderRadius={100}
              padding={3}
              paddingRight={8}
              paddingLeft={5}
              fontSize={"$sm"}
            >
              <HStack spacing={"$1"}>
                <Show
                  when={activeIssue()?.state.toLowerCase() == "open"}
                  fallback={<FaSolidCircleCheck />}
                >
                  <FaSolidCircleDot />
                </Show>
                {capitalizeFirstLetter(activeIssue()?.state)}
              </HStack>
            </Box>
            <Divider />

            <VStack
              borderRadius={5}
              w="100%"
              background={"$primaryDark1"}
              alignItems={"flex-start"}
              marginTop={10}
            >
              <Show when={activeIssue()?.assignees && activeIssue()!.assignees!.length > 0}>
                <HStack
                  spacing={"$1"}
                  alignItems={"flex-start"}
                  color={"$neutral10"}
                  border={"1px solid $neutral10"}
                  borderTopRadius={5}
                  padding={10}
                  w="100%"
                >
                  <Box
                    fontWeight={"bold"}
                    color="$neutral12">
                    {activeIssue()?.assignees?.[0].login ?? "None"}
                  </Box>
                  <Box>{"opened"}</Box>
                  <Box color={"$neutral11"}>{(new Date(activeIssue()?.createdAt!)).toLocaleString()}</Box>
                </HStack>
              </Show>

              {/* Issue Body */}
              <Box
                padding={10}
                border={"1px solid $neutral10"}
                borderBottomRadius={5}
                h={300}
                w="100%"
                overflowY={"scroll"}
              >
                <SolidMarkDownText
                  text={activeIssue()?.body || "<text color='gray'>_No description available..._</text>"}
                  allowImages
                />
              </Box>
            </VStack>

            <Divider marginTop={10} />
            {activeIssue()?.labels && <VStack alignItems={"flex-start"} spacing={"$3"}>
              <Box color={"$neutral11"}>Labels</Box>
              <GitHubLabelsView labels={activeIssue()?.labels!} />
            </VStack>}

            <Divider marginTop={10} />
            <VStack alignItems={"flex-start"} spacing={"$5"}>
              {activeIssue()?.closedAt &&
                <Box>
                  <Box color={"$neutral11"}>Closed</Box>
                  <Box>{(new Date(activeIssue()?.closedAt!)).toLocaleString()}</Box>
                </Box>
              }
              {activeIssue()?.assignees && <Box>
                <Box color={"$neutral11"}>Assignee</Box>
                <HStack spacing={"$2"}>
                  <Avatar size={"sm"} src={activeIssue()?.assignees?.[0].avatar_url} ></Avatar>
                  <Box>{activeIssue()?.assignees?.[0].login ?? "None"}</Box>
                </HStack>
              </Box>
              }
              {activeIssue()?.milestone && <Box>
                <Box color={"$neutral11"}>Milestone</Box>
                <Box>{activeIssue()?.milestone?.title}</Box>
              </Box>
              }
              {activeIssue()?.branch &&
                <Box>
                  <Box color={"$neutral11"}>Branch</Box>
                  <Box>{activeIssue()?.branch?.name}</Box>
                </Box>
              }
            </VStack>
          </VStack>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  </>);
};

export default GitHubIssuesView;