import {Box} from "@hope-ui/solid";
import {isEmpty, partition} from "lodash-es";
import orderBy from "lodash-es/orderBy";
import uniqBy from "lodash-es/uniqBy";
import {Component, createEffect, createMemo, createSignal, For, lazy, onMount, Suspense} from "solid-js";
import {useService} from "solid-services";
import {ClientSideUserDto, Roles} from "~/proto/user-renditions";
import AppService from "~/services/app.service";
import UsersService from "~/services/users-service";
import {RolesHelper, UserDtoUtil} from "~/types/user-helper";
import {mapRoleNameToHeader} from "~/types/user.types";
import {VirtualListGroupHeader} from "./common";
import {ApiUserRecordProvider, UserRecordProvider} from "~/contexts/user.context";
import {MultiProvider} from "@solid-primitives/context";

const SidebarUserItem = lazy(() => import("./sidebar.user-item"));

const SidebarUsersList: Component = () => {
    const usersService = useService(UsersService);
    const appService = useService(AppService);

    const [owners, setOwners] = createSignal<string[]>([]);
    const [developers, setDevelopers] = createSignal<string[]>([]);
    const [moderators, setModerators] = createSignal<string[]>([]);
    const [trialModerators, setTrialModerators] = createSignal<string[]>([]);
    const [roomOwners, setRoomOwners] = createSignal<string[]>([]);
    const [members, setMembers] = createSignal<string[]>([]);
    const [guests, setGuests] = createSignal<string[]>([]);
    const [bots, setBots] = createSignal<string[]>([]);
    const isClient = appService().isClient;

    createEffect(() => {
        let input = uniqBy(usersService().getUsers(), "socketID");

        let [_bots, input0] = partition(input, x => UserDtoUtil.isBot(x.userDto!.roles));
        let [_owners, input2] = partition(input0, x => UserDtoUtil.isAdminExactly(x.userDto!.roles));
        let [_devs, input3] = partition(input2, x => UserDtoUtil.isDeveloperExactly(x.userDto!.roles));
        let [_mods, input4] = partition(input3, x => UserDtoUtil.isModExactly(x.userDto!.roles));
        let [_trialmods, input41] = partition(input4, x => UserDtoUtil.isTrialModExactly(x.userDto!.roles));
        let [_rmOwners, input5] = partition(input41, x => UserDtoUtil.isRoomOwner(x.userDto!.roles) || x.socketID == appService().roomOwner());
        let [_members, input6] = partition(input5, x => UserDtoUtil.isMember(x.userDto!.roles));
        let [_guests] = partition(input6, x => UserDtoUtil.isGuest(x.userDto!.roles));

        setBots(sortUsers(_bots, isClient).map(x => x.socketID));
        setOwners(sortUsers(_owners, isClient).map(x => x.socketID));
        setDevelopers(sortUsers(_devs, isClient).map(x => x.socketID));
        setModerators(sortUsers(_mods, isClient).map(x => x.socketID));
        setTrialModerators(sortUsers(_trialmods, isClient).map(x => x.socketID));
        setRoomOwners(sortUsers(_rmOwners, isClient).map(x => x.socketID));
        setMembers(sortUsers(_members, isClient).map(x => x.socketID));
        setGuests(sortUsers(_guests, isClient).map(x => x.socketID));
    });

    return (
        <>
            {!isEmpty(owners()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.ADMIN)} items={owners()}
            />}
            {!isEmpty(developers()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.DEVELOPER)} items={developers()}
            />}
            {!isEmpty(moderators()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.MODERATOR)} items={moderators()}
            />}
            {!isEmpty(trialModerators()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.TRIAL_MODERATOR)} items={trialModerators()}
            />}
            {!isEmpty(roomOwners()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.ROOMOWNER)} items={roomOwners()}
            />}
            {!isEmpty(bots()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.BOT)} items={bots()}
            />}
            {!isEmpty(members()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.MEMBER)} items={members()}
            />}
            {!isEmpty(guests()) && <VirtualUsersList
                header={mapRoleNameToHeader(Roles.GUEST)} items={guests()}
            />}
        </>
    );
};

const VirtualUsersList: Component<{ header: string, items: string[]; }> = (props) => {
    const headerLabel = createMemo(() =>
        `${props.header}${props.items.length > 1 ? `s - ${props.items.length}` : ''}`
    );

    return (<>
        <VirtualListGroupHeader
            color="$neutral12"
            marginBoth={5}
            label={headerLabel()}
        />

        <For each={props.items} fallback={<Box>Loading...</Box>}>
            {(socketID) =>
                <Suspense fallback={<Box>Loading...</Box>}>
                    <MultiProvider
                        values={[
                            [UserRecordProvider, socketID],
                            [ApiUserRecordProvider, {socketID}],
                        ]}
                    >
                        <SidebarUserItem
                            style={{"height": "50px", "overflow": "hidden"}}
                            tabIndex={-1}
                        />
                    </MultiProvider>
                </Suspense>
            }
        </For>
    </>);
};

const sortUsers = (users: ClientSideUserDto[], isClient: (socketID: string) => boolean) => orderBy(users,
    (o) => [
        isClient(o.socketID),
        RolesHelper.getHighestRole(o.userDto!.roles)?.rank,
        o.userDto!.isProMember,
        o.userDto!.meta?.enteredRoomDateTime ? Date.parse(o.userDto!.meta?.enteredRoomDateTime) || Date.now() : Date.now(),
    ], ["desc", "desc", "desc", "desc"]
);

export default SidebarUsersList;