import { Box, Center, VStack, Spinner } from "@hope-ui/solid";
import MotionFadeIn from "./motion/motion.fade-in";

export default function ComponentLoader() {
  return (
    <MotionFadeIn duration={0.25}>
      <Center
        position="absolute"
        top={0}
        w="100vw"
        h="100dvh"
        zIndex={999999}
        background="$primaryDarkAlpha2"
        opacity={0.9}
      >
        <VStack spacing="$2">
          <Spinner thickness={"5px"} color={"$neutral12"} />
          <Box>Loading Component...</Box>
        </VStack>
      </Center>
    </MotionFadeIn>
  );
}