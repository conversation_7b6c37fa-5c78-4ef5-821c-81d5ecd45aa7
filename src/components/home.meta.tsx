import { createEffect, createResource, createSignal, Match, onCleanup, onMount, Switch } from "solid-js";
import { useService } from "solid-services";
import style from "~/sass/login.page.module.sass";
import I18nService from "~/services/i18n.service";
import { VStack, Text, Box } from "@hope-ui/solid";
import { COMMON } from "~/util/const.common";
import { getPlayersOnline } from "~/server/general.api";
import { Motion } from "solid-motionone";

const HomeMeta = () => {
  const i18nService = useService(I18nService);
  const [playerCount, { refetch }] = createResource(-1, getPlayersOnline);
  const [fetchTimeout, setFetchTimeout] = createSignal(-1);
  const [playerCountText, setPlayerCountText] = createSignal("");

  onMount(() => {
    setFetchTimeout(window.setInterval(refetch, 10_000));
  });

  onCleanup(() => {
    window.clearInterval(fetchTimeout());
  });

  createEffect(() => {
    setPlayerCountText(`${i18nService().t_loginPage("playersOnline")}: ${playerCount() ?? -1}`);
  });

  return (<>
    <Motion
      animate={{ opacity: [0, 1] }}
      transition={{ duration: 1.5, easing: "ease-in-out" }}
    >
      <VStack class={style.meta}>
        <Text>{playerCountText()}</Text>
        <Text>
          <Switch fallback={"(web) "}>
            <Match when={COMMON.IS_DESKTOP_APP}>{"(desktop) "}</Match>
          </Switch>
          <span>Client Version: {COMMON.CLIENT_VERSION}</span>
          <Box as="span"> (BETA)</Box>
        </Text>
        <Text>Build Date: {COMMON.CLIENT_BUILD_DATE || import.meta.env.VITE_BUILD_DATE}</Text>
        <Text>&copy; {new Date().getFullYear()} PianoRhythm</Text>

        {COMMON.IS_STAGING && <Text color={"yellow"}>Staging Environment</Text>}
        {COMMON.IS_DEV_MODE && <Text>Development Build | Mode: {COMMON.MODE}</Text>}
      </VStack>
    </Motion>
  </>);
};

export default HomeMeta;