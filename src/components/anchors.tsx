import I18nService from "~/services/i18n.service";
import { CurrentForm } from "~/services/login.service";
import LoginService from "~/services/login.service";
import { useNavigate } from "@solidjs/router";
import { Component, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import style from "~/sass/login.page.module.sass";
import { Anchor } from "@hope-ui/solid";
import { buttonSFX } from "~/directives/buttonsfx.directive";
import isString from "lodash-es/isString";

export const GoHome_Anchor = () => {
  const navigate = useNavigate();
  const i18nService = useService(I18nService);

  return (
    <Anchor
      color={"gray"}
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => navigate("/", { replace: true })}
      class={style.navText}>{i18nService().t_loginPage("hrefs.goHome")}</Anchor>
  );
};

export const EnterAsGuest_Anchor = () => {
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => loginService().updateForm(CurrentForm.Main)}
      class={style.navText}>{i18nService().t_loginPage("hrefs.enterAsGuest")}</Anchor>
  </>);
};

export const ForgotPassword_Anchor = () => {
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => loginService().updateForm(CurrentForm.ForgotPassword)}
      class={style.navText}>{i18nService().t_loginPage("hrefs.forgotPassword")}</Anchor>
  </>);
};

export const ResendEmailVerification_Anchor = () => {
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => loginService().updateForm(CurrentForm.ResendVerificationEmail)}
      class={style.navText}>{i18nService().t_loginPage("hrefs.resendVerificationEmail")}</Anchor>
  </>);
};

export const CreateAccount_Anchor = () => {
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => loginService().updateForm(CurrentForm.Register)}
      class={style.navText}>
      {i18nService().t_loginPage("hrefs.createAccount")}
    </Anchor>
  </>);
};

export const AlreadyHaveAccount_Anchor = () => {
  const i18nService = useService(I18nService);
  const loginService = useService(LoginService);

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => loginService().updateForm(CurrentForm.Login)}
      class={style.navText}>
      {i18nService().t_loginPage("hrefs.haveAccount")}
      {i18nService().t_loginPage("hrefs.signIn")}
    </Anchor>
  </>);
};

export const ToLoginForm_Anchor: Component<{ targetForm?: string; }> = (props) => {
  const i18nService = useService(I18nService);
  const navigate = useNavigate();
  const [target, setTarget] = createSignal("/login");

  onMount(() => {
    if (isString(props.targetForm)) {
      switch (props.targetForm.toLowerCase()) {
        case 'login': setTarget(v => `${v}?form=login`); break;
        case 'register': setTarget(v => `${v}?form=register`); break;
      }
    }
  });

  return (<>
    <Anchor
      ref={(el: HTMLElement) => buttonSFX(el)}
      onclick={() => navigate(target(), { replace: true })}
      class={style.navText}>{i18nService().t_loginPage("hrefs.signIn")}</Anchor>
  </>);
};