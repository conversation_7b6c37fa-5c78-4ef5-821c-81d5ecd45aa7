import { Box, Center, Image, Skeleton } from "@hope-ui/solid";
import { Component, Show, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import ResourceService from "~/services/resource.service";
import { UserDtoUtil } from "~/types/user-helper";
import { TransparentPixel } from "~/util/const.common";

type BackgroundImageProps = {
  profileBackgroundImageLastModified?: string | Date,
  usertag: string,
  transform?: string,
  filter?: string,
  height?: number | string,
  width?: number | string,
  className?: string,
  onClick?: () => void;
};

const UserProfileBackgroundImage: Component<BackgroundImageProps> = (props) => {
  const [imageSource, setImageSource] = createSignal<string | undefined>(undefined);
  const resourceService = useService(ResourceService);

  onMount(async () => {
    if (props.profileBackgroundImageLastModified) {
      let resource = await resourceService().getServerImage(UserDtoUtil.getUserProfileBackgroundImage(props.usertag, props.profileBackgroundImageLastModified));
      if (resource) setImageSource(resource);
    }
  });

  return (<>
    <Box
      className={props.className}
      transform={props.transform}
      w={props.width}
      h={props.height}
      onmousedown={() => { if (props.onClick) props.onClick(); }}
      overflow="hidden"
      borderTopRadius={5}
    >
      <Show when={imageSource()}
        fallback={<Skeleton w="100%" h="100%" speed={"unset"} />}>
        <Center h="100%">
          <Image
            w="100%"
            loading="lazy"
            borderRadius={5}
            style={{ "filter": props.filter }}
            background="$primary1"
            objectFit={"cover"}
            src={imageSource()}
            alt={`${props.usertag}'s profile background image`}
            onError={(img) => {
              img.currentTarget.src = TransparentPixel;
            }}
          />
        </Center>
      </Show>
    </Box>
  </>);
};

export default UserProfileBackgroundImage;