import { Box } from "@hope-ui/solid";
import clsx from "clsx";
import { clamp, findLastIndex, sortBy } from "lodash-es";
import Mousetra<PERSON> from "mousetrap";
import { Accessor, For, JSXElement, createEffect, createSignal, onCleanup, onMount } from "solid-js";
import { useService } from "solid-services";
import { ListIteratee, Many } from "~/global";
import css from "~/sass/chat-bar-trigger-list.module.sass";
import ChatService from "~/services/chat.service";

const DefaultActiveSelectedIndex = 0;

/**
 * Props for the ChatBarTriggerList component that handles trigger-based autocomplete functionality
 * @template T The type of items in the data array
 */
type ChatBarTriggerListProps<T> = {
  /** Array of items to display in the trigger list */
  data: T[];

  /** Reference to the input element that triggers the list */
  inputElement: Accessor<HTMLInputElement>;

  /** Character that triggers the list to appear (e.g. '@' for mentions) */
  triggerChar: string;

  /** Optional header text to display above the list */
  header?: string;

  /** Optional array of iteratees to sort the list items */
  sortBy?: Array<Many<ListIteratee<T>>>;

  /** Function to render each item in the list
   * @param item The item to render
   * @returns JSX element representing the item
   */
  onRender(item: T): JSXElement;

  /** Function to get the text to insert when an item is highlighted
   * @param item The highlighted item
   * @returns String to insert into the input
   */
  onHighlightedItem(item: T): string;

  /** Whether to remove the trigger character after item selection. Defaults to false if not specified */
  removeTriggerOnSelected?: boolean;

  /** Function to determine if an item matches the input text
   * @param text The current input text after the trigger char
   * @param item The item to check
   * @returns True if the item matches the input
   */
  onInputTriggerMatch(text: string | undefined, item: T): boolean;

  /** Function to handle item selection
   * @param item The selected item
   */
  onSelectItem?(item: T): void;
};

type TriggerListItemProps<T> = {
  item: T;
  focused: boolean;
  onRender(item: T): JSXElement;
  onClick(item: T): void;
};

const TriggerListItem = <T,>(props: TriggerListItemProps<T>) => {
  let element!: HTMLDivElement;

  createEffect(() => {
    if (props.focused) {
      element.scrollIntoView({
        behavior: "auto",
        block: "nearest",
      });
    }
  });

  return (
    <Box
      ref={element}
      onMouseDown={() => props.onClick(props.item)}
      className={clsx([
        css.itemRow,
        props.focused && css.itemRowIsActive
      ])}
    >{props.onRender(props.item)}</Box>
  );
};

type InputTriggerText = {
  text: string;
  index: number;
};

const ChatBarTriggerList = <T,>(props: ChatBarTriggerListProps<T>) => {
  const chatService = useService(ChatService);
  const [activeSelectedIndex, setActiveSelectedIndex] = createSignal(DefaultActiveSelectedIndex);
  const [inputTriggerText, setInputTriggerText] = createSignal<InputTriggerText>();
  const [activeData, setActiveData] = createSignal<T[]>([]);
  const [showList, setShowList] = createSignal(false);
  let commandsContainerScrollingElement!: HTMLDivElement;

  onMount(() => {
    let parent = chatService().chatBarElement();
    if (!parent) return;

    let ms = new Mousetrap(parent);
    const onClamp = (v: number) => clamp(v, DefaultActiveSelectedIndex, props.data.length - 1);
    ms.bind("up", () => setActiveSelectedIndex(v => onClamp(v - 1)));
    ms.bind("down", () => setActiveSelectedIndex(v => onClamp(v + 1)));
    ms.bind("escape", () => setShow(false));

    let ms2 = new Mousetrap(document.body);
    ms2.bind("escape", () => setShow(false));
    onCleanup(() => { ms.reset(); ms2.reset(); });
  });

  const onInputGetTrigger = (ev: Event) => {

    let target = ev.target as HTMLInputElement;
    let caretPos = target.selectionStart || 0;
    let text = target.value;

    let partitionedText = text.split(" ");
    let textBeforeCaret = text.slice(0, caretPos);
    let partitionedTextFromCaret = textBeforeCaret.split(" ");

    let potentialTriggerIndex = findLastIndex(partitionedTextFromCaret, x => x.toLowerCase().indexOf(props.triggerChar.toLowerCase()) == 0);
    let triggerFound = potentialTriggerIndex == partitionedTextFromCaret.length - 1;

    setShow(triggerFound);

    // To switch between getting the whole word or just a partial,
    // use `partitionedText` for whole or `partitionedTextFromCaret` for partial
    setInputTriggerText(triggerFound ? {
      text: partitionedText[potentialTriggerIndex]!,
      index: potentialTriggerIndex
    } : undefined);
  };

  const onSelectItem = (item: T) => {
    if (!item) return;

    let inputTrigger = inputTriggerText();
    setShow(true);

    if (inputTrigger) {
      let inputElement = props.inputElement();
      let partitionedText = inputElement.value.split(" ");
      partitionedText[inputTrigger.index] = `${props.removeTriggerOnSelected ? "" : props.triggerChar}${props.onHighlightedItem(item)} `;
      chatService().setChatBarValue(partitionedText.join(" "));
      inputElement.focus();
    }

    setShow(false);
  };

  const onInputBlur = (evt: FocusEvent) => {
    if (show()) evt.preventDefault();
  };

  const onKeyDown = (ev: KeyboardEvent) => {
    let key = ev.key.toLowerCase();
    if (show() && (key == "arrowup" || key == "arrowdown" || key == "tab" || key == "enter")) ev.preventDefault();

    if (key == "tab" || key == "enter") {
      if (activeData().length > 0) {
        let item = activeData()[activeSelectedIndex()];
        if (!item) return;

        onSelectItem(item);
        props.onSelectItem?.(item);

      } else {
        setShow(false);
      }
    }
  };

  createEffect(() => {
    let inputElement = props.inputElement();
    inputElement.addEventListener("keydown", onKeyDown);
    inputElement.addEventListener("keyup", onInputGetTrigger);
    inputElement.addEventListener("focus", onInputGetTrigger);
    inputElement.addEventListener("mouseup", onInputGetTrigger);
    inputElement.addEventListener("blur", onInputBlur);

    onCleanup(() => {
      inputElement.removeEventListener("keydown", onKeyDown);
      inputElement.removeEventListener("keyup", onInputGetTrigger);
      inputElement.removeEventListener("focus", onInputGetTrigger);
      inputElement.removeEventListener("mouseup", onInputGetTrigger);
      inputElement.removeEventListener("blur", onInputBlur);
    });
  });

  createEffect(() => {
    let data = props.data;
    if (props.sortBy != null) data = sortBy(data, ...props.sortBy);
    let filtered = data.filter(x => props.onInputTriggerMatch(inputTriggerText()?.text, x));
    setActiveData(filtered);
    if (filtered.length == 0) setShow(false);
  });

  function show() {
    return showList() || chatService().anyListCommandsDisplayed();
  }

  function setShow(value: boolean) {
    chatService().setAnyListCommandsDisplayed(value);
    setShowList(value);

    if (!value) {
      setActiveSelectedIndex(DefaultActiveSelectedIndex);
    }
  }

  return (<>
    {show() && <Box className={css.container}>
      <Box className={css.containerHeader}>
        {props.header ?? "Data"}{inputTriggerText() ? ` MATCHING: ${inputTriggerText()?.text}` : ``}
      </Box>

      <Box className={css.itemsContainer} ref={commandsContainerScrollingElement}>
        <For each={activeData()}>
          {(item, idx) =>
            <TriggerListItem
              item={item}
              onClick={onSelectItem}
              onRender={props.onRender}
              focused={activeSelectedIndex() == idx()}
            />}
        </For>
      </Box>
    </Box>
    }
  </>);
};

//Note: Note used for anything but this is a thing that works!
ChatBarTriggerList.TriggerListItem = TriggerListItem;

export default ChatBarTriggerList;