import { <PERSON><PERSON>, Badge, Box, Center, HStack, VStack } from "@hope-ui/solid";
import { clamp, isEmpty, isEqual, sortBy, uniqBy } from "lodash-es";
import Mousetrap from "mousetrap";
import { Component, createEffect, createSignal, For, onCleanup, onMount, Show } from "solid-js";
import { useService } from "solid-services";
import css from "~/sass/commands.module.sass";
import { Command, CommandArgument, CommandModule } from "~/types/chat.types";

import AppService from "~/services/app.service";
import ChatService from "~/services/chat.service";
import WebsocketService from "~/services/websocket.service";
import clsx from "clsx";

type CommandsContainerProp = {
  commands: Command[];
  modules: CommandModule[];
  input?: string;
  trigger: string;
  className?: string;
  onSubmit?: () => void;
};

const CommandModuleComponent: Component<{ module: CommandModule, isActive: boolean, onClick?: () => void; }> = (props) => {
  return (<>
    <Box
      className={css.commandModuleContainer}
      background={props.isActive ? "$primaryDark1" : ""}
      onmousedown={(evt: MouseEvent) => {
        if (evt.button == 0) {
          if (props.onClick) props.onClick();
        }
      }}
    >
      <Center>
        <Avatar
          __tooltip_title={`${props.module.name} ${props.module.shortDescription || ""}`.trim()}
          __tooltip_placement="right"
          size="sm"
          name={props.module.name}
          className={css.moduleAvatar}
          src={props.module.imagePath}
          onmousedown={() => { if (props.onClick) props.onClick(); }}
        />
      </Center>
    </Box>
  </>);
};

const CommandArgumentComponent: Component<{ argument: CommandArgument; }> = (props) => {
  return (<>
    <Badge background={"$accent1"} variant="outline" fontSize={10}>
      {props.argument.name}
    </Badge>
  </>);
};

const CommandComponent: Component<{ onClick?: () => void, focused?: boolean, command: Command, trigger: string; }> = (props) => {
  const [showArgs, setShowArgs] = createSignal(false);
  const [requiredArgs, setRequiredArgs] = createSignal<CommandArgument[]>([], { equals: isEqual });
  const [optionalArgs, setOptionalArgs] = createSignal<CommandArgument[]>([], { equals: isEqual });
  let element!: HTMLDivElement;
  let command = props.command;

  onMount(() => {
    let source = command.arguments || [];
    let required = source.filter(x => !x.optional);
    let optional = source.filter(x => x.optional);

    setRequiredArgs(required);
    setOptionalArgs(optional);
  });

  createEffect(() => {
    if (props.focused) {
      element.scrollIntoView({
        behavior: "auto",
        block: "nearest"
      });
    }
  });

  return (<>
    <Box
      className={clsx([
        css.commandContainer,
        command.disabled && "disabled"
      ])}
      onmouseenter={() => setShowArgs(true)}
      onmouseleave={() => setShowArgs(false)}
      onmousedown={() => { if (props.onClick && !command.disabled) props.onClick(); }}
      background={props.focused ? "$primary1" : ""}
      ref={element}
    >
      <VStack alignItems={"flex-start"} h="100%" position={"relative"}>
        {command.modOnly &&
          <Box
            position={"absolute"}
            top={0}
            right={0}
          >
            <Badge
              __tooltip_title="This command is only for Moderators"
              background={"$accent1"} variant="outline" fontSize={10}> Mod </Badge>
          </Box>}

        <HStack spacing={"$3"} h="100%">
          {/* Command Name */}
          <Box className={css.commandName}>{props.trigger}{command.command.toLowerCase()}</Box>

          <Show when={showArgs() || props.focused}>
            {/* Required Arguments - Tags */}
            <HStack spacing={"$1"}>
              <For each={requiredArgs()}>
                {(arg) => <CommandArgumentComponent argument={arg} />}
              </For>
            </HStack>

            {/* Optional Arguments */}
            {
              (optionalArgs().length > 0) &&
              <Box
                __tooltip_title={
                  <VStack spacing={"$1"}>
                    <For each={optionalArgs()}>
                      {(arg) => <Box>{arg.name} ({arg.type})</Box>}
                    </For>
                  </VStack>
                }
                color="gray">| +{optionalArgs().length} optional
              </Box>
            }
          </Show>
        </HStack>
        {command.shortDescription && <Box className={css.commandShortDesc}>{command.shortDescription} </Box>}
        {command.moduleID && <Box color="$neutral10" fontSize={10}>Module: {command.moduleID} </Box>}
      </VStack>
    </Box>
  </>);
};

const DefaultActiveSelectedIndex = 0;
const DefaultActiveSelectedModuleIndex = 0;

type ChatMode = "ShowAll" | "CommandMatch" | "CommandMode";
const CommandsContainer: Component<CommandsContainerProp> = (props) => {
  const [mode, setMode] = createSignal<ChatMode>("ShowAll");
  const [commandModules, setCommandModules] = createSignal<CommandModule[]>([], { equals: isEqual });
  const [allCommands, setAllCommands] = createSignal<Command[]>([], { equals: isEqual });
  const [selectedCommand, setSelectedCommand] = createSignal<Command>();
  const [activeCommands, setActiveCommands] = createSignal<Command[]>([], { equals: isEqual });
  const [activeModuleID, setActiveModuleID] = createSignal<string>();
  const [actualInput, setActualInput] = createSignal<string>();
  const [activeSelectedIndex, setActiveSelectedIndex] = createSignal(DefaultActiveSelectedIndex);
  const [activeSelectedModIndex, setActiveSelectedModIndex] = createSignal(DefaultActiveSelectedModuleIndex);
  const chatService = useService(ChatService);
  const appService = useService(AppService);
  const websocketService = useService(WebsocketService);
  let commandsContainerScrollingElement!: HTMLDivElement;

  function onSelectedCommand(_selectedCommand: Command) {
    if (_selectedCommand.disabled) return;
    setSelectedCommand(_selectedCommand);
    setMode("CommandMode");
    chatService().chatBarElement()?.focus();
    chatService().setChatBarValue(`${props.trigger}${_selectedCommand.command} `);
  }

  function show() {
    return chatService().chatCommandsDisplayed();
  }

  function setShow(value: boolean) {
    chatService().setChatCommandsDisplayed(value);
  }

  const onRef = () => {
    let parent = chatService().chatBarElement();
    if (!parent) return;

    let ms = new Mousetrap(parent);
    const onCommandIndexClamp = (v: number) => clamp(v, DefaultActiveSelectedIndex, activeCommands().length - 1);
    const onModuleIndexClamp = (v: number) => clamp(v, DefaultActiveSelectedModuleIndex, commandModules().length - 1);

    ms.bind("shift+up", () => {
      setActiveSelectedModIndex(v => onModuleIndexClamp(v - 1));
      setActiveSelectedIndex(DefaultActiveSelectedIndex);
    });

    ms.bind("up", () => {
      setActiveSelectedIndex(v => onCommandIndexClamp(v - 1));
    });

    ms.bind("shift+down", () => {
      setActiveSelectedModIndex(v => onModuleIndexClamp(v + 1));
      setActiveSelectedIndex(DefaultActiveSelectedIndex);
    });

    ms.bind("down", () => {
      setActiveSelectedIndex(v => onCommandIndexClamp(v + 1));
    });

    const onSelected = async () => {
      if (!show()) return;

      if (mode() == "CommandMode") {
        let command = selectedCommand();
        if (command) {
          let input = chatService().chatBarValue();
          chatService().setChatBarValue("");
          setShow(false);
          let args = input?.split(" ").slice(1).filter(Boolean);

          if (command.clientSideOnly || command.modOnly) {
            chatService().runClientChatCommand(command, args);
            return;
          }

          if (command.modOnly) {
            chatService().runServerModChatCommand(command, args);
            return;
          }

          if (command.roomOwnerOnly) {
            chatService().runRoomOwnerChatCommand(command, args);
            return;
          }

          if (command.membersOnly) {
            await chatService().runRoomChatServerCommand(command, args).catch(err => chatService().addMessage(err, true, true));
            return;
          }

          if (chatService().checkIfCommandCanBeRan(command)) {
            websocketService().emitRoomChatServerCommand(input);
          }
        }
        return;
      }

      let _selectedCommand = activeCommands()[activeSelectedIndex()];
      if (!_selectedCommand) return;
      onSelectedCommand(_selectedCommand);
    };

    ms.bind("enter", onSelected);
    ms.bind("tab", onSelected);

    let ms2 = new Mousetrap(document.body);
    ms2.bind("escape", () => setShow(false));

    onCleanup(() => {
      ms.reset();
      ms2.reset();
    });
  };

  const onSetCommandModules = (commands: Command[]): CommandModule[] => {
    let modules = props.modules.filter(mod => commands.some(x => x.moduleID == mod.id));
    let distinctModules = uniqBy(modules, x => x.id);
    setCommandModules(distinctModules);
    return distinctModules;
  };

  onMount(() => {
    // Only show modules that actually have any commands loaded
    let distinctModules = onSetCommandModules(props.commands);
    if (distinctModules.length > 0) setActiveModuleID(distinctModules[0]!.id);
    let sortedCommands = sortBy(props.commands, 'command');
    setAllCommands(sortedCommands);
  });

  createEffect(() => {
    setShow(props.input?.indexOf(props.trigger) == 0 && (activeCommands().length > 0 || mode() == "CommandMode"));
  });

  createEffect(() => {
    let _module = commandModules().find((_, idx) => activeSelectedModIndex() == idx);
    if (_module) setActiveModuleID(_module.id);
  });

  createEffect(() => chatService().setChatCommandsDisplayed(show()));

  createEffect(() => {
    let client = appService().client();

    let currentCommands = props.commands.map(x => {
      let disabled =
        Boolean(
          (x.membersOnly && !client.isMember) ||
          (x.proMemberOnly && !client.isProMember) ||
          (x.modOnly && !client.isMod) ||
          (x.roomOwnerOnly && !appService().isClientRoomOwner()) ||
          x.disabled
        );

      return { ...x, disabled } as Command;
    });

    // Hide modules for moderators if the user is not a moderator
    if (!client.isMod) {
      currentCommands = currentCommands.filter(x => !x.modOnly);
    }

    // Hide modules for members if the user is not a member
    if (!client.isMember) {
      currentCommands = currentCommands.filter(x => !x.membersOnly);
    }

    onSetCommandModules(currentCommands);

    let sortedCommands = sortBy(currentCommands, 'command');
    setAllCommands(sortedCommands);
  });

  /**
   * Effect hook that processes command input and updates command matching state.
   *
   * Functionality:
   * - Sanitizes and parses command input to extract the command name
   * - If input exists:
   *   - Matches partial commands and updates active command list
   *   - For exact matches, sets selected command and command mode
   *   - For multiple matches, shows matching commands list
   *   - Hides if no matches found
   * - If no input:
   *   - Shows all commands for active module
   *
   * Updates states:
   * - activeCommands - filtered list of matching commands
   * - mode - "CommandMatch", "CommandMode", or "ShowAll"
   * - activeSelectedIndex - selected command in list
   * - selectedCommand - exact command match when found
   */
  createEffect(() => {
    let sanitizedInput = (props.input ?? "").toLowerCase();
    let firstSpaceIndex = sanitizedInput.indexOf(" ");
    let actualCommand = sanitizedInput.substring(1, firstSpaceIndex != -1 ? firstSpaceIndex : sanitizedInput.length);
    let commandInput = sanitizedInput.substring(1);
    setActualInput(actualCommand);

    if (!isEmpty(commandInput)) {
      let matchingCommands =
        allCommands()
          .filter(x => commandInput.length > 0 && x.command.toLowerCase().includes(commandInput))
          .sort((a, b) => a.command.toLowerCase().indexOf(commandInput) - b.command.toLowerCase().indexOf(commandInput));

      setActiveCommands(matchingCommands);

      if (matchingCommands.length > 0) {
        setActiveSelectedIndex(DefaultActiveSelectedIndex);
        setMode("CommandMatch");
        if (commandsContainerScrollingElement) commandsContainerScrollingElement.scrollTop = 0;

        // After there's a space pressed
      } else {
        let matchingCommand =
          allCommands()
            .filter(x => actualCommand.length > 0 && x.command.toLowerCase() == actualCommand.toLowerCase());

        if (matchingCommand.length == 1) {
          setSelectedCommand(matchingCommand[0]);
          setMode("CommandMode");
        }
        else if (matchingCommand.length > 1) {
          setActiveCommands(matchingCommand);
          setMode("CommandMatch");
        }
        else {
          setShow(false);
        }
      }

    } else {
      setActiveCommands(allCommands().filter(x => x.moduleID == activeModuleID()));
      setMode("ShowAll");
    }
  });

  createEffect(() => {
    if (!show()) {
      setActiveSelectedIndex(0);
      setActiveSelectedModIndex(0);
      setSelectedCommand(undefined);
      setMode("ShowAll");
    }
  });

  return (<>
    {(show()) &&
      <Box
        ref={onRef}
        id="chat-commands-container"
        className={clsx([props.className, css.container])}>

        <VStack alignItems={"fl"} h="100%">
          {(mode() == "CommandMode" && selectedCommand()) &&
            <HStack spacing={"$2"} paddingLeft={10} paddingTop={5} background="$primaryDark1">
              <Box as="span" fontWeight="bold">{props.trigger}{selectedCommand()?.command}</Box>
              <Box as="span" fontWeight={200} color="$neutral11">{selectedCommand()?.shortDescription}</Box>
            </HStack>
          }

          {mode() == "CommandMatch" &&
            <Box className={css.commandsMatchingContainer}>
              <Box as="span" color="$accent1">Commands Matching:</Box> <Box as="span">{props.trigger}{actualInput()}</Box>
            </Box>
          }

          <HStack h="100%" alignItems={"flex-start"} background="$primary1">
            {/* SideBar */}
            {mode() == "ShowAll" &&
              <VStack
                spacing={"$1"}
                className={css.containerSideBar}
              >
                <For each={commandModules()}>
                  {(commandModule, idx) =>
                    <CommandModuleComponent
                      module={commandModule}
                      onClick={() => setActiveSelectedModIndex(idx())}
                      isActive={idx() == activeSelectedModIndex()}
                    />
                  }
                </For>
              </VStack>
            }

            {/* Main Content - Shown when mode = CommandMatch*/}
            <Box className={css.commandsContainer} ref={commandsContainerScrollingElement}>
              <VStack alignItems={"flex-start"} spacing="$2" padding={5}>
                <For each={activeCommands()}>
                  {(command, idx) => <CommandComponent
                    focused={activeSelectedIndex() == idx()}
                    command={command} trigger={props.trigger}
                    onClick={() => onSelectedCommand(command)}
                  />}
                </For>
              </VStack>
            </Box>
          </HStack>
        </VStack>
      </Box>
    }
  </>);
};

export default CommandsContainer;