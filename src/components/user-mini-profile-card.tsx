import { Box, Divider, HStack, Heading, Skeleton, Text, VStack } from "@hope-ui/solid";
import { isArray } from "lodash-es";
import { FaSolidMusic } from "solid-icons/fa";
import { Component, For, Match, Show, Suspense, Switch, createEffect, createSignal, lazy, onMount } from "solid-js";
import { useService } from "solid-services";
import { useApiUserRecordContext, useUserRecordContext } from "~/contexts/user.context";
import { Instrument } from "~/proto/midi-renditions";
import { Roles, rolesToJSON } from "~/proto/user-renditions";
import profileCss from '~/sass/sidebar.userprofile.module.sass';
import EmojifyService from "~/services/emojify.service";
import { RolesHelper } from "~/types/user-helper";
import { SolidMarkDownText } from "./solid-markdown-text";

const UserProfileBackgroundImage = lazy(() => import("./user-profile-background-image"));
const UserProfileImage = lazy(() => import("./user-profile-image"));
const UserProfileImageWidth = 60;

const UserMiniProfileCard: Component = () => {
  const { user } = useApiUserRecordContext();
  const { parsedMetaDetails: meta } = useUserRecordContext();
  const [hasFooterDetails, setHasFooterDetails] = createSignal(false);
  const [userRole, setUserRole] = createSignal<string>();
  const emojifyService = useService(EmojifyService);

  onMount(() => {
    let userRole = RolesHelper.getHighestRole(RolesHelper.mapRolesFromJSON(user.roles ?? []))?.role;
    if (userRole == null) userRole = Roles.UNRECOGNIZED;
    setUserRole(rolesToJSON(userRole));
  });

  createEffect(() => {
    setHasFooterDetails([
      user.statusText,
      user.lastOnline,
      meta()
    ].some(x => x));
  });

  return (<>
    <Box
      id={encodeURI(`user-mini-profile-card-${user.usertag}`)}
      w={user.profileBackgroundImageLastModified ? 250 : undefined}
      maxW={350}
    >
      {/* Profile BG Image */}
      <Suspense fallback={<Skeleton borderRadius={5} h={UserProfileImageWidth}></Skeleton>}>
        <Box className={profileCss.miniProfileBG}>
          {user && user.profileBackgroundImageLastModified &&
            <UserProfileBackgroundImage
              width="100%"
              height={"100%"}
              usertag={user!.usertag || ""}
              profileBackgroundImageLastModified={user.profileBackgroundImageLastModified}
            />}
        </Box>
      </Suspense>

      <HStack>
        {/* Profile Image */}
        <Box width={UserProfileImageWidth}>
          <Suspense fallback={<Skeleton borderRadius={5} h={UserProfileImageWidth}></Skeleton>}>
            <UserProfileImage height={UserProfileImageWidth} />
          </Suspense>
        </Box>

        {/* Profile Details */}
        <VStack marginLeft={10} alignItems="flex-start">
          <Heading as="h4" fontSize={16} className={profileCss.usernameHeader}>{emojifyService().decode(user.nickname || user.username)}</Heading>
          <Text opacity={0.7} className={profileCss.usertagText}>{user.usertag}</Text>
          <HStack spacing="$1">
            <Box mt={5} padding={2} borderRadius={3} background="$accent1" fontSize={8}>
              {userRole()}
            </Box>
          </HStack>
        </VStack>
      </HStack>

      {hasFooterDetails() && <Divider className={profileCss.profileCardFooterDivider} />}

      {/* Footer Details */}
      <VStack alignItems="flex-start" color="$neutral12 !important" spacing={"$1"}>
        {/* Status Text */}
        {user.statusText &&
          <Box background={"rgba(0,0,0,0.35)"} padding={5} borderRadius={5} w="100%">
            <SolidMarkDownText
              text={user.statusText as string}
              className={profileCss.usertagText} />
          </Box>
        }

        {user.lastOnline &&
          <Text fontSize={10} >Last Online: {user.lastOnline.toLocaleDateString()}</Text>
        }

        {/* Meta Details */}
        <Show when={meta()}>
          <VStack background="rgba(0,0,0,0.35)" borderRadius={5} padding={5} alignItems="flex-start" color="$neutral12 !important" className={profileCss.footerMetaContainer} >
            {meta()?.clientType && <Text className={profileCss.metaText}>Client: <b>{meta()?.clientType}</b></Text>}
            {meta()?.activeSoundfont && <Text className={profileCss.metaText}>Soundfont: <b>{meta()?.activeSoundfont}</b></Text>}
            {meta()?.currentSlotMode && <Text className={profileCss.metaText}>Channels Mode: <b>{meta()?.currentSlotMode}</b></Text>}
            {meta()?.currentInstrument &&
              <Switch>
                <Match when={isArray(meta()!.currentInstrument)}>
                  <Text className={profileCss.metaText}>Instrument(s):</Text>
                  <For each={meta()!.currentInstrument as Instrument[]}>
                    {instrument => <Box marginLeft={5} fontWeight={"bold"} fontSize={10}>{`${instrument.displayName}`}</Box>}
                  </For>
                </Match>
                <Match when={true}>
                  <Text className={profileCss.metaText}>Instrument: <b>{(meta()!.currentInstrument as Instrument).displayName as any}</b></Text>
                </Match>
              </Switch>
            }
            {meta()?.currentKeyboardLayout && <Text className={profileCss.metaText}>Layout: <b>{meta()?.currentKeyboardLayout}</b></Text>}
            {meta()?.synthEngine && <Text className={profileCss.metaText}>Synth Engine: <b>{meta()?.synthEngine}</b></Text>}
            {meta()?.graphicsEngine && <Text className={profileCss.metaText}>Graphics Engine: <b>{meta()?.graphicsEngine}</b></Text>}
            {meta()?.currentMidiFilePlaying &&
              <HStack spacing={"$1"}>
                <FaSolidMusic font-size="8px" />
                <Text className={profileCss.metaText}>Playing Midi: <b>{meta()?.currentMidiFilePlaying}</b></Text>
                <FaSolidMusic font-size="8px" />
              </HStack>
            }
            {meta()?.environment && <Text className={profileCss.metaText}>Environment: <b>{meta()?.environment}</b></Text>}
          </VStack>
        </Show>
      </VStack>
    </Box>
  </>);
};

export default UserMiniProfileCard;