import { Box } from "@hope-ui/solid";
import { clone } from "lodash-es";
import { Component, createSignal, onMount } from "solid-js";
import { useService } from "solid-services";
import ChatService from "~/services/chat.service";
import { ChatMessageRecord } from "~/types/chat.types";
import { scrollToChatMessageElement } from "./chat.common";
import css from '~/sass/chat.module.sass';

const ChatMessageReplyItem: Component<{ replyID: string; }> = (props) => {
  const chatService = useService(ChatService);
  const [chatRecord, setChatRecord] = createSignal<ChatMessageRecord>();

  onMount(() => {
    let record = chatService().getMessageByID(props.replyID);
    setChatRecord(clone(record));
  });

  return (<>
    {chatRecord() &&
      <Box
        __tooltip_title={
          <Box
            style={{
              "word-wrap": "break-word"
            }}
            innerHTML={
              `<b>${chatRecord()?.usertag || "Someone"}</b>
            said: <br><br>${chatRecord()?.message.substring(0, 150)}...`
            }
          ></Box>
        }
        __tooltip_show_arrow={false}
        onclick={() => scrollToChatMessageElement(chatRecord()?.id)}
        maxWidth={chatService().ContainerWidth - 80}
        className={css.chatmessageReply}>
        {chatRecord()?.usertag &&
          <Box
            as="span" className={css.replyMessageUser}>{chatRecord()?.usertag}:
          </Box>
        }
        {chatRecord()?.message &&
          <Box as="span" className={css.replyMessageText}>{chatRecord()?.message}</Box>
        }
      </Box>
    }
  </>);
};

export default ChatMessageReplyItem;