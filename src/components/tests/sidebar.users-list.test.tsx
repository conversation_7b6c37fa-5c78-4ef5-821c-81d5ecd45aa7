import {render, screen} from '@solidjs/testing-library';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {createRoot} from 'solid-js';
import {useService} from 'solid-services';
import UsersService from '~/services/users-service';
import AppService from '~/services/app.service';
import {ClientSideUserDto, Roles, UserDto, UserStatus} from '~/proto/user-renditions';
import SidebarUsersList from '~/components/sidebar.users-list';

// Mock the context providers
vi.mock('~/contexts/user.context', () => ({
    UserRecordProvider: (props: any) => props.children,
    ApiUserRecordProvider: (props: any) => props.children,
    useUserRecordContext: () => ({
        user: {userDto: {username: 'Test User'}},
        parsedMetaDetails: {}
    })
}));

// Mock the services
vi.mock('solid-services');
vi.mock('~/services/users-service');
vi.mock('~/services/app.service');

describe('<SidebarUsersList />', () => {
    let usersServiceMock: any;
    let appServiceMock: any;
    let mockUsers: ClientSideUserDto[];

    beforeEach(() => {
        vi.clearAllMocks();

        // Create mock users with different roles
        mockUsers = [
            createMockUser('user1', 'User 1', [Roles.ADMIN]),
            createMockUser('user2', 'User 2', [Roles.DEVELOPER]),
            createMockUser('user3', 'User 3', [Roles.MODERATOR]),
            createMockUser('user4', 'User 4', [Roles.TRIAL_MODERATOR]),
            createMockUser('user5', 'User 5', [Roles.ROOMOWNER]),
            createMockUser('user6', 'User 6', [Roles.MEMBER]),
            createMockUser('user7', 'User 7', [Roles.GUEST]),
            createMockUser('user8', 'User 8', [Roles.BOT]),
        ];

        // Setup mock services
        usersServiceMock = {
            getUsers: vi.fn().mockReturnValue(mockUsers),
        };

        appServiceMock = {
            isClient: vi.fn().mockImplementation((socketID) => socketID === 'user1'),
            roomOwner: vi.fn().mockReturnValue('user5'),
        };

        // Mock the useService hook
        (useService as any).mockImplementation((service: any) => {
            if (service === UsersService) return () => usersServiceMock;
            if (service === AppService) return () => appServiceMock;
        });
    });

    it('should render SidebarUsersList with all user categories', () => {
        createRoot(async (dispose) => {
            // Act
            render(() => <SidebarUsersList/>);

            // Assert
            // Check that each role category is rendered by looking for the group headers
            const headers = await screen.findAllByTestId('virtual-list-group-header');
            expect(headers.length).toBe(8);

            // Verify the content of the headers
            expect(headers[0]).toHaveTextContent('Creator');
            expect(headers[1]).toHaveTextContent('Developer');
            expect(headers[2]).toHaveTextContent('Moderator');
            expect(headers[3]).toHaveTextContent('Trial Moderator');
            expect(headers[4]).toHaveTextContent('Room Owner');
            expect(headers[5]).toHaveTextContent('Bot');
            expect(headers[6]).toHaveTextContent('Member');
            expect(headers[7]).toHaveTextContent('Guest');

            // Check that we have the expected number of user items
            const userItems = await screen.findAllByTestId('sidebar-user-item');
            expect(userItems.length).toBe(8);
            dispose();
        });
    });

    it('should not render categories with no users', () => {
        createRoot(async (dispose) => {
            // Arrange - modify the mock to return only one user
            usersServiceMock.getUsers.mockReturnValue([
                createMockUser('user1', 'User 1', [Roles.ADMIN])
            ]);

            // Act
            render(() => <SidebarUsersList/>);

            // Assert
            // Only Creator should be visible
            const headers = await screen.findAllByTestId('virtual-list-group-header');
            expect(headers.length).toBe(1);
            expect(headers[0]).toHaveTextContent('Creator');

            // Only one user item should be rendered
            const userItems = await screen.findAllByTestId('sidebar-user-item');
            expect(userItems.length).toBe(1);
            dispose();
        });
    });

    it('should sort users correctly with client user first', () => {
        createRoot(async (dispose) => {
            // Arrange - modify the mock to return only two users with the same role
            usersServiceMock.getUsers.mockReturnValue([
                createMockUser('user2', 'User 2', [Roles.ADMIN]),
                createMockUser('user1', 'User 1', [Roles.ADMIN])
            ]);

            // Act
            render(() => <SidebarUsersList/>);

            // Assert
            // Check that we have the expected number of user items
            const userItems = await screen.findAllByTestId('sidebar-user-item');
            expect(userItems.length).toBe(2);

            // Check that the header is correct
            const header = await screen.findByTestId('virtual-list-group-header');
            expect(header).toHaveTextContent('Creator');
            dispose();
        });
    });

    it('should display plural form for categories with multiple users', () => {
        createRoot(async (dispose) => {
            // Arrange - modify the mock to return three users with the same role
            usersServiceMock.getUsers.mockReturnValue([
                createMockUser('user1', 'User 1', [Roles.ADMIN]),
                createMockUser('user2', 'User 2', [Roles.ADMIN]),
                createMockUser('user3', 'User 3', [Roles.ADMIN])
            ]);

            // Act
            render(() => <SidebarUsersList/>);

            // Assert
            // Check that the plural form is used
            const header = await screen.findByTestId('virtual-list-group-header');
            expect(header).toHaveTextContent('Creators - 3');

            // Check that we have the expected number of user items
            const userItems = await screen.findAllByTestId('sidebar-user-item');
            expect(userItems.length).toBe(3);
            dispose();
        });
    });
});

// Helper function to create mock users
function createMockUser(socketID: string, username: string, roles: Roles[]): ClientSideUserDto {
    let userDto: UserDto = UserDto.create({username, roles});
    userDto.usertag = username.toLowerCase();
    userDto.status = UserStatus.Online;
    userDto.badges = [];
    userDto.color = '#FFFFFF';
    userDto.socketID = socketID;
    userDto.roles = roles;

    return {
        socketID,
        userDto
    } as ClientSideUserDto;
}
