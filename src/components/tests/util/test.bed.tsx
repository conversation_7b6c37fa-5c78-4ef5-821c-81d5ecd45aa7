import {<PERSON><PERSON><PERSON>ider} from "@hope-ui/solid";
import {ParentComponent, Suspense} from "solid-js";
import {ServiceRegistry} from "solid-services";
import ThemeConfig from "../../../util/theme-config";

const TestBed: ParentComponent = (props) => {
    return (
        <HopeProvider config={ThemeConfig}>
            <ServiceRegistry>
                <Suspense>
                    {props.children}
                </Suspense>
            </ServiceRegistry>
        </HopeProvider>
    );
};

export default TestBed;