import { <PERSON><PERSON><PERSON>ider } from "@hope-ui/solid";
import { MetaProvider } from "@solidjs/meta";
import { Router } from "@solidjs/router";
import { ParentComponent } from "solid-js";
import { ServiceRegistry } from "solid-services";
import ThemeConfig from "../../../util/theme-config";

const TestBedRouter: ParentComponent = (props) => {
  return (
    <HopeProvider config={ThemeConfig}>
      <ServiceRegistry>
        <MetaProvider>
          <Router>
            {props.children}
          </Router>
        </MetaProvider>
      </ServiceRegistry>
    </HopeProvider>
  );
};

export default TestBedRouter;