import { fireEvent, render, screen } from '@solidjs/testing-library';
import { DragDropProvider, DragDropSensors } from '@thisbeyond/solid-dnd';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { ActiveChannelsMode } from '~/proto/midi-renditions';
import AudioService from '~/services/audio.service';
import DisplaysService from '~/services/displays.service';
import I18nService from '~/services/i18n.service';
import AppSettingsService from '~/services/settings-storage.service';
import SoundEffectsService from '~/services/sound-effects.service';
import { MockSoundEffectsService } from '@test/mocks/service.mocks';
import InstrumentDock from '../../components/instrument-dock';
import TestBed from './util/test.bed';

// Mock services
vi.mock('~/services/audio.service', () => ({ default: vi.fn() }));
vi.mock('~/services/displays.service', () => ({ default: vi.fn() }));
vi.mock('~/services/i18n.service', () => ({ default: vi.fn() }));
vi.mock('~/services/sound-effects.service', () => ({ default: vi.fn() }));
vi.mock('~/services/settings-storage.service', () => ({ default: vi.fn() }));

const TestProvider = (props: any) => {
  return <TestBed>
    <DragDropProvider >
      <DragDropSensors>
        {props.children}
      </DragDropSensors>
    </DragDropProvider>
  </TestBed>;
};

describe('<InstrumentDock />', () => {
  let displaysServiceMock: any;
  let audioServiceMock: any;
  let i18nServiceMock: any;
  let sfxServiceMock;
  let resourceServiceMock: any;

  beforeEach(() => {
    displaysServiceMock = {
      getDisplay: vi.fn().mockReturnValue(true),
    };
    audioServiceMock = {
      getChannelNumbers: vi.fn().mockReturnValue([0, 1]),
      setTranspose: vi.fn(),
      setOctave: vi.fn(),
      incrementSlotMode: vi.fn(),
      resetChannelsToDefault: vi.fn(),
      setPrimaryChannel: vi.fn(),
      maxMultiModeChannels: vi.fn().mockReturnValue(2),
      primaryChannel: vi.fn().mockReturnValue(0),
      getDisabledChannels: vi.fn().mockReturnValue([]),
      toggleChannelActive: vi.fn(),
      setChannelVolume: vi.fn(),
      channels: vi.fn(() => [
        // TODO: Figure out why this triggers an infinite loop
        // { channel: 0, active: true, volume: 100 },
      ]),
      setCurrentChannelToEdit: vi.fn(),
      isDrumChannelMuted: vi.fn().mockReturnValue(false),
      transpose: vi.fn().mockReturnValue(0),
      octave: vi.fn().mockReturnValue(0),
      slotMode: vi.fn().mockReturnValue(ActiveChannelsMode.ALL),
    };

    let translateMock = (input: string) => {
      if (input.includes("transpose")) return 'Transpose';
      if (input.includes("octave")) return 'Octave';
      return 'hello world';
    };

    i18nServiceMock = {
      t_roomPage: vi.fn(translateMock),
      t_roomPageInstDockToolNames: vi.fn(translateMock),
      t_roomPageInstDockToolNamesToolTips: vi.fn(translateMock),
    };
    sfxServiceMock = MockSoundEffectsService();
    resourceServiceMock = {
      getResource: vi.fn().mockReturnValue(''),
      instrumentImageFetch: vi.fn().mockResolvedValue(''),
    };

    vi.mocked(DisplaysService).mockReturnValue(displaysServiceMock as any);
    vi.mocked(AudioService).mockReturnValue(audioServiceMock as any);
    vi.mocked(I18nService).mockReturnValue(i18nServiceMock as any);
    vi.mocked(SoundEffectsService).mockReturnValue(sfxServiceMock as any);
    vi.mocked(AppSettingsService).mockReturnValue(resourceServiceMock as any);
  });

  it.skip('should render InstrumentDock component', async () => {
    const { container } = render(() => <InstrumentDock />, { wrapper: TestProvider });
    expect(container).toMatchSnapshot();
  });

  it.skip('should display the correct number of InstrumentChannel components', async () => {
    render(() => <InstrumentDock />, { wrapper: TestProvider });
    const channels = await screen.findAllByText(/Channel:/);
    expect(channels.length).toBe(2);
  });

  it.skip('should handle mouse enter and leave events', async () => {
    render(() => <InstrumentDock />, { wrapper: TestProvider });
    const dock = screen.getByRole('channel-dock-container');
    fireEvent.mouseEnter(dock);
    expect(dock).toHaveStyle({ opacity: 0 });
    fireEvent.mouseLeave(dock);
    expect(dock).toHaveStyle({ opacity: 0.5 });
  });

  it('should handle button clicks', async () => {
    render(() => <InstrumentDock />, { wrapper: TestProvider });
    const transposeButton = screen.getByText('Transpose');
    fireEvent.mouseDown(transposeButton);
    expect(audioServiceMock.setTranspose).toHaveBeenCalled();
  });

  it.skip('should handle state changes correctly', async () => {
    render(() => <InstrumentDock />, { wrapper: TestProvider });
    const container = screen.getByRole('channel-dock-container');
    const channel = container.querySelector("[id='instrument-dock-slot-0']");
    expect(channel).toBeInTheDocument();
    fireEvent.mouseDown(channel!);
    expect(audioServiceMock.toggleChannelActive).toHaveBeenCalledOnce();
  });
});