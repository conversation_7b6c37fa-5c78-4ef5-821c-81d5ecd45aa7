import { describe, it, expect } from 'vitest';
import { render } from '@solidjs/testing-library';
import ImageUploader from '../image-uploader';
import TestBedRouter from './util/test.bed-router';
import { Component } from 'solid-js';

type SUTProps = {
  headerLabel?: string;
  path?: string;
};

const SUT: Component<SUTProps> = (props) => {
  return (
    <TestBedRouter>
      <ImageUploader
        headerLabel={props.headerLabel || 'Test'}
        path={props.path || '/test'}
        onClose={() => { }}
        onSave={async () => { }}
      />
    </TestBedRouter>
  );
};

describe.skip('<ImageUploader />', () => {
  it('should render', () => {
    const { container } = render(() => <SUT />);
    expect(container).toMatchSnapshot();
  });

  // Ignore this test for now. Modal not showing
  // up in DOM.
  it.skip("save button should be disabled when no image is uploaded", async () => {
    const { findByText } = render(() => <SUT />);
    const saveButton = await findByText('save');
    console.log("saveButton", saveButton);
  });
});