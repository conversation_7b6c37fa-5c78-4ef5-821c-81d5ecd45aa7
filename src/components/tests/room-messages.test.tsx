import { render, screen, fireEvent } from '@solidjs/testing-library';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import RoomMessagesDisplay from '~/components/room-message';
import AppService from '~/services/app.service';
import AudioService from '~/services/audio.service';
import AppSettingsService from '~/services/settings-storage.service';
import { WARNING_MESSAGES } from '~/util/const.common';
import TestBed from './util/test.bed';

// Mock services
vi.mock('~/services/app.service');
vi.mock('~/services/audio.service');
vi.mock('~/services/settings-storage.service');

describe.skip('<RoomMessagesDisplay />', () => {
  let appServiceMock: any;
  let audioServiceMock: any;
  let appSettingsServiceMock: any;

  beforeEach(() => {
    appServiceMock = {
      offlineMode: vi.fn().mockReturnValue(false),
      maintenanceModeActive: vi.fn().mockReturnValue(false),
      clientHasEveryoneElseMuted: vi.fn().mockReturnValue(false),
      setClientHasEveryoneElseMuted: vi.fn(),
      clientIsSelfNotesMuted: vi.fn().mockReturnValue(false),
      roomSettings: vi.fn().mockReturnValue({}),
    };
    audioServiceMock = {
      allChannelsAreInactive: vi.fn().mockReturnValue(false),
    };
    appSettingsServiceMock = {
      settingSaved: vi.fn().mockReturnValue(false),
      getSetting: vi.fn().mockReturnValue(false),
    };

    vi.mocked(AppService).mockReturnValue(appServiceMock);
    vi.mocked(AudioService).mockReturnValue(audioServiceMock);
    vi.mocked(AppSettingsService).mockReturnValue(appSettingsServiceMock);
  });

  it('should render RoomMessagesDisplay component', async () => {
    const { container } = render(RoomMessagesDisplay, { wrapper: TestBed });
    expect(container).matchSnapshot();
  });

  it('should display offline mode message', () => {
    appServiceMock.offlineMode.mockReturnValue(true);
    render(() => <RoomMessagesDisplay />);
    expect(screen.getByText('OFFLINE MODE')).toBeInTheDocument();
  });

  it('should display maintenance mode message', () => {
    appServiceMock.maintenanceModeActive.mockReturnValue(true);
    render(() => <RoomMessagesDisplay />);
    expect(screen.getByText('Server undergoing maintenance!')).toBeInTheDocument();
  });

  it('should display no active channels message', () => {
    audioServiceMock.allChannelsAreInactive.mockReturnValue(true);
    render(() => <RoomMessagesDisplay />);
    expect(screen.getByText(WARNING_MESSAGES.NoActiveChannels)).toBeInTheDocument();
  });

  it('should display audio to MIDI output only message', () => {
    appSettingsServiceMock.settingSaved.mockReturnValue(true);
    appSettingsServiceMock.getSetting.mockReturnValue(true);
    render(() => <RoomMessagesDisplay />);
    expect(screen.getByText('Audio to midi output only')).toBeInTheDocument();
  });

  it('should handle mute button click', () => {
    appServiceMock.clientIsSelfNotesMuted.mockReturnValue(true);
    render(() => <RoomMessagesDisplay />);
    const button = screen.getByText('Mute All');
    fireEvent.mouseDown(button, { button: 0 });
    expect(appServiceMock.setClientHasEveryoneElseMuted).toHaveBeenCalledWith(true);
  });

  it('should display various room settings messages', () => {
    appServiceMock.roomSettings.mockReturnValue({
      OnlyOwnerCanPlay: true,
      OnlyOwnerCanChat: true,
      NoChatAllowed: true,
      NoPlayingAllowed: true,
    });
    render(() => <RoomMessagesDisplay />);
    expect(screen.getByText('Only Room Owner Can Play')).toBeInTheDocument();
    expect(screen.getByText('Only Room Owner Can Chat')).toBeInTheDocument();
    expect(screen.getByText('No Chat Allowed')).toBeInTheDocument();
    expect(screen.getByText('No Playing Allowed')).toBeInTheDocument();
  });
});