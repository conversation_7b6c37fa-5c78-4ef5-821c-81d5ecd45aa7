import { Center } from '@hope-ui/solid';
import i18next, { i18n } from 'i18next';
import { ParentComponent, Show, createContext, createEffect, createSignal, onMount, useContext } from 'solid-js';
import { logDebug } from '~/util/logger';
import * as i18Config from './config';
import { createI18n } from './context';

const I18nContext = createContext<i18n>(i18next);

export const I18nProvider: ParentComponent = (props) => {
  const [i18nLoaded, setI18nLoaded] = createSignal(false);
  const [loaded, setLoaded] = createSignal(false);
  const { store, updateStore } = createI18n(i18next);

  onMount(async () => {
    logDebug("Loading i18n...");
    await i18Config.default;
    updateStore(i18next);
    logDebug("i18n loaded.");
    setI18nLoaded(true);
  });

  createEffect(() => {
    setLoaded(i18nLoaded());
  });

  return (
    <I18nContext.Provider value={store}>
      <Show when={loaded()}
        fallback={
          <Center class="elementFadeIn" background={"$primaryDark1"} w={"100vw"} h={"100vh"}>
            Please wait...
          </Center>
        }
      >
        {props.children}
      </Show>
    </I18nContext.Provider>
  );
};

export function useI18n() { return useContext(I18nContext); }