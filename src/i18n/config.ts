import i18next from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpApi from 'i18next-http-backend';
import { isServer } from 'solid-js/web';
import { COMMON, LANGUAGES } from '~/util/const.common';
import { logError } from '~/util/logger';

let defaultNS = "common";
let supportedLanguages = LANGUAGES.map(x => x[0]);

const i18n = i18next
  .use(LanguageDetector)
  .use(HttpApi)
  .init({
    fallbackLng: 'en',
    initImmediate: false,
    preload: ['en'],
    ns: [
      defaultNS,
      "loginPage",
      "roomPage",
      "roomPage.settingsModal",
      "server.messages",
    ],
    supportedLngs: supportedLanguages,
    defaultNS,
    fallbackNS: defaultNS,
    debug: COMMON.IS_DEV_MODE && !isServer,
    detection: {
      order: ['querystring', 'navigator', 'htmlTag'],
      lookupQuerystring: 'lang',
    },
    backend: {
      loadPath: `${isServer || !COMMON.IS_DEV_MODE ? COMMON.ASSETS_URL : ""}/locales/{{lng}}/{{ns}}.json`,
    }
  }, (err, t) => {
    if (err) return logError(`i18n Error: ${err}`);
  });

export default i18n;