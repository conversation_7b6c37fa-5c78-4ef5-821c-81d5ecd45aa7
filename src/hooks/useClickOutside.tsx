import { Accessor, createEffect, onCleanup } from 'solid-js';

export interface ClickOutsideCallback<T extends Event = Event> {
  (event: T): void;
}

const useClickOutside = (ref: Accessor<HTMLElement | undefined>, callback: ClickOutsideCallback) => {
  createEffect(() => {
    const listener = (event: MouseEvent) => {
      const element = ref();
      if (!element) return;

      // let isCtxMenu = false;
      // if (event.button == 2) {
      //   event.preventDefault();
      //   isCtxMenu = true;
      // }

      if (element.contains(event.target as Node)) {
        return;
      }

      callback(event);
    };
    document.addEventListener('click', listener);
    // document.addEventListener('contextmenu', listener);

    onCleanup(() => {
      document.removeEventListener('click', listener);
      // document.removeEventListener('contextmenu', listener);
    });
  });
};

export default useClickOutside;