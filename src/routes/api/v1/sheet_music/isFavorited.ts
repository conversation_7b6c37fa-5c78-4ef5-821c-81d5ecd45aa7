import type { APIEvent } from "@solidjs/start/server";
import { z } from "zod";
import { POST_API } from "../../api-helpers";
import { SheetMusicDBService } from "~/lib/db/services/db-sheet-music";

const InputDto = z.object({
  sheetMusicID: z.string(),
  usertag: z.string()
});

const OutputDto = z.object({});

export const POST = (event: APIEvent) =>
  POST_API<z.infer<typeof InputDto>, z.infer<typeof OutputDto>>(event, {
    schema: InputDto,
    process: async (input) => {
      const dbService = SheetMusicDBService.getInstance();
      const result = await dbService.aggregateData([
        {
          $match: {
            uuid: input.sheetMusicID,
            favorites: { $in: [new RegExp(input.usertag, "i")] }
          }
        }
      ]);
      return result.length > 0;
    }
  })

