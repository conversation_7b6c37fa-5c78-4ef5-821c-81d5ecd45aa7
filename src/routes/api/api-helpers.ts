import type { APIEvent } from "@solidjs/start/server";
import { z } from "zod";

export async function POST_API<I, O>({ request }: APIEvent, {
  schema,
  processInput
}: {
  schema: z.ZodSchema<I>,
  processInput: (input: I) => Promise<O>;
}) {
  try {
    const requestInput = await request.json();
    const input = await schema.safeParseAsync(requestInput);

    if (input.error) {
      return new Response(input.error.toString(), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const result = await processInput(input.data);

    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { 'Content-Type': 'application/json' }
    });

  } catch (error) {
    console.error(error);
    return new Response(error ? `${error}` : "An error occurred", { status: 400 });
  }
}