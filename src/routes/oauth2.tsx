import { useAction, useNavigate, useSearchParams } from "@solidjs/router";
import { onMount } from "solid-js";
import { useService } from "solid-services";
import { handleOAuth2Response } from "~/lib";
import LoginService from "~/services/login.service";
import NotificationService from "~/services/notification.service";
import { COMMON } from "~/util/const.common";
import { logError, logTrace } from "~/util/logger";

const OAuthHandler = () => {
  const loginService = useService(LoginService);
  const onAuth2Response = useAction(handleOAuth2Response);

  const [searchParams] = useSearchParams();
  const oauthType = searchParams.success;
  const oauthATToken = searchParams.at;
  const oAuthRedirectPath = searchParams.redirect_path;
  const navigate = useNavigate();

  onMount(async () => {
    try {
      if (COMMON.IS_DEV_MODE) {
        console.log("OAuthHandler", oauthType, oAuthRedirectPath);
      }

      if (!oauthType || !oauthATToken) {
        NotificationService.show({
          title: "Login failed",
          description: "OAuth login failed. Please try again later.",
          type: "danger",
        });

        logError(`OAuth login failed: ${oauthType}`, oauthType, oauthATToken);
        return navigate("/");
      }

      logTrace("OAuth token received!");

      let at = searchParams.at as string;
      let rt = Array.isArray(searchParams.rt) ? searchParams.rt[0] : searchParams.rt;
      let sessionID = Array.isArray(searchParams.sessionID) ? searchParams.sessionID[0] : searchParams.sessionID;

      await onAuth2Response({
        at,
        rt,
        sessionID
      });

      // Redirect to the path if it exists
      if (oAuthRedirectPath) {
        navigate(String(oAuthRedirectPath));
        return;
      }

      // Otherwise, navigate to target room
      let roomName = Array.isArray(searchParams.roomName) ? searchParams.roomName[0] : searchParams.roomName;
      await loginService().onEnterAppLoadingAfterLogin(roomName);

      if (COMMON.IS_DEV_MODE) {
        console.log("OAuth success", oauthType, oauthATToken, document.location.href);
      }
    } catch (error) {
      logError("OAuth login failed", error);
      console.error("OAuth login failed", error);

      NotificationService.show({
        title: "Login failed",
        description: "OAuth login failed. Please try again later.",
        type: "danger",
      });
    }
  });

  return undefined;
};

export default OAuthHandler;