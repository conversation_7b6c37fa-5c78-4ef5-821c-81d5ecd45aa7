import { Box, Center, VStack } from "@hope-ui/solid";
import { Title } from "@solidjs/meta";
import { useSearchParams } from "@solidjs/router";
import { lazy, onMount, Suspense } from "solid-js";
import { useService } from "solid-services";
import { GoHome_Anchor } from "~/components/anchors";
import notificationService from "~/services/notification.service";
import SoundEffectsService from "~/services/sound-effects.service";

const HomeMeta = lazy(() => import("~/components/home.meta"));
const BackgroundImage = lazy(() => import("~/components/home.background"));

const NotFound = () => {
  const [params] = useSearchParams();
  const sfxService = useService(SoundEffectsService);

  onMount(() => {
    try {
      const errorReason = atob(params.reason ?? "");
      sfxService().playErrorSFX();
      notificationService.show({
        title: "Server Error",
        type: "danger",
        description: errorReason
      });
    } catch { }
  });

  return (<>
    <Title>PianoRhythm - Not Found</Title>
    <Suspense>
      <HomeMeta />
    </Suspense>

    <Suspense>
      <BackgroundImage />
    </Suspense>

    <Box
      top={0}
      left={0}
      width="100vw"
      height="100vh"
      position={"absolute"}
    >
      <Center h={"100%"}>
        <VStack
          background={"primary.50"}
          padding={30} borderRadius={5}
        >
          <Box>Page Not Found</Box>
          <br />
          <GoHome_Anchor />
        </VStack>
      </Center>
    </Box>
  </>);
};

export default NotFound;
