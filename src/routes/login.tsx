import { Button, ButtonGroup, Input, Text, VStack } from "@hope-ui/solid";
import { createAsync, useAction, useLocation, useSearchParams, useSubmission, type RouteDefinition } from "@solidjs/router";
import Mousetrap from "mousetrap";
import { createEffect, createSignal, lazy, Match, on, onCleanup, onMount, Switch } from "solid-js";
import { useService } from "solid-services";
import { Transition } from "solid-transition-group";
import HomeBaseParent from "~/components/home.base-parent";
import inputSFX from "~/directives/input.directive";
import { getMemberSessionInfo } from "~/lib";
import { login } from "~/lib/index";
import style from "~/sass/login.page.module.sass";
import AppService from "~/services/app.service";
import I18nService from "~/services/i18n.service";
import LoginService, { CurrentForm } from "~/services/login.service";
import MonitorTrackingService from "~/services/monitor-tracking.service";
import AppSettingsService from "~/services/settings-storage.service";
import { CurrentPage } from "~/types/app.types";
import { COMMON } from "~/util/const.common";
import { createLoginFormData } from "~/util/helpers";

export const route = {
  preload() { getMemberSessionInfo(); }
} satisfies RouteDefinition;

const LoginForm = lazy(() => import('../components/login/login.form'));
const LoggedInForm = lazy(() => import('../components/login/logged-in.form'));
const RegisterForm = lazy(() => import('../components/login/register.form'));
const ForgotPasswordForm = lazy(() => import('../components/login/forgot-password.form'));
const ResendVerificationEmailForm = lazy(() => import('../components/login/resend-verification-email.form'));

const MainForm = () => {
  const [searchParams] = useSearchParams();
  const loginService = useService(LoginService);
  const i18nService = useService(I18nService);
  const settingsService = useService(AppSettingsService);
  const loginAction = useAction(login);
  const loggingSubmission = useSubmission(login);
  const [loading, setLoading] = createSignal(false);
  const [guestName, setGuestName] = createSignal("");

  let inputElement!: HTMLInputElement;

  onMount(() => {
    window.history.pushState(null, '', "/");

    let trap = new Mousetrap(inputElement);
    trap.bind("enter", onEnter);
    onCleanup(() => trap.unbind("enter"));
  });

  const canNotEnter = () => false;

  const onEnter = async () => {
    const username = guestName();
    settingsService().setLocalStorage("lastSavedUsername", username);
    loginService().onBeforeLogin();

    let result = await loginService().onLogin(
      loginAction(createLoginFormData(username, undefined, true))
    );

    if (!result) return loginService().onLoginFail();
    const roomName = Array.isArray(searchParams.roomName) ? searchParams.roomName[0] : searchParams.roomName;
    await loginService().onEnterAppLoadingAfterLogin(roomName);
  };

  createEffect(() => {
    setLoading(loggingSubmission.pending ?? false);
  });

  const buttonSFX = (element: HTMLElement) => {
    if (!element) return;
  };

  const handleInput = (event: InputEvent) => {
    const text: string = (event.target as any).value;
    setGuestName(text);
  };

  const onInputElement = (element: HTMLInputElement) => {
    element.value = settingsService().getLocalStorage<string>("lastSavedUsername") || "";
    setGuestName(element.value);
    inputElement = element;
    inputSFX(element);
  };

  return (<>
    <VStack spacing={"$2"}>
      <Text class={style.welcomeMessage}>{i18nService().t_loginPage("guestForm.welcomeMessage")}</Text>
      <Text class={style.welcomeTitle}>{i18nService().t_loginPage("guestForm.usernameInputMessage")}</Text>
      <Input
        data-testid="login-username"
        ref={onInputElement}
        disabled={loading()}
        onInput={handleInput}
        class={style.mainFormInput}
        variant="unstyled"
        name="nickname"
        _hover={{ "outline": "none" }}
      />
      <ButtonGroup
        class={style.mainFormButtons}
        variant="outline"
        spacing="$0"
      >
        <Button
          data-testid="login-enter"
          ref={(el: HTMLElement) => buttonSFX(el)}
          disabled={canNotEnter()}
          loading={loading()} class={style.mainFormButton}
          variant="ghost"
          onmousedown={onEnter}
        >{i18nService().t_loginPage("guestForm.buttons.enter")}
        </Button>
        <Button
          data-testid="login-register"
          ref={(el: HTMLElement) => buttonSFX(el)}
          disabled={loading()}
          onmousedown={() => {
            loginService().updateForm(CurrentForm.Register);
          }}
          class={style.mainFormButton} variant="ghost">{i18nService().t_loginPage("guestForm.buttons.register")}</Button>
        <Button
          data-testid="login-login"
          ref={(el: HTMLElement) => buttonSFX(el)}
          disabled={loading()}
          onmousedown={() => {
            loginService().updateForm(CurrentForm.Login);
          }}
          class={style.mainFormButton} variant="ghost">{i18nService().t_loginPage("guestForm.buttons.login")}</Button>
      </ButtonGroup>
    </VStack>
  </>);
};

export default function LoginPage() {
  const monitorService = useService(MonitorTrackingService);
  const user = createAsync(() => getMemberSessionInfo(), { deferStream: true });
  const loginService = useService(LoginService);
  const appService = useService(AppService);
  const [currentPath, setCurrentPath] = createSignal<string>("");
  const location = useLocation();

  onMount(async () => {
    if (COMMON.IS_DEV_MODE) console.log("Login page mounted.", CurrentForm[loginService().currentForm()]);
    appService().setActivatePageLoader(false);
    appService().setCurrentPage(CurrentPage.Home);
    setCurrentPath(location.pathname.toLowerCase());
    monitorService().initialize();
  });

  function onGetUser() {
    if (currentPath() != "/" && currentPath() != "/login") return;

    let currentForm = loginService().currentForm();
    if (user()?.usertag && currentForm != CurrentForm.LoggedIn) {
      loginService().setCurrentLoggedInUsername({ username: user()!.username, usertag: user()!.usertag });
      loginService().updateForm(CurrentForm.LoggedIn);
      loginService().onUserAutoLoginNotification(user()!.username);
    }

    if (user()?.usertag == undefined && currentForm == CurrentForm.LoggedIn) {
      loginService().updateForm(CurrentForm.Main);
      loginService().setCurrentLoggedInUsername(undefined);
    }
  }

  createEffect(on(user, onGetUser));

  return (<>
    <HomeBaseParent>
      <Transition name="fade">
        <Switch>
          <Match when={loginService().currentForm() == CurrentForm.Main}><MainForm /></Match>
          <Match when={loginService().currentForm() == CurrentForm.Login}><LoginForm /></Match>
          <Match when={loginService().currentForm() == CurrentForm.LoggedIn}><LoggedInForm /></Match>
          <Match when={loginService().currentForm() == CurrentForm.Register}><RegisterForm /></Match>
          <Match when={loginService().currentForm() == CurrentForm.ForgotPassword}><ForgotPasswordForm /></Match>
          <Match when={loginService().currentForm() == CurrentForm.ResendVerificationEmail}><ResendVerificationEmailForm /></Match>
        </Switch>
      </Transition >
    </HomeBaseParent>
  </>);
}