/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_TITLE: string
  readonly VITE_ASSETS_URL: string
  readonly VITE_HOST_PORT: string
  readonly VITE_FULL_HOST: string
  readonly VITE_DOCS_HOST: string
  readonly VITE_HOST: string
  readonly VITE_VERSION: string
  readonly VITE_WS_PROTOCOL: string
  readonly VITE_COLYSEUS_HOST: string
  readonly VITE_COLYSEUS_PORT: string
  readonly VITE_COLYSEUS_WEBSOCKET_PROTOCOL: string
  readonly VITE_EXPRESS_API_HOST: string
  readonly VITE_STATUS_PAGE_URL: string
  readonly VITE_GOOGLE_CLIENT_ID: string
  readonly VITE_SENTRY_DSN: string
  readonly vitest: string

  //TAURI
  readonly TAURI_ARCH: string
  readonly TAURI_DEBUG: string
  readonly TAURI_FAMILY: string
  readonly TAURI_PLATFORM: string
  readonly TAURI_PLATFORM_TYPE: string
  readonly TAURI_PLATFORM_VERSION: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}