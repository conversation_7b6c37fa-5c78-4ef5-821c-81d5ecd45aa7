import {Accessor, Context, ParentComponent, createContext, createSignal, useContext} from "solid-js";
import {ChatMessageRecord} from "../types/chat.types";

type ContextProps = [
  Accessor<ChatMessageRecord>,
];

//@ts-ignore
export const ChatMessageRecordContext: Context<ContextProps> = createContext<ContextProps>();

export const ChatMessageRecordProvider: ParentComponent<{ record: ChatMessageRecord; }> = (props) => {
  const [state] = createSignal(props.record);
  const input: ContextProps = [
    state,
  ];

  return (
    <ChatMessageRecordContext.Provider value={input}>
      {props.children}
    </ChatMessageRecordContext.Provider>
  );
};

export function useChatRecordContext() {
  return useContext(ChatMessageRecordContext);
}