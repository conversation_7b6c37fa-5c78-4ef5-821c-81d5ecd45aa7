import { createContextProvider } from "@solid-primitives/context";
import { createEffect, createMemo } from "solid-js";
import { createStore } from "solid-js/store";
import { useService } from "solid-services";
import UsersService from "~/services/users-service";
import { UserDtoUtil } from "~/types/user-helper";
import { ApiUserDto, ClientSideUserDtoHelper } from "~/types/user.types";

export const [ApiUserRecordProvider, useApiUserRecordContext] = createContextProvider((props: { value: { socketID?: string; default?: ApiUserDto; }; }) => {
  const usersService = useService(UsersService);

  const [user, setUser] = createStore(
    // Use the default value if the socketID is explicitly not provided
    (!props.value.socketID && props.value.default) ? props.value.default :
      UserDtoUtil.clientSideUserDtoToApiUserDto(usersService().getUserBySocketID(props.value.socketID)) ??
      props.value.default ??
      ApiUserDto.Default()
  );

  createEffect(() => {
    let activeUser = usersService().getUserBySocketID(props.value.socketID);
    if (activeUser) setUser(UserDtoUtil.clientSideUserDtoToApiUserDto(activeUser) ?? ApiUserDto.Default());
  });

  return { user } as const;
}, {
  user: ApiUserDto.Default(),
});

export const [UserRecordProvider, useUserRecordContext] = createContextProvider((props: { value: string; }) => {
  const usersService = useService(UsersService);

  const parsedMetaDetails = createMemo(() =>
    ClientSideUserDtoHelper.GetParsedMetaDetails(
      usersService().getUserBySocketID(props.value)?.userDto?.meta?.clientMetaDetails
    )
  );

  return { user: usersService().getUserBySocketID(props.value) ?? ClientSideUserDtoHelper.Default(), parsedMetaDetails };
}, {
  user: ClientSideUserDtoHelper.Default(),
  parsedMetaDetails: () => undefined
});