import assignWith from "lodash-es/assignWith";
import debounce, { DebouncedFunc } from "lodash-es/debounce";
import isEmpty from "lodash-es/isEmpty";
import isEqual from "lodash-es/isEqual";
import isPlainObject from "lodash-es/isPlainObject";
import memoize from "lodash-es/memoize";
import mergeWith from "lodash-es/mergeWith";

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type AnyFunction = (...args: any[]) => any;

export interface MemoizeDebouncedFunction<F extends AnyFunction>
  extends DebouncedFunc<F> {
  (...args: Parameters<F>): ReturnType<F> | undefined;
  flush: (...args: Parameters<F>) => ReturnType<F> | undefined;
  cancel: (...args: Parameters<F>) => void;
}

/**Combines Lodash's _.debounce with _.memoize to allow for debouncing
 * based on parameters passed to the function during runtime.
 * Source: https://docs.actuallycolab.org/engineering-blog/memoize-debounce/
 * @param func The function to debounce.
 * @param wait The number of milliseconds to delay.
 * @param options Lodash debounce options object.
 * @param resolver The function to resolve the cache key.
 */
export function memoizeDebounce<F extends AnyFunction>(
  func: F,
  wait = 0,
  options: _.DebounceSettings = {},
  resolver?: (...args: Parameters<F>) => unknown
): MemoizeDebouncedFunction<F> {
  const debounceMemo = memoize<(...args: Parameters<F>) => _.DebouncedFunc<F>>(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    (..._args: Parameters<F>) => debounce(func, wait, options),
    resolver
  );

  function wrappedFunction(
    this: MemoizeDebouncedFunction<F>,
    ...args: Parameters<F>
  ): ReturnType<F> | undefined {
    return debounceMemo(...args)(...args);
  }

  const flush: MemoizeDebouncedFunction<F>['flush'] = (...args) => {
    return debounceMemo(...args).flush();
  };

  const cancel: MemoizeDebouncedFunction<F>['cancel'] = (...args) => {
    return debounceMemo(...args).cancel();
  };

  wrappedFunction.flush = flush;
  wrappedFunction.cancel = cancel;

  return wrappedFunction;
}

export function getObjectDiff(obj1: any, obj2: any) {
  const diff = Object.keys(obj1).reduce((result, key) => {
    if (!obj2.hasOwnProperty(key)) {
      result.push(key);
    } else if (isEqual(obj1[key], obj2[key])) {
      const resultKeyIndex = result.indexOf(key);
      result.splice(resultKeyIndex, 1);
    }
    return result;
  }, Object.keys(obj2));

  return diff;
}

export function keepUnchangedRefsOnly(objValue: any, srcValue: any): any {
  if (objValue === undefined) { // do i need this?
    return srcValue;
  } else if (isPlainObject(objValue)) {
    return assignWith({}, objValue, srcValue, keepUnchangedRefsOnly);
  } else if (Array.isArray(objValue)) {
    if (isEmpty(objValue) && !isEmpty(srcValue)) return [...srcValue];
    else if (!isEmpty(objValue) && isEmpty(srcValue)) return objValue;
    else if (isEmpty(objValue) && isEmpty(srcValue)) return objValue; // both empty
    else return [...objValue, ...srcValue];
  }
}

export const keepRef = (objValue: any, srcValue: any): any => (
  objValue === undefined ? srcValue : mergeWith({}, objValue, srcValue, keepRef)
);