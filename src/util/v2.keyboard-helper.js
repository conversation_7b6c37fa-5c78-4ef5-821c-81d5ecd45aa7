export const MPP_KEY_MAP = {
  65: 8,
  90: 9,  // A 0 - Z key
  83: 10, // A# 0
  88: 11, // B 0
  67: 12, // C 0
  70: 13,
  86: 14, // D 0
  71: 15,
  66: 16, // E 0
  78: 17, // F 0
  74: 18,
  77: 19, // G 0
  75: 20,
  188: 21, // A 0
  76: 22,
  190: 23, // B 0
  191: 24, // C
  222: 25,

  //-----------------------------------
  49: 20,
  81: 21, // C 1
  50: 22, // C#1
  87: 23, // D 1
  69: 24, // E 1
  52: 25,
  82: 26, // F 1
  53: 27,
  84: 28, // G 1
  89: 29, // A 1
  55: 30,
  85: 31, // B 1
  56: 32,
  57: 34,
  189: 37,
  187: 39,
  //-----------------------------------
  73: 33, // C 2
  79: 35, // D 2
  80: 36, // E 2
  219: 38, // F 2
  221: 40, // G 2
};

export function keyCode_to_note_mpp(keyCode) {
  return MPP_KEY_MAP[keyCode] || -1;
};

export const VP_KEY_MAP = {
  49: 12, // C 0
  50: 14, // D 0
  51: 16, // E 0
  52: 17, // F 0
  53: 19, // G 0
  54: 21, // A 0
  55: 23, // B 0
  56: 24, // C 1
  57: 26, // D 1
  48: 28, // E 1
  81: 29, // F 1
  87: 31, // G 1
  69: 33, // A 1
  82: 35, // B 1
  84: 36, // C 2
  89: 38, // D 2
  85: 40, // E 2
  73: 41, // F 2
  79: 43, // G 2
  80: 45, // A 2
  65: 47, // B 2
  83: 48, // C 3
  68: 50, // D 3
  70: 52, // E 3
  71: 53, // F 3
  72: 55, // G 3
  74: 57, // A 3
  75: 59, // B 3
  76: 60, // C 4
  90: 62, // D 4
  88: 64, // E 4
  67: 65, // F 4
  86: 67, // G 4
  66: 69, // A 4
  78: 71, // B 4
  77: 72, // C 5
};

export function keyCode_to_note_vp(keyCode) {
  return VP_KEY_MAP[keyCode] || -1;
};

export const VP_KEY_MAP2 = {
  // Low keys
  48: 30, // F# 1
  49: 21, // A 0
  50: 22, // A# 0
  51: 23, // B 0
  52: 24, // C 1
  53: 25, // C#1
  54: 26, // D 1
  55: 27, // D#1
  56: 28, // E 1
  57: 29, // F 1
  81: 31, // G 1
  87: 32, // G#1
  69: 33, // A 1
  82: 34, // A#1
  84: 35, // B 1

  // High keys
  89: 97, // C# 7
  85: 98, // D 7
  73: 99, // D# 7
  79: 100, // E 7
  80: 101, // F 7
  65: 102, // F# 7
  83: 103, // G 7
  68: 104, // G# 7
  70: 105, // A 7
  71: 106, // A# 7
  72: 107, // B 7
  74: 108, // C 8
}

export function keyCode_to_note_vp_mod(keyCode) {
  return VP_KEY_MAP2[keyCode] || 0;
};