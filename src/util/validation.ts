import { createStore, SetStoreFunction } from "solid-js/store";

function checkValid({ element, validators = [] }: { element: HTMLInputElement; validators: any[]; }, setErrors: SetStoreFunction<{}>, errorClass: any) {
  return async () => {
    element.setCustomValidity("");
    element.checkValidity();
    let message = element.validationMessage;
    if (!message) {
      for (const validator of validators) {
        const text = await validator(element);
        if (text) {
          element.setCustomValidity(text);
          break;
        }
      }
      message = element.validationMessage;
    }
    if (message) {
      errorClass && element.classList.toggle(errorClass, true);
      setErrors({ [element.name]: message });
    }
  };
}

export function useForm({ errorClass }: { errorClass: string; }) {
  const [errors, setErrors] = createStore({} as any),
    fields = {} as any;

  const validate = (ref: HTMLInputElement, accessor: () => any[]) => {
    if (!ref) {
      console.warn("No element found for validation.");
      return;
    }

    const validators = accessor() || [];
    let config;
    fields[ref.name] = config = { element: ref, validators };
    ref.onblur = checkValid(config, setErrors, errorClass);
    ref.oninput = () => {
      if (!errors[ref.name]) return;
      setErrors({ [ref.name]: undefined });
      errorClass && ref.classList.toggle(errorClass, false);
    };
  };

  const formSubmit = (ref: HTMLFormElement, accessor: (_?: any) => () => void) => {
    const callback = accessor() as any || ((any: any) => { });
    ref.setAttribute("novalidate", "");
    ref.onsubmit = async (e: { preventDefault: () => void; }) => {
      e.preventDefault();
      let errored = false;

      for (const k in fields) {
        const field = fields[k];
        await checkValid(field, setErrors, errorClass)();
        if (!errored && field.element.validationMessage) {
          field.element.focus();
          errored = true;
        }
      }
      !errored && callback(ref);
    };
  };

  return { validate, formSubmit, errors };
}