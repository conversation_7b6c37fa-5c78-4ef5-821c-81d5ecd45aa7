import { isServer } from "solid-js/web";

const assets_url = import.meta.env.VITE_ASSETS_URL;

let self;

if (isServer) {
  //@ts-ignore
  self = {
    location: {
      host: "localhost",
      port: 80
    }
  } as any;
} else {
  self = globalThis;
}

const _IS_DESKTOP = self["__TAURI_INTERNALS__"] != null || self["__TAURI__"] != null || self["__TAURI_POST_MESSAGE__"] != null || self["__TAURI_METADATA__"] != null;

//@ts-ignore
const _TEST_MODE = import.meta.vitest != null || import.meta.env.MODE == "test" || import.meta.env.VITEST;

//@ts-ignore
const IS_AUTOMATED_TEST_MODE = self["__Cypress__"] || self["Cypress"] != null || import.meta.env.CYPRESS != null;

let appVersion = (self as any).APP_VERSION as string || import.meta.env.VITE_VERSION;
if (appVersion) appVersion = appVersion.substring(0, appVersion.indexOf("-")) || appVersion;

let OFFLINE_DEV_MODE = (self as any).OFFLINE_DEV_MODE;
if (OFFLINE_DEV_MODE) OFFLINE_DEV_MODE = JSON.parse(OFFLINE_DEV_MODE);

export const FEATURE_TOGGLE = {
  DISCORD_SDK_ENABLED: String(import.meta.env.VITE_DISCORD_SDK_ENABLED || false).toLowerCase() == "true"
} as const;

const mode = '__BUILD_ENV__' as string;

const IS_STAGING = mode == "staging";

export const COMMON = {
  OFFLINE_DEV_MODE: OFFLINE_DEV_MODE || false,
  COLYSEUS_HOST: import.meta.env.VITE_COLYSEUS_HOST,
  COLYSEUS_PORT: import.meta.env.VITE_COLYSEUS_PORT,
  HOST: import.meta.env.VITE_HOST,
  WS_HOST: import.meta.env.VITE_WS_HOST,
  DOCS_HOST: import.meta.env.VITE_DOCS_HOST,
  FULL_HOST: import.meta.env.VITE_FULL_HOST.replace("localhost", self.location.host),
  SEQ_URL: import.meta.env.VITE_SEQ_URL,
  STATUS_PAGE_URL: import.meta.env.VITE_STATUS_PAGE_URL,
  EXPRESS_API_HOST: import.meta.env.VITE_EXPRESS_API_HOST,
  ACKEE_DOMAIN_ID: import.meta.env.VITE_ACKEE_DOMAIN_ID,
  ANALYTICS_URL: import.meta.env.VITE_ANALYTICS_URL,
  METRICS_URL: import.meta.env.VITE_METRICS_URL,
  STATUS_PAGE_ID: import.meta.env.VITE_STATUS_PAGE_ID,
  CDN_URL: import.meta.env.VITE_CDN_URL,
  WEBSOCKET_PROTOCOL: import.meta.env.VITE_WS_PROTOCOL || "ws",
  PORT: IS_AUTOMATED_TEST_MODE && self.location.port ? self.location.port : parseInt(import.meta.env.VITE_HOST_PORT || "80"),
  ASSETS_URL: assets_url,
  ASSETS_PROXY_URL: import.meta.env.VITE_ASSETS_PROXY_URL,
  MODE: mode,
  SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN || "invalid-dsn",
  IS_LOCAL_DEV: OFFLINE_DEV_MODE || mode == "local-dev",
  IS_DEV_ENV: mode == "dev",
  IS_TEST_MODE: _TEST_MODE || false,
  IS_DEV_MODE: import.meta.env.DEV && !_TEST_MODE,
  IS_DESKTOP_APP: _IS_DESKTOP,
  IS_WEB_APP: !_IS_DESKTOP,
  IS_PRODUCTION: import.meta.env.PROD || (_IS_DESKTOP && mode == "local-dev-desktop"),
  IS_STAGING,
  IS_AUTOMATED_TEST_MODE,
  CLIENT_VERSION: '__APP_VERSION__',
  CLIENT_BUILD_DATE_RAW: '__DATE__',
  CLIENT_BUILD_DATE: new Date(parseInt('__DATE__')).toISOString(),
  RENDERER_WASM_FILE_PATH: "pianorhythm_core/target/wasm32-unknown-unknown/debug/pianorhythm_renderer.wasm",
  RENDERER_JS_FILE_PATH: "pianorhythm_core/pkg/pianorhythm_renderer.js",
  RENDERER_BEVY_WEBGPU_FILE_PATH: "pkg/webgpu/pianorhythm_bevy_renderer.js",
  RENDERER_BEVY_WEBGL2_FILE_PATH: "pkg/webgl2/pianorhythm_bevy_renderer.js",
  SYNTH_JS_FILE_PATH: "pkg/pianorhythm_core.js",
  CORE_JS_FILE_PATH: "pkg/pianorhythm_core.js",
  MANIA_JS_FILE_PATH: "pianorhythm_core/pkg/pianorhythm_mania.js",
  DAW_JS_FILE_PATH: "pianorhythm_synth/daw/dist/wasm/pianorhythm_daw.js",
  SOUNDFONT_CACHE_PREFIX: "CACHED:",
  USE_CORE_WASM_RENDERER: true,
  USE_ECS_RENDERER: true,
  DESKTOP_SOUNDFONTS_FOLDER: "soundfonts",
  USE_HOWLER: false
};

const MaxBackgroundImageWidth = 300;

export const FILE_UPLOAD = {
  MaxProfileImageFileSize: 1024 * 1024 * 3,
  MaxProfileBGImageFileSize: 1024 * 1024 * 5,
  MaxImageWidth: 256,
  MaxImageHeight: 256,
  MaxBackgroundImageWidth,
  MaxBackgroundImageHeight: MaxBackgroundImageWidth / 2.5,
  MaxSheetMusicFileSize: 1024 * 1024 * 10
} as const;

export const IMAGES = {
  LOGO: `${COMMON.ASSETS_URL}/images/logo.png`,
  DEFAULT_PROFILE_IMAGE: `${COMMON.ASSETS_URL}/images/logo.png`,
  DEFAULT_HELP_BOT_PROFILE_IMAGE: `${COMMON.ASSETS_URL}/images/helpbot_profile.webp`,
  DEFAULT_SHEETMUSIC_BG_IMAGE: `${COMMON.ASSETS_URL}/images/sheetMusicBG.jpg`,
  DEFAULT_MIDIMUSIC_BG_IMAGE: `${COMMON.ASSETS_URL}/images/midiMusicBG.png`
} as const;

export const CHANNELS = {
  PIANORHYTHM_SYNTH_EVENTS: "pianorhythm.synth.events",
  PIANORHYTHM_CORE_TO_RENDERER: "pianorhythm.core-to-renderer.events",
  PIANORHYTHM_RENDERER_TO_CORE_EVENTS: "pianorhythm.renderer-to-core.events",
  PIANORHYTHM_SYNTH_EMIT_SOCKET_EVENTS: "pianorhythm.synth.emit-socket.events",
  AUDIO_BUS: "audio.bus",
  CANVAS_BUS: "canvas.bus",
  CANVAS_PROCESS_SYNTH_EVENTS: "canvas.synth.events",
  AUDIO_WORKER_PROCESS_SYNTH_EVENTS: "audio-worker.synth.events",
  MANIA_BUS: "mania.bus",
  MANIA_EVENTS_BUS: "mania.events.bus",
  PRO_SUBSCRIPTION_CHECKOUT_RESULT: "pro.subscription.update",
  MIDI_UI_PLAYER_BUS: "midi.player.ui.bus",
  AUDIO_WORKER: "audio.worker",
  AUDIO_SERVICE_LISTEN_EVENTS: "audio.service.listen.events",
  PLUGIN_AUDIO_FOR_EMITTED_MESSAGES: "plugin.audio.messages",
  EMITTED_MIDI_FOR_SELFHOSTED_ROOM: "emitted.midi.self.host",
  WEBSOCKET_WORKER: "websocket.worker",
  NOTEBUFFER_EMIT: "emit.to.notebuffer",
  ECS_RENDER_DATA: "ecs.render.data"
} as const;

export const IDS = {
  CHAT_MESSAGE_SERVICE_LOADING: "initial-loading",
  SERVICES_INITIALIZATION: "services-initialize",
  AUDIO_INITIALIZATION: "audio-initialize",
  APP_LOADING: "app.loading",
  ROOM_LOADING: "room.loading",
  STAGE_LOADING: "stage.loading",
  LOADING_SOUNDFONT: "loading.soundfont",
  SOUNDFONT_LOAD_SUCCESS: "soundfont.load.success",
  USER_LOGGING_IN: "user.logging.in",
  USER_LOGGED_IN: "user.logged.in",
  DISCONNECT_MESSAGE_ID: "application.disconnect",
  WAIT_FOR_GESTURE_ACTION: "loading.wait-for-gesture",
  WEB_MIDI_DEVICE_NAME: "web-midi",
  MIDI_FILE_OPEN_ELEMENT_ID: "dock-midi-file-open",
  SERVICE_RECONNECT: "service-reconnect",
  INITIAL_LOGGIN_IN: "login-initial",
  SERVER_DISCONNECT: "server-disconnect",
  WEBMIDI_ACCESS: "webmidi-access"
} as const;

export const AUDIO = {
  U8_MAX_VALUE: 127,
  MAX_CHANNEL: 15,
  DEFAULT_TRANSPOSE: 0,
  DEFAULT_PRIMARY_CHANNEL: 0,
  MAX_TRANSPOSE: 20,
  MIN_TRANSPOSE: -20,
  DEFAULT_OCTAVE: 0,
  MIN_OCTAVE: -7,
  MAX_OCTAVE: 7,
  DEFAULT_PAN: 64,
  DEFAULT_CHANNEL_VOLUME: 100,
  DEFAULT_VELOCITY: 100,
  MAX_CHANNEL_VOLUME: 127,
  MIN_VELOCITY: 0,
  MAX_VELOCITY: 127,
  MAX_VELOCITY_USER_PERCENTAGE: 100,
  DEFAULT_POLYPHONY: 128,
  MAX_POLYPHONY: 2048,
  DEFAULT_NOTE_ON_TIME: 20,
  MAX_NOTE_ON_TIME: 60,
  MAX_REVERB_LEVEL: 1,
  MAX_REVERB_ROOMSIZE: 1,
  MAX_REVERB_DAMP: 1,
  MAX_REVERB_WIDTH: 100,
  MAX_SOUNDFONT_LOADTIME_WARNING: 1000 * 60 * 1.25,
  MAX_SOUNDFONT_LOADTIME: 1000 * 60 * 15,
  MAX_SFX_VOLUME: 1,
  MIN_SAMPLE_RATE: 8000,
  MAX_SAMPLE_RATE: 96000,
  MAX_VOLUME_RAW: 16384,
  REVERB: {
    MINIMAL: {
      AUDIO_REVERB_LEVEL: 0.9,
      AUDIO_REVERB_ROOMSIZE: 0.2,
      AUDIO_REVERB_DAMP: 0.0,
      AUDIO_REVERB_WIDTH: 0.5
    },
    HIGH: {
      AUDIO_REVERB_LEVEL: 0.17,
      AUDIO_REVERB_ROOMSIZE: 0.7,
      AUDIO_REVERB_DAMP: 0.15,
      AUDIO_REVERB_WIDTH: 25.00
    }
  }
} as const;

export const COMMANDS = {
  PREFIX: {
    RoomOwnerCommand: "$",
    ClientCommand: "/",
    ServerCommand: "//",
    BotCommand: "!",
    User: "@"
  }
} as const;

export const USER_INPUT = {
  MinUsernameLength: 3,
  MaxBaseUsernameLength: 32,
  MaxUsernameLength: 32 + 6,
  MinPasswordLength: 6,
  MaxPasswordLength: 100,
  MaxEmailLength: 254,
  MaxProfileDescriptionLength: 255,
  MaxStatusTextLength: 24,
  MaxChatMessageLength: 512,
  MaxKickTime: "-1"
} as const;

export const COMMON_MESSAGES = {
  MEMBERS_ONLY_FEATURE: "This feature is only for members. Create a free account today!",
  AUDIO_VELOCITY_PERCENTAGE_DESCRIPTION:
    `
    <br/><br/>So, for example, if the user plays a note with
    a max velocity of 127, and you have the slider set to 10%, then the value would
    be converted to 12.7.
    <br/><br/>This is to help mitigate and give you more control over any perceived loudness
    from users.
  `,
  DIFFERENT_SOUNDFONT_MESSAGE: (clientSoundfont?: string | null, soundfont?: string) => `
  This user has a <b>different soundfont loaded</b> than you do.
  <br/><br/>
  You or they may or may not hear the exact types of instruments/sounds
  that each of you play.
  <br/><br/>
  Soundfont user has: ${soundfont || "Unknown soundfont.."}
  <br/>
  Soundfont you have: ${clientSoundfont || "No soundfont loaded..."}
  `,
  CHECK_CONSOLE_LOGS: `Please check the console logs for more details.`
} as const;

export const ERROR_MESSAGES = {
  FailedServerRequest: "Request failed. A server error occurred.",
  SessionExpired: "Session expired. Please log back in.",
  KickedFromRoom: "You've been kicked from the room...",
  ExceptionOccurred: "An exception has occurred. Please try to log back in.",
  RoomDeleted: "The room was removed.",
  TimeoutFromRoom: "Connection to the room timed out.",
  WebMidiNotSupported: "WebMidi is not supported in this browser.",
  WebMidiRequestTimedOut: "WebMidi request timed out."
} as const;

export const WARNING_MESSAGES = {
  NoActiveChannels: "You have no active channels."
} as const;

export type LanguageType = [id: string, name: string];

export const DEFAULT_LANGUAGE: LanguageType = ["en", "english"];

export const LANGUAGES: LanguageType[] = [
  DEFAULT_LANGUAGE,
  ["hr", "croatian"],
  ["sr", "serbian"],
  ["ja", "japanese"],
  ["pt", "portuguese - brazil"],
  ["fr", "french"],
  ["es", "spanish"],
  ["ht", "haitian creole"],
  ["de", "german"],
  ["it", "italian"],
  ["ko", "korean"],
  ["nl", "dutch"],
  ["zh-CN", "chinese - simplified"],
  ["ru", "russian"]
];

export const VALIDATIONS = {
  passwordValidationRegex: (min: number, max: number) => `(?=.*[a-zA-Z0-9]).{${min},${max}}`,
  // usernameValidationRegex: (min: number, max: number) => `^[a-zA-Z0-9@._# ]{${min},${max}}$`
  usernameValidationRegex: (min: number, max: number) => `^(?=.{${min},${max}}$)(?![_.])(?!.*[_.]{2})[a-zA-Z0-9._]+(?<![_.])$`
};

export const DEFAULT_ROOM_ID_TO_JOIN = "lobby";
export const TransparentPixel = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAFhAJ/wlseKgAAAABJRU5ErkJggg==";

export const COMMON_KEYS_MAP = {
  "Backspace": 8,
  "Tab": 9,
  "Enter": 13,
  "Shift": 16,
  "Control": 17,
  "Ctrl": 17,
  "Alt": 18,
  "Pause": 19,
  "CapsLock": 20,
  "Escape": 27,
  "Space": 32,
  "PageUp": 33,
  "PageDown": 34,
  "End": 35,
  "Home": 36,
  "ArrowLeft": 37,
  "ArrowUp": 38,
  "ArrowRight": 39,
  "ArrowDown": 40,
  "PrintScreen": 44,
  "Insert": 45,
  "Delete": 46,
  "0": 48,
  "1": 49,
  "2": 50,
  "3": 51,
  "4": 52,
  "5": 53,
  "6": 54,
  "7": 55,
  "8": 56,
  "9": 57,
  "A": 65,
  "B": 66,
  "C": 67,
  "D": 68,
  "E": 69,
  "F": 70,
  "G": 71,
  "H": 72,
  "I": 73,
  "J": 74,
  "K": 75,
  "L": 76,
  "M": 77,
  "N": 78,
  "O": 79,
  "P": 80,
  "Q": 81,
  "R": 82,
  "S": 83,
  "T": 84,
  "U": 85,
  "V": 86,
  "W": 87,
  "X": 88,
  "Y": 89,
  "Z": 90,
  "Meta": 91,
  "ContextMenu": 93,
  "Num0": 96,
  "Num1": 97,
  "Num2": 98,
  "Num3": 99,
  "Num4": 100,
  "Num5": 101,
  "Num6": 102,
  "Num7": 103,
  "Num8": 104,
  "Num9": 105,
  "Multiply": 106,
  "Add": 107,
  "Subtract": 109,
  "Decimal": 110,
  "Divide": 111,
  "F1": 112,
  "F2": 113,
  "F3": 114,
  "F4": 115,
  "F5": 116,
  "F6": 117,
  "F7": 118,
  "F8": 119,
  "F9": 120,
  "F10": 121,
  "F11": 122,
  "F12": 123,
  "NumLock": 144,
  "ScrollLock": 145,
  ";": 186,
  "=": 187,
  ",": 188,
  "-": 189,
  ".": 190,
  "/": 191,
  "`": 192,
  "[": 219,
  "\\": 220,
  "]": 221,
  "'": 222
};

export const HTML_IDS = {
  INSTRUMENT_DOCK_CONTAINER: "instrument-dock-container",
  PIANO_CANVAS_2D_CONTAINER: "piano-canvas2D-container",
  ACTION_WIDGETS_CONTAINER: "action-widgets-container"
};
