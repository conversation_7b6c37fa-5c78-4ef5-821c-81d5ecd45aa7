/* eslint-disable no-bitwise */
import clamp from 'lodash-es/clamp';
import { logDebug } from './logger';

export class SynthAudioContext {
  static sampleRate?: number = 0;
  private static instance?: SynthAudioContext;
  context?: AudioContext;

  private constructor() {
    this.getAudioContext = this.getAudioContext.bind(this);

    if (this.context == null) {
      this.context = this.getAudioContext();
    }
  }

  private getAudioContext() {
    logDebug('Creating new AudioContext...');

    const ctx = new (window.AudioContext || (window as any).webkitAudioContext)({
      latencyHint: "interactive",
      sampleRate: Bo<PERSON>an(SynthAudioContext.sampleRate) ? clamp(SynthAudioContext.sampleRate ?? 48_000, 8_000, 96_000) : undefined
    });

    // for legacy browsers
    // @ts-ignore
    ctx.createGain = ctx.createGain || ctx.createGainNode;

    // Defreeze AudioContext
    const initAudioContext = async () => {
      document.removeEventListener('click', initAudioContext);
      // wake up AudioContext
      if (ctx.state === 'running' || ctx.state === 'closed') return;
      logDebug("Audio context woken up.");
      const emptySource = ctx.createBufferSource();
      emptySource.start();
      emptySource.stop();
      await ctx.resume();
    };

    document.addEventListener('click', initAudioContext);

    return ctx;
  }

  public static get Instance() {
    return this.instance || (this.instance = new this());
  }

  public static Dispose() {
    if (this.instance) {
      this.instance.context?.close();
      this.instance.context = undefined;
      this.instance = undefined;
    }
  }
}