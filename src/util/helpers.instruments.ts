export function getInstrumentImageFromName(input?: string) {
  const defaultInstrument = "piano";
  let instrumentName = input?.toLowerCase() ?? defaultInstrument;
  const nameContains = (target: string) => { return instrumentName.includes(target); };

  if (nameContains("acoustic")) {
    if (nameContains("piano")) return defaultInstrument;
    if (nameContains("acoustic") || nameContains("bs")) return "bass-guitar";
  } else {
    if (
      nameContains("electric")
      || nameContains("distortion")
      || nameContains("fretless")
      || nameContains("synth")
    ) {
      if (nameContains("piano")) return "piano-2";
      if (nameContains("guitar") || nameContains("gt")) return "electric_guitar";
      if (nameContains("bass")) return "electric_guitar-1";
    }

    if (nameContains("drum")) return "drum";

    if (
      nameContains("viola")
      || nameContains("violin")
      || nameContains("pizzicato")
      || nameContains("contrabass")
    ) return "violin";

    if (
      nameContains("horn") ||
      nameContains("brass"))
      return "trumpet";

    if (
      nameContains("saxophone") ||
      nameContains("sax"))
      return "saxophone";

    if (nameContains("square")) return "equalizer";
    if (nameContains("trumpet")) return "trumpet";
    if (nameContains("wave")) return "workstation-1";
    if (nameContains("accordion") || nameContains("bandoneon")) return "accordion";
    if (nameContains("guitar") || nameContains("gt")) return "electric_guitar";
    if (nameContains("dulcimer")) return "dulcimer";
    if (nameContains("koto")) return "koto";

    if (
      nameContains("celesta") ||
      nameContains("harpsichord") ||
      nameContains("honky-tonk") ||
      nameContains("honkytonk") ||
      nameContains("organ")
    )
      return "piano-1";

    if (nameContains("clavinet")) return "piano-2";
    if (nameContains("strings")) return "violin-1";
    if (nameContains("cello")) return "cello";
    if (nameContains("oboe")) return "oboe";
    if (nameContains("timpani")) return "timpani";
    if (nameContains("bagpipe")) return "bagpipe";
    if (nameContains("piccolo")) return "piccolo";
    if (nameContains("bassoon")) return "bassoon";
    if (nameContains("banjo")) return "banjo";
    if (nameContains("flute")) return "flute";
    if (nameContains("french_horn")) return "french-horn";
    if (nameContains("harmonica")) return "harmonica";
    if (nameContains("fx")) return "workstation-0";
    if (nameContains("lead")) return "workstation-1";
    if (nameContains("ukelele")) return "ukelele";
    if (nameContains("triangle")) return "triangle";
    if (nameContains("tuba")) return "tuba";
    if (nameContains("choir") || nameContains("ooh")) return "choir";
    if (nameContains("harp")) return "harp";
    if (nameContains("bell")) return "bell";
    if (nameContains("trombone")) return "trombone";
    if (nameContains("clarinet")) return "clarinet";
    if (nameContains("trumpet")) return "trumpet";
    if (nameContains("xylophone")) return "xylophone";
    if (nameContains("e.piano 1")) return "workstation-4";
    if (nameContains("e.piano 2")) return "workstation-5";
    if (nameContains("recorder")) return "recorder";
    if (nameContains("tabla")) return "tabla";
    if (nameContains("sitar")) return "sitar";
    if (nameContains("maracca")) return "maracca";
    if (nameContains("shamisen")) return "shamisen";
    if (nameContains("tambourine")) return "tambourine";
    if (nameContains("theremin")) return "theremin";
    if (nameContains("cymbals")) return "cymbals";
    if (nameContains("conga")) return "conga";
    if (
      nameContains("pan flute") ||
      nameContains("pan-flute")
    ) return "pan_flute";
  }
  return defaultInstrument;
}

export const getInstrumentImage = (instrument?: string) =>
  `/instruments/${getInstrumentImageFromName(instrument)}.png`;