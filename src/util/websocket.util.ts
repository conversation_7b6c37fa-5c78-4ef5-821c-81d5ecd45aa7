import { MidiDto } from "~/types/audio.types";
import { ClientMsg, Server<PERSON>ommand, ServerModCommand, ServerMsg, WebSocketDataEvent } from "~/types/websocket.types";
import { ClientMessage, ClientMessageType, MidiDto as MidiDtoProto } from "~/proto/client-message";
import { ChatMessageInputData_ChatMessageDataOptions, CreateRoomParam, ServerCommandDU, ServerCommandDU_CommandType, ServerMessage, ServerMessageType, ServerMessage_JoinRoomByNameRequest, ServerModCommandDto, ServerModCommandDto_ModCommandType, serverCommandDU_CommandTypeFromJSON } from "~/proto/server-message";
import { COMMON } from "~/util/const.common";
import { P, match } from "ts-pattern";
import { stringifyToZipzon } from "./zipzon.util";
import { MidiDtoType } from "~/proto/midi-renditions";
import isEmpty from "lodash-es/isEmpty";

function createDataEvent(msg: ClientMsg): WebSocketDataEvent<ClientMsg> {
  return { type: "ClientMsg", data: msg };
}

export const parseProtoClientMessage = (data: Uint8Array) => {
  // Parse proto messages
  let response = ClientMessage.decode(data);
  if (COMMON.IS_DEV_MODE) console.log("Proto Message ->", response);

  let output = match(response)
    .with({
      messageType: ClientMessageType.MaintenanceModeActive,
      boolValue: P.not(P.nullish)
    }, ({ boolValue }) => createDataEvent(["MaintenanceModeActive", boolValue]))
    .with({
      messageType: ClientMessageType.FriendStatusUpdated,
      friendDto: P.not(P.nullish),
    }, ({ friendDto }) => createDataEvent(["FriendUpdate", friendDto]))
    .with({
      messageType: ClientMessageType.GetFriendsList,
      friendDtoList: P.not(P.nullish),
    }, ({ friendDtoList }) => createDataEvent(["GetFriendsList", friendDtoList.friendDto]))
    .with({
      messageType: ClientMessageType.GetPendingFriendRequestsList,
      pendingFriendRequestList: P.not(P.nullish),
    }, ({ pendingFriendRequestList }) => createDataEvent(["GetPendingFriendRequestsList", pendingFriendRequestList.pendingFriendRequest]))
    .with({
      messageType: ClientMessageType.AddPendingFriendRequest,
      pendingFriendRequest: P.not(P.nullish),
    }, ({ pendingFriendRequest }) => createDataEvent(["AddPendingFriendRequest", pendingFriendRequest]))
    .with({
      messageType: ClientMessageType.RemovePendingFriendRequest,
      userId: P.not(P.nullish),
    }, ({ userId }) => createDataEvent(["RemovePendingFriendRequest", userId]))
    .with({
      messageType: ClientMessageType.GetKickedUsersList,
      kickedUsersList: P.not(P.nullish),
    }, ({ kickedUsersList }) => createDataEvent(["CmdResponse", ["KickedUsersInRoomList", kickedUsersList.kickedUsers]]))
    .with({
      messageType: ClientMessageType.UnloadRoomOwnerCommands,
    }, ({ }) => createDataEvent("UnloadRoomOwnerCommands"))
    .with({
      messageType: ClientMessageType.LoadRoomOwnerCommands,
    }, ({ }) => createDataEvent(["LoadRoomOwnerCommands", []]))
    .otherwise(() => { });

  return [response, output] as const;
};

function encodeMidiDto(dto: MidiDto): MidiDtoProto {
  return match(dto)
    .with(["NoteOn", P.not(P.nullish)], ([, dto]) => MidiDtoProto.fromJSON({ messageType: MidiDtoType.NoteOn, noteOn: dto }))
    .with(["NoteOff", P.not(P.nullish)], ([, dto]) => MidiDtoProto.fromJSON({ messageType: MidiDtoType.NoteOff, noteOff: dto }))
    .with(["Sustain", P.not(P.nullish)], ([, dto]) => MidiDtoProto.fromJSON({ messageType: MidiDtoType.Sustain, sustain: dto }))
    .with(["AllSoundOff", P.not(P.nullish)], ([, dto]) => MidiDtoProto.fromJSON({ messageType: MidiDtoType.AllSoundOff, allSoundOff: dto }))
    .with(P.any, () => MidiDtoProto.fromJSON({ messageType: MidiDtoType.Invalid }))
    .run();
}

function protoEncodeMessage(message: ServerMessage) {
  if (!message) return null;
  let encoded = ServerMessage.encode(message).finish();
  return encoded;
}

function serverCommandToJsonValue(commandType: ServerCommandDU_CommandType, data: any = null, compress = false) {
  let output: ServerCommandDU = { commandType };
  if (data) {
    if (Array.isArray(data) && data.length == 2) {
      let output = {};
      //@ts-ignore
      try { output[data[0] as any] = data[1]; data = output; } catch { }
    }
    output.jsonValue = compress ? stringifyToZipzon(data) : JSON.stringify(data);
  }
  return output;
}

const commandToUsertagValue = (commandType: ServerCommandDU_CommandType, usertag: string): ServerCommandDU => {
  return { commandType, usertag };
};


function serverCommandEncode(command: ServerCommand): Partial<ServerCommandDU> | null {
  return match(command)
    .with(["CreateRoom", P.any], ([, data]) => {
      return serverCommandToJsonValue(ServerCommandDU_CommandType.CreateRoom, data);
    })
    .with(["UpdateRoom", P.any], ([, data]) => {
      return serverCommandToJsonValue(ServerCommandDU_CommandType.UpdateRoom, data);
    })
    .with(["UploadOrchestraModelCustomizationData", P.any], ([, data]) => {
      return serverCommandToJsonValue(ServerCommandDU_CommandType.UploadOrchestraModelCustomizationData, data);
    })
    .with(["UploadClientSettings", P.any], ([, data]) => {
      return serverCommandToJsonValue(ServerCommandDU_CommandType.UploadClientSettings, data, true);
    })
    .with(["Join", P.string], ([_, roomIDorName]) => {
      return { commandType: ServerCommandDU_CommandType.Join, roomIDorName, commandData: roomIDorName };
    })
    .with(["JoinRoomWithPassword", P.string, P.string], ([_, roomID, password]) => {
      let roomIDAndPassword = { roomID, password };
      return { commandType: ServerCommandDU_CommandType.JoinRoomWithPassword, roomIDAndPassword, commandData: roomIDAndPassword };
    })
    .with(["IsTyping", P.boolean], ([_, boolean]) => {
      return { commandType: ServerCommandDU_CommandType.IsTyping, boolean };
    })
    .with(["DeleteChatMessageById", P.string], ([, data]) => {
      return ServerCommandDU.create({ commandType: ServerCommandDU_CommandType.DeleteChatMessageById, stringValue: data });
    })
    .with(["DeleteChatMessageByUsertag", P.string], ([, data]) => {
      return ServerCommandDU.create({ commandType: ServerCommandDU_CommandType.DeleteChatMessageByUsertag, usertag: data });
    })
    .with(["SendFriendRequest", P.string], ([, usertag]) => commandToUsertagValue(ServerCommandDU_CommandType.SendFriendRequest, usertag))
    .with(["SendUnfriendRequest", P.string], ([, usertag]) => commandToUsertagValue(ServerCommandDU_CommandType.SendUnfriendRequest, usertag))
    .with(["AcceptFriendRequest", P.string], ([, usertag]) => commandToUsertagValue(ServerCommandDU_CommandType.AcceptFriendRequest, usertag))
    .with(["DenyFriendRequest", P.string], ([, usertag]) => commandToUsertagValue(ServerCommandDU_CommandType.DenyFriendRequest, usertag))
    .with([P.string, P.any], ([typeName, data]) => {
      return serverCommandToJsonValue(ServerCommandDU_CommandType[typeName], data);
    })
    .with([P.string], ([typeName]) => {
      return serverCommandToJsonValue(serverCommandDU_CommandTypeFromJSON(typeName));
    })
    .otherwise(() => null);
}

function serverModCommandEncode(command: ServerModCommand): Partial<ServerModCommandDto> | null {
  return match(command)
    .with(["BanUser", P.string], ([_, usertag]) => {
      return { commandType: ServerModCommandDto_ModCommandType.BanUser, usertag };
    })
    .with(["ClearChat"], () => {
      return { commandType: ServerModCommandDto_ModCommandType.ClearChat };
    })
    .with(["AddBadge", P.not(P.nullish)], ([_, badge]) => {
      return { commandType: ServerModCommandDto_ModCommandType.AddBadge, userBadge: badge };
    })
    .with(["RemoveBadge", P.not(P.nullish)], ([_, badge]) => {
      return { commandType: ServerModCommandDto_ModCommandType.RemoveBadge, userBadge: badge };
    })
    .with(["UnbanIp", P.string], ([_, stringValue]) => {
      return { commandType: ServerModCommandDto_ModCommandType.UnbanIp, stringValue };
    })
    .with(["UnbanAccount", P.string], ([_, stringValue]) => {
      return { commandType: ServerModCommandDto_ModCommandType.UnbanAccount, stringValue };
    })
    .otherwise(() => null);
}

export const handleServerMessageEncode = (payload: ServerMsg) => {
  return match(payload)
    .with(["MidiMessage", P.any], ([, dto]) => {
      let message: any =
      {
        messageType: ServerMessageType.MidiMessage,
        midiMessageInputDto: {
          time: dto.time,
          data: dto.data.map(x => ({ data: encodeMidiDto(x.data), delay: x.delay }))
        }
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["Disconnect"], () => {
      let message = {
        messageType: ServerMessageType.Disconnect,
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["RoomChatServerCommand", P.any], ([, command]) => {
      let message = {
        messageType: ServerMessageType.RoomChatServerCommand,
        roomChatServerCommand: { command: command.command }
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["RoomChatMessage", P.any], ([, chatMessageInputData]) => {
      if (isEmpty(chatMessageInputData.text)) return null;

      let modificationType = chatMessageInputData.options.modificationType;

      let options = ChatMessageInputData_ChatMessageDataOptions.fromJSON({
        syncToDiscord: chatMessageInputData.options.syncToDiscord,
        isFromPlugin: chatMessageInputData.options.isFromPlugin || false,
        messageReplyID: modificationType?.[0] == "Reply" ? modificationType?.[1] : undefined,
        messageEditID: modificationType?.[0] == "Edit" ? modificationType?.[1] : undefined,
      });

      let message: ServerMessage = {
        messageType: ServerMessageType.RoomChatMessage,
        chatMessageInputData: { text: chatMessageInputData.text, options }
      };

      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["ServerCommand", P.any], ([, command]) => {
      let serverCommand = serverCommandEncode(command);
      if (!serverCommand) return null;

      let message = {
        messageType: ServerMessageType.ServerCommand,
        serverCommand
      };

      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["ServerModCommand", P.string, P.any], ([, usertag, command]) => {
      let serverModCommand = serverModCommandEncode(command);
      if (!serverModCommand) return null;

      let message = {
        messageType: ServerMessageType.ServerModCommand,
        serverModCommand: { usertag, serverCommand: serverModCommand }
      };

      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["CreateRoomCommand", P.any], ([_, data]) => {
      let message: any =
      {
        messageType: ServerMessageType.CreateRoomCommand,
        createRoomParam: CreateRoomParam.fromPartial(data)
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["UpdateRoomCommand", P.any], ([_, data]) => {
      let message: any =
      {
        messageType: ServerMessageType.UpdateRoomCommand,
        createRoomParam: CreateRoomParam.fromPartial(data)
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["JoinRoomByName", P.string, P.boolean], ([_, roomName, createRoomIfNotExist]) => {
      let message: ServerMessage =
      {
        messageType: ServerMessageType.JoinRoomByName,
        joinRoomRequest: ServerMessage_JoinRoomByNameRequest.create({
          roomName,
          createRoomIfNotExist
        })
      };
      return protoEncodeMessage(ServerMessage.create(message));
    })
    .with(["RawProto", P.any], ([, data]) => {
      return protoEncodeMessage(data);
    })
    .otherwise(() => null);
};
