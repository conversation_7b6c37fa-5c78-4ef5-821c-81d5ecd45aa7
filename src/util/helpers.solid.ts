import { useNavigate } from "@solidjs/router";
import { Accessor, createReaction } from "solid-js";
import { GetMemberSessionInfo } from "~/lib";
import NotificationService from "~/services/notification.service";

export const onCheckMemberSession = (
  user: Accessor<GetMemberSessionInfo | undefined | null>,
  onNotAuthenticated?: () => void,
  onAuthenticated?: (user: GetMemberSessionInfo) => void
) => createReaction(async () => {
  if (user() === null) {
    const navigate = useNavigate();

    // User is not authenticated. Redirect to login page.
    NotificationService.show({
      title: "User not authenticated",
      description: "Please login to continue...",
      type: "warning",
      duration: 5000,
    });

    onNotAuthenticated?.();
    navigate("/login", { replace: true });
    console.log("User not authenticated. Redirecting to login page...");
    return;
  }

  if (user()) onAuthenticated?.(user()!);
});