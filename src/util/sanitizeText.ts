import sanitizeHtml from "sanitize-html";

/**
 * Sanitizes the input HTML string by allowing only specific tags and attributes.
 *
 * @param input - The HTML string to be sanitized.
 * @returns The sanitized HTML string.
 *
 * The function uses `sanitizeHtml` to remove any unwanted tags and attributes.
 * It allows the following tags and their respective attributes:
 * - `text`: `color`, `bg`, `background`, `background-color`, `padding`, `border`, `border_radius`, `border-radius`
 * - `color`: `value`, `color`
 * - `user`: `uuid`
 */
export const sanitizeText = (input: string) => {
  if (!input) return input;

  return sanitizeHtml(input, {
    allowedAttributes: {
      ...sanitizeHtml.defaults.allowedAttributes,
      "text": ["color", "bg", "background", "background-color", "padding", "border", "border_radius", "border-radius"],
      "color": ["value", "color"],
      "user": ["uuid"]
    },
    allowedTags: ["color", "text", "user"]
  });
};

/**
 * Sanitizes the input text for notifications by removing all HTML tags and attributes.
 *
 * @param input - The input string to be sanitized.
 * @returns The sanitized string with all HTML tags and attributes removed.
 */
export const sanitizeTextForNotifications = (input: string) => {
  if (!input) return input;

  return sanitizeHtml(input, {
    allowedAttributes: undefined,
    allowedTags: []
  });
};

