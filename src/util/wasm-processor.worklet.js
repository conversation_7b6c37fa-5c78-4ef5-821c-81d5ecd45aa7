import * as core_wasm from '@core/pkg/pianorhythm_core';

/**
 * An AudioWorkletProcessor that uses a WebAssembly module to process audio.
 * It communicates with the main thread for MIDI data and uses a SharedArrayBuffer
 * for low-latency event handling.
 */
class WasmProcessor extends AudioWorkletProcessor {
    // A separate, static async method is used to handle WASM initialization.
    // The constructor of an AudioWorkletProcessor must be synchronous.
    static async initialize(options) {
        const [module, memory, handle] = options.processorOptions;
        // Wait for the WASM module to be compiled and instantiated.
        await core_wasm.default(module, memory);
        // Unpack the handle to get the processor instance.
        return core_wasm.WasmAudioProcessor.unpack(handle);
    }

    constructor(options) {
        super();
        let [_module, _memory, _handle, _bufferSize] = options.processorOptions;
        this.crashed = false;
        this.numOfChannels = options.outputChannelCount[0] ?? 1;

        // Will be set via message
        this.sharedBuffer = null;
        this.synthEventsSharedArrayBuffer = null;
        this.int32View = null;
        this.uint8View = null;
        this.clientSocketID = null;

        // Queue for scheduled events
        this.scheduledEvents = [];
        this.port.onmessage = (e) => {
            switch (e.data.type) {
                case "midi-data": {
                    this.processor?.parse_midi_data(e.data.data, null, null, null);
                    break;
                }
                case "set_shared_buffer": {
                    this.sharedBuffer = e.data.buffer;
                    // The first 4 * 4 = 16 bytes are for Int32Array
                    this.int32View = new Int32Array(this.sharedBuffer, 0, 4);
                    this.uint8View = new Uint8Array(this.sharedBuffer, 16); // 4 int32s = 16 bytes
                    console.log('Shared buffer received in audio worklet');
                    break;
                }
                case "set_synth_events_shared_buffer": {
                    this.synthEventsSharedArrayBuffer = e.data.buffer;
                    this.synthEventBuffer = {
                        buffer: this.synthEventsSharedArrayBuffer,
                        header: new Int32Array(this.synthEventsSharedArrayBuffer, 0, 4),
                        dataBuffer: new Uint8Array(this.synthEventsSharedArrayBuffer, 4 * 4, 1024 * 64), // After 4 * 4 bytes header
                        maxEventSize: 1024,
                        maxEvents: 64
                    };
                    break;
                }
                case "set-client-socket-id": {
                    this.clientSocketID = e.data.socketID;
                    break;
                }
                case "web-midi-event": {
                    // { id: string, name: string, active: boolean, type: "input" | "output" }
                    let event = e.data.event;
                    if (typeof event.type == "string" && event.type == "input") {
                        this.processor?.handle_midi_input_connection(event.name, event.active);
                    } else if (typeof event.type == "string" && event.type == "output") {
                        this.processor?.handle_midi_output_connection(event.name, event.active);
                    }
                    console.log("web-midi-event", event);
                    break;
                }
                default:
                    console.warn("Unknown message type:", e.data.type);
            }
        };

        // Pre-allocate a small buffer for reading headers to avoid allocation in the loop.
        this.headerBuffer = new ArrayBuffer(12);
        this.headerUint8View = new Uint8Array(this.headerBuffer);

        // --- Asynchronous Initialization ---
        // We start the initialization process here but don't block the constructor.
        WasmProcessor.initialize(options)
            .then(processor => {
                this.processor = processor;
                this.port.postMessage({
                    type: "worklet-initialized"
                });
                console.log("WASM AudioWorklet processor initialized successfully.");
            })
            .catch(error => {
                console.error("[Audio Worklet Initialization Error]", error);
                this.crashed = true;
                this.port.postMessage({
                    type: "worklet-failed",
                    error
                });
            });
    }

    writeSynthEvent(synthEvent) {
        const buffer = this.synthEventBuffer;
        if (!buffer) return false;
        const writeIdx = buffer.header[0]; // WRITE_INDEX
        const readIdx = buffer.header[1]; // READ_INDEX
        const nextWrite = (writeIdx + 1) % buffer.maxEvents;

        // Check if buffer is full
        if (nextWrite === readIdx) {
            console.warn('Synth event buffer full, dropping event');
            return false;
        }

        // Serialize event
        const eventData = JSON.stringify(synthEvent);
        const eventBytes = new TextEncoder().encode(eventData);
        if (eventBytes.length > buffer.maxEventSize - 4) {
            console.warn('Synth event too large, dropping');
            return false;
        }

        // Write to buffer
        const offset = writeIdx * buffer.maxEventSize;
        buffer.dataBuffer[offset] = eventBytes.length & 0xFF;
        buffer.dataBuffer[offset + 1] = (eventBytes.length >> 8) & 0xFF;
        buffer.dataBuffer[offset + 2] = (eventBytes.length >> 16) & 0xFF;
        buffer.dataBuffer[offset + 3] = (eventBytes.length >> 24) & 0xFF;
        for (let i = 0; i < eventBytes.length; i++) {
            buffer.dataBuffer[offset + 4 + i] = eventBytes[i];
        }

        // Update write index atomically
        Atomics.store(buffer.header, 0, nextWrite);
        return true;
    }

    process(_, outputs) {
        if (!this.processor || this.crashed) return true;
        const output = outputs[0];

        // Ensure we have output data
        if (!output || !output.length) return true;

        // Process audio through WASM
        if (this.numOfChannels === 2) {
            this.processor.process_stereo(output[0], output[1]);
        } else {
            this.processor.process(output[0]);
        }

        // Read from shared buffer if available
        if (this.int32View && this.uint8View) {
            this.readEventsFromSharedBuffer();
        }

        // Process all events that are due. The queue is sorted by time.
        while (this.scheduledEvents.length > 0 && this.scheduledEvents?.[0]?.scheduledTime <= currentTime) {
            const event = this.scheduledEvents.shift();
            this.processEventByType(event.eventData, event.eventType);
        }

        return true;
    }

    processEventByType(eventData, eventType) {
        const socketHashedID = eventData[eventData.length - 1];
        const source = eventData[eventData.length - 2];
        let is_client = this.clientSocketID === socketHashedID;

        if (eventType == 1) {
            // Note On - use from_socket_note_on
            if (eventData.length >= 11) {
                const channel = eventData[0] & 0x0F;
                const note = eventData[1];
                const velocity = eventData[2];
                const program = eventData[3];
                const bank = (eventData[4] | (eventData[5] << 8) | (eventData[6] << 16) | (eventData[7] << 24));
                const volume = eventData[8];
                const pan = eventData[9];
                const noteOnEvent = {
                    channel,
                    note,
                    velocity,
                    program: program,
                    volume: volume,
                    bank: bank,
                    expression: undefined,
                    pan: pan,
                    source: source,
                };
                this.processor?.from_socket_note_on(noteOnEvent, socketHashedID);
                let synthEvent = {
                    current_program: noteOnEvent.program,
                    current_bank: noteOnEvent.bank,
                    current_volume: noteOnEvent.volume,
                    current_expression: noteOnEvent.expression,
                    current_pan: noteOnEvent.pan,
                    source: noteOnEvent.source,
                    socket_id: socketHashedID,
                    is_client,
                    raw_bytes: [0x90 + channel, note, velocity],
                };
                this.writeSynthEvent(synthEvent);
            }
        } else if (eventType == 2) {
            // Note Off - use from_socket_note_off
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const note = eventData[1];
                this.processor?.from_socket_note_off(channel, note, socketHashedID);
                let synthEvent = {
                    source: source,
                    socket_id: socketHashedID,
                    is_client,
                    raw_bytes: [0x80 + channel, note, 0],
                };
                this.writeSynthEvent(synthEvent);
            }
        } else if (eventType == 3) {
            // Sustain - use parse_midi_data (no specific from_socket_sustain method)
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const value = eventData[1];
                // MIDI Control Change: 0xB0 + channel, controller 64 (sustain), value
                const midiData = [0xB0 + channel, 64, value];
                this.processor?.parse_midi_data(midiData, socketHashedID, source, null);
                let synthEvent = {
                    channel: channel,
                    source: source,
                    socket_id: socketHashedID,
                    is_client,
                    raw_bytes: midiData,
                };
                this.writeSynthEvent(synthEvent);
            }
        } else if (eventType == 4) {
            // All Sound Off - use all_sounds_off method
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                this.processor?.all_sounds_off(channel, socketHashedID);
                let synthEvent = {
                    channel: channel,
                    source: source,
                    socket_id: socketHashedID,
                    is_client,
                    raw_bytes: [0xB0 + channel, 120, 0],
                };
                this.writeSynthEvent(synthEvent);
            }
        } else if (eventType == 5) {
            // Pitch Bend - use from_socket_pitch
            if (eventData.length >= 4) {
                const channel = eventData[0] & 0x0F;
                const lsb = eventData[1];
                const msb = eventData[2];
                // Combine LSB and MSB into a single value (14-bit pitch bend value)
                const value = (msb << 7) | lsb;
                const pitchBendEvent = {
                    channel,
                    value,
                };
                this.processor?.from_socket_pitch(pitchBendEvent, socketHashedID);
            }
        }
    }

    /**
     * Reads and processes events from the circular buffer in the SharedArrayBuffer.
     * Updated to handle the new Vec<u32> format from Rust.
     */
    readEventsFromSharedBuffer() {
        // This is a Single-Producer, Single-Consumer (SPSC) queue. As the only
        // consumer, we don't need to lock when reading. We only need to read
        // indices atomically.
        let readIdx = Atomics.load(this.int32View, 1); // READ_INDEX
        const writeIdx = Atomics.load(this.int32View, 0); // WRITE_INDEX
        const bufferSize = this.uint8View.length;
        while (readIdx !== writeIdx) {
            // New header format: delay_low (4) + delay_high (4) + event_type (4) = 12 bytes
            const headerSize = 12;

            // Check if a full header is available to be read.
            const availableData = (writeIdx - readIdx + bufferSize) % bufferSize;
            if (availableData < headerSize) {
                break; // Not enough data for a full header.
            }

            // --- Read Event Header (Handles Buffer Wrap-Around) ---
            // CRITICAL FIX: Manually copy bytes to a local buffer to handle cases
            // where the header itself wraps around the end of the shared buffer.
            for (let i = 0; i < headerSize; i++) {
                this.headerUint8View[i] = this.uint8View[(readIdx + i) % bufferSize];
            }

            // Reconstruct the f64 delay from two u32 values (little-endian)
            const delayLow = new Uint32Array(this.headerBuffer, 0, 1)[0];
            const delayHigh = new Uint32Array(this.headerBuffer, 4, 1)[0];

            // Combine the two u32 values back into an f64
            const delayBytes = new Uint8Array(8);
            const delayLowBytes = new Uint8Array(new Uint32Array([delayLow]).buffer);
            const delayHighBytes = new Uint8Array(new Uint32Array([delayHigh]).buffer);
            delayBytes.set(delayLowBytes, 0);
            delayBytes.set(delayHighBytes, 4);
            const delayMs = new Float64Array(delayBytes.buffer)[0];
            // Read event type as u32
            const eventType = new Uint32Array(this.headerBuffer, 8, 1)[0];
            let currentPos = (readIdx + headerSize) % bufferSize;

            // Now we need to read the event data as u32 values
            // The event data length is variable and depends on the event type
            // We need to determine how many u32 values to read based on the event type
            let dataLengthInU32s;
            switch (eventType) {
                case 1: // Note On
                    dataLengthInU32s = 12; // 12 u32 values for Note On
                    break;
                case 2: // Note Off
                    dataLengthInU32s = 5; // 5 u32 values for Note Off
                    break;
                case 3: // Sustain
                    dataLengthInU32s = 5; // 5 u32 values for Sustain
                    break;
                case 4: // All Sound Off
                    dataLengthInU32s = 5; // 5 u32 values for All Sound Off
                    break;
                case 5: // Pitch Bend
                    dataLengthInU32s = 5; // 5 u32 values for Pitch Bend
                    break;
                default:
                    console.warn("Unknown event type:", eventType);
                    // Skip this event by advancing read index
                    readIdx = (readIdx + headerSize) % bufferSize;
                    Atomics.store(this.int32View, 1, readIdx);
                    continue;
            }

            const dataLengthInBytes = dataLengthInU32s * 4;

            // Check if the full event payload is available.
            const availablePayload = (writeIdx - currentPos + bufferSize) % bufferSize;
            if (availablePayload < dataLengthInBytes) {
                break; // The full event data has not been written yet.
            }

            // --- Read Event Data as u32 values ---
            const eventData = new Uint32Array(dataLengthInU32s);
            for (let i = 0; i < dataLengthInU32s; i++) {
                // Read 4 bytes at a time and convert to u32
                const byteOffset = currentPos + (i * 4);
                const bytes = new Uint8Array(4);
                for (let j = 0; j < 4; j++) {
                    bytes[j] = this.uint8View[(byteOffset + j) % bufferSize];
                }
                eventData[i] = new Uint32Array(bytes.buffer)[0];
            }

            // --- Queue The Event ---
            const scheduledTime = currentTime + (delayMs / 1000.0); // Account for block size;
            console.log('Scheduling event at', scheduledTime, 'with delay', delayMs, 'and type', eventType);
            const newEvent = {
                scheduledTime,
                eventType,
                eventData
            };

            // Insert into sorted queue.
            const index = this.scheduledEvents.findIndex(evt => evt.scheduledTime >= scheduledTime);
            if (index === -1) {
                this.scheduledEvents.push(newEvent);
            } else {
                this.scheduledEvents.splice(index, 0, newEvent);
            }
            // --- Update Read Index ---
            readIdx = (currentPos + dataLengthInBytes) % bufferSize;
            Atomics.store(this.int32View, 1, readIdx); // Atomically update READ_INDEX.
        }
    }
}

registerProcessor("WasmProcessor", WasmProcessor);