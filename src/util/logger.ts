import { debug, error, info, warn, trace } from '@tauri-apps/plugin-log';
import { COMMON } from './const.common';
import { isString } from 'lodash-es';

const overwriteConsoleLog = () => {
  let _log = console.log;
  let _error = console.error;
  let _warning = console.warn;
  let _info = console.info;
  let _debug = console.debug;

  //@ts-ignore
  console.orig_log = _log;

  let logStyle = (color: string) => [
    "font-size: 10px",
    `color: gray`,
    "padding: 2px",
    "border: 1px solid gray",
    `border-left: 5px solid ${color}`,
    "border-radius: 3px"
  ].join(";");

  const isStylized = (args: IArguments) =>
    (args && isString(args[0]) && (args[0] as string).includes("%c"));

  console.log = function (logMessage) {
    // Solid context menu is a 3rd party library that is printing to the console outside of my control. :(
    if (isString(logMessage) && logMessage.includes("solid-contextmenu")) return;

    //@ts-ignore
    if (isStylized(arguments)) return _log.apply(console, arguments);

    //@ts-ignore
    [].unshift.call(arguments, `%c[${(new Date()).toLocaleTimeString('en-US')}]`, logStyle("gray"));
    //@ts-ignore
    _log.apply(console, arguments);
  };

  console.info = function (infoMessage) {
    //@ts-ignore
    if (isStylized(arguments)) return _info.apply(console, arguments);

    //@ts-ignore
    [].unshift.call(arguments, `%c[${(new Date()).toLocaleTimeString('en-US')}]`, logStyle("white"));
    //@ts-ignore
    _info.apply(console, arguments);
  };

  console.debug = function (infoMessage) {
    //@ts-ignore
    if (isStylized(arguments)) return _debug.apply(console, arguments);

    //@ts-ignore
    [].unshift.call(arguments, `%c[${(new Date()).toLocaleTimeString('en-US')}]`, logStyle("cornflowerblue"));
    //@ts-ignore
    _debug.apply(console, arguments);
  };

  console.warn = function (warnMessage) {
    //@ts-ignore
    if (isStylized(arguments)) return _warning.apply(console, arguments);

    //@ts-ignore
    [].unshift.call(arguments, `%c[${(new Date()).toLocaleTimeString('en-US')}]`, logStyle("yellow"));
    //@ts-ignore
    _warning.apply(console, arguments);
  };

  console.error = function (message) {
    //@ts-ignore
    if (isStylized(arguments)) return _error.apply(console, arguments);

    //@ts-ignore
    [].unshift.call(arguments, `%c[${(new Date()).toLocaleTimeString('en-US')}]`, logStyle("red"));
    //@ts-ignore
    _error.apply(console, arguments);
  };
};

if ((COMMON.IS_PRODUCTION || COMMON.IS_STAGING) && !COMMON.IS_DEV_MODE) overwriteConsoleLog();
export const logInfo = COMMON.IS_WEB_APP ? console.info : info;
export const logError = COMMON.IS_WEB_APP ? console.error : error;
export const logDebug = COMMON.IS_WEB_APP ? console.debug : debug;
export const logWarn = COMMON.IS_WEB_APP ? console.warn : warn;
export const logTrace = COMMON.IS_WEB_APP ? console.trace : trace;