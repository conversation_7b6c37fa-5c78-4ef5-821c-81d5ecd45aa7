import anime from 'animejs';

export function onRemoveAnimeInstance(element: HTMLElement) {
  if (!element) return;
  anime.remove(element);
}

export const setProfileCardNearSidebarItem = (sideBarElementID: string, cardElement?: HTMLDivElement) => {
  let sidbarElement = document.querySelector(sideBarElementID);
  if (!sidbarElement) return null;

  let bounds = sidbarElement.getBoundingClientRect();
  let targetY = bounds.y;
  if (targetY + bounds.height > (window.innerHeight / 2)) {
    targetY = 15;
  }

  let targetX = bounds.x + bounds.width + 10;

  if (cardElement != null) {
    cardElement.style.transform = `translateX(${targetX}px)`;

    onRemoveAnimeInstance(cardElement);
    anime({
      targets: cardElement,
      opacity: [0, 1],
      translateY: targetY,
      delay: 10,
      duration: 200,
      easing: "easeInOutExpo"
    });
  }

  return targetY;
};