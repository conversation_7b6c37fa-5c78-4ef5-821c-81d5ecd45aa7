import { Subject } from "rxjs";

type ContextMenuEvent = {
  type: "shown" | "hidden";
  element?: HTMLElement;
};

let events = new Subject<ContextMenuEvent>();
let _useLeftClick = false;

export type FunctionVal<T, A extends Array<any> = undefined[]> = T | ((elem: Element | undefined, ...args: A) => T);

export type ItemButton = {
  type: "button",
  value: FunctionVal<string>,
  hotkey?: FunctionVal<string>,
  onclick?: (this: Element) => void,
};

export type ItemSubmenu = {
  type: "submenu",
  value: FunctionVal<string>,
  items: FunctionVal<Item[]>;
};

export type ItemLine = {
  type: "line",
};

export type Item = {
  icon?: HTMLImageElement,
  enabled?: FunctionVal<boolean>; // true
  visibility?: FunctionVal<boolean>; // true
} & (ItemButton | ItemSubmenu | ItemLine);

function getValue<T, A extends Array<any> = any[]>(val: FunctionVal<T, A>, defaultVal?: T, ...args: A): T {
  return typeof val == "function" ? (<any>val)(clickElement, ...args) : val ?? defaultVal;
}

export function useContextMenu(
  element: Element | NodeListOf<Element>,
  items: FunctionVal<Item[], [MouseEvent]>,
  onShown?: (() => void),
  onHidden?: (() => void),
  useLeftClick?: boolean
) {
  _useLeftClick = useLeftClick || false;
  // let temp_elements = new Set<Element>();
  (element instanceof Element ? [element] : [...(element as any)]).forEach(elem => {
    // if (_elements.has(elem)) return;
    // _elements.add(elem)
    // temp_elements.add(elem)
    if (!(elem instanceof HTMLElement)) return;
    elem.tabIndex = 0;
    if (useLeftClick) {
      elem.onmousedown = (evt: MouseEvent) => {
        if (evt.button == 0) execute(elem, items, evt);
      };
    } else {
      elem.oncontextmenu = e => {
        execute(elem, items, e);
      };
    }
  });

  return events.subscribe(event => {
    if (event.type == "shown" && event.element == element && onShown) onShown();
    if (event.type == "hidden") {
      if (event.element == element && onHidden) onHidden();
      // temp_elements.forEach(x => _elements.delete(x))
    }
  });
}

export function execute(elem: HTMLElement | undefined, items: FunctionVal<Item[], [MouseEvent]>, e: MouseEvent) {
  if (!CONFIG.useStyle) contextmenuElem.removeAttribute("style");
  contextmenuElem.style.position = "fixed";

  if (elem) clickElement = elem;
  let _items = getValue<Item[]>(items, undefined, e, 1);
  if (_items.length == 0) return;
  setItems(contextmenuElem, _items);
  show(elem);

  let x = e.clientX, y = e.clientY;
  x + contextmenuElem.offsetWidth > innerWidth && (x -= contextmenuElem.offsetWidth);
  y + contextmenuElem.offsetHeight > innerHeight && (y -= contextmenuElem.offsetHeight);

  contextmenuElem.style.left = x + "px";
  contextmenuElem.style.top = y + "px";
}

export const CONFIG: {
  hover: boolean,
  useStyle: boolean;
} = {
  hover: true,
  useStyle: true
};

const defaultCssText = `
    box-sizing: border-box;
    position: fixed;
    display: flex;
    flex-direction: column;
    white-space: nowrap;

    font-size: 12px;
    font-family: Arial;
    cursor: default;
    padding: 3px 0;
    outline: none;
    user-select: none;
`;
let hoverColor: string;
let _style: "dark";// | "light";

export let contextmenuElem: HTMLDivElement = __createElem();
contextmenuElem.id = "__my-context-menu";
contextmenuElem.tabIndex = 0;

window.addEventListener("mousedown", hide);

function __createElem(left: number = 0, top: number = 0, absolute = false) {
  let e = document.createElement("div");
  e.classList.add("__my-context-menu");

  if (CONFIG.useStyle) {
    e.style.cssText = absolute ? defaultCssText.replace("fixed", "absolute") : defaultCssText;
    setStyle(e, _style ?? "dark");
  }
  e.style.cssText += `
        left: ${left}px;
        top: ${top}px;
    `;
  return e;
}

let clickElement: Element;
let lastShownTargetContextElement: HTMLElement | undefined = undefined;

function show(contextElement?: HTMLElement) {
  document.body.append(contextmenuElem);
  events.next({ type: "shown", element: contextElement });
  lastShownTargetContextElement = contextElement;
  return contextmenuElem;
}

export function hide() {
  contextmenuElem.remove();
  events.next({ type: "hidden", element: lastShownTargetContextElement });
  return contextmenuElem;
}

function setItems(elem: HTMLDivElement, items: Item[]) {
  elem.innerHTML = "";

  items.flat().filter(Boolean).forEach(i => {
    if (getValue(i.visibility, true) === false) return;
    let div = document.createElement("div");
    div.classList.add(`type-${i.type}`);
    let defaultCss = `
            position: relative;
            display: flex;
            justify-content: space-between;
            flex-direction: row;
            padding: 6px 23px 4px 23px;
            font-weight: 100;
        `;

    if (i.type == "line") {
      let bg = _style == "dark" ? "#3c4043" : "#cfcfcf";
      if (CONFIG.useStyle) div.style.cssText += defaultCss + `background: ${bg}; height: 1px; padding: 0; margin: 3px 0;`;
    } else {
      if (CONFIG.useStyle) div.style.cssText = defaultCss;
      if (i.icon) {
        if (CONFIG.useStyle) i.icon.style.cssText = `
                    position: absolute;
                    left: calc(23px / 2);
                    transform: translateX(-50%);
                    height: 12px;
                `;
        div.append(i.icon);
      }
      div.innerHTML += getValue(i.value);
    }

    if (i.type == "button" && i.hotkey) {
      let __div = document.createElement("div");
      __div.classList.add(`type-hotkey`);
      __div.innerHTML = getValue(i.hotkey);
      if (CONFIG.useStyle) {
        __div.style.cssText = `
                    margin-left: 50px;
                    opacity: .5;
                `;
      }
      div.append(__div);
    }


    if (!getValue(i.enabled, true)) {
      div.toggleAttribute("disabled");
      if (CONFIG.useStyle) div.style.opacity = ".5";
    } else if (i.type !== "line") {
      div.addEventListener("mouseover", () => {
        if (CONFIG.hover && CONFIG.useStyle) div.style.background = hoverColor;
      });
      div.addEventListener("mouseout", () => {
        if (CONFIG.hover && CONFIG.useStyle) div.style.background = "none";
      });

      if (i.type == "button") i.onclick && div.addEventListener("mousedown", i.onclick);
      else if (i.type == "submenu") {
        div.addEventListener("mouseenter", () => {
          let e: HTMLDivElement = __createElem(div.offsetWidth, -4, true);
          div.append(setItems(e, getValue(i.items)));

          let { bottom, right } = e.getBoundingClientRect();
          right + 10 > innerWidth && (e.style.left = -e.offsetWidth + "px");
          bottom + 10 > innerHeight && (e.style.top = innerHeight - bottom - 10 + "px");

          if (CONFIG.useStyle) e.style.zIndex = (div.style.zIndex ?? 0) + 1;
        });
        div.addEventListener("mouseout", e => {
          for (let el = e.relatedTarget as HTMLElement; el && el != contextmenuElem; el = el?.parentNode as HTMLElement)
            if (el == div) return;

          if (CONFIG.hover && CONFIG.useStyle) div.style.background = "none";
          if (i.type == "submenu") div.querySelector(".__my-context-menu")?.remove();
        });


        let arrow = document.createElement("div");
        let __div = document.createElement("div");
        arrow.classList.add("__my-context-menu-arrow");
        arrow.append(__div);

        if (CONFIG.useStyle) {
          arrow.style.cssText = `
                        position: absolute;
                        right   : 10px;
                        top     : 50%;
                        transform: translateY(-50%);
                        pointer-events: none;
                    `;
          __div.style.cssText = `
                        border-top: 1px solid;
                        border-right: 1px solid;
                        border-color: #FFFFFF;

                        width     : 5px;
                        height    : 5px;

                        transform: rotate(45deg);
                    `;
        }

        div.append(arrow);
      }
    }
    elem.append(div);
  });

  return elem;
}

function setStyle(elem: HTMLDivElement, style: typeof _style) {
  _style = style;
  if (!CONFIG.useStyle) return elem;
  hoverColor = "#3f4042";
  elem.style.cssText += `
        border: 1px solid #3c4043;
        background: #292a2d;
        color: #FFFFFF;
    `;
  return elem;
}