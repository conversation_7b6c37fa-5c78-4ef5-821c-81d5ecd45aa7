import SoundEffectsService from "~/services/sound-effects.service";
import Swal from "sweetalert2";
import { AppSoundFx } from "~/types/app.types";

const SwalPR = (sfxService?: () => ReturnType<typeof SoundEffectsService>) => Swal.mixin({
  didRender: () => {
    const onMouseEnter = () => { sfxService?.()?.playHoverSFX(); };
    const onMouseDown = (evt: MouseEvent) => { if (evt.button == 0) sfxService?.().playSFX(AppSoundFx.CLICK2); };

    Swal.getConfirmButton()?.addEventListener("mouseenter", onMouseEnter);
    Swal.getCancelButton()?.addEventListener("mouseenter", onMouseEnter);
    Swal.getDenyButton()?.addEventListener("mouseenter", onMouseEnter);

    Swal.getConfirmButton()?.addEventListener("mousedown", onMouseDown);
    Swal.getCancelButton()?.addEventListener("mousedown", onMouseDown);
    Swal.getDenyButton()?.addEventListener("mousedown", onMouseDown);
  },
  willOpen: () => {
    let activeSFX = AppSoundFx.POPUP_OPEN2;
    let icon = Swal.getIcon();
    if (icon) {
      if (icon.className.includes("swal2-success")) activeSFX = AppSoundFx.SUCCESS;
      if (icon.className.includes("swal2-warning")) activeSFX = AppSoundFx.WARNING;
      if (icon.className.includes("swal2-error")) activeSFX = AppSoundFx.ERROR;
    }
    sfxService?.().playSFX(activeSFX);
  },
  willClose: (popup) => {
    setTimeout(() => {
      sfxService?.().playSFX(AppSoundFx.POPUP_CLOSE);
    }, 10);
  }
});

export const SwalDetailsRow = (header: string, value?: string) => {
  return `
  <h3 style='font-weight:bold;text-align:left;margin-bottom:5px'>${header}:</h3>
  <div style='text-align:left'>${value || "N/A"}</div>
  <br/>
  `;
};

export default SwalPR;