import { z } from "zod";

export namespace zodHelper {
  export const truncatedString = (maxLength: number, minLength = 0) => z.preprocess((value) => {
    if (typeof value !== "string") return value;
    return value.substring(0, Math.min(maxLength, value.length)).trim().replace(/\s\s+/g, " ");
  }, z.string().min(minLength));

  export function getDefaults<Schema extends z.AnyZodObject>(schema: Schema) {
    return Object.fromEntries(
      Object.entries(schema.shape).map(([key, value]) => {
        if (value instanceof z.ZodDefault || (value as any)._def.defaultValue != null) {
          return [key, (value as z.ZodDefault<any>)._def.defaultValue()];
        }
        return [key, undefined];
      })
    );
  }
}