import { parse } from "zipson/lib";
import { stringify } from 'zipson';
import isString from "lodash-es/isString";

const tryParseZipzon = (value: string) => {
  try { return parse(value); } catch { return undefined; }
};

export const parseZipzon = <T>(json?: string) => {
  if (!json) return undefined;

  try {
    let input = json;
    try { if (isString(json)) input = JSON.parse(json) as string; } catch { }
    let output = tryParseZipzon(input);

    // Fallback to old JSON parse
    if (!output) {
      output = JSON.parse(json);
      if (isString(output)) output = JSON.parse(output);
    }

    return output as T;
  } catch (ex) {
    console.error("Failed to parse:", ex, " | Input: ", json);
    return undefined;
  }
};

export const stringifyToZipzon = (data: any) => {
  return stringify(data);
};