import tinycolor from "tinycolor2";
import { AppThemeColors } from "~/proto/pianorhythm-app-renditions";

export function blurActiveElement() {
  try { (document.activeElement as HTMLElement).blur(); } catch { }
}

export function getRootUITheme(): HTMLElement | null {
  return document.querySelector(".hope-ui-dark")! as HTMLElement ?? document.querySelector(".hope-ui-light")! as HTMLElement;
}

export function supportsOffscreenCanvas() {
  return (HTMLCanvasElement.prototype as any)["transferControlToOffscreen"] !== undefined;
}

export function isWebMidiSupported() {
  return ('requestMIDIAccess' in navigator);
}

export const isWebWorkerSupported = () => {
  return typeof Worker !== 'undefined';
};

export const isWebGpuSupported = async () => {
  try {
    if (!(navigator as any)?.gpu) {
      return false;
    }

    const adapter = await (navigator as any)?.gpu.requestAdapter();
    if (!adapter) return false;

    return true;
  } catch (e) {
    return false;
  }
};

export const tryDecodeURI = (input: string) => {
  try { return decodeURI(input); } catch { return input; }
};

export function getUIThemeColors(rootPrimaryColor: string, rootAccentColor?: string, rootTertiaryColor?: string) {
  let primaryColor = tinycolor(rootPrimaryColor);
  let accentColor = rootAccentColor ? tinycolor(rootAccentColor) : primaryColor.clone().complement();
  let tertiaryColor = rootTertiaryColor ? tinycolor(rootTertiaryColor) : accentColor.clone().complement().complement();
  return { primary: primaryColor, accent: accentColor, tertiary: tertiaryColor };
}

function getArrayFromColor(color: tinycolor.Instance) {
  return (new Array(12).fill(0)).map((_, idx) => {
    return color.clone().darken(2 * idx);
  });
}

export function setUIThemeColors(rootPrimaryColor: string, rootAccentColor?: string, rootTertiaryColor?: string) {
  let root = getRootUITheme();

  let { primary: primaryColor, accent: accentColor, tertiary: tertiaryColor } = getUIThemeColors(rootPrimaryColor, rootAccentColor, rootTertiaryColor);
  let output = { primary: primaryColor.toHexString(), accent: accentColor.toHexString(), tertiary: tertiaryColor.toHexString() };
  if (!root) return output;

  let primaryColors = getArrayFromColor(primaryColor);

  primaryColors
    .forEach((c, idx) => {
      root?.style.setProperty(`--hope-colors-primary${idx + 1}`, c.toHexString());
    });

  let accentColors = getArrayFromColor(accentColor);
  accentColors.forEach((c, idx) => { root?.style.setProperty(`--hope-colors-accent${idx + 1}`, c.toHexString()); });

  let tertiaryColors = getArrayFromColor(tertiaryColor);
  tertiaryColors.forEach((c, idx) => { root?.style.setProperty(`--hope-colors-tertiary${idx + 1}`, c.toHexString()); });

  if (primaryColors[0]) primaryColor = primaryColors[0]?.clone();
  root.style.setProperty(`--hope-colors-primaryLight`, primaryColor.clone().brighten(10).toHexString());
  root.style.setProperty(`--hope-colors-primaryDark1`, primaryColor.clone().darken(10).toHexString());
  root.style.setProperty(`--hope-colors-primaryDark2`, primaryColor.clone().darken(20).toHexString());
  root.style.setProperty(`--hope-colors-primaryDarkAlpha`, primaryColor.clone().darken(20).toHexString() + "b2");
  root.style.setProperty(`--hope-colors-primaryAlpha`, primaryColor.clone().toHexString() + "f2");
  root.style.setProperty(`--hope-colors-primaryAlpha2`, primaryColor.clone().toHexString() + "c2");
  root.style.setProperty(`--hope-colors-primaryAlpha3`, primaryColor.clone().toHexString() + "5c");

  return AppThemeColors.create(output);
}

export function checkForPassiveEvents() {
  (function () {
    if (typeof EventTarget !== 'undefined') {
      let supportsPassive = false;
      try {
        // Test via a getter in the options object to see if the passive property is accessed
        const opts = Object.defineProperty({}, 'passive', {
          get: () => {
            supportsPassive = true;
          },
        });

        // @ts-ignore
        window.addEventListener('testPassive', null, opts);
        // @ts-ignore
        window.removeEventListener('testPassive', null, opts);
      } catch (e) { }
      const func = EventTarget.prototype.addEventListener;
      EventTarget.prototype.addEventListener = function (type, fn) {
        // @ts-ignore
        this.func = func;
        // @ts-ignore
        this.func(type, fn, supportsPassive ? { passive: false } : false);
      };
    }
  })();
}

export function isBrowser() {
  return (typeof window !== 'undefined');
}

export function isMobile() {
  if (!navigator || !navigator.userAgent) return false;

  // device detection
  return (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|ipad|iris|kindle|Android|Silk|lge |maemo|midp|mmp|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(navigator.userAgent)
    || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(navigator.userAgent.substring(0, 4)));
}