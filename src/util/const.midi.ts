export const MIDI = {
  MIN_NOTE: 21,
  MAX_NOTE: 108,
  DRUM_CHANNEL: 9,
  MAX_CHANNEL: 15,
  MAX_VOLUME: 127,
  MAX_VELOCITY: 127,
  MAX_BANK: 128 * 128,
  DEFAULT_DRUM_BANK_1: 120,
  DEFAULT_DRUM_BANK_2: 128,
  NOTE_ON_BYTE: 144,
  NOTE_OFF_BYTE: 128,
  CONTROLLER_BYTE: 176,
  PROGRAM_CHANGE_BYTE: 192,
  SLOT_MODE_SPLIT2_MAX_CHANNEL: 2,
  SLOT_MODE_SPLIT4_MAX_CHANNEL: 4,
  SLOT_MODE_SPLIT8_MAX_CHANNEL: 8,
  SLOT_MODE_MULTI_MAX_CHANNEL: 3,
  SLOT_MODE_SINGLE_MAX_CHANNEL: 1,
  MIDI_SYNTH_SOCKET_ID: 999,
}

export function mapMidiChannelToColor(channel: number) {
  switch (channel) {
    case 0: return "#48BF91";
    case 1: return "#0076BE";
    case 2: return "#CC3A3D";
    case 3: return "#FEE886";
    case 4: return "#d538fa";
    case 5: return "#c37a29";
    case 6: return "#85416d";
    case 7: return "#2d87fe";
    case 8: return "#663fc5";
    case 9: return "#ff4b04";
    case 10: return "#9d0e0a";
    case 11: return "#e7cfdb";
    case 12: return "#13a31c";
    case 13: return "#6a5029";
    case 14: return "#1e2e6f";
    case 15: return "#7d8f18";
    default: return "#DE8E4E";
  }
}