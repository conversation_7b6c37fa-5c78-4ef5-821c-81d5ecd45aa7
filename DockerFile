FROM oven/bun:1.1.26

WORKDIR /usr/src/app

ARG PR_CLIENT_VERSION
ARG PR_ASSETS_URL
ARG PR_CDN_URL
ARG PIANORHYTHM_SERVER_URL
ARG PIANORHYTHM_MONGODB_API_HOST
ARG PIANORHYTHM_MONGODB_API_DATASOURCE
ARG NODE_ENV

ENV PR_CLIENT_VERSION=$PR_CLIENT_VERSION
ENV PR_ASSETS_URL=$PR_ASSETS_URL
ENV PR_CDN_URL=$PR_CDN_URL
ENV PIANORHYTHM_SERVER_URL=$PIANORHYTHM_SERVER_URL
ENV PIANORHYTHM_MONGODB_API_HOST=$PIANORHYTHM_MONGODB_API_HOST
ENV PIANORHYTHM_MONGODB_API_DATASOURCE=$PIANORHYTHM_MONGODB_API_DATASOURCE
ENV NODE_ENV=$NODE_ENV

COPY ./.output ./.output

EXPOSE 3000
EXPOSE 8080

CMD [ "bun", ".output/server/index.mjs" ]
