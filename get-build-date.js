let DEPLOY_COMMIT_DATE = null;

if (!process.env.PR_CLIENT_BUILD_DATE) {
  try {
    // Get build date from git commit date
    const { execSync } = require('child_process');
    const commitDate = execSync('git show -s --format=%cd').toString();
    DEPLOY_COMMIT_DATE = new Date(commitDate);
  } catch { }
} else {
  try {
    DEPLOY_COMMIT_DATE = new Date(parseInt(process.env.PR_CLIENT_BUILD_DATE));
    if (isNaN(DEPLOY_COMMIT_DATE.getTime())) DEPLOY_COMMIT_DATE = new Date(process.env.PR_DEPLOY_COMMIT_DATE);
    if (!isNaN(DEPLOY_COMMIT_DATE.getTime())) console.log("Commit date found:", DEPLOY_COMMIT_DATE);
  } catch { }
}

export default DEPLOY_COMMIT_DATE;