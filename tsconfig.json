{
  "compilerOptions": {
    "target": "ESNext",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true,
    "useDefineForClassFields": false,
    "esModuleInterop": true,
    "jsx": "preserve",
    "jsxImportSource": "solid-js",
    "allowJs": true,
    "strict": true,
    "composite": true,
    "noEmit": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "incremental": true,
    "experimentalDecorators": true,
    "preserveConstEnums": true,
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "noUncheckedIndexedAccess": true,
    "noErrorTruncation": true,
    "lib": [
      "esnext",
      "DOM",
      "webworker"
    ],
    "types": [
      "vinxi/types/client",
      "vitest/globals",
      "@testing-library/jest-dom",
      "webmidi",
      "animejs",
      "tinycolor2",
      "howler",
      "lodash-es",
      "node"
    ],
    "paths": {
      "~/*": [
        "./src/*"
      ],
      "@test/*": [
        "./tests/*"
      ],
      "@core/*": [
        "./pianorhythm_core/*"
      ],
    }
  },
  "exclude": [
    "node_modules/**/*",
    "pianorhythm_synth",
    "pianorhythm_core",
    "dist",
    "**/dist/*",
    "public",
    ".tauri",
    ".vscode",
    "src-tauri",
    "**/.vscode/*"
  ],
  "ts-node": {
    "compilerOptions": {
      "module": "ESNext",
    }
  },
}