
/**
MIDI channel messages
 */
impl Synth {
    /**
    Get a control value.
     */
    pub fn get_cc(&self, socket_id: u32, chan: u8, ctrl: u16) -> Result<u8, OxiError> {
        self.core.get_cc(socket_id, chan, ctrl)
    }

    /**
    Get the pitch bend value.
     */
    pub fn get_pitch_bend(&self, socket_id: u32, chan: u8) -> Result<u16, OxiError> {
        self.core.get_pitch_bend(socket_id, chan)
    }

    /**
    Set the pitch wheel sensitivity.
     */
    pub fn pitch_wheel_sens(&mut self, socket_id: u32, chan: u8, val: u8) -> Result<(), OxiError> {
        self.core.pitch_wheel_sens(socket_id, chan, val)
    }

    /**
    Set the pitch bend value.
     */
    pub fn pitch_bend(&mut self, socket_id: u32, chan: u8, val: u16) -> Result<(), OxiError> {
        self.core.pitch_bend(socket_id, chan, val)
    }

    /**
    Get the pitch wheel sensitivity.
     */
    pub fn get_pitch_wheel_sens(&self, socket_id: u32, chan: u8) -> Result<u8, OxiError> {
        self.core.get_pitch_wheel_sens(socket_id, chan)
    }

    /**
    Select a bank.
     */
    pub fn bank_select(&mut self, socket_id: u32, chan: u8, bank: u32) -> Result<(), OxiError> {
        self.core.bank_select(socket_id, chan, bank)
    }

    pub(crate) fn bank_select_with_channel(
        &mut self,
        chan: u8,
        bank: u32,
        socket_id: &u32,
    ) -> Result<(), OxiError> {
        let user = self.socket_players.get_mut(socket_id);

        if let Some(target) = user {
            let channel = self.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize);

            let mut input_channel = if let Ok(channel) = channel {
                channel.clone()
            } else {
                return Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                });
            };

            _ = self.core.bank_select_with_channel(&mut input_channel, bank)?;

            if let Ok(channel) = self.core.channels.get_mut_by_user_id_and_channel_id(*socket_id, chan as usize) {
                *channel = input_channel;
            }

            return Ok(());
        }

        return Err(OxiError::SocketUserNotFound);
    }

    /**
    Select a sfont.
     */
    pub(super) fn sfont_select(&mut self, socket_id: u32, chan: u8, sfont_id: SoundFontId) -> Result<(), OxiError> {
        self.core.sfont_select(socket_id, chan, sfont_id)
    }

    /**
    Select a preset for a channel. The preset is specified by the
    SoundFont ID, the bank number, and the preset number. This
    allows any preset to be selected and circumvents preset masking
    due to previously loaded SoundFonts on the SoundFont stack.
     */
    pub(super) fn program_select(
        &mut self,
        socket_id: u32,
        chan: u8,
        sfont_id: SoundFontId,
        bank_num: u32,
        preset_num: u8,
    ) -> Result<(), OxiError> {
        self.core
            .program_select(socket_id, chan, sfont_id, bank_num, preset_num)
    }

    /**
    Returns the program, bank, and SoundFont number of the preset on a given channel for a particular user.
     */
    pub(crate) fn get_program(
        &self,
        chan: u8,
        socket_id: &u32,
    ) -> Result<(Option<SoundFontId>, u32, u8), OxiError> {
        let synth = self.socket_players.get(socket_id);
        if let Some(target) = synth {
            if let Ok(channel) = self.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                return Ok(internal::midi::get_program(channel));
            } else {
                return Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                });
            }
        }

        return Err(OxiError::SocketUserNotFound);
    }

    pub fn clear_program_on_channel(&mut self, chan: u8, socket_id: &u32) -> Result<(), OxiError> {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            return if let Ok(channel) = self.core.channels.get_mut_by_user_id_and_channel_id(*socket_id, chan as usize) {
                channel.set_banknum(0);
                channel.set_prognum(0);
                channel.set_preset(None);
                Ok(())
            } else {
                Err(OxiError::NoChannelFoundForUser {
                    channel: chan,
                    socket_id: socket_id.clone(),
                })
            };
        }

        return Err(OxiError::SocketUserNotFound);
    }

    /**
    Returns the program, bank, and SoundFont number of the preset on a given channel.
     */
    pub fn get_cc_by_socket_id(
        &self,
        chan: u8,
        ctrl: u16,
        socket_id: &u32,
    ) -> Result<u8, OxiError> {
        let synth = self.socket_players.get(socket_id);

        if let Some(target) = synth {
            let channel = self.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize)?;
            return Ok(internal::midi::get_cc(channel, ctrl));
        }

        return Err(OxiError::SocketUserNotFound);
    }

    pub fn is_channel_active(&self, chan: u8, socket_id: &u32) -> bool {
        let synth = self.socket_players.get(socket_id);

        if let Some(target) = synth {
            if let Ok(channel) = self.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                return channel.active();
            }
        }

        return false;
    }

    pub fn channel_has_any_preset(&self, chan: u8, socket_id: &u32) -> bool {
        let synth = self.socket_players.get(socket_id);
        if let Some(target) = synth {
            if let Ok(channel) = self.core.channels.get_by_user_id_and_channel_id(*socket_id, chan as usize) {
                // println!("channel_has_any_preset: {:?}", channel.preset().map(|x| x.name()));
                return channel.preset().is_some();
            }
        }

        return false;
    }

    pub fn set_user_interp_method(
        &mut self,
        chan: Option<usize>,
        socket_id: &u32,
        interp_method: InterpolationMethod,
    ) {
        let synth = self.socket_players.get_mut(socket_id);
        if let Some(target) = synth {
            if let Some(chan) = chan {
                let ch = self.core.channels.iter_mut().find(|ch| ch.id() == chan && ch.is_for_user(*socket_id));

                if let Some(ch) = ch {
                    ch.set_interp_method(interp_method);
                }
            } else {
                for ch in self.core.channels.iter_mut().filter(|ch| ch.is_for_user(*socket_id)) {
                    ch.set_interp_method(interp_method);
                }
            }
        }
    }

    /**
    Send a bank select and a program change to every channel to reinitialize the preset of the channel.

    This function is useful mainly after a SoundFont has been loaded, unloaded or reloaded.
     */
    pub fn program_reset(&mut self) {
        self.core.program_reset()
    }
}
