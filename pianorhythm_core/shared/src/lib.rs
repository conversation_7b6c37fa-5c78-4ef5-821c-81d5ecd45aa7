#![allow(dead_code, unused, non_camel_case_types, non_snake_case)]

pub use crate::gameloop::*;

mod gameloop;

pub const GLOBAL_TIME_OFFSET: u32 = 1000;

pub mod midi {
    pub const MIN_NOTE: u8 = 21;
    pub const MAX_NOTE: u8 = 108;
    pub const DRUM_CHANNEL: u8 = 9;
    pub const MAX_CHANNEL: u8 = 15;
    pub const MAX_VOLUME: u8 = 127;
    pub const MAX_VELOCITY: u8 = 127;
    pub const NOTE_ON_BYTE: u8 = 144;
    pub const NOTE_OFF_BYTE: u8 = 128;
    pub const CONTROLLER_BYTE: u8 = 176;
    pub const PROGRAM_CHANGE_BYTE: u8 = 192;
    pub const DEFAULT_DRUM_BANK_1: u8 = 120;
    pub const DEFAULT_TEMPO: u8 = 120;
    pub const DEFAULT_DRUM_BANK_2: u8 = 128;
    pub const SLOT_MODE_SPLIT2_MAX_CHANNEL: u8 = 2;
    pub const SLOT_MODE_SPLIT4_MAX_CHANNEL: u8 = 4;
    pub const SLOT_MODE_SPLIT8_MAX_CHANNEL: u8 = 8;
    pub const SLOT_MODE_MULTI_MAX_CHANNEL: u8 = 3;
    pub const SLOT_MODE_SINGLE_MAX_CHANNEL: u8 = 1;
}

pub mod audio {
    pub const U8_MAX_VALUE: u8 = 127;
    pub const MAX_CHANNEL: u8 = 15;
    pub const DEFAULT_TRANSPOSE: i32 = 0;
    pub const DEFAULT_PRIMARY_CHANNEL: u8 = 0;
    pub const MAX_TRANSPOSE: i32 = 20;
    pub const MIN_TRANSPOSE: i32 = -20;
    pub const DEFAULT_OCTAVE: i32 = 0;
    pub const MIN_OCTAVE: i32 = -7;
    pub const MAX_OCTAVE: i32 = 7;
    pub const DEFAULT_BANK: u8 = 0;
    pub const DEFAULT_PAN: u8 = 64;
    pub const DEFAULT_CHANNEL_VOLUME: u8 = 100;
    pub const DEFAULT_VELOCITY: u8 = 100;
    pub const MAX_CHANNEL_VOLUME: u8 = 127;
    pub const MIN_VELOCITY: u8 = 0;
    pub const MAX_VELOCITY: u8 = 127;
    pub const MAX_VELOCITY_USER_PERCENTAGE: u8 = 100;
    pub const DEFAULT_POLYPHONY: u16 = 256;
    pub const MAX_POLYPHONY: u16 = 2048;
    pub const DEFAULT_NOTE_ON_TIME: u8 = 10;
    pub const MAX_NOTE_ON_TIME: u8 = 20;
    pub const MAX_REVERB_LEVEL: f32 = 1.0;
    pub const MAX_REVERB_ROOMSIZE: f32 = 2.0;
    pub const MAX_REVERB_DAMP: f32 = 1.0;
    pub const MAX_REVERB_WIDTH: f32 = 100.0;
    pub const MAX_SOUNDFONT_LOADTIME_WARNING: f32 = 1000.0 * 60.0 * 1.25;
    pub const MAX_SOUNDFONT_LOADTIME: f32 = 1000.0 * 60.0 * 15.0;
    pub const MAX_SFX_VOLUME: f32 = 1.0;
    pub const MIN_SAMPLE_RATE: u32 = 8000;
    pub const MAX_SAMPLE_RATE: u32 = 96000;
    pub const MAX_VOLUME_RAW: u16 = 16384;

    pub mod reverb {
        pub mod minimal {
            pub const AUDIO_REVERB_LEVEL: f32 = 0.9;
            pub const AUDIO_REVERB_ROOMSIZE: f32 = 0.2;
            pub const AUDIO_REVERB_DAMP: f32 = 0.0;
            pub const AUDIO_REVERB_WIDTH: f32 = 0.5;
        }

        pub mod high {
            pub const AUDIO_REVERB_LEVEL: f32 = 0.17;
            pub const AUDIO_REVERB_ROOMSIZE: f32 = 0.7;
            pub const AUDIO_REVERB_DAMP: f32 = 0.15;
            pub const AUDIO_REVERB_WIDTH: f32 = 25.0;
        }
    }
}

/// MIDI status bytes
pub mod MIDI_STATUS_BYTES {
    /// Release a note and stop playing it
    pub const NOTE_OFF: u8 = 0x80;
    /// Play a note and start sounding it
    pub const NOTE_ON: u8 = 0x90;
    /// Apply pressure to a note playing, similar to applying pressure to electronic keyboard keys
    pub const AFTERTOUCH: u8 = 0xA0;
    /// Affect a controller, such as a slider, knob, or switch
    pub const CONTROLLER: u8 = 0xB0;
    /// Assign a program to a MIDI channel, such as an instrument, patch, or preset
    pub const PROGRAM_CHANGE: u8 = 0xC0;
    /// Apply pressure to a MIDI channel, similarly to applying pressure to electronic keyboard keys
    pub const CHANNEL_PRESSURE: u8 = 0xD0;
    /// Change a channel pitch up or down
    pub const PITCH_WHEEL: u8 = 0xE0;
    /// Perform some device specific task
    pub const SYSTEM_EXCLUSIVE_START: u8 = 0xF0;
    pub const SYSTEM_EXCLUSIVE_END: u8 = 0xF7;
    /// Set the MIDI time to keep in line with some other device
    pub const MIDI_TIME_CODE: u8 = 0xF1;
    /// Cue to a point in the MIDI sequence to be ready to play
    pub const SONG_POSITION_POINTER: u8 = 0xF2;
    /// Set a sequence for playback
    pub const SONG_SELECT: u8 = 0xF3;
    /// Tune
    pub const TUNE_REQUEST: u8 = 0xF6;
    /// Understand the position of the MIDI clock (when synchronized to another device)
    pub const MIDI_CLOCK: u8 = 0xF8;
    /// Start playback of some MIDI sequence
    pub const MIDI_START: u8 = 0xFA;
    /// Resume playback of some MIDI sequence
    pub const MIDI_CONTINUE: u8 = 0xFB;
    /// Stop playback of some MIDI sequence
    pub const MIDI_STOP: u8 = 0xFC;
    /// Understand that a MIDI connection exists (if there are no other MIDI messages)
    pub const ACTIVE_SENSE: u8 = 0xFE;
    /// Reset to default state
    pub const RESET: u8 = 0xFF;
    pub const MIDI_MEASURE_END: u8 = 0xF9;
}

/// MIDI Meta Event Types
pub mod MIDI_META_EVENT_TYPES {
    /// The number of a sequence | At delta time 0
    pub const SEQUENCE_NUMBER: u8 = 0x00;
    /// Text event
    pub const TEXT_EVENT: u8 = 0x01;
    /// Copyright notice | 	At delta time 0 in the first track
    pub const COPYRIGHT_NOTICE: u8 = 0x02;
    /// Sequence or track name | At delta time 0
    pub const TRACK_NAME: u8 = 0x03;
    /// Instrument name
    pub const INSTRUMENT_NAME: u8 = 0x04;
    /// Lyric text
    pub const LYRIC_TEXT: u8 = 0x05;
    /// Marker text
    pub const MARKER_TEXT: u8 = 0x06;
    /// Cue point
    pub const CUE_POINT: u8 = 0x07;
    /// MIDI channel prefix | A channel number (following meta messages will apply to this channel)
    pub const MIDI_CHANNEL_PREFIX_ASSIGNMENT: u8 = 0x20;
    pub const MIDI_PORT: u8 = 0x21;
    /// End of track
    pub const END_OF_TRACK: u8 = 0x2F;
    /// The number of microseconds per beat | Anywhere, but usually in the first track
    pub const TEMPO_SETTING: u8 = 0x51;
    /// SMPTE offset | SMPTE time to denote playback offset from the beginning
    pub const SMPTE_OFFSET: u8 = 0x54;
    /// Time signature | Time signature, metronome clicks, and size of a beat in 32nd notes
    pub const TIME_SIGNATURE: u8 = 0x58;
    /// Key signature
    pub const KEY_SIGNATURE: u8 = 0x59;
    /// Sequencer specific event | Something specific to the MIDI device manufacturer
    pub const SEQUENCER_SPECIFIC_EVENT: u8 = 0x7F;
}

/// Constants to represent controller change types.
///
/// A control change channel event is as follows:
/// * byte one: `CONTROL_CHANGE | channel`, where `channel` is the channel (0-16)
/// * byte two: controller type (0-127). This module contains constants for these types.
/// * byte three: new controller value
///
/// # Remark
/// Some control change types come in pairs: one with the most significant byte (MSB)
/// and one with the least significant byte (LSB).
pub mod MIDI_CONTROL_BYTES {
    const LSB_MASK: u8 = 0x20;
    pub const BANK_SELECT_COARSE: u8 = 0;
    pub const MODULATION_WHEEL_COARSE: u8 = 1;
    pub const BREATH_CONTROLLER_COARSE: u8 = 2;
    pub const FOOT_CONTROLLER_COARSE: u8 = 4;
    pub const PORTAMENTO_TIME_COARSE: u8 = 5;
    pub const DATA_ENTRY_COARSE: u8 = 6;
    pub const VOLUME_COARSE: u8 = 7;
    pub const BALANCE_COARSE: u8 = 8;
    pub const PAN_COARSE: u8 = 10;
    pub const EXPRESSION_COARSE: u8 = 11;
    pub const EFFECT_CONTROL_1_COARSE: u8 = 12;
    pub const EFFECT_CONTROL_2_COARSE: u8 = 13;
    pub const GENERAL_PURPOSE_SLIDER_1: u8 = 16;
    pub const GENERAL_PURPOSE_SLIDER_2: u8 = 17;
    pub const GENERAL_PURPOSE_SLIDER_3: u8 = 18;
    pub const GENERAL_PURPOSE_SLIDER_4: u8 = 19;
    pub const BANK_SELECT_FINE: u8 = 32;
    pub const MODULATION_WHEEL_FINE: u8 = 33;
    pub const BREATH_CONTROLLER_FINE: u8 = 34;
    pub const FOOT_CONTROLLER_FINE: u8 = 36;
    pub const PORTAMENTO_TIME_FINE: u8 = 37;
    pub const DATA_ENTRY_FINE: u8 = 38;
    pub const VOLUME_FINE: u8 = 39;
    pub const BALANCE_FINE: u8 = 40;
    pub const PAN_FINE: u8 = 42;
    pub const EXPRESSION_FINE: u8 = 43;
    pub const EFFECT_CONTROL_1_FINE: u8 = 44;
    pub const EFFECT_CONTROL_2_FINE: u8 = 45;
    pub const GENERAL_PURPOSE_BUTTON_1: u8 = 80;
    pub const GENERAL_PURPOSE_BUTTON_2: u8 = 81;
    pub const GENERAL_PURPOSE_BUTTON_3: u8 = 82;
    pub const GENERAL_PURPOSE_BUTTON_4: u8 = 83;
    pub const REVERB_LEVEL: u8 = 91;
    pub const TREMOLO_LEVEL: u8 = 92;
    pub const CHORUS_LEVEL: u8 = 93;
    pub const CELESTE_LEVEL: u8 = 94;
    pub const PHASER_LEVEL: u8 = 95;
    pub const DATA_INCREMENT: u8 = 96;
    pub const DATA_DECREMENT: u8 = 97;
    pub const ALL_CONTROLLERS_OFF: u8 = 121;
    pub const LOCAL_KEYBOARD: u8 = 122;

    /// Bank select: most significant byte.
    pub const BANK_SELECT_MSB: u8 = 0x00;

    /// Bank select: least significant byte.
    pub const BANK_SELECT_LSB: u8 = BANK_SELECT_MSB | LSB_MASK;

    /// Modulation: most significant byte.
    pub const MODULATION_MSB: u8 = 0x01;

    /// Modulation: least significant byte.
    pub const MODULATION_LSB: u8 = MODULATION_MSB | LSB_MASK;

    /// Breath controller: most significant byte.
    pub const BREATH_CONTROLLER_MSB: u8 = 0x02;

    /// Breach controller: least significant byte.
    pub const BREATH_CONTROLLER_LSB: u8 = BREATH_CONTROLLER_MSB | LSB_MASK;

    /// Foot controller: most significant byte.
    pub const FOOT_CONTROLLER_MSB: u8 = 0x04;

    /// Foot controller: least significant byte.
    pub const FOOT_CONTROLLER_LSB: u8 = FOOT_CONTROLLER_MSB | LSB_MASK;

    /// Portamento: most significant byte.
    pub const PORTAMENTO_TIME_MSB: u8 = 0x05;

    /// Portamento: least significant byte.
    pub const PORTAMENTO_TIME_LSB: u8 = PORTAMENTO_TIME_MSB | LSB_MASK;

    /// Data entry: most significant byte.
    pub const DATA_ENTRY_MSB: u8 = 0x06;

    /// Data entry: least significant byte.
    pub const DATA_ENTRY_LSB: u8 = DATA_ENTRY_MSB | LSB_MASK;

    /// Main volume: most significant byte.
    pub const MAIN_VOLUME_MSB: u8 = 0x07;

    /// Main volume: least significant byte.
    pub const MAIN_VOLUME_LSB: u8 = MAIN_VOLUME_MSB | LSB_MASK;

    /// Balance: most significant byte.
    pub const BALANCE_MSB: u8 = 0x08;

    /// Balance: least significant byte.
    pub const BALANCE_LSB: u8 = BALANCE_MSB | LSB_MASK;

    /// Pan: most significant byte.
    pub const PAN_MSB: u8 = 0x0A;

    /// Pan: least significant byte.
    pub const PAN_LSB: u8 = PAN_MSB | LSB_MASK;

    /// Expression controller: most significant byte.
    pub const EXPRESSION_CONTROLLER_MSB: u8 = 0x0B;

    /// Expression controller: least significant byte.
    pub const EXPRESSION_CONTROLLER_LSB: u8 = EXPRESSION_CONTROLLER_MSB | LSB_MASK;

    /// Effect control 1: most significant byte.
    pub const EFFECT_CONTROL_1_MSB: u8 = 0x0C;

    /// Effect control 1: least significant byte.
    pub const EFFECT_CONTROL_1_LSB: u8 = EFFECT_CONTROL_1_MSB | LSB_MASK;

    /// Effect control 2: most significant byte.
    pub const EFFECT_CONTROL_2_MSB: u8 = 0x0D;

    /// Effect control 2: least significant byte.
    pub const EFFECT_CONTROL_2_LSB: u8 = EFFECT_CONTROL_2_MSB | LSB_MASK;

    /// General purpose controller 1: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_MSB: u8 = 0x10;

    /// General purpose controller 1: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_1_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_1_MSB | LSB_MASK;

    /// General purpose controller 2: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_MSB: u8 = 0x11;

    /// General purpose controller 2: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_2_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_2_MSB | LSB_MASK;

    /// General purpose controller 3: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_MSB: u8 = 0x12;

    /// General purpose controller 3: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_3_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_3_MSB | LSB_MASK;

    /// General purpose controller 4: most significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_MSB: u8 = 0x13;

    /// General purpose controller 4: least significant byte.
    pub const GENERAL_PURPOSE_CONTROLLER_4_LSB: u8 = GENERAL_PURPOSE_CONTROLLER_4_MSB | LSB_MASK;

    /// Damper pedal.
    pub const DAMPER_PEDAL: u8 = 0x40;

    /// Portamento.
    pub const PORTAMENTO: u8 = 0x41;

    /// Sustenuto.
    pub const SUSTENUTO: u8 = 0x42;

    /// Soft pedal.
    pub const SOFT_PEDAL: u8 = 0x43;

    /// Legato footswitch.
    pub const LEGATO_FOOTSWITCH: u8 = 0x44;

    /// Hold 2.
    pub const HOLD_2: u8 = 0x45;

    /// Sound controller 1. Default: Timber variation
    pub const SOUND_CONTROLLER_1: u8 = 0x46;

    /// Sound controller 2. Default: Timber/harmonic content
    pub const SOUND_CONTROLLER_2: u8 = 0x47;

    /// Sound controller 3. Default: Release time
    pub const SOUND_CONTROLLER_3: u8 = 0x48;

    /// Sound controller 4. Default: Attack time
    pub const SOUND_CONTROLLER_4: u8 = 0x49;

    /// Sound controller 5.
    pub const SOUND_CONTROLLER_5: u8 = 0x4A;

    /// Sound controller 6.
    pub const SOUND_CONTROLLER_6: u8 = 0x4B;

    /// Sound controller 7.
    pub const SOUND_CONTROLLER_7: u8 = 0x4C;

    /// Sound controller 8.
    pub const SOUND_CONTROLLER_8: u8 = 0x4D;

    /// Sound controller 9.
    pub const SOUND_CONTROLLER_9: u8 = 0x4E;

    /// Sound controller 10.
    pub const SOUND_CONTROLLER_10: u8 = 0x4F;

    /// General purpose controller 5: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_5_MSB: u8 = 0x50;

    /// General purpose controller 6: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_6_MSB: u8 = 0x51;

    /// General purpose controller 7: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_7_MSB: u8 = 0x52;

    /// General purpose controller 8: most significant byte.
    ///
    /// # Remark
    /// As far as I know, this has no LSB (least significant byte) variant.
    pub const GENERAL_PURPOSE_CONTROLLER_8_MSB: u8 = 0x53;

    /// Portamento.
    pub const PORTAMENTO_CONTROL: u8 = 0x54;

    /// Effects depth 1. Formerly "External Effects Depth"
    pub const EFFECTS_1_DEPTH: u8 = 0x5B;

    /// Effects depth 2. Formerly "Tremolo Depth"
    pub const EFFECTS_2_DEPTH: u8 = 0x5C;

    /// Effects depth 3. Formerly "Chorus Depth"
    pub const EFFECTS_3_DEPTH: u8 = 0x5D;

    /// Effects depth 4. Formerly "Celeste Detune"
    pub const EFFECTS_4_DEPTH: u8 = 0x5E;

    /// Effects depth 5. Formerly "Phaser Depth"
    pub const EFFECTS_5_DEPTH: u8 = 0x5F;

    /// Non-registered parameter number: least significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x62;

    /// Non-registered parameter number: most significant byte.
    pub const NON_REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x63;

    /// Registered parameter number: least significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_LSB: u8 = 0x64;

    /// Registered parameter number: most significant byte.
    pub const REGISTERED_PARAMETER_NUMBER_MSB: u8 = 0x65;

    /// Mode message: all sound off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const ALL_SOUND_OFF: u8 = 0x78;

    /// Mode message: reset all controllers.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`
    pub const RESET_ALL_CONTROLLERS: u8 = 0x79;

    /// Mode message: local control.
    ///
    /// When local control is on (default), the device responds to its local controls.
    /// When local control is off, it only responds to data recieved over MIDI.
    ///
    /// See the module [`local_control`] for possible values of the data byte
    /// (the third byte of the event).
    ///
    /// [`local_control`]: ./local_control/index.html
    pub const LOCAL_CONTROL: u8 = 0x7A;

    /// Constants for the data byte (3rd byte) of a local control control change event.
    pub mod local_control {
        /// Local control off: the device only responds to data recieved over MIDI.
        pub const LOCAL_CONTROL_OFF: u8 = 0;
        /// Local control on: the device also responds to local events (keys played, ...).
        pub const LOCAL_CONTROL_ON: u8 = 127;
    }

    /// Mode message: all notes off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    pub const ALL_NOTES_OFF: u8 = 0x7B;

    /// Mode message: omni mode off.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_OFF: u8 = 0x7C;

    /// Mode message: omni mode on.
    ///
    /// For this event, the data byte (the third byte of the event) should be `0`.
    /// # Remark
    /// This message also causes all notes off.
    pub const OMNI_MODE_ON: u8 = 0x7D;

    /// Mode message: mono mode on
    ///
    /// For this event, the data byte (the third byte of the event)
    /// indicates the number of channels (omni off) or `0` (omni on).
    /// # Remark
    /// This message also causes all notes off.
    pub const MONO_MODE_ON: u8 = 0x7E;

    /// Poly mode on
    ///
    /// # Remark
    /// This message also causes all notes off.
    pub const POLY_MODE_ON: u8 = 0x7F;
}

pub mod defaults {
    use pianorhythm_proto::midi_renditions::ActiveChannelsMode;

    pub const SLOT_MODE: ActiveChannelsMode = ActiveChannelsMode::ALL;
    pub const AUDIO_MULTIMODE_MAX_CHANNELS: u8 = 3;
}

pub mod util {
    use std::fmt::Debug;
    use std::hash::Hasher;

    #[cfg(not(target_arch = "wasm32"))]
    use cached::proc_macro::cached;
    #[cfg(not(target_arch = "wasm32"))]
    use cached::TimedCache;
    use protobuf::ProtobufEnum;
    use seahash::SeaHasher;
    use serde::{Deserializer, Serialize, Serializer};
    use serde::de::{
        EnumAccess, IntoDeserializer, MapAccess, SeqAccess,
        VariantAccess,
    };
    use serde::Deserialize;
    use serde::ser::{SerializeMap, SerializeSeq};

    use pianorhythm_proto::midi_renditions::ActiveChannelsMode;
    use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
    use pianorhythm_proto::pianorhythm_app_renditions::AppNotificationConfig;
    use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};

    #[cfg_attr(not(target_arch = "wasm32"),
        cached(
            type = "TimedCache<String, u32>",
            create = "{ TimedCache::with_lifespan(3600) }"
        )
    )]
    pub fn hash_string_to_u32(s: String) -> u32 {
        let mut hasher = SeaHasher::new();
        hasher.write(s.as_bytes());
        hasher.finish() as u32
    }

    pub fn create_simple_toast_config(title: &str, message: &str) -> AppNotificationConfig {
        let mut config = AppNotificationConfig::new();
        config.set_id(uuid::Uuid::new_v4().to_string());
        config.set_title(title.to_string());
        config.set_description(message.to_string());
        config
    }

    pub fn map_velocity(value: f32, in_min: f32, in_max: f32, out_min: f32, out_max: f32) -> f32 {
        (value - in_min) * (out_max - out_min) / (in_max - in_min) + out_min
    }

    pub fn get_next_slot_mode(slot_mode: &ActiveChannelsMode) -> ActiveChannelsMode {
        match slot_mode {
            ActiveChannelsMode::SINGLE => ActiveChannelsMode::MULTI,
            ActiveChannelsMode::MULTI => ActiveChannelsMode::ALL,
            ActiveChannelsMode::ALL => ActiveChannelsMode::SPLIT2,
            ActiveChannelsMode::SPLIT2 => ActiveChannelsMode::SPLIT4,
            ActiveChannelsMode::SPLIT4 => ActiveChannelsMode::SPLIT8,
            ActiveChannelsMode::SPLIT8 => ActiveChannelsMode::SINGLE,
        }
    }

    pub fn create_effect(action: AppStateEffects_Action) -> AppStateEffects
    {
        let mut effect = AppStateEffects::new();
        effect.set_action(action);
        effect
    }

    pub fn create_effect_with<F>(action: AppStateEffects_Action, mut f: F) -> AppStateEffects
    where
        F: FnMut(&mut AppStateEffects) -> (),
    {
        let mut effect = AppStateEffects::new();
        effect.set_action(action);
        f(&mut effect);
        effect
    }

    pub fn create_action_with<F>(action: AppStateActions_Action, mut f: F) -> AppStateActions
    where
        F: FnMut(&mut AppStateActions) -> (),
    {
        let mut output = AppStateActions::new();
        output.set_action(action);
        f(&mut output);
        output
    }

    pub fn map_midi_channel_to_color(channel: u8) -> &'static str {
        match channel {
            0 => "#48BF91",
            1 => "#0076BE",
            2 => "#CC3A3D",
            3 => "#FEE886",
            4 => "#d538fa",
            5 => "#c37a29",
            6 => "#85416d",
            7 => "#2d87fe",
            8 => "#663fc5",
            9 => "#ff4b04",
            10 => "#9d0e0a",
            11 => "#e7cfdb",
            12 => "#13a31c",
            13 => "#6a5029",
            14 => "#1e2e6f",
            15 => "#7d8f18",
            _ => "#DE8E4E",
        }
    }

    pub fn vec_proto_serialize<T, S>(x: &Vec<T>, s: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
        T: Debug,
    {
        let mut seq = s.serialize_seq(Some(x.len()))?;
        for e in x {
            seq.serialize_element(&format!("{:?}", e))?;
        }
        seq.end()
    }

    pub fn option_proto_serialize<T, S>(x: &Option<T>, s: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
        T: Debug,
    {
        s.serialize_str(&match x {
            None => "None".to_string(),
            Some(v) => format!("{:?}", &v),
        })
    }

    pub fn proto_serialize<T, S>(x: &T, s: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
        T: Debug,
    {
        s.serialize_str(&format!("{:?}", &x))
    }

    pub fn proto_serialize_enum<T, S>(x: &T, s: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
        T: Debug + protobuf::ProtobufEnum,
    {
        s.serialize_i32(x.value())
    }

    pub fn proto_deserialize_enum<'de, T, D>(deserializer: D) -> Result<T, D::Error>
    where
        D: Deserializer<'de>,
        T: protobuf::ProtobufEnum,
    {
        let v = i32::deserialize(deserializer)?;
        T::from_i32(v).ok_or(serde::de::Error::custom("Invalid value"))
    }

    pub fn hashmap_proto_serialize<K, V, S>(x: &rustc_hash::FxHashMap<K, V>, s: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
        K: Serialize,
        V: Debug,
    {
        let mut map = s.serialize_map(Some(x.len()))?;
        for (k, v) in x {
            map.serialize_entry(k, &format!("{:?}", v))?;
        }
        map.end()
    }

    pub fn rainbow_color_array(size: Option<usize>) -> Vec<String> {
        let size = size.unwrap_or(128);
        let mut rainbow = vec![String::new(); size];

        fn sin_to_hex(i: f64, phase: f64, size: f64) -> String {
            let sin = ((std::f64::consts::PI / size * 2.0 * i + phase).sin() * 127.0) + 128.0;
            let int = sin.floor() as i32;
            let hex = format!("{:x}", int);
            if hex.len() == 1 {
                format!("0{}", hex)
            } else {
                hex
            }
        }

        for i in 0..size {
            let red = sin_to_hex(i as f64, 0.0 * std::f64::consts::PI * 2.0 / 3.0, size as f64); // 0   deg
            let blue = sin_to_hex(i as f64, 1.0 * std::f64::consts::PI * 2.0 / 3.0, size as f64); // 120 deg
            let green = sin_to_hex(i as f64, 2.0 * std::f64::consts::PI * 2.0 / 3.0, size as f64); // 240 deg
            rainbow[i] = format!("#{}{}{}", red, green, blue);
        }

        rainbow
    }

    pub fn degrees_to_radians(degrees: f64) -> f64 {
        degrees * (std::f64::consts::PI / 180.0)
    }

    pub fn radians_to_degrees(radians: f64) -> f64 {
        radians * (180.0 / std::f64::consts::PI)
    }
}

pub mod room_types {
    // #[derive(Debug, Clone, Default, PartialEq, Serialize, Deserialize)]
    // pub struct RoomStageDetails {
    //     #[serde(serialize_with = "util::proto_serialize_enum", deserialize_with = "util::proto_deserialize_enum")]
    //     pub stage: pianorhythm_proto::room_renditions::RoomStages,
    //     pub effects: Option<RoomStageVisualEffects>,
    //     pub audio_effects: Option<RoomStageAudioEffects>,
    // }
}

pub mod app_types {}

pub mod RENDER_CONSTS {
    pub mod RESOURCES {
        pub const DEFAULT_FONT_URL: &str = "https://assets.babylonjs.com/fonts/Kenney Future Regular.json";
    }

    pub mod ID {
        pub const MAIN_CAMERA: &str = "main-camera";
        pub const PIANO_ROOT: &str = "piano-set-root";
        pub const DEFAULT_PIANO: &str = "default-piano";
        pub const DEFAULT_DRUMS: &str = "default-drums";
        pub const DRUMS_ROOT: &str = "drums-set-root";
        pub const PIANO_KEY_PREFIX: &str = "piano_key";
        pub const PIANO_PEDAL_PREFIX: &str = "piano_pedal";
        pub const SUSTAIN_PEDAL: &str = "sustain-pedal";
        pub const DRUMS_PREFIX: &str = "drums";
        pub const DRUMS_BASS_PEDAL: &str = "bass pedal";
        pub const MAIN_LIGHT: &str = "main-light";
    }

    pub mod TAGS {
        pub const CAST_SHADOW: &str = "cast-shadow";
        pub const PIANO: &str = "piano";
        pub const KEYBOARD_MAPPING: &str = "keyboard-mapping";
        pub const WHITE_KEY: &str = "white-key";
        pub const BLACK_KEY: &str = "black-key";
    }
}

pub mod SYNTH_CONSTS {
    pub mod ID {
        pub const MIDI_SYNTH_SOCKET_ID: u32 = 999;
    }
}

pub mod GENERAL_MIDI {
    use strum::IntoEnumIterator;
    use strum_macros::{Display, EnumIter, EnumString};

    use crate::GENERAL_MIDI;

    /// Used to turn General MIDI level 1 or 2 on, or turn them off.
    ///
    /// Used in [`UniversalNonRealTimeMsg::GeneralMidi`](crate::UniversalNonRealTimeMsg::GeneralMidi)
    #[derive(Debug, Clone, Copy, PartialEq, Eq)]
    pub enum GeneralMidi {
        GM1 = 1,
        GM2 = 3,
        Off = 2,
    }

    /// The instrument that should be played when applying a [`ChannelVoiceMsg::ProgramChange`](crate::ChannelVoiceMsg::ProgramChange).
    /// Should not be used when targeting channel 10.
    /// As defined in General MIDI System Level 1 (MMA0007 / RP003).
    #[derive(Default, Debug, Clone, Copy, PartialEq, PartialOrd, Eq, EnumIter, Display, EnumString)]
    #[repr(u8)]
    pub enum GMSoundSet {
        #[default]
        AcousticGrandPiano = 0,
        BrightAcousticPiano = 1,
        ElectricGrandPiano = 2,
        HonkytonkPiano = 3,
        RhodesElectricPiano = 4,
        ChorusedElectricPiano = 5,
        Harpsichord = 6,
        Clavinet = 7,
        Celesta = 8,
        Glockenspiel = 9,
        MusicBox = 10,
        Vibraphone = 11,
        Marimba = 12,
        Xylophone = 13,
        TubularBells = 14,
        Dulcimer = 15,
        DrawbarOrgan = 16,
        PercussiveOrgan = 17,
        RockOrgan = 18,
        ChurchOrgan = 19,
        ReedOrgan = 20,
        Accordion = 21,
        Harmonica = 22,
        TangoAccordion = 23,
        AcousticGuitarNylon = 24,
        AcousticGuitarSteel = 25,
        ElectricGuitarJazz = 26,
        ElectricGuitarClean = 27,
        ElectricGuitarMuted = 28,
        OverdrivenGuitar = 29,
        DistortionGuitar = 30,
        GuitarHarmonics = 31,
        AcousticBass = 32,
        ElectricBassFinger = 33,
        ElectricBassPick = 34,
        FretlessBass = 35,
        SlapBass1 = 36,
        SlapBass2 = 37,
        SynthBass1 = 38,
        SynthBass2 = 39,
        Violin = 40,
        Viola = 41,
        Cello = 42,
        Contrabass = 43,
        TremoloStrings = 44,
        PizzicatoStrings = 45,
        OrchestralHarp = 46,
        Timpani = 47,
        StringEnsemble1 = 48,
        StringEnsemble2 = 49,
        SynthStrings1 = 50,
        SynthStrings2 = 51,
        ChoirAahs = 52,
        VoiceOohs = 53,
        SynthVoice = 54,
        OrchestraHit = 55,
        Trumpet = 56,
        Trombone = 57,
        Tuba = 58,
        MutedTrumpet = 59,
        FrenchHorn = 60,
        BrassSection = 61,
        SynthBrass1 = 62,
        SynthBrass2 = 63,
        SopranoSax = 64,
        AltoSax = 65,
        TenorSax = 66,
        BaritoneSax = 67,
        Oboe = 68,
        EnglishHorn = 69,
        Bassoon = 70,
        Clarinet = 71,
        Piccolo = 72,
        Flute = 73,
        Recorder = 74,
        PanFlute = 75,
        BlownBottle = 76,
        Shakuhachi = 77,
        Whistle = 78,
        Ocarina = 79,
        SquareWaveLead = 80,
        SawtooWaveLead = 81,
        CalliopeLead = 82,
        ChiffLead = 83,
        CharangLead = 84,
        VoiceLead = 85,
        FifthsLead = 86,
        BassLead = 87,
        NewAgePad = 88,
        WarmPad = 89,
        PolysynthPad = 90,
        ChoirPad = 91,
        BowedPad = 92,
        MetallicPad = 93,
        HaloPad = 94,
        SweepPad = 95,
        RainEffect = 96,
        SoundtrackEffect = 97,
        CrystalEffect = 98,
        AtmosphereEffect = 99,
        BrightnessEffect = 100,
        GoblinsEffect = 101,
        EchoesEffect = 102,
        SciFiEffect = 103,
        Sitar = 104,
        Banjo = 105,
        Shamisen = 106,
        Koto = 107,
        Kalimba = 108,
        Bagpipe = 109,
        Fiddle = 110,
        Shanai = 111,
        TinkleBell = 112,
        Agogo = 113,
        SteelDrums = 114,
        Woodblock = 115,
        TaikoDrum = 116,
        MelodicTom = 117,
        SynthDrum = 118,
        ReverseCymbal = 119,
        GuitarFretNoise = 120,
        BreathNoise = 121,
        Seashore = 122,
        BirdTweet = 123,
        TelephoneRing = 124,
        Helicopter = 125,
        Applause = 126,
        Gunshot = 127,
    }

    impl GMSoundSet {
        pub fn is_acoustic_grand_piano(self) -> bool {
            self == GMSoundSet::AcousticGrandPiano
        }

        pub fn is_string(self) -> bool {
            self >= GMSoundSet::Violin && self <= GMSoundSet::Timpani
        }

        pub fn is_piano(self) -> bool {
            self >= GMSoundSet::DrawbarOrgan && self <= GMSoundSet::TangoAccordion
        }

        pub fn is_organ(self) -> bool {
            self >= GMSoundSet::AcousticGrandPiano && self <= GMSoundSet::Clavinet
        }

        pub fn is_bass(self) -> bool {
            self >= GMSoundSet::AcousticBass && self <= GMSoundSet::SynthBass2
        }

        pub fn is_brass(self) -> bool {
            self >= GMSoundSet::Trumpet && self <= GMSoundSet::SynthBrass2
        }

        pub fn is_acoustic_guitar(self) -> bool {
            self == GMSoundSet::AcousticGuitarNylon ||
                self == GMSoundSet::AcousticGuitarSteel
        }

        pub fn is_guitar(self) -> bool {
            match self {
                GMSoundSet::AcousticGuitarNylon |
                GMSoundSet::AcousticGuitarSteel |
                GMSoundSet::ElectricGuitarJazz |
                GMSoundSet::ElectricGuitarClean |
                GMSoundSet::ElectricGuitarMuted |
                GMSoundSet::OverdrivenGuitar |
                GMSoundSet::DistortionGuitar |
                GMSoundSet::GuitarHarmonics |
                GMSoundSet::AcousticBass |
                GMSoundSet::ElectricBassFinger |
                GMSoundSet::ElectricBassPick |
                GMSoundSet::FretlessBass |
                GMSoundSet::SlapBass1 |
                GMSoundSet::SlapBass2 |
                GMSoundSet::SynthBass1 |
                GMSoundSet::SynthBass2 => true,
                _ => false
            }
        }

        pub fn is_drums(self) -> bool {
            match self {
                GMSoundSet::SteelDrums |
                GMSoundSet::TaikoDrum |
                GMSoundSet::SynthDrum => true,
                _ => false
            }
        }
    }

    /// The General MIDI percussion sound to play for a given note number when targeting
    /// Channel 10.
    /// As defined in General MIDI System Level 1 (MMA0007 / RP003).
    #[derive(Debug, Clone, Copy, PartialEq, Eq, EnumIter, Display, EnumString)]
    #[repr(u8)]
    pub enum GMPercussionMap {
        AcousticBassDrum = 35,
        BassDrum1 = 36,
        SideStick = 37,
        AcousticSnare = 38,
        HandClap = 39,
        ElectricSnare = 40,
        LowFloorTom = 41,
        ClosedHiHat = 42,
        HighFloorTom = 43,
        PedalHiHat = 44,
        LowTom = 45,
        OpenHiHat = 46,
        LowMidTom = 47,
        HiMidTom = 48,
        CrashCymbal1 = 49,
        HighTom = 50,
        RideCymbal1 = 51,
        ChineseCymbal = 52,
        RideBell = 53,
        Tambourine = 54,
        SplashCymbal = 55,
        Cowbell = 56,
        CrashCymbal2 = 57,
        Vibraslap = 58,
        RideCymbal2 = 59,
        HiBongo = 60,
        LowBongo = 61,
        MuteHiConga = 62,
        OpenHiConga = 63,
        LowConga = 64,
        HighTimbale = 65,
        LowTimbale = 66,
        HighAgogo = 67,
        LowAgogo = 68,
        Cabasa = 69,
        Maracas = 70,
        ShortWhistle = 71,
        LongWhistle = 72,
        ShortGuiro = 73,
        LongGuiro = 74,
        Claves = 75,
        HiWoodBlock = 76,
        LowWoodBlock = 77,
        MuteCuica = 78,
        OpenCuica = 79,
        MuteTriangle = 80,
        OpenTriangle = 81,
    }

    pub fn get_gm_percussion_from_note(note: u8) -> Option<GMPercussionMap> {
        for (i, e) in GMPercussionMap::iter().enumerate() {
            if e as u8 == note { return Some(e); }
        }
        None
    }

    pub fn get_gm_set_from_program(note: u8) -> Option<GMSoundSet> {
        for (i, e) in GMSoundSet::iter().enumerate() {
            if e as u8 == note { return Some(e); }
        }
        None
    }
}

#[cfg(target_arch = "wasm32")]
pub mod SHARED_BUFFERS {
    use js_sys::{SharedArrayBuffer, Int32Array, Uint8Array};

    // Shared buffer layout constants
    pub const HEADER_SIZE: usize = 4; // 4 i32 values for metadata
    pub const MAX_EVENT_SIZE: usize = 1024; // Max size per event
    pub const MAX_EVENTS: usize = 64; // Ring buffer size
    pub const BUFFER_SIZE: usize = HEADER_SIZE * 4 + MAX_EVENT_SIZE * MAX_EVENTS;

    // Header offsets
    pub const WRITE_INDEX: usize = 0;
    pub const READ_INDEX: usize = 1;
    pub const EVENT_COUNT: usize = 2;
    pub const FLAGS: usize = 3;

    pub struct SharedSynthEventBuffer {
        pub shared_buffer: SharedArrayBuffer,
        pub header: Int32Array,
        pub data_buffer: Uint8Array,
    }

    impl SharedSynthEventBuffer {
        pub fn new() -> Self {
            let shared_buffer = SharedArrayBuffer::new(BUFFER_SIZE as u32);
            let header = Int32Array::new_with_byte_offset(&shared_buffer, 0);
            let data_buffer = Uint8Array::new_with_byte_offset_and_length(
                &shared_buffer,
                (HEADER_SIZE * 4) as u32,
                (MAX_EVENT_SIZE * MAX_EVENTS) as u32
            );

            Self { shared_buffer, header, data_buffer }
        }
    }
}