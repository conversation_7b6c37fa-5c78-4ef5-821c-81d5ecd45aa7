// This file is generated by rust-protobuf 2.28.0. Do not edit
// @generated

// https://github.com/rust-lang/rust-clippy/issues/702
#![allow(unknown_lints)]
#![allow(clippy::all)]

#![allow(unused_attributes)]
#![cfg_attr(rustfmt, rustfmt::skip)]

#![allow(box_pointers)]
#![allow(dead_code)]
#![allow(missing_docs)]
#![allow(non_camel_case_types)]
#![allow(non_snake_case)]
#![allow(non_upper_case_globals)]
#![allow(trivial_casts)]
#![allow(unused_imports)]
#![allow(unused_results)]
//! Generated file from `user-renditions.proto`

/// Generated files are compatible only with the same version
/// of protobuf runtime.
// const _PROTOBUF_VERSION_CHECK: () = ::protobuf::VERSION_2_28_0;

#[derive(PartialEq,<PERSON><PERSON>,Default)]
pub struct UserBadges {
    // message fields
    pub badge: Badges,
    // message oneof groups
    pub _value: ::std::option::Option<UserBadges_oneof__value>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserBadges {
    fn default() -> &'a UserBadges {
        <UserBadges as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserBadges_oneof__value {
    value(::std::string::String),
}

impl UserBadges {
    pub fn new() -> UserBadges {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.Badges badge = 1;


    pub fn get_badge(&self) -> Badges {
        self.badge
    }
    pub fn clear_badge(&mut self) {
        self.badge = Badges::V3_CLOSED_ALPHA_TESTER;
    }

    // Param is passed by value, moved
    pub fn set_badge(&mut self, v: Badges) {
        self.badge = v;
    }

    // string value = 2;


    pub fn get_value(&self) -> &str {
        match self._value {
            ::std::option::Option::Some(UserBadges_oneof__value::value(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_value(&mut self) {
        self._value = ::std::option::Option::None;
    }

    pub fn has_value(&self) -> bool {
        match self._value {
            ::std::option::Option::Some(UserBadges_oneof__value::value(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_value(&mut self, v: ::std::string::String) {
        self._value = ::std::option::Option::Some(UserBadges_oneof__value::value(v))
    }

    // Mutable pointer to the field.
    pub fn mut_value(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserBadges_oneof__value::value(_)) = self._value {
        } else {
            self._value = ::std::option::Option::Some(UserBadges_oneof__value::value(::std::string::String::new()));
        }
        match self._value {
            ::std::option::Option::Some(UserBadges_oneof__value::value(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_value(&mut self) -> ::std::string::String {
        if self.has_value() {
            match self._value.take() {
                ::std::option::Option::Some(UserBadges_oneof__value::value(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for UserBadges {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.badge, 1, &mut self.unknown_fields)?
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._value = ::std::option::Option::Some(UserBadges_oneof__value::value(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.badge != Badges::V3_CLOSED_ALPHA_TESTER {
            my_size += ::protobuf::rt::enum_size(1, self.badge);
        }
        if let ::std::option::Option::Some(ref v) = self._value {
            match v {
                &UserBadges_oneof__value::value(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.badge != Badges::V3_CLOSED_ALPHA_TESTER {
            os.write_enum(1, ::protobuf::ProtobufEnum::value(&self.badge))?;
        }
        if let ::std::option::Option::Some(ref v) = self._value {
            match v {
                &UserBadges_oneof__value::value(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserBadges {
        UserBadges::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<Badges>>(
                "badge",
                |m: &UserBadges| { &m.badge },
                |m: &mut UserBadges| { &mut m.badge },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "value",
                UserBadges::has_value,
                UserBadges::get_value,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserBadges>(
                "UserBadges",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserBadges {
        static instance: ::protobuf::rt::LazyV2<UserBadges> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserBadges::new)
    }
}

impl ::protobuf::Clear for UserBadges {
    fn clear(&mut self) {
        self.badge = Badges::V3_CLOSED_ALPHA_TESTER;
        self._value = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserBadges {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserBadges {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserMetaDto {
    // message fields
    pub bot: bool,
    pub discordBot: bool,
    // message oneof groups
    pub _modifiedDate: ::std::option::Option<UserMetaDto_oneof__modifiedDate>,
    pub _clientMetaDetails: ::std::option::Option<UserMetaDto_oneof__clientMetaDetails>,
    pub _enteredRoomDateTime: ::std::option::Option<UserMetaDto_oneof__enteredRoomDateTime>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserMetaDto {
    fn default() -> &'a UserMetaDto {
        <UserMetaDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserMetaDto_oneof__modifiedDate {
    modifiedDate(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserMetaDto_oneof__clientMetaDetails {
    clientMetaDetails(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserMetaDto_oneof__enteredRoomDateTime {
    enteredRoomDateTime(::std::string::String),
}

impl UserMetaDto {
    pub fn new() -> UserMetaDto {
        ::std::default::Default::default()
    }

    // bool bot = 1;


    pub fn get_bot(&self) -> bool {
        self.bot
    }
    pub fn clear_bot(&mut self) {
        self.bot = false;
    }

    // Param is passed by value, moved
    pub fn set_bot(&mut self, v: bool) {
        self.bot = v;
    }

    // bool discordBot = 2;


    pub fn get_discordBot(&self) -> bool {
        self.discordBot
    }
    pub fn clear_discordBot(&mut self) {
        self.discordBot = false;
    }

    // Param is passed by value, moved
    pub fn set_discordBot(&mut self, v: bool) {
        self.discordBot = v;
    }

    // string modifiedDate = 3;


    pub fn get_modifiedDate(&self) -> &str {
        match self._modifiedDate {
            ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_modifiedDate(&mut self) {
        self._modifiedDate = ::std::option::Option::None;
    }

    pub fn has_modifiedDate(&self) -> bool {
        match self._modifiedDate {
            ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_modifiedDate(&mut self, v: ::std::string::String) {
        self._modifiedDate = ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(v))
    }

    // Mutable pointer to the field.
    pub fn mut_modifiedDate(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(_)) = self._modifiedDate {
        } else {
            self._modifiedDate = ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(::std::string::String::new()));
        }
        match self._modifiedDate {
            ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_modifiedDate(&mut self) -> ::std::string::String {
        if self.has_modifiedDate() {
            match self._modifiedDate.take() {
                ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string clientMetaDetails = 4;


    pub fn get_clientMetaDetails(&self) -> &str {
        match self._clientMetaDetails {
            ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_clientMetaDetails(&mut self) {
        self._clientMetaDetails = ::std::option::Option::None;
    }

    pub fn has_clientMetaDetails(&self) -> bool {
        match self._clientMetaDetails {
            ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientMetaDetails(&mut self, v: ::std::string::String) {
        self._clientMetaDetails = ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientMetaDetails(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(_)) = self._clientMetaDetails {
        } else {
            self._clientMetaDetails = ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(::std::string::String::new()));
        }
        match self._clientMetaDetails {
            ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientMetaDetails(&mut self) -> ::std::string::String {
        if self.has_clientMetaDetails() {
            match self._clientMetaDetails.take() {
                ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string enteredRoomDateTime = 6;


    pub fn get_enteredRoomDateTime(&self) -> &str {
        match self._enteredRoomDateTime {
            ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_enteredRoomDateTime(&mut self) {
        self._enteredRoomDateTime = ::std::option::Option::None;
    }

    pub fn has_enteredRoomDateTime(&self) -> bool {
        match self._enteredRoomDateTime {
            ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_enteredRoomDateTime(&mut self, v: ::std::string::String) {
        self._enteredRoomDateTime = ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(v))
    }

    // Mutable pointer to the field.
    pub fn mut_enteredRoomDateTime(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(_)) = self._enteredRoomDateTime {
        } else {
            self._enteredRoomDateTime = ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(::std::string::String::new()));
        }
        match self._enteredRoomDateTime {
            ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_enteredRoomDateTime(&mut self) -> ::std::string::String {
        if self.has_enteredRoomDateTime() {
            match self._enteredRoomDateTime.take() {
                ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }
}

impl ::protobuf::Message for UserMetaDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.bot = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.discordBot = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._modifiedDate = ::std::option::Option::Some(UserMetaDto_oneof__modifiedDate::modifiedDate(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._clientMetaDetails = ::std::option::Option::Some(UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(is.read_string()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._enteredRoomDateTime = ::std::option::Option::Some(UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(is.read_string()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.bot != false {
            my_size += 2;
        }
        if self.discordBot != false {
            my_size += 2;
        }
        if let ::std::option::Option::Some(ref v) = self._modifiedDate {
            match v {
                &UserMetaDto_oneof__modifiedDate::modifiedDate(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._clientMetaDetails {
            match v {
                &UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._enteredRoomDateTime {
            match v {
                &UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.bot != false {
            os.write_bool(1, self.bot)?;
        }
        if self.discordBot != false {
            os.write_bool(2, self.discordBot)?;
        }
        if let ::std::option::Option::Some(ref v) = self._modifiedDate {
            match v {
                &UserMetaDto_oneof__modifiedDate::modifiedDate(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._clientMetaDetails {
            match v {
                &UserMetaDto_oneof__clientMetaDetails::clientMetaDetails(ref v) => {
                    os.write_string(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._enteredRoomDateTime {
            match v {
                &UserMetaDto_oneof__enteredRoomDateTime::enteredRoomDateTime(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserMetaDto {
        UserMetaDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "bot",
                |m: &UserMetaDto| { &m.bot },
                |m: &mut UserMetaDto| { &mut m.bot },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "discordBot",
                |m: &UserMetaDto| { &m.discordBot },
                |m: &mut UserMetaDto| { &mut m.discordBot },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "modifiedDate",
                UserMetaDto::has_modifiedDate,
                UserMetaDto::get_modifiedDate,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "clientMetaDetails",
                UserMetaDto::has_clientMetaDetails,
                UserMetaDto::get_clientMetaDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "enteredRoomDateTime",
                UserMetaDto::has_enteredRoomDateTime,
                UserMetaDto::get_enteredRoomDateTime,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserMetaDto>(
                "UserMetaDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserMetaDto {
        static instance: ::protobuf::rt::LazyV2<UserMetaDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserMetaDto::new)
    }
}

impl ::protobuf::Clear for UserMetaDto {
    fn clear(&mut self) {
        self.bot = false;
        self.discordBot = false;
        self._modifiedDate = ::std::option::Option::None;
        self._clientMetaDetails = ::std::option::Option::None;
        self._enteredRoomDateTime = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserMetaDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserMetaDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AvatarWorldDataDto {
    // message oneof groups
    pub _worldPosition: ::std::option::Option<AvatarWorldDataDto_oneof__worldPosition>,
    pub _pianoBenchSeat: ::std::option::Option<AvatarWorldDataDto_oneof__pianoBenchSeat>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AvatarWorldDataDto {
    fn default() -> &'a AvatarWorldDataDto {
        <AvatarWorldDataDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarWorldDataDto_oneof__worldPosition {
    worldPosition(AvatarWorldDataDto_AvatarMessageWorldPosition),
}

#[derive(Clone,PartialEq,Debug)]
pub enum AvatarWorldDataDto_oneof__pianoBenchSeat {
    pianoBenchSeat(i32),
}

impl AvatarWorldDataDto {
    pub fn new() -> AvatarWorldDataDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition worldPosition = 1;


    pub fn get_worldPosition(&self) -> &AvatarWorldDataDto_AvatarMessageWorldPosition {
        match self._worldPosition {
            ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(ref v)) => v,
            _ => <AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_worldPosition(&mut self) {
        self._worldPosition = ::std::option::Option::None;
    }

    pub fn has_worldPosition(&self) -> bool {
        match self._worldPosition {
            ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_worldPosition(&mut self, v: AvatarWorldDataDto_AvatarMessageWorldPosition) {
        self._worldPosition = ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(v))
    }

    // Mutable pointer to the field.
    pub fn mut_worldPosition(&mut self) -> &mut AvatarWorldDataDto_AvatarMessageWorldPosition {
        if let ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(_)) = self._worldPosition {
        } else {
            self._worldPosition = ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(AvatarWorldDataDto_AvatarMessageWorldPosition::new()));
        }
        match self._worldPosition {
            ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_worldPosition(&mut self) -> AvatarWorldDataDto_AvatarMessageWorldPosition {
        if self.has_worldPosition() {
            match self._worldPosition.take() {
                ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(v)) => v,
                _ => panic!(),
            }
        } else {
            AvatarWorldDataDto_AvatarMessageWorldPosition::new()
        }
    }

    // int32 pianoBenchSeat = 2;


    pub fn get_pianoBenchSeat(&self) -> i32 {
        match self._pianoBenchSeat {
            ::std::option::Option::Some(AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_pianoBenchSeat(&mut self) {
        self._pianoBenchSeat = ::std::option::Option::None;
    }

    pub fn has_pianoBenchSeat(&self) -> bool {
        match self._pianoBenchSeat {
            ::std::option::Option::Some(AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_pianoBenchSeat(&mut self, v: i32) {
        self._pianoBenchSeat = ::std::option::Option::Some(AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(v))
    }
}

impl ::protobuf::Message for AvatarWorldDataDto {
    fn is_initialized(&self) -> bool {
        if let Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(ref v)) = self._worldPosition {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._worldPosition = ::std::option::Option::Some(AvatarWorldDataDto_oneof__worldPosition::worldPosition(is.read_message()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._pianoBenchSeat = ::std::option::Option::Some(AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(is.read_int32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let ::std::option::Option::Some(ref v) = self._worldPosition {
            match v {
                &AvatarWorldDataDto_oneof__worldPosition::worldPosition(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pianoBenchSeat {
            match v {
                &AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(v) => {
                    my_size += ::protobuf::rt::value_size(2, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let ::std::option::Option::Some(ref v) = self._worldPosition {
            match v {
                &AvatarWorldDataDto_oneof__worldPosition::worldPosition(ref v) => {
                    os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._pianoBenchSeat {
            match v {
                &AvatarWorldDataDto_oneof__pianoBenchSeat::pianoBenchSeat(v) => {
                    os.write_int32(2, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AvatarWorldDataDto {
        AvatarWorldDataDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "worldPosition",
                AvatarWorldDataDto::has_worldPosition,
                AvatarWorldDataDto::get_worldPosition,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "pianoBenchSeat",
                AvatarWorldDataDto::has_pianoBenchSeat,
                AvatarWorldDataDto::get_pianoBenchSeat,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AvatarWorldDataDto>(
                "AvatarWorldDataDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AvatarWorldDataDto {
        static instance: ::protobuf::rt::LazyV2<AvatarWorldDataDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AvatarWorldDataDto::new)
    }
}

impl ::protobuf::Clear for AvatarWorldDataDto {
    fn clear(&mut self) {
        self._worldPosition = ::std::option::Option::None;
        self._pianoBenchSeat = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AvatarWorldDataDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarWorldDataDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct AvatarWorldDataDto_AvatarMessageWorldPosition {
    // message fields
    pub x: f64,
    pub y: f64,
    pub z: f64,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a AvatarWorldDataDto_AvatarMessageWorldPosition {
    fn default() -> &'a AvatarWorldDataDto_AvatarMessageWorldPosition {
        <AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance()
    }
}

impl AvatarWorldDataDto_AvatarMessageWorldPosition {
    pub fn new() -> AvatarWorldDataDto_AvatarMessageWorldPosition {
        ::std::default::Default::default()
    }

    // double x = 1;


    pub fn get_x(&self) -> f64 {
        self.x
    }
    pub fn clear_x(&mut self) {
        self.x = 0.;
    }

    // Param is passed by value, moved
    pub fn set_x(&mut self, v: f64) {
        self.x = v;
    }

    // double y = 2;


    pub fn get_y(&self) -> f64 {
        self.y
    }
    pub fn clear_y(&mut self) {
        self.y = 0.;
    }

    // Param is passed by value, moved
    pub fn set_y(&mut self, v: f64) {
        self.y = v;
    }

    // double z = 3;


    pub fn get_z(&self) -> f64 {
        self.z
    }
    pub fn clear_z(&mut self) {
        self.z = 0.;
    }

    // Param is passed by value, moved
    pub fn set_z(&mut self, v: f64) {
        self.z = v;
    }
}

impl ::protobuf::Message for AvatarWorldDataDto_AvatarMessageWorldPosition {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.x = tmp;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.y = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeFixed64 {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_double()?;
                    self.z = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if self.x != 0. {
            my_size += 9;
        }
        if self.y != 0. {
            my_size += 9;
        }
        if self.z != 0. {
            my_size += 9;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if self.x != 0. {
            os.write_double(1, self.x)?;
        }
        if self.y != 0. {
            os.write_double(2, self.y)?;
        }
        if self.z != 0. {
            os.write_double(3, self.z)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> AvatarWorldDataDto_AvatarMessageWorldPosition {
        AvatarWorldDataDto_AvatarMessageWorldPosition::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "x",
                |m: &AvatarWorldDataDto_AvatarMessageWorldPosition| { &m.x },
                |m: &mut AvatarWorldDataDto_AvatarMessageWorldPosition| { &mut m.x },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "y",
                |m: &AvatarWorldDataDto_AvatarMessageWorldPosition| { &m.y },
                |m: &mut AvatarWorldDataDto_AvatarMessageWorldPosition| { &mut m.y },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeDouble>(
                "z",
                |m: &AvatarWorldDataDto_AvatarMessageWorldPosition| { &m.z },
                |m: &mut AvatarWorldDataDto_AvatarMessageWorldPosition| { &mut m.z },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "AvatarWorldDataDto.AvatarMessageWorldPosition",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static AvatarWorldDataDto_AvatarMessageWorldPosition {
        static instance: ::protobuf::rt::LazyV2<AvatarWorldDataDto_AvatarMessageWorldPosition> = ::protobuf::rt::LazyV2::INIT;
        instance.get(AvatarWorldDataDto_AvatarMessageWorldPosition::new)
    }
}

impl ::protobuf::Clear for AvatarWorldDataDto_AvatarMessageWorldPosition {
    fn clear(&mut self) {
        self.x = 0.;
        self.y = 0.;
        self.z = 0.;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for AvatarWorldDataDto_AvatarMessageWorldPosition {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for AvatarWorldDataDto_AvatarMessageWorldPosition {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct WorldData {
    // message oneof groups
    pub _characterDataJSON: ::std::option::Option<WorldData_oneof__characterDataJSON>,
    pub _orchestraModelCustomizationDataJSON: ::std::option::Option<WorldData_oneof__orchestraModelCustomizationDataJSON>,
    pub _avatarWorldData: ::std::option::Option<WorldData_oneof__avatarWorldData>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a WorldData {
    fn default() -> &'a WorldData {
        <WorldData as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldData_oneof__characterDataJSON {
    characterDataJSON(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldData_oneof__orchestraModelCustomizationDataJSON {
    orchestraModelCustomizationDataJSON(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum WorldData_oneof__avatarWorldData {
    avatarWorldData(AvatarWorldDataDto),
}

impl WorldData {
    pub fn new() -> WorldData {
        ::std::default::Default::default()
    }

    // string characterDataJSON = 1;


    pub fn get_characterDataJSON(&self) -> &str {
        match self._characterDataJSON {
            ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_characterDataJSON(&mut self) {
        self._characterDataJSON = ::std::option::Option::None;
    }

    pub fn has_characterDataJSON(&self) -> bool {
        match self._characterDataJSON {
            ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_characterDataJSON(&mut self, v: ::std::string::String) {
        self._characterDataJSON = ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(v))
    }

    // Mutable pointer to the field.
    pub fn mut_characterDataJSON(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(_)) = self._characterDataJSON {
        } else {
            self._characterDataJSON = ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(::std::string::String::new()));
        }
        match self._characterDataJSON {
            ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_characterDataJSON(&mut self) -> ::std::string::String {
        if self.has_characterDataJSON() {
            match self._characterDataJSON.take() {
                ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string orchestraModelCustomizationDataJSON = 2;


    pub fn get_orchestraModelCustomizationDataJSON(&self) -> &str {
        match self._orchestraModelCustomizationDataJSON {
            ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_orchestraModelCustomizationDataJSON(&mut self) {
        self._orchestraModelCustomizationDataJSON = ::std::option::Option::None;
    }

    pub fn has_orchestraModelCustomizationDataJSON(&self) -> bool {
        match self._orchestraModelCustomizationDataJSON {
            ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_orchestraModelCustomizationDataJSON(&mut self, v: ::std::string::String) {
        self._orchestraModelCustomizationDataJSON = ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(v))
    }

    // Mutable pointer to the field.
    pub fn mut_orchestraModelCustomizationDataJSON(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(_)) = self._orchestraModelCustomizationDataJSON {
        } else {
            self._orchestraModelCustomizationDataJSON = ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(::std::string::String::new()));
        }
        match self._orchestraModelCustomizationDataJSON {
            ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_orchestraModelCustomizationDataJSON(&mut self) -> ::std::string::String {
        if self.has_orchestraModelCustomizationDataJSON() {
            match self._orchestraModelCustomizationDataJSON.take() {
                ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto avatarWorldData = 3;


    pub fn get_avatarWorldData(&self) -> &AvatarWorldDataDto {
        match self._avatarWorldData {
            ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(ref v)) => v,
            _ => <AvatarWorldDataDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarWorldData(&mut self) {
        self._avatarWorldData = ::std::option::Option::None;
    }

    pub fn has_avatarWorldData(&self) -> bool {
        match self._avatarWorldData {
            ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarWorldData(&mut self, v: AvatarWorldDataDto) {
        self._avatarWorldData = ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarWorldData(&mut self) -> &mut AvatarWorldDataDto {
        if let ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(_)) = self._avatarWorldData {
        } else {
            self._avatarWorldData = ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(AvatarWorldDataDto::new()));
        }
        match self._avatarWorldData {
            ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarWorldData(&mut self) -> AvatarWorldDataDto {
        if self.has_avatarWorldData() {
            match self._avatarWorldData.take() {
                ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(v)) => v,
                _ => panic!(),
            }
        } else {
            AvatarWorldDataDto::new()
        }
    }
}

impl ::protobuf::Message for WorldData {
    fn is_initialized(&self) -> bool {
        if let Some(WorldData_oneof__avatarWorldData::avatarWorldData(ref v)) = self._avatarWorldData {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._characterDataJSON = ::std::option::Option::Some(WorldData_oneof__characterDataJSON::characterDataJSON(is.read_string()?));
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._orchestraModelCustomizationDataJSON = ::std::option::Option::Some(WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._avatarWorldData = ::std::option::Option::Some(WorldData_oneof__avatarWorldData::avatarWorldData(is.read_message()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let ::std::option::Option::Some(ref v) = self._characterDataJSON {
            match v {
                &WorldData_oneof__characterDataJSON::characterDataJSON(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._orchestraModelCustomizationDataJSON {
            match v {
                &WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._avatarWorldData {
            match v {
                &WorldData_oneof__avatarWorldData::avatarWorldData(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let ::std::option::Option::Some(ref v) = self._characterDataJSON {
            match v {
                &WorldData_oneof__characterDataJSON::characterDataJSON(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._orchestraModelCustomizationDataJSON {
            match v {
                &WorldData_oneof__orchestraModelCustomizationDataJSON::orchestraModelCustomizationDataJSON(ref v) => {
                    os.write_string(2, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._avatarWorldData {
            match v {
                &WorldData_oneof__avatarWorldData::avatarWorldData(ref v) => {
                    os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> WorldData {
        WorldData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "characterDataJSON",
                WorldData::has_characterDataJSON,
                WorldData::get_characterDataJSON,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "orchestraModelCustomizationDataJSON",
                WorldData::has_orchestraModelCustomizationDataJSON,
                WorldData::get_orchestraModelCustomizationDataJSON,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AvatarWorldDataDto>(
                "avatarWorldData",
                WorldData::has_avatarWorldData,
                WorldData::get_avatarWorldData,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<WorldData>(
                "WorldData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static WorldData {
        static instance: ::protobuf::rt::LazyV2<WorldData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(WorldData::new)
    }
}

impl ::protobuf::Clear for WorldData {
    fn clear(&mut self) {
        self._characterDataJSON = ::std::option::Option::None;
        self._orchestraModelCustomizationDataJSON = ::std::option::Option::None;
        self._avatarWorldData = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for WorldData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for WorldData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserDto {
    // message fields
    pub username: ::std::string::String,
    pub usertag: ::std::string::String,
    pub socketID: ::std::string::String,
    pub roles: ::std::vec::Vec<Roles>,
    pub badges: ::protobuf::RepeatedField<UserBadges>,
    pub color: ::std::string::String,
    pub meta: ::protobuf::SingularPtrField<UserMetaDto>,
    pub selfMuted: bool,
    pub serverNotesMuted: bool,
    pub serverChatMuted: bool,
    pub status: UserStatus,
    pub worldData: ::protobuf::SingularPtrField<WorldData>,
    pub isProMember: bool,
    pub uuid: ::std::string::String,
    // message oneof groups
    pub _nickname: ::std::option::Option<UserDto_oneof__nickname>,
    pub _ProfileDescription: ::std::option::Option<UserDto_oneof__ProfileDescription>,
    pub _profileImageLastModified: ::std::option::Option<UserDto_oneof__profileImageLastModified>,
    pub _profileBackgroundImageLastModified: ::std::option::Option<UserDto_oneof__profileBackgroundImageLastModified>,
    pub _statusText: ::std::option::Option<UserDto_oneof__statusText>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserDto {
    fn default() -> &'a UserDto {
        <UserDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserDto_oneof__nickname {
    nickname(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserDto_oneof__ProfileDescription {
    ProfileDescription(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserDto_oneof__profileImageLastModified {
    profileImageLastModified(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserDto_oneof__profileBackgroundImageLastModified {
    profileBackgroundImageLastModified(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserDto_oneof__statusText {
    statusText(::std::string::String),
}

impl UserDto {
    pub fn new() -> UserDto {
        ::std::default::Default::default()
    }

    // string username = 1;


    pub fn get_username(&self) -> &str {
        &self.username
    }
    pub fn clear_username(&mut self) {
        self.username.clear();
    }

    // Param is passed by value, moved
    pub fn set_username(&mut self, v: ::std::string::String) {
        self.username = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_username(&mut self) -> &mut ::std::string::String {
        &mut self.username
    }

    // Take field
    pub fn take_username(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.username, ::std::string::String::new())
    }

    // string usertag = 2;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string socketID = 3;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.Roles roles = 4;


    pub fn get_roles(&self) -> &[Roles] {
        &self.roles
    }
    pub fn clear_roles(&mut self) {
        self.roles.clear();
    }

    // Param is passed by value, moved
    pub fn set_roles(&mut self, v: ::std::vec::Vec<Roles>) {
        self.roles = v;
    }

    // Mutable pointer to the field.
    pub fn mut_roles(&mut self) -> &mut ::std::vec::Vec<Roles> {
        &mut self.roles
    }

    // Take field
    pub fn take_roles(&mut self) -> ::std::vec::Vec<Roles> {
        ::std::mem::replace(&mut self.roles, ::std::vec::Vec::new())
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBadges badges = 5;


    pub fn get_badges(&self) -> &[UserBadges] {
        &self.badges
    }
    pub fn clear_badges(&mut self) {
        self.badges.clear();
    }

    // Param is passed by value, moved
    pub fn set_badges(&mut self, v: ::protobuf::RepeatedField<UserBadges>) {
        self.badges = v;
    }

    // Mutable pointer to the field.
    pub fn mut_badges(&mut self) -> &mut ::protobuf::RepeatedField<UserBadges> {
        &mut self.badges
    }

    // Take field
    pub fn take_badges(&mut self) -> ::protobuf::RepeatedField<UserBadges> {
        ::std::mem::replace(&mut self.badges, ::protobuf::RepeatedField::new())
    }

    // string nickname = 6;


    pub fn get_nickname(&self) -> &str {
        match self._nickname {
            ::std::option::Option::Some(UserDto_oneof__nickname::nickname(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_nickname(&mut self) {
        self._nickname = ::std::option::Option::None;
    }

    pub fn has_nickname(&self) -> bool {
        match self._nickname {
            ::std::option::Option::Some(UserDto_oneof__nickname::nickname(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_nickname(&mut self, v: ::std::string::String) {
        self._nickname = ::std::option::Option::Some(UserDto_oneof__nickname::nickname(v))
    }

    // Mutable pointer to the field.
    pub fn mut_nickname(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserDto_oneof__nickname::nickname(_)) = self._nickname {
        } else {
            self._nickname = ::std::option::Option::Some(UserDto_oneof__nickname::nickname(::std::string::String::new()));
        }
        match self._nickname {
            ::std::option::Option::Some(UserDto_oneof__nickname::nickname(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_nickname(&mut self) -> ::std::string::String {
        if self.has_nickname() {
            match self._nickname.take() {
                ::std::option::Option::Some(UserDto_oneof__nickname::nickname(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string color = 7;


    pub fn get_color(&self) -> &str {
        &self.color
    }
    pub fn clear_color(&mut self) {
        self.color.clear();
    }

    // Param is passed by value, moved
    pub fn set_color(&mut self, v: ::std::string::String) {
        self.color = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_color(&mut self) -> &mut ::std::string::String {
        &mut self.color
    }

    // Take field
    pub fn take_color(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.color, ::std::string::String::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserMetaDto meta = 8;


    pub fn get_meta(&self) -> &UserMetaDto {
        self.meta.as_ref().unwrap_or_else(|| <UserMetaDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_meta(&mut self) {
        self.meta.clear();
    }

    pub fn has_meta(&self) -> bool {
        self.meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_meta(&mut self, v: UserMetaDto) {
        self.meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_meta(&mut self) -> &mut UserMetaDto {
        if self.meta.is_none() {
            self.meta.set_default();
        }
        self.meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_meta(&mut self) -> UserMetaDto {
        self.meta.take().unwrap_or_else(|| UserMetaDto::new())
    }

    // bool selfMuted = 9;


    pub fn get_selfMuted(&self) -> bool {
        self.selfMuted
    }
    pub fn clear_selfMuted(&mut self) {
        self.selfMuted = false;
    }

    // Param is passed by value, moved
    pub fn set_selfMuted(&mut self, v: bool) {
        self.selfMuted = v;
    }

    // bool serverNotesMuted = 10;


    pub fn get_serverNotesMuted(&self) -> bool {
        self.serverNotesMuted
    }
    pub fn clear_serverNotesMuted(&mut self) {
        self.serverNotesMuted = false;
    }

    // Param is passed by value, moved
    pub fn set_serverNotesMuted(&mut self, v: bool) {
        self.serverNotesMuted = v;
    }

    // bool serverChatMuted = 11;


    pub fn get_serverChatMuted(&self) -> bool {
        self.serverChatMuted
    }
    pub fn clear_serverChatMuted(&mut self) {
        self.serverChatMuted = false;
    }

    // Param is passed by value, moved
    pub fn set_serverChatMuted(&mut self, v: bool) {
        self.serverChatMuted = v;
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserStatus status = 12;


    pub fn get_status(&self) -> UserStatus {
        self.status
    }
    pub fn clear_status(&mut self) {
        self.status = UserStatus::None;
    }

    // Param is passed by value, moved
    pub fn set_status(&mut self, v: UserStatus) {
        self.status = v;
    }

    // string ProfileDescription = 13;


    pub fn get_ProfileDescription(&self) -> &str {
        match self._ProfileDescription {
            ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_ProfileDescription(&mut self) {
        self._ProfileDescription = ::std::option::Option::None;
    }

    pub fn has_ProfileDescription(&self) -> bool {
        match self._ProfileDescription {
            ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_ProfileDescription(&mut self, v: ::std::string::String) {
        self._ProfileDescription = ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(v))
    }

    // Mutable pointer to the field.
    pub fn mut_ProfileDescription(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(_)) = self._ProfileDescription {
        } else {
            self._ProfileDescription = ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(::std::string::String::new()));
        }
        match self._ProfileDescription {
            ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_ProfileDescription(&mut self) -> ::std::string::String {
        if self.has_ProfileDescription() {
            match self._ProfileDescription.take() {
                ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileImageLastModified = 14;


    pub fn get_profileImageLastModified(&self) -> &str {
        match self._profileImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileImageLastModified(&mut self) {
        self._profileImageLastModified = ::std::option::Option::None;
    }

    pub fn has_profileImageLastModified(&self) -> bool {
        match self._profileImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileImageLastModified(&mut self, v: ::std::string::String) {
        self._profileImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileImageLastModified(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(_)) = self._profileImageLastModified {
        } else {
            self._profileImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(::std::string::String::new()));
        }
        match self._profileImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileImageLastModified(&mut self) -> ::std::string::String {
        if self.has_profileImageLastModified() {
            match self._profileImageLastModified.take() {
                ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileBackgroundImageLastModified = 15;


    pub fn get_profileBackgroundImageLastModified(&self) -> &str {
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileBackgroundImageLastModified(&mut self) {
        self._profileBackgroundImageLastModified = ::std::option::Option::None;
    }

    pub fn has_profileBackgroundImageLastModified(&self) -> bool {
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileBackgroundImageLastModified(&mut self, v: ::std::string::String) {
        self._profileBackgroundImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileBackgroundImageLastModified(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(_)) = self._profileBackgroundImageLastModified {
        } else {
            self._profileBackgroundImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(::std::string::String::new()));
        }
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileBackgroundImageLastModified(&mut self) -> ::std::string::String {
        if self.has_profileBackgroundImageLastModified() {
            match self._profileBackgroundImageLastModified.take() {
                ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.WorldData worldData = 16;


    pub fn get_worldData(&self) -> &WorldData {
        self.worldData.as_ref().unwrap_or_else(|| <WorldData as ::protobuf::Message>::default_instance())
    }
    pub fn clear_worldData(&mut self) {
        self.worldData.clear();
    }

    pub fn has_worldData(&self) -> bool {
        self.worldData.is_some()
    }

    // Param is passed by value, moved
    pub fn set_worldData(&mut self, v: WorldData) {
        self.worldData = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_worldData(&mut self) -> &mut WorldData {
        if self.worldData.is_none() {
            self.worldData.set_default();
        }
        self.worldData.as_mut().unwrap()
    }

    // Take field
    pub fn take_worldData(&mut self) -> WorldData {
        self.worldData.take().unwrap_or_else(|| WorldData::new())
    }

    // bool isProMember = 17;


    pub fn get_isProMember(&self) -> bool {
        self.isProMember
    }
    pub fn clear_isProMember(&mut self) {
        self.isProMember = false;
    }

    // Param is passed by value, moved
    pub fn set_isProMember(&mut self, v: bool) {
        self.isProMember = v;
    }

    // string statusText = 18;


    pub fn get_statusText(&self) -> &str {
        match self._statusText {
            ::std::option::Option::Some(UserDto_oneof__statusText::statusText(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_statusText(&mut self) {
        self._statusText = ::std::option::Option::None;
    }

    pub fn has_statusText(&self) -> bool {
        match self._statusText {
            ::std::option::Option::Some(UserDto_oneof__statusText::statusText(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_statusText(&mut self, v: ::std::string::String) {
        self._statusText = ::std::option::Option::Some(UserDto_oneof__statusText::statusText(v))
    }

    // Mutable pointer to the field.
    pub fn mut_statusText(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserDto_oneof__statusText::statusText(_)) = self._statusText {
        } else {
            self._statusText = ::std::option::Option::Some(UserDto_oneof__statusText::statusText(::std::string::String::new()));
        }
        match self._statusText {
            ::std::option::Option::Some(UserDto_oneof__statusText::statusText(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_statusText(&mut self) -> ::std::string::String {
        if self.has_statusText() {
            match self._statusText.take() {
                ::std::option::Option::Some(UserDto_oneof__statusText::statusText(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string uuid = 19;


    pub fn get_uuid(&self) -> &str {
        &self.uuid
    }
    pub fn clear_uuid(&mut self) {
        self.uuid.clear();
    }

    // Param is passed by value, moved
    pub fn set_uuid(&mut self, v: ::std::string::String) {
        self.uuid = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_uuid(&mut self) -> &mut ::std::string::String {
        &mut self.uuid
    }

    // Take field
    pub fn take_uuid(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.uuid, ::std::string::String::new())
    }
}

impl ::protobuf::Message for UserDto {
    fn is_initialized(&self) -> bool {
        for v in &self.badges {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.meta {
            if !v.is_initialized() {
                return false;
            }
        };
        for v in &self.worldData {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.username)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                4 => {
                    ::protobuf::rt::read_repeated_enum_with_unknown_fields_into(wire_type, is, &mut self.roles, 4, &mut self.unknown_fields)?
                },
                5 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.badges)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._nickname = ::std::option::Option::Some(UserDto_oneof__nickname::nickname(is.read_string()?));
                },
                7 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.color)?;
                },
                8 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.meta)?;
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.selfMuted = tmp;
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.serverNotesMuted = tmp;
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.serverChatMuted = tmp;
                },
                12 => {
                    ::protobuf::rt::read_proto3_enum_with_unknown_fields_into(wire_type, is, &mut self.status, 12, &mut self.unknown_fields)?
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._ProfileDescription = ::std::option::Option::Some(UserDto_oneof__ProfileDescription::ProfileDescription(is.read_string()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._profileImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileImageLastModified::profileImageLastModified(is.read_string()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._profileBackgroundImageLastModified = ::std::option::Option::Some(UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(is.read_string()?));
                },
                16 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.worldData)?;
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isProMember = tmp;
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._statusText = ::std::option::Option::Some(UserDto_oneof__statusText::statusText(is.read_string()?));
                },
                19 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.uuid)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.username.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.username);
        }
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.usertag);
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.socketID);
        }
        for value in &self.roles {
            my_size += ::protobuf::rt::enum_size(4, *value);
        };
        for value in &self.badges {
            let len = value.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if !self.color.is_empty() {
            my_size += ::protobuf::rt::string_size(7, &self.color);
        }
        if let Some(ref v) = self.meta.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.selfMuted != false {
            my_size += 2;
        }
        if self.serverNotesMuted != false {
            my_size += 2;
        }
        if self.serverChatMuted != false {
            my_size += 2;
        }
        if self.status != UserStatus::None {
            my_size += ::protobuf::rt::enum_size(12, self.status);
        }
        if let Some(ref v) = self.worldData.as_ref() {
            let len = v.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.isProMember != false {
            my_size += 3;
        }
        if !self.uuid.is_empty() {
            my_size += ::protobuf::rt::string_size(19, &self.uuid);
        }
        if let ::std::option::Option::Some(ref v) = self._nickname {
            match v {
                &UserDto_oneof__nickname::nickname(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._ProfileDescription {
            match v {
                &UserDto_oneof__ProfileDescription::ProfileDescription(ref v) => {
                    my_size += ::protobuf::rt::string_size(13, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileImageLastModified {
            match v {
                &UserDto_oneof__profileImageLastModified::profileImageLastModified(ref v) => {
                    my_size += ::protobuf::rt::string_size(14, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileBackgroundImageLastModified {
            match v {
                &UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v) => {
                    my_size += ::protobuf::rt::string_size(15, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._statusText {
            match v {
                &UserDto_oneof__statusText::statusText(ref v) => {
                    my_size += ::protobuf::rt::string_size(18, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.username.is_empty() {
            os.write_string(1, &self.username)?;
        }
        if !self.usertag.is_empty() {
            os.write_string(2, &self.usertag)?;
        }
        if !self.socketID.is_empty() {
            os.write_string(3, &self.socketID)?;
        }
        for v in &self.roles {
            os.write_enum(4, ::protobuf::ProtobufEnum::value(v))?;
        };
        for v in &self.badges {
            os.write_tag(5, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if !self.color.is_empty() {
            os.write_string(7, &self.color)?;
        }
        if let Some(ref v) = self.meta.as_ref() {
            os.write_tag(8, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.selfMuted != false {
            os.write_bool(9, self.selfMuted)?;
        }
        if self.serverNotesMuted != false {
            os.write_bool(10, self.serverNotesMuted)?;
        }
        if self.serverChatMuted != false {
            os.write_bool(11, self.serverChatMuted)?;
        }
        if self.status != UserStatus::None {
            os.write_enum(12, ::protobuf::ProtobufEnum::value(&self.status))?;
        }
        if let Some(ref v) = self.worldData.as_ref() {
            os.write_tag(16, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.isProMember != false {
            os.write_bool(17, self.isProMember)?;
        }
        if !self.uuid.is_empty() {
            os.write_string(19, &self.uuid)?;
        }
        if let ::std::option::Option::Some(ref v) = self._nickname {
            match v {
                &UserDto_oneof__nickname::nickname(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._ProfileDescription {
            match v {
                &UserDto_oneof__ProfileDescription::ProfileDescription(ref v) => {
                    os.write_string(13, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileImageLastModified {
            match v {
                &UserDto_oneof__profileImageLastModified::profileImageLastModified(ref v) => {
                    os.write_string(14, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileBackgroundImageLastModified {
            match v {
                &UserDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v) => {
                    os.write_string(15, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._statusText {
            match v {
                &UserDto_oneof__statusText::statusText(ref v) => {
                    os.write_string(18, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserDto {
        UserDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "username",
                |m: &UserDto| { &m.username },
                |m: &mut UserDto| { &mut m.username },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &UserDto| { &m.usertag },
                |m: &mut UserDto| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &UserDto| { &m.socketID },
                |m: &mut UserDto| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeEnum<Roles>>(
                "roles",
                |m: &UserDto| { &m.roles },
                |m: &mut UserDto| { &mut m.roles },
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserBadges>>(
                "badges",
                |m: &UserDto| { &m.badges },
                |m: &mut UserDto| { &mut m.badges },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "nickname",
                UserDto::has_nickname,
                UserDto::get_nickname,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "color",
                |m: &UserDto| { &m.color },
                |m: &mut UserDto| { &mut m.color },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserMetaDto>>(
                "meta",
                |m: &UserDto| { &m.meta },
                |m: &mut UserDto| { &mut m.meta },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "selfMuted",
                |m: &UserDto| { &m.selfMuted },
                |m: &mut UserDto| { &mut m.selfMuted },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "serverNotesMuted",
                |m: &UserDto| { &m.serverNotesMuted },
                |m: &mut UserDto| { &mut m.serverNotesMuted },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "serverChatMuted",
                |m: &UserDto| { &m.serverChatMuted },
                |m: &mut UserDto| { &mut m.serverChatMuted },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeEnum<UserStatus>>(
                "status",
                |m: &UserDto| { &m.status },
                |m: &mut UserDto| { &mut m.status },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "ProfileDescription",
                UserDto::has_ProfileDescription,
                UserDto::get_ProfileDescription,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileImageLastModified",
                UserDto::has_profileImageLastModified,
                UserDto::get_profileImageLastModified,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileBackgroundImageLastModified",
                UserDto::has_profileBackgroundImageLastModified,
                UserDto::get_profileBackgroundImageLastModified,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<WorldData>>(
                "worldData",
                |m: &UserDto| { &m.worldData },
                |m: &mut UserDto| { &mut m.worldData },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isProMember",
                |m: &UserDto| { &m.isProMember },
                |m: &mut UserDto| { &mut m.isProMember },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "statusText",
                UserDto::has_statusText,
                UserDto::get_statusText,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "uuid",
                |m: &UserDto| { &m.uuid },
                |m: &mut UserDto| { &mut m.uuid },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserDto>(
                "UserDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserDto {
        static instance: ::protobuf::rt::LazyV2<UserDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserDto::new)
    }
}

impl ::protobuf::Clear for UserDto {
    fn clear(&mut self) {
        self.username.clear();
        self.usertag.clear();
        self.socketID.clear();
        self.roles.clear();
        self.badges.clear();
        self._nickname = ::std::option::Option::None;
        self.color.clear();
        self.meta.clear();
        self.selfMuted = false;
        self.serverNotesMuted = false;
        self.serverChatMuted = false;
        self.status = UserStatus::None;
        self._ProfileDescription = ::std::option::Option::None;
        self._profileImageLastModified = ::std::option::Option::None;
        self._profileBackgroundImageLastModified = ::std::option::Option::None;
        self.worldData.clear();
        self.isProMember = false;
        self._statusText = ::std::option::Option::None;
        self.uuid.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserBillingSettings {
    // message fields
    pub currentPlan: ::std::string::String,
    pub cancelationInProcess: bool,
    pub meta: ::protobuf::SingularPtrField<UserBillingSettings_BillingMeta>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserBillingSettings {
    fn default() -> &'a UserBillingSettings {
        <UserBillingSettings as ::protobuf::Message>::default_instance()
    }
}

impl UserBillingSettings {
    pub fn new() -> UserBillingSettings {
        ::std::default::Default::default()
    }

    // string currentPlan = 1;


    pub fn get_currentPlan(&self) -> &str {
        &self.currentPlan
    }
    pub fn clear_currentPlan(&mut self) {
        self.currentPlan.clear();
    }

    // Param is passed by value, moved
    pub fn set_currentPlan(&mut self, v: ::std::string::String) {
        self.currentPlan = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_currentPlan(&mut self) -> &mut ::std::string::String {
        &mut self.currentPlan
    }

    // Take field
    pub fn take_currentPlan(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.currentPlan, ::std::string::String::new())
    }

    // bool cancelationInProcess = 2;


    pub fn get_cancelationInProcess(&self) -> bool {
        self.cancelationInProcess
    }
    pub fn clear_cancelationInProcess(&mut self) {
        self.cancelationInProcess = false;
    }

    // Param is passed by value, moved
    pub fn set_cancelationInProcess(&mut self, v: bool) {
        self.cancelationInProcess = v;
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBillingSettings.BillingMeta meta = 3;


    pub fn get_meta(&self) -> &UserBillingSettings_BillingMeta {
        self.meta.as_ref().unwrap_or_else(|| <UserBillingSettings_BillingMeta as ::protobuf::Message>::default_instance())
    }
    pub fn clear_meta(&mut self) {
        self.meta.clear();
    }

    pub fn has_meta(&self) -> bool {
        self.meta.is_some()
    }

    // Param is passed by value, moved
    pub fn set_meta(&mut self, v: UserBillingSettings_BillingMeta) {
        self.meta = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_meta(&mut self) -> &mut UserBillingSettings_BillingMeta {
        if self.meta.is_none() {
            self.meta.set_default();
        }
        self.meta.as_mut().unwrap()
    }

    // Take field
    pub fn take_meta(&mut self) -> UserBillingSettings_BillingMeta {
        self.meta.take().unwrap_or_else(|| UserBillingSettings_BillingMeta::new())
    }
}

impl ::protobuf::Message for UserBillingSettings {
    fn is_initialized(&self) -> bool {
        for v in &self.meta {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.currentPlan)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.cancelationInProcess = tmp;
                },
                3 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.meta)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.currentPlan.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.currentPlan);
        }
        if self.cancelationInProcess != false {
            my_size += 2;
        }
        if let Some(ref v) = self.meta.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.currentPlan.is_empty() {
            os.write_string(1, &self.currentPlan)?;
        }
        if self.cancelationInProcess != false {
            os.write_bool(2, self.cancelationInProcess)?;
        }
        if let Some(ref v) = self.meta.as_ref() {
            os.write_tag(3, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserBillingSettings {
        UserBillingSettings::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "currentPlan",
                |m: &UserBillingSettings| { &m.currentPlan },
                |m: &mut UserBillingSettings| { &mut m.currentPlan },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "cancelationInProcess",
                |m: &UserBillingSettings| { &m.cancelationInProcess },
                |m: &mut UserBillingSettings| { &mut m.cancelationInProcess },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserBillingSettings_BillingMeta>>(
                "meta",
                |m: &UserBillingSettings| { &m.meta },
                |m: &mut UserBillingSettings| { &mut m.meta },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserBillingSettings>(
                "UserBillingSettings",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserBillingSettings {
        static instance: ::protobuf::rt::LazyV2<UserBillingSettings> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserBillingSettings::new)
    }
}

impl ::protobuf::Clear for UserBillingSettings {
    fn clear(&mut self) {
        self.currentPlan.clear();
        self.cancelationInProcess = false;
        self.meta.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserBillingSettings {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserBillingSettings {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserBillingSettings_BillingMeta {
    // message fields
    pub nextBillingDate: ::std::string::String,
    pub currencyCode: ::std::string::String,
    pub pricePerUnit: ::std::string::String,
    pub priceID: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserBillingSettings_BillingMeta {
    fn default() -> &'a UserBillingSettings_BillingMeta {
        <UserBillingSettings_BillingMeta as ::protobuf::Message>::default_instance()
    }
}

impl UserBillingSettings_BillingMeta {
    pub fn new() -> UserBillingSettings_BillingMeta {
        ::std::default::Default::default()
    }

    // string nextBillingDate = 1;


    pub fn get_nextBillingDate(&self) -> &str {
        &self.nextBillingDate
    }
    pub fn clear_nextBillingDate(&mut self) {
        self.nextBillingDate.clear();
    }

    // Param is passed by value, moved
    pub fn set_nextBillingDate(&mut self, v: ::std::string::String) {
        self.nextBillingDate = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_nextBillingDate(&mut self) -> &mut ::std::string::String {
        &mut self.nextBillingDate
    }

    // Take field
    pub fn take_nextBillingDate(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.nextBillingDate, ::std::string::String::new())
    }

    // string currencyCode = 2;


    pub fn get_currencyCode(&self) -> &str {
        &self.currencyCode
    }
    pub fn clear_currencyCode(&mut self) {
        self.currencyCode.clear();
    }

    // Param is passed by value, moved
    pub fn set_currencyCode(&mut self, v: ::std::string::String) {
        self.currencyCode = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_currencyCode(&mut self) -> &mut ::std::string::String {
        &mut self.currencyCode
    }

    // Take field
    pub fn take_currencyCode(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.currencyCode, ::std::string::String::new())
    }

    // string pricePerUnit = 3;


    pub fn get_pricePerUnit(&self) -> &str {
        &self.pricePerUnit
    }
    pub fn clear_pricePerUnit(&mut self) {
        self.pricePerUnit.clear();
    }

    // Param is passed by value, moved
    pub fn set_pricePerUnit(&mut self, v: ::std::string::String) {
        self.pricePerUnit = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_pricePerUnit(&mut self) -> &mut ::std::string::String {
        &mut self.pricePerUnit
    }

    // Take field
    pub fn take_pricePerUnit(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.pricePerUnit, ::std::string::String::new())
    }

    // string priceID = 4;


    pub fn get_priceID(&self) -> &str {
        &self.priceID
    }
    pub fn clear_priceID(&mut self) {
        self.priceID.clear();
    }

    // Param is passed by value, moved
    pub fn set_priceID(&mut self, v: ::std::string::String) {
        self.priceID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_priceID(&mut self) -> &mut ::std::string::String {
        &mut self.priceID
    }

    // Take field
    pub fn take_priceID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.priceID, ::std::string::String::new())
    }
}

impl ::protobuf::Message for UserBillingSettings_BillingMeta {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.nextBillingDate)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.currencyCode)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.pricePerUnit)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.priceID)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.nextBillingDate.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.nextBillingDate);
        }
        if !self.currencyCode.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.currencyCode);
        }
        if !self.pricePerUnit.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.pricePerUnit);
        }
        if !self.priceID.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.priceID);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.nextBillingDate.is_empty() {
            os.write_string(1, &self.nextBillingDate)?;
        }
        if !self.currencyCode.is_empty() {
            os.write_string(2, &self.currencyCode)?;
        }
        if !self.pricePerUnit.is_empty() {
            os.write_string(3, &self.pricePerUnit)?;
        }
        if !self.priceID.is_empty() {
            os.write_string(4, &self.priceID)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserBillingSettings_BillingMeta {
        UserBillingSettings_BillingMeta::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "nextBillingDate",
                |m: &UserBillingSettings_BillingMeta| { &m.nextBillingDate },
                |m: &mut UserBillingSettings_BillingMeta| { &mut m.nextBillingDate },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "currencyCode",
                |m: &UserBillingSettings_BillingMeta| { &m.currencyCode },
                |m: &mut UserBillingSettings_BillingMeta| { &mut m.currencyCode },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "pricePerUnit",
                |m: &UserBillingSettings_BillingMeta| { &m.pricePerUnit },
                |m: &mut UserBillingSettings_BillingMeta| { &mut m.pricePerUnit },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "priceID",
                |m: &UserBillingSettings_BillingMeta| { &m.priceID },
                |m: &mut UserBillingSettings_BillingMeta| { &mut m.priceID },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserBillingSettings_BillingMeta>(
                "UserBillingSettings.BillingMeta",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserBillingSettings_BillingMeta {
        static instance: ::protobuf::rt::LazyV2<UserBillingSettings_BillingMeta> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserBillingSettings_BillingMeta::new)
    }
}

impl ::protobuf::Clear for UserBillingSettings_BillingMeta {
    fn clear(&mut self) {
        self.nextBillingDate.clear();
        self.currencyCode.clear();
        self.pricePerUnit.clear();
        self.priceID.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserBillingSettings_BillingMeta {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserBillingSettings_BillingMeta {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum UserBillingSettings_SubscriptionPlan {
    Free = 0,
    Level1 = 1,
}

impl ::protobuf::ProtobufEnum for UserBillingSettings_SubscriptionPlan {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<UserBillingSettings_SubscriptionPlan> {
        match value {
            0 => ::std::option::Option::Some(UserBillingSettings_SubscriptionPlan::Free),
            1 => ::std::option::Option::Some(UserBillingSettings_SubscriptionPlan::Level1),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [UserBillingSettings_SubscriptionPlan] = &[
            UserBillingSettings_SubscriptionPlan::Free,
            UserBillingSettings_SubscriptionPlan::Level1,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<UserBillingSettings_SubscriptionPlan>("UserBillingSettings.SubscriptionPlan", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for UserBillingSettings_SubscriptionPlan {
}

impl ::std::default::Default for UserBillingSettings_SubscriptionPlan {
    fn default() -> Self {
        UserBillingSettings_SubscriptionPlan::Free
    }
}

impl ::protobuf::reflect::ProtobufValue for UserBillingSettings_SubscriptionPlan {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ClientMetaDto {
    // message fields
    pub billingSettings: ::protobuf::SingularPtrField<UserBillingSettings>,
    // message oneof groups
    pub _email: ::std::option::Option<ClientMetaDto_oneof__email>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClientMetaDto {
    fn default() -> &'a ClientMetaDto {
        <ClientMetaDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ClientMetaDto_oneof__email {
    email(::std::string::String),
}

impl ClientMetaDto {
    pub fn new() -> ClientMetaDto {
        ::std::default::Default::default()
    }

    // string email = 1;


    pub fn get_email(&self) -> &str {
        match self._email {
            ::std::option::Option::Some(ClientMetaDto_oneof__email::email(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_email(&mut self) {
        self._email = ::std::option::Option::None;
    }

    pub fn has_email(&self) -> bool {
        match self._email {
            ::std::option::Option::Some(ClientMetaDto_oneof__email::email(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_email(&mut self, v: ::std::string::String) {
        self._email = ::std::option::Option::Some(ClientMetaDto_oneof__email::email(v))
    }

    // Mutable pointer to the field.
    pub fn mut_email(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientMetaDto_oneof__email::email(_)) = self._email {
        } else {
            self._email = ::std::option::Option::Some(ClientMetaDto_oneof__email::email(::std::string::String::new()));
        }
        match self._email {
            ::std::option::Option::Some(ClientMetaDto_oneof__email::email(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_email(&mut self) -> ::std::string::String {
        if self.has_email() {
            match self._email.take() {
                ::std::option::Option::Some(ClientMetaDto_oneof__email::email(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBillingSettings billingSettings = 2;


    pub fn get_billingSettings(&self) -> &UserBillingSettings {
        self.billingSettings.as_ref().unwrap_or_else(|| <UserBillingSettings as ::protobuf::Message>::default_instance())
    }
    pub fn clear_billingSettings(&mut self) {
        self.billingSettings.clear();
    }

    pub fn has_billingSettings(&self) -> bool {
        self.billingSettings.is_some()
    }

    // Param is passed by value, moved
    pub fn set_billingSettings(&mut self, v: UserBillingSettings) {
        self.billingSettings = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_billingSettings(&mut self) -> &mut UserBillingSettings {
        if self.billingSettings.is_none() {
            self.billingSettings.set_default();
        }
        self.billingSettings.as_mut().unwrap()
    }

    // Take field
    pub fn take_billingSettings(&mut self) -> UserBillingSettings {
        self.billingSettings.take().unwrap_or_else(|| UserBillingSettings::new())
    }
}

impl ::protobuf::Message for ClientMetaDto {
    fn is_initialized(&self) -> bool {
        for v in &self.billingSettings {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._email = ::std::option::Option::Some(ClientMetaDto_oneof__email::email(is.read_string()?));
                },
                2 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.billingSettings)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.billingSettings.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if let ::std::option::Option::Some(ref v) = self._email {
            match v {
                &ClientMetaDto_oneof__email::email(ref v) => {
                    my_size += ::protobuf::rt::string_size(1, &v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.billingSettings.as_ref() {
            os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if let ::std::option::Option::Some(ref v) = self._email {
            match v {
                &ClientMetaDto_oneof__email::email(ref v) => {
                    os.write_string(1, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClientMetaDto {
        ClientMetaDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "email",
                ClientMetaDto::has_email,
                ClientMetaDto::get_email,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserBillingSettings>>(
                "billingSettings",
                |m: &ClientMetaDto| { &m.billingSettings },
                |m: &mut ClientMetaDto| { &mut m.billingSettings },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ClientMetaDto>(
                "ClientMetaDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ClientMetaDto {
        static instance: ::protobuf::rt::LazyV2<ClientMetaDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ClientMetaDto::new)
    }
}

impl ::protobuf::Clear for ClientMetaDto {
    fn clear(&mut self) {
        self._email = ::std::option::Option::None;
        self.billingSettings.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ClientMetaDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientMetaDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct KickedUserData {
    // message fields
    pub socketID: ::std::string::String,
    pub usertag: ::std::string::String,
    pub time: ::std::string::String,
    pub created_date: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a KickedUserData {
    fn default() -> &'a KickedUserData {
        <KickedUserData as ::protobuf::Message>::default_instance()
    }
}

impl KickedUserData {
    pub fn new() -> KickedUserData {
        ::std::default::Default::default()
    }

    // string socketID = 1;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // string usertag = 2;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string time = 3;


    pub fn get_time(&self) -> &str {
        &self.time
    }
    pub fn clear_time(&mut self) {
        self.time.clear();
    }

    // Param is passed by value, moved
    pub fn set_time(&mut self, v: ::std::string::String) {
        self.time = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_time(&mut self) -> &mut ::std::string::String {
        &mut self.time
    }

    // Take field
    pub fn take_time(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.time, ::std::string::String::new())
    }

    // string created_date = 4;


    pub fn get_created_date(&self) -> &str {
        &self.created_date
    }
    pub fn clear_created_date(&mut self) {
        self.created_date.clear();
    }

    // Param is passed by value, moved
    pub fn set_created_date(&mut self, v: ::std::string::String) {
        self.created_date = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_created_date(&mut self) -> &mut ::std::string::String {
        &mut self.created_date
    }

    // Take field
    pub fn take_created_date(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.created_date, ::std::string::String::new())
    }
}

impl ::protobuf::Message for KickedUserData {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.time)?;
                },
                4 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.created_date)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.socketID);
        }
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.usertag);
        }
        if !self.time.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.time);
        }
        if !self.created_date.is_empty() {
            my_size += ::protobuf::rt::string_size(4, &self.created_date);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.socketID.is_empty() {
            os.write_string(1, &self.socketID)?;
        }
        if !self.usertag.is_empty() {
            os.write_string(2, &self.usertag)?;
        }
        if !self.time.is_empty() {
            os.write_string(3, &self.time)?;
        }
        if !self.created_date.is_empty() {
            os.write_string(4, &self.created_date)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> KickedUserData {
        KickedUserData::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &KickedUserData| { &m.socketID },
                |m: &mut KickedUserData| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &KickedUserData| { &m.usertag },
                |m: &mut KickedUserData| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "time",
                |m: &KickedUserData| { &m.time },
                |m: &mut KickedUserData| { &mut m.time },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "created_date",
                |m: &KickedUserData| { &m.created_date },
                |m: &mut KickedUserData| { &mut m.created_date },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<KickedUserData>(
                "KickedUserData",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static KickedUserData {
        static instance: ::protobuf::rt::LazyV2<KickedUserData> = ::protobuf::rt::LazyV2::INIT;
        instance.get(KickedUserData::new)
    }
}

impl ::protobuf::Clear for KickedUserData {
    fn clear(&mut self) {
        self.socketID.clear();
        self.usertag.clear();
        self.time.clear();
        self.created_date.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for KickedUserData {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for KickedUserData {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserClientDto {
    // message fields
    pub userDto: ::protobuf::SingularPtrField<UserDto>,
    pub canApproveSheetMusic: bool,
    pub has2faEnabled: bool,
    pub isOAuthAccount: bool,
    // message oneof groups
    pub _meta: ::std::option::Option<UserClientDto_oneof__meta>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserClientDto {
    fn default() -> &'a UserClientDto {
        <UserClientDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserClientDto_oneof__meta {
    meta(ClientMetaDto),
}

impl UserClientDto {
    pub fn new() -> UserClientDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 1;


    pub fn get_userDto(&self) -> &UserDto {
        self.userDto.as_ref().unwrap_or_else(|| <UserDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_userDto(&mut self) {
        self.userDto.clear();
    }

    pub fn has_userDto(&self) -> bool {
        self.userDto.is_some()
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: UserDto) {
        self.userDto = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_userDto(&mut self) -> &mut UserDto {
        if self.userDto.is_none() {
            self.userDto.set_default();
        }
        self.userDto.as_mut().unwrap()
    }

    // Take field
    pub fn take_userDto(&mut self) -> UserDto {
        self.userDto.take().unwrap_or_else(|| UserDto::new())
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.ClientMetaDto meta = 2;


    pub fn get_meta(&self) -> &ClientMetaDto {
        match self._meta {
            ::std::option::Option::Some(UserClientDto_oneof__meta::meta(ref v)) => v,
            _ => <ClientMetaDto as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_meta(&mut self) {
        self._meta = ::std::option::Option::None;
    }

    pub fn has_meta(&self) -> bool {
        match self._meta {
            ::std::option::Option::Some(UserClientDto_oneof__meta::meta(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_meta(&mut self, v: ClientMetaDto) {
        self._meta = ::std::option::Option::Some(UserClientDto_oneof__meta::meta(v))
    }

    // Mutable pointer to the field.
    pub fn mut_meta(&mut self) -> &mut ClientMetaDto {
        if let ::std::option::Option::Some(UserClientDto_oneof__meta::meta(_)) = self._meta {
        } else {
            self._meta = ::std::option::Option::Some(UserClientDto_oneof__meta::meta(ClientMetaDto::new()));
        }
        match self._meta {
            ::std::option::Option::Some(UserClientDto_oneof__meta::meta(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_meta(&mut self) -> ClientMetaDto {
        if self.has_meta() {
            match self._meta.take() {
                ::std::option::Option::Some(UserClientDto_oneof__meta::meta(v)) => v,
                _ => panic!(),
            }
        } else {
            ClientMetaDto::new()
        }
    }

    // bool canApproveSheetMusic = 3;


    pub fn get_canApproveSheetMusic(&self) -> bool {
        self.canApproveSheetMusic
    }
    pub fn clear_canApproveSheetMusic(&mut self) {
        self.canApproveSheetMusic = false;
    }

    // Param is passed by value, moved
    pub fn set_canApproveSheetMusic(&mut self, v: bool) {
        self.canApproveSheetMusic = v;
    }

    // bool has2faEnabled = 4;


    pub fn get_has2faEnabled(&self) -> bool {
        self.has2faEnabled
    }
    pub fn clear_has2faEnabled(&mut self) {
        self.has2faEnabled = false;
    }

    // Param is passed by value, moved
    pub fn set_has2faEnabled(&mut self, v: bool) {
        self.has2faEnabled = v;
    }

    // bool isOAuthAccount = 5;


    pub fn get_isOAuthAccount(&self) -> bool {
        self.isOAuthAccount
    }
    pub fn clear_isOAuthAccount(&mut self) {
        self.isOAuthAccount = false;
    }

    // Param is passed by value, moved
    pub fn set_isOAuthAccount(&mut self, v: bool) {
        self.isOAuthAccount = v;
    }
}

impl ::protobuf::Message for UserClientDto {
    fn is_initialized(&self) -> bool {
        for v in &self.userDto {
            if !v.is_initialized() {
                return false;
            }
        };
        if let Some(UserClientDto_oneof__meta::meta(ref v)) = self._meta {
            if !v.is_initialized() {
                return false;
            }
        }
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.userDto)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._meta = ::std::option::Option::Some(UserClientDto_oneof__meta::meta(is.read_message()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.canApproveSheetMusic = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.has2faEnabled = tmp;
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.isOAuthAccount = tmp;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.userDto.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.canApproveSheetMusic != false {
            my_size += 2;
        }
        if self.has2faEnabled != false {
            my_size += 2;
        }
        if self.isOAuthAccount != false {
            my_size += 2;
        }
        if let ::std::option::Option::Some(ref v) = self._meta {
            match v {
                &UserClientDto_oneof__meta::meta(ref v) => {
                    let len = v.compute_size();
                    my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.userDto.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.canApproveSheetMusic != false {
            os.write_bool(3, self.canApproveSheetMusic)?;
        }
        if self.has2faEnabled != false {
            os.write_bool(4, self.has2faEnabled)?;
        }
        if self.isOAuthAccount != false {
            os.write_bool(5, self.isOAuthAccount)?;
        }
        if let ::std::option::Option::Some(ref v) = self._meta {
            match v {
                &UserClientDto_oneof__meta::meta(ref v) => {
                    os.write_tag(2, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserClientDto {
        UserClientDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserDto>>(
                "userDto",
                |m: &UserClientDto| { &m.userDto },
                |m: &mut UserClientDto| { &mut m.userDto },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, ClientMetaDto>(
                "meta",
                UserClientDto::has_meta,
                UserClientDto::get_meta,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "canApproveSheetMusic",
                |m: &UserClientDto| { &m.canApproveSheetMusic },
                |m: &mut UserClientDto| { &mut m.canApproveSheetMusic },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "has2faEnabled",
                |m: &UserClientDto| { &m.has2faEnabled },
                |m: &mut UserClientDto| { &mut m.has2faEnabled },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "isOAuthAccount",
                |m: &UserClientDto| { &m.isOAuthAccount },
                |m: &mut UserClientDto| { &mut m.isOAuthAccount },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserClientDto>(
                "UserClientDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserClientDto {
        static instance: ::protobuf::rt::LazyV2<UserClientDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserClientDto::new)
    }
}

impl ::protobuf::Clear for UserClientDto {
    fn clear(&mut self) {
        self.userDto.clear();
        self._meta = ::std::option::Option::None;
        self.canApproveSheetMusic = false;
        self.has2faEnabled = false;
        self.isOAuthAccount = false;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserClientDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserClientDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct ClientSideUserDto {
    // message fields
    pub userDto: ::protobuf::SingularPtrField<UserDto>,
    pub localNotesMuted: bool,
    pub localChatMuted: bool,
    pub socketID: ::std::string::String,
    // message oneof groups
    pub _clientMetaDetailsParsed: ::std::option::Option<ClientSideUserDto_oneof__clientMetaDetailsParsed>,
    pub _socketIDHashed: ::std::option::Option<ClientSideUserDto_oneof__socketIDHashed>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a ClientSideUserDto {
    fn default() -> &'a ClientSideUserDto {
        <ClientSideUserDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum ClientSideUserDto_oneof__clientMetaDetailsParsed {
    clientMetaDetailsParsed(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum ClientSideUserDto_oneof__socketIDHashed {
    socketIDHashed(u32),
}

impl ClientSideUserDto {
    pub fn new() -> ClientSideUserDto {
        ::std::default::Default::default()
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserDto userDto = 1;


    pub fn get_userDto(&self) -> &UserDto {
        self.userDto.as_ref().unwrap_or_else(|| <UserDto as ::protobuf::Message>::default_instance())
    }
    pub fn clear_userDto(&mut self) {
        self.userDto.clear();
    }

    pub fn has_userDto(&self) -> bool {
        self.userDto.is_some()
    }

    // Param is passed by value, moved
    pub fn set_userDto(&mut self, v: UserDto) {
        self.userDto = ::protobuf::SingularPtrField::some(v);
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_userDto(&mut self) -> &mut UserDto {
        if self.userDto.is_none() {
            self.userDto.set_default();
        }
        self.userDto.as_mut().unwrap()
    }

    // Take field
    pub fn take_userDto(&mut self) -> UserDto {
        self.userDto.take().unwrap_or_else(|| UserDto::new())
    }

    // bool localNotesMuted = 2;


    pub fn get_localNotesMuted(&self) -> bool {
        self.localNotesMuted
    }
    pub fn clear_localNotesMuted(&mut self) {
        self.localNotesMuted = false;
    }

    // Param is passed by value, moved
    pub fn set_localNotesMuted(&mut self, v: bool) {
        self.localNotesMuted = v;
    }

    // bool localChatMuted = 3;


    pub fn get_localChatMuted(&self) -> bool {
        self.localChatMuted
    }
    pub fn clear_localChatMuted(&mut self) {
        self.localChatMuted = false;
    }

    // Param is passed by value, moved
    pub fn set_localChatMuted(&mut self, v: bool) {
        self.localChatMuted = v;
    }

    // string clientMetaDetailsParsed = 4;


    pub fn get_clientMetaDetailsParsed(&self) -> &str {
        match self._clientMetaDetailsParsed {
            ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_clientMetaDetailsParsed(&mut self) {
        self._clientMetaDetailsParsed = ::std::option::Option::None;
    }

    pub fn has_clientMetaDetailsParsed(&self) -> bool {
        match self._clientMetaDetailsParsed {
            ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientMetaDetailsParsed(&mut self, v: ::std::string::String) {
        self._clientMetaDetailsParsed = ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientMetaDetailsParsed(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(_)) = self._clientMetaDetailsParsed {
        } else {
            self._clientMetaDetailsParsed = ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(::std::string::String::new()));
        }
        match self._clientMetaDetailsParsed {
            ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientMetaDetailsParsed(&mut self) -> ::std::string::String {
        if self.has_clientMetaDetailsParsed() {
            match self._clientMetaDetailsParsed.take() {
                ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string socketID = 5;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // uint32 socketIDHashed = 6;


    pub fn get_socketIDHashed(&self) -> u32 {
        match self._socketIDHashed {
            ::std::option::Option::Some(ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_socketIDHashed(&mut self) {
        self._socketIDHashed = ::std::option::Option::None;
    }

    pub fn has_socketIDHashed(&self) -> bool {
        match self._socketIDHashed {
            ::std::option::Option::Some(ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketIDHashed(&mut self, v: u32) {
        self._socketIDHashed = ::std::option::Option::Some(ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(v))
    }
}

impl ::protobuf::Message for ClientSideUserDto {
    fn is_initialized(&self) -> bool {
        for v in &self.userDto {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_message_into(wire_type, is, &mut self.userDto)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.localNotesMuted = tmp;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    let tmp = is.read_bool()?;
                    self.localChatMuted = tmp;
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._clientMetaDetailsParsed = ::std::option::Option::Some(ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(is.read_string()?));
                },
                5 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._socketIDHashed = ::std::option::Option::Some(ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(is.read_uint32()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if let Some(ref v) = self.userDto.as_ref() {
            let len = v.compute_size();
            my_size += 1 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        }
        if self.localNotesMuted != false {
            my_size += 2;
        }
        if self.localChatMuted != false {
            my_size += 2;
        }
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(5, &self.socketID);
        }
        if let ::std::option::Option::Some(ref v) = self._clientMetaDetailsParsed {
            match v {
                &ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._socketIDHashed {
            match v {
                &ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(v) => {
                    my_size += ::protobuf::rt::value_size(6, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if let Some(ref v) = self.userDto.as_ref() {
            os.write_tag(1, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        }
        if self.localNotesMuted != false {
            os.write_bool(2, self.localNotesMuted)?;
        }
        if self.localChatMuted != false {
            os.write_bool(3, self.localChatMuted)?;
        }
        if !self.socketID.is_empty() {
            os.write_string(5, &self.socketID)?;
        }
        if let ::std::option::Option::Some(ref v) = self._clientMetaDetailsParsed {
            match v {
                &ClientSideUserDto_oneof__clientMetaDetailsParsed::clientMetaDetailsParsed(ref v) => {
                    os.write_string(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._socketIDHashed {
            match v {
                &ClientSideUserDto_oneof__socketIDHashed::socketIDHashed(v) => {
                    os.write_uint32(6, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> ClientSideUserDto {
        ClientSideUserDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_singular_ptr_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserDto>>(
                "userDto",
                |m: &ClientSideUserDto| { &m.userDto },
                |m: &mut ClientSideUserDto| { &mut m.userDto },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "localNotesMuted",
                |m: &ClientSideUserDto| { &m.localNotesMuted },
                |m: &mut ClientSideUserDto| { &mut m.localNotesMuted },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeBool>(
                "localChatMuted",
                |m: &ClientSideUserDto| { &m.localChatMuted },
                |m: &mut ClientSideUserDto| { &mut m.localChatMuted },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "clientMetaDetailsParsed",
                ClientSideUserDto::has_clientMetaDetailsParsed,
                ClientSideUserDto::get_clientMetaDetailsParsed,
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &ClientSideUserDto| { &m.socketID },
                |m: &mut ClientSideUserDto| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_u32_accessor::<_>(
                "socketIDHashed",
                ClientSideUserDto::has_socketIDHashed,
                ClientSideUserDto::get_socketIDHashed,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<ClientSideUserDto>(
                "ClientSideUserDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static ClientSideUserDto {
        static instance: ::protobuf::rt::LazyV2<ClientSideUserDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(ClientSideUserDto::new)
    }
}

impl ::protobuf::Clear for ClientSideUserDto {
    fn clear(&mut self) {
        self.userDto.clear();
        self.localNotesMuted = false;
        self.localChatMuted = false;
        self._clientMetaDetailsParsed = ::std::option::Option::None;
        self.socketID.clear();
        self._socketIDHashed = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for ClientSideUserDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for ClientSideUserDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct UserUpdateCommand {
    // message fields
    pub socketID: ::std::string::String,
    pub badges: ::protobuf::RepeatedField<UserBadges>,
    // message oneof groups
    pub data: ::std::option::Option<UserUpdateCommand_oneof_data>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a UserUpdateCommand {
    fn default() -> &'a UserUpdateCommand {
        <UserUpdateCommand as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum UserUpdateCommand_oneof_data {
    userColor(::std::string::String),
    userStatus(UserStatus),
    clientMetaDetails(::std::string::String),
    profileDescription(::std::string::String),
    selfMuted(bool),
    statusText(::std::string::String),
    nickname(::std::string::String),
    profileImageUpdated(::std::string::String),
    profileBackgroundImageUpdated(::std::string::String),
    profileImageCleared(bool),
    profileBackgroundImageCleared(bool),
    orchestraModel(::std::string::String),
    characterModel(::std::string::String),
    serverNotesMuted(bool),
    serverChatMuted(bool),
    avatarWorldPosition(AvatarWorldDataDto_AvatarMessageWorldPosition),
    avatarPianoBenchSeat(i32),
}

impl UserUpdateCommand {
    pub fn new() -> UserUpdateCommand {
        ::std::default::Default::default()
    }

    // string socketID = 1;


    pub fn get_socketID(&self) -> &str {
        &self.socketID
    }
    pub fn clear_socketID(&mut self) {
        self.socketID.clear();
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self.socketID = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        &mut self.socketID
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.socketID, ::std::string::String::new())
    }

    // string userColor = 2;


    pub fn get_userColor(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_userColor(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userColor(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userColor(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(v))
    }

    // Mutable pointer to the field.
    pub fn mut_userColor(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_userColor(&mut self) -> ::std::string::String {
        if self.has_userColor() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserStatus userStatus = 3;


    pub fn get_userStatus(&self) -> UserStatus {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::userStatus(v)) => v,
            _ => UserStatus::None,
        }
    }
    pub fn clear_userStatus(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_userStatus(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::userStatus(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_userStatus(&mut self, v: UserStatus) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::userStatus(v))
    }

    // string clientMetaDetails = 4;


    pub fn get_clientMetaDetails(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_clientMetaDetails(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_clientMetaDetails(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_clientMetaDetails(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(v))
    }

    // Mutable pointer to the field.
    pub fn mut_clientMetaDetails(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_clientMetaDetails(&mut self) -> ::std::string::String {
        if self.has_clientMetaDetails() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileDescription = 5;


    pub fn get_profileDescription(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileDescription(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_profileDescription(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileDescription(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileDescription(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileDescription(&mut self) -> ::std::string::String {
        if self.has_profileDescription() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool selfMuted = 6;


    pub fn get_selfMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::selfMuted(v)) => v,
            _ => false,
        }
    }
    pub fn clear_selfMuted(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_selfMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::selfMuted(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_selfMuted(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::selfMuted(v))
    }

    // string statusText = 7;


    pub fn get_statusText(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_statusText(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_statusText(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_statusText(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(v))
    }

    // Mutable pointer to the field.
    pub fn mut_statusText(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_statusText(&mut self) -> ::std::string::String {
        if self.has_statusText() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string nickname = 8;


    pub fn get_nickname(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_nickname(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_nickname(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_nickname(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(v))
    }

    // Mutable pointer to the field.
    pub fn mut_nickname(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_nickname(&mut self) -> ::std::string::String {
        if self.has_nickname() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileImageUpdated = 9;


    pub fn get_profileImageUpdated(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileImageUpdated(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_profileImageUpdated(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileImageUpdated(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileImageUpdated(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileImageUpdated(&mut self) -> ::std::string::String {
        if self.has_profileImageUpdated() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileBackgroundImageUpdated = 10;


    pub fn get_profileBackgroundImageUpdated(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileBackgroundImageUpdated(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_profileBackgroundImageUpdated(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileBackgroundImageUpdated(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileBackgroundImageUpdated(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileBackgroundImageUpdated(&mut self) -> ::std::string::String {
        if self.has_profileBackgroundImageUpdated() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool profileImageCleared = 11;


    pub fn get_profileImageCleared(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageCleared(v)) => v,
            _ => false,
        }
    }
    pub fn clear_profileImageCleared(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_profileImageCleared(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageCleared(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileImageCleared(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageCleared(v))
    }

    // bool profileBackgroundImageCleared = 12;


    pub fn get_profileBackgroundImageCleared(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageCleared(v)) => v,
            _ => false,
        }
    }
    pub fn clear_profileBackgroundImageCleared(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_profileBackgroundImageCleared(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageCleared(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileBackgroundImageCleared(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageCleared(v))
    }

    // string orchestraModel = 13;


    pub fn get_orchestraModel(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_orchestraModel(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_orchestraModel(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_orchestraModel(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(v))
    }

    // Mutable pointer to the field.
    pub fn mut_orchestraModel(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_orchestraModel(&mut self) -> ::std::string::String {
        if self.has_orchestraModel() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string characterModel = 14;


    pub fn get_characterModel(&self) -> &str {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_characterModel(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_characterModel(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_characterModel(&mut self, v: ::std::string::String) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(v))
    }

    // Mutable pointer to the field.
    pub fn mut_characterModel(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(::std::string::String::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_characterModel(&mut self) -> ::std::string::String {
        if self.has_characterModel() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // bool serverNotesMuted = 15;


    pub fn get_serverNotesMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverNotesMuted(v)) => v,
            _ => false,
        }
    }
    pub fn clear_serverNotesMuted(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_serverNotesMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverNotesMuted(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_serverNotesMuted(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverNotesMuted(v))
    }

    // bool serverChatMuted = 16;


    pub fn get_serverChatMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverChatMuted(v)) => v,
            _ => false,
        }
    }
    pub fn clear_serverChatMuted(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_serverChatMuted(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverChatMuted(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_serverChatMuted(&mut self, v: bool) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverChatMuted(v))
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessageWorldPosition avatarWorldPosition = 17;


    pub fn get_avatarWorldPosition(&self) -> &AvatarWorldDataDto_AvatarMessageWorldPosition {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(ref v)) => v,
            _ => <AvatarWorldDataDto_AvatarMessageWorldPosition as ::protobuf::Message>::default_instance(),
        }
    }
    pub fn clear_avatarWorldPosition(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_avatarWorldPosition(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarWorldPosition(&mut self, v: AvatarWorldDataDto_AvatarMessageWorldPosition) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(v))
    }

    // Mutable pointer to the field.
    pub fn mut_avatarWorldPosition(&mut self) -> &mut AvatarWorldDataDto_AvatarMessageWorldPosition {
        if let ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(_)) = self.data {
        } else {
            self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(AvatarWorldDataDto_AvatarMessageWorldPosition::new()));
        }
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_avatarWorldPosition(&mut self) -> AvatarWorldDataDto_AvatarMessageWorldPosition {
        if self.has_avatarWorldPosition() {
            match self.data.take() {
                ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(v)) => v,
                _ => panic!(),
            }
        } else {
            AvatarWorldDataDto_AvatarMessageWorldPosition::new()
        }
    }

    // int32 avatarPianoBenchSeat = 18;


    pub fn get_avatarPianoBenchSeat(&self) -> i32 {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarPianoBenchSeat(v)) => v,
            _ => 0,
        }
    }
    pub fn clear_avatarPianoBenchSeat(&mut self) {
        self.data = ::std::option::Option::None;
    }

    pub fn has_avatarPianoBenchSeat(&self) -> bool {
        match self.data {
            ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarPianoBenchSeat(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_avatarPianoBenchSeat(&mut self, v: i32) {
        self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarPianoBenchSeat(v))
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.UserBadges badges = 50;


    pub fn get_badges(&self) -> &[UserBadges] {
        &self.badges
    }
    pub fn clear_badges(&mut self) {
        self.badges.clear();
    }

    // Param is passed by value, moved
    pub fn set_badges(&mut self, v: ::protobuf::RepeatedField<UserBadges>) {
        self.badges = v;
    }

    // Mutable pointer to the field.
    pub fn mut_badges(&mut self) -> &mut ::protobuf::RepeatedField<UserBadges> {
        &mut self.badges
    }

    // Take field
    pub fn take_badges(&mut self) -> ::protobuf::RepeatedField<UserBadges> {
        ::std::mem::replace(&mut self.badges, ::protobuf::RepeatedField::new())
    }
}

impl ::protobuf::Message for UserUpdateCommand {
    fn is_initialized(&self) -> bool {
        if let Some(UserUpdateCommand_oneof_data::avatarWorldPosition(ref v)) = self.data {
            if !v.is_initialized() {
                return false;
            }
        }
        for v in &self.badges {
            if !v.is_initialized() {
                return false;
            }
        };
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.socketID)?;
                },
                2 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::userColor(is.read_string()?));
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::userStatus(is.read_enum()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::clientMetaDetails(is.read_string()?));
                },
                5 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileDescription(is.read_string()?));
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::selfMuted(is.read_bool()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::statusText(is.read_string()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::nickname(is.read_string()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageUpdated(is.read_string()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(is.read_string()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileImageCleared(is.read_bool()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::profileBackgroundImageCleared(is.read_bool()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::orchestraModel(is.read_string()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::characterModel(is.read_string()?));
                },
                15 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverNotesMuted(is.read_bool()?));
                },
                16 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::serverChatMuted(is.read_bool()?));
                },
                17 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarWorldPosition(is.read_message()?));
                },
                18 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self.data = ::std::option::Option::Some(UserUpdateCommand_oneof_data::avatarPianoBenchSeat(is.read_int32()?));
                },
                50 => {
                    ::protobuf::rt::read_repeated_message_into(wire_type, is, &mut self.badges)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.socketID.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.socketID);
        }
        for value in &self.badges {
            let len = value.compute_size();
            my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
        };
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &UserUpdateCommand_oneof_data::userColor(ref v) => {
                    my_size += ::protobuf::rt::string_size(2, &v);
                },
                &UserUpdateCommand_oneof_data::userStatus(v) => {
                    my_size += ::protobuf::rt::enum_size(3, v);
                },
                &UserUpdateCommand_oneof_data::clientMetaDetails(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
                &UserUpdateCommand_oneof_data::profileDescription(ref v) => {
                    my_size += ::protobuf::rt::string_size(5, &v);
                },
                &UserUpdateCommand_oneof_data::selfMuted(v) => {
                    my_size += 2;
                },
                &UserUpdateCommand_oneof_data::statusText(ref v) => {
                    my_size += ::protobuf::rt::string_size(7, &v);
                },
                &UserUpdateCommand_oneof_data::nickname(ref v) => {
                    my_size += ::protobuf::rt::string_size(8, &v);
                },
                &UserUpdateCommand_oneof_data::profileImageUpdated(ref v) => {
                    my_size += ::protobuf::rt::string_size(9, &v);
                },
                &UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(ref v) => {
                    my_size += ::protobuf::rt::string_size(10, &v);
                },
                &UserUpdateCommand_oneof_data::profileImageCleared(v) => {
                    my_size += 2;
                },
                &UserUpdateCommand_oneof_data::profileBackgroundImageCleared(v) => {
                    my_size += 2;
                },
                &UserUpdateCommand_oneof_data::orchestraModel(ref v) => {
                    my_size += ::protobuf::rt::string_size(13, &v);
                },
                &UserUpdateCommand_oneof_data::characterModel(ref v) => {
                    my_size += ::protobuf::rt::string_size(14, &v);
                },
                &UserUpdateCommand_oneof_data::serverNotesMuted(v) => {
                    my_size += 2;
                },
                &UserUpdateCommand_oneof_data::serverChatMuted(v) => {
                    my_size += 3;
                },
                &UserUpdateCommand_oneof_data::avatarWorldPosition(ref v) => {
                    let len = v.compute_size();
                    my_size += 2 + ::protobuf::rt::compute_raw_varint32_size(len) + len;
                },
                &UserUpdateCommand_oneof_data::avatarPianoBenchSeat(v) => {
                    my_size += ::protobuf::rt::value_size(18, v, ::protobuf::wire_format::WireTypeVarint);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.socketID.is_empty() {
            os.write_string(1, &self.socketID)?;
        }
        for v in &self.badges {
            os.write_tag(50, ::protobuf::wire_format::WireTypeLengthDelimited)?;
            os.write_raw_varint32(v.get_cached_size())?;
            v.write_to_with_cached_sizes(os)?;
        };
        if let ::std::option::Option::Some(ref v) = self.data {
            match v {
                &UserUpdateCommand_oneof_data::userColor(ref v) => {
                    os.write_string(2, v)?;
                },
                &UserUpdateCommand_oneof_data::userStatus(v) => {
                    os.write_enum(3, ::protobuf::ProtobufEnum::value(&v))?;
                },
                &UserUpdateCommand_oneof_data::clientMetaDetails(ref v) => {
                    os.write_string(4, v)?;
                },
                &UserUpdateCommand_oneof_data::profileDescription(ref v) => {
                    os.write_string(5, v)?;
                },
                &UserUpdateCommand_oneof_data::selfMuted(v) => {
                    os.write_bool(6, v)?;
                },
                &UserUpdateCommand_oneof_data::statusText(ref v) => {
                    os.write_string(7, v)?;
                },
                &UserUpdateCommand_oneof_data::nickname(ref v) => {
                    os.write_string(8, v)?;
                },
                &UserUpdateCommand_oneof_data::profileImageUpdated(ref v) => {
                    os.write_string(9, v)?;
                },
                &UserUpdateCommand_oneof_data::profileBackgroundImageUpdated(ref v) => {
                    os.write_string(10, v)?;
                },
                &UserUpdateCommand_oneof_data::profileImageCleared(v) => {
                    os.write_bool(11, v)?;
                },
                &UserUpdateCommand_oneof_data::profileBackgroundImageCleared(v) => {
                    os.write_bool(12, v)?;
                },
                &UserUpdateCommand_oneof_data::orchestraModel(ref v) => {
                    os.write_string(13, v)?;
                },
                &UserUpdateCommand_oneof_data::characterModel(ref v) => {
                    os.write_string(14, v)?;
                },
                &UserUpdateCommand_oneof_data::serverNotesMuted(v) => {
                    os.write_bool(15, v)?;
                },
                &UserUpdateCommand_oneof_data::serverChatMuted(v) => {
                    os.write_bool(16, v)?;
                },
                &UserUpdateCommand_oneof_data::avatarWorldPosition(ref v) => {
                    os.write_tag(17, ::protobuf::wire_format::WireTypeLengthDelimited)?;
                    os.write_raw_varint32(v.get_cached_size())?;
                    v.write_to_with_cached_sizes(os)?;
                },
                &UserUpdateCommand_oneof_data::avatarPianoBenchSeat(v) => {
                    os.write_int32(18, v)?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> UserUpdateCommand {
        UserUpdateCommand::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "socketID",
                |m: &UserUpdateCommand| { &m.socketID },
                |m: &mut UserUpdateCommand| { &mut m.socketID },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "userColor",
                UserUpdateCommand::has_userColor,
                UserUpdateCommand::get_userColor,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, UserStatus>(
                "userStatus",
                UserUpdateCommand::has_userStatus,
                UserUpdateCommand::get_userStatus,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "clientMetaDetails",
                UserUpdateCommand::has_clientMetaDetails,
                UserUpdateCommand::get_clientMetaDetails,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileDescription",
                UserUpdateCommand::has_profileDescription,
                UserUpdateCommand::get_profileDescription,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "selfMuted",
                UserUpdateCommand::has_selfMuted,
                UserUpdateCommand::get_selfMuted,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "statusText",
                UserUpdateCommand::has_statusText,
                UserUpdateCommand::get_statusText,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "nickname",
                UserUpdateCommand::has_nickname,
                UserUpdateCommand::get_nickname,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileImageUpdated",
                UserUpdateCommand::has_profileImageUpdated,
                UserUpdateCommand::get_profileImageUpdated,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileBackgroundImageUpdated",
                UserUpdateCommand::has_profileBackgroundImageUpdated,
                UserUpdateCommand::get_profileBackgroundImageUpdated,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "profileImageCleared",
                UserUpdateCommand::has_profileImageCleared,
                UserUpdateCommand::get_profileImageCleared,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "profileBackgroundImageCleared",
                UserUpdateCommand::has_profileBackgroundImageCleared,
                UserUpdateCommand::get_profileBackgroundImageCleared,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "orchestraModel",
                UserUpdateCommand::has_orchestraModel,
                UserUpdateCommand::get_orchestraModel,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "characterModel",
                UserUpdateCommand::has_characterModel,
                UserUpdateCommand::get_characterModel,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "serverNotesMuted",
                UserUpdateCommand::has_serverNotesMuted,
                UserUpdateCommand::get_serverNotesMuted,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_bool_accessor::<_>(
                "serverChatMuted",
                UserUpdateCommand::has_serverChatMuted,
                UserUpdateCommand::get_serverChatMuted,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_message_accessor::<_, AvatarWorldDataDto_AvatarMessageWorldPosition>(
                "avatarWorldPosition",
                UserUpdateCommand::has_avatarWorldPosition,
                UserUpdateCommand::get_avatarWorldPosition,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_i32_accessor::<_>(
                "avatarPianoBenchSeat",
                UserUpdateCommand::has_avatarPianoBenchSeat,
                UserUpdateCommand::get_avatarPianoBenchSeat,
            ));
            fields.push(::protobuf::reflect::accessor::make_repeated_field_accessor::<_, ::protobuf::types::ProtobufTypeMessage<UserBadges>>(
                "badges",
                |m: &UserUpdateCommand| { &m.badges },
                |m: &mut UserUpdateCommand| { &mut m.badges },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<UserUpdateCommand>(
                "UserUpdateCommand",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static UserUpdateCommand {
        static instance: ::protobuf::rt::LazyV2<UserUpdateCommand> = ::protobuf::rt::LazyV2::INIT;
        instance.get(UserUpdateCommand::new)
    }
}

impl ::protobuf::Clear for UserUpdateCommand {
    fn clear(&mut self) {
        self.socketID.clear();
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.data = ::std::option::Option::None;
        self.badges.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for UserUpdateCommand {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for UserUpdateCommand {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct FriendDto {
    // message fields
    pub username: ::std::string::String,
    pub usertag: ::std::string::String,
    pub roles: ::std::vec::Vec<Roles>,
    // message oneof groups
    pub _socketID: ::std::option::Option<FriendDto_oneof__socketID>,
    pub _roomID: ::std::option::Option<FriendDto_oneof__roomID>,
    pub _nickname: ::std::option::Option<FriendDto_oneof__nickname>,
    pub _color: ::std::option::Option<FriendDto_oneof__color>,
    pub _statusText: ::std::option::Option<FriendDto_oneof__statusText>,
    pub _profileDescription: ::std::option::Option<FriendDto_oneof__profileDescription>,
    pub _becameFriendsDate: ::std::option::Option<FriendDto_oneof__becameFriendsDate>,
    pub _profileImageLastModified: ::std::option::Option<FriendDto_oneof__profileImageLastModified>,
    pub _profileBackgroundImageLastModified: ::std::option::Option<FriendDto_oneof__profileBackgroundImageLastModified>,
    pub _lastOnline: ::std::option::Option<FriendDto_oneof__lastOnline>,
    pub _onlineStatus: ::std::option::Option<FriendDto_oneof__onlineStatus>,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a FriendDto {
    fn default() -> &'a FriendDto {
        <FriendDto as ::protobuf::Message>::default_instance()
    }
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__socketID {
    socketID(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__roomID {
    roomID(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__nickname {
    nickname(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__color {
    color(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__statusText {
    statusText(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__profileDescription {
    profileDescription(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__becameFriendsDate {
    becameFriendsDate(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__profileImageLastModified {
    profileImageLastModified(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__profileBackgroundImageLastModified {
    profileBackgroundImageLastModified(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__lastOnline {
    lastOnline(::std::string::String),
}

#[derive(Clone,PartialEq,Debug)]
pub enum FriendDto_oneof__onlineStatus {
    onlineStatus(FriendDto_FriendOnlineStatus),
}

impl FriendDto {
    pub fn new() -> FriendDto {
        ::std::default::Default::default()
    }

    // string username = 1;


    pub fn get_username(&self) -> &str {
        &self.username
    }
    pub fn clear_username(&mut self) {
        self.username.clear();
    }

    // Param is passed by value, moved
    pub fn set_username(&mut self, v: ::std::string::String) {
        self.username = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_username(&mut self) -> &mut ::std::string::String {
        &mut self.username
    }

    // Take field
    pub fn take_username(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.username, ::std::string::String::new())
    }

    // string usertag = 2;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string socketID = 3;


    pub fn get_socketID(&self) -> &str {
        match self._socketID {
            ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_socketID(&mut self) {
        self._socketID = ::std::option::Option::None;
    }

    pub fn has_socketID(&self) -> bool {
        match self._socketID {
            ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_socketID(&mut self, v: ::std::string::String) {
        self._socketID = ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_socketID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(_)) = self._socketID {
        } else {
            self._socketID = ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(::std::string::String::new()));
        }
        match self._socketID {
            ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_socketID(&mut self) -> ::std::string::String {
        if self.has_socketID() {
            match self._socketID.take() {
                ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string roomID = 4;


    pub fn get_roomID(&self) -> &str {
        match self._roomID {
            ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_roomID(&mut self) {
        self._roomID = ::std::option::Option::None;
    }

    pub fn has_roomID(&self) -> bool {
        match self._roomID {
            ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_roomID(&mut self, v: ::std::string::String) {
        self._roomID = ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(v))
    }

    // Mutable pointer to the field.
    pub fn mut_roomID(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(_)) = self._roomID {
        } else {
            self._roomID = ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(::std::string::String::new()));
        }
        match self._roomID {
            ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_roomID(&mut self) -> ::std::string::String {
        if self.has_roomID() {
            match self._roomID.take() {
                ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // repeated .PianoRhythm.Serialization.ServerToClient.UserRenditions.Roles roles = 5;


    pub fn get_roles(&self) -> &[Roles] {
        &self.roles
    }
    pub fn clear_roles(&mut self) {
        self.roles.clear();
    }

    // Param is passed by value, moved
    pub fn set_roles(&mut self, v: ::std::vec::Vec<Roles>) {
        self.roles = v;
    }

    // Mutable pointer to the field.
    pub fn mut_roles(&mut self) -> &mut ::std::vec::Vec<Roles> {
        &mut self.roles
    }

    // Take field
    pub fn take_roles(&mut self) -> ::std::vec::Vec<Roles> {
        ::std::mem::replace(&mut self.roles, ::std::vec::Vec::new())
    }

    // string nickname = 6;


    pub fn get_nickname(&self) -> &str {
        match self._nickname {
            ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_nickname(&mut self) {
        self._nickname = ::std::option::Option::None;
    }

    pub fn has_nickname(&self) -> bool {
        match self._nickname {
            ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_nickname(&mut self, v: ::std::string::String) {
        self._nickname = ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(v))
    }

    // Mutable pointer to the field.
    pub fn mut_nickname(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(_)) = self._nickname {
        } else {
            self._nickname = ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(::std::string::String::new()));
        }
        match self._nickname {
            ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_nickname(&mut self) -> ::std::string::String {
        if self.has_nickname() {
            match self._nickname.take() {
                ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string color = 7;


    pub fn get_color(&self) -> &str {
        match self._color {
            ::std::option::Option::Some(FriendDto_oneof__color::color(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_color(&mut self) {
        self._color = ::std::option::Option::None;
    }

    pub fn has_color(&self) -> bool {
        match self._color {
            ::std::option::Option::Some(FriendDto_oneof__color::color(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_color(&mut self, v: ::std::string::String) {
        self._color = ::std::option::Option::Some(FriendDto_oneof__color::color(v))
    }

    // Mutable pointer to the field.
    pub fn mut_color(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__color::color(_)) = self._color {
        } else {
            self._color = ::std::option::Option::Some(FriendDto_oneof__color::color(::std::string::String::new()));
        }
        match self._color {
            ::std::option::Option::Some(FriendDto_oneof__color::color(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_color(&mut self) -> ::std::string::String {
        if self.has_color() {
            match self._color.take() {
                ::std::option::Option::Some(FriendDto_oneof__color::color(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string statusText = 8;


    pub fn get_statusText(&self) -> &str {
        match self._statusText {
            ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_statusText(&mut self) {
        self._statusText = ::std::option::Option::None;
    }

    pub fn has_statusText(&self) -> bool {
        match self._statusText {
            ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_statusText(&mut self, v: ::std::string::String) {
        self._statusText = ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(v))
    }

    // Mutable pointer to the field.
    pub fn mut_statusText(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(_)) = self._statusText {
        } else {
            self._statusText = ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(::std::string::String::new()));
        }
        match self._statusText {
            ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_statusText(&mut self) -> ::std::string::String {
        if self.has_statusText() {
            match self._statusText.take() {
                ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileDescription = 9;


    pub fn get_profileDescription(&self) -> &str {
        match self._profileDescription {
            ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileDescription(&mut self) {
        self._profileDescription = ::std::option::Option::None;
    }

    pub fn has_profileDescription(&self) -> bool {
        match self._profileDescription {
            ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileDescription(&mut self, v: ::std::string::String) {
        self._profileDescription = ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileDescription(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(_)) = self._profileDescription {
        } else {
            self._profileDescription = ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(::std::string::String::new()));
        }
        match self._profileDescription {
            ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileDescription(&mut self) -> ::std::string::String {
        if self.has_profileDescription() {
            match self._profileDescription.take() {
                ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string becameFriendsDate = 10;


    pub fn get_becameFriendsDate(&self) -> &str {
        match self._becameFriendsDate {
            ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_becameFriendsDate(&mut self) {
        self._becameFriendsDate = ::std::option::Option::None;
    }

    pub fn has_becameFriendsDate(&self) -> bool {
        match self._becameFriendsDate {
            ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_becameFriendsDate(&mut self, v: ::std::string::String) {
        self._becameFriendsDate = ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(v))
    }

    // Mutable pointer to the field.
    pub fn mut_becameFriendsDate(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(_)) = self._becameFriendsDate {
        } else {
            self._becameFriendsDate = ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(::std::string::String::new()));
        }
        match self._becameFriendsDate {
            ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_becameFriendsDate(&mut self) -> ::std::string::String {
        if self.has_becameFriendsDate() {
            match self._becameFriendsDate.take() {
                ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileImageLastModified = 11;


    pub fn get_profileImageLastModified(&self) -> &str {
        match self._profileImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileImageLastModified(&mut self) {
        self._profileImageLastModified = ::std::option::Option::None;
    }

    pub fn has_profileImageLastModified(&self) -> bool {
        match self._profileImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileImageLastModified(&mut self, v: ::std::string::String) {
        self._profileImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileImageLastModified(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(_)) = self._profileImageLastModified {
        } else {
            self._profileImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(::std::string::String::new()));
        }
        match self._profileImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileImageLastModified(&mut self) -> ::std::string::String {
        if self.has_profileImageLastModified() {
            match self._profileImageLastModified.take() {
                ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string profileBackgroundImageLastModified = 12;


    pub fn get_profileBackgroundImageLastModified(&self) -> &str {
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_profileBackgroundImageLastModified(&mut self) {
        self._profileBackgroundImageLastModified = ::std::option::Option::None;
    }

    pub fn has_profileBackgroundImageLastModified(&self) -> bool {
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_profileBackgroundImageLastModified(&mut self, v: ::std::string::String) {
        self._profileBackgroundImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(v))
    }

    // Mutable pointer to the field.
    pub fn mut_profileBackgroundImageLastModified(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(_)) = self._profileBackgroundImageLastModified {
        } else {
            self._profileBackgroundImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(::std::string::String::new()));
        }
        match self._profileBackgroundImageLastModified {
            ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_profileBackgroundImageLastModified(&mut self) -> ::std::string::String {
        if self.has_profileBackgroundImageLastModified() {
            match self._profileBackgroundImageLastModified.take() {
                ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // string lastOnline = 13;


    pub fn get_lastOnline(&self) -> &str {
        match self._lastOnline {
            ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(ref v)) => v,
            _ => "",
        }
    }
    pub fn clear_lastOnline(&mut self) {
        self._lastOnline = ::std::option::Option::None;
    }

    pub fn has_lastOnline(&self) -> bool {
        match self._lastOnline {
            ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_lastOnline(&mut self, v: ::std::string::String) {
        self._lastOnline = ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(v))
    }

    // Mutable pointer to the field.
    pub fn mut_lastOnline(&mut self) -> &mut ::std::string::String {
        if let ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(_)) = self._lastOnline {
        } else {
            self._lastOnline = ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(::std::string::String::new()));
        }
        match self._lastOnline {
            ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(ref mut v)) => v,
            _ => panic!(),
        }
    }

    // Take field
    pub fn take_lastOnline(&mut self) -> ::std::string::String {
        if self.has_lastOnline() {
            match self._lastOnline.take() {
                ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(v)) => v,
                _ => panic!(),
            }
        } else {
            ::std::string::String::new()
        }
    }

    // .PianoRhythm.Serialization.ServerToClient.UserRenditions.FriendDto.FriendOnlineStatus onlineStatus = 14;


    pub fn get_onlineStatus(&self) -> FriendDto_FriendOnlineStatus {
        match self._onlineStatus {
            ::std::option::Option::Some(FriendDto_oneof__onlineStatus::onlineStatus(v)) => v,
            _ => FriendDto_FriendOnlineStatus::None,
        }
    }
    pub fn clear_onlineStatus(&mut self) {
        self._onlineStatus = ::std::option::Option::None;
    }

    pub fn has_onlineStatus(&self) -> bool {
        match self._onlineStatus {
            ::std::option::Option::Some(FriendDto_oneof__onlineStatus::onlineStatus(..)) => true,
            _ => false,
        }
    }

    // Param is passed by value, moved
    pub fn set_onlineStatus(&mut self, v: FriendDto_FriendOnlineStatus) {
        self._onlineStatus = ::std::option::Option::Some(FriendDto_oneof__onlineStatus::onlineStatus(v))
    }
}

impl ::protobuf::Message for FriendDto {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.username)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                3 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._socketID = ::std::option::Option::Some(FriendDto_oneof__socketID::socketID(is.read_string()?));
                },
                4 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._roomID = ::std::option::Option::Some(FriendDto_oneof__roomID::roomID(is.read_string()?));
                },
                5 => {
                    ::protobuf::rt::read_repeated_enum_with_unknown_fields_into(wire_type, is, &mut self.roles, 5, &mut self.unknown_fields)?
                },
                6 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._nickname = ::std::option::Option::Some(FriendDto_oneof__nickname::nickname(is.read_string()?));
                },
                7 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._color = ::std::option::Option::Some(FriendDto_oneof__color::color(is.read_string()?));
                },
                8 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._statusText = ::std::option::Option::Some(FriendDto_oneof__statusText::statusText(is.read_string()?));
                },
                9 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._profileDescription = ::std::option::Option::Some(FriendDto_oneof__profileDescription::profileDescription(is.read_string()?));
                },
                10 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._becameFriendsDate = ::std::option::Option::Some(FriendDto_oneof__becameFriendsDate::becameFriendsDate(is.read_string()?));
                },
                11 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._profileImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileImageLastModified::profileImageLastModified(is.read_string()?));
                },
                12 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._profileBackgroundImageLastModified = ::std::option::Option::Some(FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(is.read_string()?));
                },
                13 => {
                    if wire_type != ::protobuf::wire_format::WireTypeLengthDelimited {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._lastOnline = ::std::option::Option::Some(FriendDto_oneof__lastOnline::lastOnline(is.read_string()?));
                },
                14 => {
                    if wire_type != ::protobuf::wire_format::WireTypeVarint {
                        return ::std::result::Result::Err(::protobuf::rt::unexpected_wire_type(wire_type));
                    }
                    self._onlineStatus = ::std::option::Option::Some(FriendDto_oneof__onlineStatus::onlineStatus(is.read_enum()?));
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.username.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.username);
        }
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.usertag);
        }
        for value in &self.roles {
            my_size += ::protobuf::rt::enum_size(5, *value);
        };
        if let ::std::option::Option::Some(ref v) = self._socketID {
            match v {
                &FriendDto_oneof__socketID::socketID(ref v) => {
                    my_size += ::protobuf::rt::string_size(3, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._roomID {
            match v {
                &FriendDto_oneof__roomID::roomID(ref v) => {
                    my_size += ::protobuf::rt::string_size(4, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._nickname {
            match v {
                &FriendDto_oneof__nickname::nickname(ref v) => {
                    my_size += ::protobuf::rt::string_size(6, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._color {
            match v {
                &FriendDto_oneof__color::color(ref v) => {
                    my_size += ::protobuf::rt::string_size(7, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._statusText {
            match v {
                &FriendDto_oneof__statusText::statusText(ref v) => {
                    my_size += ::protobuf::rt::string_size(8, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileDescription {
            match v {
                &FriendDto_oneof__profileDescription::profileDescription(ref v) => {
                    my_size += ::protobuf::rt::string_size(9, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._becameFriendsDate {
            match v {
                &FriendDto_oneof__becameFriendsDate::becameFriendsDate(ref v) => {
                    my_size += ::protobuf::rt::string_size(10, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileImageLastModified {
            match v {
                &FriendDto_oneof__profileImageLastModified::profileImageLastModified(ref v) => {
                    my_size += ::protobuf::rt::string_size(11, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileBackgroundImageLastModified {
            match v {
                &FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v) => {
                    my_size += ::protobuf::rt::string_size(12, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._lastOnline {
            match v {
                &FriendDto_oneof__lastOnline::lastOnline(ref v) => {
                    my_size += ::protobuf::rt::string_size(13, &v);
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._onlineStatus {
            match v {
                &FriendDto_oneof__onlineStatus::onlineStatus(v) => {
                    my_size += ::protobuf::rt::enum_size(14, v);
                },
            };
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.username.is_empty() {
            os.write_string(1, &self.username)?;
        }
        if !self.usertag.is_empty() {
            os.write_string(2, &self.usertag)?;
        }
        for v in &self.roles {
            os.write_enum(5, ::protobuf::ProtobufEnum::value(v))?;
        };
        if let ::std::option::Option::Some(ref v) = self._socketID {
            match v {
                &FriendDto_oneof__socketID::socketID(ref v) => {
                    os.write_string(3, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._roomID {
            match v {
                &FriendDto_oneof__roomID::roomID(ref v) => {
                    os.write_string(4, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._nickname {
            match v {
                &FriendDto_oneof__nickname::nickname(ref v) => {
                    os.write_string(6, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._color {
            match v {
                &FriendDto_oneof__color::color(ref v) => {
                    os.write_string(7, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._statusText {
            match v {
                &FriendDto_oneof__statusText::statusText(ref v) => {
                    os.write_string(8, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileDescription {
            match v {
                &FriendDto_oneof__profileDescription::profileDescription(ref v) => {
                    os.write_string(9, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._becameFriendsDate {
            match v {
                &FriendDto_oneof__becameFriendsDate::becameFriendsDate(ref v) => {
                    os.write_string(10, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileImageLastModified {
            match v {
                &FriendDto_oneof__profileImageLastModified::profileImageLastModified(ref v) => {
                    os.write_string(11, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._profileBackgroundImageLastModified {
            match v {
                &FriendDto_oneof__profileBackgroundImageLastModified::profileBackgroundImageLastModified(ref v) => {
                    os.write_string(12, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._lastOnline {
            match v {
                &FriendDto_oneof__lastOnline::lastOnline(ref v) => {
                    os.write_string(13, v)?;
                },
            };
        }
        if let ::std::option::Option::Some(ref v) = self._onlineStatus {
            match v {
                &FriendDto_oneof__onlineStatus::onlineStatus(v) => {
                    os.write_enum(14, ::protobuf::ProtobufEnum::value(&v))?;
                },
            };
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> FriendDto {
        FriendDto::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "username",
                |m: &FriendDto| { &m.username },
                |m: &mut FriendDto| { &mut m.username },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &FriendDto| { &m.usertag },
                |m: &mut FriendDto| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "socketID",
                FriendDto::has_socketID,
                FriendDto::get_socketID,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "roomID",
                FriendDto::has_roomID,
                FriendDto::get_roomID,
            ));
            fields.push(::protobuf::reflect::accessor::make_vec_accessor::<_, ::protobuf::types::ProtobufTypeEnum<Roles>>(
                "roles",
                |m: &FriendDto| { &m.roles },
                |m: &mut FriendDto| { &mut m.roles },
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "nickname",
                FriendDto::has_nickname,
                FriendDto::get_nickname,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "color",
                FriendDto::has_color,
                FriendDto::get_color,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "statusText",
                FriendDto::has_statusText,
                FriendDto::get_statusText,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileDescription",
                FriendDto::has_profileDescription,
                FriendDto::get_profileDescription,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "becameFriendsDate",
                FriendDto::has_becameFriendsDate,
                FriendDto::get_becameFriendsDate,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileImageLastModified",
                FriendDto::has_profileImageLastModified,
                FriendDto::get_profileImageLastModified,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "profileBackgroundImageLastModified",
                FriendDto::has_profileBackgroundImageLastModified,
                FriendDto::get_profileBackgroundImageLastModified,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_string_accessor::<_>(
                "lastOnline",
                FriendDto::has_lastOnline,
                FriendDto::get_lastOnline,
            ));
            fields.push(::protobuf::reflect::accessor::make_singular_enum_accessor::<_, FriendDto_FriendOnlineStatus>(
                "onlineStatus",
                FriendDto::has_onlineStatus,
                FriendDto::get_onlineStatus,
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<FriendDto>(
                "FriendDto",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static FriendDto {
        static instance: ::protobuf::rt::LazyV2<FriendDto> = ::protobuf::rt::LazyV2::INIT;
        instance.get(FriendDto::new)
    }
}

impl ::protobuf::Clear for FriendDto {
    fn clear(&mut self) {
        self.username.clear();
        self.usertag.clear();
        self._socketID = ::std::option::Option::None;
        self._roomID = ::std::option::Option::None;
        self.roles.clear();
        self._nickname = ::std::option::Option::None;
        self._color = ::std::option::Option::None;
        self._statusText = ::std::option::Option::None;
        self._profileDescription = ::std::option::Option::None;
        self._becameFriendsDate = ::std::option::Option::None;
        self._profileImageLastModified = ::std::option::Option::None;
        self._profileBackgroundImageLastModified = ::std::option::Option::None;
        self._lastOnline = ::std::option::Option::None;
        self._onlineStatus = ::std::option::Option::None;
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for FriendDto {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for FriendDto {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum FriendDto_FriendOnlineStatus {
    None = 0,
    Unknown = 1,
    Online = 2,
    Idle = 3,
    Offline = 4,
    PendingAcceptance = 5,
}

impl ::protobuf::ProtobufEnum for FriendDto_FriendOnlineStatus {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<FriendDto_FriendOnlineStatus> {
        match value {
            0 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::None),
            1 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::Unknown),
            2 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::Online),
            3 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::Idle),
            4 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::Offline),
            5 => ::std::option::Option::Some(FriendDto_FriendOnlineStatus::PendingAcceptance),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [FriendDto_FriendOnlineStatus] = &[
            FriendDto_FriendOnlineStatus::None,
            FriendDto_FriendOnlineStatus::Unknown,
            FriendDto_FriendOnlineStatus::Online,
            FriendDto_FriendOnlineStatus::Idle,
            FriendDto_FriendOnlineStatus::Offline,
            FriendDto_FriendOnlineStatus::PendingAcceptance,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<FriendDto_FriendOnlineStatus>("FriendDto.FriendOnlineStatus", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for FriendDto_FriendOnlineStatus {
}

impl ::std::default::Default for FriendDto_FriendOnlineStatus {
    fn default() -> Self {
        FriendDto_FriendOnlineStatus::None
    }
}

impl ::protobuf::reflect::ProtobufValue for FriendDto_FriendOnlineStatus {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(PartialEq,Clone,Default)]
pub struct PendingFriendRequest {
    // message fields
    pub usertag: ::std::string::String,
    pub uuid: ::std::string::String,
    pub createdDate: ::std::string::String,
    // special fields
    pub unknown_fields: ::protobuf::UnknownFields,
    pub cached_size: ::protobuf::CachedSize,
}

impl<'a> ::std::default::Default for &'a PendingFriendRequest {
    fn default() -> &'a PendingFriendRequest {
        <PendingFriendRequest as ::protobuf::Message>::default_instance()
    }
}

impl PendingFriendRequest {
    pub fn new() -> PendingFriendRequest {
        ::std::default::Default::default()
    }

    // string usertag = 1;


    pub fn get_usertag(&self) -> &str {
        &self.usertag
    }
    pub fn clear_usertag(&mut self) {
        self.usertag.clear();
    }

    // Param is passed by value, moved
    pub fn set_usertag(&mut self, v: ::std::string::String) {
        self.usertag = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_usertag(&mut self) -> &mut ::std::string::String {
        &mut self.usertag
    }

    // Take field
    pub fn take_usertag(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.usertag, ::std::string::String::new())
    }

    // string uuid = 2;


    pub fn get_uuid(&self) -> &str {
        &self.uuid
    }
    pub fn clear_uuid(&mut self) {
        self.uuid.clear();
    }

    // Param is passed by value, moved
    pub fn set_uuid(&mut self, v: ::std::string::String) {
        self.uuid = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_uuid(&mut self) -> &mut ::std::string::String {
        &mut self.uuid
    }

    // Take field
    pub fn take_uuid(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.uuid, ::std::string::String::new())
    }

    // string createdDate = 3;


    pub fn get_createdDate(&self) -> &str {
        &self.createdDate
    }
    pub fn clear_createdDate(&mut self) {
        self.createdDate.clear();
    }

    // Param is passed by value, moved
    pub fn set_createdDate(&mut self, v: ::std::string::String) {
        self.createdDate = v;
    }

    // Mutable pointer to the field.
    // If field is not initialized, it is initialized with default value first.
    pub fn mut_createdDate(&mut self) -> &mut ::std::string::String {
        &mut self.createdDate
    }

    // Take field
    pub fn take_createdDate(&mut self) -> ::std::string::String {
        ::std::mem::replace(&mut self.createdDate, ::std::string::String::new())
    }
}

impl ::protobuf::Message for PendingFriendRequest {
    fn is_initialized(&self) -> bool {
        true
    }

    fn merge_from(&mut self, is: &mut ::protobuf::CodedInputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        while !is.eof()? {
            let (field_number, wire_type) = is.read_tag_unpack()?;
            match field_number {
                1 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.usertag)?;
                },
                2 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.uuid)?;
                },
                3 => {
                    ::protobuf::rt::read_singular_proto3_string_into(wire_type, is, &mut self.createdDate)?;
                },
                _ => {
                    ::protobuf::rt::read_unknown_or_skip_group(field_number, wire_type, is, self.mut_unknown_fields())?;
                },
            };
        }
        ::std::result::Result::Ok(())
    }

    // Compute sizes of nested messages
    #[allow(unused_variables)]
    fn compute_size(&self) -> u32 {
        let mut my_size = 0;
        if !self.usertag.is_empty() {
            my_size += ::protobuf::rt::string_size(1, &self.usertag);
        }
        if !self.uuid.is_empty() {
            my_size += ::protobuf::rt::string_size(2, &self.uuid);
        }
        if !self.createdDate.is_empty() {
            my_size += ::protobuf::rt::string_size(3, &self.createdDate);
        }
        my_size += ::protobuf::rt::unknown_fields_size(self.get_unknown_fields());
        self.cached_size.set(my_size);
        my_size
    }

    fn write_to_with_cached_sizes(&self, os: &mut ::protobuf::CodedOutputStream<'_>) -> ::protobuf::ProtobufResult<()> {
        if !self.usertag.is_empty() {
            os.write_string(1, &self.usertag)?;
        }
        if !self.uuid.is_empty() {
            os.write_string(2, &self.uuid)?;
        }
        if !self.createdDate.is_empty() {
            os.write_string(3, &self.createdDate)?;
        }
        os.write_unknown_fields(self.get_unknown_fields())?;
        ::std::result::Result::Ok(())
    }

    fn get_cached_size(&self) -> u32 {
        self.cached_size.get()
    }

    fn get_unknown_fields(&self) -> &::protobuf::UnknownFields {
        &self.unknown_fields
    }

    fn mut_unknown_fields(&mut self) -> &mut ::protobuf::UnknownFields {
        &mut self.unknown_fields
    }

    fn as_any(&self) -> &dyn (::std::any::Any) {
        self as &dyn (::std::any::Any)
    }
    fn as_any_mut(&mut self) -> &mut dyn (::std::any::Any) {
        self as &mut dyn (::std::any::Any)
    }
    fn into_any(self: ::std::boxed::Box<Self>) -> ::std::boxed::Box<dyn (::std::any::Any)> {
        self
    }

    fn descriptor(&self) -> &'static ::protobuf::reflect::MessageDescriptor {
        Self::descriptor_static()
    }

    fn new() -> PendingFriendRequest {
        PendingFriendRequest::new()
    }

    fn descriptor_static() -> &'static ::protobuf::reflect::MessageDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::MessageDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            let mut fields = ::std::vec::Vec::new();
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "usertag",
                |m: &PendingFriendRequest| { &m.usertag },
                |m: &mut PendingFriendRequest| { &mut m.usertag },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "uuid",
                |m: &PendingFriendRequest| { &m.uuid },
                |m: &mut PendingFriendRequest| { &mut m.uuid },
            ));
            fields.push(::protobuf::reflect::accessor::make_simple_field_accessor::<_, ::protobuf::types::ProtobufTypeString>(
                "createdDate",
                |m: &PendingFriendRequest| { &m.createdDate },
                |m: &mut PendingFriendRequest| { &mut m.createdDate },
            ));
            ::protobuf::reflect::MessageDescriptor::new_pb_name::<PendingFriendRequest>(
                "PendingFriendRequest",
                fields,
                file_descriptor_proto()
            )
        })
    }

    fn default_instance() -> &'static PendingFriendRequest {
        static instance: ::protobuf::rt::LazyV2<PendingFriendRequest> = ::protobuf::rt::LazyV2::INIT;
        instance.get(PendingFriendRequest::new)
    }
}

impl ::protobuf::Clear for PendingFriendRequest {
    fn clear(&mut self) {
        self.usertag.clear();
        self.uuid.clear();
        self.createdDate.clear();
        self.unknown_fields.clear();
    }
}

impl ::std::fmt::Debug for PendingFriendRequest {
    fn fmt(&self, f: &mut ::std::fmt::Formatter<'_>) -> ::std::fmt::Result {
        ::protobuf::text_format::fmt(self, f)
    }
}

impl ::protobuf::reflect::ProtobufValue for PendingFriendRequest {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Message(self)
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum Roles {
    ADMIN = 0,
    SYSTEM = 1,
    DEVELOPER = 2,
    MODERATOR = 3,
    TRIAL_MODERATOR = 4,
    MEMBER = 5,
    ROOMOWNER = 6,
    BOT = 7,
    HELPBOT = 8,
    DISCORDBOT = 9,
    GUEST = 10,
    SHEETMUSICEDITOR = 11,
    MIDIMUSICEDITOR = 12,
    PLUGIN = 13,
    PRO = 14,
    UNKNOWN = 15,
}

impl ::protobuf::ProtobufEnum for Roles {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<Roles> {
        match value {
            0 => ::std::option::Option::Some(Roles::ADMIN),
            1 => ::std::option::Option::Some(Roles::SYSTEM),
            2 => ::std::option::Option::Some(Roles::DEVELOPER),
            3 => ::std::option::Option::Some(Roles::MODERATOR),
            4 => ::std::option::Option::Some(Roles::TRIAL_MODERATOR),
            5 => ::std::option::Option::Some(Roles::MEMBER),
            6 => ::std::option::Option::Some(Roles::ROOMOWNER),
            7 => ::std::option::Option::Some(Roles::BOT),
            8 => ::std::option::Option::Some(Roles::HELPBOT),
            9 => ::std::option::Option::Some(Roles::DISCORDBOT),
            10 => ::std::option::Option::Some(Roles::GUEST),
            11 => ::std::option::Option::Some(Roles::SHEETMUSICEDITOR),
            12 => ::std::option::Option::Some(Roles::MIDIMUSICEDITOR),
            13 => ::std::option::Option::Some(Roles::PLUGIN),
            14 => ::std::option::Option::Some(Roles::PRO),
            15 => ::std::option::Option::Some(Roles::UNKNOWN),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [Roles] = &[
            Roles::ADMIN,
            Roles::SYSTEM,
            Roles::DEVELOPER,
            Roles::MODERATOR,
            Roles::TRIAL_MODERATOR,
            Roles::MEMBER,
            Roles::ROOMOWNER,
            Roles::BOT,
            Roles::HELPBOT,
            Roles::DISCORDBOT,
            Roles::GUEST,
            Roles::SHEETMUSICEDITOR,
            Roles::MIDIMUSICEDITOR,
            Roles::PLUGIN,
            Roles::PRO,
            Roles::UNKNOWN,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<Roles>("Roles", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for Roles {
}

impl ::std::default::Default for Roles {
    fn default() -> Self {
        Roles::ADMIN
    }
}

impl ::protobuf::reflect::ProtobufValue for Roles {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum Badges {
    V3_CLOSED_ALPHA_TESTER = 0,
    V2_OG_MEMBER = 1,
    PRO_MEMBER = 2,
    TranslationMasterContributor = 3,
    TranslationContributor = 4,
    CUSTOM = 5,
}

impl ::protobuf::ProtobufEnum for Badges {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<Badges> {
        match value {
            0 => ::std::option::Option::Some(Badges::V3_CLOSED_ALPHA_TESTER),
            1 => ::std::option::Option::Some(Badges::V2_OG_MEMBER),
            2 => ::std::option::Option::Some(Badges::PRO_MEMBER),
            3 => ::std::option::Option::Some(Badges::TranslationMasterContributor),
            4 => ::std::option::Option::Some(Badges::TranslationContributor),
            5 => ::std::option::Option::Some(Badges::CUSTOM),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [Badges] = &[
            Badges::V3_CLOSED_ALPHA_TESTER,
            Badges::V2_OG_MEMBER,
            Badges::PRO_MEMBER,
            Badges::TranslationMasterContributor,
            Badges::TranslationContributor,
            Badges::CUSTOM,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<Badges>("Badges", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for Badges {
}

impl ::std::default::Default for Badges {
    fn default() -> Self {
        Badges::V3_CLOSED_ALPHA_TESTER
    }
}

impl ::protobuf::reflect::ProtobufValue for Badges {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

#[derive(Clone,PartialEq,Eq,Debug,Hash)]
pub enum UserStatus {
    None = 0,
    Unknown = 1,
    Online = 2,
    Idle = 3,
    DoNotDisturb = 4,
    AFK = 5,
}

impl ::protobuf::ProtobufEnum for UserStatus {
    fn value(&self) -> i32 {
        *self as i32
    }

    fn from_i32(value: i32) -> ::std::option::Option<UserStatus> {
        match value {
            0 => ::std::option::Option::Some(UserStatus::None),
            1 => ::std::option::Option::Some(UserStatus::Unknown),
            2 => ::std::option::Option::Some(UserStatus::Online),
            3 => ::std::option::Option::Some(UserStatus::Idle),
            4 => ::std::option::Option::Some(UserStatus::DoNotDisturb),
            5 => ::std::option::Option::Some(UserStatus::AFK),
            _ => ::std::option::Option::None
        }
    }

    fn values() -> &'static [Self] {
        static values: &'static [UserStatus] = &[
            UserStatus::None,
            UserStatus::Unknown,
            UserStatus::Online,
            UserStatus::Idle,
            UserStatus::DoNotDisturb,
            UserStatus::AFK,
        ];
        values
    }

    fn enum_descriptor_static() -> &'static ::protobuf::reflect::EnumDescriptor {
        static descriptor: ::protobuf::rt::LazyV2<::protobuf::reflect::EnumDescriptor> = ::protobuf::rt::LazyV2::INIT;
        descriptor.get(|| {
            ::protobuf::reflect::EnumDescriptor::new_pb_name::<UserStatus>("UserStatus", file_descriptor_proto())
        })
    }
}

impl ::std::marker::Copy for UserStatus {
}

impl ::std::default::Default for UserStatus {
    fn default() -> Self {
        UserStatus::None
    }
}

impl ::protobuf::reflect::ProtobufValue for UserStatus {
    fn as_ref(&self) -> ::protobuf::reflect::ReflectValueRef {
        ::protobuf::reflect::ReflectValueRef::Enum(::protobuf::ProtobufEnum::descriptor(self))
    }
}

static file_descriptor_proto_data: &'static [u8] = b"\
    \n\x15user-renditions.proto\x127PianoRhythm.Serialization.ServerToClient\
    .UserRenditions\"\x88\x01\n\nUserBadges\x12U\n\x05badge\x18\x01\x20\x01(\
    \x0e2?.PianoRhythm.Serialization.ServerToClient.UserRenditions.BadgesR\
    \x05badge\x12\x19\n\x05value\x18\x02\x20\x01(\tH\0R\x05value\x88\x01\x01\
    B\x08\n\x06_value\"\x91\x02\n\x0bUserMetaDto\x12\x10\n\x03bot\x18\x01\
    \x20\x01(\x08R\x03bot\x12\x1e\n\ndiscordBot\x18\x02\x20\x01(\x08R\ndisco\
    rdBot\x12'\n\x0cmodifiedDate\x18\x03\x20\x01(\tH\0R\x0cmodifiedDate\x88\
    \x01\x01\x121\n\x11clientMetaDetails\x18\x04\x20\x01(\tH\x01R\x11clientM\
    etaDetails\x88\x01\x01\x125\n\x13enteredRoomDateTime\x18\x06\x20\x01(\tH\
    \x02R\x13enteredRoomDateTime\x88\x01\x01B\x0f\n\r_modifiedDateB\x14\n\
    \x12_clientMetaDetailsB\x16\n\x14_enteredRoomDateTime\"\xc2\x02\n\x12Ava\
    tarWorldDataDto\x12\x91\x01\n\rworldPosition\x18\x01\x20\x01(\x0b2f.Pian\
    oRhythm.Serialization.ServerToClient.UserRenditions.AvatarWorldDataDto.A\
    vatarMessageWorldPositionH\0R\rworldPosition\x88\x01\x01\x12+\n\x0epiano\
    BenchSeat\x18\x02\x20\x01(\x05H\x01R\x0epianoBenchSeat\x88\x01\x01\x1aF\
    \n\x1aAvatarMessageWorldPosition\x12\x0c\n\x01x\x18\x01\x20\x01(\x01R\
    \x01x\x12\x0c\n\x01y\x18\x02\x20\x01(\x01R\x01y\x12\x0c\n\x01z\x18\x03\
    \x20\x01(\x01R\x01zB\x10\n\x0e_worldPositionB\x11\n\x0f_pianoBenchSeat\"\
    \xe3\x02\n\tWorldData\x121\n\x11characterDataJSON\x18\x01\x20\x01(\tH\0R\
    \x11characterDataJSON\x88\x01\x01\x12U\n#orchestraModelCustomizationData\
    JSON\x18\x02\x20\x01(\tH\x01R#orchestraModelCustomizationDataJSON\x88\
    \x01\x01\x12z\n\x0favatarWorldData\x18\x03\x20\x01(\x0b2K.PianoRhythm.Se\
    rialization.ServerToClient.UserRenditions.AvatarWorldDataDtoH\x02R\x0fav\
    atarWorldData\x88\x01\x01B\x14\n\x12_characterDataJSONB&\n$_orchestraMod\
    elCustomizationDataJSONB\x12\n\x10_avatarWorldData\"\xef\x08\n\x07UserDt\
    o\x12\x1a\n\x08username\x18\x01\x20\x01(\tR\x08username\x12\x18\n\x07use\
    rtag\x18\x02\x20\x01(\tR\x07usertag\x12\x1a\n\x08socketID\x18\x03\x20\
    \x01(\tR\x08socketID\x12T\n\x05roles\x18\x04\x20\x03(\x0e2>.PianoRhythm.\
    Serialization.ServerToClient.UserRenditions.RolesR\x05roles\x12[\n\x06ba\
    dges\x18\x05\x20\x03(\x0b2C.PianoRhythm.Serialization.ServerToClient.Use\
    rRenditions.UserBadgesR\x06badges\x12\x1f\n\x08nickname\x18\x06\x20\x01(\
    \tH\0R\x08nickname\x88\x01\x01\x12\x14\n\x05color\x18\x07\x20\x01(\tR\
    \x05color\x12X\n\x04meta\x18\x08\x20\x01(\x0b2D.PianoRhythm.Serializatio\
    n.ServerToClient.UserRenditions.UserMetaDtoR\x04meta\x12\x1c\n\tselfMute\
    d\x18\t\x20\x01(\x08R\tselfMuted\x12*\n\x10serverNotesMuted\x18\n\x20\
    \x01(\x08R\x10serverNotesMuted\x12(\n\x0fserverChatMuted\x18\x0b\x20\x01\
    (\x08R\x0fserverChatMuted\x12[\n\x06status\x18\x0c\x20\x01(\x0e2C.PianoR\
    hythm.Serialization.ServerToClient.UserRenditions.UserStatusR\x06status\
    \x123\n\x12ProfileDescription\x18\r\x20\x01(\tH\x01R\x12ProfileDescripti\
    on\x88\x01\x01\x12?\n\x18profileImageLastModified\x18\x0e\x20\x01(\tH\
    \x02R\x18profileImageLastModified\x88\x01\x01\x12S\n\"profileBackgroundI\
    mageLastModified\x18\x0f\x20\x01(\tH\x03R\"profileBackgroundImageLastMod\
    ified\x88\x01\x01\x12`\n\tworldData\x18\x10\x20\x01(\x0b2B.PianoRhythm.S\
    erialization.ServerToClient.UserRenditions.WorldDataR\tworldData\x12\x20\
    \n\x0bisProMember\x18\x11\x20\x01(\x08R\x0bisProMember\x12#\n\nstatusTex\
    t\x18\x12\x20\x01(\tH\x04R\nstatusText\x88\x01\x01\x12\x12\n\x04uuid\x18\
    \x13\x20\x01(\tR\x04uuidB\x0b\n\t_nicknameB\x15\n\x13_ProfileDescription\
    B\x1b\n\x19_profileImageLastModifiedB%\n#_profileBackgroundImageLastModi\
    fiedB\r\n\x0b_statusText\"\x9f\x03\n\x13UserBillingSettings\x12\x20\n\
    \x0bcurrentPlan\x18\x01\x20\x01(\tR\x0bcurrentPlan\x122\n\x14cancelation\
    InProcess\x18\x02\x20\x01(\x08R\x14cancelationInProcess\x12l\n\x04meta\
    \x18\x03\x20\x01(\x0b2X.PianoRhythm.Serialization.ServerToClient.UserRen\
    ditions.UserBillingSettings.BillingMetaR\x04meta\x1a\x99\x01\n\x0bBillin\
    gMeta\x12(\n\x0fnextBillingDate\x18\x01\x20\x01(\tR\x0fnextBillingDate\
    \x12\"\n\x0ccurrencyCode\x18\x02\x20\x01(\tR\x0ccurrencyCode\x12\"\n\x0c\
    pricePerUnit\x18\x03\x20\x01(\tR\x0cpricePerUnit\x12\x18\n\x07priceID\
    \x18\x04\x20\x01(\tR\x07priceID\"(\n\x10SubscriptionPlan\x12\x08\n\x04Fr\
    ee\x10\0\x12\n\n\x06Level1\x10\x01\"\xac\x01\n\rClientMetaDto\x12\x19\n\
    \x05email\x18\x01\x20\x01(\tH\0R\x05email\x88\x01\x01\x12v\n\x0fbillingS\
    ettings\x18\x02\x20\x01(\x0b2L.PianoRhythm.Serialization.ServerToClient.\
    UserRenditions.UserBillingSettingsR\x0fbillingSettingsB\x08\n\x06_email\
    \"}\n\x0eKickedUserData\x12\x1a\n\x08socketID\x18\x01\x20\x01(\tR\x08soc\
    ketID\x12\x18\n\x07usertag\x18\x02\x20\x01(\tR\x07usertag\x12\x12\n\x04t\
    ime\x18\x03\x20\x01(\tR\x04time\x12!\n\x0ccreated_date\x18\x04\x20\x01(\
    \tR\x0bcreatedDate\"\xd7\x02\n\rUserClientDto\x12Z\n\x07userDto\x18\x01\
    \x20\x01(\<EMAIL>.\
    UserDtoR\x07userDto\x12_\n\x04meta\x18\x02\x20\x01(\x0b2F.PianoRhythm.Se\
    rialization.ServerToClient.UserRenditions.ClientMetaDtoH\0R\x04meta\x88\
    \x01\x01\x122\n\x14canApproveSheetMusic\x18\x03\x20\x01(\x08R\x14canAppr\
    oveSheetMusic\x12$\n\rhas2faEnabled\x18\x04\x20\x01(\x08R\rhas2faEnabled\
    \x12&\n\x0eisOAuthAccount\x18\x05\x20\x01(\x08R\x0eisOAuthAccountB\x07\n\
    \x05_meta\"\xf8\x02\n\x11ClientSideUserDto\x12Z\n\x07userDto\x18\x01\x20\
    \x01(\<EMAIL>\
    DtoR\x07userDto\x12(\n\x0flocalNotesMuted\x18\x02\x20\x01(\x08R\x0flocal\
    NotesMuted\x12&\n\x0elocalChatMuted\x18\x03\x20\x01(\x08R\x0elocalChatMu\
    ted\x12=\n\x17clientMetaDetailsParsed\x18\x04\x20\x01(\tH\0R\x17clientMe\
    taDetailsParsed\x88\x01\x01\x12\x1a\n\x08socketID\x18\x05\x20\x01(\tR\
    \x08socketID\x12+\n\x0esocketIDHashed\x18\x06\x20\x01(\rH\x01R\x0esocket\
    IDHashed\x88\x01\x01B\x1a\n\x18_clientMetaDetailsParsedB\x11\n\x0f_socke\
    tIDHashed\"\xd6\x08\n\x11UserUpdateCommand\x12\x1a\n\x08socketID\x18\x01\
    \x20\x01(\tR\x08socketID\x12\x1e\n\tuserColor\x18\x02\x20\x01(\tH\0R\tus\
    erColor\x12e\n\nuserStatus\x18\x03\x20\x01(\x0e2C.PianoRhythm.Serializat\
    ion.ServerToClient.UserRenditions.UserStatusH\0R\nuserStatus\x12.\n\x11c\
    lientMetaDetails\x18\x04\x20\x01(\tH\0R\x11clientMetaDetails\x120\n\x12p\
    rofileDescription\x18\x05\x20\x01(\tH\0R\x12profileDescription\x12\x1e\n\
    \tselfMuted\x18\x06\x20\x01(\x08H\0R\tselfMuted\x12\x20\n\nstatusText\
    \x18\x07\x20\x01(\tH\0R\nstatusText\x12\x1c\n\x08nickname\x18\x08\x20\
    \x01(\tH\0R\x08nickname\x122\n\x13profileImageUpdated\x18\t\x20\x01(\tH\
    \0R\x13profileImageUpdated\x12F\n\x1dprofileBackgroundImageUpdated\x18\n\
    \x20\x01(\tH\0R\x1dprofileBackgroundImageUpdated\x122\n\x13profileImageC\
    leared\x18\x0b\x20\x01(\x08H\0R\x13profileImageCleared\x12F\n\x1dprofile\
    BackgroundImageCleared\x18\x0c\x20\x01(\x08H\0R\x1dprofileBackgroundImag\
    eCleared\x12(\n\x0eorchestraModel\x18\r\x20\x01(\tH\0R\x0eorchestraModel\
    \x12(\n\x0echaracterModel\x18\x0e\x20\x01(\tH\0R\x0echaracterModel\x12,\
    \n\x10serverNotesMuted\x18\x0f\x20\x01(\x08H\0R\x10serverNotesMuted\x12*\
    \n\x0fserverChatMuted\x18\x10\x20\x01(\x08H\0R\x0fserverChatMuted\x12\
    \x9a\x01\n\x13avatarWorldPosition\x18\x11\x20\x01(\x0b2f.PianoRhythm.Ser\
    ialization.ServerToClient.UserRenditions.AvatarWorldDataDto.AvatarMessag\
    eWorldPositionH\0R\x13avatarWorldPosition\x124\n\x14avatarPianoBenchSeat\
    \x18\x12\x20\x01(\x05H\0R\x14avatarPianoBenchSeat\x12[\n\x06badges\x182\
    \x20\x03(\x0b2C.PianoRhythm.Serialization.ServerToClient.UserRenditions.\
    UserBadgesR\x06badgesB\x06\n\x04data\"\x8f\x08\n\tFriendDto\x12\x1a\n\
    \x08username\x18\x01\x20\x01(\tR\x08username\x12\x18\n\x07usertag\x18\
    \x02\x20\x01(\tR\x07usertag\x12\x1f\n\x08socketID\x18\x03\x20\x01(\tH\0R\
    \x08socketID\x88\x01\x01\x12\x1b\n\x06roomID\x18\x04\x20\x01(\tH\x01R\
    \x06roomID\x88\x01\x01\x12T\n\x05roles\x18\x05\x20\x03(\x0e2>.PianoRhyth\
    m.Serialization.ServerToClient.UserRenditions.RolesR\x05roles\x12\x1f\n\
    \x08nickname\x18\x06\x20\x01(\tH\x02R\x08nickname\x88\x01\x01\x12\x19\n\
    \x05color\x18\x07\x20\x01(\tH\x03R\x05color\x88\x01\x01\x12#\n\nstatusTe\
    xt\x18\x08\x20\x01(\tH\x04R\nstatusText\x88\x01\x01\x123\n\x12profileDes\
    cription\x18\t\x20\x01(\tH\x05R\x12profileDescription\x88\x01\x01\x121\n\
    \x11becameFriendsDate\x18\n\x20\x01(\tH\x06R\x11becameFriendsDate\x88\
    \x01\x01\x12?\n\x18profileImageLastModified\x18\x0b\x20\x01(\tH\x07R\x18\
    profileImageLastModified\x88\x01\x01\x12S\n\"profileBackgroundImageLastM\
    odified\x18\x0c\x20\x01(\tH\x08R\"profileBackgroundImageLastModified\x88\
    \x01\x01\x12#\n\nlastOnline\x18\r\x20\x01(\tH\tR\nlastOnline\x88\x01\x01\
    \x12~\n\x0conlineStatus\x18\x0e\x20\x01(\x0e2U.PianoRhythm.Serialization\
    .ServerToClient.UserRenditions.FriendDto.FriendOnlineStatusH\nR\x0conlin\
    eStatus\x88\x01\x01\"e\n\x12FriendOnlineStatus\x12\x08\n\x04None\x10\0\
    \x12\x0b\n\x07Unknown\x10\x01\x12\n\n\x06Online\x10\x02\x12\x08\n\x04Idl\
    e\x10\x03\x12\x0b\n\x07Offline\x10\x04\x12\x15\n\x11PendingAcceptance\
    \x10\x05B\x0b\n\t_socketIDB\t\n\x07_roomIDB\x0b\n\t_nicknameB\x08\n\x06_\
    colorB\r\n\x0b_statusTextB\x15\n\x13_profileDescriptionB\x14\n\x12_becam\
    eFriendsDateB\x1b\n\x19_profileImageLastModifiedB%\n#_profileBackgroundI\
    mageLastModifiedB\r\n\x0b_lastOnlineB\x0f\n\r_onlineStatus\"f\n\x14Pendi\
    ngFriendRequest\x12\x18\n\x07usertag\x18\x01\x20\x01(\tR\x07usertag\x12\
    \x12\n\x04uuid\x18\x02\x20\x01(\tR\x04uuid\x12\x20\n\x0bcreatedDate\x18\
    \x03\x20\x01(\tR\x0bcreatedDate*\xea\x01\n\x05Roles\x12\t\n\x05ADMIN\x10\
    \0\x12\n\n\x06SYSTEM\x10\x01\x12\r\n\tDEVELOPER\x10\x02\x12\r\n\tMODERAT\
    OR\x10\x03\x12\x13\n\x0fTRIAL_MODERATOR\x10\x04\x12\n\n\x06MEMBER\x10\
    \x05\x12\r\n\tROOMOWNER\x10\x06\x12\x07\n\x03BOT\x10\x07\x12\x0b\n\x07HE\
    LPBOT\x10\x08\x12\x0e\n\nDISCORDBOT\x10\t\x12\t\n\x05GUEST\x10\n\x12\x14\
    \n\x10SHEETMUSICEDITOR\x10\x0b\x12\x13\n\x0fMIDIMUSICEDITOR\x10\x0c\x12\
    \n\n\x06PLUGIN\x10\r\x12\x07\n\x03PRO\x10\x0e\x12\x0b\n\x07UNKNOWN\x10\
    \x0f*\x90\x01\n\x06Badges\x12\x1a\n\x16V3_CLOSED_ALPHA_TESTER\x10\0\x12\
    \x10\n\x0cV2_OG_MEMBER\x10\x01\x12\x0e\n\nPRO_MEMBER\x10\x02\x12\x20\n\
    \x1cTranslationMasterContributor\x10\x03\x12\x1a\n\x16TranslationContrib\
    utor\x10\x04\x12\n\n\x06CUSTOM\x10\x05*T\n\nUserStatus\x12\x08\n\x04None\
    \x10\0\x12\x0b\n\x07Unknown\x10\x01\x12\n\n\x06Online\x10\x02\x12\x08\n\
    \x04Idle\x10\x03\x12\x10\n\x0cDoNotDisturb\x10\x04\x12\x07\n\x03AFK\x10\
    \x05b\x06proto3\
";

static file_descriptor_proto_lazy: ::protobuf::rt::LazyV2<::protobuf::descriptor::FileDescriptorProto> = ::protobuf::rt::LazyV2::INIT;

fn parse_descriptor_proto() -> ::protobuf::descriptor::FileDescriptorProto {
    ::protobuf::Message::parse_from_bytes(file_descriptor_proto_data).unwrap()
}

pub fn file_descriptor_proto() -> &'static ::protobuf::descriptor::FileDescriptorProto {
    file_descriptor_proto_lazy.get(|| {
        parse_descriptor_proto()
    })
}
