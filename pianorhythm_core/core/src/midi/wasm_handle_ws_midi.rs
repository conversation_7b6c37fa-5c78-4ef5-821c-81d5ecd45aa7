use std::rc::Rc;

use crate::midi::audio_scheduler::schedule_midi_batch_global;
use crate::midi::{handle_ws_midi_message_optimized, BatchedNoteSchedule, HandleWebsocketMidiMessage, ScheduledNote};
use crate::reducers::app_state::AppState;
use crate::types::CoreClientApiType;
use crate::PianoRhythmWebSocketMidiPitchBend;
use pianorhythm_proto::client_message::MidiMessageOutputDto;
use pianorhythm_proto::midi_renditions::MidiDtoType;
use pianorhythm_synth::PianoRhythmWebSocketMidiNoteOn;

pub struct WasmHandleMidiMessage<'c> {
    pub core_api: &'c CoreClientApiType,
}

impl<'c> HandleWebsocketMidiMessage for WasmHandleMidiMessage<'c> {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> () {
        if let Some(batch) = handle_ws_midi_message_optimized(&message, state) {
            self.schedule_note_batch_optimized(batch);
        } else {
            #[cfg(debug_assertions)]
            log::warn!("No batch to schedule.");
        }
    }

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule) {
        // Use audio-context-synchronized scheduling instead of timeouts
        let notes_with_timing: Vec<(f64, ScheduledNote)> =
            batch.notes.into_iter().map(|note| (note.delay_ms, note)).collect();

        // Schedule the entire batch using sample-accurate timing
        schedule_midi_batch_global(notes_with_timing);
    }
}
