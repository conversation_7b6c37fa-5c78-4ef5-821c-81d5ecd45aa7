use std::rc::Rc;

use pianorhythm_proto::client_message::{MidiDto, MidiMessageOutputDto};
use pianorhythm_proto::midi_renditions::{MidiDtoType};
#[cfg(feature = "use_synth")]
use pianorhythm_synth::{NoteSourceType, PianoRhythmWebSocketMidiNoteOn, PianoRhythmWebSocketMidiPitchBend};

use crate::reducers::app_state::AppState;
use crate::utils::hash_socket_id;

#[cfg(target_arch = "wasm32")]
pub mod wasm_handle_ws_midi;

#[cfg(target_arch = "wasm32")]
pub mod audio_scheduler;

#[cfg(target_arch = "wasm32")]
pub mod audio_scheduler_test;

#[cfg(not(target_arch = "wasm32"))]
pub mod handle_ws_midi;

pub trait HandleWebsocketMidiMessage {
    fn handle(&self, message: &MidiMessageOutputDto, state: Rc<AppState>) -> ();

    fn schedule_note_batch_optimized(&self, batch: BatchedNoteSchedule);
}

fn get_velocity_percentage(velocity: f32, percentage: f32) -> f32 {
    ((percentage / 100.0) * velocity).round()
}

fn calculate_velocity_from_percentage(
    velocity: f32,
    global_velocity_percentage: Option<f32>,
    user_velocity_percentage: Option<f32>,
) -> f32 {
    match global_velocity_percentage {
        None => velocity,
        Some(global_velocity_percentage) => match user_velocity_percentage {
            Some(user_velocity_percentage)
                if user_velocity_percentage != pianorhythm_shared::audio::MAX_VELOCITY_USER_PERCENTAGE as f32 =>
            {
                get_velocity_percentage(velocity, user_velocity_percentage)
            }
            _ => get_velocity_percentage(velocity, global_velocity_percentage),
        },
    }
}

pub type HandleWsMidiMessageClosure = Box<dyn Fn() + Send>;

pub type HandleWsMidiMessageOutput = Box<dyn Fn(Vec<(f64, HandleWsMidiMessageClosure)>) + Send>;

/// Handles an incoming MIDI message from a websocket, processes it according to the current
/// application audio state, and returns a vector of closures to be executed at specific times.
///
/// # Arguments
/// * `message` - The MIDI message received from the websocket.
/// * `state` - The current application state wrapped in an `Rc`.
///
/// # Returns
/// * `Option<Vec<(f64, HandleWsMidiMessageClosure)>>` - Returns `None` if the message should not be processed,
///   otherwise returns a vector of tuples containing the scheduled time (in ms) and the closure to execute.
pub fn handle_ws_midi_message(
    message: &MidiMessageOutputDto,
    state: Rc<AppState>,
) -> Option<Vec<(f64, HandleWsMidiMessageClosure)>> {
    let current_audio_state = &state.audio_process_state;

    if current_audio_state.muted_everyone_else {
        return None;
    }

    let midi_message = message.clone();
    let message_socket_id = midi_message.get_socketID();

    if message_socket_id.is_empty() {
        return None;
    }

    let socket_id_hash = hash_socket_id(&message_socket_id);

    let mut output: Vec<(f64, HandleWsMidiMessageClosure)> = vec![];

    #[cfg(feature = "use_synth")]
    if !current_audio_state
        .muted_users
        .contains(&message_socket_id.to_lowercase())
    {
        let now = chrono::Utc::now().timestamp_millis() as f64;
        let mut message_time = midi_message.get_time().parse::<f64>().unwrap_or_default();

        // Handles edge case where client's message time doesn't have
        // current server offset.
        if message_time < now {
            message_time = message_time + current_audio_state.server_time_offset as f64;
        }

        let mut t = message_time - current_audio_state.server_time_offset as f64
            + pianorhythm_shared::GLOBAL_TIME_OFFSET as f64
            - now;

        // log::info!("offset called: {:?} | t: {:?} | {} | {}", &current_audio_state.server_time_offset, &t, message_time, now);
        t = t.abs();

        if t < 0. {
            t = pianorhythm_shared::GLOBAL_TIME_OFFSET as f64;
        }

        for buffer in midi_message
            .get_data()
            .into_iter()
            .filter(|buffer| buffer.data.is_some())
        {
            let delay = buffer.get_delay().min(1000.0);
            let mut ms = (t + delay).max(0.0);
            ms = ms + (ms / 1000.0);

            if ms > 10000. {
                continue;
            }

            let buffer_data = buffer.get_data().to_owned();
            let note_source = NoteSourceType::from_proto_source(buffer_data.get_noteSource());

            // log::info!("{:?} -- ms: {:?} | delay: {:?} | {:?}", &buffer_data.messageType, &ms, &delay, buffer.get_delay());

            let on_emit = move || {
                match buffer_data.messageType {
                    MidiDtoType::NoteOn if buffer_data.has_noteOn() => {
                        let value = buffer_data.get_noteOn();

                        let event = PianoRhythmWebSocketMidiNoteOn {
                            channel: value.get_channel() as u8,
                            note: value.get_note() as u8,
                            velocity: value.get_velocity() as u8,
                            program: Some(value.get_program() as u8),
                            bank: Some(value.get_bank() as u32),
                            volume: Some(value.get_volume() as u8),
                            pan: Some(value.get_pan() as u8),
                            source: Some(note_source.to_u8()),
                            ..Default::default()
                        };

                        pianorhythm_synth::synth_ws_socket_note_on(event, socket_id_hash);
                    }
                    MidiDtoType::NoteOff if buffer_data.has_noteOff() => {
                        let value = buffer_data.get_noteOff();

                        _ = pianorhythm_synth::parse_midi_data(
                            &[
                                pianorhythm_shared::midi::NOTE_OFF_BYTE + value.get_channel() as u8,
                                value.get_note() as u8,
                                0,
                            ],
                            socket_id_hash,
                            Some(note_source.to_u8()),
                            None,
                        );
                    }
                    MidiDtoType::Sustain if buffer_data.has_sustain() => {
                        let value = buffer_data.get_sustain();

                        _ = pianorhythm_synth::parse_midi_data(
                            &[
                                pianorhythm_shared::midi::CONTROLLER_BYTE,
                                64,
                                if value.value { 64 } else { 0 },
                            ],
                            socket_id_hash,
                            Some(note_source.to_u8()),
                            None,
                        );
                    }
                    MidiDtoType::AllSoundOff => {
                        if buffer_data.has_allSoundOff() {
                            _ = pianorhythm_synth::parse_midi_data(
                                &[
                                    pianorhythm_shared::midi::CONTROLLER_BYTE
                                        + buffer_data.get_allSoundOff().get_channel() as u8,
                                    0x78,
                                    0,
                                ],
                                socket_id_hash,
                                Some(note_source.to_u8()),
                                None,
                            );
                        }
                    }
                    MidiDtoType::PitchBend => {
                        if buffer_data.has_pitchBend() {
                            let value = buffer_data.get_pitchBend();

                            _ = pianorhythm_synth::synth_ws_socket_pitch(
                                PianoRhythmWebSocketMidiPitchBend {
                                    channel: value.get_channel() as u8,
                                    value: value.get_value(),
                                },
                                socket_id_hash,
                            );
                        }
                    }
                    _ => {}
                };
            };

            output.push((ms, Box::new(on_emit)));
        }
    }

    Some(output)
}

pub struct TimingContext {
    pub message_received_at: instant::Instant,
    pub message_wall_time: f64,
    pub server_time_offset: f64,
}

pub fn calculate_note_timing_optimized(
    message_time: f64,
    server_time_offset: f64,
    timing_context: &TimingContext,
) -> f64 {
    // Use wall-clock time for server synchronization
    let server_adjusted_time = message_time + server_time_offset;

    // Calculate delay from when message was received (monotonic)
    let processing_delay = timing_context.message_received_at.elapsed().as_millis() as f64;

    // Base delay calculation using wall-clock times
    let base_delay = server_adjusted_time - timing_context.message_wall_time;

    // Subtract processing time to maintain accuracy
    let adjusted_delay = base_delay - processing_delay;

    // Clamp to reasonable bounds (0-5000ms)
    adjusted_delay.max(0.0).min(5000.0)
}

#[derive(Debug, Clone)]
pub struct ScheduledNote {
    pub delay_ms: f64,
    pub note_data: MidiDto,
    pub note_source: NoteSourceType,
    pub socket_id_hash: Option<u32>,
}

#[derive(Debug)]
pub struct BatchedNoteSchedule {
    pub notes: Vec<ScheduledNote>,
    pub base_time: f64,
}

pub fn handle_ws_midi_message_optimized(
    message: &MidiMessageOutputDto,
    state: Rc<AppState>,
) -> Option<BatchedNoteSchedule> {
    let current_audio_state = &state.audio_process_state;

    if current_audio_state.muted_everyone_else {
        return None;
    }

    let midi_message = message.clone();
    let message_socket_id = midi_message.get_socketID();

    if message_socket_id.is_empty() {
        return None;
    }

    let socket_id_hash = hash_socket_id(&message_socket_id);
    //log::info!("socket_id_hash: {:?} | socket_id: {:?}", &socket_id_hash, &message_socket_id);

    #[cfg(feature = "use_synth")]
    if !current_audio_state
        .muted_users
        .contains(&message_socket_id.to_lowercase())
    {
        // Create timing context with monotonic clock
        let timing_context = TimingContext {
            message_received_at: instant::Instant::now(),
            message_wall_time: chrono::Utc::now().timestamp_millis() as f64,
            server_time_offset: current_audio_state.server_time_offset as f64,
        };

        let message_time = midi_message
            .get_time()
            .parse::<f64>()
            .unwrap_or(timing_context.message_wall_time);

        // Use optimized timing calculation with monotonic clock support
        let base_delay = calculate_note_timing_optimized(
            message_time,
            current_audio_state.server_time_offset as f64,
            &timing_context,
        );

        let mut batched_notes = Vec::new();

        for buffer in midi_message
            .get_data()
            .into_iter()
            .filter(|buffer| buffer.data.is_some())
        {
            let note_delay = buffer.get_delay().min(1000.0);
            let total_delay = base_delay + note_delay;

            // Skip notes scheduled too far in the future
            if total_delay > 5000.0 {
                continue;
            }

            let buffer_data = buffer.get_data().to_owned();
            let note_source = NoteSourceType::from_proto_source(buffer_data.get_noteSource());

            batched_notes.push(ScheduledNote {
                delay_ms: total_delay,
                note_data: buffer_data,
                note_source,
                socket_id_hash,
            });
        }

        if !batched_notes.is_empty() {
            return Some(BatchedNoteSchedule {
                notes: batched_notes,
                base_time: timing_context.message_wall_time,
            });
        }
    }

    None
}
