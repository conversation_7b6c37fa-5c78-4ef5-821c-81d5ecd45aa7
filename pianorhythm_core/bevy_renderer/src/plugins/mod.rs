use bevy::prelude::*;
use std::sync::atomic::{AtomicBool, Ordering};

pub mod avatars;
pub mod debug_ui;
pub mod drums;
pub mod games;
pub mod guitars;
pub mod ik;
pub mod piano;
pub mod stages;

#[derive(Default)]
pub struct GameControlPlugin;

pub static SHOULD_EXIT: AtomicBool = AtomicBool::new(false);

impl Plugin for GameControlPlugin {
    fn build(&self, app: &mut App) {
        app.add_systems(Update, exit_system);
    }
}

fn exit_system(mut exit: EventWriter<AppExit>) {
    if SHOULD_EXIT.load(Ordering::SeqCst) {
        log::warn!("Exiting app...");
        exit.write(AppExit::Success);
        SHOULD_EXIT.store(false, Ordering::SeqCst);
    }
}
