use std::time::Duration;

use bevy::animation::{AnimationPlayer, RepeatAnimation};
use bevy::asset::Assets;
use bevy::gltf::Gltf;
use bevy::math::prelude::*;
use bevy::math::{NormedVectorSpace, Vec3};
use bevy::prelude::*;
use bevy::window::PrimaryWindow;
use bevy_descendant_collector::*;
use bevy_sequential_actions::ActionsBundle;
use bevy_tweening::lens::TransformScaleLens;
use bevy_tweening::{Animator, Tween};

use pianorhythm_proto::pianorhythm_actions::AppStateActions_Action;
use pianorhythm_proto::pianorhythm_effects::AppStateEffects_Action;

use crate::components::MainCamera;
use crate::core::events::{AvatarEventsBroadcastAction, ECSWorldAction, ECSWorldActions};
use crate::plugins::avatars::assets::{CharacterAssets, TextureAssets};
use crate::plugins::avatars::components::*;
use crate::plugins::avatars::events;
use crate::plugins::avatars::resources::{AvatarAnimations, AvatarSystems, AvatarSystemsID, AvatarTargetPosition};
#[cfg(feature = "desktop")]
use crate::resources::CursorPositionDesktop;
use crate::resources::{CameraBounds, ClientAppSettings, ClientSocketID, SpawnedAvatarUsers, Users};
use crate::utils::common_traits::StableInterpolate;

const TARGET_WALKING_SPEED: f32 = 1.5;
const COMMON_ANIM_SPEED: f32 = 1.0;

pub(super) fn plugin_setup(
    mut commands: Commands, mut graphs: ResMut<Assets<AnimationGraph>>, mut meshes: ResMut<Assets<Mesh>>,
    mut materials: ResMut<Assets<StandardMaterial>>, assets: Res<CharacterAssets>, assets_gltf: Res<Assets<Gltf>>,
) {
    let Some(gltf) = assets_gltf.get(&assets.base_character) else {
        return;
    };

    // Build the animation graph
    let mut graph = AnimationGraph::new();
    let animations = graph.add_clips(gltf.animations.clone(), 1.0, graph.root).collect();

    let graph = graphs.add(graph);
    commands.insert_resource(AvatarAnimations {
        animations,
        graph: graph.clone(),
    });

    commands.spawn((
        Mesh3d(meshes.add(Extrusion::new(Circle::new(0.3), 0.01))),
        MeshMaterial3d(materials.add(StandardMaterial {
            base_color: Color::WHITE,
            alpha_mode: AlphaMode::Multiply,
            ..default()
        })),
        Transform::from_rotation(Quat::from_rotation_x(-std::f32::consts::FRAC_PI_2)),
        Visibility::Hidden,
        Name::new("Client Avatar Target Mesh"),
        ClientAvatarTargetPositionMesh,
    ));
}

pub(super) fn spawn_avatar(
    mut commands: Commands,
    mut spawned_avatar_users: ResMut<SpawnedAvatarUsers>,
    // mut meshes: ResMut<Assets<Mesh>>,
    texture_assets: Res<TextureAssets>,
    character_assets: Res<CharacterAssets>,
    assets_gltf: Res<Assets<Gltf>>,
    users: Res<Users>,
    settings: Res<ClientAppSettings>, client_socket_id: Res<ClientSocketID>,
) {
    if !settings.0.GRAPHICS_ENABLE_AVATARS {
        return;
    }

    let Some(gltf) = assets_gltf.get(&character_assets.base_character) else {
        return;
    };

    if gltf.scenes.is_empty() {
        warn!("GLTF Scene not found for avatar model.");
        return;
    }

    for (socket_id, dto) in users.0.iter() {
        if spawned_avatar_users.0.contains_key(socket_id) {
            continue;
        }
        let position = dto.get_userDto().get_worldData().get_avatarWorldData().get_worldPosition().clone();

        let mut entity_commands = commands.spawn((
            SceneRoot(gltf.scenes[0].clone()),
            Transform::default()
                .with_translation(Vec3::new(position.x as f32, 0., position.z as f32))
                .with_scale(Vec3::splat(0.3)),
            AvatarModel,
            AvatarRelatedPlugin,
            AvatarTrackingState::default(),
            DescendantCollectorTarget::<BaseCharacterArmature>::default(),
            AvatarSocketID(socket_id.clone()),
            ActionsBundle::new(),
            Name::new(format!("Avatar: {}", dto.get_userDto().get_usertag())),
        ));

        let is_client = client_socket_id.0.eq(socket_id);
        if is_client {
            entity_commands.insert(ClientAvatarModel);
        }

        // TODO
        // entity_commands.with_children(|builder| {
        //     builder
        //         .spawn(BillboardTextBundle {
        //             transform: Transform::from_translation(Vec3::new(0., 3.5, 0.)).with_scale(Vec3::splat(0.003)),
        //             text: Text::from_sections([TextSection {
        //                 value: if is_client {
        //                     "ME".into()
        //                 } else {
        //                     dto.get_userDto().get_usertag().into()
        //                 },
        //                 style: TextStyle {
        //                     font_size: 36.0,
        //                     color: Color::srgb_u8(0, 209, 178),
        //                     ..default()
        //                 },
        //             }])
        //             .with_justify(JustifyText::Center),
        //             ..default()
        //         })
        //         .insert(AvatarNameTag)
        //         .with_children(|builder| {
        //             builder.spawn(BillboardTextureBundle {
        //                 transform: Transform::from_translation(Vec3::default().with_y(-75.)),
        //                 texture: BillboardTextureHandle(texture_assets.cursor_arrow_down.clone()),
        //                 mesh: BillboardMeshHandle(Mesh3d(meshes.add(Rectangle::from_size(Vec2::splat(0.1))))),
        //                 ..default()
        //             });
        //         });
        // });

        spawned_avatar_users.0.insert(socket_id.clone(), entity_commands.id());
    }
}

pub(super) fn set_avatar_animations(
    mut commands: Commands, animations: Res<AvatarAnimations>, players: Query<(Entity, &Name), Added<AnimationPlayer>>, q_parent: Query<&ChildOf>,
    q_client: Query<&AvatarModel>,
) {
    for (entity, name) in &players {
        if name.to_string() != "CharacterArmature" {
            continue;
        };

        let transitions = AnimationTransitions::new();
        let mut is_client = false;

        if let Ok(parent_1) = q_parent.get(entity) {
            if let Ok(parent_2) = q_parent.get(parent_1.get().clone()) {
                is_client = q_client.get(parent_2.get()).is_ok();

                if is_client {
                    // Cache a reference to the parent avatar
                    commands.entity(entity).insert(AvatarReference(parent_2.get()));

                    // Cache a reference of the armature entity at the root
                    commands.entity(parent_2.get()).insert(AvatarArmatureReference(entity.clone()));
                }
            }
        }

        let mut entity_commands = commands.entity(entity);

        entity_commands
            .insert(AvatarTargetAnimation::idle())
            .insert(AvatarArmature)
            .insert(AnimationGraphHandle(animations.graph.clone()))
            .insert(transitions);

        if is_client {
            entity_commands.insert(ClientAvatarArmature);
        }
    }
}

pub(super) fn handle_avatar_target_animation_changed(
    animations: Res<AvatarAnimations>,
    mut players: Query<
        (&AvatarTargetAnimation, &mut AnimationTransitions, &mut AnimationPlayer),
        (Changed<AvatarTargetAnimation>, With<AvatarArmature>),
    >,
) {
    for (target_animation, mut transitions, mut player) in &mut players {
        let Some(animation_node) = animations.animations.get(target_animation.to_node_index()).cloned() else {
            continue;
        };

        let transition_duration = target_animation.transition_duration.unwrap_or(Duration::ZERO);
        let target_speed = target_animation.speed.unwrap_or(1.0);

        let mut animation = player.animation_mut(animation_node);
        if animation.is_none() {
            animation = Some(transitions.play(&mut player, animation_node, transition_duration));
        }

        let Some(animation) = animation else {
            continue;
        };

        animation.set_speed(target_speed);

        if target_animation.repeat {
            animation.repeat();
        } else {
            animation.set_repeat(RepeatAnimation::Never);
        }
    }
}

pub(super) fn handle_avatar_animation_finished(
    mut commands: Commands, mut players: Query<(Entity, &mut AnimationPlayer), (With<AvatarTargetAnimation>, With<AvatarArmature>)>,
) {
    for (entity, mut player) in &mut players {
        if player.all_finished() {
            player.stop_all();

            commands.entity(entity).remove::<AvatarTargetAnimation>();
        }
    }
}

pub(super) fn despawn_all_avatars(mut commands: Commands, mut spawned_avatar_users: ResMut<SpawnedAvatarUsers>) {
    let mut removed_entities: Vec<u32> = vec![];

    for (socket_id, entity) in spawned_avatar_users.0.iter() {
        commands.entity(entity.clone()).despawn();
        removed_entities.push(socket_id.clone());
    }

    for socket_id in removed_entities {
        spawned_avatar_users.0.remove(&socket_id);
    }
}

pub(super) fn despawn_avatar(mut commands: Commands, users: Res<Users>, mut spawned_avatar_users: ResMut<SpawnedAvatarUsers>) {
    let mut removed_entities: Vec<u32> = vec![];

    for (socket_id, entity) in spawned_avatar_users.0.iter() {
        if users.0.contains_key(socket_id) {
            continue;
        }

        commands.entity(entity.clone()).despawn();
        removed_entities.push(socket_id.clone());
    }

    for socket_id in removed_entities {
        spawned_avatar_users.0.remove(&socket_id);
    }
}

pub(super) fn on_disable_avatars(
    mut commands: Commands, client_app_settings: Res<ClientAppSettings>, query: Query<Entity, With<AvatarRelatedPlugin>>,
) {
    if client_app_settings.0.GRAPHICS_ENABLE_AVATARS {
        return;
    }

    for entity in &query {
        commands.entity(entity).insert(Visibility::Hidden);
    }
}

pub(super) fn on_enable_avatars(
    mut commands: Commands, client_app_settings: Res<ClientAppSettings>, query: Query<Entity, With<AvatarRelatedPlugin>>,
) {
    if !client_app_settings.0.GRAPHICS_ENABLE_AVATARS {
        return;
    }

    for entity in &query {
        commands.entity(entity).insert(Visibility::Inherited);
    }
}

pub(super) fn handle_avatar_nametag_visibility(
    mut query: Query<(&GlobalTransform, &mut Visibility), With<AvatarNameTag>>, q_camera: Query<&Transform, (With<MainCamera>, Changed<Transform>)>,
) {
    let Ok(camera_transform) = q_camera.single() else {
        return;
    };

    for (tag_transform, mut visibility) in &mut query {
        let delta = camera_transform.translation - tag_transform.translation();
        let abs_delta = delta.norm();
        *visibility = if abs_delta <= 5. { Visibility::Inherited } else { Visibility::Hidden };
    }
}

pub(super) fn set_client_avatar_target_position(
    mut commands: Commands, mut event_writer: EventWriter<AvatarEventsBroadcastAction>, q_window: Query<&Window, With<PrimaryWindow>>,
    q_camera: Query<(&Camera, &GlobalTransform), With<MainCamera>>, bounds: Res<CameraBounds>,
    #[cfg(feature = "desktop")] desktop_cursor_position: Res<CursorPositionDesktop>,
    mut q_target: Query<(Entity, &mut Transform, &mut Visibility), With<ClientAvatarTargetPositionMesh>>,
) {
    let Ok((camera, camera_transform)) = q_camera.single() else {
        return;
    };

    // There is only one primary window, so we can similarly get it from the query:
    let Ok(window) = q_window.single() else {
        return;
    };

    // check if the cursor is inside the window and get its position
    let Some(cursor_position) = window.cursor_position().or(if cfg!(feature = "desktop") {
        #[cfg(feature = "desktop")]
        {
            desktop_cursor_position.0
        }
        #[cfg(not(feature = "desktop"))]
        {
            None
        }
    } else {
        None
    }) else {
        return;
    };

    let Ok((target_mesh_entity, mut target_mesh_transform, mut target_mesh_visibility)) = q_target.single_mut() else {
        return;
    };

    // Ask Bevy to give us a ray pointing from the viewport (screen) into the world
    let Ok(ray) = camera.viewport_to_world(camera_transform, cursor_position) else {
        return;
    };

    let Some(distance) = ray.intersect_plane(Vec3::ZERO, InfinitePlane3d::new(Vec3::Y)) else {
        return;
    };

    let mut target = ray.origin + ray.direction.normalize() * distance;

    // Restrict within bounds
    target.x = target.x.clamp(bounds.min_x, bounds.max_x);
    target.z = target.z.clamp(bounds.min_z, bounds.max_z);

    target_mesh_transform.translation = Vec3::new(target.x, 0., target.z);
    *target_mesh_visibility = Visibility::Visible;

    commands
        .entity(target_mesh_entity)
        .remove::<AvatarSittingOnSeat>()
        .insert(Animator::new(Tween::new(
            EaseFunction::BounceInOut,
            std::time::Duration::from_millis(200),
            TransformScaleLens {
                start: Vec3::splat(0.1),
                end: Vec3::splat(1.),
            },
        )));

    commands.insert_resource(AvatarTargetPosition(target.clone()));
    commands.remove_resource::<AvatarPianoSeatMarker>();

    event_writer.send(crate::core::events::AvatarEventsBroadcastAction::SetPosition {
        x: target.x,
        y: target.y,
        z: target.z,
    });
}

/// Updates the avatar's position and animation based on the target position.
pub(super) fn track_avatar_position(
    mut commands: Commands, mut event_writer: EventWriter<events::OnAvatarReachedTarget>, time: Res<Time>,
    client_avatar_target_position_res: Option<Res<AvatarTargetPosition>>,
    mut q_armature_reference: Query<
        (Entity, &AvatarSocketID, &AvatarArmatureReference, Option<&AvatarTargetPosition>, &mut AvatarTrackingState),
        With<AvatarModel>,
    >,
    q_client: Query<(Entity, &GlobalTransform, Option<&AvatarTargetAnimation>), With<AvatarArmature>>, client_socket_id: Res<ClientSocketID>,
) {
    let has_client_target = client_avatar_target_position_res.is_some();
    let client_avatar_target_position = if has_client_target {
        Some(client_avatar_target_position_res.unwrap().0)
    } else {
        None
    };

    for (avatar_entity, avatar_socket_id, reference, avatar_target_position, mut state) in q_armature_reference.iter_mut() {
        for (armature_entity, avatar_transform, avatar_animation) in q_client.iter() {
            if armature_entity != reference.0 {
                continue;
            }

            let Some(target_pos) = (match true {
                _ if avatar_socket_id.0 == client_socket_id.0 => client_avatar_target_position,
                _ => avatar_target_position.map(|x| x.0),
            }) else {
                continue;
            };

            let current_animation = avatar_animation.cloned().unwrap_or(AvatarTargetAnimation::default());
            let avatar_translation = avatar_transform.translation();

            let mut emit_target_reached = || {
                event_writer.send(events::OnAvatarReachedTarget(avatar_entity.clone()));
            };

            match Dir3::new(target_pos - avatar_translation) {
                Ok(_) => {
                    let delta = target_pos - avatar_translation;
                    let abs_delta = delta.norm();

                    if abs_delta <= 0.1 {
                        state.set_target_reached(&mut commands, armature_entity);
                        emit_target_reached();
                        return;
                    }

                    let velocity = (avatar_translation - state.previous_translation) / time.delta_secs();
                    let speed = velocity.length();

                    let (animation, target_speed) = if speed <= TARGET_WALKING_SPEED {
                        (AvatarAnimationID::WALK, speed.remap(TARGET_WALKING_SPEED, 0.1, COMMON_ANIM_SPEED, 0.8))
                    } else {
                        (AvatarAnimationID::RUN, speed.remap(10., TARGET_WALKING_SPEED, 2.0, COMMON_ANIM_SPEED))
                    };

                    if current_animation.animation != animation || current_animation.speed != Some(target_speed) {
                        commands.entity(armature_entity).insert(AvatarTargetAnimation {
                            animation,
                            speed: Some(target_speed),
                            transition_duration: Some(Duration::from_millis(500)),
                            ..default()
                        });
                    }

                    state.update(avatar_translation);
                }
                _ => {
                    state.set_target_reached(&mut commands, armature_entity);
                    emit_target_reached();
                }
            }
        }
    }
}

pub(super) fn move_avatar_to_position(
    client_avatar_target_position_res: Option<Res<AvatarTargetPosition>>, time: Res<Time>,
    mut q_avatars: Query<(&AvatarSocketID, &mut Transform, &mut AvatarTrackingState, Option<&AvatarTargetPosition>), With<AvatarModel>>,
    client_socket_id: Res<ClientSocketID>,
) {
    let has_client_target = client_avatar_target_position_res.is_some();
    let client_avatar_target_position = if has_client_target {
        Some(client_avatar_target_position_res.unwrap().0)
    } else {
        None
    };

    for (avatar_socket_id, mut client_transform, mut tracking_state, avatar_target_position) in q_avatars.iter_mut() {
        let Some(target_pos) = (match true {
            _ if avatar_socket_id.0 == client_socket_id.0 => client_avatar_target_position,
            _ => avatar_target_position.map(|x| x.0),
        }) else {
            continue;
        };

        let coords = target_pos;
        let decay_rate = smoothstep(0.525); //f32::ln(3.0);

        let translation = client_transform.translation;
        let target_direction = (translation - coords).normalize();
        let current_forward = client_transform.forward();

        // Make sure avatar is on the ground
        if client_transform.translation.y > 0. {
            client_transform.translation.y = 0.
        }

        if translation.distance(coords) < 0.1 || tracking_state.last_position != coords {
            tracking_state.lerp_v = 0.0;
        } else {
            tracking_state.lerp_v = (tracking_state.lerp_v + 1.0 * time.delta_secs()).clamp(0.0, 1.0);
        }

        let direction = current_forward.lerp(target_direction, tracking_state.lerp_v);

        client_transform.look_to(direction, Vec3::Y);

        client_transform.translation.smooth_nudge(&coords, decay_rate, time.delta_secs());

        tracking_state.last_position = coords;
    }
}

pub(super) fn hide_client_avatar_target_position_mesh(mut q_target: Query<&mut Visibility, With<ClientAvatarTargetPositionMesh>>) {
    let Ok(mut target_visibility) = q_target.single_mut() else {
        return;
    };
    *target_visibility = Visibility::Hidden;
}

pub(super) fn receiving_app_actions(
    mut commands: Commands, mut event_reader: EventReader<ECSWorldActions>, systems: Res<AvatarSystems>,
    q_avatar: Query<(Entity, &AvatarSocketID), With<AvatarModel>>,
) {
    for my_event in event_reader.read() {
        match &my_event.message {
            ECSWorldAction::AppAction(action) => match action.action {
                AppStateActions_Action::Logout => {
                    commands.run_system(systems.0[&AvatarSystemsID::DespawnAllAvatars]);
                }
                _ => {}
            },
            ECSWorldAction::AppEffect(effect) => match effect.action {
                AppStateEffects_Action::UpdateUser if effect.has_clientSideUserDto() => {
                    let user = effect.get_clientSideUserDto();

                    for (entity, avatar_socket_id) in q_avatar.iter() {
                        if avatar_socket_id.0 != user.get_socketIDHashed() {
                            continue;
                        }

                        let world_data = user.get_userDto().get_worldData();
                        let avatar_data = world_data.get_avatarWorldData();
                        let position = avatar_data.get_worldPosition();

                        #[cfg(debug_assertions)]
                        log::info!("Update user has_avatarWorldPosition {} | {} | {:?}", user.get_socketID(), effect.get_sourceSocketID(), position);

                        commands
                            .entity(entity)
                            .insert(AvatarTargetPosition(Vec3::new(position.x as f32, position.y as f32, position.z as f32)));
                    }
                }
                _ => {}
            },
            _ => {}
        }
    }
}

fn calculate_velocity(r1: Vec3, r2: Vec3, dt: f32) -> (Vec3, f32) {
    let displacement = r2 - r1;
    let velocity = displacement / dt;
    let speed = velocity.length();
    (velocity, speed)
}

fn smoothstep(x: f32) -> f32 {
    x * x * (3.0 - 2.0 * x)
}

fn ease_in_out_quadratic(x: f32) -> f32 {
    if x < 0.5 {
        2.0 * x * x
    } else {
        1.0 - 2.0 * (1.0 - x) * (1.0 - x)
    }
}

pub(super) mod conditions {
    use super::*;

    pub fn run_avatars_enabled(client_app_settings: Res<ClientAppSettings>) -> bool {
        client_app_settings.0.GRAPHICS_ENABLE_AVATARS
    }
}
