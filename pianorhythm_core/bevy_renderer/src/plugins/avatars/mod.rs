use bevy::animation::animate_targets;
use bevy::input::common_conditions::{input_just_pressed, input_pressed};
use bevy::prelude::*;
use bevy_asset_loader::prelude::{StandardDynamicAssetCollection, *};
use bevy_descendant_collector::*;

use crate::plugins::avatars::assets::{CharacterAssets, TextureAssets};
use crate::plugins::avatars::components::BaseCharacterArmature;
use crate::plugins::avatars::resources::{AvatarAnimations, AvatarSystems};
use crate::resources::{ClientAppSettings, SpawnedAvatarUsers, Users};
use crate::{plugins, utils};

mod assets;
mod avatar_play_piano;
mod components;
mod events;
mod resources;
mod systems;

#[derive(Default)]
pub struct AvatarsPlugin;

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Eq, PartialEq, Hash, States, Reflect)]
pub enum AvatarPluginState {
    #[default]
    AssetLoading,
    Ready,
}

impl Plugin for AvatarsPlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<AvatarPluginState>()
            .init_resource::<AvatarSystems>()
            .init_resource::<SpawnedAvatarUsers>()
            .register_type::<resources::AvatarTargetPosition>()
            .register_type::<components::AvatarTargetAnimation>()
            .register_type::<components::AvatarPianoSeatMarker>()
            .register_type::<components::AvatarSittingOnSeat>()
            .register_type::<components::AvatarArmatureReference>()
            .register_type::<components::AvatarReference>()
            .register_type::<components::AvatarSocketID>()
            .register_type::<components::AvatarAnimationID>()
            .register_type::<components::BaseCharacterArmature>()
            .add_event::<events::OnAvatarReachedTarget>()
            .add_loading_state(
                LoadingState::new(AvatarPluginState::AssetLoading)
                    .continue_to_state(AvatarPluginState::Ready)
                    .with_dynamic_assets_file::<StandardDynamicAssetCollection>(utils::get_model_assets_file_path())
                    .load_collection::<TextureAssets>()
                    .load_collection::<CharacterAssets>(),
            );

        app
            .add_plugins(plugins::ik::InverseKinematicsPlugin)
            .add_plugins(avatar_play_piano::AvatarPlayPianoPlugin)
            .add_plugins(DescendantCollectorPlugin::<BaseCharacterArmature>::new(HierarchyRootPosition::Scene));

        app.add_systems(OnEnter(AvatarPluginState::Ready), |mut assets_loaded: ResMut<crate::resources::AppAssetsLoaded>| {
            assets_loaded.avatar_models = true;
        })
        .add_systems(OnEnter(AvatarPluginState::Ready), systems::plugin_setup)
        .add_systems(PreUpdate, systems::receiving_app_actions)
        .add_systems(
            Update,
            (systems::on_enable_avatars, systems::on_disable_avatars)
                .run_if(resource_exists_and_changed::<ClientAppSettings>)
                .run_if(in_state(AvatarPluginState::Ready)),
        )
        .add_systems(
            Update,
            (
                systems::handle_avatar_target_animation_changed,
                systems::handle_avatar_animation_finished,
                systems::handle_avatar_nametag_visibility,
                systems::spawn_avatar.run_if(resource_changed::<Users>),
                systems::despawn_avatar.run_if(resource_changed::<Users>),
                systems::set_avatar_animations
                    .before(animate_targets)
                    .run_if(resource_exists::<AvatarAnimations>),
                systems::set_client_avatar_target_position
                    .run_if(input_pressed(KeyCode::ShiftLeft))
                    .run_if(input_just_pressed(MouseButton::Left)),
                systems::hide_client_avatar_target_position_mesh.run_if(resource_removed::<resources::AvatarTargetPosition>),
                systems::track_avatar_position,
                systems::move_avatar_to_position.after(systems::track_avatar_position),
            )
                .run_if(systems::conditions::run_avatars_enabled)
                .run_if(in_state(AvatarPluginState::Ready)),
        );

        log::info!("Built {:?}", self.name());
    }

    fn finish(&self, _app: &mut App) {
        log::info!("{:?} finished.", &self.name());
    }
}
