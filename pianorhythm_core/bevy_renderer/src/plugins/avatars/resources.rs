use bevy::ecs::system::SystemId;
use bevy::platform::collections::HashMap;
use bevy::prelude::*;

use crate::plugins::avatars::systems;

#[derive(Resource)]
pub struct AvatarAnimations {
    pub animations: Vec<AnimationNodeIndex>,
    #[allow(dead_code)]
    pub graph: Handle<AnimationGraph>,
}

#[derive(Reflect, Debug, Component, Resource, Clone, Default)]
#[reflect(Resource)]
pub struct AvatarTargetPosition(pub Vec3);

#[derive(Co<PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub enum AvatarSystemsID {
    DespawnAllAvatars,
}

#[derive(Resource)]
pub struct AvatarSystems(pub HashMap<AvatarSystemsID, SystemId>);

impl FromWorld for AvatarSystems {
    fn from_world(world: &mut World) -> Self {
        let mut system = AvatarSystems(HashMap::new());

        system
            .0
            .insert(AvatarSystemsID::DespawnAllAvatars, world.register_system(systems::despawn_all_avatars));

        system
    }
}
