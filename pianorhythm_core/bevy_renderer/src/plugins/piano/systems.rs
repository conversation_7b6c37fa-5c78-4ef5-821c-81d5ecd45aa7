use std::time::Duration;

use bevy::prelude::*;
use bevy_tweening::lens::TransformRotationLens;
use bevy_tweening::{Animator, AssetAnimator, Tween, TweenCompleted};
use hex_color::HexColor;

use pianorhythm_proto::midi_renditions::{ActiveChannelsMode as MidiActiveChannelsMode, MidiNoteSource};
use pianorhythm_proto::pianorhythm_app_renditions::AppPianoKeyType;
use pianorhythm_shared::util;

use crate::components::tween_lens::StandardMaterialBaseColorLens;
use crate::components::{
    AllSoundOffTriggered, AnimationDown, AnimationState, BaseMeshColor, MeshDisabled, OnActiveNote, PianoMeshType, SplitModeMaterialColor
};
use crate::core::events::{SynthEventsBroadcastAction, UserNoteData};
use crate::plugins::piano::resources::LastMouseNoteOn;
use crate::plugins::piano::{queries, PianoPluginSystemID, PianoPluginSystems};
use crate::resources::{DisplayKeysInputMapping, SynthAudioChannelsActiveState};
use crate::{components, resources};

const MESH_ACTIVE_COLOR_TWEEN_DUR: u64 = 25;
const MESH_INACTIVE_COLOR_TWEEN_DUR: u64 = 150;

// Improved animation durations - faster for high velocity
const MESH_ACTIVE_ROTX_TWEEN_DUR_MIN: u64 = 40; // For highest velocity (127)
const MESH_ACTIVE_ROTX_TWEEN_DUR_MAX: u64 = 80; // For lowest velocity (1)
const MESH_INACTIVE_ROTX_TWEEN_DUR: u64 = 100;

/// System that is responsible for adding a [AnimationDown] timer
/// when the mesh is in a down state.
pub(super) fn check_animation_state_is_down(
    mut commands: Commands,
    query: Query<
        (Entity, &Visibility, &AnimationState),
        (With<PianoMeshType>, Without<AnimationDown>, Without<AllSoundOffTriggered>, Changed<AnimationState>),
    >,
    app_settings: Res<resources::ClientAppSettings>, systems: Res<PianoPluginSystems>,
) {
    if !app_settings.0.GRAPHICS_ENABLE_ANIMATIONS {
        return;
    }

    for (entity, visible, _) in query.iter() {
        if visible == Visibility::Hidden {
            continue;
        }

        commands.run_system_with(systems.0[&PianoPluginSystemID::TriggerKeyDown], entity);
    }
}

/// System that is responsible for keep tracking of the elapsed
/// time for a mesh from their [AnimationDown] component and triggering
/// the up state.
pub(super) fn check_animation_state_is_up(
    mut commands: Commands, query: Query<(Entity, &AnimationState), (With<PianoMeshType>, Changed<AnimationState>)>, systems: Res<PianoPluginSystems>,
) {
    for (entity, state) in query.iter() {
        if state.is_down {
            continue;
        }
        commands.run_system_with(systems.0[&PianoPluginSystemID::TriggerKeyUp], entity);
    }
}

/// Tracks the timing for piano key down animations.
/// When the animation timer completes, sets the key state to up.
pub(super) fn mesh_animation_down_timer(mut query: Query<(&mut AnimationDown, &mut AnimationState), With<PianoMeshType>>, time: Res<Time>) {
    for (mut animation_down, mut state) in query.iter_mut() {
        animation_down.start_time.tick(time.delta());

        if animation_down.start_time.finished() {
            state.is_down = false;
        }
    }
}

/// Handles mouse button release events by sending note-off MIDI messages.
/// Clears the last mouse note on resource after sending the event.
pub(super) fn on_mouse_up(mut commands: Commands, last_mouse_note_on: Res<LastMouseNoteOn>, mut ev: EventWriter<SynthEventsBroadcastAction>) {
    if let Some(note) = last_mouse_note_on.0 {
        ev.write(SynthEventsBroadcastAction::NoteOff(UserNoteData {
            socket_id: None,
            channel: 0,
            note: note,
            source: MidiNoteSource::MOUSE,
            ..default()
        }));

        commands.insert_resource(LastMouseNoteOn::default());
    }
}

/// System that is responsible for removing the [Animator] or [AssetAnimator]
/// tween component when the animation is completed.
pub(super) fn remove_animation_tween_on_complete(mut commands: Commands, mut reader: EventReader<TweenCompleted>) {
    for ev in reader.read() {
        if ev.user_data == 0 {
            commands.entity(ev.entity).remove::<Animator<Transform>>();
        }

        if ev.user_data == 1 {
            commands.entity(ev.entity).remove::<AssetAnimator<StandardMaterial>>();
        }
    }
}

/// Sets up animation components for a key press (down state).
/// Creates rotation and color tweens based on user colors and note velocity.
/// Also adds a timer that will automatically trigger the key release after a duration.
pub(super) fn set_animation_down_component(
    In(entity): In<Entity>, mut commands: Commands, query: Query<queries::PianoKeyTriggerDownQuery>, users: Res<resources::UserColors>,
    materials: Res<Assets<StandardMaterial>>, client_socket_id: Res<resources::ClientSocketID>,
) {
    let Ok(key) = query.get(entity.clone()) else {
        return;
    };
    let Some(material) = materials.get(key.material_handle) else {
        return;
    };

    let is_mouse = key.active_info.0.source == MidiNoteSource::MOUSE;

    let Some(socket_id) = ({
        match key.active_info.0.socket_id {
            Some(socket_id) => Some(socket_id),
            None if is_mouse => Some(client_socket_id.0),
            _ => None,
        }
    }) else {
        return;
    };

    let mut target_color = users.get_color(&socket_id).unwrap_or(Color::BLACK);

    // Keep the alpha the same (covers mesh disabled)
    target_color.set_alpha(material.base_color.alpha());

    let velocity = key.active_info.0.vel;

    // Determine rotation based on input velocity
    let rotation_angle = calculate_key_rotation(velocity);

    // Dynamic animation duration based on velocity (faster for harder hits)
    let duration_ms = calculate_animation_duration(velocity);

    // Set the animators
    commands
        .entity(entity)
        .insert(Animator::new(Tween::new(
            bevy_tweening::EaseMethod::EaseFunction(EaseFunction::CubicIn),
            std::time::Duration::from_millis(duration_ms),
            TransformRotationLens {
                start: Quat::from_axis_angle(Vec3::X, 0f32.to_radians()),
                end: Quat::from_axis_angle(Vec3::X, rotation_angle.to_radians()),
            },
        )))
        .insert(AssetAnimator::new(Tween::new(
            bevy_tweening::EaseMethod::EaseFunction(EaseFunction::QuarticIn),
            std::time::Duration::from_millis(MESH_ACTIVE_COLOR_TWEEN_DUR),
            StandardMaterialBaseColorLens {
                start: material.base_color,
                end: target_color,
            },
        )))
        .insert(AnimationDown {
            start_time: Timer::new(
                Duration::from_secs(if is_mouse {
                    components::consts::MAX_KEYDOWN_ANIMATION_FOR_MOUSE
                } else {
                    components::consts::MAX_KEYDOWN_ANIMATION
                }),
                TimerMode::Once,
            ),
        });
}

/// Sets up animation components for a key release (up state).
/// Creates rotation and color tweens to return the key to its resting position and color.
/// Removes animation down and active note components.
pub(super) fn set_animation_up_component(
    In(entity): In<Entity>, mut commands: Commands, query: Query<queries::PianoKeyTriggerUpQuery>, materials: Res<Assets<StandardMaterial>>,
) {
    let Ok(key) = query.get(entity.clone()) else {
        return;
    };
    let Some(material) = materials.get(key.material_handle) else {
        return;
    };

    let mut entity_commands = commands.entity(entity);

    entity_commands
        .remove::<AnimationDown>()
        .remove::<OnActiveNote>()
        .insert(Animator::new(
            Tween::new(
                EaseFunction::BounceInOut,
                std::time::Duration::from_millis(MESH_INACTIVE_ROTX_TWEEN_DUR),
                TransformRotationLens {
                    start: Quat::from_rotation_x(key.transform.rotation.x),
                    end: Quat::from_axis_angle(Vec3::X, 0_f32.to_radians()),
                },
            )
            .with_completed_event(0),
        ))
        .insert(AssetAnimator::new(
            Tween::new(
                bevy_tweening::EaseMethod::EaseFunction(EaseFunction::Linear),
                std::time::Duration::from_millis(MESH_INACTIVE_COLOR_TWEEN_DUR),
                StandardMaterialBaseColorLens {
                    start: material.base_color,
                    end: key.base_mesh_color.0.with_alpha(material.base_color.alpha()),
                },
            )
            .with_completed_event(1),
        ));
}

/// Handles the "all notes off" MIDI command by setting all affected keys to up state.
/// Runs when AllSoundOffTriggered component is added to piano key entities.
pub(super) fn mesh_on_all_notes_off(
    mut commands: Commands, mut query: Query<(Entity, &mut AnimationState), (With<PianoMeshType>, Added<components::AllSoundOffTriggered>)>,
) {
    for (entity, mut state) in query.iter_mut() {
        state.is_down = false;
        commands.entity(entity).remove::<components::AllSoundOffTriggered>();
    }
}

/// Animates color transitions when a key's BaseMeshColor changes.
/// Creates a tween to smoothly transition from current material color to new base color.
pub(super) fn on_base_mesh_color_changed(
    mut commands: Commands, query: Query<queries::PianoKeyMaterialQuery, (Changed<BaseMeshColor>, With<PianoMeshType>)>,
    materials: Res<Assets<StandardMaterial>>,
) {
    for key in query.iter() {
        let Some(material) = materials.get(key.material_handle) else {
            continue;
        };

        commands.entity(key.entity).insert(AssetAnimator::new(
            Tween::new(
                bevy_tweening::EaseMethod::EaseFunction(EaseFunction::Linear),
                std::time::Duration::from_millis(250),
                StandardMaterialBaseColorLens {
                    start: material.base_color,
                    end: key.base_mesh_color.0,
                },
            )
            .with_completed_event(1),
        ));
    }
}

/// Applies transparency effect to keys when they are disabled.
/// Changes material alpha mode and reduces opacity.
pub(super) fn on_add_mesh_disabled(
    query: Query<queries::PianoKeyMaterialQuery, (Added<MeshDisabled>, With<PianoMeshType>)>, mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for key in query.iter() {
        let Some(material) = materials.get_mut(key.material_handle) else {
            continue;
        };
        material.alpha_mode = AlphaMode::Add;
        material.base_color.set_alpha(0.1);
    }
}

/// Restores normal opacity to keys when they are re-enabled.
/// Changes material alpha mode to opaque and sets full opacity.
pub(super) fn on_remove_mesh_disabled(
    mut removals: RemovedComponents<MeshDisabled>, query: Query<queries::PianoKeyMaterialQuery, With<PianoMeshType>>,
    mut materials: ResMut<Assets<StandardMaterial>>,
) {
    for entity in removals.read() {
        let Ok(key) = query.get(entity) else {
            continue;
        };
        let Some(material) = materials.get_mut(key.material_handle) else {
            continue;
        };
        material.alpha_mode = AlphaMode::Opaque;
        material.base_color.set_alpha(1.0);
    }
}

/// Updates key colors and visibility based on MIDI channel modes.
/// Handles different split modes (2, 4, 8 channels) and assigns appropriate colors.
/// Disables keys for inactive channels.
pub(super) fn on_active_channels_mode_change(
    mut commands: Commands, query: Query<queries::PianoKeyMaterialQuery>, channels: Res<resources::SynthAudioChannelsActiveState>,
    mode: Res<resources::ChannelsMode>,
) {
    let vals: Vec<u8> = (pianorhythm_shared::midi::MIN_NOTE..=pianorhythm_shared::midi::MAX_NOTE).collect();

    for key in query.iter() {
        let midi_id = key.midi_id.0;

        match mode.0 {
            MidiActiveChannelsMode::ALL | MidiActiveChannelsMode::SINGLE | MidiActiveChannelsMode::MULTI => {
                let mut entity_commands = commands.entity(key.entity);

                entity_commands
                    .remove::<components::SplitModeMaterialColor>()
                    .remove::<components::SplitModeChannel>();

                if !is_channel_active(&channels, None) {
                    entity_commands.insert(components::MeshDisabled);
                } else {
                    entity_commands.remove::<components::MeshDisabled>();
                }

                entity_commands.insert(BaseMeshColor(key.default_mesh_color.0.clone()));
            }
            MidiActiveChannelsMode::SPLIT2 | MidiActiveChannelsMode::SPLIT4 | MidiActiveChannelsMode::SPLIT8 => {
                let split_size = match mode.0 {
                    MidiActiveChannelsMode::SPLIT2 => pianorhythm_shared::midi::SLOT_MODE_SPLIT2_MAX_CHANNEL,
                    MidiActiveChannelsMode::SPLIT4 => pianorhythm_shared::midi::SLOT_MODE_SPLIT4_MAX_CHANNEL,
                    MidiActiveChannelsMode::SPLIT8 => pianorhythm_shared::midi::SLOT_MODE_SPLIT8_MAX_CHANNEL,
                    _ => pianorhythm_shared::midi::SLOT_MODE_SINGLE_MAX_CHANNEL,
                };

                let target_chunk_size = vals.len() as u8 / split_size;
                let chunks: Vec<Vec<u8>> = vals.chunks(target_chunk_size.into()).map(|s| s.into()).collect();
                let channel = chunks.iter().position(|r| r.contains(&midi_id)).unwrap_or_default() as u8;
                let hex_color = HexColor::parse(&pianorhythm_shared::util::map_midi_channel_to_color(channel)).unwrap_or_default();

                let color = SplitModeMaterialColor(Color::srgba(
                    hex_color.r as f32 / u8::MAX as f32,
                    hex_color.g as f32 / u8::MAX as f32,
                    hex_color.b as f32 / u8::MAX as f32,
                    1.0,
                ));

                let mut entity_commands = commands.entity(key.entity);

                entity_commands
                    .insert(BaseMeshColor(color.0.clone()))
                    .insert(color)
                    .insert(components::SplitModeChannel(channel));

                if !is_channel_active(&channels, Some(channel)) {
                    entity_commands.insert(components::MeshDisabled);
                } else {
                    entity_commands.remove::<components::MeshDisabled>();
                }
            }
        }
    }
}

/// Helper function to check if a MIDI channel is active.
/// When channel is None, checks if any channel is active.
/// When channel is specified, checks if that particular channel is active.
fn is_channel_active(channels: &SynthAudioChannelsActiveState, channel: Option<u8>) -> bool {
    if let Some(target) = channel {
        channels.0.get(&(target as u32)).cloned().unwrap_or(false)
    } else {
        channels.0.values().any(|x| *x)
    }
}

/// Updates key visibility based on changes to channel activity.
/// Disables keys for inactive channels and enables keys for active channels.
pub(super) fn on_synth_audio_channel_active_change(
    mut commands: Commands, query: Query<queries::PianoKeyMaterialSplitModeQuery>, channels: Res<resources::SynthAudioChannelsActiveState>,
) {
    for item in query.iter() {
        let mut entity_commands = commands.entity(item.key.entity);

        if !is_channel_active(&channels, item.split_mode_channel.map(|x| x.0).clone()) {
            entity_commands.insert(components::MeshDisabled);
        } else {
            entity_commands.remove::<components::MeshDisabled>();
        }
    }
}

/// Resets all piano keys to up state when a new stage is loaded.
/// Adds AllSoundOffTriggered component to all keys to force animation to stop.
pub(super) fn on_stage_loaded(mut commands: Commands, query: Query<Entity, (With<PianoMeshType>, Without<components::AllSoundOffTriggered>)>) {
    for entity in query.iter() {
        commands.entity(entity).insert(components::AllSoundOffTriggered);
    }
}

/// Displays keyboard input mappings on piano keys when visualization is enabled.
/// Creates text billboards above keys to show which keyboard key is mapped to each piano key.
pub fn on_key_input_mapping_visualize(
    mut commands: Commands, display_mappings: Res<DisplayKeysInputMapping>, mapping: Res<resources::KeysInputMapping>,
    query: Query<queries::PianoKeyInputMappingQuery, With<PianoMeshType>>,
) {
    for key in query.iter() {
        let mut entity_commands = commands.entity(key.entity.clone());
        entity_commands.despawn();

        if !display_mappings.0 {
            continue;
        }

        let Some(mapping) = mapping.0.get_mappings().iter().find(|x| x.note == key.midi_id.0 as u32) else {
            continue;
        };

        entity_commands.with_children(|builder| {
            let z_pos = if key.key_type.0 == AppPianoKeyType::White { 0.2 } else { 0.13 };

            // TODO
            // builder
            //     .spawn(KeyInputMappingBillboard)
            //     .insert(key.midi_id.clone())
            //     .insert(BillboardTextBundle {
            //         transform: Transform::from_translation(Vec3::new(0., 0.05, z_pos))
            //             .with_scale(Vec3::splat(0.0009)),
            //         text: Text::from_sections([
            //             TextSection {
            //                 value: mapping.key.clone(),
            //                 style: TextStyle {
            //                     font_size: 36.0,
            //                     color: Color::srgb_u8(0, 209, 178),
            //                     ..default()
            //                 },
            //             }])
            //             .with_justify(JustifyText::Center),
            //         ..default()
            //     });
        });
    }
}

/// Calculate the appropriate animation duration based on note velocity
fn calculate_animation_duration(velocity: u8) -> u64 {
    util::map_velocity(velocity as f32, 0., 127., MESH_ACTIVE_ROTX_TWEEN_DUR_MAX as f32, MESH_ACTIVE_ROTX_TWEEN_DUR_MIN as f32) as u64
}

/// Calculate the appropriate key rotation angle based on note velocity
fn calculate_key_rotation(velocity: u8) -> f32 {
    util::map_velocity(velocity as f32, 0., 127., 2.8, 5.2)
}

#[cfg(test)]
mod tests {
    // #[test]
    // pub fn test_set_animation_down_component() {
    //     let mut commands = Commands::default();
    //     let entity = Entity::new(0);
    //     let material = StandardMaterial::default();
    //     let active_info = OnActiveNote(components::ActiveNote {
    //         vel: 127,
    //         socket_id: Some(0),
    //         source: MidiNoteSource::MOUSE,
    //     });
    //     let users = Res::new(resources::UserColors::default());
    //     let client_socket_id = Res::new(resources::ClientSocketID(0));
    //
    //     set_animation_down_component(&mut commands, entity, &material, &active_info, &users, client_socket_id.0);
    //
    //     let animator = commands.entity(entity).get_component::<Animator<Transform>>();
    //     let asset_animator = commands.entity(entity).get_component::<AssetAnimator<StandardMaterial>>();
    //     let animation_down = commands.entity(entity).get_component::<AnimationDown>();
    //
    //     assert!(animator.is_some());
    //     assert!(asset_animator.is_some());
    //     assert!(animation_down.is_some());
    // }
    //
    // #[test]
    // pub fn test_mesh_on_all_notes_off() {
    //     let mut commands = Commands::default();
    //     let entity = Entity::new(0);
    //     let mut state = AnimationState { is_down: true };
    //
    //     commands.entity(entity).insert(state);
    //
    //     let query = Query::new();
    //     let all_sound_off_triggered = components::AllSoundOffTriggered;
    //
    //     mesh_on_all_notes_off(commands, query.with_component(entity).with_component(state).with_added(all_sound_off_triggered));
    //
    //     let state = commands.entity(entity).get_component::<AnimationState>();
    //
    //     assert!(state.is_some());
    //     assert_eq!(state.unwrap().is_down, false);
    // }
}
