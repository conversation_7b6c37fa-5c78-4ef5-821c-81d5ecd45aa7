use std::time::Duration;

use bevy::diagnostic::FrameTimeDiagnosticsPlugin;
use bevy::prelude::*;
use bevy::time::common_conditions::on_timer;
use bevy_hanabi::prelude::*;
use bevy_tweening::{asset_animator_system, AnimationSystem, TweenCompleted};
use crossbeam_channel::{unbounded, Receiver, Sender};
use rustc_hash::FxHashMap;
use smooth_bevy_cameras::controllers::orbit::OrbitCameraPlugin;
use smooth_bevy_cameras::{LookTransform, LookTransformPlugin, Smoother};

use pianorhythm_proto::pianorhythm_app_renditions::AppSettings;
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects_Action, AppStateEffects_LoadRoomStageDetails};
use pianorhythm_proto::user_renditions::UserClientDto;

use crate::actions::animate_look_transform::CameraAnimating;
use crate::core::events::{
    AppExtensions, AvatarEventsBroadcastAction, ECSSynthEvents, ECSSynthEventsAction, ECSWorldActions, ECSWorldEffects, ECSWorldEvents, SynthEventsBroadcastAction
};
use crate::resources::{CameraBounds, DOFAppSettings};
use crate::{components, plugins, resources};

#[cfg(feature = "desktop")]
use crate::resources::CursorPositionDesktop;

pub mod events;
pub mod process_synth_event;
mod systems;
#[cfg(target_arch = "wasm32")]
mod web_listen_core_events;
#[cfg(target_arch = "wasm32")]
mod web_listen_synth_events;

#[derive(Debug, Clone, Copy, Default, Eq, PartialEq, Hash, States, Reflect)]
pub enum AppState {
    #[default]
    AppLoading,
    AppAssetsLoaded,
    RoomLoading,
    InGame,
}

pub struct CorePlugin {
    tx_world_actions: Sender<ECSWorldActions>,
    rx_world_actions: Receiver<ECSWorldActions>,
    tx_synth_events: Sender<ECSSynthEventsAction>,
    rx_synth_events: Receiver<ECSSynthEventsAction>,
}

impl Default for CorePlugin {
    fn default() -> Self {
        let (tx_world_actions, rx_world_actions) = unbounded::<ECSWorldActions>();
        let (tx_synth_events, rx_synth_events) = unbounded::<ECSSynthEventsAction>();

        Self {
            tx_world_actions,
            rx_world_actions,
            tx_synth_events,
            rx_synth_events,
        }
    }
}

impl CorePlugin {
    #[cfg(target_arch = "wasm32")]
    pub fn setup_web_events(&self,
        synth_events_shared_buffer: Option<js_sys::SharedArrayBuffer>,
    ) {
        // Core and synth events is handled in the bevy_tao mod
        log::info!("Setting up web event listeners...");
        let synth_events_sender = self.tx_synth_events.clone();
        let core_events_sender = self.tx_world_actions.clone();

        crate::core::web_listen_core_events::execute(core_events_sender);
        crate::core::web_listen_synth_events::execute(synth_events_sender.clone());

        if let Some(shared_buffer) = synth_events_shared_buffer.clone() {
            crate::core::web_listen_synth_events::execute_with_shared_buffer(synth_events_sender.clone(), shared_buffer);
        } else {
            log::error!("Failed to get shared buffer for synth events.");
        }
    }
}

impl Plugin for CorePlugin {
    fn build(&self, app: &mut App) {
        app.init_state::<AppState>();

        // -- Plugins
        // Note - If disabling a plugin, make sure to remove from AppAssetsLoaded 'all_loaded' method
        app.add_plugins(LookTransformPlugin)
            .add_plugins(OrbitCameraPlugin {
                override_input_system: true,
            })
            // .add_plugins(plugins::avatars::AvatarsPlugin::default())
            // .add_plugins(plugins::games::GamesPlugin::default())
            .add_plugins(plugins::stages::StagesPlugin::default())
            .add_plugins(plugins::piano::PianoModelPlugin::default())
            .add_plugins(plugins::drums::DrumsModelPlugin::default());
        // .add_plugins(plugins::guitars::GuitarsModelPlugin::default());

        #[cfg(target_arch = "wasm32")]
        {
            app.add_plugins(plugins::GameControlPlugin::default());
        }

        // #[cfg(debug_assertions)]
        // #[cfg(feature = "run_bevy_in_web_worker")]
        // {
        //     app
        //         .add_plugins(DebugUIPlugin);
        // }

        #[cfg(any(feature = "webgpu", feature = "desktop"))]
        {
            app.add_plugins(HanabiPlugin);
        }

        // -- Types
        app.register_type::<DOFAppSettings>()
            .register_type::<smooth_bevy_cameras::controllers::orbit::OrbitCameraController>()
            .register_type::<LookTransform>()
            .register_type::<Smoother>()
            .register_type::<resources::PrimaryAudioChannel>()
            .register_type::<resources::CameraBounds>()
            .register_type::<resources::ClientSocketID>()
            .register_type::<resources::ClientIsMobile>()
            .register_type::<resources::ClientSocketIDStr>()
            .register_type::<resources::DrumsDisplayed>()
            .register_type::<resources::PianoDisplayed>()
            .register_type::<resources::PianoBenchDisplayed>()
            .register_type::<resources::CameraLock>()
            .register_type::<resources::DisplayKeysInputMapping>()
            .register_type::<crate::components::BaseMeshColor>()
            .register_type::<crate::components::DefaultMeshColor>()
            .register_type::<crate::components::InstrumentType>()
            .register_type::<crate::components::MainInstrument>()
            .register_type::<crate::components::SplitModeChannel>()
            .register_type::<crate::components::SplitModeMaterialColor>()
            .register_type::<crate::components::AnimationState>();

        // -- Events
        app.add_event_channel::<ECSWorldActions>(self.tx_world_actions.clone(), self.rx_world_actions.clone())
            .add_event_channel::<ECSSynthEventsAction>(self.tx_synth_events.clone(), self.rx_synth_events.clone())
            .add_event::<ECSWorldEffects>()
            .add_event::<ECSWorldEvents>()
            .add_event::<ECSSynthEvents>()
            .add_event::<SynthEventsBroadcastAction>()
            .add_event::<AvatarEventsBroadcastAction>()
            .add_event::<TweenCompleted>();

        // -- Resources
        app.insert_resource(ClearColor(Color::NONE))
            .insert_resource(AmbientLight {
                brightness: 350.0,
                ..default()
            })
            .insert_resource(CameraBounds {
                min_x: -10.0,
                max_x: 10.0,
                min_y: 0.5,
                max_y: 8.0,
                min_z: -15.0,
                max_z: 15.0,
            })
            .insert_resource(resources::AppAssetsLoaded::default())
            .insert_resource(resources::Client(UserClientDto::default()))
            .insert_resource(resources::ClientSocketID::default())
            .insert_resource(resources::ClientSocketIDStr::default())
            .insert_resource(resources::ClientAppSettings(AppSettings::default()))
            .insert_resource(resources::ClientIsMobile(false))
            .insert_resource(resources::Users(FxHashMap::default()))
            .insert_resource(resources::UserColors(FxHashMap::default()))
            .insert_resource(resources::ChannelsMode::default())
            .insert_resource(resources::CurrentRoomType::default())
            .insert_resource(resources::SynthAudioChannels::default())
            .insert_resource(resources::SynthAudioChannelsActiveState::default())
            .insert_resource(resources::ActiveStageSettings::default())
            .insert_resource(resources::PrimaryAudioChannel::default())
            .insert_resource(resources::PianoBenchDisplayed(true))
            .insert_resource(resources::PianoDisplayed(true))
            .insert_resource(resources::DrumsDisplayed(false))
            .insert_resource(resources::DisplayKeysInputMapping(false))
            .insert_resource(resources::KeysInputMapping::default())
            .insert_resource(resources::CameraLock(false))
            .insert_resource(resources::DefaultCameraPosition {
                target: Vec3::new(-0.2519522, 0.588438, 0.07148834),
                eye: Vec3::new(0.935753, 1.8205159, 2.2219148),
            })
            .insert_resource(resources::DefaultCameraTopPosition {
                target: Vec3::new(0.051, 0.521, 0.333),
                eye: Vec3::new(0.048, 1.712, 1.208),
            })
            .init_resource::<resources::CoreSystems>()
            .insert_resource(resources::RoomStage(AppStateEffects_LoadRoomStageDetails::default()));

        #[cfg(feature = "desktop")]
        app.insert_resource(CursorPositionDesktop::default());

        // -- Systems
        app.add_systems(
            Update,
            bevy_tweening::component_animator_system::<LookTransform>.in_set(AnimationSystem::AnimationUpdate),
        )
        .add_systems(
            Update,
            asset_animator_system::<StandardMaterial, MeshMaterial3d<StandardMaterial>>
                .in_set(AnimationSystem::AnimationUpdate),
        )
        .add_systems(Startup, components::main_camera::setup_camera)
        .add_systems(
            Update,
            (
                components::main_camera::limit_camera_movement,
                components::main_camera::orbit_camera_input_map.run_if(not(resource_exists::<CameraAnimating>)),
                #[cfg(feature = "desktop")]
                systems::cursor_position_desktop_set,
                systems::on_active_stage_setting_change.run_if(resource_changed::<resources::ActiveStageSettings>),
                events::on_app_setting_change.run_if(resource_changed::<resources::ClientAppSettings>),
                check_assets_loaded.run_if(resource_exists_and_changed::<resources::AppAssetsLoaded>),
            ),
        )
        .add_systems(
            PreUpdate,
            (
                (events::receiving_app_actions, events::receiving_app_effects).chain(),
                events::broadcasting_effects_to_app,
                events::broadcasting_events_to_app,
                events::broadcast_synth_events,
                events::broadcast_avatar_events,
            ),
        );

        #[cfg(any(feature = "webgpu", feature = "desktop"))]
        {
            // TODO
            //app
            //    .add_systems(Startup, crate::core::setup_effects);
        }

        // -- FPS
        app
            // TODO
            //.add_plugins(FrameTimeDiagnosticsPlugin)
            .add_systems(Startup, components::fps_counter::setup_fps_counter)
            .add_systems(
                FixedUpdate,
                (
                    components::fps_counter::fps_text_update_system.run_if(on_timer(Duration::from_millis(1000))),
                    components::fps_counter::fps_counter_showhide
                        .run_if(resource_changed::<resources::ClientAppSettings>),
                ),
            );

        log::info!("Built {:?}", self.name());
    }
}

fn check_assets_loaded(
    mut commands: Commands,
    assets_loaded: ResMut<crate::resources::AppAssetsLoaded>,
    mut event_writer: EventWriter<ECSWorldEffects>,
    mut next_app_state: ResMut<NextState<AppState>>,
) {
    if assets_loaded.all_loaded() {
        ::log::info!("All required assets loaded");
        event_writer.write(ECSWorldEffects::new(AppStateEffects_Action::RendererLoaded));
        commands.remove_resource::<resources::AppAssetsLoaded>();
        next_app_state.set(AppState::AppAssetsLoaded);
    }
}

//#[cfg(any(feature = "webgpu", feature = "desktop"))]
//fn setup_effects(
//    asset_server: Res<AssetServer>,
//    mut commands: Commands,
//    mut effects: ResMut<Assets<EffectAsset>>,
//) {
//    let mut color_gradient = Gradient::new();
//    let target_color = Vec4::new(1.0, 1.0, 1.0, 0.4);
//    // gradient.add_key(0.0, Vec4::new(1.0, 0.0, 0.0, 1.0));
//    color_gradient.add_key(0.0, Vec4::ZERO);
//    color_gradient.add_key(0.2, target_color);
//    color_gradient.add_key(0.8, target_color);
//    color_gradient.add_key(1.0, Vec4::ZERO);
//
//    let target_size = 0.2;
//    let mut size_gradient = Gradient::new();
//    // size_gradient.add_key(0.0, Vec2::new(0., 0.));
//    size_gradient.add_key(0., Vec3::new(target_size, target_size, target_size));
//    size_gradient.add_key(1., Vec3::new(target_size, target_size, target_size));
//    // size_gradient.add_key(1.0, Vec2::ZERO);
//
//    let writer1 = ExprWriter::new();
//    let age1 = writer1.lit(0.).expr();
//    let init_age1 = SetAttributeModifier::new(Attribute::AGE, age1);
//    let lifetime1 = writer1.lit(8.).expr();
//    let init_lifetime1 = SetAttributeModifier::new(Attribute::LIFETIME, lifetime1);
//    let init_pos1 = SetPositionSphereModifier {
//        center: writer1.lit(Vec3::ZERO).expr(),
//        radius: writer1.lit(20.).expr(),
//        dimension: ShapeDimension::Volume,
//    };
//
//    let init_vel1 = SetVelocitySphereModifier {
//        center: writer1.lit(Vec3::ZERO).expr(),
//        speed: (writer1.rand(ScalarType::Float) * writer1.lit(0.1) + writer1.lit(0.5)).expr(),
//    };
//
//    // Use the F32_0 attribute as a per-particle rotation value, initialized on
//    // spawn and constant after. The rotation angle is in radians, here randomly
//    // selected in [0:2*PI].
//    let rotation1 = (writer1.rand(ScalarType::Float) * writer1.lit(std::f32::consts::TAU)).expr();
//    let init_rotation1 = SetAttributeModifier::new(Attribute::F32_0, rotation1);
//    let rotation_attr1 = writer1.attr(Attribute::F32_0).expr();
//    let texture_handle1: Handle<Image> = asset_server.load("/textures/quavers-2-joined-white.png");
//    let texture_slot_1 = writer1.lit(0u32).expr();
//
//    let mut module = writer1.finish();
//    module.add_texture_slot("quavers-2-joined");
//
//    commands
//        .spawn((
//            Name::new("star-field-quavers-joined"),
//            ParticleEffectBundle {
//                effect: ParticleEffect::new(effects.add(
//                    EffectAsset::new(
//                        32,
//                        Spawner::rate(3.0.into()),
//                        module,
//                    )
//                        .with_name("emit:star-note-field")
//                        .with_alpha_mode(bevy_hanabi::AlphaMode::Blend)
//                        .init(init_pos1)
//                        .init(init_vel1)
//                        .init(init_age1)
//                        .init(init_rotation1)
//                        .init(init_lifetime1)
//                        .render(ParticleTextureModifier {
//                            texture_slot: texture_slot_1,
//                            sample_mapping: ImageSampleMapping::Modulate,
//                        })
//                        .render(SizeOverLifetimeModifier {
//                            gradient: size_gradient.clone(),
//                            screen_space_size: false,
//                        })
//                        .render(OrientModifier {
//                            mode: OrientMode::FaceCameraPosition,
//                            rotation: Some(rotation_attr1),
//                        })
//                        .render(ColorOverLifetimeModifier { gradient: color_gradient.clone() }),
//                )),
//                transform: Transform::from_translation(Vec3::new(0., 5., 0.)),
//                ..Default::default()
//            },
//            // bevy::render::view::RenderLayers::layer(3),
//            EffectMaterial {
//                images: vec![texture_handle1.clone()],
//            },
//        ));
//
//    let writer1 = ExprWriter::new();
//    let age1 = writer1.lit(0.).expr();
//    let init_age1 = SetAttributeModifier::new(Attribute::AGE, age1);
//    let lifetime1 = writer1.lit(6.).expr();
//    let init_lifetime1 = SetAttributeModifier::new(Attribute::LIFETIME, lifetime1);
//    let init_pos1 = SetPositionSphereModifier {
//        center: writer1.lit(Vec3::ZERO).expr(),
//        radius: writer1.lit(10.).expr(),
//        dimension: ShapeDimension::Volume,
//    };
//
//    let init_vel1 = SetVelocitySphereModifier {
//        center: writer1.lit(Vec3::ZERO).expr(),
//        speed: (writer1.rand(ScalarType::Float) * writer1.lit(0.1) + writer1.lit(0.5)).expr(),
//    };
//
//    let rotation1 = (writer1.rand(ScalarType::Float) * writer1.lit(std::f32::consts::TAU)).expr();
//    let init_rotation1 = SetAttributeModifier::new(Attribute::F32_0, rotation1);
//    let rotation_attr1 = writer1.attr(Attribute::F32_0).expr();
//    let texture_handle2: Handle<Image> = asset_server.load("/textures/quavers-2-single-white.png");
//    let texture_slot_2 = writer1.lit(0u32).expr();
//
//    let mut module = writer1.finish();
//    module.add_texture_slot("quavers-2-single");
//
//    commands
//        .spawn((
//            Name::new("star-field-quavers-single"),
//            ParticleEffectBundle {
//                effect: ParticleEffect::new(effects.add(
//                    EffectAsset::new(
//                        32,
//                        Spawner::rate(2.5.into()),
//                        module,
//                    )
//                        .with_name("emit:star-note-field-2")
//                        .with_alpha_mode(bevy_hanabi::AlphaMode::Blend)
//                        .init(init_pos1)
//                        .init(init_vel1)
//                        .init(init_age1)
//                        .init(init_rotation1)
//                        .init(init_lifetime1)
//                        .render(ParticleTextureModifier {
//                            texture_slot: texture_slot_2,
//                            sample_mapping: ImageSampleMapping::Modulate,
//                        })
//                        .render(SizeOverLifetimeModifier {
//                            gradient: size_gradient,
//                            screen_space_size: false,
//                        })
//                        .render(OrientModifier {
//                            mode: OrientMode::FaceCameraPosition,
//                            rotation: Some(rotation_attr1),
//                        })
//                        .render(ColorOverLifetimeModifier { gradient: color_gradient }),
//                )),
//                transform: Transform::from_translation(Vec3::new(0., 5., 0.)),
//                ..Default::default()
//            },
//            // bevy::render::view::RenderLayers::layer(3),
//            EffectMaterial {
//                images: vec![texture_handle2.clone()],
//            },
//        ));
//}
