{"$schema": "http://json-schema.org/draft-07/schema#", "title": "CapabilityFile", "description": "Capability formats accepted in a capability file.", "anyOf": [{"description": "A single capability.", "allOf": [{"$ref": "#/definitions/Capability"}]}, {"description": "A list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}, {"description": "A list of capabilities.", "type": "object", "required": ["capabilities"], "properties": {"capabilities": {"description": "The list of capabilities.", "type": "array", "items": {"$ref": "#/definitions/Capability"}}}}], "definitions": {"Capability": {"description": "A grouping and boundary mechanism developers can use to isolate access to the IPC layer.\n\nIt controls application windows fine grained access to the Tauri core, application, or plugin commands. If a window is not matching any capability then it has no access to the IPC layer at all.\n\nThis can be done to create groups of windows, based on their required system access, which can reduce impact of frontend vulnerabilities in less privileged windows. Windows can be added to a capability by exact name (e.g. `main-window`) or glob patterns like `*` or `admin-*`. A Window can have none, one, or multiple associated capabilities.\n\n## Example\n\n```json { \"identifier\": \"main-user-files-write\", \"description\": \"This capability allows the `main` window on macOS and Windows access to `filesystem` write related commands and `dialog` commands to enable programatic access to files selected by the user.\", \"windows\": [ \"main\" ], \"permissions\": [ \"core:default\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] }, \"platforms\": [\"macOS\",\"windows\"] } ```", "type": "object", "required": ["identifier", "permissions"], "properties": {"identifier": {"description": "Identifier of the capability.\n\n## Example\n\n`main-user-files-write`", "type": "string"}, "description": {"description": "Description of what the capability is intended to allow on associated windows.\n\nIt should contain a description of what the grouped permissions should allow.\n\n## Example\n\nThis capability allows the `main` window access to `filesystem` write related commands and `dialog` commands to enable programatic access to files selected by the user.", "default": "", "type": "string"}, "remote": {"description": "Configure remote URLs that can use the capability permissions.\n\nThis setting is optional and defaults to not being set, as our default use case is that the content is served from our local application.\n\n:::caution Make sure you understand the security implications of providing remote sources with local system access. :::\n\n## Example\n\n```json { \"urls\": [\"https://*.mydomain.dev\"] } ```", "anyOf": [{"$ref": "#/definitions/CapabilityRemote"}, {"type": "null"}]}, "local": {"description": "Whether this capability is enabled for local app URLs or not. Defaults to `true`.", "default": true, "type": "boolean"}, "windows": {"description": "List of windows that are affected by this capability. Can be a glob pattern.\n\nOn multiwebview windows, prefer [`Self::webviews`] for a fine grained access control.\n\n## Example\n\n`[\"main\"]`", "type": "array", "items": {"type": "string"}}, "webviews": {"description": "List of webviews that are affected by this capability. Can be a glob pattern.\n\nThis is only required when using on multiwebview contexts, by default all child webviews of a window that matches [`Self::windows`] are linked.\n\n## Example\n\n`[\"sub-webview-one\", \"sub-webview-two\"]`", "type": "array", "items": {"type": "string"}}, "permissions": {"description": "List of permissions attached to this capability.\n\nMust include the plugin name as prefix in the form of `${plugin-name}:${permission-name}`. For commands directly implemented in the application itself only `${permission-name}` is required.\n\n## Example\n\n```json [ \"core:default\", \"shell:allow-open\", \"dialog:open\", { \"identifier\": \"fs:allow-write-text-file\", \"allow\": [{ \"path\": \"$HOME/test.txt\" }] } ```", "type": "array", "items": {"$ref": "#/definitions/PermissionEntry"}, "uniqueItems": true}, "platforms": {"description": "Limit which target platforms this capability applies to.\n\nBy default all platforms are targeted.\n\n## Example\n\n`[\"macOS\",\"windows\"]`", "type": ["array", "null"], "items": {"$ref": "#/definitions/Target"}}}}, "CapabilityRemote": {"description": "Configuration for remote URLs that are associated with the capability.", "type": "object", "required": ["urls"], "properties": {"urls": {"description": "Remote domains this capability refers to using the [URLPattern standard](https://urlpattern.spec.whatwg.org/).\n\n## Examples\n\n- \"https://*.mydomain.dev\": allows subdomains of mydomain.dev - \"https://mydomain.dev/api/*\": allows any subpath of mydomain.dev/api", "type": "array", "items": {"type": "string"}}}}, "PermissionEntry": {"description": "An entry for a permission value in a [`Capability`] can be either a raw permission [`Identifier`] or an object that references a permission and extends its scope.", "anyOf": [{"description": "Reference a permission or permission set by identifier.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, {"description": "Reference a permission or permission set by identifier and extends its scope.", "type": "object", "allOf": [{"if": {"properties": {"identifier": {"anyOf": [{"description": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n", "type": "string", "const": "fs:default"}, {"description": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.", "type": "string", "const": "fs:allow-app-meta"}, {"description": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.", "type": "string", "const": "fs:allow-app-meta-recursive"}, {"description": "This allows non-recursive read access to the application folders.", "type": "string", "const": "fs:allow-app-read"}, {"description": "This allows full recursive read access to the complete application folders, files and subdirectories.", "type": "string", "const": "fs:allow-app-read-recursive"}, {"description": "This allows non-recursive write access to the application folders.", "type": "string", "const": "fs:allow-app-write"}, {"description": "This allows full recursive write access to the complete application folders, files and subdirectories.", "type": "string", "const": "fs:allow-app-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appcache-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appcache-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPCACHE` folder.", "type": "string", "const": "fs:allow-appcache-read"}, {"description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appcache-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPCACHE` folder.", "type": "string", "const": "fs:allow-appcache-write"}, {"description": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appcache-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appconfig-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appconfig-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPCONFIG` folder.", "type": "string", "const": "fs:allow-appconfig-read"}, {"description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appconfig-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPCONFIG` folder.", "type": "string", "const": "fs:allow-appconfig-write"}, {"description": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appconfig-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appdata-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appdata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPDATA` folder.", "type": "string", "const": "fs:allow-appdata-read"}, {"description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appdata-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPDATA` folder.", "type": "string", "const": "fs:allow-appdata-write"}, {"description": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appdata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applocaldata-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applocaldata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:allow-applocaldata-read"}, {"description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applocaldata-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:allow-applocaldata-write"}, {"description": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applocaldata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applog-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applog-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPLOG` folder.", "type": "string", "const": "fs:allow-applog-read"}, {"description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applog-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPLOG` folder.", "type": "string", "const": "fs:allow-applog-write"}, {"description": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applog-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-audio-meta"}, {"description": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-audio-meta-recursive"}, {"description": "This allows non-recursive read access to the `$AUDIO` folder.", "type": "string", "const": "fs:allow-audio-read"}, {"description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-audio-read-recursive"}, {"description": "This allows non-recursive write access to the `$AUDIO` folder.", "type": "string", "const": "fs:allow-audio-write"}, {"description": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-audio-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-cache-meta"}, {"description": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-cache-meta-recursive"}, {"description": "This allows non-recursive read access to the `$CACHE` folder.", "type": "string", "const": "fs:allow-cache-read"}, {"description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-cache-read-recursive"}, {"description": "This allows non-recursive write access to the `$CACHE` folder.", "type": "string", "const": "fs:allow-cache-write"}, {"description": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-cache-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-config-meta"}, {"description": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-config-meta-recursive"}, {"description": "This allows non-recursive read access to the `$CONFIG` folder.", "type": "string", "const": "fs:allow-config-read"}, {"description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-config-read-recursive"}, {"description": "This allows non-recursive write access to the `$CONFIG` folder.", "type": "string", "const": "fs:allow-config-write"}, {"description": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-config-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-data-meta"}, {"description": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-data-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DATA` folder.", "type": "string", "const": "fs:allow-data-read"}, {"description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-data-read-recursive"}, {"description": "This allows non-recursive write access to the `$DATA` folder.", "type": "string", "const": "fs:allow-data-write"}, {"description": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-data-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-desktop-meta"}, {"description": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-desktop-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DESKTOP` folder.", "type": "string", "const": "fs:allow-desktop-read"}, {"description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-desktop-read-recursive"}, {"description": "This allows non-recursive write access to the `$DESKTOP` folder.", "type": "string", "const": "fs:allow-desktop-write"}, {"description": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-desktop-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-document-meta"}, {"description": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-document-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DOCUMENT` folder.", "type": "string", "const": "fs:allow-document-read"}, {"description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-document-read-recursive"}, {"description": "This allows non-recursive write access to the `$DOCUMENT` folder.", "type": "string", "const": "fs:allow-document-write"}, {"description": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-document-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-download-meta"}, {"description": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-download-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DOWNLOAD` folder.", "type": "string", "const": "fs:allow-download-read"}, {"description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.", "type": "string", "const": "fs:allow-download-read-recursive"}, {"description": "This allows non-recursive write access to the `$DOWNLOAD` folder.", "type": "string", "const": "fs:allow-download-write"}, {"description": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.", "type": "string", "const": "fs:allow-download-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-exe-meta"}, {"description": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-exe-meta-recursive"}, {"description": "This allows non-recursive read access to the `$EXE` folder.", "type": "string", "const": "fs:allow-exe-read"}, {"description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-exe-read-recursive"}, {"description": "This allows non-recursive write access to the `$EXE` folder.", "type": "string", "const": "fs:allow-exe-write"}, {"description": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-exe-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-font-meta"}, {"description": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-font-meta-recursive"}, {"description": "This allows non-recursive read access to the `$FONT` folder.", "type": "string", "const": "fs:allow-font-read"}, {"description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-font-read-recursive"}, {"description": "This allows non-recursive write access to the `$FONT` folder.", "type": "string", "const": "fs:allow-font-write"}, {"description": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-font-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-home-meta"}, {"description": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-home-meta-recursive"}, {"description": "This allows non-recursive read access to the `$HOME` folder.", "type": "string", "const": "fs:allow-home-read"}, {"description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-home-read-recursive"}, {"description": "This allows non-recursive write access to the `$HOME` folder.", "type": "string", "const": "fs:allow-home-write"}, {"description": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-home-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-localdata-meta"}, {"description": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-localdata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$LOCALDATA` folder.", "type": "string", "const": "fs:allow-localdata-read"}, {"description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-localdata-read-recursive"}, {"description": "This allows non-recursive write access to the `$LOCALDATA` folder.", "type": "string", "const": "fs:allow-localdata-write"}, {"description": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-localdata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-log-meta"}, {"description": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-log-meta-recursive"}, {"description": "This allows non-recursive read access to the `$LOG` folder.", "type": "string", "const": "fs:allow-log-read"}, {"description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-log-read-recursive"}, {"description": "This allows non-recursive write access to the `$LOG` folder.", "type": "string", "const": "fs:allow-log-write"}, {"description": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-log-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-picture-meta"}, {"description": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-picture-meta-recursive"}, {"description": "This allows non-recursive read access to the `$PICTURE` folder.", "type": "string", "const": "fs:allow-picture-read"}, {"description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-picture-read-recursive"}, {"description": "This allows non-recursive write access to the `$PICTURE` folder.", "type": "string", "const": "fs:allow-picture-write"}, {"description": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-picture-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-public-meta"}, {"description": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-public-meta-recursive"}, {"description": "This allows non-recursive read access to the `$PUBLIC` folder.", "type": "string", "const": "fs:allow-public-read"}, {"description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.", "type": "string", "const": "fs:allow-public-read-recursive"}, {"description": "This allows non-recursive write access to the `$PUBLIC` folder.", "type": "string", "const": "fs:allow-public-write"}, {"description": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.", "type": "string", "const": "fs:allow-public-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-resource-meta"}, {"description": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-resource-meta-recursive"}, {"description": "This allows non-recursive read access to the `$RESOURCE` folder.", "type": "string", "const": "fs:allow-resource-read"}, {"description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-resource-read-recursive"}, {"description": "This allows non-recursive write access to the `$RESOURCE` folder.", "type": "string", "const": "fs:allow-resource-write"}, {"description": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-resource-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-runtime-meta"}, {"description": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-runtime-meta-recursive"}, {"description": "This allows non-recursive read access to the `$RUNTIME` folder.", "type": "string", "const": "fs:allow-runtime-read"}, {"description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-runtime-read-recursive"}, {"description": "This allows non-recursive write access to the `$RUNTIME` folder.", "type": "string", "const": "fs:allow-runtime-write"}, {"description": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-runtime-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-temp-meta"}, {"description": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-temp-meta-recursive"}, {"description": "This allows non-recursive read access to the `$TEMP` folder.", "type": "string", "const": "fs:allow-temp-read"}, {"description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-temp-read-recursive"}, {"description": "This allows non-recursive write access to the `$TEMP` folder.", "type": "string", "const": "fs:allow-temp-write"}, {"description": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-temp-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-template-meta"}, {"description": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-template-meta-recursive"}, {"description": "This allows non-recursive read access to the `$TEMPLATE` folder.", "type": "string", "const": "fs:allow-template-read"}, {"description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-template-read-recursive"}, {"description": "This allows non-recursive write access to the `$TEMPLATE` folder.", "type": "string", "const": "fs:allow-template-write"}, {"description": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-template-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-video-meta"}, {"description": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-video-meta-recursive"}, {"description": "This allows non-recursive read access to the `$VIDEO` folder.", "type": "string", "const": "fs:allow-video-read"}, {"description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-video-read-recursive"}, {"description": "This allows non-recursive write access to the `$VIDEO` folder.", "type": "string", "const": "fs:allow-video-write"}, {"description": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-video-write-recursive"}, {"description": "This denies access to dangerous Tauri relevant files and folders by default.", "type": "string", "const": "fs:deny-default"}, {"description": "Enables the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-copy-file"}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "fs:allow-create"}, {"description": "Enables the exists command without any pre-configured scope.", "type": "string", "const": "fs:allow-exists"}, {"description": "Enables the fstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-fstat"}, {"description": "Enables the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-ftruncate"}, {"description": "Enables the lstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-lstat"}, {"description": "Enables the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:allow-mkdir"}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "fs:allow-open"}, {"description": "Enables the read command without any pre-configured scope.", "type": "string", "const": "fs:allow-read"}, {"description": "Enables the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-dir"}, {"description": "Enables the read_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-file"}, {"description": "Enables the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file"}, {"description": "Enables the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines"}, {"description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines-next"}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "fs:allow-remove"}, {"description": "Enables the rename command without any pre-configured scope.", "type": "string", "const": "fs:allow-rename"}, {"description": "Enables the seek command without any pre-configured scope.", "type": "string", "const": "fs:allow-seek"}, {"description": "Enables the stat command without any pre-configured scope.", "type": "string", "const": "fs:allow-stat"}, {"description": "Enables the truncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-truncate"}, {"description": "Enables the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:allow-unwatch"}, {"description": "Enables the watch command without any pre-configured scope.", "type": "string", "const": "fs:allow-watch"}, {"description": "Enables the write command without any pre-configured scope.", "type": "string", "const": "fs:allow-write"}, {"description": "Enables the write_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-file"}, {"description": "Enables the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-text-file"}, {"description": "This permissions allows to create the application specific directories.\n", "type": "string", "const": "fs:create-app-specific-dirs"}, {"description": "Denies the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-copy-file"}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "fs:deny-create"}, {"description": "Denies the exists command without any pre-configured scope.", "type": "string", "const": "fs:deny-exists"}, {"description": "Denies the fstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-fstat"}, {"description": "Denies the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-ftruncate"}, {"description": "Denies the lstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-lstat"}, {"description": "Denies the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:deny-mkdir"}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "fs:deny-open"}, {"description": "Denies the read command without any pre-configured scope.", "type": "string", "const": "fs:deny-read"}, {"description": "Denies the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-dir"}, {"description": "Denies the read_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-file"}, {"description": "Denies the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file"}, {"description": "Denies the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines"}, {"description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines-next"}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "fs:deny-remove"}, {"description": "Denies the rename command without any pre-configured scope.", "type": "string", "const": "fs:deny-rename"}, {"description": "Denies the seek command without any pre-configured scope.", "type": "string", "const": "fs:deny-seek"}, {"description": "Denies the stat command without any pre-configured scope.", "type": "string", "const": "fs:deny-stat"}, {"description": "Denies the truncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-truncate"}, {"description": "Denies the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:deny-unwatch"}, {"description": "Denies the watch command without any pre-configured scope.", "type": "string", "const": "fs:deny-watch"}, {"description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-linux"}, {"description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-windows"}, {"description": "Denies the write command without any pre-configured scope.", "type": "string", "const": "fs:deny-write"}, {"description": "Denies the write_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-file"}, {"description": "Denies the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-text-file"}, {"description": "This enables all read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-all"}, {"description": "This permission allows recursive read functionality on the application\nspecific base directories. \n", "type": "string", "const": "fs:read-app-specific-dirs-recursive"}, {"description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-dirs"}, {"description": "This enables file read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-files"}, {"description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-meta"}, {"description": "An empty permission you can use to modify the global scope.", "type": "string", "const": "fs:scope"}, {"description": "This scope permits access to all files and list content of top level directories in the application folders.", "type": "string", "const": "fs:scope-app"}, {"description": "This scope permits to list all files and folders in the application directories.", "type": "string", "const": "fs:scope-app-index"}, {"description": "This scope permits recursive access to the complete application folders, including sub directories and files.", "type": "string", "const": "fs:scope-app-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder.", "type": "string", "const": "fs:scope-appcache"}, {"description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "type": "string", "const": "fs:scope-appcache-index"}, {"description": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appcache-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder.", "type": "string", "const": "fs:scope-appconfig"}, {"description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "type": "string", "const": "fs:scope-appconfig-index"}, {"description": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appconfig-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder.", "type": "string", "const": "fs:scope-appdata"}, {"description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "type": "string", "const": "fs:scope-appdata-index"}, {"description": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appdata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:scope-applocaldata"}, {"description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "type": "string", "const": "fs:scope-applocaldata-index"}, {"description": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applocaldata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder.", "type": "string", "const": "fs:scope-applog"}, {"description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "type": "string", "const": "fs:scope-applog-index"}, {"description": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applog-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder.", "type": "string", "const": "fs:scope-audio"}, {"description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "type": "string", "const": "fs:scope-audio-index"}, {"description": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-audio-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder.", "type": "string", "const": "fs:scope-cache"}, {"description": "This scope permits to list all files and folders in the `$CACHE`folder.", "type": "string", "const": "fs:scope-cache-index"}, {"description": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-cache-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder.", "type": "string", "const": "fs:scope-config"}, {"description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "type": "string", "const": "fs:scope-config-index"}, {"description": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-config-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DATA` folder.", "type": "string", "const": "fs:scope-data"}, {"description": "This scope permits to list all files and folders in the `$DATA`folder.", "type": "string", "const": "fs:scope-data-index"}, {"description": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-data-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder.", "type": "string", "const": "fs:scope-desktop"}, {"description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "type": "string", "const": "fs:scope-desktop-index"}, {"description": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-desktop-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder.", "type": "string", "const": "fs:scope-document"}, {"description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "type": "string", "const": "fs:scope-document-index"}, {"description": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-document-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder.", "type": "string", "const": "fs:scope-download"}, {"description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "type": "string", "const": "fs:scope-download-index"}, {"description": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "type": "string", "const": "fs:scope-download-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$EXE` folder.", "type": "string", "const": "fs:scope-exe"}, {"description": "This scope permits to list all files and folders in the `$EXE`folder.", "type": "string", "const": "fs:scope-exe-index"}, {"description": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-exe-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$FONT` folder.", "type": "string", "const": "fs:scope-font"}, {"description": "This scope permits to list all files and folders in the `$FONT`folder.", "type": "string", "const": "fs:scope-font-index"}, {"description": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-font-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$HOME` folder.", "type": "string", "const": "fs:scope-home"}, {"description": "This scope permits to list all files and folders in the `$HOME`folder.", "type": "string", "const": "fs:scope-home-index"}, {"description": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-home-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder.", "type": "string", "const": "fs:scope-localdata"}, {"description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "type": "string", "const": "fs:scope-localdata-index"}, {"description": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-localdata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOG` folder.", "type": "string", "const": "fs:scope-log"}, {"description": "This scope permits to list all files and folders in the `$LOG`folder.", "type": "string", "const": "fs:scope-log-index"}, {"description": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-log-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder.", "type": "string", "const": "fs:scope-picture"}, {"description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "type": "string", "const": "fs:scope-picture-index"}, {"description": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-picture-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder.", "type": "string", "const": "fs:scope-public"}, {"description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "type": "string", "const": "fs:scope-public-index"}, {"description": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "type": "string", "const": "fs:scope-public-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder.", "type": "string", "const": "fs:scope-resource"}, {"description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "type": "string", "const": "fs:scope-resource-index"}, {"description": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-resource-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder.", "type": "string", "const": "fs:scope-runtime"}, {"description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "type": "string", "const": "fs:scope-runtime-index"}, {"description": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-runtime-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder.", "type": "string", "const": "fs:scope-temp"}, {"description": "This scope permits to list all files and folders in the `$TEMP`folder.", "type": "string", "const": "fs:scope-temp-index"}, {"description": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-temp-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder.", "type": "string", "const": "fs:scope-template"}, {"description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "type": "string", "const": "fs:scope-template-index"}, {"description": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-template-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder.", "type": "string", "const": "fs:scope-video"}, {"description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "type": "string", "const": "fs:scope-video-index"}, {"description": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-video-recursive"}, {"description": "This enables all write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-all"}, {"description": "This enables all file write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-files"}]}}}, "then": {"properties": {"allow": {"items": {"title": "FsScopeEntry", "description": "FS scope entry.", "anyOf": [{"description": "FS scope path.", "type": "string"}, {"type": "object", "required": ["path"], "properties": {"path": {"description": "FS scope path.", "type": "string"}}}]}}, "deny": {"items": {"title": "FsScopeEntry", "description": "FS scope entry.", "anyOf": [{"description": "FS scope path.", "type": "string"}, {"type": "object", "required": ["path"], "properties": {"path": {"description": "FS scope path.", "type": "string"}}}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"if": {"properties": {"identifier": {"anyOf": [{"description": "This permission set configures what kind of\nfetch operations are available from the http plugin.\n\nThis enables all fetch operations but does not\nallow explicitly any origins to be fetched. This needs to\nbe manually configured before usage.\n\n#### Granted Permissions\n\nAll fetch operations are enabled.\n\n", "type": "string", "const": "http:default"}, {"description": "Enables the fetch command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch"}, {"description": "Enables the fetch_cancel command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-cancel"}, {"description": "Enables the fetch_read_body command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-read-body"}, {"description": "Enables the fetch_send command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-send"}, {"description": "Denies the fetch command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch"}, {"description": "Denies the fetch_cancel command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-cancel"}, {"description": "Denies the fetch_read_body command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-read-body"}, {"description": "Denies the fetch_send command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-send"}]}}}, "then": {"properties": {"allow": {"items": {"title": "HttpScopeEntry", "description": "HTTP scope entry.", "anyOf": [{"description": "A URL that can be accessed by the webview when using the HTTP APIs. Wildcards can be used following the URL pattern standard.\n\nSee [the URL Pattern spec](https://urlpattern.spec.whatwg.org/) for more information.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin on port 443\n\n- \"https://*:*\" : allows all HTTPS origin on any port\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}, {"type": "object", "required": ["url"], "properties": {"url": {"description": "A URL that can be accessed by the webview when using the HTTP APIs. Wildcards can be used following the URL pattern standard.\n\nSee [the URL Pattern spec](https://urlpattern.spec.whatwg.org/) for more information.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin on port 443\n\n- \"https://*:*\" : allows all HTTPS origin on any port\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}}]}}, "deny": {"items": {"title": "HttpScopeEntry", "description": "HTTP scope entry.", "anyOf": [{"description": "A URL that can be accessed by the webview when using the HTTP APIs. Wildcards can be used following the URL pattern standard.\n\nSee [the URL Pattern spec](https://urlpattern.spec.whatwg.org/) for more information.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin on port 443\n\n- \"https://*:*\" : allows all HTTPS origin on any port\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}, {"type": "object", "required": ["url"], "properties": {"url": {"description": "A URL that can be accessed by the webview when using the HTTP APIs. Wildcards can be used following the URL pattern standard.\n\nSee [the URL Pattern spec](https://urlpattern.spec.whatwg.org/) for more information.\n\nExamples:\n\n- \"https://*\" : allows all HTTPS origin on port 443\n\n- \"https://*:*\" : allows all HTTPS origin on any port\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}}]}}}}, "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}}}, {"properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, "allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}}], "required": ["identifier"]}]}, "Identifier": {"description": "Permission identifier", "oneOf": [{"description": "Default core plugins set which includes:\n- 'core:path:default'\n- 'core:event:default'\n- 'core:window:default'\n- 'core:webview:default'\n- 'core:app:default'\n- 'core:image:default'\n- 'core:resources:default'\n- 'core:menu:default'\n- 'core:tray:default'\n", "type": "string", "const": "core:default"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:app:default"}, {"description": "Enables the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-hide"}, {"description": "Enables the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:allow-app-show"}, {"description": "Enables the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:allow-default-window-icon"}, {"description": "Enables the name command without any pre-configured scope.", "type": "string", "const": "core:app:allow-name"}, {"description": "Enables the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:allow-set-app-theme"}, {"description": "Enables the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-tauri-version"}, {"description": "Enables the version command without any pre-configured scope.", "type": "string", "const": "core:app:allow-version"}, {"description": "Denies the app_hide command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-hide"}, {"description": "Denies the app_show command without any pre-configured scope.", "type": "string", "const": "core:app:deny-app-show"}, {"description": "Denies the default_window_icon command without any pre-configured scope.", "type": "string", "const": "core:app:deny-default-window-icon"}, {"description": "Denies the name command without any pre-configured scope.", "type": "string", "const": "core:app:deny-name"}, {"description": "Denies the set_app_theme command without any pre-configured scope.", "type": "string", "const": "core:app:deny-set-app-theme"}, {"description": "Denies the tauri_version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-tauri-version"}, {"description": "Denies the version command without any pre-configured scope.", "type": "string", "const": "core:app:deny-version"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:event:default"}, {"description": "Enables the emit command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit"}, {"description": "Enables the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:allow-emit-to"}, {"description": "Enables the listen command without any pre-configured scope.", "type": "string", "const": "core:event:allow-listen"}, {"description": "Enables the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:allow-unlisten"}, {"description": "Denies the emit command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit"}, {"description": "Denies the emit_to command without any pre-configured scope.", "type": "string", "const": "core:event:deny-emit-to"}, {"description": "Denies the listen command without any pre-configured scope.", "type": "string", "const": "core:event:deny-listen"}, {"description": "Denies the unlisten command without any pre-configured scope.", "type": "string", "const": "core:event:deny-unlisten"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:image:default"}, {"description": "Enables the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-bytes"}, {"description": "Enables the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:allow-from-path"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:image:allow-new"}, {"description": "Enables the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:allow-rgba"}, {"description": "Enables the size command without any pre-configured scope.", "type": "string", "const": "core:image:allow-size"}, {"description": "Denies the from_bytes command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-bytes"}, {"description": "Denies the from_path command without any pre-configured scope.", "type": "string", "const": "core:image:deny-from-path"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:image:deny-new"}, {"description": "Denies the rgba command without any pre-configured scope.", "type": "string", "const": "core:image:deny-rgba"}, {"description": "Denies the size command without any pre-configured scope.", "type": "string", "const": "core:image:deny-size"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:menu:default"}, {"description": "Enables the append command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-append"}, {"description": "Enables the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-create-default"}, {"description": "Enables the get command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-get"}, {"description": "Enables the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-insert"}, {"description": "Enables the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-checked"}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-is-enabled"}, {"description": "Enables the items command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-items"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-new"}, {"description": "Enables the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-popup"}, {"description": "Enables the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-prepend"}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove"}, {"description": "Enables the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-remove-at"}, {"description": "Enables the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-accelerator"}, {"description": "Enables the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-app-menu"}, {"description": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-help-menu-for-nsapp"}, {"description": "Enables the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-window-menu"}, {"description": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-as-windows-menu-for-nsapp"}, {"description": "Enables the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-checked"}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-enabled"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-icon"}, {"description": "Enables the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-set-text"}, {"description": "Enables the text command without any pre-configured scope.", "type": "string", "const": "core:menu:allow-text"}, {"description": "Denies the append command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-append"}, {"description": "Denies the create_default command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-create-default"}, {"description": "Denies the get command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-get"}, {"description": "Denies the insert command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-insert"}, {"description": "Denies the is_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-checked"}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-is-enabled"}, {"description": "Denies the items command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-items"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-new"}, {"description": "Denies the popup command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-popup"}, {"description": "Denies the prepend command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-prepend"}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove"}, {"description": "Denies the remove_at command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-remove-at"}, {"description": "Denies the set_accelerator command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-accelerator"}, {"description": "Denies the set_as_app_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-app-menu"}, {"description": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-help-menu-for-nsapp"}, {"description": "Denies the set_as_window_menu command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-window-menu"}, {"description": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-as-windows-menu-for-nsapp"}, {"description": "Denies the set_checked command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-checked"}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-enabled"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-icon"}, {"description": "Denies the set_text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-set-text"}, {"description": "Denies the text command without any pre-configured scope.", "type": "string", "const": "core:menu:deny-text"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:path:default"}, {"description": "Enables the basename command without any pre-configured scope.", "type": "string", "const": "core:path:allow-basename"}, {"description": "Enables the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-dirname"}, {"description": "Enables the extname command without any pre-configured scope.", "type": "string", "const": "core:path:allow-extname"}, {"description": "Enables the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:allow-is-absolute"}, {"description": "Enables the join command without any pre-configured scope.", "type": "string", "const": "core:path:allow-join"}, {"description": "Enables the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:allow-normalize"}, {"description": "Enables the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve"}, {"description": "Enables the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:allow-resolve-directory"}, {"description": "Denies the basename command without any pre-configured scope.", "type": "string", "const": "core:path:deny-basename"}, {"description": "Denies the dirname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-dirname"}, {"description": "Denies the extname command without any pre-configured scope.", "type": "string", "const": "core:path:deny-extname"}, {"description": "Denies the is_absolute command without any pre-configured scope.", "type": "string", "const": "core:path:deny-is-absolute"}, {"description": "Denies the join command without any pre-configured scope.", "type": "string", "const": "core:path:deny-join"}, {"description": "Denies the normalize command without any pre-configured scope.", "type": "string", "const": "core:path:deny-normalize"}, {"description": "Denies the resolve command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve"}, {"description": "Denies the resolve_directory command without any pre-configured scope.", "type": "string", "const": "core:path:deny-resolve-directory"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:resources:default"}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:resources:allow-close"}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:resources:deny-close"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:tray:default"}, {"description": "Enables the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-get-by-id"}, {"description": "Enables the new command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-new"}, {"description": "Enables the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-remove-by-id"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon"}, {"description": "Enables the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-icon-as-template"}, {"description": "Enables the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-menu"}, {"description": "Enables the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-show-menu-on-left-click"}, {"description": "Enables the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-temp-dir-path"}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-title"}, {"description": "Enables the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-tooltip"}, {"description": "Enables the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:allow-set-visible"}, {"description": "Denies the get_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-get-by-id"}, {"description": "Denies the new command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-new"}, {"description": "Denies the remove_by_id command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-remove-by-id"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon"}, {"description": "Denies the set_icon_as_template command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-icon-as-template"}, {"description": "Denies the set_menu command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-menu"}, {"description": "Denies the set_show_menu_on_left_click command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-show-menu-on-left-click"}, {"description": "Denies the set_temp_dir_path command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-temp-dir-path"}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-title"}, {"description": "Denies the set_tooltip command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-tooltip"}, {"description": "Denies the set_visible command without any pre-configured scope.", "type": "string", "const": "core:tray:deny-set-visible"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:webview:default"}, {"description": "Enables the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-clear-all-browsing-data"}, {"description": "Enables the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview"}, {"description": "Enables the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-create-webview-window"}, {"description": "Enables the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-get-all-webviews"}, {"description": "Enables the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-internal-toggle-devtools"}, {"description": "Enables the print command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-print"}, {"description": "Enables the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-reparent"}, {"description": "Enables the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-focus"}, {"description": "Enables the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-position"}, {"description": "Enables the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-size"}, {"description": "Enables the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-set-webview-zoom"}, {"description": "Enables the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-close"}, {"description": "Enables the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-hide"}, {"description": "Enables the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-position"}, {"description": "Enables the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-show"}, {"description": "Enables the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:allow-webview-size"}, {"description": "Denies the clear_all_browsing_data command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-clear-all-browsing-data"}, {"description": "Denies the create_webview command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview"}, {"description": "Denies the create_webview_window command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-create-webview-window"}, {"description": "Denies the get_all_webviews command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-get-all-webviews"}, {"description": "Denies the internal_toggle_devtools command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-internal-toggle-devtools"}, {"description": "Denies the print command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-print"}, {"description": "Denies the reparent command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-reparent"}, {"description": "Denies the set_webview_focus command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-focus"}, {"description": "Denies the set_webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-position"}, {"description": "Denies the set_webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-size"}, {"description": "Denies the set_webview_zoom command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-set-webview-zoom"}, {"description": "Denies the webview_close command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-close"}, {"description": "Denies the webview_hide command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-hide"}, {"description": "Denies the webview_position command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-position"}, {"description": "Denies the webview_show command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-show"}, {"description": "Denies the webview_size command without any pre-configured scope.", "type": "string", "const": "core:webview:deny-webview-size"}, {"description": "Default permissions for the plugin.", "type": "string", "const": "core:window:default"}, {"description": "Enables the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:allow-available-monitors"}, {"description": "Enables the center command without any pre-configured scope.", "type": "string", "const": "core:window:allow-center"}, {"description": "Enables the close command without any pre-configured scope.", "type": "string", "const": "core:window:allow-close"}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "core:window:allow-create"}, {"description": "Enables the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-current-monitor"}, {"description": "Enables the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-cursor-position"}, {"description": "Enables the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:allow-destroy"}, {"description": "Enables the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:allow-get-all-windows"}, {"description": "Enables the hide command without any pre-configured scope.", "type": "string", "const": "core:window:allow-hide"}, {"description": "Enables the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-position"}, {"description": "Enables the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-inner-size"}, {"description": "Enables the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-internal-toggle-maximize"}, {"description": "Enables the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-closable"}, {"description": "Enables the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-decorated"}, {"description": "Enables the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-enabled"}, {"description": "Enables the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-focused"}, {"description": "Enables the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-fullscreen"}, {"description": "Enables the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximizable"}, {"description": "Enables the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-maximized"}, {"description": "Enables the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimizable"}, {"description": "Enables the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-minimized"}, {"description": "Enables the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-resizable"}, {"description": "Enables the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-is-visible"}, {"description": "Enables the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-maximize"}, {"description": "Enables the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-minimize"}, {"description": "Enables the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:allow-monitor-from-point"}, {"description": "Enables the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-position"}, {"description": "Enables the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-outer-size"}, {"description": "Enables the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-primary-monitor"}, {"description": "Enables the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:allow-request-user-attention"}, {"description": "Enables the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:allow-scale-factor"}, {"description": "Enables the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-bottom"}, {"description": "Enables the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-always-on-top"}, {"description": "Enables the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-closable"}, {"description": "Enables the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-content-protected"}, {"description": "Enables the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-grab"}, {"description": "Enables the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-icon"}, {"description": "Enables the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-position"}, {"description": "Enables the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-cursor-visible"}, {"description": "Enables the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-decorations"}, {"description": "Enables the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-effects"}, {"description": "Enables the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-enabled"}, {"description": "Enables the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-focus"}, {"description": "Enables the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-fullscreen"}, {"description": "Enables the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-icon"}, {"description": "Enables the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-ignore-cursor-events"}, {"description": "Enables the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-max-size"}, {"description": "Enables the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-maximizable"}, {"description": "Enables the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-min-size"}, {"description": "Enables the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-minimizable"}, {"description": "Enables the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-position"}, {"description": "Enables the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-progress-bar"}, {"description": "Enables the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-resizable"}, {"description": "Enables the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-shadow"}, {"description": "Enables the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size"}, {"description": "Enables the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-size-constraints"}, {"description": "Enables the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-skip-taskbar"}, {"description": "Enables the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-theme"}, {"description": "Enables the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title"}, {"description": "Enables the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-title-bar-style"}, {"description": "Enables the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:allow-set-visible-on-all-workspaces"}, {"description": "Enables the show command without any pre-configured scope.", "type": "string", "const": "core:window:allow-show"}, {"description": "Enables the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-dragging"}, {"description": "Enables the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:allow-start-resize-dragging"}, {"description": "Enables the theme command without any pre-configured scope.", "type": "string", "const": "core:window:allow-theme"}, {"description": "Enables the title command without any pre-configured scope.", "type": "string", "const": "core:window:allow-title"}, {"description": "Enables the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-toggle-maximize"}, {"description": "Enables the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unmaximize"}, {"description": "Enables the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:allow-unminimize"}, {"description": "Denies the available_monitors command without any pre-configured scope.", "type": "string", "const": "core:window:deny-available-monitors"}, {"description": "Denies the center command without any pre-configured scope.", "type": "string", "const": "core:window:deny-center"}, {"description": "Denies the close command without any pre-configured scope.", "type": "string", "const": "core:window:deny-close"}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "core:window:deny-create"}, {"description": "Denies the current_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-current-monitor"}, {"description": "Denies the cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-cursor-position"}, {"description": "Denies the destroy command without any pre-configured scope.", "type": "string", "const": "core:window:deny-destroy"}, {"description": "Denies the get_all_windows command without any pre-configured scope.", "type": "string", "const": "core:window:deny-get-all-windows"}, {"description": "Denies the hide command without any pre-configured scope.", "type": "string", "const": "core:window:deny-hide"}, {"description": "Denies the inner_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-position"}, {"description": "Denies the inner_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-inner-size"}, {"description": "Denies the internal_toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-internal-toggle-maximize"}, {"description": "Denies the is_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-closable"}, {"description": "Denies the is_decorated command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-decorated"}, {"description": "Denies the is_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-enabled"}, {"description": "Denies the is_focused command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-focused"}, {"description": "Denies the is_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-fullscreen"}, {"description": "Denies the is_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximizable"}, {"description": "Denies the is_maximized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-maximized"}, {"description": "Denies the is_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimizable"}, {"description": "Denies the is_minimized command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-minimized"}, {"description": "Denies the is_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-resizable"}, {"description": "Denies the is_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-is-visible"}, {"description": "Denies the maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-maximize"}, {"description": "Denies the minimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-minimize"}, {"description": "Denies the monitor_from_point command without any pre-configured scope.", "type": "string", "const": "core:window:deny-monitor-from-point"}, {"description": "Denies the outer_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-position"}, {"description": "Denies the outer_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-outer-size"}, {"description": "Denies the primary_monitor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-primary-monitor"}, {"description": "Denies the request_user_attention command without any pre-configured scope.", "type": "string", "const": "core:window:deny-request-user-attention"}, {"description": "Denies the scale_factor command without any pre-configured scope.", "type": "string", "const": "core:window:deny-scale-factor"}, {"description": "Denies the set_always_on_bottom command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-bottom"}, {"description": "Denies the set_always_on_top command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-always-on-top"}, {"description": "Denies the set_closable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-closable"}, {"description": "Denies the set_content_protected command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-content-protected"}, {"description": "Denies the set_cursor_grab command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-grab"}, {"description": "Denies the set_cursor_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-icon"}, {"description": "Denies the set_cursor_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-position"}, {"description": "Denies the set_cursor_visible command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-cursor-visible"}, {"description": "Denies the set_decorations command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-decorations"}, {"description": "Denies the set_effects command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-effects"}, {"description": "Denies the set_enabled command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-enabled"}, {"description": "Denies the set_focus command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-focus"}, {"description": "Denies the set_fullscreen command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-fullscreen"}, {"description": "Denies the set_icon command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-icon"}, {"description": "Denies the set_ignore_cursor_events command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-ignore-cursor-events"}, {"description": "Denies the set_max_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-max-size"}, {"description": "Denies the set_maximizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-maximizable"}, {"description": "Denies the set_min_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-min-size"}, {"description": "Denies the set_minimizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-minimizable"}, {"description": "Denies the set_position command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-position"}, {"description": "Denies the set_progress_bar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-progress-bar"}, {"description": "Denies the set_resizable command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-resizable"}, {"description": "Denies the set_shadow command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-shadow"}, {"description": "Denies the set_size command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size"}, {"description": "Denies the set_size_constraints command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-size-constraints"}, {"description": "Denies the set_skip_taskbar command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-skip-taskbar"}, {"description": "Denies the set_theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-theme"}, {"description": "Denies the set_title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title"}, {"description": "Denies the set_title_bar_style command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-title-bar-style"}, {"description": "Denies the set_visible_on_all_workspaces command without any pre-configured scope.", "type": "string", "const": "core:window:deny-set-visible-on-all-workspaces"}, {"description": "Denies the show command without any pre-configured scope.", "type": "string", "const": "core:window:deny-show"}, {"description": "Denies the start_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-dragging"}, {"description": "Denies the start_resize_dragging command without any pre-configured scope.", "type": "string", "const": "core:window:deny-start-resize-dragging"}, {"description": "Denies the theme command without any pre-configured scope.", "type": "string", "const": "core:window:deny-theme"}, {"description": "Denies the title command without any pre-configured scope.", "type": "string", "const": "core:window:deny-title"}, {"description": "Denies the toggle_maximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-toggle-maximize"}, {"description": "Denies the unmaximize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unmaximize"}, {"description": "Denies the unminimize command without any pre-configured scope.", "type": "string", "const": "core:window:deny-unminimize"}, {"description": "This permission set configures the types of dialogs\navailable from the dialog plugin.\n\n#### Granted Permissions\n\nAll dialog types are enabled.\n\n\n", "type": "string", "const": "dialog:default"}, {"description": "Enables the ask command without any pre-configured scope.", "type": "string", "const": "dialog:allow-ask"}, {"description": "Enables the confirm command without any pre-configured scope.", "type": "string", "const": "dialog:allow-confirm"}, {"description": "Enables the message command without any pre-configured scope.", "type": "string", "const": "dialog:allow-message"}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "dialog:allow-open"}, {"description": "Enables the save command without any pre-configured scope.", "type": "string", "const": "dialog:allow-save"}, {"description": "Denies the ask command without any pre-configured scope.", "type": "string", "const": "dialog:deny-ask"}, {"description": "Denies the confirm command without any pre-configured scope.", "type": "string", "const": "dialog:deny-confirm"}, {"description": "Denies the message command without any pre-configured scope.", "type": "string", "const": "dialog:deny-message"}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "dialog:deny-open"}, {"description": "Denies the save command without any pre-configured scope.", "type": "string", "const": "dialog:deny-save"}, {"description": "This set of permissions describes the what kind of\nfile system access the `fs` plugin has enabled or denied by default.\n\n#### Granted Permissions\n\nThis default permission set enables read access to the\napplication specific directories (AppConfig, AppData, AppLocalData, AppCache,\nAppLog) and all files and sub directories created in it.\nThe location of these directories depends on the operating system,\nwhere the application is run.\n\nIn general these directories need to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\nTherefore, it is also allowed to create all of these folders via\nthe `mkdir` command.\n\n#### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n", "type": "string", "const": "fs:default"}, {"description": "This allows non-recursive read access to metadata of the application folders, including file listing and statistics.", "type": "string", "const": "fs:allow-app-meta"}, {"description": "This allows full recursive read access to metadata of the application folders, including file listing and statistics.", "type": "string", "const": "fs:allow-app-meta-recursive"}, {"description": "This allows non-recursive read access to the application folders.", "type": "string", "const": "fs:allow-app-read"}, {"description": "This allows full recursive read access to the complete application folders, files and subdirectories.", "type": "string", "const": "fs:allow-app-read-recursive"}, {"description": "This allows non-recursive write access to the application folders.", "type": "string", "const": "fs:allow-app-write"}, {"description": "This allows full recursive write access to the complete application folders, files and subdirectories.", "type": "string", "const": "fs:allow-app-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appcache-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appcache-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPCACHE` folder.", "type": "string", "const": "fs:allow-appcache-read"}, {"description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appcache-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPCACHE` folder.", "type": "string", "const": "fs:allow-appcache-write"}, {"description": "This allows full recursive write access to the complete `$APPCACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appcache-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appconfig-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appconfig-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPCONFIG` folder.", "type": "string", "const": "fs:allow-appconfig-read"}, {"description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appconfig-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPCONFIG` folder.", "type": "string", "const": "fs:allow-appconfig-write"}, {"description": "This allows full recursive write access to the complete `$APPCONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appconfig-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appdata-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-appdata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPDATA` folder.", "type": "string", "const": "fs:allow-appdata-read"}, {"description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appdata-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPDATA` folder.", "type": "string", "const": "fs:allow-appdata-write"}, {"description": "This allows full recursive write access to the complete `$APPDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-appdata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applocaldata-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applocaldata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:allow-applocaldata-read"}, {"description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applocaldata-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:allow-applocaldata-write"}, {"description": "This allows full recursive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applocaldata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applog-meta"}, {"description": "This allows full recursive read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-applog-meta-recursive"}, {"description": "This allows non-recursive read access to the `$APPLOG` folder.", "type": "string", "const": "fs:allow-applog-read"}, {"description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applog-read-recursive"}, {"description": "This allows non-recursive write access to the `$APPLOG` folder.", "type": "string", "const": "fs:allow-applog-write"}, {"description": "This allows full recursive write access to the complete `$APPLOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-applog-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-audio-meta"}, {"description": "This allows full recursive read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-audio-meta-recursive"}, {"description": "This allows non-recursive read access to the `$AUDIO` folder.", "type": "string", "const": "fs:allow-audio-read"}, {"description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-audio-read-recursive"}, {"description": "This allows non-recursive write access to the `$AUDIO` folder.", "type": "string", "const": "fs:allow-audio-write"}, {"description": "This allows full recursive write access to the complete `$AUDIO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-audio-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-cache-meta"}, {"description": "This allows full recursive read access to metadata of the `$CACHE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-cache-meta-recursive"}, {"description": "This allows non-recursive read access to the `$CACHE` folder.", "type": "string", "const": "fs:allow-cache-read"}, {"description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-cache-read-recursive"}, {"description": "This allows non-recursive write access to the `$CACHE` folder.", "type": "string", "const": "fs:allow-cache-write"}, {"description": "This allows full recursive write access to the complete `$CACHE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-cache-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-config-meta"}, {"description": "This allows full recursive read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-config-meta-recursive"}, {"description": "This allows non-recursive read access to the `$CONFIG` folder.", "type": "string", "const": "fs:allow-config-read"}, {"description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-config-read-recursive"}, {"description": "This allows non-recursive write access to the `$CONFIG` folder.", "type": "string", "const": "fs:allow-config-write"}, {"description": "This allows full recursive write access to the complete `$CONFIG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-config-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-data-meta"}, {"description": "This allows full recursive read access to metadata of the `$DATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-data-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DATA` folder.", "type": "string", "const": "fs:allow-data-read"}, {"description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-data-read-recursive"}, {"description": "This allows non-recursive write access to the `$DATA` folder.", "type": "string", "const": "fs:allow-data-write"}, {"description": "This allows full recursive write access to the complete `$DATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-data-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-desktop-meta"}, {"description": "This allows full recursive read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-desktop-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DESKTOP` folder.", "type": "string", "const": "fs:allow-desktop-read"}, {"description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-desktop-read-recursive"}, {"description": "This allows non-recursive write access to the `$DESKTOP` folder.", "type": "string", "const": "fs:allow-desktop-write"}, {"description": "This allows full recursive write access to the complete `$DESKTOP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-desktop-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-document-meta"}, {"description": "This allows full recursive read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-document-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DOCUMENT` folder.", "type": "string", "const": "fs:allow-document-read"}, {"description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-document-read-recursive"}, {"description": "This allows non-recursive write access to the `$DOCUMENT` folder.", "type": "string", "const": "fs:allow-document-write"}, {"description": "This allows full recursive write access to the complete `$DOCUMENT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-document-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-download-meta"}, {"description": "This allows full recursive read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-download-meta-recursive"}, {"description": "This allows non-recursive read access to the `$DOWNLOAD` folder.", "type": "string", "const": "fs:allow-download-read"}, {"description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.", "type": "string", "const": "fs:allow-download-read-recursive"}, {"description": "This allows non-recursive write access to the `$DOWNLOAD` folder.", "type": "string", "const": "fs:allow-download-write"}, {"description": "This allows full recursive write access to the complete `$DOWNLOAD` folder, files and subdirectories.", "type": "string", "const": "fs:allow-download-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-exe-meta"}, {"description": "This allows full recursive read access to metadata of the `$EXE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-exe-meta-recursive"}, {"description": "This allows non-recursive read access to the `$EXE` folder.", "type": "string", "const": "fs:allow-exe-read"}, {"description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-exe-read-recursive"}, {"description": "This allows non-recursive write access to the `$EXE` folder.", "type": "string", "const": "fs:allow-exe-write"}, {"description": "This allows full recursive write access to the complete `$EXE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-exe-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-font-meta"}, {"description": "This allows full recursive read access to metadata of the `$FONT` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-font-meta-recursive"}, {"description": "This allows non-recursive read access to the `$FONT` folder.", "type": "string", "const": "fs:allow-font-read"}, {"description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-font-read-recursive"}, {"description": "This allows non-recursive write access to the `$FONT` folder.", "type": "string", "const": "fs:allow-font-write"}, {"description": "This allows full recursive write access to the complete `$FONT` folder, files and subdirectories.", "type": "string", "const": "fs:allow-font-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-home-meta"}, {"description": "This allows full recursive read access to metadata of the `$HOME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-home-meta-recursive"}, {"description": "This allows non-recursive read access to the `$HOME` folder.", "type": "string", "const": "fs:allow-home-read"}, {"description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-home-read-recursive"}, {"description": "This allows non-recursive write access to the `$HOME` folder.", "type": "string", "const": "fs:allow-home-write"}, {"description": "This allows full recursive write access to the complete `$HOME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-home-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-localdata-meta"}, {"description": "This allows full recursive read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-localdata-meta-recursive"}, {"description": "This allows non-recursive read access to the `$LOCALDATA` folder.", "type": "string", "const": "fs:allow-localdata-read"}, {"description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-localdata-read-recursive"}, {"description": "This allows non-recursive write access to the `$LOCALDATA` folder.", "type": "string", "const": "fs:allow-localdata-write"}, {"description": "This allows full recursive write access to the complete `$LOCALDATA` folder, files and subdirectories.", "type": "string", "const": "fs:allow-localdata-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-log-meta"}, {"description": "This allows full recursive read access to metadata of the `$LOG` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-log-meta-recursive"}, {"description": "This allows non-recursive read access to the `$LOG` folder.", "type": "string", "const": "fs:allow-log-read"}, {"description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-log-read-recursive"}, {"description": "This allows non-recursive write access to the `$LOG` folder.", "type": "string", "const": "fs:allow-log-write"}, {"description": "This allows full recursive write access to the complete `$LOG` folder, files and subdirectories.", "type": "string", "const": "fs:allow-log-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-picture-meta"}, {"description": "This allows full recursive read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-picture-meta-recursive"}, {"description": "This allows non-recursive read access to the `$PICTURE` folder.", "type": "string", "const": "fs:allow-picture-read"}, {"description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-picture-read-recursive"}, {"description": "This allows non-recursive write access to the `$PICTURE` folder.", "type": "string", "const": "fs:allow-picture-write"}, {"description": "This allows full recursive write access to the complete `$PICTURE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-picture-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-public-meta"}, {"description": "This allows full recursive read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-public-meta-recursive"}, {"description": "This allows non-recursive read access to the `$PUBLIC` folder.", "type": "string", "const": "fs:allow-public-read"}, {"description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.", "type": "string", "const": "fs:allow-public-read-recursive"}, {"description": "This allows non-recursive write access to the `$PUBLIC` folder.", "type": "string", "const": "fs:allow-public-write"}, {"description": "This allows full recursive write access to the complete `$PUBLIC` folder, files and subdirectories.", "type": "string", "const": "fs:allow-public-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-resource-meta"}, {"description": "This allows full recursive read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-resource-meta-recursive"}, {"description": "This allows non-recursive read access to the `$RESOURCE` folder.", "type": "string", "const": "fs:allow-resource-read"}, {"description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-resource-read-recursive"}, {"description": "This allows non-recursive write access to the `$RESOURCE` folder.", "type": "string", "const": "fs:allow-resource-write"}, {"description": "This allows full recursive write access to the complete `$RESOURCE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-resource-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-runtime-meta"}, {"description": "This allows full recursive read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-runtime-meta-recursive"}, {"description": "This allows non-recursive read access to the `$RUNTIME` folder.", "type": "string", "const": "fs:allow-runtime-read"}, {"description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-runtime-read-recursive"}, {"description": "This allows non-recursive write access to the `$RUNTIME` folder.", "type": "string", "const": "fs:allow-runtime-write"}, {"description": "This allows full recursive write access to the complete `$RUNTIME` folder, files and subdirectories.", "type": "string", "const": "fs:allow-runtime-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-temp-meta"}, {"description": "This allows full recursive read access to metadata of the `$TEMP` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-temp-meta-recursive"}, {"description": "This allows non-recursive read access to the `$TEMP` folder.", "type": "string", "const": "fs:allow-temp-read"}, {"description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-temp-read-recursive"}, {"description": "This allows non-recursive write access to the `$TEMP` folder.", "type": "string", "const": "fs:allow-temp-write"}, {"description": "This allows full recursive write access to the complete `$TEMP` folder, files and subdirectories.", "type": "string", "const": "fs:allow-temp-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-template-meta"}, {"description": "This allows full recursive read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-template-meta-recursive"}, {"description": "This allows non-recursive read access to the `$TEMPLATE` folder.", "type": "string", "const": "fs:allow-template-read"}, {"description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-template-read-recursive"}, {"description": "This allows non-recursive write access to the `$TEMPLATE` folder.", "type": "string", "const": "fs:allow-template-write"}, {"description": "This allows full recursive write access to the complete `$TEMPLATE` folder, files and subdirectories.", "type": "string", "const": "fs:allow-template-write-recursive"}, {"description": "This allows non-recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-video-meta"}, {"description": "This allows full recursive read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "type": "string", "const": "fs:allow-video-meta-recursive"}, {"description": "This allows non-recursive read access to the `$VIDEO` folder.", "type": "string", "const": "fs:allow-video-read"}, {"description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-video-read-recursive"}, {"description": "This allows non-recursive write access to the `$VIDEO` folder.", "type": "string", "const": "fs:allow-video-write"}, {"description": "This allows full recursive write access to the complete `$VIDEO` folder, files and subdirectories.", "type": "string", "const": "fs:allow-video-write-recursive"}, {"description": "This denies access to dangerous Tauri relevant files and folders by default.", "type": "string", "const": "fs:deny-default"}, {"description": "Enables the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-copy-file"}, {"description": "Enables the create command without any pre-configured scope.", "type": "string", "const": "fs:allow-create"}, {"description": "Enables the exists command without any pre-configured scope.", "type": "string", "const": "fs:allow-exists"}, {"description": "Enables the fstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-fstat"}, {"description": "Enables the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-ftruncate"}, {"description": "Enables the lstat command without any pre-configured scope.", "type": "string", "const": "fs:allow-lstat"}, {"description": "Enables the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:allow-mkdir"}, {"description": "Enables the open command without any pre-configured scope.", "type": "string", "const": "fs:allow-open"}, {"description": "Enables the read command without any pre-configured scope.", "type": "string", "const": "fs:allow-read"}, {"description": "Enables the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-dir"}, {"description": "Enables the read_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-file"}, {"description": "Enables the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file"}, {"description": "Enables the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines"}, {"description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:allow-read-text-file-lines-next"}, {"description": "Enables the remove command without any pre-configured scope.", "type": "string", "const": "fs:allow-remove"}, {"description": "Enables the rename command without any pre-configured scope.", "type": "string", "const": "fs:allow-rename"}, {"description": "Enables the seek command without any pre-configured scope.", "type": "string", "const": "fs:allow-seek"}, {"description": "Enables the stat command without any pre-configured scope.", "type": "string", "const": "fs:allow-stat"}, {"description": "Enables the truncate command without any pre-configured scope.", "type": "string", "const": "fs:allow-truncate"}, {"description": "Enables the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:allow-unwatch"}, {"description": "Enables the watch command without any pre-configured scope.", "type": "string", "const": "fs:allow-watch"}, {"description": "Enables the write command without any pre-configured scope.", "type": "string", "const": "fs:allow-write"}, {"description": "Enables the write_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-file"}, {"description": "Enables the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:allow-write-text-file"}, {"description": "This permissions allows to create the application specific directories.\n", "type": "string", "const": "fs:create-app-specific-dirs"}, {"description": "Denies the copy_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-copy-file"}, {"description": "Denies the create command without any pre-configured scope.", "type": "string", "const": "fs:deny-create"}, {"description": "Denies the exists command without any pre-configured scope.", "type": "string", "const": "fs:deny-exists"}, {"description": "Denies the fstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-fstat"}, {"description": "Denies the ftruncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-ftruncate"}, {"description": "Denies the lstat command without any pre-configured scope.", "type": "string", "const": "fs:deny-lstat"}, {"description": "Denies the mkdir command without any pre-configured scope.", "type": "string", "const": "fs:deny-mkdir"}, {"description": "Denies the open command without any pre-configured scope.", "type": "string", "const": "fs:deny-open"}, {"description": "Denies the read command without any pre-configured scope.", "type": "string", "const": "fs:deny-read"}, {"description": "Denies the read_dir command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-dir"}, {"description": "Denies the read_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-file"}, {"description": "Denies the read_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file"}, {"description": "Denies the read_text_file_lines command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines"}, {"description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "type": "string", "const": "fs:deny-read-text-file-lines-next"}, {"description": "Denies the remove command without any pre-configured scope.", "type": "string", "const": "fs:deny-remove"}, {"description": "Denies the rename command without any pre-configured scope.", "type": "string", "const": "fs:deny-rename"}, {"description": "Denies the seek command without any pre-configured scope.", "type": "string", "const": "fs:deny-seek"}, {"description": "Denies the stat command without any pre-configured scope.", "type": "string", "const": "fs:deny-stat"}, {"description": "Denies the truncate command without any pre-configured scope.", "type": "string", "const": "fs:deny-truncate"}, {"description": "Denies the unwatch command without any pre-configured scope.", "type": "string", "const": "fs:deny-unwatch"}, {"description": "Denies the watch command without any pre-configured scope.", "type": "string", "const": "fs:deny-watch"}, {"description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-linux"}, {"description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "type": "string", "const": "fs:deny-webview-data-windows"}, {"description": "Denies the write command without any pre-configured scope.", "type": "string", "const": "fs:deny-write"}, {"description": "Denies the write_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-file"}, {"description": "Denies the write_text_file command without any pre-configured scope.", "type": "string", "const": "fs:deny-write-text-file"}, {"description": "This enables all read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-all"}, {"description": "This permission allows recursive read functionality on the application\nspecific base directories. \n", "type": "string", "const": "fs:read-app-specific-dirs-recursive"}, {"description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-dirs"}, {"description": "This enables file read related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-files"}, {"description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:read-meta"}, {"description": "An empty permission you can use to modify the global scope.", "type": "string", "const": "fs:scope"}, {"description": "This scope permits access to all files and list content of top level directories in the application folders.", "type": "string", "const": "fs:scope-app"}, {"description": "This scope permits to list all files and folders in the application directories.", "type": "string", "const": "fs:scope-app-index"}, {"description": "This scope permits recursive access to the complete application folders, including sub directories and files.", "type": "string", "const": "fs:scope-app-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE` folder.", "type": "string", "const": "fs:scope-appcache"}, {"description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "type": "string", "const": "fs:scope-appcache-index"}, {"description": "This scope permits recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appcache-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG` folder.", "type": "string", "const": "fs:scope-appconfig"}, {"description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "type": "string", "const": "fs:scope-appconfig-index"}, {"description": "This scope permits recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appconfig-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPDATA` folder.", "type": "string", "const": "fs:scope-appdata"}, {"description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "type": "string", "const": "fs:scope-appdata-index"}, {"description": "This scope permits recursive access to the complete `$APPDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-appdata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA` folder.", "type": "string", "const": "fs:scope-applocaldata"}, {"description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "type": "string", "const": "fs:scope-applocaldata-index"}, {"description": "This scope permits recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applocaldata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$APPLOG` folder.", "type": "string", "const": "fs:scope-applog"}, {"description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "type": "string", "const": "fs:scope-applog-index"}, {"description": "This scope permits recursive access to the complete `$APPLOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-applog-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$AUDIO` folder.", "type": "string", "const": "fs:scope-audio"}, {"description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "type": "string", "const": "fs:scope-audio-index"}, {"description": "This scope permits recursive access to the complete `$AUDIO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-audio-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$CACHE` folder.", "type": "string", "const": "fs:scope-cache"}, {"description": "This scope permits to list all files and folders in the `$CACHE`folder.", "type": "string", "const": "fs:scope-cache-index"}, {"description": "This scope permits recursive access to the complete `$CACHE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-cache-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$CONFIG` folder.", "type": "string", "const": "fs:scope-config"}, {"description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "type": "string", "const": "fs:scope-config-index"}, {"description": "This scope permits recursive access to the complete `$CONFIG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-config-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DATA` folder.", "type": "string", "const": "fs:scope-data"}, {"description": "This scope permits to list all files and folders in the `$DATA`folder.", "type": "string", "const": "fs:scope-data-index"}, {"description": "This scope permits recursive access to the complete `$DATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-data-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP` folder.", "type": "string", "const": "fs:scope-desktop"}, {"description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "type": "string", "const": "fs:scope-desktop-index"}, {"description": "This scope permits recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-desktop-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT` folder.", "type": "string", "const": "fs:scope-document"}, {"description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "type": "string", "const": "fs:scope-document-index"}, {"description": "This scope permits recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-document-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD` folder.", "type": "string", "const": "fs:scope-download"}, {"description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "type": "string", "const": "fs:scope-download-index"}, {"description": "This scope permits recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "type": "string", "const": "fs:scope-download-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$EXE` folder.", "type": "string", "const": "fs:scope-exe"}, {"description": "This scope permits to list all files and folders in the `$EXE`folder.", "type": "string", "const": "fs:scope-exe-index"}, {"description": "This scope permits recursive access to the complete `$EXE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-exe-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$FONT` folder.", "type": "string", "const": "fs:scope-font"}, {"description": "This scope permits to list all files and folders in the `$FONT`folder.", "type": "string", "const": "fs:scope-font-index"}, {"description": "This scope permits recursive access to the complete `$FONT` folder, including sub directories and files.", "type": "string", "const": "fs:scope-font-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$HOME` folder.", "type": "string", "const": "fs:scope-home"}, {"description": "This scope permits to list all files and folders in the `$HOME`folder.", "type": "string", "const": "fs:scope-home-index"}, {"description": "This scope permits recursive access to the complete `$HOME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-home-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA` folder.", "type": "string", "const": "fs:scope-localdata"}, {"description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "type": "string", "const": "fs:scope-localdata-index"}, {"description": "This scope permits recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "type": "string", "const": "fs:scope-localdata-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$LOG` folder.", "type": "string", "const": "fs:scope-log"}, {"description": "This scope permits to list all files and folders in the `$LOG`folder.", "type": "string", "const": "fs:scope-log-index"}, {"description": "This scope permits recursive access to the complete `$LOG` folder, including sub directories and files.", "type": "string", "const": "fs:scope-log-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$PICTURE` folder.", "type": "string", "const": "fs:scope-picture"}, {"description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "type": "string", "const": "fs:scope-picture-index"}, {"description": "This scope permits recursive access to the complete `$PICTURE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-picture-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC` folder.", "type": "string", "const": "fs:scope-public"}, {"description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "type": "string", "const": "fs:scope-public-index"}, {"description": "This scope permits recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "type": "string", "const": "fs:scope-public-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE` folder.", "type": "string", "const": "fs:scope-resource"}, {"description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "type": "string", "const": "fs:scope-resource-index"}, {"description": "This scope permits recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-resource-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME` folder.", "type": "string", "const": "fs:scope-runtime"}, {"description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "type": "string", "const": "fs:scope-runtime-index"}, {"description": "This scope permits recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "type": "string", "const": "fs:scope-runtime-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMP` folder.", "type": "string", "const": "fs:scope-temp"}, {"description": "This scope permits to list all files and folders in the `$TEMP`folder.", "type": "string", "const": "fs:scope-temp-index"}, {"description": "This scope permits recursive access to the complete `$TEMP` folder, including sub directories and files.", "type": "string", "const": "fs:scope-temp-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE` folder.", "type": "string", "const": "fs:scope-template"}, {"description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "type": "string", "const": "fs:scope-template-index"}, {"description": "This scope permits recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "type": "string", "const": "fs:scope-template-recursive"}, {"description": "This scope permits access to all files and list content of top level directories in the `$VIDEO` folder.", "type": "string", "const": "fs:scope-video"}, {"description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "type": "string", "const": "fs:scope-video-index"}, {"description": "This scope permits recursive access to the complete `$VIDEO` folder, including sub directories and files.", "type": "string", "const": "fs:scope-video-recursive"}, {"description": "This enables all write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-all"}, {"description": "This enables all file write related commands without any pre-configured accessible paths.", "type": "string", "const": "fs:write-files"}, {"description": "This permission set configures what kind of\nfetch operations are available from the http plugin.\n\nThis enables all fetch operations but does not\nallow explicitly any origins to be fetched. This needs to\nbe manually configured before usage.\n\n#### Granted Permissions\n\nAll fetch operations are enabled.\n\n", "type": "string", "const": "http:default"}, {"description": "Enables the fetch command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch"}, {"description": "Enables the fetch_cancel command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-cancel"}, {"description": "Enables the fetch_read_body command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-read-body"}, {"description": "Enables the fetch_send command without any pre-configured scope.", "type": "string", "const": "http:allow-fetch-send"}, {"description": "Denies the fetch command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch"}, {"description": "Denies the fetch_cancel command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-cancel"}, {"description": "Denies the fetch_read_body command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-read-body"}, {"description": "Denies the fetch_send command without any pre-configured scope.", "type": "string", "const": "http:deny-fetch-send"}, {"description": "Allows the log command", "type": "string", "const": "log:default"}, {"description": "Enables the log command without any pre-configured scope.", "type": "string", "const": "log:allow-log"}, {"description": "Denies the log command without any pre-configured scope.", "type": "string", "const": "log:deny-log"}, {"description": "This permission set configures which kind of\nupdater functions are exposed to the frontend.\n\n#### Granted Permissions\n\nThe full workflow from checking for updates to installing them\nis enabled.\n\n", "type": "string", "const": "updater:default"}, {"description": "Enables the check command without any pre-configured scope.", "type": "string", "const": "updater:allow-check"}, {"description": "Enables the download command without any pre-configured scope.", "type": "string", "const": "updater:allow-download"}, {"description": "Enables the download_and_install command without any pre-configured scope.", "type": "string", "const": "updater:allow-download-and-install"}, {"description": "Enables the install command without any pre-configured scope.", "type": "string", "const": "updater:allow-install"}, {"description": "Denies the check command without any pre-configured scope.", "type": "string", "const": "updater:deny-check"}, {"description": "Denies the download command without any pre-configured scope.", "type": "string", "const": "updater:deny-download"}, {"description": "Denies the download_and_install command without any pre-configured scope.", "type": "string", "const": "updater:deny-download-and-install"}, {"description": "Denies the install command without any pre-configured scope.", "type": "string", "const": "updater:deny-install"}]}, "Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}, "Target": {"description": "Platform target.", "oneOf": [{"description": "MacOS.", "type": "string", "enum": ["macOS"]}, {"description": "Windows.", "type": "string", "enum": ["windows"]}, {"description": "Linux.", "type": "string", "enum": ["linux"]}, {"description": "Android.", "type": "string", "enum": ["android"]}, {"description": "iOS.", "type": "string", "enum": ["iOS"]}]}}}