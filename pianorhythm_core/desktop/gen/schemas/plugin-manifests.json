{"app": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-version", "allow-name", "allow-tauri-version"]}, "permissions": {"allow-app-hide": {"version": null, "identifier": "allow-app-hide", "description": "Enables the app_hide command without any pre-configured scope.", "commands": {"allow": ["app_hide"], "deny": []}, "scope": {}}, "allow-app-show": {"version": null, "identifier": "allow-app-show", "description": "Enables the app_show command without any pre-configured scope.", "commands": {"allow": ["app_show"], "deny": []}, "scope": {}}, "allow-name": {"version": null, "identifier": "allow-name", "description": "Enables the name command without any pre-configured scope.", "commands": {"allow": ["name"], "deny": []}, "scope": {}}, "allow-tauri-version": {"version": null, "identifier": "allow-tauri-version", "description": "Enables the tauri_version command without any pre-configured scope.", "commands": {"allow": ["tauri_version"], "deny": []}, "scope": {}}, "allow-version": {"version": null, "identifier": "allow-version", "description": "Enables the version command without any pre-configured scope.", "commands": {"allow": ["version"], "deny": []}, "scope": {}}, "deny-app-hide": {"version": null, "identifier": "deny-app-hide", "description": "Denies the app_hide command without any pre-configured scope.", "commands": {"allow": [], "deny": ["app_hide"]}, "scope": {}}, "deny-app-show": {"version": null, "identifier": "deny-app-show", "description": "Denies the app_show command without any pre-configured scope.", "commands": {"allow": [], "deny": ["app_show"]}, "scope": {}}, "deny-name": {"version": null, "identifier": "deny-name", "description": "Denies the name command without any pre-configured scope.", "commands": {"allow": [], "deny": ["name"]}, "scope": {}}, "deny-tauri-version": {"version": null, "identifier": "deny-tauri-version", "description": "Denies the tauri_version command without any pre-configured scope.", "commands": {"allow": [], "deny": ["tauri_version"]}, "scope": {}}, "deny-version": {"version": null, "identifier": "deny-version", "description": "Denies the version command without any pre-configured scope.", "commands": {"allow": [], "deny": ["version"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "dialog": {"default_permission": null, "permissions": {"allow-ask": {"version": null, "identifier": "allow-ask", "description": "Enables the ask command without any pre-configured scope.", "commands": {"allow": ["ask"], "deny": []}, "scope": {}}, "allow-confirm": {"version": null, "identifier": "allow-confirm", "description": "Enables the confirm command without any pre-configured scope.", "commands": {"allow": ["confirm"], "deny": []}, "scope": {}}, "allow-message": {"version": null, "identifier": "allow-message", "description": "Enables the message command without any pre-configured scope.", "commands": {"allow": ["message"], "deny": []}, "scope": {}}, "allow-open": {"version": null, "identifier": "allow-open", "description": "Enables the open command without any pre-configured scope.", "commands": {"allow": ["open"], "deny": []}, "scope": {}}, "allow-save": {"version": null, "identifier": "allow-save", "description": "Enables the save command without any pre-configured scope.", "commands": {"allow": ["save"], "deny": []}, "scope": {}}, "deny-ask": {"version": null, "identifier": "deny-ask", "description": "Denies the ask command without any pre-configured scope.", "commands": {"allow": [], "deny": ["ask"]}, "scope": {}}, "deny-confirm": {"version": null, "identifier": "deny-confirm", "description": "Denies the confirm command without any pre-configured scope.", "commands": {"allow": [], "deny": ["confirm"]}, "scope": {}}, "deny-message": {"version": null, "identifier": "deny-message", "description": "Denies the message command without any pre-configured scope.", "commands": {"allow": [], "deny": ["message"]}, "scope": {}}, "deny-open": {"version": null, "identifier": "deny-open", "description": "Denies the open command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open"]}, "scope": {}}, "deny-save": {"version": null, "identifier": "deny-save", "description": "Denies the save command without any pre-configured scope.", "commands": {"allow": [], "deny": ["save"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "event": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-listen", "allow-unlisten", "allow-emit", "allow-emit-to"]}, "permissions": {"allow-emit": {"version": null, "identifier": "allow-emit", "description": "Enables the emit command without any pre-configured scope.", "commands": {"allow": ["emit"], "deny": []}, "scope": {}}, "allow-emit-to": {"version": null, "identifier": "allow-emit-to", "description": "Enables the emit_to command without any pre-configured scope.", "commands": {"allow": ["emit_to"], "deny": []}, "scope": {}}, "allow-listen": {"version": null, "identifier": "allow-listen", "description": "Enables the listen command without any pre-configured scope.", "commands": {"allow": ["listen"], "deny": []}, "scope": {}}, "allow-unlisten": {"version": null, "identifier": "allow-unlisten", "description": "Enables the unlisten command without any pre-configured scope.", "commands": {"allow": ["unlisten"], "deny": []}, "scope": {}}, "deny-emit": {"version": null, "identifier": "deny-emit", "description": "Denies the emit command without any pre-configured scope.", "commands": {"allow": [], "deny": ["emit"]}, "scope": {}}, "deny-emit-to": {"version": null, "identifier": "deny-emit-to", "description": "Denies the emit_to command without any pre-configured scope.", "commands": {"allow": [], "deny": ["emit_to"]}, "scope": {}}, "deny-listen": {"version": null, "identifier": "deny-listen", "description": "Denies the listen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["listen"]}, "scope": {}}, "deny-unlisten": {"version": null, "identifier": "deny-unlisten", "description": "Denies the unlisten command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unlisten"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "fs": {"default_permission": {"identifier": "default", "description": "# Tauri `fs` default permissions\n\nThis configuration file defines the default permissions granted\nto the filesystem.\n\n### Granted Permissions\n\nThis default permission set enables all read-related commands and\nallows access to the `$APP` folder and sub directories created in it.\nThe location of the `$APP` folder depends on the operating system,\nwhere the application is run.\n\nIn general the `$APP` folder needs to be manually created\nby the application at runtime, before accessing files or folders\nin it is possible.\n\n### Denied Permissions\n\nThis default permission set prevents access to critical components\nof the Tauri application by default.\nOn Windows the webview data folder access is denied.\n\n", "permissions": ["read-all", "scope-app-recursive", "deny-default"]}, "permissions": {"allow-copy-file": {"version": null, "identifier": "allow-copy-file", "description": "Enables the copy_file command without any pre-configured scope.", "commands": {"allow": ["copy_file"], "deny": []}, "scope": {}}, "allow-create": {"version": null, "identifier": "allow-create", "description": "Enables the create command without any pre-configured scope.", "commands": {"allow": ["create"], "deny": []}, "scope": {}}, "allow-exists": {"version": null, "identifier": "allow-exists", "description": "Enables the exists command without any pre-configured scope.", "commands": {"allow": ["exists"], "deny": []}, "scope": {}}, "allow-fstat": {"version": null, "identifier": "allow-fstat", "description": "Enables the fstat command without any pre-configured scope.", "commands": {"allow": ["fstat"], "deny": []}, "scope": {}}, "allow-ftruncate": {"version": null, "identifier": "allow-ftruncate", "description": "Enables the ftruncate command without any pre-configured scope.", "commands": {"allow": ["ftrun<PERSON>"], "deny": []}, "scope": {}}, "allow-lstat": {"version": null, "identifier": "allow-lstat", "description": "Enables the lstat command without any pre-configured scope.", "commands": {"allow": ["lstat"], "deny": []}, "scope": {}}, "allow-mkdir": {"version": null, "identifier": "allow-mkdir", "description": "Enables the mkdir command without any pre-configured scope.", "commands": {"allow": ["mkdir"], "deny": []}, "scope": {}}, "allow-open": {"version": null, "identifier": "allow-open", "description": "Enables the open command without any pre-configured scope.", "commands": {"allow": ["open"], "deny": []}, "scope": {}}, "allow-read": {"version": null, "identifier": "allow-read", "description": "Enables the read command without any pre-configured scope.", "commands": {"allow": ["read"], "deny": []}, "scope": {}}, "allow-read-dir": {"version": null, "identifier": "allow-read-dir", "description": "Enables the read_dir command without any pre-configured scope.", "commands": {"allow": ["read_dir"], "deny": []}, "scope": {}}, "allow-read-file": {"version": null, "identifier": "allow-read-file", "description": "Enables the read_file command without any pre-configured scope.", "commands": {"allow": ["read_file"], "deny": []}, "scope": {}}, "allow-read-text-file": {"version": null, "identifier": "allow-read-text-file", "description": "Enables the read_text_file command without any pre-configured scope.", "commands": {"allow": ["read_text_file"], "deny": []}, "scope": {}}, "allow-read-text-file-lines": {"version": null, "identifier": "allow-read-text-file-lines", "description": "Enables the read_text_file_lines command without any pre-configured scope.", "commands": {"allow": ["read_text_file_lines"], "deny": []}, "scope": {}}, "allow-read-text-file-lines-next": {"version": null, "identifier": "allow-read-text-file-lines-next", "description": "Enables the read_text_file_lines_next command without any pre-configured scope.", "commands": {"allow": ["read_text_file_lines_next"], "deny": []}, "scope": {}}, "allow-remove": {"version": null, "identifier": "allow-remove", "description": "Enables the remove command without any pre-configured scope.", "commands": {"allow": ["remove"], "deny": []}, "scope": {}}, "allow-rename": {"version": null, "identifier": "allow-rename", "description": "Enables the rename command without any pre-configured scope.", "commands": {"allow": ["rename"], "deny": []}, "scope": {}}, "allow-seek": {"version": null, "identifier": "allow-seek", "description": "Enables the seek command without any pre-configured scope.", "commands": {"allow": ["seek"], "deny": []}, "scope": {}}, "allow-stat": {"version": null, "identifier": "allow-stat", "description": "Enables the stat command without any pre-configured scope.", "commands": {"allow": ["stat"], "deny": []}, "scope": {}}, "allow-truncate": {"version": null, "identifier": "allow-truncate", "description": "Enables the truncate command without any pre-configured scope.", "commands": {"allow": ["truncate"], "deny": []}, "scope": {}}, "allow-unwatch": {"version": null, "identifier": "allow-unwatch", "description": "Enables the unwatch command without any pre-configured scope.", "commands": {"allow": ["unwatch"], "deny": []}, "scope": {}}, "allow-watch": {"version": null, "identifier": "allow-watch", "description": "Enables the watch command without any pre-configured scope.", "commands": {"allow": ["watch"], "deny": []}, "scope": {}}, "allow-write": {"version": null, "identifier": "allow-write", "description": "Enables the write command without any pre-configured scope.", "commands": {"allow": ["write"], "deny": []}, "scope": {}}, "allow-write-file": {"version": null, "identifier": "allow-write-file", "description": "Enables the write_file command without any pre-configured scope.", "commands": {"allow": ["write_file"], "deny": []}, "scope": {}}, "allow-write-text-file": {"version": null, "identifier": "allow-write-text-file", "description": "Enables the write_text_file command without any pre-configured scope.", "commands": {"allow": ["write_text_file"], "deny": []}, "scope": {}}, "deny-copy-file": {"version": null, "identifier": "deny-copy-file", "description": "Denies the copy_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["copy_file"]}, "scope": {}}, "deny-create": {"version": null, "identifier": "deny-create", "description": "Denies the create command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create"]}, "scope": {}}, "deny-exists": {"version": null, "identifier": "deny-exists", "description": "Denies the exists command without any pre-configured scope.", "commands": {"allow": [], "deny": ["exists"]}, "scope": {}}, "deny-fstat": {"version": null, "identifier": "deny-fstat", "description": "Denies the fstat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fstat"]}, "scope": {}}, "deny-ftruncate": {"version": null, "identifier": "deny-ftruncate", "description": "Denies the ftruncate command without any pre-configured scope.", "commands": {"allow": [], "deny": ["ftrun<PERSON>"]}, "scope": {}}, "deny-lstat": {"version": null, "identifier": "deny-lstat", "description": "Denies the lstat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["lstat"]}, "scope": {}}, "deny-mkdir": {"version": null, "identifier": "deny-mkdir", "description": "Denies the mkdir command without any pre-configured scope.", "commands": {"allow": [], "deny": ["mkdir"]}, "scope": {}}, "deny-open": {"version": null, "identifier": "deny-open", "description": "Denies the open command without any pre-configured scope.", "commands": {"allow": [], "deny": ["open"]}, "scope": {}}, "deny-read": {"version": null, "identifier": "deny-read", "description": "Denies the read command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read"]}, "scope": {}}, "deny-read-dir": {"version": null, "identifier": "deny-read-dir", "description": "Denies the read_dir command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_dir"]}, "scope": {}}, "deny-read-file": {"version": null, "identifier": "deny-read-file", "description": "Denies the read_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_file"]}, "scope": {}}, "deny-read-text-file": {"version": null, "identifier": "deny-read-text-file", "description": "Denies the read_text_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file"]}, "scope": {}}, "deny-read-text-file-lines": {"version": null, "identifier": "deny-read-text-file-lines", "description": "Denies the read_text_file_lines command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file_lines"]}, "scope": {}}, "deny-read-text-file-lines-next": {"version": null, "identifier": "deny-read-text-file-lines-next", "description": "Denies the read_text_file_lines_next command without any pre-configured scope.", "commands": {"allow": [], "deny": ["read_text_file_lines_next"]}, "scope": {}}, "deny-remove": {"version": null, "identifier": "deny-remove", "description": "Denies the remove command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove"]}, "scope": {}}, "deny-rename": {"version": null, "identifier": "deny-rename", "description": "Denies the rename command without any pre-configured scope.", "commands": {"allow": [], "deny": ["rename"]}, "scope": {}}, "deny-seek": {"version": null, "identifier": "deny-seek", "description": "Denies the seek command without any pre-configured scope.", "commands": {"allow": [], "deny": ["seek"]}, "scope": {}}, "deny-stat": {"version": null, "identifier": "deny-stat", "description": "Denies the stat command without any pre-configured scope.", "commands": {"allow": [], "deny": ["stat"]}, "scope": {}}, "deny-truncate": {"version": null, "identifier": "deny-truncate", "description": "Denies the truncate command without any pre-configured scope.", "commands": {"allow": [], "deny": ["truncate"]}, "scope": {}}, "deny-unwatch": {"version": null, "identifier": "deny-unwatch", "description": "Denies the unwatch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unwatch"]}, "scope": {}}, "deny-watch": {"version": null, "identifier": "deny-watch", "description": "Denies the watch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["watch"]}, "scope": {}}, "deny-webview-data-linux": {"version": null, "identifier": "deny-webview-data-linux", "description": "This denies read access to the\n`$APPLOCALDATA` folder on linux as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "commands": {"allow": [], "deny": []}, "scope": {}}, "deny-webview-data-windows": {"version": null, "identifier": "deny-webview-data-windows", "description": "This denies read access to the\n`$APPLOCALDATA/EBWebView` folder on windows as the webview data and configuration values are stored here.\nAllowing access can lead to sensitive information disclosure and should be well considered.", "commands": {"allow": [], "deny": []}, "scope": {}}, "deny-write": {"version": null, "identifier": "deny-write", "description": "Denies the write command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write"]}, "scope": {}}, "deny-write-file": {"version": null, "identifier": "deny-write-file", "description": "Denies the write_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write_file"]}, "scope": {}}, "deny-write-text-file": {"version": null, "identifier": "deny-write-text-file", "description": "Denies the write_text_file command without any pre-configured scope.", "commands": {"allow": [], "deny": ["write_text_file"]}, "scope": {}}, "read-all": {"version": null, "identifier": "read-all", "description": "This enables all read related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "read_file", "read", "open", "read_text_file", "read_text_file_lines", "read_text_file_lines_next", "seek", "stat", "lstat", "fstat", "exists", "watch", "unwatch"], "deny": []}, "scope": {}}, "read-dirs": {"version": null, "identifier": "read-dirs", "description": "This enables directory read and file metadata related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "stat", "lstat", "fstat", "exists"], "deny": []}, "scope": {}}, "read-files": {"version": null, "identifier": "read-files", "description": "This enables file read related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_file", "read", "open", "read_text_file", "read_text_file_lines", "read_text_file_lines_next", "seek", "stat", "lstat", "fstat", "exists"], "deny": []}, "scope": {}}, "read-meta": {"version": null, "identifier": "read-meta", "description": "This enables all index or metadata related commands without any pre-configured accessible paths.", "commands": {"allow": ["read_dir", "stat", "lstat", "fstat", "exists"], "deny": []}, "scope": {}}, "scope": {"version": null, "identifier": "scope", "description": "An empty permission you can use to modify the global scope.", "commands": {"allow": [], "deny": []}, "scope": {}}, "scope-app": {"version": null, "identifier": "scope-app", "description": "This scope permits access to all files and list content of top level directories in the `$APP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APP/*"}]}}, "scope-app-index": {"version": null, "identifier": "scope-app-index", "description": "This scope permits to list all files and folders in the `$APP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APP/"}]}}, "scope-app-recursive": {"version": null, "identifier": "scope-app-recursive", "description": "This scope recursive access to the complete `$APP` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APP/**"}]}}, "scope-appcache": {"version": null, "identifier": "scope-appcache", "description": "This scope permits access to all files and list content of top level directories in the `$APPCACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE/*"}]}}, "scope-appcache-index": {"version": null, "identifier": "scope-appcache-index", "description": "This scope permits to list all files and folders in the `$APPCACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE/"}]}}, "scope-appcache-recursive": {"version": null, "identifier": "scope-appcache-recursive", "description": "This scope recursive access to the complete `$APPCACHE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCACHE/**"}]}}, "scope-appconfig": {"version": null, "identifier": "scope-appconfig", "description": "This scope permits access to all files and list content of top level directories in the `$APPCONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG/*"}]}}, "scope-appconfig-index": {"version": null, "identifier": "scope-appconfig-index", "description": "This scope permits to list all files and folders in the `$APPCONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG/"}]}}, "scope-appconfig-recursive": {"version": null, "identifier": "scope-appconfig-recursive", "description": "This scope recursive access to the complete `$APPCONFIG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPCONFIG/**"}]}}, "scope-appdata": {"version": null, "identifier": "scope-appdata", "description": "This scope permits access to all files and list content of top level directories in the `$APPDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA/*"}]}}, "scope-appdata-index": {"version": null, "identifier": "scope-appdata-index", "description": "This scope permits to list all files and folders in the `$APPDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA/"}]}}, "scope-appdata-recursive": {"version": null, "identifier": "scope-appdata-recursive", "description": "This scope recursive access to the complete `$APPDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPDATA/**"}]}}, "scope-applocaldata": {"version": null, "identifier": "scope-applocaldata", "description": "This scope permits access to all files and list content of top level directories in the `$APPLOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA/*"}]}}, "scope-applocaldata-index": {"version": null, "identifier": "scope-applocaldata-index", "description": "This scope permits to list all files and folders in the `$APPLOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA/"}]}}, "scope-applocaldata-recursive": {"version": null, "identifier": "scope-applocaldata-recursive", "description": "This scope recursive access to the complete `$APPLOCALDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOCALDATA/**"}]}}, "scope-applog": {"version": null, "identifier": "scope-applog", "description": "This scope permits access to all files and list content of top level directories in the `$APPLOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG/*"}]}}, "scope-applog-index": {"version": null, "identifier": "scope-applog-index", "description": "This scope permits to list all files and folders in the `$APPLOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG/"}]}}, "scope-applog-recursive": {"version": null, "identifier": "scope-applog-recursive", "description": "This scope recursive access to the complete `$APPLOG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$APPLOG/**"}]}}, "scope-audio": {"version": null, "identifier": "scope-audio", "description": "This scope permits access to all files and list content of top level directories in the `$AUDIO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO/*"}]}}, "scope-audio-index": {"version": null, "identifier": "scope-audio-index", "description": "This scope permits to list all files and folders in the `$AUDIO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO/"}]}}, "scope-audio-recursive": {"version": null, "identifier": "scope-audio-recursive", "description": "This scope recursive access to the complete `$AUDIO` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$AUDIO/**"}]}}, "scope-cache": {"version": null, "identifier": "scope-cache", "description": "This scope permits access to all files and list content of top level directories in the `$CACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE/*"}]}}, "scope-cache-index": {"version": null, "identifier": "scope-cache-index", "description": "This scope permits to list all files and folders in the `$CACHE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE/"}]}}, "scope-cache-recursive": {"version": null, "identifier": "scope-cache-recursive", "description": "This scope recursive access to the complete `$CACHE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CACHE/**"}]}}, "scope-config": {"version": null, "identifier": "scope-config", "description": "This scope permits access to all files and list content of top level directories in the `$CONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG/*"}]}}, "scope-config-index": {"version": null, "identifier": "scope-config-index", "description": "This scope permits to list all files and folders in the `$CONFIG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG/"}]}}, "scope-config-recursive": {"version": null, "identifier": "scope-config-recursive", "description": "This scope recursive access to the complete `$CONFIG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$CONFIG/**"}]}}, "scope-data": {"version": null, "identifier": "scope-data", "description": "This scope permits access to all files and list content of top level directories in the `$DATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA/*"}]}}, "scope-data-index": {"version": null, "identifier": "scope-data-index", "description": "This scope permits to list all files and folders in the `$DATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA/"}]}}, "scope-data-recursive": {"version": null, "identifier": "scope-data-recursive", "description": "This scope recursive access to the complete `$DATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DATA/**"}]}}, "scope-desktop": {"version": null, "identifier": "scope-desktop", "description": "This scope permits access to all files and list content of top level directories in the `$DESKTOP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP/*"}]}}, "scope-desktop-index": {"version": null, "identifier": "scope-desktop-index", "description": "This scope permits to list all files and folders in the `$DESKTOP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP/"}]}}, "scope-desktop-recursive": {"version": null, "identifier": "scope-desktop-recursive", "description": "This scope recursive access to the complete `$DESKTOP` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DESKTOP/**"}]}}, "scope-document": {"version": null, "identifier": "scope-document", "description": "This scope permits access to all files and list content of top level directories in the `$DOCUMENT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT/*"}]}}, "scope-document-index": {"version": null, "identifier": "scope-document-index", "description": "This scope permits to list all files and folders in the `$DOCUMENT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT/"}]}}, "scope-document-recursive": {"version": null, "identifier": "scope-document-recursive", "description": "This scope recursive access to the complete `$DOCUMENT` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOCUMENT/**"}]}}, "scope-download": {"version": null, "identifier": "scope-download", "description": "This scope permits access to all files and list content of top level directories in the `$DOWNLOAD`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD/*"}]}}, "scope-download-index": {"version": null, "identifier": "scope-download-index", "description": "This scope permits to list all files and folders in the `$DOWNLOAD`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD/"}]}}, "scope-download-recursive": {"version": null, "identifier": "scope-download-recursive", "description": "This scope recursive access to the complete `$DOWNLOAD` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$DOWNLOAD/**"}]}}, "scope-exe": {"version": null, "identifier": "scope-exe", "description": "This scope permits access to all files and list content of top level directories in the `$EXE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE/*"}]}}, "scope-exe-index": {"version": null, "identifier": "scope-exe-index", "description": "This scope permits to list all files and folders in the `$EXE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE/"}]}}, "scope-exe-recursive": {"version": null, "identifier": "scope-exe-recursive", "description": "This scope recursive access to the complete `$EXE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$EXE/**"}]}}, "scope-font": {"version": null, "identifier": "scope-font", "description": "This scope permits access to all files and list content of top level directories in the `$FONT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT/*"}]}}, "scope-font-index": {"version": null, "identifier": "scope-font-index", "description": "This scope permits to list all files and folders in the `$FONT`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT/"}]}}, "scope-font-recursive": {"version": null, "identifier": "scope-font-recursive", "description": "This scope recursive access to the complete `$FONT` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$FONT/**"}]}}, "scope-home": {"version": null, "identifier": "scope-home", "description": "This scope permits access to all files and list content of top level directories in the `$HOME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME/*"}]}}, "scope-home-index": {"version": null, "identifier": "scope-home-index", "description": "This scope permits to list all files and folders in the `$HOME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME/"}]}}, "scope-home-recursive": {"version": null, "identifier": "scope-home-recursive", "description": "This scope recursive access to the complete `$HOME` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$HOME/**"}]}}, "scope-localdata": {"version": null, "identifier": "scope-localdata", "description": "This scope permits access to all files and list content of top level directories in the `$LOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA/*"}]}}, "scope-localdata-index": {"version": null, "identifier": "scope-localdata-index", "description": "This scope permits to list all files and folders in the `$LOCALDATA`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA/"}]}}, "scope-localdata-recursive": {"version": null, "identifier": "scope-localdata-recursive", "description": "This scope recursive access to the complete `$LOCALDATA` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOCALDATA/**"}]}}, "scope-log": {"version": null, "identifier": "scope-log", "description": "This scope permits access to all files and list content of top level directories in the `$LOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG/*"}]}}, "scope-log-index": {"version": null, "identifier": "scope-log-index", "description": "This scope permits to list all files and folders in the `$LOG`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG/"}]}}, "scope-log-recursive": {"version": null, "identifier": "scope-log-recursive", "description": "This scope recursive access to the complete `$LOG` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$LOG/**"}]}}, "scope-picture": {"version": null, "identifier": "scope-picture", "description": "This scope permits access to all files and list content of top level directories in the `$PICTURE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE/*"}]}}, "scope-picture-index": {"version": null, "identifier": "scope-picture-index", "description": "This scope permits to list all files and folders in the `$PICTURE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE/"}]}}, "scope-picture-recursive": {"version": null, "identifier": "scope-picture-recursive", "description": "This scope recursive access to the complete `$PICTURE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PICTURE/**"}]}}, "scope-public": {"version": null, "identifier": "scope-public", "description": "This scope permits access to all files and list content of top level directories in the `$PUBLIC`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC/*"}]}}, "scope-public-index": {"version": null, "identifier": "scope-public-index", "description": "This scope permits to list all files and folders in the `$PUBLIC`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC/"}]}}, "scope-public-recursive": {"version": null, "identifier": "scope-public-recursive", "description": "This scope recursive access to the complete `$PUBLIC` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$PUBLIC/**"}]}}, "scope-resource": {"version": null, "identifier": "scope-resource", "description": "This scope permits access to all files and list content of top level directories in the `$RESOURCE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE/*"}]}}, "scope-resource-index": {"version": null, "identifier": "scope-resource-index", "description": "This scope permits to list all files and folders in the `$RESOURCE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE/"}]}}, "scope-resource-recursive": {"version": null, "identifier": "scope-resource-recursive", "description": "This scope recursive access to the complete `$RESOURCE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RESOURCE/**"}]}}, "scope-runtime": {"version": null, "identifier": "scope-runtime", "description": "This scope permits access to all files and list content of top level directories in the `$RUNTIME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME/*"}]}}, "scope-runtime-index": {"version": null, "identifier": "scope-runtime-index", "description": "This scope permits to list all files and folders in the `$RUNTIME`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME/"}]}}, "scope-runtime-recursive": {"version": null, "identifier": "scope-runtime-recursive", "description": "This scope recursive access to the complete `$RUNTIME` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$RUNTIME/**"}]}}, "scope-temp": {"version": null, "identifier": "scope-temp", "description": "This scope permits access to all files and list content of top level directories in the `$TEMP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP/*"}]}}, "scope-temp-index": {"version": null, "identifier": "scope-temp-index", "description": "This scope permits to list all files and folders in the `$TEMP`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP/"}]}}, "scope-temp-recursive": {"version": null, "identifier": "scope-temp-recursive", "description": "This scope recursive access to the complete `$TEMP` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMP/**"}]}}, "scope-template": {"version": null, "identifier": "scope-template", "description": "This scope permits access to all files and list content of top level directories in the `$TEMPLATE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE/*"}]}}, "scope-template-index": {"version": null, "identifier": "scope-template-index", "description": "This scope permits to list all files and folders in the `$TEMPLATE`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE/"}]}}, "scope-template-recursive": {"version": null, "identifier": "scope-template-recursive", "description": "This scope recursive access to the complete `$TEMPLATE` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$TEMPLATE/**"}]}}, "scope-video": {"version": null, "identifier": "scope-video", "description": "This scope permits access to all files and list content of top level directories in the `$VIDEO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO/*"}]}}, "scope-video-index": {"version": null, "identifier": "scope-video-index", "description": "This scope permits to list all files and folders in the `$VIDEO`folder.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO/"}]}}, "scope-video-recursive": {"version": null, "identifier": "scope-video-recursive", "description": "This scope recursive access to the complete `$VIDEO` folder, including sub directories and files.", "commands": {"allow": [], "deny": []}, "scope": {"allow": [{"path": "$VIDEO/**"}]}}, "write-all": {"version": null, "identifier": "write-all", "description": "This enables all write related commands without any pre-configured accessible paths.", "commands": {"allow": ["mkdir", "create", "copy_file", "remove", "rename", "truncate", "ftrun<PERSON>", "write", "write_file", "write_text_file"], "deny": []}, "scope": {}}, "write-files": {"version": null, "identifier": "write-files", "description": "This enables all file write related commands without any pre-configured accessible paths.", "commands": {"allow": ["create", "copy_file", "remove", "rename", "truncate", "ftrun<PERSON>", "write", "write_file", "write_text_file"], "deny": []}, "scope": {}}}, "permission_sets": {"allow-app-meta": {"identifier": "allow-app-meta", "description": "This allows read access to metadata of the `$APP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-app-index"]}, "allow-app-meta-recursive": {"identifier": "allow-app-meta-recursive", "description": "This allows read access to metadata of the `$APP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-app-recursive"]}, "allow-app-read": {"identifier": "allow-app-read", "description": "This allows non-recursive read access to the `$APP` folder.", "permissions": ["read-all", "scope-app"]}, "allow-app-read-recursive": {"identifier": "allow-app-read-recursive", "description": "This allows full recursive read access to the complete `$APP` folder, files and subdirectories.", "permissions": ["read-all", "scope-app-recursive"]}, "allow-app-write": {"identifier": "allow-app-write", "description": "This allows non-recursive write access to the `$APP` folder.", "permissions": ["write-all", "scope-app"]}, "allow-app-write-recursive": {"identifier": "allow-app-write-recursive", "description": "This allows full recusrive write access to the complete `$APP` folder, files and subdirectories.", "permissions": ["write-all", "scope-app-recursive"]}, "allow-appcache-meta": {"identifier": "allow-appcache-meta", "description": "This allows read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appcache-index"]}, "allow-appcache-meta-recursive": {"identifier": "allow-appcache-meta-recursive", "description": "This allows read access to metadata of the `$APPCACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appcache-recursive"]}, "allow-appcache-read": {"identifier": "allow-appcache-read", "description": "This allows non-recursive read access to the `$APPCACHE` folder.", "permissions": ["read-all", "scope-appcache"]}, "allow-appcache-read-recursive": {"identifier": "allow-appcache-read-recursive", "description": "This allows full recursive read access to the complete `$APPCACHE` folder, files and subdirectories.", "permissions": ["read-all", "scope-appcache-recursive"]}, "allow-appcache-write": {"identifier": "allow-appcache-write", "description": "This allows non-recursive write access to the `$APPCACHE` folder.", "permissions": ["write-all", "scope-appcache"]}, "allow-appcache-write-recursive": {"identifier": "allow-appcache-write-recursive", "description": "This allows full recusrive write access to the complete `$APPCACHE` folder, files and subdirectories.", "permissions": ["write-all", "scope-appcache-recursive"]}, "allow-appconfig-meta": {"identifier": "allow-appconfig-meta", "description": "This allows read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appconfig-index"]}, "allow-appconfig-meta-recursive": {"identifier": "allow-appconfig-meta-recursive", "description": "This allows read access to metadata of the `$APPCONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appconfig-recursive"]}, "allow-appconfig-read": {"identifier": "allow-appconfig-read", "description": "This allows non-recursive read access to the `$APPCONFIG` folder.", "permissions": ["read-all", "scope-appconfig"]}, "allow-appconfig-read-recursive": {"identifier": "allow-appconfig-read-recursive", "description": "This allows full recursive read access to the complete `$APPCONFIG` folder, files and subdirectories.", "permissions": ["read-all", "scope-appconfig-recursive"]}, "allow-appconfig-write": {"identifier": "allow-appconfig-write", "description": "This allows non-recursive write access to the `$APPCONFIG` folder.", "permissions": ["write-all", "scope-appconfig"]}, "allow-appconfig-write-recursive": {"identifier": "allow-appconfig-write-recursive", "description": "This allows full recusrive write access to the complete `$APPCONFIG` folder, files and subdirectories.", "permissions": ["write-all", "scope-appconfig-recursive"]}, "allow-appdata-meta": {"identifier": "allow-appdata-meta", "description": "This allows read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appdata-index"]}, "allow-appdata-meta-recursive": {"identifier": "allow-appdata-meta-recursive", "description": "This allows read access to metadata of the `$APPDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-appdata-recursive"]}, "allow-appdata-read": {"identifier": "allow-appdata-read", "description": "This allows non-recursive read access to the `$APPDATA` folder.", "permissions": ["read-all", "scope-appdata"]}, "allow-appdata-read-recursive": {"identifier": "allow-appdata-read-recursive", "description": "This allows full recursive read access to the complete `$APPDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-appdata-recursive"]}, "allow-appdata-write": {"identifier": "allow-appdata-write", "description": "This allows non-recursive write access to the `$APPDATA` folder.", "permissions": ["write-all", "scope-appdata"]}, "allow-appdata-write-recursive": {"identifier": "allow-appdata-write-recursive", "description": "This allows full recusrive write access to the complete `$APPDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-appdata-recursive"]}, "allow-applocaldata-meta": {"identifier": "allow-applocaldata-meta", "description": "This allows read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applocaldata-index"]}, "allow-applocaldata-meta-recursive": {"identifier": "allow-applocaldata-meta-recursive", "description": "This allows read access to metadata of the `$APPLOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applocaldata-recursive"]}, "allow-applocaldata-read": {"identifier": "allow-applocaldata-read", "description": "This allows non-recursive read access to the `$APPLOCALDATA` folder.", "permissions": ["read-all", "scope-applocaldata"]}, "allow-applocaldata-read-recursive": {"identifier": "allow-applocaldata-read-recursive", "description": "This allows full recursive read access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-applocaldata-recursive"]}, "allow-applocaldata-write": {"identifier": "allow-applocaldata-write", "description": "This allows non-recursive write access to the `$APPLOCALDATA` folder.", "permissions": ["write-all", "scope-applocaldata"]}, "allow-applocaldata-write-recursive": {"identifier": "allow-applocaldata-write-recursive", "description": "This allows full recusrive write access to the complete `$APPLOCALDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-applocaldata-recursive"]}, "allow-applog-meta": {"identifier": "allow-applog-meta", "description": "This allows read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applog-index"]}, "allow-applog-meta-recursive": {"identifier": "allow-applog-meta-recursive", "description": "This allows read access to metadata of the `$APPLOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-applog-recursive"]}, "allow-applog-read": {"identifier": "allow-applog-read", "description": "This allows non-recursive read access to the `$APPLOG` folder.", "permissions": ["read-all", "scope-applog"]}, "allow-applog-read-recursive": {"identifier": "allow-applog-read-recursive", "description": "This allows full recursive read access to the complete `$APPLOG` folder, files and subdirectories.", "permissions": ["read-all", "scope-applog-recursive"]}, "allow-applog-write": {"identifier": "allow-applog-write", "description": "This allows non-recursive write access to the `$APPLOG` folder.", "permissions": ["write-all", "scope-applog"]}, "allow-applog-write-recursive": {"identifier": "allow-applog-write-recursive", "description": "This allows full recusrive write access to the complete `$APPLOG` folder, files and subdirectories.", "permissions": ["write-all", "scope-applog-recursive"]}, "allow-audio-meta": {"identifier": "allow-audio-meta", "description": "This allows read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-audio-index"]}, "allow-audio-meta-recursive": {"identifier": "allow-audio-meta-recursive", "description": "This allows read access to metadata of the `$AUDIO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-audio-recursive"]}, "allow-audio-read": {"identifier": "allow-audio-read", "description": "This allows non-recursive read access to the `$AUDIO` folder.", "permissions": ["read-all", "scope-audio"]}, "allow-audio-read-recursive": {"identifier": "allow-audio-read-recursive", "description": "This allows full recursive read access to the complete `$AUDIO` folder, files and subdirectories.", "permissions": ["read-all", "scope-audio-recursive"]}, "allow-audio-write": {"identifier": "allow-audio-write", "description": "This allows non-recursive write access to the `$AUDIO` folder.", "permissions": ["write-all", "scope-audio"]}, "allow-audio-write-recursive": {"identifier": "allow-audio-write-recursive", "description": "This allows full recusrive write access to the complete `$AUDIO` folder, files and subdirectories.", "permissions": ["write-all", "scope-audio-recursive"]}, "allow-cache-meta": {"identifier": "allow-cache-meta", "description": "This allows read access to metadata of the `$CACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-cache-index"]}, "allow-cache-meta-recursive": {"identifier": "allow-cache-meta-recursive", "description": "This allows read access to metadata of the `$CACHE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-cache-recursive"]}, "allow-cache-read": {"identifier": "allow-cache-read", "description": "This allows non-recursive read access to the `$CACHE` folder.", "permissions": ["read-all", "scope-cache"]}, "allow-cache-read-recursive": {"identifier": "allow-cache-read-recursive", "description": "This allows full recursive read access to the complete `$CACHE` folder, files and subdirectories.", "permissions": ["read-all", "scope-cache-recursive"]}, "allow-cache-write": {"identifier": "allow-cache-write", "description": "This allows non-recursive write access to the `$CACHE` folder.", "permissions": ["write-all", "scope-cache"]}, "allow-cache-write-recursive": {"identifier": "allow-cache-write-recursive", "description": "This allows full recusrive write access to the complete `$CACHE` folder, files and subdirectories.", "permissions": ["write-all", "scope-cache-recursive"]}, "allow-config-meta": {"identifier": "allow-config-meta", "description": "This allows read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-config-index"]}, "allow-config-meta-recursive": {"identifier": "allow-config-meta-recursive", "description": "This allows read access to metadata of the `$CONFIG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-config-recursive"]}, "allow-config-read": {"identifier": "allow-config-read", "description": "This allows non-recursive read access to the `$CONFIG` folder.", "permissions": ["read-all", "scope-config"]}, "allow-config-read-recursive": {"identifier": "allow-config-read-recursive", "description": "This allows full recursive read access to the complete `$CONFIG` folder, files and subdirectories.", "permissions": ["read-all", "scope-config-recursive"]}, "allow-config-write": {"identifier": "allow-config-write", "description": "This allows non-recursive write access to the `$CONFIG` folder.", "permissions": ["write-all", "scope-config"]}, "allow-config-write-recursive": {"identifier": "allow-config-write-recursive", "description": "This allows full recusrive write access to the complete `$CONFIG` folder, files and subdirectories.", "permissions": ["write-all", "scope-config-recursive"]}, "allow-data-meta": {"identifier": "allow-data-meta", "description": "This allows read access to metadata of the `$DATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-data-index"]}, "allow-data-meta-recursive": {"identifier": "allow-data-meta-recursive", "description": "This allows read access to metadata of the `$DATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-data-recursive"]}, "allow-data-read": {"identifier": "allow-data-read", "description": "This allows non-recursive read access to the `$DATA` folder.", "permissions": ["read-all", "scope-data"]}, "allow-data-read-recursive": {"identifier": "allow-data-read-recursive", "description": "This allows full recursive read access to the complete `$DATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-data-recursive"]}, "allow-data-write": {"identifier": "allow-data-write", "description": "This allows non-recursive write access to the `$DATA` folder.", "permissions": ["write-all", "scope-data"]}, "allow-data-write-recursive": {"identifier": "allow-data-write-recursive", "description": "This allows full recusrive write access to the complete `$DATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-data-recursive"]}, "allow-desktop-meta": {"identifier": "allow-desktop-meta", "description": "This allows read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-desktop-index"]}, "allow-desktop-meta-recursive": {"identifier": "allow-desktop-meta-recursive", "description": "This allows read access to metadata of the `$DESKTOP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-desktop-recursive"]}, "allow-desktop-read": {"identifier": "allow-desktop-read", "description": "This allows non-recursive read access to the `$DESKTOP` folder.", "permissions": ["read-all", "scope-desktop"]}, "allow-desktop-read-recursive": {"identifier": "allow-desktop-read-recursive", "description": "This allows full recursive read access to the complete `$DESKTOP` folder, files and subdirectories.", "permissions": ["read-all", "scope-desktop-recursive"]}, "allow-desktop-write": {"identifier": "allow-desktop-write", "description": "This allows non-recursive write access to the `$DESKTOP` folder.", "permissions": ["write-all", "scope-desktop"]}, "allow-desktop-write-recursive": {"identifier": "allow-desktop-write-recursive", "description": "This allows full recusrive write access to the complete `$DESKTOP` folder, files and subdirectories.", "permissions": ["write-all", "scope-desktop-recursive"]}, "allow-document-meta": {"identifier": "allow-document-meta", "description": "This allows read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-document-index"]}, "allow-document-meta-recursive": {"identifier": "allow-document-meta-recursive", "description": "This allows read access to metadata of the `$DOCUMENT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-document-recursive"]}, "allow-document-read": {"identifier": "allow-document-read", "description": "This allows non-recursive read access to the `$DOCUMENT` folder.", "permissions": ["read-all", "scope-document"]}, "allow-document-read-recursive": {"identifier": "allow-document-read-recursive", "description": "This allows full recursive read access to the complete `$DOCUMENT` folder, files and subdirectories.", "permissions": ["read-all", "scope-document-recursive"]}, "allow-document-write": {"identifier": "allow-document-write", "description": "This allows non-recursive write access to the `$DOCUMENT` folder.", "permissions": ["write-all", "scope-document"]}, "allow-document-write-recursive": {"identifier": "allow-document-write-recursive", "description": "This allows full recusrive write access to the complete `$DOCUMENT` folder, files and subdirectories.", "permissions": ["write-all", "scope-document-recursive"]}, "allow-download-meta": {"identifier": "allow-download-meta", "description": "This allows read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-download-index"]}, "allow-download-meta-recursive": {"identifier": "allow-download-meta-recursive", "description": "This allows read access to metadata of the `$DOWNLOAD` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-download-recursive"]}, "allow-download-read": {"identifier": "allow-download-read", "description": "This allows non-recursive read access to the `$DOWNLOAD` folder.", "permissions": ["read-all", "scope-download"]}, "allow-download-read-recursive": {"identifier": "allow-download-read-recursive", "description": "This allows full recursive read access to the complete `$DOWNLOAD` folder, files and subdirectories.", "permissions": ["read-all", "scope-download-recursive"]}, "allow-download-write": {"identifier": "allow-download-write", "description": "This allows non-recursive write access to the `$DOWNLOAD` folder.", "permissions": ["write-all", "scope-download"]}, "allow-download-write-recursive": {"identifier": "allow-download-write-recursive", "description": "This allows full recusrive write access to the complete `$DOWNLOAD` folder, files and subdirectories.", "permissions": ["write-all", "scope-download-recursive"]}, "allow-exe-meta": {"identifier": "allow-exe-meta", "description": "This allows read access to metadata of the `$EXE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-exe-index"]}, "allow-exe-meta-recursive": {"identifier": "allow-exe-meta-recursive", "description": "This allows read access to metadata of the `$EXE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-exe-recursive"]}, "allow-exe-read": {"identifier": "allow-exe-read", "description": "This allows non-recursive read access to the `$EXE` folder.", "permissions": ["read-all", "scope-exe"]}, "allow-exe-read-recursive": {"identifier": "allow-exe-read-recursive", "description": "This allows full recursive read access to the complete `$EXE` folder, files and subdirectories.", "permissions": ["read-all", "scope-exe-recursive"]}, "allow-exe-write": {"identifier": "allow-exe-write", "description": "This allows non-recursive write access to the `$EXE` folder.", "permissions": ["write-all", "scope-exe"]}, "allow-exe-write-recursive": {"identifier": "allow-exe-write-recursive", "description": "This allows full recusrive write access to the complete `$EXE` folder, files and subdirectories.", "permissions": ["write-all", "scope-exe-recursive"]}, "allow-font-meta": {"identifier": "allow-font-meta", "description": "This allows read access to metadata of the `$FONT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-font-index"]}, "allow-font-meta-recursive": {"identifier": "allow-font-meta-recursive", "description": "This allows read access to metadata of the `$FONT` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-font-recursive"]}, "allow-font-read": {"identifier": "allow-font-read", "description": "This allows non-recursive read access to the `$FONT` folder.", "permissions": ["read-all", "scope-font"]}, "allow-font-read-recursive": {"identifier": "allow-font-read-recursive", "description": "This allows full recursive read access to the complete `$FONT` folder, files and subdirectories.", "permissions": ["read-all", "scope-font-recursive"]}, "allow-font-write": {"identifier": "allow-font-write", "description": "This allows non-recursive write access to the `$FONT` folder.", "permissions": ["write-all", "scope-font"]}, "allow-font-write-recursive": {"identifier": "allow-font-write-recursive", "description": "This allows full recusrive write access to the complete `$FONT` folder, files and subdirectories.", "permissions": ["write-all", "scope-font-recursive"]}, "allow-home-meta": {"identifier": "allow-home-meta", "description": "This allows read access to metadata of the `$HOME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-home-index"]}, "allow-home-meta-recursive": {"identifier": "allow-home-meta-recursive", "description": "This allows read access to metadata of the `$HOME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-home-recursive"]}, "allow-home-read": {"identifier": "allow-home-read", "description": "This allows non-recursive read access to the `$HOME` folder.", "permissions": ["read-all", "scope-home"]}, "allow-home-read-recursive": {"identifier": "allow-home-read-recursive", "description": "This allows full recursive read access to the complete `$HOME` folder, files and subdirectories.", "permissions": ["read-all", "scope-home-recursive"]}, "allow-home-write": {"identifier": "allow-home-write", "description": "This allows non-recursive write access to the `$HOME` folder.", "permissions": ["write-all", "scope-home"]}, "allow-home-write-recursive": {"identifier": "allow-home-write-recursive", "description": "This allows full recusrive write access to the complete `$HOME` folder, files and subdirectories.", "permissions": ["write-all", "scope-home-recursive"]}, "allow-localdata-meta": {"identifier": "allow-localdata-meta", "description": "This allows read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-localdata-index"]}, "allow-localdata-meta-recursive": {"identifier": "allow-localdata-meta-recursive", "description": "This allows read access to metadata of the `$LOCALDATA` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-localdata-recursive"]}, "allow-localdata-read": {"identifier": "allow-localdata-read", "description": "This allows non-recursive read access to the `$LOCALDATA` folder.", "permissions": ["read-all", "scope-localdata"]}, "allow-localdata-read-recursive": {"identifier": "allow-localdata-read-recursive", "description": "This allows full recursive read access to the complete `$LOCALDATA` folder, files and subdirectories.", "permissions": ["read-all", "scope-localdata-recursive"]}, "allow-localdata-write": {"identifier": "allow-localdata-write", "description": "This allows non-recursive write access to the `$LOCALDATA` folder.", "permissions": ["write-all", "scope-localdata"]}, "allow-localdata-write-recursive": {"identifier": "allow-localdata-write-recursive", "description": "This allows full recusrive write access to the complete `$LOCALDATA` folder, files and subdirectories.", "permissions": ["write-all", "scope-localdata-recursive"]}, "allow-log-meta": {"identifier": "allow-log-meta", "description": "This allows read access to metadata of the `$LOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-log-index"]}, "allow-log-meta-recursive": {"identifier": "allow-log-meta-recursive", "description": "This allows read access to metadata of the `$LOG` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-log-recursive"]}, "allow-log-read": {"identifier": "allow-log-read", "description": "This allows non-recursive read access to the `$LOG` folder.", "permissions": ["read-all", "scope-log"]}, "allow-log-read-recursive": {"identifier": "allow-log-read-recursive", "description": "This allows full recursive read access to the complete `$LOG` folder, files and subdirectories.", "permissions": ["read-all", "scope-log-recursive"]}, "allow-log-write": {"identifier": "allow-log-write", "description": "This allows non-recursive write access to the `$LOG` folder.", "permissions": ["write-all", "scope-log"]}, "allow-log-write-recursive": {"identifier": "allow-log-write-recursive", "description": "This allows full recusrive write access to the complete `$LOG` folder, files and subdirectories.", "permissions": ["write-all", "scope-log-recursive"]}, "allow-picture-meta": {"identifier": "allow-picture-meta", "description": "This allows read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-picture-index"]}, "allow-picture-meta-recursive": {"identifier": "allow-picture-meta-recursive", "description": "This allows read access to metadata of the `$PICTURE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-picture-recursive"]}, "allow-picture-read": {"identifier": "allow-picture-read", "description": "This allows non-recursive read access to the `$PICTURE` folder.", "permissions": ["read-all", "scope-picture"]}, "allow-picture-read-recursive": {"identifier": "allow-picture-read-recursive", "description": "This allows full recursive read access to the complete `$PICTURE` folder, files and subdirectories.", "permissions": ["read-all", "scope-picture-recursive"]}, "allow-picture-write": {"identifier": "allow-picture-write", "description": "This allows non-recursive write access to the `$PICTURE` folder.", "permissions": ["write-all", "scope-picture"]}, "allow-picture-write-recursive": {"identifier": "allow-picture-write-recursive", "description": "This allows full recusrive write access to the complete `$PICTURE` folder, files and subdirectories.", "permissions": ["write-all", "scope-picture-recursive"]}, "allow-public-meta": {"identifier": "allow-public-meta", "description": "This allows read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-public-index"]}, "allow-public-meta-recursive": {"identifier": "allow-public-meta-recursive", "description": "This allows read access to metadata of the `$PUBLIC` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-public-recursive"]}, "allow-public-read": {"identifier": "allow-public-read", "description": "This allows non-recursive read access to the `$PUBLIC` folder.", "permissions": ["read-all", "scope-public"]}, "allow-public-read-recursive": {"identifier": "allow-public-read-recursive", "description": "This allows full recursive read access to the complete `$PUBLIC` folder, files and subdirectories.", "permissions": ["read-all", "scope-public-recursive"]}, "allow-public-write": {"identifier": "allow-public-write", "description": "This allows non-recursive write access to the `$PUBLIC` folder.", "permissions": ["write-all", "scope-public"]}, "allow-public-write-recursive": {"identifier": "allow-public-write-recursive", "description": "This allows full recusrive write access to the complete `$PUBLIC` folder, files and subdirectories.", "permissions": ["write-all", "scope-public-recursive"]}, "allow-resource-meta": {"identifier": "allow-resource-meta", "description": "This allows read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-resource-index"]}, "allow-resource-meta-recursive": {"identifier": "allow-resource-meta-recursive", "description": "This allows read access to metadata of the `$RESOURCE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-resource-recursive"]}, "allow-resource-read": {"identifier": "allow-resource-read", "description": "This allows non-recursive read access to the `$RESOURCE` folder.", "permissions": ["read-all", "scope-resource"]}, "allow-resource-read-recursive": {"identifier": "allow-resource-read-recursive", "description": "This allows full recursive read access to the complete `$RESOURCE` folder, files and subdirectories.", "permissions": ["read-all", "scope-resource-recursive"]}, "allow-resource-write": {"identifier": "allow-resource-write", "description": "This allows non-recursive write access to the `$RESOURCE` folder.", "permissions": ["write-all", "scope-resource"]}, "allow-resource-write-recursive": {"identifier": "allow-resource-write-recursive", "description": "This allows full recusrive write access to the complete `$RESOURCE` folder, files and subdirectories.", "permissions": ["write-all", "scope-resource-recursive"]}, "allow-runtime-meta": {"identifier": "allow-runtime-meta", "description": "This allows read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-runtime-index"]}, "allow-runtime-meta-recursive": {"identifier": "allow-runtime-meta-recursive", "description": "This allows read access to metadata of the `$RUNTIME` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-runtime-recursive"]}, "allow-runtime-read": {"identifier": "allow-runtime-read", "description": "This allows non-recursive read access to the `$RUNTIME` folder.", "permissions": ["read-all", "scope-runtime"]}, "allow-runtime-read-recursive": {"identifier": "allow-runtime-read-recursive", "description": "This allows full recursive read access to the complete `$RUNTIME` folder, files and subdirectories.", "permissions": ["read-all", "scope-runtime-recursive"]}, "allow-runtime-write": {"identifier": "allow-runtime-write", "description": "This allows non-recursive write access to the `$RUNTIME` folder.", "permissions": ["write-all", "scope-runtime"]}, "allow-runtime-write-recursive": {"identifier": "allow-runtime-write-recursive", "description": "This allows full recusrive write access to the complete `$RUNTIME` folder, files and subdirectories.", "permissions": ["write-all", "scope-runtime-recursive"]}, "allow-temp-meta": {"identifier": "allow-temp-meta", "description": "This allows read access to metadata of the `$TEMP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-temp-index"]}, "allow-temp-meta-recursive": {"identifier": "allow-temp-meta-recursive", "description": "This allows read access to metadata of the `$TEMP` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-temp-recursive"]}, "allow-temp-read": {"identifier": "allow-temp-read", "description": "This allows non-recursive read access to the `$TEMP` folder.", "permissions": ["read-all", "scope-temp"]}, "allow-temp-read-recursive": {"identifier": "allow-temp-read-recursive", "description": "This allows full recursive read access to the complete `$TEMP` folder, files and subdirectories.", "permissions": ["read-all", "scope-temp-recursive"]}, "allow-temp-write": {"identifier": "allow-temp-write", "description": "This allows non-recursive write access to the `$TEMP` folder.", "permissions": ["write-all", "scope-temp"]}, "allow-temp-write-recursive": {"identifier": "allow-temp-write-recursive", "description": "This allows full recusrive write access to the complete `$TEMP` folder, files and subdirectories.", "permissions": ["write-all", "scope-temp-recursive"]}, "allow-template-meta": {"identifier": "allow-template-meta", "description": "This allows read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-template-index"]}, "allow-template-meta-recursive": {"identifier": "allow-template-meta-recursive", "description": "This allows read access to metadata of the `$TEMPLATE` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-template-recursive"]}, "allow-template-read": {"identifier": "allow-template-read", "description": "This allows non-recursive read access to the `$TEMPLATE` folder.", "permissions": ["read-all", "scope-template"]}, "allow-template-read-recursive": {"identifier": "allow-template-read-recursive", "description": "This allows full recursive read access to the complete `$TEMPLATE` folder, files and subdirectories.", "permissions": ["read-all", "scope-template-recursive"]}, "allow-template-write": {"identifier": "allow-template-write", "description": "This allows non-recursive write access to the `$TEMPLATE` folder.", "permissions": ["write-all", "scope-template"]}, "allow-template-write-recursive": {"identifier": "allow-template-write-recursive", "description": "This allows full recusrive write access to the complete `$TEMPLATE` folder, files and subdirectories.", "permissions": ["write-all", "scope-template-recursive"]}, "allow-video-meta": {"identifier": "allow-video-meta", "description": "This allows read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-video-index"]}, "allow-video-meta-recursive": {"identifier": "allow-video-meta-recursive", "description": "This allows read access to metadata of the `$VIDEO` folder, including file listing and statistics.", "permissions": ["read-meta", "scope-video-recursive"]}, "allow-video-read": {"identifier": "allow-video-read", "description": "This allows non-recursive read access to the `$VIDEO` folder.", "permissions": ["read-all", "scope-video"]}, "allow-video-read-recursive": {"identifier": "allow-video-read-recursive", "description": "This allows full recursive read access to the complete `$VIDEO` folder, files and subdirectories.", "permissions": ["read-all", "scope-video-recursive"]}, "allow-video-write": {"identifier": "allow-video-write", "description": "This allows non-recursive write access to the `$VIDEO` folder.", "permissions": ["write-all", "scope-video"]}, "allow-video-write-recursive": {"identifier": "allow-video-write-recursive", "description": "This allows full recusrive write access to the complete `$VIDEO` folder, files and subdirectories.", "permissions": ["write-all", "scope-video-recursive"]}, "deny-default": {"identifier": "deny-default", "description": "This denies access to dangerous Tauri relevant files and folders by default.", "permissions": ["deny-webview-data-linux", "deny-webview-data-windows"]}}, "global_scope_schema": {"$schema": "http://json-schema.org/draft-07/schema#", "properties": {"path": {"type": "string"}}, "required": ["path"], "title": "Entry", "type": "object"}}, "http": {"default_permission": {"identifier": "default", "description": "Allows all fetch operations", "permissions": ["allow-fetch", "allow-fetch-cancel", "allow-fetch-read-body", "allow-fetch-send"]}, "permissions": {"allow-fetch": {"version": null, "identifier": "allow-fetch", "description": "Enables the fetch command without any pre-configured scope.", "commands": {"allow": ["fetch"], "deny": []}, "scope": {}}, "allow-fetch-cancel": {"version": null, "identifier": "allow-fetch-cancel", "description": "Enables the fetch_cancel command without any pre-configured scope.", "commands": {"allow": ["fetch_cancel"], "deny": []}, "scope": {}}, "allow-fetch-read-body": {"version": null, "identifier": "allow-fetch-read-body", "description": "Enables the fetch_read_body command without any pre-configured scope.", "commands": {"allow": ["fetch_read_body"], "deny": []}, "scope": {}}, "allow-fetch-send": {"version": null, "identifier": "allow-fetch-send", "description": "Enables the fetch_send command without any pre-configured scope.", "commands": {"allow": ["fetch_send"], "deny": []}, "scope": {}}, "deny-fetch": {"version": null, "identifier": "deny-fetch", "description": "Denies the fetch command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fetch"]}, "scope": {}}, "deny-fetch-cancel": {"version": null, "identifier": "deny-fetch-cancel", "description": "Denies the fetch_cancel command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fetch_cancel"]}, "scope": {}}, "deny-fetch-read-body": {"version": null, "identifier": "deny-fetch-read-body", "description": "Denies the fetch_read_body command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fetch_read_body"]}, "scope": {}}, "deny-fetch-send": {"version": null, "identifier": "deny-fetch-send", "description": "Denies the fetch_send command without any pre-configured scope.", "commands": {"allow": [], "deny": ["fetch_send"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": {"$schema": "http://json-schema.org/draft-07/schema#", "description": "HTTP scope entry object definition.", "properties": {"url": {"description": "A URL that can be accessed by the webview when using the HTTP APIs. The scoped URL is matched against the request URL using a glob pattern.\n\nExamples:\n\n- \"https://*\" or \"https://**\" : allows all HTTPS urls\n\n- \"https://*.github.com/tauri-apps/tauri\": allows any subdomain of \"github.com\" with the \"tauri-apps/api\" path\n\n- \"https://myapi.service.com/users/*\": allows access to any URLs that begins with \"https://myapi.service.com/users/\"", "type": "string"}}, "required": ["url"], "title": "ScopeEntry", "type": "object"}}, "log": {"default_permission": {"identifier": "default", "description": "Allows the log command", "permissions": ["allow-log"]}, "permissions": {"allow-log": {"version": null, "identifier": "allow-log", "description": "Enables the log command without any pre-configured scope.", "commands": {"allow": ["log"], "deny": []}, "scope": {}}, "deny-log": {"version": null, "identifier": "deny-log", "description": "Denies the log command without any pre-configured scope.", "commands": {"allow": [], "deny": ["log"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "menu": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": []}, "permissions": {"allow-append": {"version": null, "identifier": "allow-append", "description": "Enables the append command without any pre-configured scope.", "commands": {"allow": ["append"], "deny": []}, "scope": {}}, "allow-create-default": {"version": null, "identifier": "allow-create-default", "description": "Enables the create_default command without any pre-configured scope.", "commands": {"allow": ["create_default"], "deny": []}, "scope": {}}, "allow-get": {"version": null, "identifier": "allow-get", "description": "Enables the get command without any pre-configured scope.", "commands": {"allow": ["get"], "deny": []}, "scope": {}}, "allow-insert": {"version": null, "identifier": "allow-insert", "description": "Enables the insert command without any pre-configured scope.", "commands": {"allow": ["insert"], "deny": []}, "scope": {}}, "allow-is-checked": {"version": null, "identifier": "allow-is-checked", "description": "Enables the is_checked command without any pre-configured scope.", "commands": {"allow": ["is_checked"], "deny": []}, "scope": {}}, "allow-is-enabled": {"version": null, "identifier": "allow-is-enabled", "description": "Enables the is_enabled command without any pre-configured scope.", "commands": {"allow": ["is_enabled"], "deny": []}, "scope": {}}, "allow-items": {"version": null, "identifier": "allow-items", "description": "Enables the items command without any pre-configured scope.", "commands": {"allow": ["items"], "deny": []}, "scope": {}}, "allow-new": {"version": null, "identifier": "allow-new", "description": "Enables the new command without any pre-configured scope.", "commands": {"allow": ["new"], "deny": []}, "scope": {}}, "allow-popup": {"version": null, "identifier": "allow-popup", "description": "Enables the popup command without any pre-configured scope.", "commands": {"allow": ["popup"], "deny": []}, "scope": {}}, "allow-prepend": {"version": null, "identifier": "allow-prepend", "description": "Enables the prepend command without any pre-configured scope.", "commands": {"allow": ["prepend"], "deny": []}, "scope": {}}, "allow-remove": {"version": null, "identifier": "allow-remove", "description": "Enables the remove command without any pre-configured scope.", "commands": {"allow": ["remove"], "deny": []}, "scope": {}}, "allow-remove-at": {"version": null, "identifier": "allow-remove-at", "description": "Enables the remove_at command without any pre-configured scope.", "commands": {"allow": ["remove_at"], "deny": []}, "scope": {}}, "allow-set-accelerator": {"version": null, "identifier": "allow-set-accelerator", "description": "Enables the set_accelerator command without any pre-configured scope.", "commands": {"allow": ["set_accelerator"], "deny": []}, "scope": {}}, "allow-set-as-app-menu": {"version": null, "identifier": "allow-set-as-app-menu", "description": "Enables the set_as_app_menu command without any pre-configured scope.", "commands": {"allow": ["set_as_app_menu"], "deny": []}, "scope": {}}, "allow-set-as-help-menu-for-nsapp": {"version": null, "identifier": "allow-set-as-help-menu-for-nsapp", "description": "Enables the set_as_help_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": ["set_as_help_menu_for_nsapp"], "deny": []}, "scope": {}}, "allow-set-as-window-menu": {"version": null, "identifier": "allow-set-as-window-menu", "description": "Enables the set_as_window_menu command without any pre-configured scope.", "commands": {"allow": ["set_as_window_menu"], "deny": []}, "scope": {}}, "allow-set-as-windows-menu-for-nsapp": {"version": null, "identifier": "allow-set-as-windows-menu-for-nsapp", "description": "Enables the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": ["set_as_windows_menu_for_nsapp"], "deny": []}, "scope": {}}, "allow-set-checked": {"version": null, "identifier": "allow-set-checked", "description": "Enables the set_checked command without any pre-configured scope.", "commands": {"allow": ["set_checked"], "deny": []}, "scope": {}}, "allow-set-enabled": {"version": null, "identifier": "allow-set-enabled", "description": "Enables the set_enabled command without any pre-configured scope.", "commands": {"allow": ["set_enabled"], "deny": []}, "scope": {}}, "allow-set-icon": {"version": null, "identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}, "scope": {}}, "allow-set-text": {"version": null, "identifier": "allow-set-text", "description": "Enables the set_text command without any pre-configured scope.", "commands": {"allow": ["set_text"], "deny": []}, "scope": {}}, "allow-text": {"version": null, "identifier": "allow-text", "description": "Enables the text command without any pre-configured scope.", "commands": {"allow": ["text"], "deny": []}, "scope": {}}, "deny-append": {"version": null, "identifier": "deny-append", "description": "Denies the append command without any pre-configured scope.", "commands": {"allow": [], "deny": ["append"]}, "scope": {}}, "deny-create-default": {"version": null, "identifier": "deny-create-default", "description": "Denies the create_default command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_default"]}, "scope": {}}, "deny-get": {"version": null, "identifier": "deny-get", "description": "Denies the get command without any pre-configured scope.", "commands": {"allow": [], "deny": ["get"]}, "scope": {}}, "deny-insert": {"version": null, "identifier": "deny-insert", "description": "Denies the insert command without any pre-configured scope.", "commands": {"allow": [], "deny": ["insert"]}, "scope": {}}, "deny-is-checked": {"version": null, "identifier": "deny-is-checked", "description": "Denies the is_checked command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_checked"]}, "scope": {}}, "deny-is-enabled": {"version": null, "identifier": "deny-is-enabled", "description": "Denies the is_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_enabled"]}, "scope": {}}, "deny-items": {"version": null, "identifier": "deny-items", "description": "Denies the items command without any pre-configured scope.", "commands": {"allow": [], "deny": ["items"]}, "scope": {}}, "deny-new": {"version": null, "identifier": "deny-new", "description": "Denies the new command without any pre-configured scope.", "commands": {"allow": [], "deny": ["new"]}, "scope": {}}, "deny-popup": {"version": null, "identifier": "deny-popup", "description": "Denies the popup command without any pre-configured scope.", "commands": {"allow": [], "deny": ["popup"]}, "scope": {}}, "deny-prepend": {"version": null, "identifier": "deny-prepend", "description": "Denies the prepend command without any pre-configured scope.", "commands": {"allow": [], "deny": ["prepend"]}, "scope": {}}, "deny-remove": {"version": null, "identifier": "deny-remove", "description": "Denies the remove command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove"]}, "scope": {}}, "deny-remove-at": {"version": null, "identifier": "deny-remove-at", "description": "Denies the remove_at command without any pre-configured scope.", "commands": {"allow": [], "deny": ["remove_at"]}, "scope": {}}, "deny-set-accelerator": {"version": null, "identifier": "deny-set-accelerator", "description": "Denies the set_accelerator command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_accelerator"]}, "scope": {}}, "deny-set-as-app-menu": {"version": null, "identifier": "deny-set-as-app-menu", "description": "Denies the set_as_app_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_app_menu"]}, "scope": {}}, "deny-set-as-help-menu-for-nsapp": {"version": null, "identifier": "deny-set-as-help-menu-for-nsapp", "description": "Denies the set_as_help_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_help_menu_for_nsapp"]}, "scope": {}}, "deny-set-as-window-menu": {"version": null, "identifier": "deny-set-as-window-menu", "description": "Denies the set_as_window_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_window_menu"]}, "scope": {}}, "deny-set-as-windows-menu-for-nsapp": {"version": null, "identifier": "deny-set-as-windows-menu-for-nsapp", "description": "Denies the set_as_windows_menu_for_nsapp command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_as_windows_menu_for_nsapp"]}, "scope": {}}, "deny-set-checked": {"version": null, "identifier": "deny-set-checked", "description": "Denies the set_checked command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_checked"]}, "scope": {}}, "deny-set-enabled": {"version": null, "identifier": "deny-set-enabled", "description": "Denies the set_enabled command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_enabled"]}, "scope": {}}, "deny-set-icon": {"version": null, "identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}, "scope": {}}, "deny-set-text": {"version": null, "identifier": "deny-set-text", "description": "Denies the set_text command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_text"]}, "scope": {}}, "deny-text": {"version": null, "identifier": "deny-text", "description": "Denies the text command without any pre-configured scope.", "commands": {"allow": [], "deny": ["text"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "path": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-resolve-directory", "allow-resolve", "allow-normalize", "allow-join", "allow-dirname", "allow-extname", "allow-basename", "allow-is-absolute"]}, "permissions": {"allow-basename": {"version": null, "identifier": "allow-basename", "description": "Enables the basename command without any pre-configured scope.", "commands": {"allow": ["basename"], "deny": []}, "scope": {}}, "allow-dirname": {"version": null, "identifier": "allow-dirname", "description": "Enables the dirname command without any pre-configured scope.", "commands": {"allow": ["dirname"], "deny": []}, "scope": {}}, "allow-extname": {"version": null, "identifier": "allow-extname", "description": "Enables the extname command without any pre-configured scope.", "commands": {"allow": ["extname"], "deny": []}, "scope": {}}, "allow-is-absolute": {"version": null, "identifier": "allow-is-absolute", "description": "Enables the is_absolute command without any pre-configured scope.", "commands": {"allow": ["is_absolute"], "deny": []}, "scope": {}}, "allow-join": {"version": null, "identifier": "allow-join", "description": "Enables the join command without any pre-configured scope.", "commands": {"allow": ["join"], "deny": []}, "scope": {}}, "allow-normalize": {"version": null, "identifier": "allow-normalize", "description": "Enables the normalize command without any pre-configured scope.", "commands": {"allow": ["normalize"], "deny": []}, "scope": {}}, "allow-resolve": {"version": null, "identifier": "allow-resolve", "description": "Enables the resolve command without any pre-configured scope.", "commands": {"allow": ["resolve"], "deny": []}, "scope": {}}, "allow-resolve-directory": {"version": null, "identifier": "allow-resolve-directory", "description": "Enables the resolve_directory command without any pre-configured scope.", "commands": {"allow": ["resolve_directory"], "deny": []}, "scope": {}}, "deny-basename": {"version": null, "identifier": "deny-basename", "description": "Denies the basename command without any pre-configured scope.", "commands": {"allow": [], "deny": ["basename"]}, "scope": {}}, "deny-dirname": {"version": null, "identifier": "deny-dirname", "description": "Denies the dirname command without any pre-configured scope.", "commands": {"allow": [], "deny": ["dirname"]}, "scope": {}}, "deny-extname": {"version": null, "identifier": "deny-extname", "description": "Denies the extname command without any pre-configured scope.", "commands": {"allow": [], "deny": ["extname"]}, "scope": {}}, "deny-is-absolute": {"version": null, "identifier": "deny-is-absolute", "description": "Denies the is_absolute command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_absolute"]}, "scope": {}}, "deny-join": {"version": null, "identifier": "deny-join", "description": "Denies the join command without any pre-configured scope.", "commands": {"allow": [], "deny": ["join"]}, "scope": {}}, "deny-normalize": {"version": null, "identifier": "deny-normalize", "description": "Denies the normalize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["normalize"]}, "scope": {}}, "deny-resolve": {"version": null, "identifier": "deny-resolve", "description": "Denies the resolve command without any pre-configured scope.", "commands": {"allow": [], "deny": ["resolve"]}, "scope": {}}, "deny-resolve-directory": {"version": null, "identifier": "deny-resolve-directory", "description": "Denies the resolve_directory command without any pre-configured scope.", "commands": {"allow": [], "deny": ["resolve_directory"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "resources": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-close"]}, "permissions": {"allow-close": {"version": null, "identifier": "allow-close", "description": "Enables the close command without any pre-configured scope.", "commands": {"allow": ["close"], "deny": []}, "scope": {}}, "deny-close": {"version": null, "identifier": "deny-close", "description": "Denies the close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["close"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "tray": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": []}, "permissions": {"allow-new": {"version": null, "identifier": "allow-new", "description": "Enables the new command without any pre-configured scope.", "commands": {"allow": ["new"], "deny": []}, "scope": {}}, "allow-set-icon": {"version": null, "identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}, "scope": {}}, "allow-set-icon-as-template": {"version": null, "identifier": "allow-set-icon-as-template", "description": "Enables the set_icon_as_template command without any pre-configured scope.", "commands": {"allow": ["set_icon_as_template"], "deny": []}, "scope": {}}, "allow-set-menu": {"version": null, "identifier": "allow-set-menu", "description": "Enables the set_menu command without any pre-configured scope.", "commands": {"allow": ["set_menu"], "deny": []}, "scope": {}}, "allow-set-show-menu-on-left-click": {"version": null, "identifier": "allow-set-show-menu-on-left-click", "description": "Enables the set_show_menu_on_left_click command without any pre-configured scope.", "commands": {"allow": ["set_show_menu_on_left_click"], "deny": []}, "scope": {}}, "allow-set-temp-dir-path": {"version": null, "identifier": "allow-set-temp-dir-path", "description": "Enables the set_temp_dir_path command without any pre-configured scope.", "commands": {"allow": ["set_temp_dir_path"], "deny": []}, "scope": {}}, "allow-set-title": {"version": null, "identifier": "allow-set-title", "description": "Enables the set_title command without any pre-configured scope.", "commands": {"allow": ["set_title"], "deny": []}, "scope": {}}, "allow-set-tooltip": {"version": null, "identifier": "allow-set-tooltip", "description": "Enables the set_tooltip command without any pre-configured scope.", "commands": {"allow": ["set_tooltip"], "deny": []}, "scope": {}}, "allow-set-visible": {"version": null, "identifier": "allow-set-visible", "description": "Enables the set_visible command without any pre-configured scope.", "commands": {"allow": ["set_visible"], "deny": []}, "scope": {}}, "deny-new": {"version": null, "identifier": "deny-new", "description": "Denies the new command without any pre-configured scope.", "commands": {"allow": [], "deny": ["new"]}, "scope": {}}, "deny-set-icon": {"version": null, "identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}, "scope": {}}, "deny-set-icon-as-template": {"version": null, "identifier": "deny-set-icon-as-template", "description": "Denies the set_icon_as_template command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon_as_template"]}, "scope": {}}, "deny-set-menu": {"version": null, "identifier": "deny-set-menu", "description": "Denies the set_menu command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_menu"]}, "scope": {}}, "deny-set-show-menu-on-left-click": {"version": null, "identifier": "deny-set-show-menu-on-left-click", "description": "Denies the set_show_menu_on_left_click command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_show_menu_on_left_click"]}, "scope": {}}, "deny-set-temp-dir-path": {"version": null, "identifier": "deny-set-temp-dir-path", "description": "Denies the set_temp_dir_path command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_temp_dir_path"]}, "scope": {}}, "deny-set-title": {"version": null, "identifier": "deny-set-title", "description": "Denies the set_title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_title"]}, "scope": {}}, "deny-set-tooltip": {"version": null, "identifier": "deny-set-tooltip", "description": "Denies the set_tooltip command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_tooltip"]}, "scope": {}}, "deny-set-visible": {"version": null, "identifier": "deny-set-visible", "description": "Denies the set_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_visible"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "updater": {"default_permission": {"identifier": "default", "description": "Allows checking for new updates and installing them", "permissions": ["allow-check", "allow-download-and-install"]}, "permissions": {"allow-check": {"version": null, "identifier": "allow-check", "description": "Enables the check command without any pre-configured scope.", "commands": {"allow": ["check"], "deny": []}, "scope": {}}, "allow-download-and-install": {"version": null, "identifier": "allow-download-and-install", "description": "Enables the download_and_install command without any pre-configured scope.", "commands": {"allow": ["download_and_install"], "deny": []}, "scope": {}}, "deny-check": {"version": null, "identifier": "deny-check", "description": "Denies the check command without any pre-configured scope.", "commands": {"allow": [], "deny": ["check"]}, "scope": {}}, "deny-download-and-install": {"version": null, "identifier": "deny-download-and-install", "description": "Denies the download_and_install command without any pre-configured scope.", "commands": {"allow": [], "deny": ["download_and_install"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "webview": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-webview-position", "allow-webview-size", "allow-internal-toggle-devtools"]}, "permissions": {"allow-create-webview": {"version": null, "identifier": "allow-create-webview", "description": "Enables the create_webview command without any pre-configured scope.", "commands": {"allow": ["create_webview"], "deny": []}, "scope": {}}, "allow-create-webview-window": {"version": null, "identifier": "allow-create-webview-window", "description": "Enables the create_webview_window command without any pre-configured scope.", "commands": {"allow": ["create_webview_window"], "deny": []}, "scope": {}}, "allow-internal-toggle-devtools": {"version": null, "identifier": "allow-internal-toggle-devtools", "description": "Enables the internal_toggle_devtools command without any pre-configured scope.", "commands": {"allow": ["internal_toggle_devtools"], "deny": []}, "scope": {}}, "allow-print": {"version": null, "identifier": "allow-print", "description": "Enables the print command without any pre-configured scope.", "commands": {"allow": ["print"], "deny": []}, "scope": {}}, "allow-reparent": {"version": null, "identifier": "allow-reparent", "description": "Enables the reparent command without any pre-configured scope.", "commands": {"allow": ["reparent"], "deny": []}, "scope": {}}, "allow-set-webview-focus": {"version": null, "identifier": "allow-set-webview-focus", "description": "Enables the set_webview_focus command without any pre-configured scope.", "commands": {"allow": ["set_webview_focus"], "deny": []}, "scope": {}}, "allow-set-webview-position": {"version": null, "identifier": "allow-set-webview-position", "description": "Enables the set_webview_position command without any pre-configured scope.", "commands": {"allow": ["set_webview_position"], "deny": []}, "scope": {}}, "allow-set-webview-size": {"version": null, "identifier": "allow-set-webview-size", "description": "Enables the set_webview_size command without any pre-configured scope.", "commands": {"allow": ["set_webview_size"], "deny": []}, "scope": {}}, "allow-webview-close": {"version": null, "identifier": "allow-webview-close", "description": "Enables the webview_close command without any pre-configured scope.", "commands": {"allow": ["webview_close"], "deny": []}, "scope": {}}, "allow-webview-position": {"version": null, "identifier": "allow-webview-position", "description": "Enables the webview_position command without any pre-configured scope.", "commands": {"allow": ["webview_position"], "deny": []}, "scope": {}}, "allow-webview-size": {"version": null, "identifier": "allow-webview-size", "description": "Enables the webview_size command without any pre-configured scope.", "commands": {"allow": ["webview_size"], "deny": []}, "scope": {}}, "deny-create-webview": {"version": null, "identifier": "deny-create-webview", "description": "Denies the create_webview command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_webview"]}, "scope": {}}, "deny-create-webview-window": {"version": null, "identifier": "deny-create-webview-window", "description": "Denies the create_webview_window command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create_webview_window"]}, "scope": {}}, "deny-internal-toggle-devtools": {"version": null, "identifier": "deny-internal-toggle-devtools", "description": "Denies the internal_toggle_devtools command without any pre-configured scope.", "commands": {"allow": [], "deny": ["internal_toggle_devtools"]}, "scope": {}}, "deny-print": {"version": null, "identifier": "deny-print", "description": "Denies the print command without any pre-configured scope.", "commands": {"allow": [], "deny": ["print"]}, "scope": {}}, "deny-reparent": {"version": null, "identifier": "deny-reparent", "description": "Denies the reparent command without any pre-configured scope.", "commands": {"allow": [], "deny": ["reparent"]}, "scope": {}}, "deny-set-webview-focus": {"version": null, "identifier": "deny-set-webview-focus", "description": "Denies the set_webview_focus command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_focus"]}, "scope": {}}, "deny-set-webview-position": {"version": null, "identifier": "deny-set-webview-position", "description": "Denies the set_webview_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_position"]}, "scope": {}}, "deny-set-webview-size": {"version": null, "identifier": "deny-set-webview-size", "description": "Denies the set_webview_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_webview_size"]}, "scope": {}}, "deny-webview-close": {"version": null, "identifier": "deny-webview-close", "description": "Denies the webview_close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_close"]}, "scope": {}}, "deny-webview-position": {"version": null, "identifier": "deny-webview-position", "description": "Denies the webview_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_position"]}, "scope": {}}, "deny-webview-size": {"version": null, "identifier": "deny-webview-size", "description": "Denies the webview_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["webview_size"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}, "window": {"default_permission": {"identifier": "default", "description": "Default permissions for the plugin.", "permissions": ["allow-scale-factor", "allow-inner-position", "allow-outer-position", "allow-inner-size", "allow-outer-size", "allow-is-fullscreen", "allow-is-minimized", "allow-is-maximized", "allow-is-focused", "allow-is-decorated", "allow-is-resizable", "allow-is-maximizable", "allow-is-minimizable", "allow-is-closable", "allow-is-visible", "allow-title", "allow-current-monitor", "allow-primary-monitor", "allow-available-monitors", "allow-theme", "allow-internal-toggle-maximize"]}, "permissions": {"allow-available-monitors": {"version": null, "identifier": "allow-available-monitors", "description": "Enables the available_monitors command without any pre-configured scope.", "commands": {"allow": ["available_monitors"], "deny": []}, "scope": {}}, "allow-center": {"version": null, "identifier": "allow-center", "description": "Enables the center command without any pre-configured scope.", "commands": {"allow": ["center"], "deny": []}, "scope": {}}, "allow-close": {"version": null, "identifier": "allow-close", "description": "Enables the close command without any pre-configured scope.", "commands": {"allow": ["close"], "deny": []}, "scope": {}}, "allow-create": {"version": null, "identifier": "allow-create", "description": "Enables the create command without any pre-configured scope.", "commands": {"allow": ["create"], "deny": []}, "scope": {}}, "allow-current-monitor": {"version": null, "identifier": "allow-current-monitor", "description": "Enables the current_monitor command without any pre-configured scope.", "commands": {"allow": ["current_monitor"], "deny": []}, "scope": {}}, "allow-destroy": {"version": null, "identifier": "allow-destroy", "description": "Enables the destroy command without any pre-configured scope.", "commands": {"allow": ["destroy"], "deny": []}, "scope": {}}, "allow-hide": {"version": null, "identifier": "allow-hide", "description": "Enables the hide command without any pre-configured scope.", "commands": {"allow": ["hide"], "deny": []}, "scope": {}}, "allow-inner-position": {"version": null, "identifier": "allow-inner-position", "description": "Enables the inner_position command without any pre-configured scope.", "commands": {"allow": ["inner_position"], "deny": []}, "scope": {}}, "allow-inner-size": {"version": null, "identifier": "allow-inner-size", "description": "Enables the inner_size command without any pre-configured scope.", "commands": {"allow": ["inner_size"], "deny": []}, "scope": {}}, "allow-internal-toggle-maximize": {"version": null, "identifier": "allow-internal-toggle-maximize", "description": "Enables the internal_toggle_maximize command without any pre-configured scope.", "commands": {"allow": ["internal_toggle_maximize"], "deny": []}, "scope": {}}, "allow-is-closable": {"version": null, "identifier": "allow-is-closable", "description": "Enables the is_closable command without any pre-configured scope.", "commands": {"allow": ["is_closable"], "deny": []}, "scope": {}}, "allow-is-decorated": {"version": null, "identifier": "allow-is-decorated", "description": "Enables the is_decorated command without any pre-configured scope.", "commands": {"allow": ["is_decorated"], "deny": []}, "scope": {}}, "allow-is-focused": {"version": null, "identifier": "allow-is-focused", "description": "Enables the is_focused command without any pre-configured scope.", "commands": {"allow": ["is_focused"], "deny": []}, "scope": {}}, "allow-is-fullscreen": {"version": null, "identifier": "allow-is-fullscreen", "description": "Enables the is_fullscreen command without any pre-configured scope.", "commands": {"allow": ["is_fullscreen"], "deny": []}, "scope": {}}, "allow-is-maximizable": {"version": null, "identifier": "allow-is-maximizable", "description": "Enables the is_maximizable command without any pre-configured scope.", "commands": {"allow": ["is_maximizable"], "deny": []}, "scope": {}}, "allow-is-maximized": {"version": null, "identifier": "allow-is-maximized", "description": "Enables the is_maximized command without any pre-configured scope.", "commands": {"allow": ["is_maximized"], "deny": []}, "scope": {}}, "allow-is-minimizable": {"version": null, "identifier": "allow-is-minimizable", "description": "Enables the is_minimizable command without any pre-configured scope.", "commands": {"allow": ["is_minimizable"], "deny": []}, "scope": {}}, "allow-is-minimized": {"version": null, "identifier": "allow-is-minimized", "description": "Enables the is_minimized command without any pre-configured scope.", "commands": {"allow": ["is_minimized"], "deny": []}, "scope": {}}, "allow-is-resizable": {"version": null, "identifier": "allow-is-resizable", "description": "Enables the is_resizable command without any pre-configured scope.", "commands": {"allow": ["is_resizable"], "deny": []}, "scope": {}}, "allow-is-visible": {"version": null, "identifier": "allow-is-visible", "description": "Enables the is_visible command without any pre-configured scope.", "commands": {"allow": ["is_visible"], "deny": []}, "scope": {}}, "allow-maximize": {"version": null, "identifier": "allow-maximize", "description": "Enables the maximize command without any pre-configured scope.", "commands": {"allow": ["maximize"], "deny": []}, "scope": {}}, "allow-minimize": {"version": null, "identifier": "allow-minimize", "description": "Enables the minimize command without any pre-configured scope.", "commands": {"allow": ["minimize"], "deny": []}, "scope": {}}, "allow-outer-position": {"version": null, "identifier": "allow-outer-position", "description": "Enables the outer_position command without any pre-configured scope.", "commands": {"allow": ["outer_position"], "deny": []}, "scope": {}}, "allow-outer-size": {"version": null, "identifier": "allow-outer-size", "description": "Enables the outer_size command without any pre-configured scope.", "commands": {"allow": ["outer_size"], "deny": []}, "scope": {}}, "allow-primary-monitor": {"version": null, "identifier": "allow-primary-monitor", "description": "Enables the primary_monitor command without any pre-configured scope.", "commands": {"allow": ["primary_monitor"], "deny": []}, "scope": {}}, "allow-request-user-attention": {"version": null, "identifier": "allow-request-user-attention", "description": "Enables the request_user_attention command without any pre-configured scope.", "commands": {"allow": ["request_user_attention"], "deny": []}, "scope": {}}, "allow-scale-factor": {"version": null, "identifier": "allow-scale-factor", "description": "Enables the scale_factor command without any pre-configured scope.", "commands": {"allow": ["scale_factor"], "deny": []}, "scope": {}}, "allow-set-always-on-bottom": {"version": null, "identifier": "allow-set-always-on-bottom", "description": "Enables the set_always_on_bottom command without any pre-configured scope.", "commands": {"allow": ["set_always_on_bottom"], "deny": []}, "scope": {}}, "allow-set-always-on-top": {"version": null, "identifier": "allow-set-always-on-top", "description": "Enables the set_always_on_top command without any pre-configured scope.", "commands": {"allow": ["set_always_on_top"], "deny": []}, "scope": {}}, "allow-set-closable": {"version": null, "identifier": "allow-set-closable", "description": "Enables the set_closable command without any pre-configured scope.", "commands": {"allow": ["set_closable"], "deny": []}, "scope": {}}, "allow-set-content-protected": {"version": null, "identifier": "allow-set-content-protected", "description": "Enables the set_content_protected command without any pre-configured scope.", "commands": {"allow": ["set_content_protected"], "deny": []}, "scope": {}}, "allow-set-cursor-grab": {"version": null, "identifier": "allow-set-cursor-grab", "description": "Enables the set_cursor_grab command without any pre-configured scope.", "commands": {"allow": ["set_cursor_grab"], "deny": []}, "scope": {}}, "allow-set-cursor-icon": {"version": null, "identifier": "allow-set-cursor-icon", "description": "Enables the set_cursor_icon command without any pre-configured scope.", "commands": {"allow": ["set_cursor_icon"], "deny": []}, "scope": {}}, "allow-set-cursor-position": {"version": null, "identifier": "allow-set-cursor-position", "description": "Enables the set_cursor_position command without any pre-configured scope.", "commands": {"allow": ["set_cursor_position"], "deny": []}, "scope": {}}, "allow-set-cursor-visible": {"version": null, "identifier": "allow-set-cursor-visible", "description": "Enables the set_cursor_visible command without any pre-configured scope.", "commands": {"allow": ["set_cursor_visible"], "deny": []}, "scope": {}}, "allow-set-decorations": {"version": null, "identifier": "allow-set-decorations", "description": "Enables the set_decorations command without any pre-configured scope.", "commands": {"allow": ["set_decorations"], "deny": []}, "scope": {}}, "allow-set-effects": {"version": null, "identifier": "allow-set-effects", "description": "Enables the set_effects command without any pre-configured scope.", "commands": {"allow": ["set_effects"], "deny": []}, "scope": {}}, "allow-set-focus": {"version": null, "identifier": "allow-set-focus", "description": "Enables the set_focus command without any pre-configured scope.", "commands": {"allow": ["set_focus"], "deny": []}, "scope": {}}, "allow-set-fullscreen": {"version": null, "identifier": "allow-set-fullscreen", "description": "Enables the set_fullscreen command without any pre-configured scope.", "commands": {"allow": ["set_fullscreen"], "deny": []}, "scope": {}}, "allow-set-icon": {"version": null, "identifier": "allow-set-icon", "description": "Enables the set_icon command without any pre-configured scope.", "commands": {"allow": ["set_icon"], "deny": []}, "scope": {}}, "allow-set-ignore-cursor-events": {"version": null, "identifier": "allow-set-ignore-cursor-events", "description": "Enables the set_ignore_cursor_events command without any pre-configured scope.", "commands": {"allow": ["set_ignore_cursor_events"], "deny": []}, "scope": {}}, "allow-set-max-size": {"version": null, "identifier": "allow-set-max-size", "description": "Enables the set_max_size command without any pre-configured scope.", "commands": {"allow": ["set_max_size"], "deny": []}, "scope": {}}, "allow-set-maximizable": {"version": null, "identifier": "allow-set-maximizable", "description": "Enables the set_maximizable command without any pre-configured scope.", "commands": {"allow": ["set_maximizable"], "deny": []}, "scope": {}}, "allow-set-min-size": {"version": null, "identifier": "allow-set-min-size", "description": "Enables the set_min_size command without any pre-configured scope.", "commands": {"allow": ["set_min_size"], "deny": []}, "scope": {}}, "allow-set-minimizable": {"version": null, "identifier": "allow-set-minimizable", "description": "Enables the set_minimizable command without any pre-configured scope.", "commands": {"allow": ["set_minimizable"], "deny": []}, "scope": {}}, "allow-set-position": {"version": null, "identifier": "allow-set-position", "description": "Enables the set_position command without any pre-configured scope.", "commands": {"allow": ["set_position"], "deny": []}, "scope": {}}, "allow-set-progress-bar": {"version": null, "identifier": "allow-set-progress-bar", "description": "Enables the set_progress_bar command without any pre-configured scope.", "commands": {"allow": ["set_progress_bar"], "deny": []}, "scope": {}}, "allow-set-resizable": {"version": null, "identifier": "allow-set-resizable", "description": "Enables the set_resizable command without any pre-configured scope.", "commands": {"allow": ["set_resizable"], "deny": []}, "scope": {}}, "allow-set-shadow": {"version": null, "identifier": "allow-set-shadow", "description": "Enables the set_shadow command without any pre-configured scope.", "commands": {"allow": ["set_shadow"], "deny": []}, "scope": {}}, "allow-set-size": {"version": null, "identifier": "allow-set-size", "description": "Enables the set_size command without any pre-configured scope.", "commands": {"allow": ["set_size"], "deny": []}, "scope": {}}, "allow-set-skip-taskbar": {"version": null, "identifier": "allow-set-skip-taskbar", "description": "Enables the set_skip_taskbar command without any pre-configured scope.", "commands": {"allow": ["set_skip_taskbar"], "deny": []}, "scope": {}}, "allow-set-title": {"version": null, "identifier": "allow-set-title", "description": "Enables the set_title command without any pre-configured scope.", "commands": {"allow": ["set_title"], "deny": []}, "scope": {}}, "allow-set-visible-on-all-workspaces": {"version": null, "identifier": "allow-set-visible-on-all-workspaces", "description": "Enables the set_visible_on_all_workspaces command without any pre-configured scope.", "commands": {"allow": ["set_visible_on_all_workspaces"], "deny": []}, "scope": {}}, "allow-show": {"version": null, "identifier": "allow-show", "description": "Enables the show command without any pre-configured scope.", "commands": {"allow": ["show"], "deny": []}, "scope": {}}, "allow-start-dragging": {"version": null, "identifier": "allow-start-dragging", "description": "Enables the start_dragging command without any pre-configured scope.", "commands": {"allow": ["start_dragging"], "deny": []}, "scope": {}}, "allow-theme": {"version": null, "identifier": "allow-theme", "description": "Enables the theme command without any pre-configured scope.", "commands": {"allow": ["theme"], "deny": []}, "scope": {}}, "allow-title": {"version": null, "identifier": "allow-title", "description": "Enables the title command without any pre-configured scope.", "commands": {"allow": ["title"], "deny": []}, "scope": {}}, "allow-toggle-maximize": {"version": null, "identifier": "allow-toggle-maximize", "description": "Enables the toggle_maximize command without any pre-configured scope.", "commands": {"allow": ["toggle_maximize"], "deny": []}, "scope": {}}, "allow-unmaximize": {"version": null, "identifier": "allow-unmaximize", "description": "Enables the unmaximize command without any pre-configured scope.", "commands": {"allow": ["unmaximize"], "deny": []}, "scope": {}}, "allow-unminimize": {"version": null, "identifier": "allow-unminimize", "description": "Enables the unminimize command without any pre-configured scope.", "commands": {"allow": ["unminimize"], "deny": []}, "scope": {}}, "deny-available-monitors": {"version": null, "identifier": "deny-available-monitors", "description": "Denies the available_monitors command without any pre-configured scope.", "commands": {"allow": [], "deny": ["available_monitors"]}, "scope": {}}, "deny-center": {"version": null, "identifier": "deny-center", "description": "Denies the center command without any pre-configured scope.", "commands": {"allow": [], "deny": ["center"]}, "scope": {}}, "deny-close": {"version": null, "identifier": "deny-close", "description": "Denies the close command without any pre-configured scope.", "commands": {"allow": [], "deny": ["close"]}, "scope": {}}, "deny-create": {"version": null, "identifier": "deny-create", "description": "Denies the create command without any pre-configured scope.", "commands": {"allow": [], "deny": ["create"]}, "scope": {}}, "deny-current-monitor": {"version": null, "identifier": "deny-current-monitor", "description": "Denies the current_monitor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["current_monitor"]}, "scope": {}}, "deny-destroy": {"version": null, "identifier": "deny-destroy", "description": "Denies the destroy command without any pre-configured scope.", "commands": {"allow": [], "deny": ["destroy"]}, "scope": {}}, "deny-hide": {"version": null, "identifier": "deny-hide", "description": "Denies the hide command without any pre-configured scope.", "commands": {"allow": [], "deny": ["hide"]}, "scope": {}}, "deny-inner-position": {"version": null, "identifier": "deny-inner-position", "description": "Denies the inner_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["inner_position"]}, "scope": {}}, "deny-inner-size": {"version": null, "identifier": "deny-inner-size", "description": "Denies the inner_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["inner_size"]}, "scope": {}}, "deny-internal-toggle-maximize": {"version": null, "identifier": "deny-internal-toggle-maximize", "description": "Denies the internal_toggle_maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["internal_toggle_maximize"]}, "scope": {}}, "deny-is-closable": {"version": null, "identifier": "deny-is-closable", "description": "Denies the is_closable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_closable"]}, "scope": {}}, "deny-is-decorated": {"version": null, "identifier": "deny-is-decorated", "description": "Denies the is_decorated command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_decorated"]}, "scope": {}}, "deny-is-focused": {"version": null, "identifier": "deny-is-focused", "description": "Denies the is_focused command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_focused"]}, "scope": {}}, "deny-is-fullscreen": {"version": null, "identifier": "deny-is-fullscreen", "description": "Denies the is_fullscreen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_fullscreen"]}, "scope": {}}, "deny-is-maximizable": {"version": null, "identifier": "deny-is-maximizable", "description": "Denies the is_maximizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_maximizable"]}, "scope": {}}, "deny-is-maximized": {"version": null, "identifier": "deny-is-maximized", "description": "Denies the is_maximized command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_maximized"]}, "scope": {}}, "deny-is-minimizable": {"version": null, "identifier": "deny-is-minimizable", "description": "Denies the is_minimizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_minimizable"]}, "scope": {}}, "deny-is-minimized": {"version": null, "identifier": "deny-is-minimized", "description": "Denies the is_minimized command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_minimized"]}, "scope": {}}, "deny-is-resizable": {"version": null, "identifier": "deny-is-resizable", "description": "Denies the is_resizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_resizable"]}, "scope": {}}, "deny-is-visible": {"version": null, "identifier": "deny-is-visible", "description": "Denies the is_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["is_visible"]}, "scope": {}}, "deny-maximize": {"version": null, "identifier": "deny-maximize", "description": "Denies the maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["maximize"]}, "scope": {}}, "deny-minimize": {"version": null, "identifier": "deny-minimize", "description": "Denies the minimize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["minimize"]}, "scope": {}}, "deny-outer-position": {"version": null, "identifier": "deny-outer-position", "description": "Denies the outer_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["outer_position"]}, "scope": {}}, "deny-outer-size": {"version": null, "identifier": "deny-outer-size", "description": "Denies the outer_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["outer_size"]}, "scope": {}}, "deny-primary-monitor": {"version": null, "identifier": "deny-primary-monitor", "description": "Denies the primary_monitor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["primary_monitor"]}, "scope": {}}, "deny-request-user-attention": {"version": null, "identifier": "deny-request-user-attention", "description": "Denies the request_user_attention command without any pre-configured scope.", "commands": {"allow": [], "deny": ["request_user_attention"]}, "scope": {}}, "deny-scale-factor": {"version": null, "identifier": "deny-scale-factor", "description": "Denies the scale_factor command without any pre-configured scope.", "commands": {"allow": [], "deny": ["scale_factor"]}, "scope": {}}, "deny-set-always-on-bottom": {"version": null, "identifier": "deny-set-always-on-bottom", "description": "Denies the set_always_on_bottom command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_always_on_bottom"]}, "scope": {}}, "deny-set-always-on-top": {"version": null, "identifier": "deny-set-always-on-top", "description": "Denies the set_always_on_top command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_always_on_top"]}, "scope": {}}, "deny-set-closable": {"version": null, "identifier": "deny-set-closable", "description": "Denies the set_closable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_closable"]}, "scope": {}}, "deny-set-content-protected": {"version": null, "identifier": "deny-set-content-protected", "description": "Denies the set_content_protected command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_content_protected"]}, "scope": {}}, "deny-set-cursor-grab": {"version": null, "identifier": "deny-set-cursor-grab", "description": "Denies the set_cursor_grab command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_grab"]}, "scope": {}}, "deny-set-cursor-icon": {"version": null, "identifier": "deny-set-cursor-icon", "description": "Denies the set_cursor_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_icon"]}, "scope": {}}, "deny-set-cursor-position": {"version": null, "identifier": "deny-set-cursor-position", "description": "Denies the set_cursor_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_position"]}, "scope": {}}, "deny-set-cursor-visible": {"version": null, "identifier": "deny-set-cursor-visible", "description": "Denies the set_cursor_visible command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_cursor_visible"]}, "scope": {}}, "deny-set-decorations": {"version": null, "identifier": "deny-set-decorations", "description": "Denies the set_decorations command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_decorations"]}, "scope": {}}, "deny-set-effects": {"version": null, "identifier": "deny-set-effects", "description": "Denies the set_effects command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_effects"]}, "scope": {}}, "deny-set-focus": {"version": null, "identifier": "deny-set-focus", "description": "Denies the set_focus command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_focus"]}, "scope": {}}, "deny-set-fullscreen": {"version": null, "identifier": "deny-set-fullscreen", "description": "Denies the set_fullscreen command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_fullscreen"]}, "scope": {}}, "deny-set-icon": {"version": null, "identifier": "deny-set-icon", "description": "Denies the set_icon command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_icon"]}, "scope": {}}, "deny-set-ignore-cursor-events": {"version": null, "identifier": "deny-set-ignore-cursor-events", "description": "Denies the set_ignore_cursor_events command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_ignore_cursor_events"]}, "scope": {}}, "deny-set-max-size": {"version": null, "identifier": "deny-set-max-size", "description": "Denies the set_max_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_max_size"]}, "scope": {}}, "deny-set-maximizable": {"version": null, "identifier": "deny-set-maximizable", "description": "Denies the set_maximizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_maximizable"]}, "scope": {}}, "deny-set-min-size": {"version": null, "identifier": "deny-set-min-size", "description": "Denies the set_min_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_min_size"]}, "scope": {}}, "deny-set-minimizable": {"version": null, "identifier": "deny-set-minimizable", "description": "Denies the set_minimizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_minimizable"]}, "scope": {}}, "deny-set-position": {"version": null, "identifier": "deny-set-position", "description": "Denies the set_position command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_position"]}, "scope": {}}, "deny-set-progress-bar": {"version": null, "identifier": "deny-set-progress-bar", "description": "Denies the set_progress_bar command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_progress_bar"]}, "scope": {}}, "deny-set-resizable": {"version": null, "identifier": "deny-set-resizable", "description": "Denies the set_resizable command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_resizable"]}, "scope": {}}, "deny-set-shadow": {"version": null, "identifier": "deny-set-shadow", "description": "Denies the set_shadow command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_shadow"]}, "scope": {}}, "deny-set-size": {"version": null, "identifier": "deny-set-size", "description": "Denies the set_size command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_size"]}, "scope": {}}, "deny-set-skip-taskbar": {"version": null, "identifier": "deny-set-skip-taskbar", "description": "Denies the set_skip_taskbar command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_skip_taskbar"]}, "scope": {}}, "deny-set-title": {"version": null, "identifier": "deny-set-title", "description": "Denies the set_title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_title"]}, "scope": {}}, "deny-set-visible-on-all-workspaces": {"version": null, "identifier": "deny-set-visible-on-all-workspaces", "description": "Denies the set_visible_on_all_workspaces command without any pre-configured scope.", "commands": {"allow": [], "deny": ["set_visible_on_all_workspaces"]}, "scope": {}}, "deny-show": {"version": null, "identifier": "deny-show", "description": "Denies the show command without any pre-configured scope.", "commands": {"allow": [], "deny": ["show"]}, "scope": {}}, "deny-start-dragging": {"version": null, "identifier": "deny-start-dragging", "description": "Denies the start_dragging command without any pre-configured scope.", "commands": {"allow": [], "deny": ["start_dragging"]}, "scope": {}}, "deny-theme": {"version": null, "identifier": "deny-theme", "description": "Denies the theme command without any pre-configured scope.", "commands": {"allow": [], "deny": ["theme"]}, "scope": {}}, "deny-title": {"version": null, "identifier": "deny-title", "description": "Denies the title command without any pre-configured scope.", "commands": {"allow": [], "deny": ["title"]}, "scope": {}}, "deny-toggle-maximize": {"version": null, "identifier": "deny-toggle-maximize", "description": "Denies the toggle_maximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["toggle_maximize"]}, "scope": {}}, "deny-unmaximize": {"version": null, "identifier": "deny-unmaximize", "description": "Denies the unmaximize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unmaximize"]}, "scope": {}}, "deny-unminimize": {"version": null, "identifier": "deny-unminimize", "description": "Denies the unminimize command without any pre-configured scope.", "commands": {"allow": [], "deny": ["unminimize"]}, "scope": {}}}, "permission_sets": {}, "global_scope_schema": null}}