<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="All Tests" type="JavaScriptTestRunnerVitest" nameIsGenerated="true">
    <config value="$PROJECT_DIR$/vitest.config.ts" />
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <vitest-options value="--run" />
    <envs />
    <scope-kind value="ALL" />
    <method v="2" />
  </configuration>
</component>