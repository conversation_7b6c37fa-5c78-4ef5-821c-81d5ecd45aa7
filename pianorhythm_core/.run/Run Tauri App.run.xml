<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run Tauri App" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
    <option name="command" value="run --package pianorhythm_desktop --no-default-features --features use_bevy_ecs" />
    <option name="workingDirectory" value="file://$PROJECT_DIR$" />
    <envs />
    <option name="emulateTerminal" value="true" />
    <option name="channel" value="DEFAULT" />
    <option name="requiredFeatures" value="true" />
    <option name="allFeatures" value="false" />
    <option name="withSudo" value="false" />
    <option name="buildTarget" value="REMOTE" />
    <option name="backtrace" value="SHORT" />
    <option name="isRedirectInput" value="false" />
    <option name="redirectInputPath" value="" />
    <method v="2" />
  </configuration>
</component>