const { spawn } = require("child_process");
let convertPath = (windowsPath) => windowsPath.replace(/^\\\\\?\\/, "").replace(/\\/g, '\/').replace(/\/\/+/g, '\/');

// # Path to this plugin
let PROTOC_GEN_TS_PATH = convertPath(`.\\node_modules\\.bin\\protoc-gen-ts_proto`);

let isWindows = process.platform === "win32";
if (isWindows) {
  PROTOC_GEN_TS_PATH = `.\\node_modules.\\.bin.\\protoc-gen-ts_proto`;
  PROTOC_GEN_TS_PATH += ".cmd";
}

// # Directory to write generated code
const OUT_DIR = convertPath(".\\src\\proto");
let PROTO_PATH = convertPath(".\\pianorhythm_core\\proto\\raw");
let createProtoPath = (fileName) => convertPath(`${PROTO_PATH}\\${fileName}.proto`);

const ls = spawn(`./build/protoc/${isWindows ? "protoc_win64.exe" : "protoc_linux64"}`, [
  `--plugin=protoc-gen-ts=${PROTOC_GEN_TS_PATH}`,
  `--proto_path=${PROTO_PATH}`,
  `--ts_opt=esModuleInterop=true`,
  `--ts_out=${OUT_DIR}`,
  `${createProtoPath("room-renditions")}`,
  `${createProtoPath("user-renditions")}`,
  `${createProtoPath("client-message")}`,
  `${createProtoPath("server-message")}`,
  `${createProtoPath("midi-renditions")}`,
  `${createProtoPath("pianorhythm-app-renditions")}`,
  `${createProtoPath("pianorhythm-actions")}`,
  `${createProtoPath("pianorhythm-events")}`,
  `${createProtoPath("pianorhythm-effects")}`,
]);

ls.stdout.on("data", data => {
  console.log(`[build protobuf] stdout: ${data}`);
});

ls.stderr.on("data", data => {
  console.log(`[build protobuf] stderr: ${data}`);
});

ls.on('error', (error) => {
  console.error(`[build protobuf] error: ${error.message}`);
});

ls.on("close", code => {
  console.info(`[build protobuf] child process exited with code ${code}`);
});